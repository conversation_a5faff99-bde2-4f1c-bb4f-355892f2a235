{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parse, stringify } from 'zrender/lib/tool/color.js';\nimport * as graphic from '../../util/graphic.js';\nimport { enableHoverEmphasis } from '../../util/states.js';\nimport { setLabelStyle, createTextStyle } from '../../label/labelStyle.js';\nimport { makeBackground } from '../helper/listComponent.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport ComponentView from '../../view/Component.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport { getECData } from '../../util/innerStore.js';\nvar curry = zrUtil.curry;\nvar each = zrUtil.each;\nvar Group = graphic.Group;\nvar LegendView = /** @class */function (_super) {\n  __extends(LegendView, _super);\n  function LegendView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LegendView.type;\n    _this.newlineDisabled = false;\n    return _this;\n  }\n  LegendView.prototype.init = function () {\n    this.group.add(this._contentGroup = new Group());\n    this.group.add(this._selectorGroup = new Group());\n    this._isFirstRender = true;\n  };\n  /**\n   * @protected\n   */\n  LegendView.prototype.getContentGroup = function () {\n    return this._contentGroup;\n  };\n  /**\n   * @protected\n   */\n  LegendView.prototype.getSelectorGroup = function () {\n    return this._selectorGroup;\n  };\n  /**\n   * @override\n   */\n  LegendView.prototype.render = function (legendModel, ecModel, api) {\n    var isFirstRender = this._isFirstRender;\n    this._isFirstRender = false;\n    this.resetInner();\n    if (!legendModel.get('show', true)) {\n      return;\n    }\n    var itemAlign = legendModel.get('align');\n    var orient = legendModel.get('orient');\n    if (!itemAlign || itemAlign === 'auto') {\n      itemAlign = legendModel.get('left') === 'right' && orient === 'vertical' ? 'right' : 'left';\n    }\n    // selector has been normalized to an array in model\n    var selector = legendModel.get('selector', true);\n    var selectorPosition = legendModel.get('selectorPosition', true);\n    if (selector && (!selectorPosition || selectorPosition === 'auto')) {\n      selectorPosition = orient === 'horizontal' ? 'end' : 'start';\n    }\n    this.renderInner(itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition);\n    // Perform layout.\n    var positionInfo = legendModel.getBoxLayoutParams();\n    var viewportSize = {\n      width: api.getWidth(),\n      height: api.getHeight()\n    };\n    var padding = legendModel.get('padding');\n    var maxSize = layoutUtil.getLayoutRect(positionInfo, viewportSize, padding);\n    var mainRect = this.layoutInner(legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition);\n    // Place mainGroup, based on the calculated `mainRect`.\n    var layoutRect = layoutUtil.getLayoutRect(zrUtil.defaults({\n      width: mainRect.width,\n      height: mainRect.height\n    }, positionInfo), viewportSize, padding);\n    this.group.x = layoutRect.x - mainRect.x;\n    this.group.y = layoutRect.y - mainRect.y;\n    this.group.markRedraw();\n    // Render background after group is layout.\n    this.group.add(this._backgroundEl = makeBackground(mainRect, legendModel));\n  };\n  LegendView.prototype.resetInner = function () {\n    this.getContentGroup().removeAll();\n    this._backgroundEl && this.group.remove(this._backgroundEl);\n    this.getSelectorGroup().removeAll();\n  };\n  LegendView.prototype.renderInner = function (itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition) {\n    var contentGroup = this.getContentGroup();\n    var legendDrawnMap = zrUtil.createHashMap();\n    var selectMode = legendModel.get('selectedMode');\n    var excludeSeriesId = [];\n    ecModel.eachRawSeries(function (seriesModel) {\n      !seriesModel.get('legendHoverLink') && excludeSeriesId.push(seriesModel.id);\n    });\n    each(legendModel.getData(), function (legendItemModel, dataIndex) {\n      var name = legendItemModel.get('name');\n      // Use empty string or \\n as a newline string\n      if (!this.newlineDisabled && (name === '' || name === '\\n')) {\n        var g = new Group();\n        // @ts-ignore\n        g.newline = true;\n        contentGroup.add(g);\n        return;\n      }\n      // Representitive series.\n      var seriesModel = ecModel.getSeriesByName(name)[0];\n      if (legendDrawnMap.get(name)) {\n        // Have been drawn\n        return;\n      }\n      // Legend to control series.\n      if (seriesModel) {\n        var data = seriesModel.getData();\n        var lineVisualStyle = data.getVisual('legendLineStyle') || {};\n        var legendIcon = data.getVisual('legendIcon');\n        /**\n         * `data.getVisual('style')` may be the color from the register\n         * in series. For example, for line series,\n         */\n        var style = data.getVisual('style');\n        var itemGroup = this._createItem(seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, lineVisualStyle, style, legendIcon, selectMode, api);\n        itemGroup.on('click', curry(dispatchSelectAction, name, null, api, excludeSeriesId)).on('mouseover', curry(dispatchHighlightAction, seriesModel.name, null, api, excludeSeriesId)).on('mouseout', curry(dispatchDownplayAction, seriesModel.name, null, api, excludeSeriesId));\n        if (ecModel.ssr) {\n          itemGroup.eachChild(function (child) {\n            var ecData = getECData(child);\n            ecData.seriesIndex = seriesModel.seriesIndex;\n            ecData.dataIndex = dataIndex;\n            ecData.ssrType = 'legend';\n          });\n        }\n        legendDrawnMap.set(name, true);\n      } else {\n        // Legend to control data. In pie and funnel.\n        ecModel.eachRawSeries(function (seriesModel) {\n          // In case multiple series has same data name\n          if (legendDrawnMap.get(name)) {\n            return;\n          }\n          if (seriesModel.legendVisualProvider) {\n            var provider = seriesModel.legendVisualProvider;\n            if (!provider.containName(name)) {\n              return;\n            }\n            var idx = provider.indexOfName(name);\n            var style = provider.getItemVisual(idx, 'style');\n            var legendIcon = provider.getItemVisual(idx, 'legendIcon');\n            var colorArr = parse(style.fill);\n            // Color may be set to transparent in visualMap when data is out of range.\n            // Do not show nothing.\n            if (colorArr && colorArr[3] === 0) {\n              colorArr[3] = 0.2;\n              // TODO color is set to 0, 0, 0, 0. Should show correct RGBA\n              style = zrUtil.extend(zrUtil.extend({}, style), {\n                fill: stringify(colorArr, 'rgba')\n              });\n            }\n            var itemGroup = this._createItem(seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, {}, style, legendIcon, selectMode, api);\n            // FIXME: consider different series has items with the same name.\n            itemGroup.on('click', curry(dispatchSelectAction, null, name, api, excludeSeriesId))\n            // Should not specify the series name, consider legend controls\n            // more than one pie series.\n            .on('mouseover', curry(dispatchHighlightAction, null, name, api, excludeSeriesId)).on('mouseout', curry(dispatchDownplayAction, null, name, api, excludeSeriesId));\n            if (ecModel.ssr) {\n              itemGroup.eachChild(function (child) {\n                var ecData = getECData(child);\n                ecData.seriesIndex = seriesModel.seriesIndex;\n                ecData.dataIndex = dataIndex;\n                ecData.ssrType = 'legend';\n              });\n            }\n            legendDrawnMap.set(name, true);\n          }\n        }, this);\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (!legendDrawnMap.get(name)) {\n          console.warn(name + ' series not exists. Legend data should be same with series name or data name.');\n        }\n      }\n    }, this);\n    if (selector) {\n      this._createSelector(selector, legendModel, api, orient, selectorPosition);\n    }\n  };\n  LegendView.prototype._createSelector = function (selector, legendModel, api, orient, selectorPosition) {\n    var selectorGroup = this.getSelectorGroup();\n    each(selector, function createSelectorButton(selectorItem) {\n      var type = selectorItem.type;\n      var labelText = new graphic.Text({\n        style: {\n          x: 0,\n          y: 0,\n          align: 'center',\n          verticalAlign: 'middle'\n        },\n        onclick: function () {\n          api.dispatchAction({\n            type: type === 'all' ? 'legendAllSelect' : 'legendInverseSelect'\n          });\n        }\n      });\n      selectorGroup.add(labelText);\n      var labelModel = legendModel.getModel('selectorLabel');\n      var emphasisLabelModel = legendModel.getModel(['emphasis', 'selectorLabel']);\n      setLabelStyle(labelText, {\n        normal: labelModel,\n        emphasis: emphasisLabelModel\n      }, {\n        defaultText: selectorItem.title\n      });\n      enableHoverEmphasis(labelText);\n    });\n  };\n  LegendView.prototype._createItem = function (seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, lineVisualStyle, itemVisualStyle, legendIcon, selectMode, api) {\n    var drawType = seriesModel.visualDrawType;\n    var itemWidth = legendModel.get('itemWidth');\n    var itemHeight = legendModel.get('itemHeight');\n    var isSelected = legendModel.isSelected(name);\n    var iconRotate = legendItemModel.get('symbolRotate');\n    var symbolKeepAspect = legendItemModel.get('symbolKeepAspect');\n    var legendIconType = legendItemModel.get('icon');\n    legendIcon = legendIconType || legendIcon || 'roundRect';\n    var style = getLegendStyle(legendIcon, legendItemModel, lineVisualStyle, itemVisualStyle, drawType, isSelected, api);\n    var itemGroup = new Group();\n    var textStyleModel = legendItemModel.getModel('textStyle');\n    if (zrUtil.isFunction(seriesModel.getLegendIcon) && (!legendIconType || legendIconType === 'inherit')) {\n      // Series has specific way to define legend icon\n      itemGroup.add(seriesModel.getLegendIcon({\n        itemWidth: itemWidth,\n        itemHeight: itemHeight,\n        icon: legendIcon,\n        iconRotate: iconRotate,\n        itemStyle: style.itemStyle,\n        lineStyle: style.lineStyle,\n        symbolKeepAspect: symbolKeepAspect\n      }));\n    } else {\n      // Use default legend icon policy for most series\n      var rotate = legendIconType === 'inherit' && seriesModel.getData().getVisual('symbol') ? iconRotate === 'inherit' ? seriesModel.getData().getVisual('symbolRotate') : iconRotate : 0; // No rotation for no icon\n      itemGroup.add(getDefaultLegendIcon({\n        itemWidth: itemWidth,\n        itemHeight: itemHeight,\n        icon: legendIcon,\n        iconRotate: rotate,\n        itemStyle: style.itemStyle,\n        lineStyle: style.lineStyle,\n        symbolKeepAspect: symbolKeepAspect\n      }));\n    }\n    var textX = itemAlign === 'left' ? itemWidth + 5 : -5;\n    var textAlign = itemAlign;\n    var formatter = legendModel.get('formatter');\n    var content = name;\n    if (zrUtil.isString(formatter) && formatter) {\n      content = formatter.replace('{name}', name != null ? name : '');\n    } else if (zrUtil.isFunction(formatter)) {\n      content = formatter(name);\n    }\n    var textColor = isSelected ? textStyleModel.getTextColor() : legendItemModel.get('inactiveColor');\n    itemGroup.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        text: content,\n        x: textX,\n        y: itemHeight / 2,\n        fill: textColor,\n        align: textAlign,\n        verticalAlign: 'middle'\n      }, {\n        inheritColor: textColor\n      })\n    }));\n    // Add a invisible rect to increase the area of mouse hover\n    var hitRect = new graphic.Rect({\n      shape: itemGroup.getBoundingRect(),\n      style: {\n        // Cannot use 'invisible' because SVG SSR will miss the node\n        fill: 'transparent'\n      }\n    });\n    var tooltipModel = legendItemModel.getModel('tooltip');\n    if (tooltipModel.get('show')) {\n      graphic.setTooltipConfig({\n        el: hitRect,\n        componentModel: legendModel,\n        itemName: name,\n        itemTooltipOption: tooltipModel.option\n      });\n    }\n    itemGroup.add(hitRect);\n    itemGroup.eachChild(function (child) {\n      child.silent = true;\n    });\n    hitRect.silent = !selectMode;\n    this.getContentGroup().add(itemGroup);\n    enableHoverEmphasis(itemGroup);\n    // @ts-ignore\n    itemGroup.__legendDataIndex = dataIndex;\n    return itemGroup;\n  };\n  LegendView.prototype.layoutInner = function (legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition) {\n    var contentGroup = this.getContentGroup();\n    var selectorGroup = this.getSelectorGroup();\n    // Place items in contentGroup.\n    layoutUtil.box(legendModel.get('orient'), contentGroup, legendModel.get('itemGap'), maxSize.width, maxSize.height);\n    var contentRect = contentGroup.getBoundingRect();\n    var contentPos = [-contentRect.x, -contentRect.y];\n    selectorGroup.markRedraw();\n    contentGroup.markRedraw();\n    if (selector) {\n      // Place buttons in selectorGroup\n      layoutUtil.box(\n      // Buttons in selectorGroup always layout horizontally\n      'horizontal', selectorGroup, legendModel.get('selectorItemGap', true));\n      var selectorRect = selectorGroup.getBoundingRect();\n      var selectorPos = [-selectorRect.x, -selectorRect.y];\n      var selectorButtonGap = legendModel.get('selectorButtonGap', true);\n      var orientIdx = legendModel.getOrient().index;\n      var wh = orientIdx === 0 ? 'width' : 'height';\n      var hw = orientIdx === 0 ? 'height' : 'width';\n      var yx = orientIdx === 0 ? 'y' : 'x';\n      if (selectorPosition === 'end') {\n        selectorPos[orientIdx] += contentRect[wh] + selectorButtonGap;\n      } else {\n        contentPos[orientIdx] += selectorRect[wh] + selectorButtonGap;\n      }\n      // Always align selector to content as 'middle'\n      selectorPos[1 - orientIdx] += contentRect[hw] / 2 - selectorRect[hw] / 2;\n      selectorGroup.x = selectorPos[0];\n      selectorGroup.y = selectorPos[1];\n      contentGroup.x = contentPos[0];\n      contentGroup.y = contentPos[1];\n      var mainRect = {\n        x: 0,\n        y: 0\n      };\n      mainRect[wh] = contentRect[wh] + selectorButtonGap + selectorRect[wh];\n      mainRect[hw] = Math.max(contentRect[hw], selectorRect[hw]);\n      mainRect[yx] = Math.min(0, selectorRect[yx] + selectorPos[1 - orientIdx]);\n      return mainRect;\n    } else {\n      contentGroup.x = contentPos[0];\n      contentGroup.y = contentPos[1];\n      return this.group.getBoundingRect();\n    }\n  };\n  /**\n   * @protected\n   */\n  LegendView.prototype.remove = function () {\n    this.getContentGroup().removeAll();\n    this._isFirstRender = true;\n  };\n  LegendView.type = 'legend.plain';\n  return LegendView;\n}(ComponentView);\nfunction getLegendStyle(iconType, legendItemModel, lineVisualStyle, itemVisualStyle, drawType, isSelected, api) {\n  /**\n   * Use series style if is inherit;\n   * elsewise, use legend style\n   */\n  function handleCommonProps(style, visualStyle) {\n    // If lineStyle.width is 'auto', it is set to be 2 if series has border\n    if (style.lineWidth === 'auto') {\n      style.lineWidth = visualStyle.lineWidth > 0 ? 2 : 0;\n    }\n    each(style, function (propVal, propName) {\n      style[propName] === 'inherit' && (style[propName] = visualStyle[propName]);\n    });\n  }\n  // itemStyle\n  var itemStyleModel = legendItemModel.getModel('itemStyle');\n  var itemStyle = itemStyleModel.getItemStyle();\n  var iconBrushType = iconType.lastIndexOf('empty', 0) === 0 ? 'fill' : 'stroke';\n  var decalStyle = itemStyleModel.getShallow('decal');\n  itemStyle.decal = !decalStyle || decalStyle === 'inherit' ? itemVisualStyle.decal : createOrUpdatePatternFromDecal(decalStyle, api);\n  if (itemStyle.fill === 'inherit') {\n    /**\n     * Series with visualDrawType as 'stroke' should have\n     * series stroke as legend fill\n     */\n    itemStyle.fill = itemVisualStyle[drawType];\n  }\n  if (itemStyle.stroke === 'inherit') {\n    /**\n     * icon type with \"emptyXXX\" should use fill color\n     * in visual style\n     */\n    itemStyle.stroke = itemVisualStyle[iconBrushType];\n  }\n  if (itemStyle.opacity === 'inherit') {\n    /**\n     * Use lineStyle.opacity if drawType is stroke\n     */\n    itemStyle.opacity = (drawType === 'fill' ? itemVisualStyle : lineVisualStyle).opacity;\n  }\n  handleCommonProps(itemStyle, itemVisualStyle);\n  // lineStyle\n  var legendLineModel = legendItemModel.getModel('lineStyle');\n  var lineStyle = legendLineModel.getLineStyle();\n  handleCommonProps(lineStyle, lineVisualStyle);\n  // Fix auto color to real color\n  itemStyle.fill === 'auto' && (itemStyle.fill = itemVisualStyle.fill);\n  itemStyle.stroke === 'auto' && (itemStyle.stroke = itemVisualStyle.fill);\n  lineStyle.stroke === 'auto' && (lineStyle.stroke = itemVisualStyle.fill);\n  if (!isSelected) {\n    var borderWidth = legendItemModel.get('inactiveBorderWidth');\n    /**\n     * Since stroke is set to be inactiveBorderColor, it may occur that\n     * there is no border in series but border in legend, so we need to\n     * use border only when series has border if is set to be auto\n     */\n    var visualHasBorder = itemStyle[iconBrushType];\n    itemStyle.lineWidth = borderWidth === 'auto' ? itemVisualStyle.lineWidth > 0 && visualHasBorder ? 2 : 0 : itemStyle.lineWidth;\n    itemStyle.fill = legendItemModel.get('inactiveColor');\n    itemStyle.stroke = legendItemModel.get('inactiveBorderColor');\n    lineStyle.stroke = legendLineModel.get('inactiveColor');\n    lineStyle.lineWidth = legendLineModel.get('inactiveWidth');\n  }\n  return {\n    itemStyle: itemStyle,\n    lineStyle: lineStyle\n  };\n}\nfunction getDefaultLegendIcon(opt) {\n  var symboType = opt.icon || 'roundRect';\n  var icon = createSymbol(symboType, 0, 0, opt.itemWidth, opt.itemHeight, opt.itemStyle.fill, opt.symbolKeepAspect);\n  icon.setStyle(opt.itemStyle);\n  icon.rotation = (opt.iconRotate || 0) * Math.PI / 180;\n  icon.setOrigin([opt.itemWidth / 2, opt.itemHeight / 2]);\n  if (symboType.indexOf('empty') > -1) {\n    icon.style.stroke = icon.style.fill;\n    icon.style.fill = '#fff';\n    icon.style.lineWidth = 2;\n  }\n  return icon;\n}\nfunction dispatchSelectAction(seriesName, dataName, api, excludeSeriesId) {\n  // downplay before unselect\n  dispatchDownplayAction(seriesName, dataName, api, excludeSeriesId);\n  api.dispatchAction({\n    type: 'legendToggleSelect',\n    name: seriesName != null ? seriesName : dataName\n  });\n  // highlight after select\n  // TODO highlight immediately may cause animation loss.\n  dispatchHighlightAction(seriesName, dataName, api, excludeSeriesId);\n}\nfunction isUseHoverLayer(api) {\n  var list = api.getZr().storage.getDisplayList();\n  var emphasisState;\n  var i = 0;\n  var len = list.length;\n  while (i < len && !(emphasisState = list[i].states.emphasis)) {\n    i++;\n  }\n  return emphasisState && emphasisState.hoverLayer;\n}\nfunction dispatchHighlightAction(seriesName, dataName, api, excludeSeriesId) {\n  // If element hover will move to a hoverLayer.\n  if (!isUseHoverLayer(api)) {\n    api.dispatchAction({\n      type: 'highlight',\n      seriesName: seriesName,\n      name: dataName,\n      excludeSeriesId: excludeSeriesId\n    });\n  }\n}\nfunction dispatchDownplayAction(seriesName, dataName, api, excludeSeriesId) {\n  // If element hover will move to a hoverLayer.\n  if (!isUseHoverLayer(api)) {\n    api.dispatchAction({\n      type: 'downplay',\n      seriesName: seriesName,\n      name: dataName,\n      excludeSeriesId: excludeSeriesId\n    });\n  }\n}\nexport default LegendView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "parse", "stringify", "graphic", "enableHoverEmphasis", "setLabelStyle", "createTextStyle", "makeBackground", "layoutUtil", "ComponentView", "createSymbol", "createOrUpdatePatternFromDecal", "getECData", "curry", "each", "Group", "LegendView", "_super", "_this", "apply", "arguments", "type", "newlineDisabled", "prototype", "init", "group", "add", "_contentGroup", "_selectorGroup", "_isFirstRender", "getContentGroup", "getSelectorGroup", "render", "legend<PERSON><PERSON><PERSON>", "ecModel", "api", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetInner", "get", "itemAlign", "orient", "selector", "selectorPosition", "renderInner", "positionInfo", "getBoxLayoutParams", "viewportSize", "width", "getWidth", "height", "getHeight", "padding", "maxSize", "getLayoutRect", "mainRect", "layoutInner", "layoutRect", "defaults", "x", "y", "mark<PERSON><PERSON><PERSON>", "_backgroundEl", "removeAll", "remove", "contentGroup", "legendDrawnMap", "createHashMap", "selectMode", "excludeSeriesId", "eachRawSeries", "seriesModel", "push", "id", "getData", "legendItemModel", "dataIndex", "name", "g", "newline", "getSeriesByName", "data", "lineVisualStyle", "getVisual", "legendIcon", "style", "itemGroup", "_createItem", "on", "dispatchSelectAction", "dispatchHighlightAction", "dispatchDownplayAction", "ssr", "<PERSON><PERSON><PERSON><PERSON>", "child", "ecData", "seriesIndex", "ssrType", "set", "legend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "provider", "containName", "idx", "indexOfName", "getItemVisual", "colorArr", "fill", "extend", "process", "env", "NODE_ENV", "console", "warn", "_createSelector", "selectorGroup", "createSelectorButton", "selectorItem", "labelText", "Text", "align", "verticalAlign", "onclick", "dispatchAction", "labelModel", "getModel", "emphasisLabelModel", "normal", "emphasis", "defaultText", "title", "itemVisualStyle", "drawType", "visualDrawType", "itemWidth", "itemHeight", "isSelected", "iconRotate", "symbolKeepAspect", "legendIconType", "getLegendStyle", "textStyleModel", "isFunction", "getLegendIcon", "icon", "itemStyle", "lineStyle", "rotate", "getDefaultLegendIcon", "textX", "textAlign", "formatter", "content", "isString", "replace", "textColor", "getTextColor", "text", "inheritColor", "hitRect", "Rect", "shape", "getBoundingRect", "tooltipModel", "setTooltipConfig", "el", "componentModel", "itemName", "itemTooltipOption", "option", "silent", "__legendDataIndex", "box", "contentRect", "contentPos", "selectorRect", "selectorPos", "selectorButtonGap", "orientIdx", "getOrient", "index", "wh", "hw", "yx", "Math", "max", "min", "iconType", "handleCommonProps", "visualStyle", "lineWidth", "propVal", "propName", "itemStyleModel", "getItemStyle", "iconBrushType", "lastIndexOf", "decalStyle", "getShallow", "decal", "stroke", "opacity", "legendLineModel", "getLineStyle", "borderWidth", "visualHasBorder", "opt", "symboType", "setStyle", "rotation", "PI", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "seriesName", "dataName", "isUseHoverLayer", "list", "getZr", "storage", "getDisplayList", "emphasisState", "i", "len", "length", "states", "hoverLayer"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/component/legend/LegendView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parse, stringify } from 'zrender/lib/tool/color.js';\nimport * as graphic from '../../util/graphic.js';\nimport { enableHoverEmphasis } from '../../util/states.js';\nimport { setLabelStyle, createTextStyle } from '../../label/labelStyle.js';\nimport { makeBackground } from '../helper/listComponent.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport ComponentView from '../../view/Component.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport { getECData } from '../../util/innerStore.js';\nvar curry = zrUtil.curry;\nvar each = zrUtil.each;\nvar Group = graphic.Group;\nvar LegendView = /** @class */function (_super) {\n  __extends(LegendView, _super);\n  function LegendView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LegendView.type;\n    _this.newlineDisabled = false;\n    return _this;\n  }\n  LegendView.prototype.init = function () {\n    this.group.add(this._contentGroup = new Group());\n    this.group.add(this._selectorGroup = new Group());\n    this._isFirstRender = true;\n  };\n  /**\n   * @protected\n   */\n  LegendView.prototype.getContentGroup = function () {\n    return this._contentGroup;\n  };\n  /**\n   * @protected\n   */\n  LegendView.prototype.getSelectorGroup = function () {\n    return this._selectorGroup;\n  };\n  /**\n   * @override\n   */\n  LegendView.prototype.render = function (legendModel, ecModel, api) {\n    var isFirstRender = this._isFirstRender;\n    this._isFirstRender = false;\n    this.resetInner();\n    if (!legendModel.get('show', true)) {\n      return;\n    }\n    var itemAlign = legendModel.get('align');\n    var orient = legendModel.get('orient');\n    if (!itemAlign || itemAlign === 'auto') {\n      itemAlign = legendModel.get('left') === 'right' && orient === 'vertical' ? 'right' : 'left';\n    }\n    // selector has been normalized to an array in model\n    var selector = legendModel.get('selector', true);\n    var selectorPosition = legendModel.get('selectorPosition', true);\n    if (selector && (!selectorPosition || selectorPosition === 'auto')) {\n      selectorPosition = orient === 'horizontal' ? 'end' : 'start';\n    }\n    this.renderInner(itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition);\n    // Perform layout.\n    var positionInfo = legendModel.getBoxLayoutParams();\n    var viewportSize = {\n      width: api.getWidth(),\n      height: api.getHeight()\n    };\n    var padding = legendModel.get('padding');\n    var maxSize = layoutUtil.getLayoutRect(positionInfo, viewportSize, padding);\n    var mainRect = this.layoutInner(legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition);\n    // Place mainGroup, based on the calculated `mainRect`.\n    var layoutRect = layoutUtil.getLayoutRect(zrUtil.defaults({\n      width: mainRect.width,\n      height: mainRect.height\n    }, positionInfo), viewportSize, padding);\n    this.group.x = layoutRect.x - mainRect.x;\n    this.group.y = layoutRect.y - mainRect.y;\n    this.group.markRedraw();\n    // Render background after group is layout.\n    this.group.add(this._backgroundEl = makeBackground(mainRect, legendModel));\n  };\n  LegendView.prototype.resetInner = function () {\n    this.getContentGroup().removeAll();\n    this._backgroundEl && this.group.remove(this._backgroundEl);\n    this.getSelectorGroup().removeAll();\n  };\n  LegendView.prototype.renderInner = function (itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition) {\n    var contentGroup = this.getContentGroup();\n    var legendDrawnMap = zrUtil.createHashMap();\n    var selectMode = legendModel.get('selectedMode');\n    var excludeSeriesId = [];\n    ecModel.eachRawSeries(function (seriesModel) {\n      !seriesModel.get('legendHoverLink') && excludeSeriesId.push(seriesModel.id);\n    });\n    each(legendModel.getData(), function (legendItemModel, dataIndex) {\n      var name = legendItemModel.get('name');\n      // Use empty string or \\n as a newline string\n      if (!this.newlineDisabled && (name === '' || name === '\\n')) {\n        var g = new Group();\n        // @ts-ignore\n        g.newline = true;\n        contentGroup.add(g);\n        return;\n      }\n      // Representitive series.\n      var seriesModel = ecModel.getSeriesByName(name)[0];\n      if (legendDrawnMap.get(name)) {\n        // Have been drawn\n        return;\n      }\n      // Legend to control series.\n      if (seriesModel) {\n        var data = seriesModel.getData();\n        var lineVisualStyle = data.getVisual('legendLineStyle') || {};\n        var legendIcon = data.getVisual('legendIcon');\n        /**\n         * `data.getVisual('style')` may be the color from the register\n         * in series. For example, for line series,\n         */\n        var style = data.getVisual('style');\n        var itemGroup = this._createItem(seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, lineVisualStyle, style, legendIcon, selectMode, api);\n        itemGroup.on('click', curry(dispatchSelectAction, name, null, api, excludeSeriesId)).on('mouseover', curry(dispatchHighlightAction, seriesModel.name, null, api, excludeSeriesId)).on('mouseout', curry(dispatchDownplayAction, seriesModel.name, null, api, excludeSeriesId));\n        if (ecModel.ssr) {\n          itemGroup.eachChild(function (child) {\n            var ecData = getECData(child);\n            ecData.seriesIndex = seriesModel.seriesIndex;\n            ecData.dataIndex = dataIndex;\n            ecData.ssrType = 'legend';\n          });\n        }\n        legendDrawnMap.set(name, true);\n      } else {\n        // Legend to control data. In pie and funnel.\n        ecModel.eachRawSeries(function (seriesModel) {\n          // In case multiple series has same data name\n          if (legendDrawnMap.get(name)) {\n            return;\n          }\n          if (seriesModel.legendVisualProvider) {\n            var provider = seriesModel.legendVisualProvider;\n            if (!provider.containName(name)) {\n              return;\n            }\n            var idx = provider.indexOfName(name);\n            var style = provider.getItemVisual(idx, 'style');\n            var legendIcon = provider.getItemVisual(idx, 'legendIcon');\n            var colorArr = parse(style.fill);\n            // Color may be set to transparent in visualMap when data is out of range.\n            // Do not show nothing.\n            if (colorArr && colorArr[3] === 0) {\n              colorArr[3] = 0.2;\n              // TODO color is set to 0, 0, 0, 0. Should show correct RGBA\n              style = zrUtil.extend(zrUtil.extend({}, style), {\n                fill: stringify(colorArr, 'rgba')\n              });\n            }\n            var itemGroup = this._createItem(seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, {}, style, legendIcon, selectMode, api);\n            // FIXME: consider different series has items with the same name.\n            itemGroup.on('click', curry(dispatchSelectAction, null, name, api, excludeSeriesId))\n            // Should not specify the series name, consider legend controls\n            // more than one pie series.\n            .on('mouseover', curry(dispatchHighlightAction, null, name, api, excludeSeriesId)).on('mouseout', curry(dispatchDownplayAction, null, name, api, excludeSeriesId));\n            if (ecModel.ssr) {\n              itemGroup.eachChild(function (child) {\n                var ecData = getECData(child);\n                ecData.seriesIndex = seriesModel.seriesIndex;\n                ecData.dataIndex = dataIndex;\n                ecData.ssrType = 'legend';\n              });\n            }\n            legendDrawnMap.set(name, true);\n          }\n        }, this);\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (!legendDrawnMap.get(name)) {\n          console.warn(name + ' series not exists. Legend data should be same with series name or data name.');\n        }\n      }\n    }, this);\n    if (selector) {\n      this._createSelector(selector, legendModel, api, orient, selectorPosition);\n    }\n  };\n  LegendView.prototype._createSelector = function (selector, legendModel, api, orient, selectorPosition) {\n    var selectorGroup = this.getSelectorGroup();\n    each(selector, function createSelectorButton(selectorItem) {\n      var type = selectorItem.type;\n      var labelText = new graphic.Text({\n        style: {\n          x: 0,\n          y: 0,\n          align: 'center',\n          verticalAlign: 'middle'\n        },\n        onclick: function () {\n          api.dispatchAction({\n            type: type === 'all' ? 'legendAllSelect' : 'legendInverseSelect'\n          });\n        }\n      });\n      selectorGroup.add(labelText);\n      var labelModel = legendModel.getModel('selectorLabel');\n      var emphasisLabelModel = legendModel.getModel(['emphasis', 'selectorLabel']);\n      setLabelStyle(labelText, {\n        normal: labelModel,\n        emphasis: emphasisLabelModel\n      }, {\n        defaultText: selectorItem.title\n      });\n      enableHoverEmphasis(labelText);\n    });\n  };\n  LegendView.prototype._createItem = function (seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, lineVisualStyle, itemVisualStyle, legendIcon, selectMode, api) {\n    var drawType = seriesModel.visualDrawType;\n    var itemWidth = legendModel.get('itemWidth');\n    var itemHeight = legendModel.get('itemHeight');\n    var isSelected = legendModel.isSelected(name);\n    var iconRotate = legendItemModel.get('symbolRotate');\n    var symbolKeepAspect = legendItemModel.get('symbolKeepAspect');\n    var legendIconType = legendItemModel.get('icon');\n    legendIcon = legendIconType || legendIcon || 'roundRect';\n    var style = getLegendStyle(legendIcon, legendItemModel, lineVisualStyle, itemVisualStyle, drawType, isSelected, api);\n    var itemGroup = new Group();\n    var textStyleModel = legendItemModel.getModel('textStyle');\n    if (zrUtil.isFunction(seriesModel.getLegendIcon) && (!legendIconType || legendIconType === 'inherit')) {\n      // Series has specific way to define legend icon\n      itemGroup.add(seriesModel.getLegendIcon({\n        itemWidth: itemWidth,\n        itemHeight: itemHeight,\n        icon: legendIcon,\n        iconRotate: iconRotate,\n        itemStyle: style.itemStyle,\n        lineStyle: style.lineStyle,\n        symbolKeepAspect: symbolKeepAspect\n      }));\n    } else {\n      // Use default legend icon policy for most series\n      var rotate = legendIconType === 'inherit' && seriesModel.getData().getVisual('symbol') ? iconRotate === 'inherit' ? seriesModel.getData().getVisual('symbolRotate') : iconRotate : 0; // No rotation for no icon\n      itemGroup.add(getDefaultLegendIcon({\n        itemWidth: itemWidth,\n        itemHeight: itemHeight,\n        icon: legendIcon,\n        iconRotate: rotate,\n        itemStyle: style.itemStyle,\n        lineStyle: style.lineStyle,\n        symbolKeepAspect: symbolKeepAspect\n      }));\n    }\n    var textX = itemAlign === 'left' ? itemWidth + 5 : -5;\n    var textAlign = itemAlign;\n    var formatter = legendModel.get('formatter');\n    var content = name;\n    if (zrUtil.isString(formatter) && formatter) {\n      content = formatter.replace('{name}', name != null ? name : '');\n    } else if (zrUtil.isFunction(formatter)) {\n      content = formatter(name);\n    }\n    var textColor = isSelected ? textStyleModel.getTextColor() : legendItemModel.get('inactiveColor');\n    itemGroup.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        text: content,\n        x: textX,\n        y: itemHeight / 2,\n        fill: textColor,\n        align: textAlign,\n        verticalAlign: 'middle'\n      }, {\n        inheritColor: textColor\n      })\n    }));\n    // Add a invisible rect to increase the area of mouse hover\n    var hitRect = new graphic.Rect({\n      shape: itemGroup.getBoundingRect(),\n      style: {\n        // Cannot use 'invisible' because SVG SSR will miss the node\n        fill: 'transparent'\n      }\n    });\n    var tooltipModel = legendItemModel.getModel('tooltip');\n    if (tooltipModel.get('show')) {\n      graphic.setTooltipConfig({\n        el: hitRect,\n        componentModel: legendModel,\n        itemName: name,\n        itemTooltipOption: tooltipModel.option\n      });\n    }\n    itemGroup.add(hitRect);\n    itemGroup.eachChild(function (child) {\n      child.silent = true;\n    });\n    hitRect.silent = !selectMode;\n    this.getContentGroup().add(itemGroup);\n    enableHoverEmphasis(itemGroup);\n    // @ts-ignore\n    itemGroup.__legendDataIndex = dataIndex;\n    return itemGroup;\n  };\n  LegendView.prototype.layoutInner = function (legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition) {\n    var contentGroup = this.getContentGroup();\n    var selectorGroup = this.getSelectorGroup();\n    // Place items in contentGroup.\n    layoutUtil.box(legendModel.get('orient'), contentGroup, legendModel.get('itemGap'), maxSize.width, maxSize.height);\n    var contentRect = contentGroup.getBoundingRect();\n    var contentPos = [-contentRect.x, -contentRect.y];\n    selectorGroup.markRedraw();\n    contentGroup.markRedraw();\n    if (selector) {\n      // Place buttons in selectorGroup\n      layoutUtil.box(\n      // Buttons in selectorGroup always layout horizontally\n      'horizontal', selectorGroup, legendModel.get('selectorItemGap', true));\n      var selectorRect = selectorGroup.getBoundingRect();\n      var selectorPos = [-selectorRect.x, -selectorRect.y];\n      var selectorButtonGap = legendModel.get('selectorButtonGap', true);\n      var orientIdx = legendModel.getOrient().index;\n      var wh = orientIdx === 0 ? 'width' : 'height';\n      var hw = orientIdx === 0 ? 'height' : 'width';\n      var yx = orientIdx === 0 ? 'y' : 'x';\n      if (selectorPosition === 'end') {\n        selectorPos[orientIdx] += contentRect[wh] + selectorButtonGap;\n      } else {\n        contentPos[orientIdx] += selectorRect[wh] + selectorButtonGap;\n      }\n      // Always align selector to content as 'middle'\n      selectorPos[1 - orientIdx] += contentRect[hw] / 2 - selectorRect[hw] / 2;\n      selectorGroup.x = selectorPos[0];\n      selectorGroup.y = selectorPos[1];\n      contentGroup.x = contentPos[0];\n      contentGroup.y = contentPos[1];\n      var mainRect = {\n        x: 0,\n        y: 0\n      };\n      mainRect[wh] = contentRect[wh] + selectorButtonGap + selectorRect[wh];\n      mainRect[hw] = Math.max(contentRect[hw], selectorRect[hw]);\n      mainRect[yx] = Math.min(0, selectorRect[yx] + selectorPos[1 - orientIdx]);\n      return mainRect;\n    } else {\n      contentGroup.x = contentPos[0];\n      contentGroup.y = contentPos[1];\n      return this.group.getBoundingRect();\n    }\n  };\n  /**\n   * @protected\n   */\n  LegendView.prototype.remove = function () {\n    this.getContentGroup().removeAll();\n    this._isFirstRender = true;\n  };\n  LegendView.type = 'legend.plain';\n  return LegendView;\n}(ComponentView);\nfunction getLegendStyle(iconType, legendItemModel, lineVisualStyle, itemVisualStyle, drawType, isSelected, api) {\n  /**\n   * Use series style if is inherit;\n   * elsewise, use legend style\n   */\n  function handleCommonProps(style, visualStyle) {\n    // If lineStyle.width is 'auto', it is set to be 2 if series has border\n    if (style.lineWidth === 'auto') {\n      style.lineWidth = visualStyle.lineWidth > 0 ? 2 : 0;\n    }\n    each(style, function (propVal, propName) {\n      style[propName] === 'inherit' && (style[propName] = visualStyle[propName]);\n    });\n  }\n  // itemStyle\n  var itemStyleModel = legendItemModel.getModel('itemStyle');\n  var itemStyle = itemStyleModel.getItemStyle();\n  var iconBrushType = iconType.lastIndexOf('empty', 0) === 0 ? 'fill' : 'stroke';\n  var decalStyle = itemStyleModel.getShallow('decal');\n  itemStyle.decal = !decalStyle || decalStyle === 'inherit' ? itemVisualStyle.decal : createOrUpdatePatternFromDecal(decalStyle, api);\n  if (itemStyle.fill === 'inherit') {\n    /**\n     * Series with visualDrawType as 'stroke' should have\n     * series stroke as legend fill\n     */\n    itemStyle.fill = itemVisualStyle[drawType];\n  }\n  if (itemStyle.stroke === 'inherit') {\n    /**\n     * icon type with \"emptyXXX\" should use fill color\n     * in visual style\n     */\n    itemStyle.stroke = itemVisualStyle[iconBrushType];\n  }\n  if (itemStyle.opacity === 'inherit') {\n    /**\n     * Use lineStyle.opacity if drawType is stroke\n     */\n    itemStyle.opacity = (drawType === 'fill' ? itemVisualStyle : lineVisualStyle).opacity;\n  }\n  handleCommonProps(itemStyle, itemVisualStyle);\n  // lineStyle\n  var legendLineModel = legendItemModel.getModel('lineStyle');\n  var lineStyle = legendLineModel.getLineStyle();\n  handleCommonProps(lineStyle, lineVisualStyle);\n  // Fix auto color to real color\n  itemStyle.fill === 'auto' && (itemStyle.fill = itemVisualStyle.fill);\n  itemStyle.stroke === 'auto' && (itemStyle.stroke = itemVisualStyle.fill);\n  lineStyle.stroke === 'auto' && (lineStyle.stroke = itemVisualStyle.fill);\n  if (!isSelected) {\n    var borderWidth = legendItemModel.get('inactiveBorderWidth');\n    /**\n     * Since stroke is set to be inactiveBorderColor, it may occur that\n     * there is no border in series but border in legend, so we need to\n     * use border only when series has border if is set to be auto\n     */\n    var visualHasBorder = itemStyle[iconBrushType];\n    itemStyle.lineWidth = borderWidth === 'auto' ? itemVisualStyle.lineWidth > 0 && visualHasBorder ? 2 : 0 : itemStyle.lineWidth;\n    itemStyle.fill = legendItemModel.get('inactiveColor');\n    itemStyle.stroke = legendItemModel.get('inactiveBorderColor');\n    lineStyle.stroke = legendLineModel.get('inactiveColor');\n    lineStyle.lineWidth = legendLineModel.get('inactiveWidth');\n  }\n  return {\n    itemStyle: itemStyle,\n    lineStyle: lineStyle\n  };\n}\nfunction getDefaultLegendIcon(opt) {\n  var symboType = opt.icon || 'roundRect';\n  var icon = createSymbol(symboType, 0, 0, opt.itemWidth, opt.itemHeight, opt.itemStyle.fill, opt.symbolKeepAspect);\n  icon.setStyle(opt.itemStyle);\n  icon.rotation = (opt.iconRotate || 0) * Math.PI / 180;\n  icon.setOrigin([opt.itemWidth / 2, opt.itemHeight / 2]);\n  if (symboType.indexOf('empty') > -1) {\n    icon.style.stroke = icon.style.fill;\n    icon.style.fill = '#fff';\n    icon.style.lineWidth = 2;\n  }\n  return icon;\n}\nfunction dispatchSelectAction(seriesName, dataName, api, excludeSeriesId) {\n  // downplay before unselect\n  dispatchDownplayAction(seriesName, dataName, api, excludeSeriesId);\n  api.dispatchAction({\n    type: 'legendToggleSelect',\n    name: seriesName != null ? seriesName : dataName\n  });\n  // highlight after select\n  // TODO highlight immediately may cause animation loss.\n  dispatchHighlightAction(seriesName, dataName, api, excludeSeriesId);\n}\nfunction isUseHoverLayer(api) {\n  var list = api.getZr().storage.getDisplayList();\n  var emphasisState;\n  var i = 0;\n  var len = list.length;\n  while (i < len && !(emphasisState = list[i].states.emphasis)) {\n    i++;\n  }\n  return emphasisState && emphasisState.hoverLayer;\n}\nfunction dispatchHighlightAction(seriesName, dataName, api, excludeSeriesId) {\n  // If element hover will move to a hoverLayer.\n  if (!isUseHoverLayer(api)) {\n    api.dispatchAction({\n      type: 'highlight',\n      seriesName: seriesName,\n      name: dataName,\n      excludeSeriesId: excludeSeriesId\n    });\n  }\n}\nfunction dispatchDownplayAction(seriesName, dataName, api, excludeSeriesId) {\n  // If element hover will move to a hoverLayer.\n  if (!isUseHoverLayer(api)) {\n    api.dispatchAction({\n      type: 'downplay',\n      seriesName: seriesName,\n      name: dataName,\n      excludeSeriesId: excludeSeriesId\n    });\n  }\n}\nexport default LegendView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,KAAK,EAAEC,SAAS,QAAQ,2BAA2B;AAC5D,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,aAAa,EAAEC,eAAe,QAAQ,2BAA2B;AAC1E,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,8BAA8B,QAAQ,qBAAqB;AACpE,SAASC,SAAS,QAAQ,0BAA0B;AACpD,IAAIC,KAAK,GAAGb,MAAM,CAACa,KAAK;AACxB,IAAIC,IAAI,GAAGd,MAAM,CAACc,IAAI;AACtB,IAAIC,KAAK,GAAGZ,OAAO,CAACY,KAAK;AACzB,IAAIC,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9ClB,SAAS,CAACiB,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IACpB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,UAAU,CAACK,IAAI;IAC5BH,KAAK,CAACI,eAAe,GAAG,KAAK;IAC7B,OAAOJ,KAAK;EACd;EACAF,UAAU,CAACO,SAAS,CAACC,IAAI,GAAG,YAAY;IACtC,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC,IAAI,CAACC,aAAa,GAAG,IAAIZ,KAAK,CAAC,CAAC,CAAC;IAChD,IAAI,CAACU,KAAK,CAACC,GAAG,CAAC,IAAI,CAACE,cAAc,GAAG,IAAIb,KAAK,CAAC,CAAC,CAAC;IACjD,IAAI,CAACc,cAAc,GAAG,IAAI;EAC5B,CAAC;EACD;AACF;AACA;EACEb,UAAU,CAACO,SAAS,CAACO,eAAe,GAAG,YAAY;IACjD,OAAO,IAAI,CAACH,aAAa;EAC3B,CAAC;EACD;AACF;AACA;EACEX,UAAU,CAACO,SAAS,CAACQ,gBAAgB,GAAG,YAAY;IAClD,OAAO,IAAI,CAACH,cAAc;EAC5B,CAAC;EACD;AACF;AACA;EACEZ,UAAU,CAACO,SAAS,CAACS,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACjE,IAAIC,aAAa,GAAG,IAAI,CAACP,cAAc;IACvC,IAAI,CAACA,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACQ,UAAU,CAAC,CAAC;IACjB,IAAI,CAACJ,WAAW,CAACK,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;MAClC;IACF;IACA,IAAIC,SAAS,GAAGN,WAAW,CAACK,GAAG,CAAC,OAAO,CAAC;IACxC,IAAIE,MAAM,GAAGP,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC;IACtC,IAAI,CAACC,SAAS,IAAIA,SAAS,KAAK,MAAM,EAAE;MACtCA,SAAS,GAAGN,WAAW,CAACK,GAAG,CAAC,MAAM,CAAC,KAAK,OAAO,IAAIE,MAAM,KAAK,UAAU,GAAG,OAAO,GAAG,MAAM;IAC7F;IACA;IACA,IAAIC,QAAQ,GAAGR,WAAW,CAACK,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;IAChD,IAAII,gBAAgB,GAAGT,WAAW,CAACK,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC;IAChE,IAAIG,QAAQ,KAAK,CAACC,gBAAgB,IAAIA,gBAAgB,KAAK,MAAM,CAAC,EAAE;MAClEA,gBAAgB,GAAGF,MAAM,KAAK,YAAY,GAAG,KAAK,GAAG,OAAO;IAC9D;IACA,IAAI,CAACG,WAAW,CAACJ,SAAS,EAAEN,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEM,QAAQ,EAAED,MAAM,EAAEE,gBAAgB,CAAC;IAC1F;IACA,IAAIE,YAAY,GAAGX,WAAW,CAACY,kBAAkB,CAAC,CAAC;IACnD,IAAIC,YAAY,GAAG;MACjBC,KAAK,EAAEZ,GAAG,CAACa,QAAQ,CAAC,CAAC;MACrBC,MAAM,EAAEd,GAAG,CAACe,SAAS,CAAC;IACxB,CAAC;IACD,IAAIC,OAAO,GAAGlB,WAAW,CAACK,GAAG,CAAC,SAAS,CAAC;IACxC,IAAIc,OAAO,GAAG5C,UAAU,CAAC6C,aAAa,CAACT,YAAY,EAAEE,YAAY,EAAEK,OAAO,CAAC;IAC3E,IAAIG,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACtB,WAAW,EAAEM,SAAS,EAAEa,OAAO,EAAEhB,aAAa,EAAEK,QAAQ,EAAEC,gBAAgB,CAAC;IAC3G;IACA,IAAIc,UAAU,GAAGhD,UAAU,CAAC6C,aAAa,CAACrD,MAAM,CAACyD,QAAQ,CAAC;MACxDV,KAAK,EAAEO,QAAQ,CAACP,KAAK;MACrBE,MAAM,EAAEK,QAAQ,CAACL;IACnB,CAAC,EAAEL,YAAY,CAAC,EAAEE,YAAY,EAAEK,OAAO,CAAC;IACxC,IAAI,CAAC1B,KAAK,CAACiC,CAAC,GAAGF,UAAU,CAACE,CAAC,GAAGJ,QAAQ,CAACI,CAAC;IACxC,IAAI,CAACjC,KAAK,CAACkC,CAAC,GAAGH,UAAU,CAACG,CAAC,GAAGL,QAAQ,CAACK,CAAC;IACxC,IAAI,CAAClC,KAAK,CAACmC,UAAU,CAAC,CAAC;IACvB;IACA,IAAI,CAACnC,KAAK,CAACC,GAAG,CAAC,IAAI,CAACmC,aAAa,GAAGtD,cAAc,CAAC+C,QAAQ,EAAErB,WAAW,CAAC,CAAC;EAC5E,CAAC;EACDjB,UAAU,CAACO,SAAS,CAACc,UAAU,GAAG,YAAY;IAC5C,IAAI,CAACP,eAAe,CAAC,CAAC,CAACgC,SAAS,CAAC,CAAC;IAClC,IAAI,CAACD,aAAa,IAAI,IAAI,CAACpC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAACF,aAAa,CAAC;IAC3D,IAAI,CAAC9B,gBAAgB,CAAC,CAAC,CAAC+B,SAAS,CAAC,CAAC;EACrC,CAAC;EACD9C,UAAU,CAACO,SAAS,CAACoB,WAAW,GAAG,UAAUJ,SAAS,EAAEN,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEM,QAAQ,EAAED,MAAM,EAAEE,gBAAgB,EAAE;IACrH,IAAIsB,YAAY,GAAG,IAAI,CAAClC,eAAe,CAAC,CAAC;IACzC,IAAImC,cAAc,GAAGjE,MAAM,CAACkE,aAAa,CAAC,CAAC;IAC3C,IAAIC,UAAU,GAAGlC,WAAW,CAACK,GAAG,CAAC,cAAc,CAAC;IAChD,IAAI8B,eAAe,GAAG,EAAE;IACxBlC,OAAO,CAACmC,aAAa,CAAC,UAAUC,WAAW,EAAE;MAC3C,CAACA,WAAW,CAAChC,GAAG,CAAC,iBAAiB,CAAC,IAAI8B,eAAe,CAACG,IAAI,CAACD,WAAW,CAACE,EAAE,CAAC;IAC7E,CAAC,CAAC;IACF1D,IAAI,CAACmB,WAAW,CAACwC,OAAO,CAAC,CAAC,EAAE,UAAUC,eAAe,EAAEC,SAAS,EAAE;MAChE,IAAIC,IAAI,GAAGF,eAAe,CAACpC,GAAG,CAAC,MAAM,CAAC;MACtC;MACA,IAAI,CAAC,IAAI,CAAChB,eAAe,KAAKsD,IAAI,KAAK,EAAE,IAAIA,IAAI,KAAK,IAAI,CAAC,EAAE;QAC3D,IAAIC,CAAC,GAAG,IAAI9D,KAAK,CAAC,CAAC;QACnB;QACA8D,CAAC,CAACC,OAAO,GAAG,IAAI;QAChBd,YAAY,CAACtC,GAAG,CAACmD,CAAC,CAAC;QACnB;MACF;MACA;MACA,IAAIP,WAAW,GAAGpC,OAAO,CAAC6C,eAAe,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC;MAClD,IAAIX,cAAc,CAAC3B,GAAG,CAACsC,IAAI,CAAC,EAAE;QAC5B;QACA;MACF;MACA;MACA,IAAIN,WAAW,EAAE;QACf,IAAIU,IAAI,GAAGV,WAAW,CAACG,OAAO,CAAC,CAAC;QAChC,IAAIQ,eAAe,GAAGD,IAAI,CAACE,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7D,IAAIC,UAAU,GAAGH,IAAI,CAACE,SAAS,CAAC,YAAY,CAAC;QAC7C;AACR;AACA;AACA;QACQ,IAAIE,KAAK,GAAGJ,IAAI,CAACE,SAAS,CAAC,OAAO,CAAC;QACnC,IAAIG,SAAS,GAAG,IAAI,CAACC,WAAW,CAAChB,WAAW,EAAEM,IAAI,EAAED,SAAS,EAAED,eAAe,EAAEzC,WAAW,EAAEM,SAAS,EAAE0C,eAAe,EAAEG,KAAK,EAAED,UAAU,EAAEhB,UAAU,EAAEhC,GAAG,CAAC;QAC5JkD,SAAS,CAACE,EAAE,CAAC,OAAO,EAAE1E,KAAK,CAAC2E,oBAAoB,EAAEZ,IAAI,EAAE,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC,CAACmB,EAAE,CAAC,WAAW,EAAE1E,KAAK,CAAC4E,uBAAuB,EAAEnB,WAAW,CAACM,IAAI,EAAE,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC,CAACmB,EAAE,CAAC,UAAU,EAAE1E,KAAK,CAAC6E,sBAAsB,EAAEpB,WAAW,CAACM,IAAI,EAAE,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC;QAC9Q,IAAIlC,OAAO,CAACyD,GAAG,EAAE;UACfN,SAAS,CAACO,SAAS,CAAC,UAAUC,KAAK,EAAE;YACnC,IAAIC,MAAM,GAAGlF,SAAS,CAACiF,KAAK,CAAC;YAC7BC,MAAM,CAACC,WAAW,GAAGzB,WAAW,CAACyB,WAAW;YAC5CD,MAAM,CAACnB,SAAS,GAAGA,SAAS;YAC5BmB,MAAM,CAACE,OAAO,GAAG,QAAQ;UAC3B,CAAC,CAAC;QACJ;QACA/B,cAAc,CAACgC,GAAG,CAACrB,IAAI,EAAE,IAAI,CAAC;MAChC,CAAC,MAAM;QACL;QACA1C,OAAO,CAACmC,aAAa,CAAC,UAAUC,WAAW,EAAE;UAC3C;UACA,IAAIL,cAAc,CAAC3B,GAAG,CAACsC,IAAI,CAAC,EAAE;YAC5B;UACF;UACA,IAAIN,WAAW,CAAC4B,oBAAoB,EAAE;YACpC,IAAIC,QAAQ,GAAG7B,WAAW,CAAC4B,oBAAoB;YAC/C,IAAI,CAACC,QAAQ,CAACC,WAAW,CAACxB,IAAI,CAAC,EAAE;cAC/B;YACF;YACA,IAAIyB,GAAG,GAAGF,QAAQ,CAACG,WAAW,CAAC1B,IAAI,CAAC;YACpC,IAAIQ,KAAK,GAAGe,QAAQ,CAACI,aAAa,CAACF,GAAG,EAAE,OAAO,CAAC;YAChD,IAAIlB,UAAU,GAAGgB,QAAQ,CAACI,aAAa,CAACF,GAAG,EAAE,YAAY,CAAC;YAC1D,IAAIG,QAAQ,GAAGvG,KAAK,CAACmF,KAAK,CAACqB,IAAI,CAAC;YAChC;YACA;YACA,IAAID,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;cACjCA,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG;cACjB;cACApB,KAAK,GAAGpF,MAAM,CAAC0G,MAAM,CAAC1G,MAAM,CAAC0G,MAAM,CAAC,CAAC,CAAC,EAAEtB,KAAK,CAAC,EAAE;gBAC9CqB,IAAI,EAAEvG,SAAS,CAACsG,QAAQ,EAAE,MAAM;cAClC,CAAC,CAAC;YACJ;YACA,IAAInB,SAAS,GAAG,IAAI,CAACC,WAAW,CAAChB,WAAW,EAAEM,IAAI,EAAED,SAAS,EAAED,eAAe,EAAEzC,WAAW,EAAEM,SAAS,EAAE,CAAC,CAAC,EAAE6C,KAAK,EAAED,UAAU,EAAEhB,UAAU,EAAEhC,GAAG,CAAC;YAC/I;YACAkD,SAAS,CAACE,EAAE,CAAC,OAAO,EAAE1E,KAAK,CAAC2E,oBAAoB,EAAE,IAAI,EAAEZ,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC;YACnF;YACA;YAAA,CACCmB,EAAE,CAAC,WAAW,EAAE1E,KAAK,CAAC4E,uBAAuB,EAAE,IAAI,EAAEb,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC,CAACmB,EAAE,CAAC,UAAU,EAAE1E,KAAK,CAAC6E,sBAAsB,EAAE,IAAI,EAAEd,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC;YAClK,IAAIlC,OAAO,CAACyD,GAAG,EAAE;cACfN,SAAS,CAACO,SAAS,CAAC,UAAUC,KAAK,EAAE;gBACnC,IAAIC,MAAM,GAAGlF,SAAS,CAACiF,KAAK,CAAC;gBAC7BC,MAAM,CAACC,WAAW,GAAGzB,WAAW,CAACyB,WAAW;gBAC5CD,MAAM,CAACnB,SAAS,GAAGA,SAAS;gBAC5BmB,MAAM,CAACE,OAAO,GAAG,QAAQ;cAC3B,CAAC,CAAC;YACJ;YACA/B,cAAc,CAACgC,GAAG,CAACrB,IAAI,EAAE,IAAI,CAAC;UAChC;QACF,CAAC,EAAE,IAAI,CAAC;MACV;MACA,IAAI+B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI,CAAC5C,cAAc,CAAC3B,GAAG,CAACsC,IAAI,CAAC,EAAE;UAC7BkC,OAAO,CAACC,IAAI,CAACnC,IAAI,GAAG,+EAA+E,CAAC;QACtG;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IACR,IAAInC,QAAQ,EAAE;MACZ,IAAI,CAACuE,eAAe,CAACvE,QAAQ,EAAER,WAAW,EAAEE,GAAG,EAAEK,MAAM,EAAEE,gBAAgB,CAAC;IAC5E;EACF,CAAC;EACD1B,UAAU,CAACO,SAAS,CAACyF,eAAe,GAAG,UAAUvE,QAAQ,EAAER,WAAW,EAAEE,GAAG,EAAEK,MAAM,EAAEE,gBAAgB,EAAE;IACrG,IAAIuE,aAAa,GAAG,IAAI,CAAClF,gBAAgB,CAAC,CAAC;IAC3CjB,IAAI,CAAC2B,QAAQ,EAAE,SAASyE,oBAAoBA,CAACC,YAAY,EAAE;MACzD,IAAI9F,IAAI,GAAG8F,YAAY,CAAC9F,IAAI;MAC5B,IAAI+F,SAAS,GAAG,IAAIjH,OAAO,CAACkH,IAAI,CAAC;QAC/BjC,KAAK,EAAE;UACL1B,CAAC,EAAE,CAAC;UACJC,CAAC,EAAE,CAAC;UACJ2D,KAAK,EAAE,QAAQ;UACfC,aAAa,EAAE;QACjB,CAAC;QACDC,OAAO,EAAE,SAAAA,CAAA,EAAY;UACnBrF,GAAG,CAACsF,cAAc,CAAC;YACjBpG,IAAI,EAAEA,IAAI,KAAK,KAAK,GAAG,iBAAiB,GAAG;UAC7C,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MACF4F,aAAa,CAACvF,GAAG,CAAC0F,SAAS,CAAC;MAC5B,IAAIM,UAAU,GAAGzF,WAAW,CAAC0F,QAAQ,CAAC,eAAe,CAAC;MACtD,IAAIC,kBAAkB,GAAG3F,WAAW,CAAC0F,QAAQ,CAAC,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;MAC5EtH,aAAa,CAAC+G,SAAS,EAAE;QACvBS,MAAM,EAAEH,UAAU;QAClBI,QAAQ,EAAEF;MACZ,CAAC,EAAE;QACDG,WAAW,EAAEZ,YAAY,CAACa;MAC5B,CAAC,CAAC;MACF5H,mBAAmB,CAACgH,SAAS,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC;EACDpG,UAAU,CAACO,SAAS,CAAC+D,WAAW,GAAG,UAAUhB,WAAW,EAAEM,IAAI,EAAED,SAAS,EAAED,eAAe,EAAEzC,WAAW,EAAEM,SAAS,EAAE0C,eAAe,EAAEgD,eAAe,EAAE9C,UAAU,EAAEhB,UAAU,EAAEhC,GAAG,EAAE;IACjL,IAAI+F,QAAQ,GAAG5D,WAAW,CAAC6D,cAAc;IACzC,IAAIC,SAAS,GAAGnG,WAAW,CAACK,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAI+F,UAAU,GAAGpG,WAAW,CAACK,GAAG,CAAC,YAAY,CAAC;IAC9C,IAAIgG,UAAU,GAAGrG,WAAW,CAACqG,UAAU,CAAC1D,IAAI,CAAC;IAC7C,IAAI2D,UAAU,GAAG7D,eAAe,CAACpC,GAAG,CAAC,cAAc,CAAC;IACpD,IAAIkG,gBAAgB,GAAG9D,eAAe,CAACpC,GAAG,CAAC,kBAAkB,CAAC;IAC9D,IAAImG,cAAc,GAAG/D,eAAe,CAACpC,GAAG,CAAC,MAAM,CAAC;IAChD6C,UAAU,GAAGsD,cAAc,IAAItD,UAAU,IAAI,WAAW;IACxD,IAAIC,KAAK,GAAGsD,cAAc,CAACvD,UAAU,EAAET,eAAe,EAAEO,eAAe,EAAEgD,eAAe,EAAEC,QAAQ,EAAEI,UAAU,EAAEnG,GAAG,CAAC;IACpH,IAAIkD,SAAS,GAAG,IAAItE,KAAK,CAAC,CAAC;IAC3B,IAAI4H,cAAc,GAAGjE,eAAe,CAACiD,QAAQ,CAAC,WAAW,CAAC;IAC1D,IAAI3H,MAAM,CAAC4I,UAAU,CAACtE,WAAW,CAACuE,aAAa,CAAC,KAAK,CAACJ,cAAc,IAAIA,cAAc,KAAK,SAAS,CAAC,EAAE;MACrG;MACApD,SAAS,CAAC3D,GAAG,CAAC4C,WAAW,CAACuE,aAAa,CAAC;QACtCT,SAAS,EAAEA,SAAS;QACpBC,UAAU,EAAEA,UAAU;QACtBS,IAAI,EAAE3D,UAAU;QAChBoD,UAAU,EAAEA,UAAU;QACtBQ,SAAS,EAAE3D,KAAK,CAAC2D,SAAS;QAC1BC,SAAS,EAAE5D,KAAK,CAAC4D,SAAS;QAC1BR,gBAAgB,EAAEA;MACpB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL;MACA,IAAIS,MAAM,GAAGR,cAAc,KAAK,SAAS,IAAInE,WAAW,CAACG,OAAO,CAAC,CAAC,CAACS,SAAS,CAAC,QAAQ,CAAC,GAAGqD,UAAU,KAAK,SAAS,GAAGjE,WAAW,CAACG,OAAO,CAAC,CAAC,CAACS,SAAS,CAAC,cAAc,CAAC,GAAGqD,UAAU,GAAG,CAAC,CAAC,CAAC;MACtLlD,SAAS,CAAC3D,GAAG,CAACwH,oBAAoB,CAAC;QACjCd,SAAS,EAAEA,SAAS;QACpBC,UAAU,EAAEA,UAAU;QACtBS,IAAI,EAAE3D,UAAU;QAChBoD,UAAU,EAAEU,MAAM;QAClBF,SAAS,EAAE3D,KAAK,CAAC2D,SAAS;QAC1BC,SAAS,EAAE5D,KAAK,CAAC4D,SAAS;QAC1BR,gBAAgB,EAAEA;MACpB,CAAC,CAAC,CAAC;IACL;IACA,IAAIW,KAAK,GAAG5G,SAAS,KAAK,MAAM,GAAG6F,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;IACrD,IAAIgB,SAAS,GAAG7G,SAAS;IACzB,IAAI8G,SAAS,GAAGpH,WAAW,CAACK,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAIgH,OAAO,GAAG1E,IAAI;IAClB,IAAI5E,MAAM,CAACuJ,QAAQ,CAACF,SAAS,CAAC,IAAIA,SAAS,EAAE;MAC3CC,OAAO,GAAGD,SAAS,CAACG,OAAO,CAAC,QAAQ,EAAE5E,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAG,EAAE,CAAC;IACjE,CAAC,MAAM,IAAI5E,MAAM,CAAC4I,UAAU,CAACS,SAAS,CAAC,EAAE;MACvCC,OAAO,GAAGD,SAAS,CAACzE,IAAI,CAAC;IAC3B;IACA,IAAI6E,SAAS,GAAGnB,UAAU,GAAGK,cAAc,CAACe,YAAY,CAAC,CAAC,GAAGhF,eAAe,CAACpC,GAAG,CAAC,eAAe,CAAC;IACjG+C,SAAS,CAAC3D,GAAG,CAAC,IAAIvB,OAAO,CAACkH,IAAI,CAAC;MAC7BjC,KAAK,EAAE9E,eAAe,CAACqI,cAAc,EAAE;QACrCgB,IAAI,EAAEL,OAAO;QACb5F,CAAC,EAAEyF,KAAK;QACRxF,CAAC,EAAE0E,UAAU,GAAG,CAAC;QACjB5B,IAAI,EAAEgD,SAAS;QACfnC,KAAK,EAAE8B,SAAS;QAChB7B,aAAa,EAAE;MACjB,CAAC,EAAE;QACDqC,YAAY,EAAEH;MAChB,CAAC;IACH,CAAC,CAAC,CAAC;IACH;IACA,IAAII,OAAO,GAAG,IAAI1J,OAAO,CAAC2J,IAAI,CAAC;MAC7BC,KAAK,EAAE1E,SAAS,CAAC2E,eAAe,CAAC,CAAC;MAClC5E,KAAK,EAAE;QACL;QACAqB,IAAI,EAAE;MACR;IACF,CAAC,CAAC;IACF,IAAIwD,YAAY,GAAGvF,eAAe,CAACiD,QAAQ,CAAC,SAAS,CAAC;IACtD,IAAIsC,YAAY,CAAC3H,GAAG,CAAC,MAAM,CAAC,EAAE;MAC5BnC,OAAO,CAAC+J,gBAAgB,CAAC;QACvBC,EAAE,EAAEN,OAAO;QACXO,cAAc,EAAEnI,WAAW;QAC3BoI,QAAQ,EAAEzF,IAAI;QACd0F,iBAAiB,EAAEL,YAAY,CAACM;MAClC,CAAC,CAAC;IACJ;IACAlF,SAAS,CAAC3D,GAAG,CAACmI,OAAO,CAAC;IACtBxE,SAAS,CAACO,SAAS,CAAC,UAAUC,KAAK,EAAE;MACnCA,KAAK,CAAC2E,MAAM,GAAG,IAAI;IACrB,CAAC,CAAC;IACFX,OAAO,CAACW,MAAM,GAAG,CAACrG,UAAU;IAC5B,IAAI,CAACrC,eAAe,CAAC,CAAC,CAACJ,GAAG,CAAC2D,SAAS,CAAC;IACrCjF,mBAAmB,CAACiF,SAAS,CAAC;IAC9B;IACAA,SAAS,CAACoF,iBAAiB,GAAG9F,SAAS;IACvC,OAAOU,SAAS;EAClB,CAAC;EACDrE,UAAU,CAACO,SAAS,CAACgC,WAAW,GAAG,UAAUtB,WAAW,EAAEM,SAAS,EAAEa,OAAO,EAAEhB,aAAa,EAAEK,QAAQ,EAAEC,gBAAgB,EAAE;IACvH,IAAIsB,YAAY,GAAG,IAAI,CAAClC,eAAe,CAAC,CAAC;IACzC,IAAImF,aAAa,GAAG,IAAI,CAAClF,gBAAgB,CAAC,CAAC;IAC3C;IACAvB,UAAU,CAACkK,GAAG,CAACzI,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC,EAAE0B,YAAY,EAAE/B,WAAW,CAACK,GAAG,CAAC,SAAS,CAAC,EAAEc,OAAO,CAACL,KAAK,EAAEK,OAAO,CAACH,MAAM,CAAC;IAClH,IAAI0H,WAAW,GAAG3G,YAAY,CAACgG,eAAe,CAAC,CAAC;IAChD,IAAIY,UAAU,GAAG,CAAC,CAACD,WAAW,CAACjH,CAAC,EAAE,CAACiH,WAAW,CAAChH,CAAC,CAAC;IACjDsD,aAAa,CAACrD,UAAU,CAAC,CAAC;IAC1BI,YAAY,CAACJ,UAAU,CAAC,CAAC;IACzB,IAAInB,QAAQ,EAAE;MACZ;MACAjC,UAAU,CAACkK,GAAG;MACd;MACA,YAAY,EAAEzD,aAAa,EAAEhF,WAAW,CAACK,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;MACtE,IAAIuI,YAAY,GAAG5D,aAAa,CAAC+C,eAAe,CAAC,CAAC;MAClD,IAAIc,WAAW,GAAG,CAAC,CAACD,YAAY,CAACnH,CAAC,EAAE,CAACmH,YAAY,CAAClH,CAAC,CAAC;MACpD,IAAIoH,iBAAiB,GAAG9I,WAAW,CAACK,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;MAClE,IAAI0I,SAAS,GAAG/I,WAAW,CAACgJ,SAAS,CAAC,CAAC,CAACC,KAAK;MAC7C,IAAIC,EAAE,GAAGH,SAAS,KAAK,CAAC,GAAG,OAAO,GAAG,QAAQ;MAC7C,IAAII,EAAE,GAAGJ,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,OAAO;MAC7C,IAAIK,EAAE,GAAGL,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG;MACpC,IAAItI,gBAAgB,KAAK,KAAK,EAAE;QAC9BoI,WAAW,CAACE,SAAS,CAAC,IAAIL,WAAW,CAACQ,EAAE,CAAC,GAAGJ,iBAAiB;MAC/D,CAAC,MAAM;QACLH,UAAU,CAACI,SAAS,CAAC,IAAIH,YAAY,CAACM,EAAE,CAAC,GAAGJ,iBAAiB;MAC/D;MACA;MACAD,WAAW,CAAC,CAAC,GAAGE,SAAS,CAAC,IAAIL,WAAW,CAACS,EAAE,CAAC,GAAG,CAAC,GAAGP,YAAY,CAACO,EAAE,CAAC,GAAG,CAAC;MACxEnE,aAAa,CAACvD,CAAC,GAAGoH,WAAW,CAAC,CAAC,CAAC;MAChC7D,aAAa,CAACtD,CAAC,GAAGmH,WAAW,CAAC,CAAC,CAAC;MAChC9G,YAAY,CAACN,CAAC,GAAGkH,UAAU,CAAC,CAAC,CAAC;MAC9B5G,YAAY,CAACL,CAAC,GAAGiH,UAAU,CAAC,CAAC,CAAC;MAC9B,IAAItH,QAAQ,GAAG;QACbI,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE;MACL,CAAC;MACDL,QAAQ,CAAC6H,EAAE,CAAC,GAAGR,WAAW,CAACQ,EAAE,CAAC,GAAGJ,iBAAiB,GAAGF,YAAY,CAACM,EAAE,CAAC;MACrE7H,QAAQ,CAAC8H,EAAE,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACZ,WAAW,CAACS,EAAE,CAAC,EAAEP,YAAY,CAACO,EAAE,CAAC,CAAC;MAC1D9H,QAAQ,CAAC+H,EAAE,CAAC,GAAGC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEX,YAAY,CAACQ,EAAE,CAAC,GAAGP,WAAW,CAAC,CAAC,GAAGE,SAAS,CAAC,CAAC;MACzE,OAAO1H,QAAQ;IACjB,CAAC,MAAM;MACLU,YAAY,CAACN,CAAC,GAAGkH,UAAU,CAAC,CAAC,CAAC;MAC9B5G,YAAY,CAACL,CAAC,GAAGiH,UAAU,CAAC,CAAC,CAAC;MAC9B,OAAO,IAAI,CAACnJ,KAAK,CAACuI,eAAe,CAAC,CAAC;IACrC;EACF,CAAC;EACD;AACF;AACA;EACEhJ,UAAU,CAACO,SAAS,CAACwC,MAAM,GAAG,YAAY;IACxC,IAAI,CAACjC,eAAe,CAAC,CAAC,CAACgC,SAAS,CAAC,CAAC;IAClC,IAAI,CAACjC,cAAc,GAAG,IAAI;EAC5B,CAAC;EACDb,UAAU,CAACK,IAAI,GAAG,cAAc;EAChC,OAAOL,UAAU;AACnB,CAAC,CAACP,aAAa,CAAC;AAChB,SAASiI,cAAcA,CAAC+C,QAAQ,EAAE/G,eAAe,EAAEO,eAAe,EAAEgD,eAAe,EAAEC,QAAQ,EAAEI,UAAU,EAAEnG,GAAG,EAAE;EAC9G;AACF;AACA;AACA;EACE,SAASuJ,iBAAiBA,CAACtG,KAAK,EAAEuG,WAAW,EAAE;IAC7C;IACA,IAAIvG,KAAK,CAACwG,SAAS,KAAK,MAAM,EAAE;MAC9BxG,KAAK,CAACwG,SAAS,GAAGD,WAAW,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACrD;IACA9K,IAAI,CAACsE,KAAK,EAAE,UAAUyG,OAAO,EAAEC,QAAQ,EAAE;MACvC1G,KAAK,CAAC0G,QAAQ,CAAC,KAAK,SAAS,KAAK1G,KAAK,CAAC0G,QAAQ,CAAC,GAAGH,WAAW,CAACG,QAAQ,CAAC,CAAC;IAC5E,CAAC,CAAC;EACJ;EACA;EACA,IAAIC,cAAc,GAAGrH,eAAe,CAACiD,QAAQ,CAAC,WAAW,CAAC;EAC1D,IAAIoB,SAAS,GAAGgD,cAAc,CAACC,YAAY,CAAC,CAAC;EAC7C,IAAIC,aAAa,GAAGR,QAAQ,CAACS,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,QAAQ;EAC9E,IAAIC,UAAU,GAAGJ,cAAc,CAACK,UAAU,CAAC,OAAO,CAAC;EACnDrD,SAAS,CAACsD,KAAK,GAAG,CAACF,UAAU,IAAIA,UAAU,KAAK,SAAS,GAAGlE,eAAe,CAACoE,KAAK,GAAG1L,8BAA8B,CAACwL,UAAU,EAAEhK,GAAG,CAAC;EACnI,IAAI4G,SAAS,CAACtC,IAAI,KAAK,SAAS,EAAE;IAChC;AACJ;AACA;AACA;IACIsC,SAAS,CAACtC,IAAI,GAAGwB,eAAe,CAACC,QAAQ,CAAC;EAC5C;EACA,IAAIa,SAAS,CAACuD,MAAM,KAAK,SAAS,EAAE;IAClC;AACJ;AACA;AACA;IACIvD,SAAS,CAACuD,MAAM,GAAGrE,eAAe,CAACgE,aAAa,CAAC;EACnD;EACA,IAAIlD,SAAS,CAACwD,OAAO,KAAK,SAAS,EAAE;IACnC;AACJ;AACA;IACIxD,SAAS,CAACwD,OAAO,GAAG,CAACrE,QAAQ,KAAK,MAAM,GAAGD,eAAe,GAAGhD,eAAe,EAAEsH,OAAO;EACvF;EACAb,iBAAiB,CAAC3C,SAAS,EAAEd,eAAe,CAAC;EAC7C;EACA,IAAIuE,eAAe,GAAG9H,eAAe,CAACiD,QAAQ,CAAC,WAAW,CAAC;EAC3D,IAAIqB,SAAS,GAAGwD,eAAe,CAACC,YAAY,CAAC,CAAC;EAC9Cf,iBAAiB,CAAC1C,SAAS,EAAE/D,eAAe,CAAC;EAC7C;EACA8D,SAAS,CAACtC,IAAI,KAAK,MAAM,KAAKsC,SAAS,CAACtC,IAAI,GAAGwB,eAAe,CAACxB,IAAI,CAAC;EACpEsC,SAAS,CAACuD,MAAM,KAAK,MAAM,KAAKvD,SAAS,CAACuD,MAAM,GAAGrE,eAAe,CAACxB,IAAI,CAAC;EACxEuC,SAAS,CAACsD,MAAM,KAAK,MAAM,KAAKtD,SAAS,CAACsD,MAAM,GAAGrE,eAAe,CAACxB,IAAI,CAAC;EACxE,IAAI,CAAC6B,UAAU,EAAE;IACf,IAAIoE,WAAW,GAAGhI,eAAe,CAACpC,GAAG,CAAC,qBAAqB,CAAC;IAC5D;AACJ;AACA;AACA;AACA;IACI,IAAIqK,eAAe,GAAG5D,SAAS,CAACkD,aAAa,CAAC;IAC9ClD,SAAS,CAAC6C,SAAS,GAAGc,WAAW,KAAK,MAAM,GAAGzE,eAAe,CAAC2D,SAAS,GAAG,CAAC,IAAIe,eAAe,GAAG,CAAC,GAAG,CAAC,GAAG5D,SAAS,CAAC6C,SAAS;IAC7H7C,SAAS,CAACtC,IAAI,GAAG/B,eAAe,CAACpC,GAAG,CAAC,eAAe,CAAC;IACrDyG,SAAS,CAACuD,MAAM,GAAG5H,eAAe,CAACpC,GAAG,CAAC,qBAAqB,CAAC;IAC7D0G,SAAS,CAACsD,MAAM,GAAGE,eAAe,CAAClK,GAAG,CAAC,eAAe,CAAC;IACvD0G,SAAS,CAAC4C,SAAS,GAAGY,eAAe,CAAClK,GAAG,CAAC,eAAe,CAAC;EAC5D;EACA,OAAO;IACLyG,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEA;EACb,CAAC;AACH;AACA,SAASE,oBAAoBA,CAAC0D,GAAG,EAAE;EACjC,IAAIC,SAAS,GAAGD,GAAG,CAAC9D,IAAI,IAAI,WAAW;EACvC,IAAIA,IAAI,GAAGpI,YAAY,CAACmM,SAAS,EAAE,CAAC,EAAE,CAAC,EAAED,GAAG,CAACxE,SAAS,EAAEwE,GAAG,CAACvE,UAAU,EAAEuE,GAAG,CAAC7D,SAAS,CAACtC,IAAI,EAAEmG,GAAG,CAACpE,gBAAgB,CAAC;EACjHM,IAAI,CAACgE,QAAQ,CAACF,GAAG,CAAC7D,SAAS,CAAC;EAC5BD,IAAI,CAACiE,QAAQ,GAAG,CAACH,GAAG,CAACrE,UAAU,IAAI,CAAC,IAAI+C,IAAI,CAAC0B,EAAE,GAAG,GAAG;EACrDlE,IAAI,CAACmE,SAAS,CAAC,CAACL,GAAG,CAACxE,SAAS,GAAG,CAAC,EAAEwE,GAAG,CAACvE,UAAU,GAAG,CAAC,CAAC,CAAC;EACvD,IAAIwE,SAAS,CAACK,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;IACnCpE,IAAI,CAAC1D,KAAK,CAACkH,MAAM,GAAGxD,IAAI,CAAC1D,KAAK,CAACqB,IAAI;IACnCqC,IAAI,CAAC1D,KAAK,CAACqB,IAAI,GAAG,MAAM;IACxBqC,IAAI,CAAC1D,KAAK,CAACwG,SAAS,GAAG,CAAC;EAC1B;EACA,OAAO9C,IAAI;AACb;AACA,SAAStD,oBAAoBA,CAAC2H,UAAU,EAAEC,QAAQ,EAAEjL,GAAG,EAAEiC,eAAe,EAAE;EACxE;EACAsB,sBAAsB,CAACyH,UAAU,EAAEC,QAAQ,EAAEjL,GAAG,EAAEiC,eAAe,CAAC;EAClEjC,GAAG,CAACsF,cAAc,CAAC;IACjBpG,IAAI,EAAE,oBAAoB;IAC1BuD,IAAI,EAAEuI,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGC;EAC1C,CAAC,CAAC;EACF;EACA;EACA3H,uBAAuB,CAAC0H,UAAU,EAAEC,QAAQ,EAAEjL,GAAG,EAAEiC,eAAe,CAAC;AACrE;AACA,SAASiJ,eAAeA,CAAClL,GAAG,EAAE;EAC5B,IAAImL,IAAI,GAAGnL,GAAG,CAACoL,KAAK,CAAC,CAAC,CAACC,OAAO,CAACC,cAAc,CAAC,CAAC;EAC/C,IAAIC,aAAa;EACjB,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,GAAG,GAAGN,IAAI,CAACO,MAAM;EACrB,OAAOF,CAAC,GAAGC,GAAG,IAAI,EAAEF,aAAa,GAAGJ,IAAI,CAACK,CAAC,CAAC,CAACG,MAAM,CAAChG,QAAQ,CAAC,EAAE;IAC5D6F,CAAC,EAAE;EACL;EACA,OAAOD,aAAa,IAAIA,aAAa,CAACK,UAAU;AAClD;AACA,SAAStI,uBAAuBA,CAAC0H,UAAU,EAAEC,QAAQ,EAAEjL,GAAG,EAAEiC,eAAe,EAAE;EAC3E;EACA,IAAI,CAACiJ,eAAe,CAAClL,GAAG,CAAC,EAAE;IACzBA,GAAG,CAACsF,cAAc,CAAC;MACjBpG,IAAI,EAAE,WAAW;MACjB8L,UAAU,EAAEA,UAAU;MACtBvI,IAAI,EAAEwI,QAAQ;MACdhJ,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ;AACF;AACA,SAASsB,sBAAsBA,CAACyH,UAAU,EAAEC,QAAQ,EAAEjL,GAAG,EAAEiC,eAAe,EAAE;EAC1E;EACA,IAAI,CAACiJ,eAAe,CAAClL,GAAG,CAAC,EAAE;IACzBA,GAAG,CAACsF,cAAc,CAAC;MACjBpG,IAAI,EAAE,UAAU;MAChB8L,UAAU,EAAEA,UAAU;MACtBvI,IAAI,EAAEwI,QAAQ;MACdhJ,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ;AACF;AACA,eAAepD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}