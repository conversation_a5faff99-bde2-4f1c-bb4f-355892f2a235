{"ast": null, "code": "import _asyncToGenerator from \"C:/e-syndic/esyndic-velzon/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, from } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"keycloak-angular\";\nexport class KeycloakAuthService {\n  constructor(keycloakService) {\n    this.keycloakService = keycloakService;\n    this.userProfileSubject = new BehaviorSubject(null);\n    this.userProfile$ = this.userProfileSubject.asObservable();\n    this.loadUserProfile();\n  }\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated() {\n    return this.keycloakService.isLoggedIn();\n  }\n  /**\n   * Get current user profile\n   */\n  getCurrentUser() {\n    return this.userProfileSubject.value;\n  }\n  /**\n   * Load user profile from Keycloak\n   */\n  loadUserProfile() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.isAuthenticated()) {\n        try {\n          const keycloakProfile = yield _this.keycloakService.loadUserProfile();\n          const userRoles = _this.keycloakService.getUserRoles();\n          const realmRoles = _this.keycloakService.getUserRoles(false);\n          const resourceRoles = []; // getResourceRoles not available in this version\n          const userProfile = {\n            id: keycloakProfile.id,\n            username: keycloakProfile.username,\n            email: keycloakProfile.email,\n            firstName: keycloakProfile.firstName,\n            lastName: keycloakProfile.lastName,\n            fullName: `${keycloakProfile.firstName || ''} ${keycloakProfile.lastName || ''}`.trim(),\n            roles: userRoles,\n            realmRoles: realmRoles,\n            resourceRoles: resourceRoles\n          };\n          _this.userProfileSubject.next(userProfile);\n        } catch (error) {\n          console.error('Error loading user profile:', error);\n          _this.userProfileSubject.next(null);\n        }\n      }\n    })();\n  }\n  /**\n   * Login user\n   */\n  login() {\n    this.keycloakService.login({\n      redirectUri: window.location.origin + '/dashboard'\n    });\n  }\n  /**\n   * Logout user\n   */\n  logout() {\n    this.keycloakService.logout(window.location.origin);\n  }\n  /**\n   * Get access token\n   */\n  getToken() {\n    return this.keycloakService.getToken();\n  }\n  /**\n   * Check if user has specific realm role\n   */\n  hasRealmRole(role) {\n    return this.keycloakService.isUserInRole(role);\n  }\n  /**\n   * Check if user has specific resource role\n   */\n  hasResourceRole(role, resource) {\n    // Resource roles not supported in this version, check realm roles instead\n    return this.keycloakService.isUserInRole(role);\n  }\n  /**\n   * Check if user has any of the specified roles\n   */\n  hasAnyRole(roles, isResourceRole = false, resource) {\n    return roles.some(role => isResourceRole ? this.hasResourceRole(role, resource) : this.hasRealmRole(role));\n  }\n  /**\n   * Check if user has all of the specified roles\n   */\n  hasAllRoles(roles, isResourceRole = false, resource) {\n    return roles.every(role => isResourceRole ? this.hasResourceRole(role, resource) : this.hasRealmRole(role));\n  }\n  /**\n   * Check if user is Super Admin\n   */\n  isSuperAdmin() {\n    return this.hasRealmRole('SUPERADMIN');\n  }\n  /**\n   * Check if user is Admin\n   */\n  isAdmin() {\n    return this.hasRealmRole('ADMIN') || this.isSuperAdmin();\n  }\n  /**\n   * Check if user is President\n   */\n  isPresident() {\n    return this.hasRealmRole('PRESIDENT') || this.isAdmin();\n  }\n  /**\n   * Check if user is Owner\n   */\n  isOwner() {\n    return this.hasRealmRole('OWNER') || this.isPresident();\n  }\n  /**\n   * Check if user is Resident\n   */\n  isResident() {\n    return this.hasRealmRole('RESIDENT') || this.isOwner();\n  }\n  /**\n   * Get user's highest role level\n   */\n  getUserRoleLevel() {\n    if (this.isSuperAdmin()) return 'SUPERADMIN';\n    if (this.isAdmin()) return 'ADMIN';\n    if (this.isPresident()) return 'PRESIDENT';\n    if (this.isOwner()) return 'OWNER';\n    if (this.isResident()) return 'RESIDENT';\n    return 'GUEST';\n  }\n  /**\n   * Get navigation route based on user role\n   */\n  getDefaultRoute() {\n    if (this.isSuperAdmin()) return '/admin/dashboard';\n    if (this.isAdmin()) return '/admin/dashboard';\n    if (this.isPresident()) return '/president/dashboard';\n    if (this.isOwner()) return '/owner/dashboard';\n    if (this.isResident()) return '/resident/dashboard';\n    return '/dashboard';\n  }\n  /**\n   * Refresh token\n   */\n  refreshToken() {\n    return from(this.keycloakService.updateToken(30)).pipe(map(() => true), catchError(() => {\n      this.logout();\n      return [false];\n    }));\n  }\n  /**\n   * Check if token is expired\n   */\n  isTokenExpired() {\n    return this.keycloakService.isTokenExpired();\n  }\n  static {\n    this.ɵfac = function KeycloakAuthService_Factory(t) {\n      return new (t || KeycloakAuthService)(i0.ɵɵinject(i1.KeycloakService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: KeycloakAuthService,\n      factory: KeycloakAuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "from", "map", "catchError", "KeycloakAuthService", "constructor", "keycloakService", "userProfileSubject", "userProfile$", "asObservable", "loadUserProfile", "isAuthenticated", "isLoggedIn", "getCurrentUser", "value", "_this", "_asyncToGenerator", "keycloakProfile", "userRoles", "getUserRoles", "realmRoles", "resourceRoles", "userProfile", "id", "username", "email", "firstName", "lastName", "fullName", "trim", "roles", "next", "error", "console", "login", "redirectUri", "window", "location", "origin", "logout", "getToken", "hasRealmRole", "role", "isUserInRole", "hasResourceRole", "resource", "hasAnyRole", "isResourceRole", "some", "hasAllRoles", "every", "isSuperAdmin", "isAdmin", "isPresident", "isOwner", "isResident", "getUserRoleLevel", "getDefaultRoute", "refreshToken", "updateToken", "pipe", "isTokenExpired", "i0", "ɵɵinject", "i1", "KeycloakService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\core\\services\\keycloak-auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { KeycloakService } from 'keycloak-angular';\nimport { KeycloakProfile } from 'keycloak-js';\nimport { Observable, BehaviorSubject, from } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\n\nexport interface UserProfile {\n  id?: string;\n  username?: string;\n  email?: string;\n  firstName?: string;\n  lastName?: string;\n  fullName?: string;\n  roles?: string[];\n  realmRoles?: string[];\n  resourceRoles?: { [key: string]: string[] };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class KeycloakAuthService {\n  private userProfileSubject = new BehaviorSubject<UserProfile | null>(null);\n  public userProfile$ = this.userProfileSubject.asObservable();\n\n  constructor(private keycloakService: KeycloakService) {\n    this.loadUserProfile();\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated(): boolean {\n    return this.keycloakService.isLoggedIn();\n  }\n\n  /**\n   * Get current user profile\n   */\n  getCurrentUser(): UserProfile | null {\n    return this.userProfileSubject.value;\n  }\n\n  /**\n   * Load user profile from Keycloak\n   */\n  private async loadUserProfile(): Promise<void> {\n    if (this.isAuthenticated()) {\n      try {\n        const keycloakProfile = await this.keycloakService.loadUserProfile();\n        const userRoles = this.keycloakService.getUserRoles();\n        const realmRoles = this.keycloakService.getUserRoles(false);\n        const resourceRoles: string[] = []; // getResourceRoles not available in this version\n\n        const userProfile: UserProfile = {\n          id: keycloakProfile.id,\n          username: keycloakProfile.username,\n          email: keycloakProfile.email,\n          firstName: keycloakProfile.firstName,\n          lastName: keycloakProfile.lastName,\n          fullName: `${keycloakProfile.firstName || ''} ${keycloakProfile.lastName || ''}`.trim(),\n          roles: userRoles,\n          realmRoles: realmRoles,\n          resourceRoles: resourceRoles\n        };\n\n        this.userProfileSubject.next(userProfile);\n      } catch (error) {\n        console.error('Error loading user profile:', error);\n        this.userProfileSubject.next(null);\n      }\n    }\n  }\n\n  /**\n   * Login user\n   */\n  login(): void {\n    this.keycloakService.login({\n      redirectUri: window.location.origin + '/dashboard'\n    });\n  }\n\n  /**\n   * Logout user\n   */\n  logout(): void {\n    this.keycloakService.logout(window.location.origin);\n  }\n\n  /**\n   * Get access token\n   */\n  getToken(): string | undefined {\n    return this.keycloakService.getToken();\n  }\n\n  /**\n   * Check if user has specific realm role\n   */\n  hasRealmRole(role: string): boolean {\n    return this.keycloakService.isUserInRole(role);\n  }\n\n  /**\n   * Check if user has specific resource role\n   */\n  hasResourceRole(role: string, resource?: string): boolean {\n    // Resource roles not supported in this version, check realm roles instead\n    return this.keycloakService.isUserInRole(role);\n  }\n\n  /**\n   * Check if user has any of the specified roles\n   */\n  hasAnyRole(roles: string[], isResourceRole: boolean = false, resource?: string): boolean {\n    return roles.some(role => \n      isResourceRole ? this.hasResourceRole(role, resource) : this.hasRealmRole(role)\n    );\n  }\n\n  /**\n   * Check if user has all of the specified roles\n   */\n  hasAllRoles(roles: string[], isResourceRole: boolean = false, resource?: string): boolean {\n    return roles.every(role => \n      isResourceRole ? this.hasResourceRole(role, resource) : this.hasRealmRole(role)\n    );\n  }\n\n  /**\n   * Check if user is Super Admin\n   */\n  isSuperAdmin(): boolean {\n    return this.hasRealmRole('SUPERADMIN');\n  }\n\n  /**\n   * Check if user is Admin\n   */\n  isAdmin(): boolean {\n    return this.hasRealmRole('ADMIN') || this.isSuperAdmin();\n  }\n\n  /**\n   * Check if user is President\n   */\n  isPresident(): boolean {\n    return this.hasRealmRole('PRESIDENT') || this.isAdmin();\n  }\n\n  /**\n   * Check if user is Owner\n   */\n  isOwner(): boolean {\n    return this.hasRealmRole('OWNER') || this.isPresident();\n  }\n\n  /**\n   * Check if user is Resident\n   */\n  isResident(): boolean {\n    return this.hasRealmRole('RESIDENT') || this.isOwner();\n  }\n\n  /**\n   * Get user's highest role level\n   */\n  getUserRoleLevel(): string {\n    if (this.isSuperAdmin()) return 'SUPERADMIN';\n    if (this.isAdmin()) return 'ADMIN';\n    if (this.isPresident()) return 'PRESIDENT';\n    if (this.isOwner()) return 'OWNER';\n    if (this.isResident()) return 'RESIDENT';\n    return 'GUEST';\n  }\n\n  /**\n   * Get navigation route based on user role\n   */\n  getDefaultRoute(): string {\n    if (this.isSuperAdmin()) return '/admin/dashboard';\n    if (this.isAdmin()) return '/admin/dashboard';\n    if (this.isPresident()) return '/president/dashboard';\n    if (this.isOwner()) return '/owner/dashboard';\n    if (this.isResident()) return '/resident/dashboard';\n    return '/dashboard';\n  }\n\n  /**\n   * Refresh token\n   */\n  refreshToken(): Observable<boolean> {\n    return from(this.keycloakService.updateToken(30)).pipe(\n      map(() => true),\n      catchError(() => {\n        this.logout();\n        return [false];\n      })\n    );\n  }\n\n  /**\n   * Check if token is expired\n   */\n  isTokenExpired(): boolean {\n    return this.keycloakService.isTokenExpired();\n  }\n}\n"], "mappings": ";AAGA,SAAqBA,eAAe,EAAEC,IAAI,QAAQ,MAAM;AACxD,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;AAiBhD,OAAM,MAAOC,mBAAmB;EAI9BC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAH3B,KAAAC,kBAAkB,GAAG,IAAIP,eAAe,CAAqB,IAAI,CAAC;IACnE,KAAAQ,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IAG1D,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;;;EAGAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACL,eAAe,CAACM,UAAU,EAAE;EAC1C;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACN,kBAAkB,CAACO,KAAK;EACtC;EAEA;;;EAGcJ,eAAeA,CAAA;IAAA,IAAAK,KAAA;IAAA,OAAAC,iBAAA;MAC3B,IAAID,KAAI,CAACJ,eAAe,EAAE,EAAE;QAC1B,IAAI;UACF,MAAMM,eAAe,SAASF,KAAI,CAACT,eAAe,CAACI,eAAe,EAAE;UACpE,MAAMQ,SAAS,GAAGH,KAAI,CAACT,eAAe,CAACa,YAAY,EAAE;UACrD,MAAMC,UAAU,GAAGL,KAAI,CAACT,eAAe,CAACa,YAAY,CAAC,KAAK,CAAC;UAC3D,MAAME,aAAa,GAAa,EAAE,CAAC,CAAC;UAEpC,MAAMC,WAAW,GAAgB;YAC/BC,EAAE,EAAEN,eAAe,CAACM,EAAE;YACtBC,QAAQ,EAAEP,eAAe,CAACO,QAAQ;YAClCC,KAAK,EAAER,eAAe,CAACQ,KAAK;YAC5BC,SAAS,EAAET,eAAe,CAACS,SAAS;YACpCC,QAAQ,EAAEV,eAAe,CAACU,QAAQ;YAClCC,QAAQ,EAAE,GAAGX,eAAe,CAACS,SAAS,IAAI,EAAE,IAAIT,eAAe,CAACU,QAAQ,IAAI,EAAE,EAAE,CAACE,IAAI,EAAE;YACvFC,KAAK,EAAEZ,SAAS;YAChBE,UAAU,EAAEA,UAAU;YACtBC,aAAa,EAAEA;WAChB;UAEDN,KAAI,CAACR,kBAAkB,CAACwB,IAAI,CAACT,WAAW,CAAC;QAC3C,CAAC,CAAC,OAAOU,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnDjB,KAAI,CAACR,kBAAkB,CAACwB,IAAI,CAAC,IAAI,CAAC;QACpC;MACF;IAAC;EACH;EAEA;;;EAGAG,KAAKA,CAAA;IACH,IAAI,CAAC5B,eAAe,CAAC4B,KAAK,CAAC;MACzBC,WAAW,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM,GAAG;KACvC,CAAC;EACJ;EAEA;;;EAGAC,MAAMA,CAAA;IACJ,IAAI,CAACjC,eAAe,CAACiC,MAAM,CAACH,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;EACrD;EAEA;;;EAGAE,QAAQA,CAAA;IACN,OAAO,IAAI,CAAClC,eAAe,CAACkC,QAAQ,EAAE;EACxC;EAEA;;;EAGAC,YAAYA,CAACC,IAAY;IACvB,OAAO,IAAI,CAACpC,eAAe,CAACqC,YAAY,CAACD,IAAI,CAAC;EAChD;EAEA;;;EAGAE,eAAeA,CAACF,IAAY,EAAEG,QAAiB;IAC7C;IACA,OAAO,IAAI,CAACvC,eAAe,CAACqC,YAAY,CAACD,IAAI,CAAC;EAChD;EAEA;;;EAGAI,UAAUA,CAAChB,KAAe,EAAEiB,cAAA,GAA0B,KAAK,EAAEF,QAAiB;IAC5E,OAAOf,KAAK,CAACkB,IAAI,CAACN,IAAI,IACpBK,cAAc,GAAG,IAAI,CAACH,eAAe,CAACF,IAAI,EAAEG,QAAQ,CAAC,GAAG,IAAI,CAACJ,YAAY,CAACC,IAAI,CAAC,CAChF;EACH;EAEA;;;EAGAO,WAAWA,CAACnB,KAAe,EAAEiB,cAAA,GAA0B,KAAK,EAAEF,QAAiB;IAC7E,OAAOf,KAAK,CAACoB,KAAK,CAACR,IAAI,IACrBK,cAAc,GAAG,IAAI,CAACH,eAAe,CAACF,IAAI,EAAEG,QAAQ,CAAC,GAAG,IAAI,CAACJ,YAAY,CAACC,IAAI,CAAC,CAChF;EACH;EAEA;;;EAGAS,YAAYA,CAAA;IACV,OAAO,IAAI,CAACV,YAAY,CAAC,YAAY,CAAC;EACxC;EAEA;;;EAGAW,OAAOA,CAAA;IACL,OAAO,IAAI,CAACX,YAAY,CAAC,OAAO,CAAC,IAAI,IAAI,CAACU,YAAY,EAAE;EAC1D;EAEA;;;EAGAE,WAAWA,CAAA;IACT,OAAO,IAAI,CAACZ,YAAY,CAAC,WAAW,CAAC,IAAI,IAAI,CAACW,OAAO,EAAE;EACzD;EAEA;;;EAGAE,OAAOA,CAAA;IACL,OAAO,IAAI,CAACb,YAAY,CAAC,OAAO,CAAC,IAAI,IAAI,CAACY,WAAW,EAAE;EACzD;EAEA;;;EAGAE,UAAUA,CAAA;IACR,OAAO,IAAI,CAACd,YAAY,CAAC,UAAU,CAAC,IAAI,IAAI,CAACa,OAAO,EAAE;EACxD;EAEA;;;EAGAE,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACL,YAAY,EAAE,EAAE,OAAO,YAAY;IAC5C,IAAI,IAAI,CAACC,OAAO,EAAE,EAAE,OAAO,OAAO;IAClC,IAAI,IAAI,CAACC,WAAW,EAAE,EAAE,OAAO,WAAW;IAC1C,IAAI,IAAI,CAACC,OAAO,EAAE,EAAE,OAAO,OAAO;IAClC,IAAI,IAAI,CAACC,UAAU,EAAE,EAAE,OAAO,UAAU;IACxC,OAAO,OAAO;EAChB;EAEA;;;EAGAE,eAAeA,CAAA;IACb,IAAI,IAAI,CAACN,YAAY,EAAE,EAAE,OAAO,kBAAkB;IAClD,IAAI,IAAI,CAACC,OAAO,EAAE,EAAE,OAAO,kBAAkB;IAC7C,IAAI,IAAI,CAACC,WAAW,EAAE,EAAE,OAAO,sBAAsB;IACrD,IAAI,IAAI,CAACC,OAAO,EAAE,EAAE,OAAO,kBAAkB;IAC7C,IAAI,IAAI,CAACC,UAAU,EAAE,EAAE,OAAO,qBAAqB;IACnD,OAAO,YAAY;EACrB;EAEA;;;EAGAG,YAAYA,CAAA;IACV,OAAOzD,IAAI,CAAC,IAAI,CAACK,eAAe,CAACqD,WAAW,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CACpD1D,GAAG,CAAC,MAAM,IAAI,CAAC,EACfC,UAAU,CAAC,MAAK;MACd,IAAI,CAACoC,MAAM,EAAE;MACb,OAAO,CAAC,KAAK,CAAC;IAChB,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAsB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACvD,eAAe,CAACuD,cAAc,EAAE;EAC9C;;;uBA1LWzD,mBAAmB,EAAA0D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;aAAnB7D,mBAAmB;MAAA8D,OAAA,EAAnB9D,mBAAmB,CAAA+D,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}