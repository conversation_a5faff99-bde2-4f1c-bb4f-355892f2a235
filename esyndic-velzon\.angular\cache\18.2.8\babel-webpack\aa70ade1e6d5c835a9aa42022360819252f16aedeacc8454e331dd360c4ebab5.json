{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar SeriesDimensionDefine = /** @class */function () {\n  /**\n   * @param opt All of the fields will be shallow copied.\n   */\n  function SeriesDimensionDefine(opt) {\n    /**\n     * The format of `otherDims` is:\n     * ```js\n     * {\n     *     tooltip?: number\n     *     label?: number\n     *     itemName?: number\n     *     seriesName?: number\n     * }\n     * ```\n     *\n     * A `series.encode` can specified these fields:\n     * ```js\n     * encode: {\n     *     // \"3, 1, 5\" is the index of data dimension.\n     *     tooltip: [3, 1, 5],\n     *     label: [0, 3],\n     *     ...\n     * }\n     * ```\n     * `otherDims` is the parse result of the `series.encode` above, like:\n     * ```js\n     * // Suppose the index of this data dimension is `3`.\n     * this.otherDims = {\n     *     // `3` is at the index `0` of the `encode.tooltip`\n     *     tooltip: 0,\n     *     // `3` is at the index `1` of the `encode.label`\n     *     label: 1\n     * };\n     * ```\n     *\n     * This prop should never be `null`/`undefined` after initialized.\n     */\n    this.otherDims = {};\n    if (opt != null) {\n      zrUtil.extend(this, opt);\n    }\n  }\n  return SeriesDimensionDefine;\n}();\n;\nexport default SeriesDimensionDefine;", "map": {"version": 3, "names": ["zrUtil", "SeriesDimensionDefine", "opt", "otherDims", "extend"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/data/SeriesDimensionDefine.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar SeriesDimensionDefine = /** @class */function () {\n  /**\n   * @param opt All of the fields will be shallow copied.\n   */\n  function SeriesDimensionDefine(opt) {\n    /**\n     * The format of `otherDims` is:\n     * ```js\n     * {\n     *     tooltip?: number\n     *     label?: number\n     *     itemName?: number\n     *     seriesName?: number\n     * }\n     * ```\n     *\n     * A `series.encode` can specified these fields:\n     * ```js\n     * encode: {\n     *     // \"3, 1, 5\" is the index of data dimension.\n     *     tooltip: [3, 1, 5],\n     *     label: [0, 3],\n     *     ...\n     * }\n     * ```\n     * `otherDims` is the parse result of the `series.encode` above, like:\n     * ```js\n     * // Suppose the index of this data dimension is `3`.\n     * this.otherDims = {\n     *     // `3` is at the index `0` of the `encode.tooltip`\n     *     tooltip: 0,\n     *     // `3` is at the index `1` of the `encode.label`\n     *     label: 1\n     * };\n     * ```\n     *\n     * This prop should never be `null`/`undefined` after initialized.\n     */\n    this.otherDims = {};\n    if (opt != null) {\n      zrUtil.extend(this, opt);\n    }\n  }\n  return SeriesDimensionDefine;\n}();\n;\nexport default SeriesDimensionDefine;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,IAAIC,qBAAqB,GAAG,aAAa,YAAY;EACnD;AACF;AACA;EACE,SAASA,qBAAqBA,CAACC,GAAG,EAAE;IAClC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAID,GAAG,IAAI,IAAI,EAAE;MACfF,MAAM,CAACI,MAAM,CAAC,IAAI,EAAEF,GAAG,CAAC;IAC1B;EACF;EACA,OAAOD,qBAAqB;AAC9B,CAAC,CAAC,CAAC;AACH;AACA,eAAeA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}