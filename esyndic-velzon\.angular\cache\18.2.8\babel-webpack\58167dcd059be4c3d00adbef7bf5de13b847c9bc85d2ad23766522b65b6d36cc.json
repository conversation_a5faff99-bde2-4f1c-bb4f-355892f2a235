{"ast": null, "code": "import _asyncToGenerator from \"C:/e-syndic/esyndic-velzon/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*\n * Copyright 2016 Red Hat, Inc. and/or its affiliates\n * and other contributors as indicated by the <AUTHOR>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction Keycloak(config) {\n  if (!(this instanceof Keycloak)) {\n    throw new Error(\"The 'Keycloak' constructor must be invoked with 'new'.\");\n  }\n  if (typeof config !== 'string' && !isObject(config)) {\n    throw new Error(\"The 'Keycloak' constructor must be provided with a configuration object, or a URL to a JSON configuration file.\");\n  }\n  if (isObject(config)) {\n    const requiredProperties = 'oidcProvider' in config ? ['clientId'] : ['url', 'realm', 'clientId'];\n    for (const property of requiredProperties) {\n      if (!config[property]) {\n        throw new Error(`The configuration object is missing the required '${property}' property.`);\n      }\n    }\n  }\n  var kc = this;\n  var adapter;\n  var refreshQueue = [];\n  var callbackStorage;\n  var loginIframe = {\n    enable: true,\n    callbackList: [],\n    interval: 5\n  };\n  kc.didInitialize = false;\n  var useNonce = true;\n  var logInfo = createLogger(console.info);\n  var logWarn = createLogger(console.warn);\n  if (!globalThis.isSecureContext) {\n    logWarn(\"[KEYCLOAK] Keycloak JS must be used in a 'secure context' to function properly as it relies on browser APIs that are otherwise not available.\\n\" + \"Continuing to run your application insecurely will lead to unexpected behavior and breakage.\\n\\n\" + \"For more information see: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts\");\n  }\n  kc.init = function (initOptions = {}) {\n    if (kc.didInitialize) {\n      throw new Error(\"A 'Keycloak' instance can only be initialized once.\");\n    }\n    kc.didInitialize = true;\n    kc.authenticated = false;\n    callbackStorage = createCallbackStorage();\n    var adapters = ['default', 'cordova', 'cordova-native'];\n    if (adapters.indexOf(initOptions.adapter) > -1) {\n      adapter = loadAdapter(initOptions.adapter);\n    } else if (typeof initOptions.adapter === \"object\") {\n      adapter = initOptions.adapter;\n    } else {\n      if (window.Cordova || window.cordova) {\n        adapter = loadAdapter('cordova');\n      } else {\n        adapter = loadAdapter();\n      }\n    }\n    if (typeof initOptions.useNonce !== 'undefined') {\n      useNonce = initOptions.useNonce;\n    }\n    if (typeof initOptions.checkLoginIframe !== 'undefined') {\n      loginIframe.enable = initOptions.checkLoginIframe;\n    }\n    if (initOptions.checkLoginIframeInterval) {\n      loginIframe.interval = initOptions.checkLoginIframeInterval;\n    }\n    if (initOptions.onLoad === 'login-required') {\n      kc.loginRequired = true;\n    }\n    if (initOptions.responseMode) {\n      if (initOptions.responseMode === 'query' || initOptions.responseMode === 'fragment') {\n        kc.responseMode = initOptions.responseMode;\n      } else {\n        throw 'Invalid value for responseMode';\n      }\n    }\n    if (initOptions.flow) {\n      switch (initOptions.flow) {\n        case 'standard':\n          kc.responseType = 'code';\n          break;\n        case 'implicit':\n          kc.responseType = 'id_token token';\n          break;\n        case 'hybrid':\n          kc.responseType = 'code id_token token';\n          break;\n        default:\n          throw 'Invalid value for flow';\n      }\n      kc.flow = initOptions.flow;\n    }\n    if (initOptions.timeSkew != null) {\n      kc.timeSkew = initOptions.timeSkew;\n    }\n    if (initOptions.redirectUri) {\n      kc.redirectUri = initOptions.redirectUri;\n    }\n    if (initOptions.silentCheckSsoRedirectUri) {\n      kc.silentCheckSsoRedirectUri = initOptions.silentCheckSsoRedirectUri;\n    }\n    if (typeof initOptions.silentCheckSsoFallback === 'boolean') {\n      kc.silentCheckSsoFallback = initOptions.silentCheckSsoFallback;\n    } else {\n      kc.silentCheckSsoFallback = true;\n    }\n    if (typeof initOptions.pkceMethod !== \"undefined\") {\n      if (initOptions.pkceMethod !== \"S256\" && initOptions.pkceMethod !== false) {\n        throw new TypeError(`Invalid value for pkceMethod', expected 'S256' or false but got ${initOptions.pkceMethod}.`);\n      }\n      kc.pkceMethod = initOptions.pkceMethod;\n    } else {\n      kc.pkceMethod = \"S256\";\n    }\n    if (typeof initOptions.enableLogging === 'boolean') {\n      kc.enableLogging = initOptions.enableLogging;\n    } else {\n      kc.enableLogging = false;\n    }\n    if (initOptions.logoutMethod === 'POST') {\n      kc.logoutMethod = 'POST';\n    } else {\n      kc.logoutMethod = 'GET';\n    }\n    if (typeof initOptions.scope === 'string') {\n      kc.scope = initOptions.scope;\n    }\n    if (typeof initOptions.acrValues === 'string') {\n      kc.acrValues = initOptions.acrValues;\n    }\n    if (typeof initOptions.messageReceiveTimeout === 'number' && initOptions.messageReceiveTimeout > 0) {\n      kc.messageReceiveTimeout = initOptions.messageReceiveTimeout;\n    } else {\n      kc.messageReceiveTimeout = 10000;\n    }\n    if (!kc.responseMode) {\n      kc.responseMode = 'fragment';\n    }\n    if (!kc.responseType) {\n      kc.responseType = 'code';\n      kc.flow = 'standard';\n    }\n    var promise = createPromise();\n    var initPromise = createPromise();\n    initPromise.promise.then(function () {\n      kc.onReady && kc.onReady(kc.authenticated);\n      promise.setSuccess(kc.authenticated);\n    }).catch(function (error) {\n      promise.setError(error);\n    });\n    var configPromise = loadConfig();\n    function onLoad() {\n      var doLogin = function (prompt) {\n        if (!prompt) {\n          options.prompt = 'none';\n        }\n        if (initOptions.locale) {\n          options.locale = initOptions.locale;\n        }\n        kc.login(options).then(function () {\n          initPromise.setSuccess();\n        }).catch(function (error) {\n          initPromise.setError(error);\n        });\n      };\n      var checkSsoSilently = /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* () {\n          var ifrm = document.createElement(\"iframe\");\n          var src = yield kc.createLoginUrl({\n            prompt: 'none',\n            redirectUri: kc.silentCheckSsoRedirectUri\n          });\n          ifrm.setAttribute(\"src\", src);\n          ifrm.setAttribute(\"sandbox\", \"allow-storage-access-by-user-activation allow-scripts allow-same-origin\");\n          ifrm.setAttribute(\"title\", \"keycloak-silent-check-sso\");\n          ifrm.style.display = \"none\";\n          document.body.appendChild(ifrm);\n          var messageCallback = function (event) {\n            if (event.origin !== window.location.origin || ifrm.contentWindow !== event.source) {\n              return;\n            }\n            var oauth = parseCallback(event.data);\n            processCallback(oauth, initPromise);\n            document.body.removeChild(ifrm);\n            window.removeEventListener(\"message\", messageCallback);\n          };\n          window.addEventListener(\"message\", messageCallback);\n        });\n        return function checkSsoSilently() {\n          return _ref.apply(this, arguments);\n        };\n      }();\n      var options = {};\n      switch (initOptions.onLoad) {\n        case 'check-sso':\n          if (loginIframe.enable) {\n            setupCheckLoginIframe().then(function () {\n              checkLoginIframe().then(function (unchanged) {\n                if (!unchanged) {\n                  kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                } else {\n                  initPromise.setSuccess();\n                }\n              }).catch(function (error) {\n                initPromise.setError(error);\n              });\n            });\n          } else {\n            kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n          }\n          break;\n        case 'login-required':\n          doLogin(true);\n          break;\n        default:\n          throw 'Invalid value for onLoad';\n      }\n    }\n    function processInit() {\n      var callback = parseCallback(window.location.href);\n      if (callback) {\n        window.history.replaceState(window.history.state, null, callback.newUrl);\n      }\n      if (callback && callback.valid) {\n        return setupCheckLoginIframe().then(function () {\n          processCallback(callback, initPromise);\n        }).catch(function (error) {\n          initPromise.setError(error);\n        });\n      }\n      if (initOptions.token && initOptions.refreshToken) {\n        setToken(initOptions.token, initOptions.refreshToken, initOptions.idToken);\n        if (loginIframe.enable) {\n          setupCheckLoginIframe().then(function () {\n            checkLoginIframe().then(function (unchanged) {\n              if (unchanged) {\n                kc.onAuthSuccess && kc.onAuthSuccess();\n                initPromise.setSuccess();\n                scheduleCheckIframe();\n              } else {\n                initPromise.setSuccess();\n              }\n            }).catch(function (error) {\n              initPromise.setError(error);\n            });\n          });\n        } else {\n          kc.updateToken(-1).then(function () {\n            kc.onAuthSuccess && kc.onAuthSuccess();\n            initPromise.setSuccess();\n          }).catch(function (error) {\n            kc.onAuthError && kc.onAuthError();\n            if (initOptions.onLoad) {\n              onLoad();\n            } else {\n              initPromise.setError(error);\n            }\n          });\n        }\n      } else if (initOptions.onLoad) {\n        onLoad();\n      } else {\n        initPromise.setSuccess();\n      }\n    }\n    configPromise.then(function () {\n      check3pCookiesSupported().then(processInit).catch(function (error) {\n        promise.setError(error);\n      });\n    });\n    configPromise.catch(function (error) {\n      promise.setError(error);\n    });\n    return promise.promise;\n  };\n  kc.login = function (options) {\n    return adapter.login(options);\n  };\n  function generateRandomData(len) {\n    if (typeof crypto === \"undefined\" || typeof crypto.getRandomValues === \"undefined\") {\n      throw new Error(\"Web Crypto API is not available.\");\n    }\n    return crypto.getRandomValues(new Uint8Array(len));\n  }\n  function generateCodeVerifier(len) {\n    return generateRandomString(len, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');\n  }\n  function generateRandomString(len, alphabet) {\n    var randomData = generateRandomData(len);\n    var chars = new Array(len);\n    for (var i = 0; i < len; i++) {\n      chars[i] = alphabet.charCodeAt(randomData[i] % alphabet.length);\n    }\n    return String.fromCharCode.apply(null, chars);\n  }\n  function generatePkceChallenge(_x, _x2) {\n    return _generatePkceChallenge.apply(this, arguments);\n  }\n  function _generatePkceChallenge() {\n    _generatePkceChallenge = _asyncToGenerator(function* (pkceMethod, codeVerifier) {\n      if (pkceMethod !== \"S256\") {\n        throw new TypeError(`Invalid value for 'pkceMethod', expected 'S256' but got '${pkceMethod}'.`);\n      }\n\n      // hash codeVerifier, then encode as url-safe base64 without padding\n      const hashBytes = new Uint8Array(yield sha256Digest(codeVerifier));\n      const encodedHash = bytesToBase64(hashBytes).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/\\=/g, '');\n      return encodedHash;\n    });\n    return _generatePkceChallenge.apply(this, arguments);\n  }\n  function buildClaimsParameter(requestedAcr) {\n    var claims = {\n      id_token: {\n        acr: requestedAcr\n      }\n    };\n    return JSON.stringify(claims);\n  }\n  kc.createLoginUrl = /*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator(function* (options) {\n      var state = createUUID();\n      var nonce = createUUID();\n      var redirectUri = adapter.redirectUri(options);\n      var callbackState = {\n        state: state,\n        nonce: nonce,\n        redirectUri: encodeURIComponent(redirectUri),\n        loginOptions: options\n      };\n      if (options && options.prompt) {\n        callbackState.prompt = options.prompt;\n      }\n      var baseUrl;\n      if (options && options.action == 'register') {\n        baseUrl = kc.endpoints.register();\n      } else {\n        baseUrl = kc.endpoints.authorize();\n      }\n      var scope = options && options.scope || kc.scope;\n      if (!scope) {\n        // if scope is not set, default to \"openid\"\n        scope = \"openid\";\n      } else if (scope.indexOf(\"openid\") === -1) {\n        // if openid scope is missing, prefix the given scopes with it\n        scope = \"openid \" + scope;\n      }\n      var url = baseUrl + '?client_id=' + encodeURIComponent(kc.clientId) + '&redirect_uri=' + encodeURIComponent(redirectUri) + '&state=' + encodeURIComponent(state) + '&response_mode=' + encodeURIComponent(kc.responseMode) + '&response_type=' + encodeURIComponent(kc.responseType) + '&scope=' + encodeURIComponent(scope);\n      if (useNonce) {\n        url = url + '&nonce=' + encodeURIComponent(nonce);\n      }\n      if (options && options.prompt) {\n        url += '&prompt=' + encodeURIComponent(options.prompt);\n      }\n      if (options && typeof options.maxAge === 'number') {\n        url += '&max_age=' + encodeURIComponent(options.maxAge);\n      }\n      if (options && options.loginHint) {\n        url += '&login_hint=' + encodeURIComponent(options.loginHint);\n      }\n      if (options && options.idpHint) {\n        url += '&kc_idp_hint=' + encodeURIComponent(options.idpHint);\n      }\n      if (options && options.action && options.action != 'register') {\n        url += '&kc_action=' + encodeURIComponent(options.action);\n      }\n      if (options && options.locale) {\n        url += '&ui_locales=' + encodeURIComponent(options.locale);\n      }\n      if (options && options.acr) {\n        var claimsParameter = buildClaimsParameter(options.acr);\n        url += '&claims=' + encodeURIComponent(claimsParameter);\n      }\n      if (options && options.acrValues || kc.acrValues) {\n        url += '&acr_values=' + encodeURIComponent(options.acrValues || kc.acrValues);\n      }\n      if (kc.pkceMethod) {\n        try {\n          const codeVerifier = generateCodeVerifier(96);\n          const pkceChallenge = yield generatePkceChallenge(kc.pkceMethod, codeVerifier);\n          callbackState.pkceCodeVerifier = codeVerifier;\n          url += '&code_challenge=' + pkceChallenge;\n          url += '&code_challenge_method=' + kc.pkceMethod;\n        } catch (error) {\n          throw new Error(\"Failed to generate PKCE challenge.\", {\n            cause: error\n          });\n        }\n      }\n      callbackStorage.add(callbackState);\n      return url;\n    });\n    return function (_x3) {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  kc.logout = function (options) {\n    return adapter.logout(options);\n  };\n  kc.createLogoutUrl = function (options) {\n    const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n    if (logoutMethod === 'POST') {\n      return kc.endpoints.logout();\n    }\n    var url = kc.endpoints.logout() + '?client_id=' + encodeURIComponent(kc.clientId) + '&post_logout_redirect_uri=' + encodeURIComponent(adapter.redirectUri(options, false));\n    if (kc.idToken) {\n      url += '&id_token_hint=' + encodeURIComponent(kc.idToken);\n    }\n    return url;\n  };\n  kc.register = function (options) {\n    return adapter.register(options);\n  };\n  kc.createRegisterUrl = /*#__PURE__*/function () {\n    var _ref3 = _asyncToGenerator(function* (options) {\n      if (!options) {\n        options = {};\n      }\n      options.action = 'register';\n      return yield kc.createLoginUrl(options);\n    });\n    return function (_x4) {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  kc.createAccountUrl = function (options) {\n    var realm = getRealmUrl();\n    var url = undefined;\n    if (typeof realm !== 'undefined') {\n      url = realm + '/account' + '?referrer=' + encodeURIComponent(kc.clientId) + '&referrer_uri=' + encodeURIComponent(adapter.redirectUri(options));\n    }\n    return url;\n  };\n  kc.accountManagement = function () {\n    return adapter.accountManagement();\n  };\n  kc.hasRealmRole = function (role) {\n    var access = kc.realmAccess;\n    return !!access && access.roles.indexOf(role) >= 0;\n  };\n  kc.hasResourceRole = function (role, resource) {\n    if (!kc.resourceAccess) {\n      return false;\n    }\n    var access = kc.resourceAccess[resource || kc.clientId];\n    return !!access && access.roles.indexOf(role) >= 0;\n  };\n  kc.loadUserProfile = function () {\n    var url = getRealmUrl() + '/account';\n    var req = new XMLHttpRequest();\n    req.open('GET', url, true);\n    req.setRequestHeader('Accept', 'application/json');\n    req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n    var promise = createPromise();\n    req.onreadystatechange = function () {\n      if (req.readyState == 4) {\n        if (req.status == 200) {\n          kc.profile = JSON.parse(req.responseText);\n          promise.setSuccess(kc.profile);\n        } else {\n          promise.setError();\n        }\n      }\n    };\n    req.send();\n    return promise.promise;\n  };\n  kc.loadUserInfo = function () {\n    var url = kc.endpoints.userinfo();\n    var req = new XMLHttpRequest();\n    req.open('GET', url, true);\n    req.setRequestHeader('Accept', 'application/json');\n    req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n    var promise = createPromise();\n    req.onreadystatechange = function () {\n      if (req.readyState == 4) {\n        if (req.status == 200) {\n          kc.userInfo = JSON.parse(req.responseText);\n          promise.setSuccess(kc.userInfo);\n        } else {\n          promise.setError();\n        }\n      }\n    };\n    req.send();\n    return promise.promise;\n  };\n  kc.isTokenExpired = function (minValidity) {\n    if (!kc.tokenParsed || !kc.refreshToken && kc.flow != 'implicit') {\n      throw 'Not authenticated';\n    }\n    if (kc.timeSkew == null) {\n      logInfo('[KEYCLOAK] Unable to determine if token is expired as timeskew is not set');\n      return true;\n    }\n    var expiresIn = kc.tokenParsed['exp'] - Math.ceil(new Date().getTime() / 1000) + kc.timeSkew;\n    if (minValidity) {\n      if (isNaN(minValidity)) {\n        throw 'Invalid minValidity';\n      }\n      expiresIn -= minValidity;\n    }\n    return expiresIn < 0;\n  };\n  kc.updateToken = function (minValidity) {\n    var promise = createPromise();\n    if (!kc.refreshToken) {\n      promise.setError();\n      return promise.promise;\n    }\n    minValidity = minValidity || 5;\n    var exec = function () {\n      var refreshToken = false;\n      if (minValidity == -1) {\n        refreshToken = true;\n        logInfo('[KEYCLOAK] Refreshing token: forced refresh');\n      } else if (!kc.tokenParsed || kc.isTokenExpired(minValidity)) {\n        refreshToken = true;\n        logInfo('[KEYCLOAK] Refreshing token: token expired');\n      }\n      if (!refreshToken) {\n        promise.setSuccess(false);\n      } else {\n        var params = 'grant_type=refresh_token&' + 'refresh_token=' + kc.refreshToken;\n        var url = kc.endpoints.token();\n        refreshQueue.push(promise);\n        if (refreshQueue.length == 1) {\n          var req = new XMLHttpRequest();\n          req.open('POST', url, true);\n          req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n          req.withCredentials = true;\n          params += '&client_id=' + encodeURIComponent(kc.clientId);\n          var timeLocal = new Date().getTime();\n          req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n              if (req.status == 200) {\n                logInfo('[KEYCLOAK] Token refreshed');\n                timeLocal = (timeLocal + new Date().getTime()) / 2;\n                var tokenResponse = JSON.parse(req.responseText);\n                setToken(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], timeLocal);\n                kc.onAuthRefreshSuccess && kc.onAuthRefreshSuccess();\n                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                  p.setSuccess(true);\n                }\n              } else {\n                logWarn('[KEYCLOAK] Failed to refresh token');\n                if (req.status == 400) {\n                  kc.clearToken();\n                }\n                kc.onAuthRefreshError && kc.onAuthRefreshError();\n                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                  p.setError(\"Failed to refresh token: An unexpected HTTP error occurred while attempting to refresh the token.\");\n                }\n              }\n            }\n          };\n          req.send(params);\n        }\n      }\n    };\n    if (loginIframe.enable) {\n      var iframePromise = checkLoginIframe();\n      iframePromise.then(function () {\n        exec();\n      }).catch(function (error) {\n        promise.setError(error);\n      });\n    } else {\n      exec();\n    }\n    return promise.promise;\n  };\n  kc.clearToken = function () {\n    if (kc.token) {\n      setToken(null, null, null);\n      kc.onAuthLogout && kc.onAuthLogout();\n      if (kc.loginRequired) {\n        kc.login();\n      }\n    }\n  };\n  function getRealmUrl() {\n    if (typeof kc.authServerUrl !== 'undefined') {\n      if (kc.authServerUrl.charAt(kc.authServerUrl.length - 1) == '/') {\n        return kc.authServerUrl + 'realms/' + encodeURIComponent(kc.realm);\n      } else {\n        return kc.authServerUrl + '/realms/' + encodeURIComponent(kc.realm);\n      }\n    } else {\n      return undefined;\n    }\n  }\n  function getOrigin() {\n    if (!window.location.origin) {\n      return window.location.protocol + \"//\" + window.location.hostname + (window.location.port ? ':' + window.location.port : '');\n    } else {\n      return window.location.origin;\n    }\n  }\n  function processCallback(oauth, promise) {\n    var code = oauth.code;\n    var error = oauth.error;\n    var prompt = oauth.prompt;\n    var timeLocal = new Date().getTime();\n    if (oauth['kc_action_status']) {\n      kc.onActionUpdate && kc.onActionUpdate(oauth['kc_action_status'], oauth['kc_action']);\n    }\n    if (error) {\n      if (prompt != 'none') {\n        if (oauth.error_description && oauth.error_description === \"authentication_expired\") {\n          kc.login(oauth.loginOptions);\n        } else {\n          var errorData = {\n            error: error,\n            error_description: oauth.error_description\n          };\n          kc.onAuthError && kc.onAuthError(errorData);\n          promise && promise.setError(errorData);\n        }\n      } else {\n        promise && promise.setSuccess();\n      }\n      return;\n    } else if (kc.flow != 'standard' && (oauth.access_token || oauth.id_token)) {\n      authSuccess(oauth.access_token, null, oauth.id_token, true);\n    }\n    if (kc.flow != 'implicit' && code) {\n      var params = 'code=' + code + '&grant_type=authorization_code';\n      var url = kc.endpoints.token();\n      var req = new XMLHttpRequest();\n      req.open('POST', url, true);\n      req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n      params += '&client_id=' + encodeURIComponent(kc.clientId);\n      params += '&redirect_uri=' + oauth.redirectUri;\n      if (oauth.pkceCodeVerifier) {\n        params += '&code_verifier=' + oauth.pkceCodeVerifier;\n      }\n      req.withCredentials = true;\n      req.onreadystatechange = function () {\n        if (req.readyState == 4) {\n          if (req.status == 200) {\n            var tokenResponse = JSON.parse(req.responseText);\n            authSuccess(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], kc.flow === 'standard');\n            scheduleCheckIframe();\n          } else {\n            kc.onAuthError && kc.onAuthError();\n            promise && promise.setError();\n          }\n        }\n      };\n      req.send(params);\n    }\n    function authSuccess(accessToken, refreshToken, idToken, fulfillPromise) {\n      timeLocal = (timeLocal + new Date().getTime()) / 2;\n      setToken(accessToken, refreshToken, idToken, timeLocal);\n      if (useNonce && kc.idTokenParsed && kc.idTokenParsed.nonce != oauth.storedNonce) {\n        logInfo('[KEYCLOAK] Invalid nonce, clearing token');\n        kc.clearToken();\n        promise && promise.setError();\n      } else {\n        if (fulfillPromise) {\n          kc.onAuthSuccess && kc.onAuthSuccess();\n          promise && promise.setSuccess();\n        }\n      }\n    }\n  }\n  function loadConfig() {\n    var promise = createPromise();\n    var configUrl;\n    if (typeof config === 'string') {\n      configUrl = config;\n    }\n    function setupOidcEndoints(oidcConfiguration) {\n      if (!oidcConfiguration) {\n        kc.endpoints = {\n          authorize: function () {\n            return getRealmUrl() + '/protocol/openid-connect/auth';\n          },\n          token: function () {\n            return getRealmUrl() + '/protocol/openid-connect/token';\n          },\n          logout: function () {\n            return getRealmUrl() + '/protocol/openid-connect/logout';\n          },\n          checkSessionIframe: function () {\n            return getRealmUrl() + '/protocol/openid-connect/login-status-iframe.html';\n          },\n          thirdPartyCookiesIframe: function () {\n            return getRealmUrl() + '/protocol/openid-connect/3p-cookies/step1.html';\n          },\n          register: function () {\n            return getRealmUrl() + '/protocol/openid-connect/registrations';\n          },\n          userinfo: function () {\n            return getRealmUrl() + '/protocol/openid-connect/userinfo';\n          }\n        };\n      } else {\n        kc.endpoints = {\n          authorize: function () {\n            return oidcConfiguration.authorization_endpoint;\n          },\n          token: function () {\n            return oidcConfiguration.token_endpoint;\n          },\n          logout: function () {\n            if (!oidcConfiguration.end_session_endpoint) {\n              throw \"Not supported by the OIDC server\";\n            }\n            return oidcConfiguration.end_session_endpoint;\n          },\n          checkSessionIframe: function () {\n            if (!oidcConfiguration.check_session_iframe) {\n              throw \"Not supported by the OIDC server\";\n            }\n            return oidcConfiguration.check_session_iframe;\n          },\n          register: function () {\n            throw 'Redirection to \"Register user\" page not supported in standard OIDC mode';\n          },\n          userinfo: function () {\n            if (!oidcConfiguration.userinfo_endpoint) {\n              throw \"Not supported by the OIDC server\";\n            }\n            return oidcConfiguration.userinfo_endpoint;\n          }\n        };\n      }\n    }\n    if (configUrl) {\n      var req = new XMLHttpRequest();\n      req.open('GET', configUrl, true);\n      req.setRequestHeader('Accept', 'application/json');\n      req.onreadystatechange = function () {\n        if (req.readyState == 4) {\n          if (req.status == 200 || fileLoaded(req)) {\n            var config = JSON.parse(req.responseText);\n            kc.authServerUrl = config['auth-server-url'];\n            kc.realm = config['realm'];\n            kc.clientId = config['resource'];\n            setupOidcEndoints(null);\n            promise.setSuccess();\n          } else {\n            promise.setError();\n          }\n        }\n      };\n      req.send();\n    } else {\n      kc.clientId = config.clientId;\n      var oidcProvider = config['oidcProvider'];\n      if (!oidcProvider) {\n        kc.authServerUrl = config.url;\n        kc.realm = config.realm;\n        setupOidcEndoints(null);\n        promise.setSuccess();\n      } else {\n        if (typeof oidcProvider === 'string') {\n          var oidcProviderConfigUrl;\n          if (oidcProvider.charAt(oidcProvider.length - 1) == '/') {\n            oidcProviderConfigUrl = oidcProvider + '.well-known/openid-configuration';\n          } else {\n            oidcProviderConfigUrl = oidcProvider + '/.well-known/openid-configuration';\n          }\n          var req = new XMLHttpRequest();\n          req.open('GET', oidcProviderConfigUrl, true);\n          req.setRequestHeader('Accept', 'application/json');\n          req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n              if (req.status == 200 || fileLoaded(req)) {\n                var oidcProviderConfig = JSON.parse(req.responseText);\n                setupOidcEndoints(oidcProviderConfig);\n                promise.setSuccess();\n              } else {\n                promise.setError();\n              }\n            }\n          };\n          req.send();\n        } else {\n          setupOidcEndoints(oidcProvider);\n          promise.setSuccess();\n        }\n      }\n    }\n    return promise.promise;\n  }\n  function fileLoaded(xhr) {\n    return xhr.status == 0 && xhr.responseText && xhr.responseURL.startsWith('file:');\n  }\n  function setToken(token, refreshToken, idToken, timeLocal) {\n    if (kc.tokenTimeoutHandle) {\n      clearTimeout(kc.tokenTimeoutHandle);\n      kc.tokenTimeoutHandle = null;\n    }\n    if (refreshToken) {\n      kc.refreshToken = refreshToken;\n      kc.refreshTokenParsed = decodeToken(refreshToken);\n    } else {\n      delete kc.refreshToken;\n      delete kc.refreshTokenParsed;\n    }\n    if (idToken) {\n      kc.idToken = idToken;\n      kc.idTokenParsed = decodeToken(idToken);\n    } else {\n      delete kc.idToken;\n      delete kc.idTokenParsed;\n    }\n    if (token) {\n      kc.token = token;\n      kc.tokenParsed = decodeToken(token);\n      kc.sessionId = kc.tokenParsed.sid;\n      kc.authenticated = true;\n      kc.subject = kc.tokenParsed.sub;\n      kc.realmAccess = kc.tokenParsed.realm_access;\n      kc.resourceAccess = kc.tokenParsed.resource_access;\n      if (timeLocal) {\n        kc.timeSkew = Math.floor(timeLocal / 1000) - kc.tokenParsed.iat;\n      }\n      if (kc.timeSkew != null) {\n        logInfo('[KEYCLOAK] Estimated time difference between browser and server is ' + kc.timeSkew + ' seconds');\n        if (kc.onTokenExpired) {\n          var expiresIn = (kc.tokenParsed['exp'] - new Date().getTime() / 1000 + kc.timeSkew) * 1000;\n          logInfo('[KEYCLOAK] Token expires in ' + Math.round(expiresIn / 1000) + ' s');\n          if (expiresIn <= 0) {\n            kc.onTokenExpired();\n          } else {\n            kc.tokenTimeoutHandle = setTimeout(kc.onTokenExpired, expiresIn);\n          }\n        }\n      }\n    } else {\n      delete kc.token;\n      delete kc.tokenParsed;\n      delete kc.subject;\n      delete kc.realmAccess;\n      delete kc.resourceAccess;\n      kc.authenticated = false;\n    }\n  }\n  function createUUID() {\n    if (typeof crypto === \"undefined\" || typeof crypto.randomUUID === \"undefined\") {\n      throw new Error(\"Web Crypto API is not available.\");\n    }\n    return crypto.randomUUID();\n  }\n  function parseCallback(url) {\n    var oauth = parseCallbackUrl(url);\n    if (!oauth) {\n      return;\n    }\n    var oauthState = callbackStorage.get(oauth.state);\n    if (oauthState) {\n      oauth.valid = true;\n      oauth.redirectUri = oauthState.redirectUri;\n      oauth.storedNonce = oauthState.nonce;\n      oauth.prompt = oauthState.prompt;\n      oauth.pkceCodeVerifier = oauthState.pkceCodeVerifier;\n      oauth.loginOptions = oauthState.loginOptions;\n    }\n    return oauth;\n  }\n  function parseCallbackUrl(url) {\n    var supportedParams;\n    switch (kc.flow) {\n      case 'standard':\n        supportedParams = ['code', 'state', 'session_state', 'kc_action_status', 'kc_action', 'iss'];\n        break;\n      case 'implicit':\n        supportedParams = ['access_token', 'token_type', 'id_token', 'state', 'session_state', 'expires_in', 'kc_action_status', 'kc_action', 'iss'];\n        break;\n      case 'hybrid':\n        supportedParams = ['access_token', 'token_type', 'id_token', 'code', 'state', 'session_state', 'expires_in', 'kc_action_status', 'kc_action', 'iss'];\n        break;\n    }\n    supportedParams.push('error');\n    supportedParams.push('error_description');\n    supportedParams.push('error_uri');\n    var queryIndex = url.indexOf('?');\n    var fragmentIndex = url.indexOf('#');\n    var newUrl;\n    var parsed;\n    if (kc.responseMode === 'query' && queryIndex !== -1) {\n      newUrl = url.substring(0, queryIndex);\n      parsed = parseCallbackParams(url.substring(queryIndex + 1, fragmentIndex !== -1 ? fragmentIndex : url.length), supportedParams);\n      if (parsed.paramsString !== '') {\n        newUrl += '?' + parsed.paramsString;\n      }\n      if (fragmentIndex !== -1) {\n        newUrl += url.substring(fragmentIndex);\n      }\n    } else if (kc.responseMode === 'fragment' && fragmentIndex !== -1) {\n      newUrl = url.substring(0, fragmentIndex);\n      parsed = parseCallbackParams(url.substring(fragmentIndex + 1), supportedParams);\n      if (parsed.paramsString !== '') {\n        newUrl += '#' + parsed.paramsString;\n      }\n    }\n    if (parsed && parsed.oauthParams) {\n      if (kc.flow === 'standard' || kc.flow === 'hybrid') {\n        if ((parsed.oauthParams.code || parsed.oauthParams.error) && parsed.oauthParams.state) {\n          parsed.oauthParams.newUrl = newUrl;\n          return parsed.oauthParams;\n        }\n      } else if (kc.flow === 'implicit') {\n        if ((parsed.oauthParams.access_token || parsed.oauthParams.error) && parsed.oauthParams.state) {\n          parsed.oauthParams.newUrl = newUrl;\n          return parsed.oauthParams;\n        }\n      }\n    }\n  }\n  function parseCallbackParams(paramsString, supportedParams) {\n    var p = paramsString.split('&');\n    var result = {\n      paramsString: '',\n      oauthParams: {}\n    };\n    for (var i = 0; i < p.length; i++) {\n      var split = p[i].indexOf(\"=\");\n      var key = p[i].slice(0, split);\n      if (supportedParams.indexOf(key) !== -1) {\n        result.oauthParams[key] = p[i].slice(split + 1);\n      } else {\n        if (result.paramsString !== '') {\n          result.paramsString += '&';\n        }\n        result.paramsString += p[i];\n      }\n    }\n    return result;\n  }\n  function createPromise() {\n    // Need to create a native Promise which also preserves the\n    // interface of the custom promise type previously used by the API\n    var p = {\n      setSuccess: function (result) {\n        p.resolve(result);\n      },\n      setError: function (result) {\n        p.reject(result);\n      }\n    };\n    p.promise = new Promise(function (resolve, reject) {\n      p.resolve = resolve;\n      p.reject = reject;\n    });\n    return p;\n  }\n\n  // Function to extend existing native Promise with timeout\n  function applyTimeoutToPromise(promise, timeout, errorMessage) {\n    var timeoutHandle = null;\n    var timeoutPromise = new Promise(function (resolve, reject) {\n      timeoutHandle = setTimeout(function () {\n        reject({\n          \"error\": errorMessage || \"Promise is not settled within timeout of \" + timeout + \"ms\"\n        });\n      }, timeout);\n    });\n    return Promise.race([promise, timeoutPromise]).finally(function () {\n      clearTimeout(timeoutHandle);\n    });\n  }\n  function setupCheckLoginIframe() {\n    var promise = createPromise();\n    if (!loginIframe.enable) {\n      promise.setSuccess();\n      return promise.promise;\n    }\n    if (loginIframe.iframe) {\n      promise.setSuccess();\n      return promise.promise;\n    }\n    var iframe = document.createElement('iframe');\n    loginIframe.iframe = iframe;\n    iframe.onload = function () {\n      var authUrl = kc.endpoints.authorize();\n      if (authUrl.charAt(0) === '/') {\n        loginIframe.iframeOrigin = getOrigin();\n      } else {\n        loginIframe.iframeOrigin = authUrl.substring(0, authUrl.indexOf('/', 8));\n      }\n      promise.setSuccess();\n    };\n    var src = kc.endpoints.checkSessionIframe();\n    iframe.setAttribute('src', src);\n    iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n    iframe.setAttribute('title', 'keycloak-session-iframe');\n    iframe.style.display = 'none';\n    document.body.appendChild(iframe);\n    var messageCallback = function (event) {\n      if (event.origin !== loginIframe.iframeOrigin || loginIframe.iframe.contentWindow !== event.source) {\n        return;\n      }\n      if (!(event.data == 'unchanged' || event.data == 'changed' || event.data == 'error')) {\n        return;\n      }\n      if (event.data != 'unchanged') {\n        kc.clearToken();\n      }\n      var callbacks = loginIframe.callbackList.splice(0, loginIframe.callbackList.length);\n      for (var i = callbacks.length - 1; i >= 0; --i) {\n        var promise = callbacks[i];\n        if (event.data == 'error') {\n          promise.setError();\n        } else {\n          promise.setSuccess(event.data == 'unchanged');\n        }\n      }\n    };\n    window.addEventListener('message', messageCallback, false);\n    return promise.promise;\n  }\n  function scheduleCheckIframe() {\n    if (loginIframe.enable) {\n      if (kc.token) {\n        setTimeout(function () {\n          checkLoginIframe().then(function (unchanged) {\n            if (unchanged) {\n              scheduleCheckIframe();\n            }\n          });\n        }, loginIframe.interval * 1000);\n      }\n    }\n  }\n  function checkLoginIframe() {\n    var promise = createPromise();\n    if (loginIframe.iframe && loginIframe.iframeOrigin) {\n      var msg = kc.clientId + ' ' + (kc.sessionId ? kc.sessionId : '');\n      loginIframe.callbackList.push(promise);\n      var origin = loginIframe.iframeOrigin;\n      if (loginIframe.callbackList.length == 1) {\n        loginIframe.iframe.contentWindow.postMessage(msg, origin);\n      }\n    } else {\n      promise.setSuccess();\n    }\n    return promise.promise;\n  }\n  function check3pCookiesSupported() {\n    var promise = createPromise();\n    if ((loginIframe.enable || kc.silentCheckSsoRedirectUri) && typeof kc.endpoints.thirdPartyCookiesIframe === 'function') {\n      var iframe = document.createElement('iframe');\n      iframe.setAttribute('src', kc.endpoints.thirdPartyCookiesIframe());\n      iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n      iframe.setAttribute('title', 'keycloak-3p-check-iframe');\n      iframe.style.display = 'none';\n      document.body.appendChild(iframe);\n      var messageCallback = function (event) {\n        if (iframe.contentWindow !== event.source) {\n          return;\n        }\n        if (event.data !== \"supported\" && event.data !== \"unsupported\") {\n          return;\n        } else if (event.data === \"unsupported\") {\n          logWarn(\"[KEYCLOAK] Your browser is blocking access to 3rd-party cookies, this means:\\n\\n\" + \" - It is not possible to retrieve tokens without redirecting to the Keycloak server (a.k.a. no support for silent authentication).\\n\" + \" - It is not possible to automatically detect changes to the session status (such as the user logging out in another tab).\\n\\n\" + \"For more information see: https://www.keycloak.org/securing-apps/javascript-adapter#_modern_browsers\");\n          loginIframe.enable = false;\n          if (kc.silentCheckSsoFallback) {\n            kc.silentCheckSsoRedirectUri = false;\n          }\n        }\n        document.body.removeChild(iframe);\n        window.removeEventListener(\"message\", messageCallback);\n        promise.setSuccess();\n      };\n      window.addEventListener('message', messageCallback, false);\n    } else {\n      promise.setSuccess();\n    }\n    return applyTimeoutToPromise(promise.promise, kc.messageReceiveTimeout, \"Timeout when waiting for 3rd party check iframe message.\");\n  }\n  function loadAdapter(type) {\n    if (!type || type == 'default') {\n      return {\n        login: function () {\n          var _ref4 = _asyncToGenerator(function* (options) {\n            window.location.assign(yield kc.createLoginUrl(options));\n            return createPromise().promise;\n          });\n          return function login(_x5) {\n            return _ref4.apply(this, arguments);\n          };\n        }(),\n        logout: function () {\n          var _ref5 = _asyncToGenerator(function* (options) {\n            const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n            if (logoutMethod === \"GET\") {\n              window.location.replace(kc.createLogoutUrl(options));\n              return;\n            }\n\n            // Create form to send POST request.\n            const form = document.createElement(\"form\");\n            form.setAttribute(\"method\", \"POST\");\n            form.setAttribute(\"action\", kc.createLogoutUrl(options));\n            form.style.display = \"none\";\n\n            // Add data to form as hidden input fields.\n            const data = {\n              id_token_hint: kc.idToken,\n              client_id: kc.clientId,\n              post_logout_redirect_uri: adapter.redirectUri(options, false)\n            };\n            for (const [name, value] of Object.entries(data)) {\n              const input = document.createElement(\"input\");\n              input.setAttribute(\"type\", \"hidden\");\n              input.setAttribute(\"name\", name);\n              input.setAttribute(\"value\", value);\n              form.appendChild(input);\n            }\n\n            // Append form to page and submit it to perform logout and redirect.\n            document.body.appendChild(form);\n            form.submit();\n          });\n          return function logout(_x6) {\n            return _ref5.apply(this, arguments);\n          };\n        }(),\n        register: function () {\n          var _ref6 = _asyncToGenerator(function* (options) {\n            window.location.assign(yield kc.createRegisterUrl(options));\n            return createPromise().promise;\n          });\n          return function register(_x7) {\n            return _ref6.apply(this, arguments);\n          };\n        }(),\n        accountManagement: function () {\n          var accountUrl = kc.createAccountUrl();\n          if (typeof accountUrl !== 'undefined') {\n            window.location.href = accountUrl;\n          } else {\n            throw \"Not supported by the OIDC server\";\n          }\n          return createPromise().promise;\n        },\n        redirectUri: function (options, encodeHash) {\n          if (arguments.length == 1) {\n            encodeHash = true;\n          }\n          if (options && options.redirectUri) {\n            return options.redirectUri;\n          } else if (kc.redirectUri) {\n            return kc.redirectUri;\n          } else {\n            return location.href;\n          }\n        }\n      };\n    }\n    if (type == 'cordova') {\n      loginIframe.enable = false;\n      var cordovaOpenWindowWrapper = function (loginUrl, target, options) {\n        if (window.cordova && window.cordova.InAppBrowser) {\n          // Use inappbrowser for IOS and Android if available\n          return window.cordova.InAppBrowser.open(loginUrl, target, options);\n        } else {\n          return window.open(loginUrl, target, options);\n        }\n      };\n      var shallowCloneCordovaOptions = function (userOptions) {\n        if (userOptions && userOptions.cordovaOptions) {\n          return Object.keys(userOptions.cordovaOptions).reduce(function (options, optionName) {\n            options[optionName] = userOptions.cordovaOptions[optionName];\n            return options;\n          }, {});\n        } else {\n          return {};\n        }\n      };\n      var formatCordovaOptions = function (cordovaOptions) {\n        return Object.keys(cordovaOptions).reduce(function (options, optionName) {\n          options.push(optionName + \"=\" + cordovaOptions[optionName]);\n          return options;\n        }, []).join(\",\");\n      };\n      var createCordovaOptions = function (userOptions) {\n        var cordovaOptions = shallowCloneCordovaOptions(userOptions);\n        cordovaOptions.location = 'no';\n        if (userOptions && userOptions.prompt == 'none') {\n          cordovaOptions.hidden = 'yes';\n        }\n        return formatCordovaOptions(cordovaOptions);\n      };\n      var getCordovaRedirectUri = function () {\n        return kc.redirectUri || 'http://localhost';\n      };\n      return {\n        login: function () {\n          var _ref7 = _asyncToGenerator(function* (options) {\n            var promise = createPromise();\n            var cordovaOptions = createCordovaOptions(options);\n            var loginUrl = yield kc.createLoginUrl(options);\n            var ref = cordovaOpenWindowWrapper(loginUrl, '_blank', cordovaOptions);\n            var completed = false;\n            var closed = false;\n            var closeBrowser = function () {\n              closed = true;\n              ref.close();\n            };\n            ref.addEventListener('loadstart', function (event) {\n              if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                var callback = parseCallback(event.url);\n                processCallback(callback, promise);\n                closeBrowser();\n                completed = true;\n              }\n            });\n            ref.addEventListener('loaderror', function (event) {\n              if (!completed) {\n                if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                  var callback = parseCallback(event.url);\n                  processCallback(callback, promise);\n                  closeBrowser();\n                  completed = true;\n                } else {\n                  promise.setError();\n                  closeBrowser();\n                }\n              }\n            });\n            ref.addEventListener('exit', function (event) {\n              if (!closed) {\n                promise.setError({\n                  reason: \"closed_by_user\"\n                });\n              }\n            });\n            return promise.promise;\n          });\n          return function login(_x8) {\n            return _ref7.apply(this, arguments);\n          };\n        }(),\n        logout: function (options) {\n          var promise = createPromise();\n          var logoutUrl = kc.createLogoutUrl(options);\n          var ref = cordovaOpenWindowWrapper(logoutUrl, '_blank', 'location=no,hidden=yes,clearcache=yes');\n          var error;\n          ref.addEventListener('loadstart', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              ref.close();\n            }\n          });\n          ref.addEventListener('loaderror', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              ref.close();\n            } else {\n              error = true;\n              ref.close();\n            }\n          });\n          ref.addEventListener('exit', function (event) {\n            if (error) {\n              promise.setError();\n            } else {\n              kc.clearToken();\n              promise.setSuccess();\n            }\n          });\n          return promise.promise;\n        },\n        register: function () {\n          var _ref8 = _asyncToGenerator(function* (options) {\n            var promise = createPromise();\n            var registerUrl = yield kc.createRegisterUrl();\n            var cordovaOptions = createCordovaOptions(options);\n            var ref = cordovaOpenWindowWrapper(registerUrl, '_blank', cordovaOptions);\n            ref.addEventListener('loadstart', function (event) {\n              if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                ref.close();\n                var oauth = parseCallback(event.url);\n                processCallback(oauth, promise);\n              }\n            });\n            return promise.promise;\n          });\n          return function register(_x9) {\n            return _ref8.apply(this, arguments);\n          };\n        }(),\n        accountManagement: function () {\n          var accountUrl = kc.createAccountUrl();\n          if (typeof accountUrl !== 'undefined') {\n            var ref = cordovaOpenWindowWrapper(accountUrl, '_blank', 'location=no');\n            ref.addEventListener('loadstart', function (event) {\n              if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                ref.close();\n              }\n            });\n          } else {\n            throw \"Not supported by the OIDC server\";\n          }\n        },\n        redirectUri: function (options) {\n          return getCordovaRedirectUri();\n        }\n      };\n    }\n    if (type == 'cordova-native') {\n      loginIframe.enable = false;\n      return {\n        login: function () {\n          var _ref9 = _asyncToGenerator(function* (options) {\n            var promise = createPromise();\n            var loginUrl = yield kc.createLoginUrl(options);\n            universalLinks.subscribe('keycloak', function (event) {\n              universalLinks.unsubscribe('keycloak');\n              window.cordova.plugins.browsertab.close();\n              var oauth = parseCallback(event.url);\n              processCallback(oauth, promise);\n            });\n            window.cordova.plugins.browsertab.openUrl(loginUrl);\n            return promise.promise;\n          });\n          return function login(_x10) {\n            return _ref9.apply(this, arguments);\n          };\n        }(),\n        logout: function (options) {\n          var promise = createPromise();\n          var logoutUrl = kc.createLogoutUrl(options);\n          universalLinks.subscribe('keycloak', function (event) {\n            universalLinks.unsubscribe('keycloak');\n            window.cordova.plugins.browsertab.close();\n            kc.clearToken();\n            promise.setSuccess();\n          });\n          window.cordova.plugins.browsertab.openUrl(logoutUrl);\n          return promise.promise;\n        },\n        register: function () {\n          var _ref10 = _asyncToGenerator(function* (options) {\n            var promise = createPromise();\n            var registerUrl = yield kc.createRegisterUrl(options);\n            universalLinks.subscribe('keycloak', function (event) {\n              universalLinks.unsubscribe('keycloak');\n              window.cordova.plugins.browsertab.close();\n              var oauth = parseCallback(event.url);\n              processCallback(oauth, promise);\n            });\n            window.cordova.plugins.browsertab.openUrl(registerUrl);\n            return promise.promise;\n          });\n          return function register(_x11) {\n            return _ref10.apply(this, arguments);\n          };\n        }(),\n        accountManagement: function () {\n          var accountUrl = kc.createAccountUrl();\n          if (typeof accountUrl !== 'undefined') {\n            window.cordova.plugins.browsertab.openUrl(accountUrl);\n          } else {\n            throw \"Not supported by the OIDC server\";\n          }\n        },\n        redirectUri: function (options) {\n          if (options && options.redirectUri) {\n            return options.redirectUri;\n          } else if (kc.redirectUri) {\n            return kc.redirectUri;\n          } else {\n            return \"http://localhost\";\n          }\n        }\n      };\n    }\n    throw 'invalid adapter type: ' + type;\n  }\n  const STORAGE_KEY_PREFIX = 'kc-callback-';\n  var LocalStorage = function () {\n    if (!(this instanceof LocalStorage)) {\n      return new LocalStorage();\n    }\n    localStorage.setItem('kc-test', 'test');\n    localStorage.removeItem('kc-test');\n    var cs = this;\n\n    /**\n     * Clears all values from local storage that are no longer valid.\n     */\n    function clearInvalidValues() {\n      const currentTime = Date.now();\n      for (const [key, value] of getStoredEntries()) {\n        // Attempt to parse the expiry time from the value.\n        const expiry = parseExpiry(value);\n\n        // Discard the value if it is malformed or expired.\n        if (expiry === null || expiry < currentTime) {\n          localStorage.removeItem(key);\n        }\n      }\n    }\n\n    /**\n     * Clears all known values from local storage.\n     */\n    function clearAllValues() {\n      for (const [key] of getStoredEntries()) {\n        localStorage.removeItem(key);\n      }\n    }\n\n    /**\n     * Gets all entries stored in local storage that are known to be managed by this class.\n     * @returns {Array<[string, unknown]>} An array of key-value pairs.\n     */\n    function getStoredEntries() {\n      return Object.entries(localStorage).filter(([key]) => key.startsWith(STORAGE_KEY_PREFIX));\n    }\n\n    /**\n     * Parses the expiry time from a value stored in local storage.\n     * @param {unknown} value\n     * @returns {number | null} The expiry time in milliseconds, or `null` if the value is malformed.\n     */\n    function parseExpiry(value) {\n      let parsedValue;\n\n      // Attempt to parse the value as JSON.\n      try {\n        parsedValue = JSON.parse(value);\n      } catch (error) {\n        return null;\n      }\n\n      // Attempt to extract the 'expires' property.\n      if (isObject(parsedValue) && 'expires' in parsedValue && typeof parsedValue.expires === 'number') {\n        return parsedValue.expires;\n      }\n      return null;\n    }\n    cs.get = function (state) {\n      if (!state) {\n        return;\n      }\n      var key = STORAGE_KEY_PREFIX + state;\n      var value = localStorage.getItem(key);\n      if (value) {\n        localStorage.removeItem(key);\n        value = JSON.parse(value);\n      }\n      clearInvalidValues();\n      return value;\n    };\n    cs.add = function (state) {\n      clearInvalidValues();\n      const key = STORAGE_KEY_PREFIX + state.state;\n      const value = JSON.stringify({\n        ...state,\n        // Set the expiry time to 1 hour from now.\n        expires: Date.now() + 60 * 60 * 1000\n      });\n      try {\n        localStorage.setItem(key, value);\n      } catch (error) {\n        // If the storage is full, clear all known values and try again.\n        clearAllValues();\n        localStorage.setItem(key, value);\n      }\n    };\n  };\n  var CookieStorage = function () {\n    if (!(this instanceof CookieStorage)) {\n      return new CookieStorage();\n    }\n    var cs = this;\n    cs.get = function (state) {\n      if (!state) {\n        return;\n      }\n      var value = getCookie(STORAGE_KEY_PREFIX + state);\n      setCookie(STORAGE_KEY_PREFIX + state, '', cookieExpiration(-100));\n      if (value) {\n        return JSON.parse(value);\n      }\n    };\n    cs.add = function (state) {\n      setCookie(STORAGE_KEY_PREFIX + state.state, JSON.stringify(state), cookieExpiration(60));\n    };\n    cs.removeItem = function (key) {\n      setCookie(key, '', cookieExpiration(-100));\n    };\n    var cookieExpiration = function (minutes) {\n      var exp = new Date();\n      exp.setTime(exp.getTime() + minutes * 60 * 1000);\n      return exp;\n    };\n    var getCookie = function (key) {\n      var name = key + '=';\n      var ca = document.cookie.split(';');\n      for (var i = 0; i < ca.length; i++) {\n        var c = ca[i];\n        while (c.charAt(0) == ' ') {\n          c = c.substring(1);\n        }\n        if (c.indexOf(name) == 0) {\n          return c.substring(name.length, c.length);\n        }\n      }\n      return '';\n    };\n    var setCookie = function (key, value, expirationDate) {\n      var cookie = key + '=' + value + '; ' + 'expires=' + expirationDate.toUTCString() + '; ';\n      document.cookie = cookie;\n    };\n  };\n  function createCallbackStorage() {\n    try {\n      return new LocalStorage();\n    } catch (err) {}\n    return new CookieStorage();\n  }\n  function createLogger(fn) {\n    return function () {\n      if (kc.enableLogging) {\n        fn.apply(console, Array.prototype.slice.call(arguments));\n      }\n    };\n  }\n}\nexport default Keycloak;\n\n/**\n * @param {ArrayBuffer} bytes\n * @see https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\n */\nfunction bytesToBase64(bytes) {\n  const binString = String.fromCodePoint(...bytes);\n  return btoa(binString);\n}\n\n/**\n * @param {string} message\n * @see https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/digest#basic_example\n */\nfunction sha256Digest(_x12) {\n  return _sha256Digest.apply(this, arguments);\n}\n/**\n * @param {string} token\n */\nfunction _sha256Digest() {\n  _sha256Digest = _asyncToGenerator(function* (message) {\n    const encoder = new TextEncoder();\n    const data = encoder.encode(message);\n    if (typeof crypto === \"undefined\" || typeof crypto.subtle === \"undefined\") {\n      throw new Error(\"Web Crypto API is not available.\");\n    }\n    return yield crypto.subtle.digest(\"SHA-256\", data);\n  });\n  return _sha256Digest.apply(this, arguments);\n}\nfunction decodeToken(token) {\n  const [header, payload] = token.split(\".\");\n  if (typeof payload !== \"string\") {\n    throw new Error(\"Unable to decode token, payload not found.\");\n  }\n  let decoded;\n  try {\n    decoded = base64UrlDecode(payload);\n  } catch (error) {\n    throw new Error(\"Unable to decode token, payload is not a valid Base64URL value.\", {\n      cause: error\n    });\n  }\n  try {\n    return JSON.parse(decoded);\n  } catch (error) {\n    throw new Error(\"Unable to decode token, payload is not a valid JSON value.\", {\n      cause: error\n    });\n  }\n}\n\n/**\n * @param {string} input\n */\nfunction base64UrlDecode(input) {\n  let output = input.replaceAll(\"-\", \"+\").replaceAll(\"_\", \"/\");\n  switch (output.length % 4) {\n    case 0:\n      break;\n    case 2:\n      output += \"==\";\n      break;\n    case 3:\n      output += \"=\";\n      break;\n    default:\n      throw new Error(\"Input is not of the correct length.\");\n  }\n  try {\n    return b64DecodeUnicode(output);\n  } catch (error) {\n    return atob(output);\n  }\n}\n\n/**\n * @param {string} input\n */\nfunction b64DecodeUnicode(input) {\n  return decodeURIComponent(atob(input).replace(/(.)/g, (m, p) => {\n    let code = p.charCodeAt(0).toString(16).toUpperCase();\n    if (code.length < 2) {\n      code = \"0\" + code;\n    }\n    return \"%\" + code;\n  }));\n}\n\n/**\n * Check if the input is an object that can be operated on.\n * @param {unknown} input\n */\nfunction isObject(input) {\n  return typeof input === 'object' && input !== null;\n}", "map": {"version": 3, "names": ["Keycloak", "config", "Error", "isObject", "requiredProperties", "property", "kc", "adapter", "refreshQueue", "callbackStorage", "loginIframe", "enable", "callbackList", "interval", "didInitialize", "useNonce", "logInfo", "createLogger", "console", "info", "log<PERSON>arn", "warn", "globalThis", "isSecureContext", "init", "initOptions", "authenticated", "createCallbackStorage", "adapters", "indexOf", "loadAdapter", "window", "Cordova", "<PERSON><PERSON>", "checkLoginIframe", "checkLoginIframeInterval", "onLoad", "loginRequired", "responseMode", "flow", "responseType", "timeSkew", "redirectUri", "silentCheckSsoRedirectUri", "silentCheckSsoFallback", "pkceMethod", "TypeError", "enableLogging", "logoutMethod", "scope", "acr<PERSON><PERSON><PERSON>", "messageReceiveTimeout", "promise", "createPromise", "initPromise", "then", "onReady", "setSuccess", "catch", "error", "setError", "config<PERSON>rom<PERSON>", "loadConfig", "do<PERSON><PERSON><PERSON>", "prompt", "options", "locale", "login", "checkSsoSilently", "_ref", "_asyncToGenerator", "ifrm", "document", "createElement", "src", "createLoginUrl", "setAttribute", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "messageCallback", "event", "origin", "location", "contentWindow", "source", "o<PERSON>h", "parse<PERSON><PERSON>back", "data", "processCallback", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "addEventListener", "apply", "arguments", "setupCheckLoginIframe", "unchanged", "processInit", "callback", "href", "history", "replaceState", "state", "newUrl", "valid", "token", "refreshToken", "setToken", "idToken", "onAuthSuccess", "scheduleCheckIframe", "updateToken", "onAuthError", "check3pCookiesSupported", "generateRandomData", "len", "crypto", "getRandomValues", "Uint8Array", "generateCodeVerifier", "generateRandomString", "alphabet", "randomData", "chars", "Array", "i", "charCodeAt", "length", "String", "fromCharCode", "generatePkceChallenge", "_x", "_x2", "_generatePkceChallenge", "codeVerifier", "hashBytes", "sha256Digest", "encodedHash", "bytesToBase64", "replace", "buildClaimsParameter", "requestedAcr", "claims", "id_token", "acr", "JSON", "stringify", "_ref2", "createUUID", "nonce", "callbackState", "encodeURIComponent", "loginOptions", "baseUrl", "action", "endpoints", "register", "authorize", "url", "clientId", "maxAge", "loginHint", "idpHint", "claimsParameter", "pkceChallenge", "pkceCodeVerifier", "cause", "add", "_x3", "logout", "createLogoutUrl", "createRegisterUrl", "_ref3", "_x4", "createAccountUrl", "realm", "getRealmUrl", "undefined", "accountManagement", "hasRealmRole", "role", "access", "realmAccess", "roles", "hasResourceRole", "resource", "resourceAccess", "loadUserProfile", "req", "XMLHttpRequest", "open", "setRequestHeader", "onreadystatechange", "readyState", "status", "profile", "parse", "responseText", "send", "loadUserInfo", "userinfo", "userInfo", "isTokenExpired", "minValidity", "tokenParsed", "expiresIn", "Math", "ceil", "Date", "getTime", "isNaN", "exec", "params", "push", "withCredentials", "timeLocal", "tokenResponse", "onAuthRefreshSuccess", "p", "pop", "clearToken", "onAuthRefreshError", "iframePromise", "onAuthLogout", "authServerUrl", "char<PERSON>t", "<PERSON><PERSON><PERSON><PERSON>", "protocol", "hostname", "port", "code", "onActionUpdate", "error_description", "errorData", "access_token", "authSuccess", "accessToken", "fulfillPromise", "idTokenParsed", "storedNonce", "configUrl", "setupOidcEndoints", "oidcConfiguration", "checkSessionIframe", "thirdPartyCookiesIframe", "authorization_endpoint", "token_endpoint", "end_session_endpoint", "check_session_iframe", "userinfo_endpoint", "fileLoaded", "oidcProvider", "oidcProviderConfigUrl", "oidcProviderConfig", "xhr", "responseURL", "startsWith", "tokenTimeoutHandle", "clearTimeout", "refreshTokenParsed", "decodeToken", "sessionId", "sid", "subject", "sub", "realm_access", "resource_access", "floor", "iat", "onTokenExpired", "round", "setTimeout", "randomUUID", "parseCallbackUrl", "oauthState", "get", "supportedParams", "queryIndex", "fragmentIndex", "parsed", "substring", "parseCallbackParams", "paramsString", "oauthParams", "split", "result", "key", "slice", "resolve", "reject", "Promise", "applyTimeoutToPromise", "timeout", "errorMessage", "timeoutH<PERSON>le", "timeoutPromise", "race", "finally", "iframe", "onload", "authUrl", "iframe<PERSON><PERSON>in", "callbacks", "splice", "msg", "postMessage", "type", "_ref4", "assign", "_x5", "_ref5", "form", "id_token_hint", "client_id", "post_logout_redirect_uri", "name", "value", "Object", "entries", "input", "submit", "_x6", "_ref6", "_x7", "accountUrl", "encodeHash", "cordovaOpenWindowWrapper", "loginUrl", "target", "InAppBrowser", "shallowCloneCordovaOptions", "userOptions", "cordovaOptions", "keys", "reduce", "optionName", "formatCordovaOptions", "join", "createCordovaOptions", "hidden", "getCordovaRedirectUri", "_ref7", "ref", "completed", "closed", "<PERSON><PERSON>rowser", "close", "reason", "_x8", "logoutUrl", "_ref8", "registerUrl", "_x9", "_ref9", "universalLinks", "subscribe", "unsubscribe", "plugins", "browsertab", "openUrl", "_x10", "_ref10", "_x11", "STORAGE_KEY_PREFIX", "LocalStorage", "localStorage", "setItem", "removeItem", "cs", "clearInvalidValues", "currentTime", "now", "getStoredEntries", "expiry", "parseExpiry", "clearAllValues", "filter", "parsedValue", "expires", "getItem", "Cookie<PERSON>torage", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "cookieExpiration", "minutes", "exp", "setTime", "ca", "cookie", "c", "expirationDate", "toUTCString", "err", "fn", "prototype", "call", "bytes", "binString", "fromCodePoint", "btoa", "_x12", "_sha256Digest", "message", "encoder", "TextEncoder", "encode", "subtle", "digest", "header", "payload", "decoded", "base64UrlDecode", "output", "replaceAll", "b64DecodeUnicode", "atob", "decodeURIComponent", "m", "toString", "toUpperCase"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/keycloak-js/lib/keycloak.js"], "sourcesContent": ["/*\n * Copyright 2016 Red Hat, Inc. and/or its affiliates\n * and other contributors as indicated by the <AUTHOR>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction Keycloak (config) {\n    if (!(this instanceof Keycloak)) {\n        throw new Error(\"The 'Keycloak' constructor must be invoked with 'new'.\")\n    }\n\n    if (typeof config !== 'string' && !isObject(config)) {\n        throw new Error(\"The 'Keycloak' constructor must be provided with a configuration object, or a URL to a JSON configuration file.\");\n    }\n\n    if (isObject(config)) {\n        const requiredProperties = 'oidcProvider' in config\n            ? ['clientId']\n            : ['url', 'realm', 'clientId'];\n\n        for (const property of requiredProperties) {\n            if (!config[property]) {\n                throw new Error(`The configuration object is missing the required '${property}' property.`);\n            }\n        }\n    }\n\n    var kc = this;\n    var adapter;\n    var refreshQueue = [];\n    var callbackStorage;\n\n    var loginIframe = {\n        enable: true,\n        callbackList: [],\n        interval: 5\n    };\n\n    kc.didInitialize = false;\n\n    var useNonce = true;\n    var logInfo = createLogger(console.info);\n    var logWarn = createLogger(console.warn);\n\n    if (!globalThis.isSecureContext) {\n        logWarn(\n            \"[KEYCLOAK] Keycloak JS must be used in a 'secure context' to function properly as it relies on browser APIs that are otherwise not available.\\n\" +\n            \"Continuing to run your application insecurely will lead to unexpected behavior and breakage.\\n\\n\" +\n            \"For more information see: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts\"\n        );\n    }\n\n    kc.init = function (initOptions = {}) {\n        if (kc.didInitialize) {\n            throw new Error(\"A 'Keycloak' instance can only be initialized once.\");\n        }\n\n        kc.didInitialize = true;\n\n        kc.authenticated = false;\n\n        callbackStorage = createCallbackStorage();\n        var adapters = ['default', 'cordova', 'cordova-native'];\n\n        if (adapters.indexOf(initOptions.adapter) > -1) {\n            adapter = loadAdapter(initOptions.adapter);\n        } else if (typeof initOptions.adapter === \"object\") {\n            adapter = initOptions.adapter;\n        } else {\n            if (window.Cordova || window.cordova) {\n                adapter = loadAdapter('cordova');\n            } else {\n                adapter = loadAdapter();\n            }\n        }\n\n        if (typeof initOptions.useNonce !== 'undefined') {\n            useNonce = initOptions.useNonce;\n        }\n\n        if (typeof initOptions.checkLoginIframe !== 'undefined') {\n            loginIframe.enable = initOptions.checkLoginIframe;\n        }\n\n        if (initOptions.checkLoginIframeInterval) {\n            loginIframe.interval = initOptions.checkLoginIframeInterval;\n        }\n\n        if (initOptions.onLoad === 'login-required') {\n            kc.loginRequired = true;\n        }\n\n        if (initOptions.responseMode) {\n            if (initOptions.responseMode === 'query' || initOptions.responseMode === 'fragment') {\n                kc.responseMode = initOptions.responseMode;\n            } else {\n                throw 'Invalid value for responseMode';\n            }\n        }\n\n        if (initOptions.flow) {\n            switch (initOptions.flow) {\n                case 'standard':\n                    kc.responseType = 'code';\n                    break;\n                case 'implicit':\n                    kc.responseType = 'id_token token';\n                    break;\n                case 'hybrid':\n                    kc.responseType = 'code id_token token';\n                    break;\n                default:\n                    throw 'Invalid value for flow';\n            }\n            kc.flow = initOptions.flow;\n        }\n\n        if (initOptions.timeSkew != null) {\n            kc.timeSkew = initOptions.timeSkew;\n        }\n\n        if(initOptions.redirectUri) {\n            kc.redirectUri = initOptions.redirectUri;\n        }\n\n        if (initOptions.silentCheckSsoRedirectUri) {\n            kc.silentCheckSsoRedirectUri = initOptions.silentCheckSsoRedirectUri;\n        }\n\n        if (typeof initOptions.silentCheckSsoFallback === 'boolean') {\n            kc.silentCheckSsoFallback = initOptions.silentCheckSsoFallback;\n        } else {\n            kc.silentCheckSsoFallback = true;\n        }\n\n        if (typeof initOptions.pkceMethod !== \"undefined\") {\n            if (initOptions.pkceMethod !== \"S256\" && initOptions.pkceMethod !== false) {\n                throw new TypeError(`Invalid value for pkceMethod', expected 'S256' or false but got ${initOptions.pkceMethod}.`);\n            }\n\n            kc.pkceMethod = initOptions.pkceMethod;\n        } else {\n            kc.pkceMethod = \"S256\";\n        }\n\n        if (typeof initOptions.enableLogging === 'boolean') {\n            kc.enableLogging = initOptions.enableLogging;\n        } else {\n            kc.enableLogging = false;\n        }\n\n        if (initOptions.logoutMethod === 'POST') {\n            kc.logoutMethod = 'POST';\n        } else {\n            kc.logoutMethod = 'GET';\n        }\n\n        if (typeof initOptions.scope === 'string') {\n            kc.scope = initOptions.scope;\n        }\n\n        if (typeof initOptions.acrValues === 'string') {\n            kc.acrValues = initOptions.acrValues;\n        }\n\n        if (typeof initOptions.messageReceiveTimeout === 'number' && initOptions.messageReceiveTimeout > 0) {\n            kc.messageReceiveTimeout = initOptions.messageReceiveTimeout;\n        } else {\n            kc.messageReceiveTimeout = 10000;\n        }\n\n        if (!kc.responseMode) {\n            kc.responseMode = 'fragment';\n        }\n        if (!kc.responseType) {\n            kc.responseType = 'code';\n            kc.flow = 'standard';\n        }\n\n        var promise = createPromise();\n\n        var initPromise = createPromise();\n        initPromise.promise.then(function() {\n            kc.onReady && kc.onReady(kc.authenticated);\n            promise.setSuccess(kc.authenticated);\n        }).catch(function(error) {\n            promise.setError(error);\n        });\n\n        var configPromise = loadConfig();\n\n        function onLoad() {\n            var doLogin = function(prompt) {\n                if (!prompt) {\n                    options.prompt = 'none';\n                }\n\n                if (initOptions.locale) {\n                    options.locale = initOptions.locale;\n                }\n                kc.login(options).then(function () {\n                    initPromise.setSuccess();\n                }).catch(function (error) {\n                    initPromise.setError(error);\n                });\n            }\n\n            var checkSsoSilently = async function() {\n                var ifrm = document.createElement(\"iframe\");\n                var src = await kc.createLoginUrl({prompt: 'none', redirectUri: kc.silentCheckSsoRedirectUri});\n                ifrm.setAttribute(\"src\", src);\n                ifrm.setAttribute(\"sandbox\", \"allow-storage-access-by-user-activation allow-scripts allow-same-origin\");\n                ifrm.setAttribute(\"title\", \"keycloak-silent-check-sso\");\n                ifrm.style.display = \"none\";\n                document.body.appendChild(ifrm);\n\n                var messageCallback = function(event) {\n                    if (event.origin !== window.location.origin || ifrm.contentWindow !== event.source) {\n                        return;\n                    }\n\n                    var oauth = parseCallback(event.data);\n                    processCallback(oauth, initPromise);\n\n                    document.body.removeChild(ifrm);\n                    window.removeEventListener(\"message\", messageCallback);\n                };\n\n                window.addEventListener(\"message\", messageCallback);\n            };\n\n            var options = {};\n            switch (initOptions.onLoad) {\n                case 'check-sso':\n                    if (loginIframe.enable) {\n                        setupCheckLoginIframe().then(function() {\n                            checkLoginIframe().then(function (unchanged) {\n                                if (!unchanged) {\n                                    kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                                } else {\n                                    initPromise.setSuccess();\n                                }\n                            }).catch(function (error) {\n                                initPromise.setError(error);\n                            });\n                        });\n                    } else {\n                        kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                    }\n                    break;\n                case 'login-required':\n                    doLogin(true);\n                    break;\n                default:\n                    throw 'Invalid value for onLoad';\n            }\n        }\n\n        function processInit() {\n            var callback = parseCallback(window.location.href);\n\n            if (callback) {\n                window.history.replaceState(window.history.state, null, callback.newUrl);\n            }\n\n            if (callback && callback.valid) {\n                return setupCheckLoginIframe().then(function() {\n                    processCallback(callback, initPromise);\n                }).catch(function (error) {\n                    initPromise.setError(error);\n                });\n            }\n\n            if (initOptions.token && initOptions.refreshToken) {\n                setToken(initOptions.token, initOptions.refreshToken, initOptions.idToken);\n\n                if (loginIframe.enable) {\n                    setupCheckLoginIframe().then(function() {\n                        checkLoginIframe().then(function (unchanged) {\n                            if (unchanged) {\n                                kc.onAuthSuccess && kc.onAuthSuccess();\n                                initPromise.setSuccess();\n                                scheduleCheckIframe();\n                            } else {\n                                initPromise.setSuccess();\n                            }\n                        }).catch(function (error) {\n                            initPromise.setError(error);\n                        });\n                    });\n                } else {\n                    kc.updateToken(-1).then(function() {\n                        kc.onAuthSuccess && kc.onAuthSuccess();\n                        initPromise.setSuccess();\n                    }).catch(function(error) {\n                        kc.onAuthError && kc.onAuthError();\n                        if (initOptions.onLoad) {\n                            onLoad();\n                        } else {\n                            initPromise.setError(error);\n                        }\n                    });\n                }\n            } else if (initOptions.onLoad) {\n                onLoad();\n            } else {\n                initPromise.setSuccess();\n            }\n        }\n\n        configPromise.then(function () {\n            check3pCookiesSupported()\n                .then(processInit)\n                .catch(function (error) {\n                    promise.setError(error);\n                });\n        });\n        configPromise.catch(function (error) {\n            promise.setError(error);\n        });\n\n        return promise.promise;\n    }\n\n    kc.login = function (options) {\n        return adapter.login(options);\n    }\n\n    function generateRandomData(len) {\n        if (typeof crypto === \"undefined\" || typeof crypto.getRandomValues === \"undefined\") {\n            throw new Error(\"Web Crypto API is not available.\");\n        }\n\n        return crypto.getRandomValues(new Uint8Array(len));\n    }\n\n    function generateCodeVerifier(len) {\n        return generateRandomString(len, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');\n    }\n\n    function generateRandomString(len, alphabet){\n        var randomData = generateRandomData(len);\n        var chars = new Array(len);\n        for (var i = 0; i < len; i++) {\n            chars[i] = alphabet.charCodeAt(randomData[i] % alphabet.length);\n        }\n        return String.fromCharCode.apply(null, chars);\n    }\n\n    async function generatePkceChallenge(pkceMethod, codeVerifier) {\n        if (pkceMethod !== \"S256\") {\n            throw new TypeError(`Invalid value for 'pkceMethod', expected 'S256' but got '${pkceMethod}'.`);\n        }\n\n        // hash codeVerifier, then encode as url-safe base64 without padding\n        const hashBytes = new Uint8Array(await sha256Digest(codeVerifier));\n        const encodedHash = bytesToBase64(hashBytes)\n            .replace(/\\+/g, '-')\n            .replace(/\\//g, '_')\n            .replace(/\\=/g, '');\n\n        return encodedHash;\n    }\n\n    function buildClaimsParameter(requestedAcr){\n        var claims = {\n            id_token: {\n                acr: requestedAcr\n            }\n        }\n        return JSON.stringify(claims);\n    }\n\n    kc.createLoginUrl = async function(options) {\n        var state = createUUID();\n        var nonce = createUUID();\n\n        var redirectUri = adapter.redirectUri(options);\n\n        var callbackState = {\n            state: state,\n            nonce: nonce,\n            redirectUri: encodeURIComponent(redirectUri),\n            loginOptions: options\n        };\n\n        if (options && options.prompt) {\n            callbackState.prompt = options.prompt;\n        }\n\n        var baseUrl;\n        if (options && options.action == 'register') {\n            baseUrl = kc.endpoints.register();\n        } else {\n            baseUrl = kc.endpoints.authorize();\n        }\n\n        var scope = options && options.scope || kc.scope;\n        if (!scope) {\n            // if scope is not set, default to \"openid\"\n            scope = \"openid\";\n        } else if (scope.indexOf(\"openid\") === -1) {\n            // if openid scope is missing, prefix the given scopes with it\n            scope = \"openid \" + scope;\n        }\n\n        var url = baseUrl\n            + '?client_id=' + encodeURIComponent(kc.clientId)\n            + '&redirect_uri=' + encodeURIComponent(redirectUri)\n            + '&state=' + encodeURIComponent(state)\n            + '&response_mode=' + encodeURIComponent(kc.responseMode)\n            + '&response_type=' + encodeURIComponent(kc.responseType)\n            + '&scope=' + encodeURIComponent(scope);\n        if (useNonce) {\n            url = url + '&nonce=' + encodeURIComponent(nonce);\n        }\n\n        if (options && options.prompt) {\n            url += '&prompt=' + encodeURIComponent(options.prompt);\n        }\n\n        if (options && typeof options.maxAge === 'number') {\n            url += '&max_age=' + encodeURIComponent(options.maxAge);\n        }\n\n        if (options && options.loginHint) {\n            url += '&login_hint=' + encodeURIComponent(options.loginHint);\n        }\n\n        if (options && options.idpHint) {\n            url += '&kc_idp_hint=' + encodeURIComponent(options.idpHint);\n        }\n\n        if (options && options.action && options.action != 'register') {\n            url += '&kc_action=' + encodeURIComponent(options.action);\n        }\n\n        if (options && options.locale) {\n            url += '&ui_locales=' + encodeURIComponent(options.locale);\n        }\n\n        if (options && options.acr) {\n            var claimsParameter = buildClaimsParameter(options.acr);\n            url += '&claims=' + encodeURIComponent(claimsParameter);\n        }\n\n        if ((options && options.acrValues) || kc.acrValues) {\n            url += '&acr_values=' + encodeURIComponent(options.acrValues || kc.acrValues);\n        }\n\n        if (kc.pkceMethod) {\n            try {\n                const codeVerifier = generateCodeVerifier(96);\n                const pkceChallenge = await generatePkceChallenge(kc.pkceMethod, codeVerifier);\n\n                callbackState.pkceCodeVerifier = codeVerifier;\n\n                url += '&code_challenge=' + pkceChallenge;\n                url += '&code_challenge_method=' + kc.pkceMethod;\n            } catch (error) {\n                throw new Error(\"Failed to generate PKCE challenge.\", { cause: error });\n            }\n        }\n\n        callbackStorage.add(callbackState);\n\n        return url;\n    }\n\n    kc.logout = function(options) {\n        return adapter.logout(options);\n    }\n\n    kc.createLogoutUrl = function(options) {\n\n        const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n        if (logoutMethod === 'POST') {\n            return kc.endpoints.logout();\n        }\n\n        var url = kc.endpoints.logout()\n            + '?client_id=' + encodeURIComponent(kc.clientId)\n            + '&post_logout_redirect_uri=' + encodeURIComponent(adapter.redirectUri(options, false));\n\n        if (kc.idToken) {\n            url += '&id_token_hint=' + encodeURIComponent(kc.idToken);\n        }\n\n        return url;\n    }\n\n    kc.register = function (options) {\n        return adapter.register(options);\n    }\n\n    kc.createRegisterUrl = async function(options) {\n        if (!options) {\n            options = {};\n        }\n        options.action = 'register';\n        return await kc.createLoginUrl(options);\n    }\n\n    kc.createAccountUrl = function(options) {\n        var realm = getRealmUrl();\n        var url = undefined;\n        if (typeof realm !== 'undefined') {\n            url = realm\n            + '/account'\n            + '?referrer=' + encodeURIComponent(kc.clientId)\n            + '&referrer_uri=' + encodeURIComponent(adapter.redirectUri(options));\n        }\n        return url;\n    }\n\n    kc.accountManagement = function() {\n        return adapter.accountManagement();\n    }\n\n    kc.hasRealmRole = function (role) {\n        var access = kc.realmAccess;\n        return !!access && access.roles.indexOf(role) >= 0;\n    }\n\n    kc.hasResourceRole = function(role, resource) {\n        if (!kc.resourceAccess) {\n            return false;\n        }\n\n        var access = kc.resourceAccess[resource || kc.clientId];\n        return !!access && access.roles.indexOf(role) >= 0;\n    }\n\n    kc.loadUserProfile = function() {\n        var url = getRealmUrl() + '/account';\n        var req = new XMLHttpRequest();\n        req.open('GET', url, true);\n        req.setRequestHeader('Accept', 'application/json');\n        req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n\n        var promise = createPromise();\n\n        req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n                if (req.status == 200) {\n                    kc.profile = JSON.parse(req.responseText);\n                    promise.setSuccess(kc.profile);\n                } else {\n                    promise.setError();\n                }\n            }\n        }\n\n        req.send();\n\n        return promise.promise;\n    }\n\n    kc.loadUserInfo = function() {\n        var url = kc.endpoints.userinfo();\n        var req = new XMLHttpRequest();\n        req.open('GET', url, true);\n        req.setRequestHeader('Accept', 'application/json');\n        req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n\n        var promise = createPromise();\n\n        req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n                if (req.status == 200) {\n                    kc.userInfo = JSON.parse(req.responseText);\n                    promise.setSuccess(kc.userInfo);\n                } else {\n                    promise.setError();\n                }\n            }\n        }\n\n        req.send();\n\n        return promise.promise;\n    }\n\n    kc.isTokenExpired = function(minValidity) {\n        if (!kc.tokenParsed || (!kc.refreshToken && kc.flow != 'implicit' )) {\n            throw 'Not authenticated';\n        }\n\n        if (kc.timeSkew == null) {\n            logInfo('[KEYCLOAK] Unable to determine if token is expired as timeskew is not set');\n            return true;\n        }\n\n        var expiresIn = kc.tokenParsed['exp'] - Math.ceil(new Date().getTime() / 1000) + kc.timeSkew;\n        if (minValidity) {\n            if (isNaN(minValidity)) {\n                throw 'Invalid minValidity';\n            }\n            expiresIn -= minValidity;\n        }\n        return expiresIn < 0;\n    }\n\n    kc.updateToken = function(minValidity) {\n        var promise = createPromise();\n\n        if (!kc.refreshToken) {\n            promise.setError();\n            return promise.promise;\n        }\n\n        minValidity = minValidity || 5;\n\n        var exec = function() {\n            var refreshToken = false;\n            if (minValidity == -1) {\n                refreshToken = true;\n                logInfo('[KEYCLOAK] Refreshing token: forced refresh');\n            } else if (!kc.tokenParsed || kc.isTokenExpired(minValidity)) {\n                refreshToken = true;\n                logInfo('[KEYCLOAK] Refreshing token: token expired');\n            }\n\n            if (!refreshToken) {\n                promise.setSuccess(false);\n            } else {\n                var params = 'grant_type=refresh_token&' + 'refresh_token=' + kc.refreshToken;\n                var url = kc.endpoints.token();\n\n                refreshQueue.push(promise);\n\n                if (refreshQueue.length == 1) {\n                    var req = new XMLHttpRequest();\n                    req.open('POST', url, true);\n                    req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n                    req.withCredentials = true;\n\n                    params += '&client_id=' + encodeURIComponent(kc.clientId);\n\n                    var timeLocal = new Date().getTime();\n\n                    req.onreadystatechange = function () {\n                        if (req.readyState == 4) {\n                            if (req.status == 200) {\n                                logInfo('[KEYCLOAK] Token refreshed');\n\n                                timeLocal = (timeLocal + new Date().getTime()) / 2;\n\n                                var tokenResponse = JSON.parse(req.responseText);\n\n                                setToken(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], timeLocal);\n\n                                kc.onAuthRefreshSuccess && kc.onAuthRefreshSuccess();\n                                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                                    p.setSuccess(true);\n                                }\n                            } else {\n                                logWarn('[KEYCLOAK] Failed to refresh token');\n\n                                if (req.status == 400) {\n                                    kc.clearToken();\n                                }\n\n                                kc.onAuthRefreshError && kc.onAuthRefreshError();\n                                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                                    p.setError(\"Failed to refresh token: An unexpected HTTP error occurred while attempting to refresh the token.\");\n                                }\n                            }\n                        }\n                    };\n\n                    req.send(params);\n                }\n            }\n        }\n\n        if (loginIframe.enable) {\n            var iframePromise = checkLoginIframe();\n            iframePromise.then(function() {\n                exec();\n            }).catch(function(error) {\n                promise.setError(error);\n            });\n        } else {\n            exec();\n        }\n\n        return promise.promise;\n    }\n\n    kc.clearToken = function() {\n        if (kc.token) {\n            setToken(null, null, null);\n            kc.onAuthLogout && kc.onAuthLogout();\n            if (kc.loginRequired) {\n                kc.login();\n            }\n        }\n    }\n\n    function getRealmUrl() {\n        if (typeof kc.authServerUrl !== 'undefined') {\n            if (kc.authServerUrl.charAt(kc.authServerUrl.length - 1) == '/') {\n                return kc.authServerUrl + 'realms/' + encodeURIComponent(kc.realm);\n            } else {\n                return kc.authServerUrl + '/realms/' + encodeURIComponent(kc.realm);\n            }\n        } else {\n            return undefined;\n        }\n    }\n\n    function getOrigin() {\n        if (!window.location.origin) {\n            return window.location.protocol + \"//\" + window.location.hostname + (window.location.port ? ':' + window.location.port: '');\n        } else {\n            return window.location.origin;\n        }\n    }\n\n    function processCallback(oauth, promise) {\n        var code = oauth.code;\n        var error = oauth.error;\n        var prompt = oauth.prompt;\n\n        var timeLocal = new Date().getTime();\n\n        if (oauth['kc_action_status']) {\n            kc.onActionUpdate && kc.onActionUpdate(oauth['kc_action_status'], oauth['kc_action']);\n        }\n\n        if (error) {\n            if (prompt != 'none') {\n                if (oauth.error_description && oauth.error_description === \"authentication_expired\") {\n                    kc.login(oauth.loginOptions);\n                } else {\n                    var errorData = { error: error, error_description: oauth.error_description };\n                    kc.onAuthError && kc.onAuthError(errorData);\n                    promise && promise.setError(errorData);\n                }\n            } else {\n                promise && promise.setSuccess();\n            }\n            return;\n        } else if ((kc.flow != 'standard') && (oauth.access_token || oauth.id_token)) {\n            authSuccess(oauth.access_token, null, oauth.id_token, true);\n        }\n\n        if ((kc.flow != 'implicit') && code) {\n            var params = 'code=' + code + '&grant_type=authorization_code';\n            var url = kc.endpoints.token();\n\n            var req = new XMLHttpRequest();\n            req.open('POST', url, true);\n            req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n\n            params += '&client_id=' + encodeURIComponent(kc.clientId);\n            params += '&redirect_uri=' + oauth.redirectUri;\n\n            if (oauth.pkceCodeVerifier) {\n                params += '&code_verifier=' + oauth.pkceCodeVerifier;\n            }\n\n            req.withCredentials = true;\n\n            req.onreadystatechange = function() {\n                if (req.readyState == 4) {\n                    if (req.status == 200) {\n\n                        var tokenResponse = JSON.parse(req.responseText);\n                        authSuccess(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], kc.flow === 'standard');\n                        scheduleCheckIframe();\n                    } else {\n                        kc.onAuthError && kc.onAuthError();\n                        promise && promise.setError();\n                    }\n                }\n            };\n\n            req.send(params);\n        }\n\n        function authSuccess(accessToken, refreshToken, idToken, fulfillPromise) {\n            timeLocal = (timeLocal + new Date().getTime()) / 2;\n\n            setToken(accessToken, refreshToken, idToken, timeLocal);\n\n            if (useNonce && (kc.idTokenParsed && kc.idTokenParsed.nonce != oauth.storedNonce)) {\n                logInfo('[KEYCLOAK] Invalid nonce, clearing token');\n                kc.clearToken();\n                promise && promise.setError();\n            } else {\n                if (fulfillPromise) {\n                    kc.onAuthSuccess && kc.onAuthSuccess();\n                    promise && promise.setSuccess();\n                }\n            }\n        }\n\n    }\n\n    function loadConfig() {\n        var promise = createPromise();\n        var configUrl;\n\n        if (typeof config === 'string') {\n            configUrl = config;\n        }\n\n        function setupOidcEndoints(oidcConfiguration) {\n            if (! oidcConfiguration) {\n                kc.endpoints = {\n                    authorize: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/auth';\n                    },\n                    token: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/token';\n                    },\n                    logout: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/logout';\n                    },\n                    checkSessionIframe: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/login-status-iframe.html';\n                    },\n                    thirdPartyCookiesIframe: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/3p-cookies/step1.html';\n                    },\n                    register: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/registrations';\n                    },\n                    userinfo: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/userinfo';\n                    }\n                };\n            } else {\n                kc.endpoints = {\n                    authorize: function() {\n                        return oidcConfiguration.authorization_endpoint;\n                    },\n                    token: function() {\n                        return oidcConfiguration.token_endpoint;\n                    },\n                    logout: function() {\n                        if (!oidcConfiguration.end_session_endpoint) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.end_session_endpoint;\n                    },\n                    checkSessionIframe: function() {\n                        if (!oidcConfiguration.check_session_iframe) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.check_session_iframe;\n                    },\n                    register: function() {\n                        throw 'Redirection to \"Register user\" page not supported in standard OIDC mode';\n                    },\n                    userinfo: function() {\n                        if (!oidcConfiguration.userinfo_endpoint) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.userinfo_endpoint;\n                    }\n                }\n            }\n        }\n\n        if (configUrl) {\n            var req = new XMLHttpRequest();\n            req.open('GET', configUrl, true);\n            req.setRequestHeader('Accept', 'application/json');\n\n            req.onreadystatechange = function () {\n                if (req.readyState == 4) {\n                    if (req.status == 200 || fileLoaded(req)) {\n                        var config = JSON.parse(req.responseText);\n\n                        kc.authServerUrl = config['auth-server-url'];\n                        kc.realm = config['realm'];\n                        kc.clientId = config['resource'];\n                        setupOidcEndoints(null);\n                        promise.setSuccess();\n                    } else {\n                        promise.setError();\n                    }\n                }\n            };\n\n            req.send();\n        } else {\n            kc.clientId = config.clientId;\n\n            var oidcProvider = config['oidcProvider'];\n            if (!oidcProvider) {\n                kc.authServerUrl = config.url;\n                kc.realm = config.realm;\n                setupOidcEndoints(null);\n                promise.setSuccess();\n            } else {\n                if (typeof oidcProvider === 'string') {\n                    var oidcProviderConfigUrl;\n                    if (oidcProvider.charAt(oidcProvider.length - 1) == '/') {\n                        oidcProviderConfigUrl = oidcProvider + '.well-known/openid-configuration';\n                    } else {\n                        oidcProviderConfigUrl = oidcProvider + '/.well-known/openid-configuration';\n                    }\n                    var req = new XMLHttpRequest();\n                    req.open('GET', oidcProviderConfigUrl, true);\n                    req.setRequestHeader('Accept', 'application/json');\n\n                    req.onreadystatechange = function () {\n                        if (req.readyState == 4) {\n                            if (req.status == 200 || fileLoaded(req)) {\n                                var oidcProviderConfig = JSON.parse(req.responseText);\n                                setupOidcEndoints(oidcProviderConfig);\n                                promise.setSuccess();\n                            } else {\n                                promise.setError();\n                            }\n                        }\n                    };\n\n                    req.send();\n                } else {\n                    setupOidcEndoints(oidcProvider);\n                    promise.setSuccess();\n                }\n            }\n        }\n\n        return promise.promise;\n    }\n\n    function fileLoaded(xhr) {\n        return xhr.status == 0 && xhr.responseText && xhr.responseURL.startsWith('file:');\n    }\n\n    function setToken(token, refreshToken, idToken, timeLocal) {\n        if (kc.tokenTimeoutHandle) {\n            clearTimeout(kc.tokenTimeoutHandle);\n            kc.tokenTimeoutHandle = null;\n        }\n\n        if (refreshToken) {\n            kc.refreshToken = refreshToken;\n            kc.refreshTokenParsed = decodeToken(refreshToken);\n        } else {\n            delete kc.refreshToken;\n            delete kc.refreshTokenParsed;\n        }\n\n        if (idToken) {\n            kc.idToken = idToken;\n            kc.idTokenParsed = decodeToken(idToken);\n        } else {\n            delete kc.idToken;\n            delete kc.idTokenParsed;\n        }\n\n        if (token) {\n            kc.token = token;\n            kc.tokenParsed = decodeToken(token);\n            kc.sessionId = kc.tokenParsed.sid;\n            kc.authenticated = true;\n            kc.subject = kc.tokenParsed.sub;\n            kc.realmAccess = kc.tokenParsed.realm_access;\n            kc.resourceAccess = kc.tokenParsed.resource_access;\n\n            if (timeLocal) {\n                kc.timeSkew = Math.floor(timeLocal / 1000) - kc.tokenParsed.iat;\n            }\n\n            if (kc.timeSkew != null) {\n                logInfo('[KEYCLOAK] Estimated time difference between browser and server is ' + kc.timeSkew + ' seconds');\n\n                if (kc.onTokenExpired) {\n                    var expiresIn = (kc.tokenParsed['exp'] - (new Date().getTime() / 1000) + kc.timeSkew) * 1000;\n                    logInfo('[KEYCLOAK] Token expires in ' + Math.round(expiresIn / 1000) + ' s');\n                    if (expiresIn <= 0) {\n                        kc.onTokenExpired();\n                    } else {\n                        kc.tokenTimeoutHandle = setTimeout(kc.onTokenExpired, expiresIn);\n                    }\n                }\n            }\n        } else {\n            delete kc.token;\n            delete kc.tokenParsed;\n            delete kc.subject;\n            delete kc.realmAccess;\n            delete kc.resourceAccess;\n\n            kc.authenticated = false;\n        }\n    }\n\n    function createUUID() {\n        if (typeof crypto === \"undefined\" || typeof crypto.randomUUID === \"undefined\") {\n            throw new Error(\"Web Crypto API is not available.\");\n        }\n\n        return crypto.randomUUID();\n    }\n\n    function parseCallback(url) {\n        var oauth = parseCallbackUrl(url);\n        if (!oauth) {\n            return;\n        }\n\n        var oauthState = callbackStorage.get(oauth.state);\n\n        if (oauthState) {\n            oauth.valid = true;\n            oauth.redirectUri = oauthState.redirectUri;\n            oauth.storedNonce = oauthState.nonce;\n            oauth.prompt = oauthState.prompt;\n            oauth.pkceCodeVerifier = oauthState.pkceCodeVerifier;\n            oauth.loginOptions = oauthState.loginOptions;\n        }\n\n        return oauth;\n    }\n\n    function parseCallbackUrl(url) {\n        var supportedParams;\n        switch (kc.flow) {\n            case 'standard':\n                supportedParams = ['code', 'state', 'session_state', 'kc_action_status', 'kc_action', 'iss'];\n                break;\n            case 'implicit':\n                supportedParams = ['access_token', 'token_type', 'id_token', 'state', 'session_state', 'expires_in', 'kc_action_status', 'kc_action', 'iss'];\n                break;\n            case 'hybrid':\n                supportedParams = ['access_token', 'token_type', 'id_token', 'code', 'state', 'session_state', 'expires_in', 'kc_action_status', 'kc_action', 'iss'];\n                break;\n        }\n\n        supportedParams.push('error');\n        supportedParams.push('error_description');\n        supportedParams.push('error_uri');\n\n        var queryIndex = url.indexOf('?');\n        var fragmentIndex = url.indexOf('#');\n\n        var newUrl;\n        var parsed;\n\n        if (kc.responseMode === 'query' && queryIndex !== -1) {\n            newUrl = url.substring(0, queryIndex);\n            parsed = parseCallbackParams(url.substring(queryIndex + 1, fragmentIndex !== -1 ? fragmentIndex : url.length), supportedParams);\n            if (parsed.paramsString !== '') {\n                newUrl += '?' + parsed.paramsString;\n            }\n            if (fragmentIndex !== -1) {\n                newUrl += url.substring(fragmentIndex);\n            }\n        } else if (kc.responseMode === 'fragment' && fragmentIndex !== -1) {\n            newUrl = url.substring(0, fragmentIndex);\n            parsed = parseCallbackParams(url.substring(fragmentIndex + 1), supportedParams);\n            if (parsed.paramsString !== '') {\n                newUrl += '#' + parsed.paramsString;\n            }\n        }\n\n        if (parsed && parsed.oauthParams) {\n            if (kc.flow === 'standard' || kc.flow === 'hybrid') {\n                if ((parsed.oauthParams.code || parsed.oauthParams.error) && parsed.oauthParams.state) {\n                    parsed.oauthParams.newUrl = newUrl;\n                    return parsed.oauthParams;\n                }\n            } else if (kc.flow === 'implicit') {\n                if ((parsed.oauthParams.access_token || parsed.oauthParams.error) && parsed.oauthParams.state) {\n                    parsed.oauthParams.newUrl = newUrl;\n                    return parsed.oauthParams;\n                }\n            }\n        }\n    }\n\n    function parseCallbackParams(paramsString, supportedParams) {\n        var p = paramsString.split('&');\n        var result = {\n            paramsString: '',\n            oauthParams: {}\n        }\n        for (var i = 0; i < p.length; i++) {\n            var split = p[i].indexOf(\"=\");\n            var key = p[i].slice(0, split);\n            if (supportedParams.indexOf(key) !== -1) {\n                result.oauthParams[key] = p[i].slice(split + 1);\n            } else {\n                if (result.paramsString !== '') {\n                    result.paramsString += '&';\n                }\n                result.paramsString += p[i];\n            }\n        }\n        return result;\n    }\n\n    function createPromise() {\n        // Need to create a native Promise which also preserves the\n        // interface of the custom promise type previously used by the API\n        var p = {\n            setSuccess: function(result) {\n                p.resolve(result);\n            },\n\n            setError: function(result) {\n                p.reject(result);\n            }\n        };\n        p.promise = new Promise(function(resolve, reject) {\n            p.resolve = resolve;\n            p.reject = reject;\n        });\n\n        return p;\n    }\n\n    // Function to extend existing native Promise with timeout\n    function applyTimeoutToPromise(promise, timeout, errorMessage) {\n        var timeoutHandle = null;\n        var timeoutPromise = new Promise(function (resolve, reject) {\n            timeoutHandle = setTimeout(function () {\n                reject({ \"error\": errorMessage || \"Promise is not settled within timeout of \" + timeout + \"ms\" });\n            }, timeout);\n        });\n\n        return Promise.race([promise, timeoutPromise]).finally(function () {\n            clearTimeout(timeoutHandle);\n        });\n    }\n\n    function setupCheckLoginIframe() {\n        var promise = createPromise();\n\n        if (!loginIframe.enable) {\n            promise.setSuccess();\n            return promise.promise;\n        }\n\n        if (loginIframe.iframe) {\n            promise.setSuccess();\n            return promise.promise;\n        }\n\n        var iframe = document.createElement('iframe');\n        loginIframe.iframe = iframe;\n\n        iframe.onload = function() {\n            var authUrl = kc.endpoints.authorize();\n            if (authUrl.charAt(0) === '/') {\n                loginIframe.iframeOrigin = getOrigin();\n            } else {\n                loginIframe.iframeOrigin = authUrl.substring(0, authUrl.indexOf('/', 8));\n            }\n            promise.setSuccess();\n        }\n\n        var src = kc.endpoints.checkSessionIframe();\n        iframe.setAttribute('src', src );\n        iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n        iframe.setAttribute('title', 'keycloak-session-iframe' );\n        iframe.style.display = 'none';\n        document.body.appendChild(iframe);\n\n        var messageCallback = function(event) {\n            if ((event.origin !== loginIframe.iframeOrigin) || (loginIframe.iframe.contentWindow !== event.source)) {\n                return;\n            }\n\n            if (!(event.data == 'unchanged' || event.data == 'changed' || event.data == 'error')) {\n                return;\n            }\n\n\n            if (event.data != 'unchanged') {\n                kc.clearToken();\n            }\n\n            var callbacks = loginIframe.callbackList.splice(0, loginIframe.callbackList.length);\n\n            for (var i = callbacks.length - 1; i >= 0; --i) {\n                var promise = callbacks[i];\n                if (event.data == 'error') {\n                    promise.setError();\n                } else {\n                    promise.setSuccess(event.data == 'unchanged');\n                }\n            }\n        };\n\n        window.addEventListener('message', messageCallback, false);\n\n        return promise.promise;\n    }\n\n    function scheduleCheckIframe() {\n        if (loginIframe.enable) {\n            if (kc.token) {\n                setTimeout(function() {\n                    checkLoginIframe().then(function(unchanged) {\n                        if (unchanged) {\n                            scheduleCheckIframe();\n                        }\n                    });\n                }, loginIframe.interval * 1000);\n            }\n        }\n    }\n\n    function checkLoginIframe() {\n        var promise = createPromise();\n\n        if (loginIframe.iframe && loginIframe.iframeOrigin ) {\n            var msg = kc.clientId + ' ' + (kc.sessionId ? kc.sessionId : '');\n            loginIframe.callbackList.push(promise);\n            var origin = loginIframe.iframeOrigin;\n            if (loginIframe.callbackList.length == 1) {\n                loginIframe.iframe.contentWindow.postMessage(msg, origin);\n            }\n        } else {\n            promise.setSuccess();\n        }\n\n        return promise.promise;\n    }\n\n    function check3pCookiesSupported() {\n        var promise = createPromise();\n\n        if ((loginIframe.enable || kc.silentCheckSsoRedirectUri) && typeof kc.endpoints.thirdPartyCookiesIframe === 'function') {\n            var iframe = document.createElement('iframe');\n            iframe.setAttribute('src', kc.endpoints.thirdPartyCookiesIframe());\n            iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n            iframe.setAttribute('title', 'keycloak-3p-check-iframe' );\n            iframe.style.display = 'none';\n            document.body.appendChild(iframe);\n\n            var messageCallback = function(event) {\n                if (iframe.contentWindow !== event.source) {\n                    return;\n                }\n\n                if (event.data !== \"supported\" && event.data !== \"unsupported\") {\n                    return;\n                } else if (event.data === \"unsupported\") {\n                    logWarn(\n                        \"[KEYCLOAK] Your browser is blocking access to 3rd-party cookies, this means:\\n\\n\" +\n                        \" - It is not possible to retrieve tokens without redirecting to the Keycloak server (a.k.a. no support for silent authentication).\\n\" +\n                        \" - It is not possible to automatically detect changes to the session status (such as the user logging out in another tab).\\n\\n\" +\n                        \"For more information see: https://www.keycloak.org/securing-apps/javascript-adapter#_modern_browsers\"\n                    );\n\n                    loginIframe.enable = false;\n                    if (kc.silentCheckSsoFallback) {\n                        kc.silentCheckSsoRedirectUri = false;\n                    }\n                }\n\n                document.body.removeChild(iframe);\n                window.removeEventListener(\"message\", messageCallback);\n                promise.setSuccess();\n            };\n\n            window.addEventListener('message', messageCallback, false);\n        } else {\n            promise.setSuccess();\n        }\n\n        return applyTimeoutToPromise(promise.promise, kc.messageReceiveTimeout, \"Timeout when waiting for 3rd party check iframe message.\");\n    }\n\n    function loadAdapter(type) {\n        if (!type || type == 'default') {\n            return {\n                login: async function(options) {\n                    window.location.assign(await kc.createLoginUrl(options));\n                    return createPromise().promise;\n                },\n\n                logout: async function(options) {\n\n                    const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n                    if (logoutMethod === \"GET\") {\n                        window.location.replace(kc.createLogoutUrl(options));\n                        return;\n                    }\n\n                    // Create form to send POST request.\n                    const form = document.createElement(\"form\");\n\n                    form.setAttribute(\"method\", \"POST\");\n                    form.setAttribute(\"action\", kc.createLogoutUrl(options));\n                    form.style.display = \"none\";\n\n                    // Add data to form as hidden input fields.\n                    const data = {\n                        id_token_hint: kc.idToken,\n                        client_id: kc.clientId,\n                        post_logout_redirect_uri: adapter.redirectUri(options, false)\n                    };\n\n                    for (const [name, value] of Object.entries(data)) {\n                        const input = document.createElement(\"input\");\n\n                        input.setAttribute(\"type\", \"hidden\");\n                        input.setAttribute(\"name\", name);\n                        input.setAttribute(\"value\", value);\n\n                        form.appendChild(input);\n                    }\n\n                    // Append form to page and submit it to perform logout and redirect.\n                    document.body.appendChild(form);\n                    form.submit();\n                },\n\n                register: async function(options) {\n                    window.location.assign(await kc.createRegisterUrl(options));\n                    return createPromise().promise;\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        window.location.href = accountUrl;\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                    return createPromise().promise;\n                },\n\n                redirectUri: function(options, encodeHash) {\n                    if (arguments.length == 1) {\n                        encodeHash = true;\n                    }\n\n                    if (options && options.redirectUri) {\n                        return options.redirectUri;\n                    } else if (kc.redirectUri) {\n                        return kc.redirectUri;\n                    } else {\n                        return location.href;\n                    }\n                }\n            };\n        }\n\n        if (type == 'cordova') {\n            loginIframe.enable = false;\n            var cordovaOpenWindowWrapper = function(loginUrl, target, options) {\n                if (window.cordova && window.cordova.InAppBrowser) {\n                    // Use inappbrowser for IOS and Android if available\n                    return window.cordova.InAppBrowser.open(loginUrl, target, options);\n                } else {\n                    return window.open(loginUrl, target, options);\n                }\n            };\n\n            var shallowCloneCordovaOptions = function (userOptions) {\n                if (userOptions && userOptions.cordovaOptions) {\n                    return Object.keys(userOptions.cordovaOptions).reduce(function (options, optionName) {\n                        options[optionName] = userOptions.cordovaOptions[optionName];\n                        return options;\n                    }, {});\n                } else {\n                    return {};\n                }\n            };\n\n            var formatCordovaOptions = function (cordovaOptions) {\n                return Object.keys(cordovaOptions).reduce(function (options, optionName) {\n                    options.push(optionName+\"=\"+cordovaOptions[optionName]);\n                    return options;\n                }, []).join(\",\");\n            };\n\n            var createCordovaOptions = function (userOptions) {\n                var cordovaOptions = shallowCloneCordovaOptions(userOptions);\n                cordovaOptions.location = 'no';\n                if (userOptions && userOptions.prompt == 'none') {\n                    cordovaOptions.hidden = 'yes';\n                }\n                return formatCordovaOptions(cordovaOptions);\n            };\n\n            var getCordovaRedirectUri = function() {\n                return kc.redirectUri || 'http://localhost';\n            }\n\n            return {\n                login: async function(options) {\n                    var promise = createPromise();\n\n                    var cordovaOptions = createCordovaOptions(options);\n                    var loginUrl = await kc.createLoginUrl(options);\n                    var ref = cordovaOpenWindowWrapper(loginUrl, '_blank', cordovaOptions);\n                    var completed = false;\n\n                    var closed = false;\n                    var closeBrowser = function() {\n                        closed = true;\n                        ref.close();\n                    };\n\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            var callback = parseCallback(event.url);\n                            processCallback(callback, promise);\n                            closeBrowser();\n                            completed = true;\n                        }\n                    });\n\n                    ref.addEventListener('loaderror', function(event) {\n                        if (!completed) {\n                            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                                var callback = parseCallback(event.url);\n                                processCallback(callback, promise);\n                                closeBrowser();\n                                completed = true;\n                            } else {\n                                promise.setError();\n                                closeBrowser();\n                            }\n                        }\n                    });\n\n                    ref.addEventListener('exit', function(event) {\n                        if (!closed) {\n                            promise.setError({\n                                reason: \"closed_by_user\"\n                            });\n                        }\n                    });\n\n                    return promise.promise;\n                },\n\n                logout: function(options) {\n                    var promise = createPromise();\n\n                    var logoutUrl = kc.createLogoutUrl(options);\n                    var ref = cordovaOpenWindowWrapper(logoutUrl, '_blank', 'location=no,hidden=yes,clearcache=yes');\n\n                    var error;\n\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                        }\n                    });\n\n                    ref.addEventListener('loaderror', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                        } else {\n                            error = true;\n                            ref.close();\n                        }\n                    });\n\n                    ref.addEventListener('exit', function(event) {\n                        if (error) {\n                            promise.setError();\n                        } else {\n                            kc.clearToken();\n                            promise.setSuccess();\n                        }\n                    });\n\n                    return promise.promise;\n                },\n\n                register : async function(options) {\n                    var promise = createPromise();\n                    var registerUrl = await kc.createRegisterUrl();\n                    var cordovaOptions = createCordovaOptions(options);\n                    var ref = cordovaOpenWindowWrapper(registerUrl, '_blank', cordovaOptions);\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                            var oauth = parseCallback(event.url);\n                            processCallback(oauth, promise);\n                        }\n                    });\n                    return promise.promise;\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        var ref = cordovaOpenWindowWrapper(accountUrl, '_blank', 'location=no');\n                        ref.addEventListener('loadstart', function(event) {\n                            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                                ref.close();\n                            }\n                        });\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                },\n\n                redirectUri: function(options) {\n                    return getCordovaRedirectUri();\n                }\n            }\n        }\n\n        if (type == 'cordova-native') {\n            loginIframe.enable = false;\n\n            return {\n                login: async function(options) {\n                    var promise = createPromise();\n                    var loginUrl = await kc.createLoginUrl(options);\n\n                    universalLinks.subscribe('keycloak', function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        var oauth = parseCallback(event.url);\n                        processCallback(oauth, promise);\n                    });\n\n                    window.cordova.plugins.browsertab.openUrl(loginUrl);\n                    return promise.promise;\n                },\n\n                logout: function(options) {\n                    var promise = createPromise();\n                    var logoutUrl = kc.createLogoutUrl(options);\n\n                    universalLinks.subscribe('keycloak', function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        kc.clearToken();\n                        promise.setSuccess();\n                    });\n\n                    window.cordova.plugins.browsertab.openUrl(logoutUrl);\n                    return promise.promise;\n                },\n\n                register : async function(options) {\n                    var promise = createPromise();\n                    var registerUrl = await kc.createRegisterUrl(options);\n                    universalLinks.subscribe('keycloak' , function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        var oauth = parseCallback(event.url);\n                        processCallback(oauth, promise);\n                    });\n                    window.cordova.plugins.browsertab.openUrl(registerUrl);\n                    return promise.promise;\n\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        window.cordova.plugins.browsertab.openUrl(accountUrl);\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                },\n\n                redirectUri: function(options) {\n                    if (options && options.redirectUri) {\n                        return options.redirectUri;\n                    } else if (kc.redirectUri) {\n                        return kc.redirectUri;\n                    } else {\n                        return \"http://localhost\";\n                    }\n                }\n            }\n        }\n\n        throw 'invalid adapter type: ' + type;\n    }\n\n    const STORAGE_KEY_PREFIX = 'kc-callback-';\n\n    var LocalStorage = function() {\n        if (!(this instanceof LocalStorage)) {\n            return new LocalStorage();\n        }\n\n        localStorage.setItem('kc-test', 'test');\n        localStorage.removeItem('kc-test');\n\n        var cs = this;\n\n        /**\n         * Clears all values from local storage that are no longer valid.\n         */\n        function clearInvalidValues() {\n            const currentTime = Date.now();\n\n            for (const [key, value] of getStoredEntries()) {\n                // Attempt to parse the expiry time from the value.\n                const expiry = parseExpiry(value);\n\n                // Discard the value if it is malformed or expired.\n                if (expiry === null || expiry < currentTime) {\n                    localStorage.removeItem(key);\n                }\n            }\n        }\n\n        /**\n         * Clears all known values from local storage.\n         */\n        function clearAllValues() {\n            for (const [key] of getStoredEntries()) {\n                localStorage.removeItem(key);\n            }\n        }\n\n        /**\n         * Gets all entries stored in local storage that are known to be managed by this class.\n         * @returns {Array<[string, unknown]>} An array of key-value pairs.\n         */\n        function getStoredEntries() {\n            return Object.entries(localStorage).filter(([key]) => key.startsWith(STORAGE_KEY_PREFIX));\n        }\n\n        /**\n         * Parses the expiry time from a value stored in local storage.\n         * @param {unknown} value\n         * @returns {number | null} The expiry time in milliseconds, or `null` if the value is malformed.\n         */\n        function parseExpiry(value) {\n            let parsedValue;\n\n            // Attempt to parse the value as JSON.\n            try {\n                parsedValue = JSON.parse(value);\n            } catch (error) {\n                return null;\n            }\n\n            // Attempt to extract the 'expires' property.\n            if (isObject(parsedValue) && 'expires' in parsedValue && typeof parsedValue.expires === 'number') {\n                return parsedValue.expires;\n            }\n\n            return null;\n        }\n\n        cs.get = function(state) {\n            if (!state) {\n                return;\n            }\n\n            var key = STORAGE_KEY_PREFIX + state;\n            var value = localStorage.getItem(key);\n            if (value) {\n                localStorage.removeItem(key);\n                value = JSON.parse(value);\n            }\n\n            clearInvalidValues();\n            return value;\n        };\n\n        cs.add = function(state) {\n            clearInvalidValues();\n\n            const key = STORAGE_KEY_PREFIX + state.state;\n            const value = JSON.stringify({\n                ...state,\n                // Set the expiry time to 1 hour from now.\n                expires: Date.now() + (60 * 60 * 1000)\n            });\n\n            try {\n                localStorage.setItem(key, value);\n            } catch (error) {\n                // If the storage is full, clear all known values and try again.\n                clearAllValues();\n                localStorage.setItem(key, value);\n            }\n        };\n    };\n\n    var CookieStorage = function() {\n        if (!(this instanceof CookieStorage)) {\n            return new CookieStorage();\n        }\n\n        var cs = this;\n\n        cs.get = function(state) {\n            if (!state) {\n                return;\n            }\n\n            var value = getCookie(STORAGE_KEY_PREFIX + state);\n            setCookie(STORAGE_KEY_PREFIX + state, '', cookieExpiration(-100));\n            if (value) {\n                return JSON.parse(value);\n            }\n        };\n\n        cs.add = function(state) {\n            setCookie(STORAGE_KEY_PREFIX + state.state, JSON.stringify(state), cookieExpiration(60));\n        };\n\n        cs.removeItem = function(key) {\n            setCookie(key, '', cookieExpiration(-100));\n        };\n\n        var cookieExpiration = function (minutes) {\n            var exp = new Date();\n            exp.setTime(exp.getTime() + (minutes*60*1000));\n            return exp;\n        };\n\n        var getCookie = function (key) {\n            var name = key + '=';\n            var ca = document.cookie.split(';');\n            for (var i = 0; i < ca.length; i++) {\n                var c = ca[i];\n                while (c.charAt(0) == ' ') {\n                    c = c.substring(1);\n                }\n                if (c.indexOf(name) == 0) {\n                    return c.substring(name.length, c.length);\n                }\n            }\n            return '';\n        };\n\n        var setCookie = function (key, value, expirationDate) {\n            var cookie = key + '=' + value + '; '\n                + 'expires=' + expirationDate.toUTCString() + '; ';\n            document.cookie = cookie;\n        }\n    };\n\n    function createCallbackStorage() {\n        try {\n            return new LocalStorage();\n        } catch (err) {\n        }\n\n        return new CookieStorage();\n    }\n\n    function createLogger(fn) {\n        return function() {\n            if (kc.enableLogging) {\n                fn.apply(console, Array.prototype.slice.call(arguments));\n            }\n        };\n    }\n}\n\nexport default Keycloak;\n\n/**\n * @param {ArrayBuffer} bytes\n * @see https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\n */\nfunction bytesToBase64(bytes) {\n    const binString = String.fromCodePoint(...bytes);\n    return btoa(binString);\n}\n\n/**\n * @param {string} message\n * @see https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/digest#basic_example\n */\nasync function sha256Digest(message) {\n    const encoder = new TextEncoder();\n    const data = encoder.encode(message);\n\n    if (typeof crypto === \"undefined\" || typeof crypto.subtle === \"undefined\") {\n        throw new Error(\"Web Crypto API is not available.\");\n    }\n\n    return await crypto.subtle.digest(\"SHA-256\", data);\n}\n\n/**\n * @param {string} token\n */\nfunction decodeToken(token) {\n    const [header, payload] = token.split(\".\");\n\n    if (typeof payload !== \"string\") {\n        throw new Error(\"Unable to decode token, payload not found.\");\n    }\n\n    let decoded;\n\n    try {\n        decoded = base64UrlDecode(payload);\n    } catch (error) {\n        throw new Error(\"Unable to decode token, payload is not a valid Base64URL value.\", { cause: error });\n    }\n\n    try {\n        return JSON.parse(decoded);\n    } catch (error) {\n        throw new Error(\"Unable to decode token, payload is not a valid JSON value.\", { cause: error });\n    }\n}\n\n/**\n * @param {string} input\n */\nfunction base64UrlDecode(input) {\n    let output = input\n        .replaceAll(\"-\", \"+\")\n        .replaceAll(\"_\", \"/\");\n\n    switch (output.length % 4) {\n        case 0:\n            break;\n        case 2:\n            output += \"==\";\n            break;\n        case 3:\n            output += \"=\";\n            break;\n        default:\n            throw new Error(\"Input is not of the correct length.\");\n    }\n\n    try {\n        return b64DecodeUnicode(output);\n    } catch (error) {\n        return atob(output);\n    }\n}\n\n/**\n * @param {string} input\n */\nfunction b64DecodeUnicode(input) {\n    return decodeURIComponent(atob(input).replace(/(.)/g, (m, p) => {\n        let code = p.charCodeAt(0).toString(16).toUpperCase();\n\n        if (code.length < 2) {\n            code = \"0\" + code;\n        }\n\n        return \"%\" + code;\n    }));\n}\n\n/**\n * Check if the input is an object that can be operated on.\n * @param {unknown} input\n */\nfunction isObject(input) {\n    return typeof input === 'object' && input !== null;\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAAEC,MAAM,EAAE;EACvB,IAAI,EAAE,IAAI,YAAYD,QAAQ,CAAC,EAAE;IAC7B,MAAM,IAAIE,KAAK,CAAC,wDAAwD,CAAC;EAC7E;EAEA,IAAI,OAAOD,MAAM,KAAK,QAAQ,IAAI,CAACE,QAAQ,CAACF,MAAM,CAAC,EAAE;IACjD,MAAM,IAAIC,KAAK,CAAC,iHAAiH,CAAC;EACtI;EAEA,IAAIC,QAAQ,CAACF,MAAM,CAAC,EAAE;IAClB,MAAMG,kBAAkB,GAAG,cAAc,IAAIH,MAAM,GAC7C,CAAC,UAAU,CAAC,GACZ,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU,CAAC;IAElC,KAAK,MAAMI,QAAQ,IAAID,kBAAkB,EAAE;MACvC,IAAI,CAACH,MAAM,CAACI,QAAQ,CAAC,EAAE;QACnB,MAAM,IAAIH,KAAK,CAAC,qDAAqDG,QAAQ,aAAa,CAAC;MAC/F;IACJ;EACJ;EAEA,IAAIC,EAAE,GAAG,IAAI;EACb,IAAIC,OAAO;EACX,IAAIC,YAAY,GAAG,EAAE;EACrB,IAAIC,eAAe;EAEnB,IAAIC,WAAW,GAAG;IACdC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE;EACd,CAAC;EAEDP,EAAE,CAACQ,aAAa,GAAG,KAAK;EAExB,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAIC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAACC,IAAI,CAAC;EACxC,IAAIC,OAAO,GAAGH,YAAY,CAACC,OAAO,CAACG,IAAI,CAAC;EAExC,IAAI,CAACC,UAAU,CAACC,eAAe,EAAE;IAC7BH,OAAO,CACH,iJAAiJ,GACjJ,kGAAkG,GAClG,iGACJ,CAAC;EACL;EAEAd,EAAE,CAACkB,IAAI,GAAG,UAAUC,WAAW,GAAG,CAAC,CAAC,EAAE;IAClC,IAAInB,EAAE,CAACQ,aAAa,EAAE;MAClB,MAAM,IAAIZ,KAAK,CAAC,qDAAqD,CAAC;IAC1E;IAEAI,EAAE,CAACQ,aAAa,GAAG,IAAI;IAEvBR,EAAE,CAACoB,aAAa,GAAG,KAAK;IAExBjB,eAAe,GAAGkB,qBAAqB,CAAC,CAAC;IACzC,IAAIC,QAAQ,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,gBAAgB,CAAC;IAEvD,IAAIA,QAAQ,CAACC,OAAO,CAACJ,WAAW,CAAClB,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAC5CA,OAAO,GAAGuB,WAAW,CAACL,WAAW,CAAClB,OAAO,CAAC;IAC9C,CAAC,MAAM,IAAI,OAAOkB,WAAW,CAAClB,OAAO,KAAK,QAAQ,EAAE;MAChDA,OAAO,GAAGkB,WAAW,CAAClB,OAAO;IACjC,CAAC,MAAM;MACH,IAAIwB,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,OAAO,EAAE;QAClC1B,OAAO,GAAGuB,WAAW,CAAC,SAAS,CAAC;MACpC,CAAC,MAAM;QACHvB,OAAO,GAAGuB,WAAW,CAAC,CAAC;MAC3B;IACJ;IAEA,IAAI,OAAOL,WAAW,CAACV,QAAQ,KAAK,WAAW,EAAE;MAC7CA,QAAQ,GAAGU,WAAW,CAACV,QAAQ;IACnC;IAEA,IAAI,OAAOU,WAAW,CAACS,gBAAgB,KAAK,WAAW,EAAE;MACrDxB,WAAW,CAACC,MAAM,GAAGc,WAAW,CAACS,gBAAgB;IACrD;IAEA,IAAIT,WAAW,CAACU,wBAAwB,EAAE;MACtCzB,WAAW,CAACG,QAAQ,GAAGY,WAAW,CAACU,wBAAwB;IAC/D;IAEA,IAAIV,WAAW,CAACW,MAAM,KAAK,gBAAgB,EAAE;MACzC9B,EAAE,CAAC+B,aAAa,GAAG,IAAI;IAC3B;IAEA,IAAIZ,WAAW,CAACa,YAAY,EAAE;MAC1B,IAAIb,WAAW,CAACa,YAAY,KAAK,OAAO,IAAIb,WAAW,CAACa,YAAY,KAAK,UAAU,EAAE;QACjFhC,EAAE,CAACgC,YAAY,GAAGb,WAAW,CAACa,YAAY;MAC9C,CAAC,MAAM;QACH,MAAM,gCAAgC;MAC1C;IACJ;IAEA,IAAIb,WAAW,CAACc,IAAI,EAAE;MAClB,QAAQd,WAAW,CAACc,IAAI;QACpB,KAAK,UAAU;UACXjC,EAAE,CAACkC,YAAY,GAAG,MAAM;UACxB;QACJ,KAAK,UAAU;UACXlC,EAAE,CAACkC,YAAY,GAAG,gBAAgB;UAClC;QACJ,KAAK,QAAQ;UACTlC,EAAE,CAACkC,YAAY,GAAG,qBAAqB;UACvC;QACJ;UACI,MAAM,wBAAwB;MACtC;MACAlC,EAAE,CAACiC,IAAI,GAAGd,WAAW,CAACc,IAAI;IAC9B;IAEA,IAAId,WAAW,CAACgB,QAAQ,IAAI,IAAI,EAAE;MAC9BnC,EAAE,CAACmC,QAAQ,GAAGhB,WAAW,CAACgB,QAAQ;IACtC;IAEA,IAAGhB,WAAW,CAACiB,WAAW,EAAE;MACxBpC,EAAE,CAACoC,WAAW,GAAGjB,WAAW,CAACiB,WAAW;IAC5C;IAEA,IAAIjB,WAAW,CAACkB,yBAAyB,EAAE;MACvCrC,EAAE,CAACqC,yBAAyB,GAAGlB,WAAW,CAACkB,yBAAyB;IACxE;IAEA,IAAI,OAAOlB,WAAW,CAACmB,sBAAsB,KAAK,SAAS,EAAE;MACzDtC,EAAE,CAACsC,sBAAsB,GAAGnB,WAAW,CAACmB,sBAAsB;IAClE,CAAC,MAAM;MACHtC,EAAE,CAACsC,sBAAsB,GAAG,IAAI;IACpC;IAEA,IAAI,OAAOnB,WAAW,CAACoB,UAAU,KAAK,WAAW,EAAE;MAC/C,IAAIpB,WAAW,CAACoB,UAAU,KAAK,MAAM,IAAIpB,WAAW,CAACoB,UAAU,KAAK,KAAK,EAAE;QACvE,MAAM,IAAIC,SAAS,CAAC,mEAAmErB,WAAW,CAACoB,UAAU,GAAG,CAAC;MACrH;MAEAvC,EAAE,CAACuC,UAAU,GAAGpB,WAAW,CAACoB,UAAU;IAC1C,CAAC,MAAM;MACHvC,EAAE,CAACuC,UAAU,GAAG,MAAM;IAC1B;IAEA,IAAI,OAAOpB,WAAW,CAACsB,aAAa,KAAK,SAAS,EAAE;MAChDzC,EAAE,CAACyC,aAAa,GAAGtB,WAAW,CAACsB,aAAa;IAChD,CAAC,MAAM;MACHzC,EAAE,CAACyC,aAAa,GAAG,KAAK;IAC5B;IAEA,IAAItB,WAAW,CAACuB,YAAY,KAAK,MAAM,EAAE;MACrC1C,EAAE,CAAC0C,YAAY,GAAG,MAAM;IAC5B,CAAC,MAAM;MACH1C,EAAE,CAAC0C,YAAY,GAAG,KAAK;IAC3B;IAEA,IAAI,OAAOvB,WAAW,CAACwB,KAAK,KAAK,QAAQ,EAAE;MACvC3C,EAAE,CAAC2C,KAAK,GAAGxB,WAAW,CAACwB,KAAK;IAChC;IAEA,IAAI,OAAOxB,WAAW,CAACyB,SAAS,KAAK,QAAQ,EAAE;MAC3C5C,EAAE,CAAC4C,SAAS,GAAGzB,WAAW,CAACyB,SAAS;IACxC;IAEA,IAAI,OAAOzB,WAAW,CAAC0B,qBAAqB,KAAK,QAAQ,IAAI1B,WAAW,CAAC0B,qBAAqB,GAAG,CAAC,EAAE;MAChG7C,EAAE,CAAC6C,qBAAqB,GAAG1B,WAAW,CAAC0B,qBAAqB;IAChE,CAAC,MAAM;MACH7C,EAAE,CAAC6C,qBAAqB,GAAG,KAAK;IACpC;IAEA,IAAI,CAAC7C,EAAE,CAACgC,YAAY,EAAE;MAClBhC,EAAE,CAACgC,YAAY,GAAG,UAAU;IAChC;IACA,IAAI,CAAChC,EAAE,CAACkC,YAAY,EAAE;MAClBlC,EAAE,CAACkC,YAAY,GAAG,MAAM;MACxBlC,EAAE,CAACiC,IAAI,GAAG,UAAU;IACxB;IAEA,IAAIa,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7B,IAAIC,WAAW,GAAGD,aAAa,CAAC,CAAC;IACjCC,WAAW,CAACF,OAAO,CAACG,IAAI,CAAC,YAAW;MAChCjD,EAAE,CAACkD,OAAO,IAAIlD,EAAE,CAACkD,OAAO,CAAClD,EAAE,CAACoB,aAAa,CAAC;MAC1C0B,OAAO,CAACK,UAAU,CAACnD,EAAE,CAACoB,aAAa,CAAC;IACxC,CAAC,CAAC,CAACgC,KAAK,CAAC,UAASC,KAAK,EAAE;MACrBP,OAAO,CAACQ,QAAQ,CAACD,KAAK,CAAC;IAC3B,CAAC,CAAC;IAEF,IAAIE,aAAa,GAAGC,UAAU,CAAC,CAAC;IAEhC,SAAS1B,MAAMA,CAAA,EAAG;MACd,IAAI2B,OAAO,GAAG,SAAAA,CAASC,MAAM,EAAE;QAC3B,IAAI,CAACA,MAAM,EAAE;UACTC,OAAO,CAACD,MAAM,GAAG,MAAM;QAC3B;QAEA,IAAIvC,WAAW,CAACyC,MAAM,EAAE;UACpBD,OAAO,CAACC,MAAM,GAAGzC,WAAW,CAACyC,MAAM;QACvC;QACA5D,EAAE,CAAC6D,KAAK,CAACF,OAAO,CAAC,CAACV,IAAI,CAAC,YAAY;UAC/BD,WAAW,CAACG,UAAU,CAAC,CAAC;QAC5B,CAAC,CAAC,CAACC,KAAK,CAAC,UAAUC,KAAK,EAAE;UACtBL,WAAW,CAACM,QAAQ,CAACD,KAAK,CAAC;QAC/B,CAAC,CAAC;MACN,CAAC;MAED,IAAIS,gBAAgB;QAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAiB;UACpC,IAAIC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC3C,IAAIC,GAAG,SAASpE,EAAE,CAACqE,cAAc,CAAC;YAACX,MAAM,EAAE,MAAM;YAAEtB,WAAW,EAAEpC,EAAE,CAACqC;UAAyB,CAAC,CAAC;UAC9F4B,IAAI,CAACK,YAAY,CAAC,KAAK,EAAEF,GAAG,CAAC;UAC7BH,IAAI,CAACK,YAAY,CAAC,SAAS,EAAE,yEAAyE,CAAC;UACvGL,IAAI,CAACK,YAAY,CAAC,OAAO,EAAE,2BAA2B,CAAC;UACvDL,IAAI,CAACM,KAAK,CAACC,OAAO,GAAG,MAAM;UAC3BN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;UAE/B,IAAIU,eAAe,GAAG,SAAAA,CAASC,KAAK,EAAE;YAClC,IAAIA,KAAK,CAACC,MAAM,KAAKpD,MAAM,CAACqD,QAAQ,CAACD,MAAM,IAAIZ,IAAI,CAACc,aAAa,KAAKH,KAAK,CAACI,MAAM,EAAE;cAChF;YACJ;YAEA,IAAIC,KAAK,GAAGC,aAAa,CAACN,KAAK,CAACO,IAAI,CAAC;YACrCC,eAAe,CAACH,KAAK,EAAEjC,WAAW,CAAC;YAEnCkB,QAAQ,CAACO,IAAI,CAACY,WAAW,CAACpB,IAAI,CAAC;YAC/BxC,MAAM,CAAC6D,mBAAmB,CAAC,SAAS,EAAEX,eAAe,CAAC;UAC1D,CAAC;UAEDlD,MAAM,CAAC8D,gBAAgB,CAAC,SAAS,EAAEZ,eAAe,CAAC;QACvD,CAAC;QAAA,gBAtBGb,gBAAgBA,CAAA;UAAA,OAAAC,IAAA,CAAAyB,KAAA,OAAAC,SAAA;QAAA;MAAA,GAsBnB;MAED,IAAI9B,OAAO,GAAG,CAAC,CAAC;MAChB,QAAQxC,WAAW,CAACW,MAAM;QACtB,KAAK,WAAW;UACZ,IAAI1B,WAAW,CAACC,MAAM,EAAE;YACpBqF,qBAAqB,CAAC,CAAC,CAACzC,IAAI,CAAC,YAAW;cACpCrB,gBAAgB,CAAC,CAAC,CAACqB,IAAI,CAAC,UAAU0C,SAAS,EAAE;gBACzC,IAAI,CAACA,SAAS,EAAE;kBACZ3F,EAAE,CAACqC,yBAAyB,GAAGyB,gBAAgB,CAAC,CAAC,GAAGL,OAAO,CAAC,KAAK,CAAC;gBACtE,CAAC,MAAM;kBACHT,WAAW,CAACG,UAAU,CAAC,CAAC;gBAC5B;cACJ,CAAC,CAAC,CAACC,KAAK,CAAC,UAAUC,KAAK,EAAE;gBACtBL,WAAW,CAACM,QAAQ,CAACD,KAAK,CAAC;cAC/B,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,MAAM;YACHrD,EAAE,CAACqC,yBAAyB,GAAGyB,gBAAgB,CAAC,CAAC,GAAGL,OAAO,CAAC,KAAK,CAAC;UACtE;UACA;QACJ,KAAK,gBAAgB;UACjBA,OAAO,CAAC,IAAI,CAAC;UACb;QACJ;UACI,MAAM,0BAA0B;MACxC;IACJ;IAEA,SAASmC,WAAWA,CAAA,EAAG;MACnB,IAAIC,QAAQ,GAAGX,aAAa,CAACzD,MAAM,CAACqD,QAAQ,CAACgB,IAAI,CAAC;MAElD,IAAID,QAAQ,EAAE;QACVpE,MAAM,CAACsE,OAAO,CAACC,YAAY,CAACvE,MAAM,CAACsE,OAAO,CAACE,KAAK,EAAE,IAAI,EAAEJ,QAAQ,CAACK,MAAM,CAAC;MAC5E;MAEA,IAAIL,QAAQ,IAAIA,QAAQ,CAACM,KAAK,EAAE;QAC5B,OAAOT,qBAAqB,CAAC,CAAC,CAACzC,IAAI,CAAC,YAAW;UAC3CmC,eAAe,CAACS,QAAQ,EAAE7C,WAAW,CAAC;QAC1C,CAAC,CAAC,CAACI,KAAK,CAAC,UAAUC,KAAK,EAAE;UACtBL,WAAW,CAACM,QAAQ,CAACD,KAAK,CAAC;QAC/B,CAAC,CAAC;MACN;MAEA,IAAIlC,WAAW,CAACiF,KAAK,IAAIjF,WAAW,CAACkF,YAAY,EAAE;QAC/CC,QAAQ,CAACnF,WAAW,CAACiF,KAAK,EAAEjF,WAAW,CAACkF,YAAY,EAAElF,WAAW,CAACoF,OAAO,CAAC;QAE1E,IAAInG,WAAW,CAACC,MAAM,EAAE;UACpBqF,qBAAqB,CAAC,CAAC,CAACzC,IAAI,CAAC,YAAW;YACpCrB,gBAAgB,CAAC,CAAC,CAACqB,IAAI,CAAC,UAAU0C,SAAS,EAAE;cACzC,IAAIA,SAAS,EAAE;gBACX3F,EAAE,CAACwG,aAAa,IAAIxG,EAAE,CAACwG,aAAa,CAAC,CAAC;gBACtCxD,WAAW,CAACG,UAAU,CAAC,CAAC;gBACxBsD,mBAAmB,CAAC,CAAC;cACzB,CAAC,MAAM;gBACHzD,WAAW,CAACG,UAAU,CAAC,CAAC;cAC5B;YACJ,CAAC,CAAC,CAACC,KAAK,CAAC,UAAUC,KAAK,EAAE;cACtBL,WAAW,CAACM,QAAQ,CAACD,KAAK,CAAC;YAC/B,CAAC,CAAC;UACN,CAAC,CAAC;QACN,CAAC,MAAM;UACHrD,EAAE,CAAC0G,WAAW,CAAC,CAAC,CAAC,CAAC,CAACzD,IAAI,CAAC,YAAW;YAC/BjD,EAAE,CAACwG,aAAa,IAAIxG,EAAE,CAACwG,aAAa,CAAC,CAAC;YACtCxD,WAAW,CAACG,UAAU,CAAC,CAAC;UAC5B,CAAC,CAAC,CAACC,KAAK,CAAC,UAASC,KAAK,EAAE;YACrBrD,EAAE,CAAC2G,WAAW,IAAI3G,EAAE,CAAC2G,WAAW,CAAC,CAAC;YAClC,IAAIxF,WAAW,CAACW,MAAM,EAAE;cACpBA,MAAM,CAAC,CAAC;YACZ,CAAC,MAAM;cACHkB,WAAW,CAACM,QAAQ,CAACD,KAAK,CAAC;YAC/B;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,MAAM,IAAIlC,WAAW,CAACW,MAAM,EAAE;QAC3BA,MAAM,CAAC,CAAC;MACZ,CAAC,MAAM;QACHkB,WAAW,CAACG,UAAU,CAAC,CAAC;MAC5B;IACJ;IAEAI,aAAa,CAACN,IAAI,CAAC,YAAY;MAC3B2D,uBAAuB,CAAC,CAAC,CACpB3D,IAAI,CAAC2C,WAAW,CAAC,CACjBxC,KAAK,CAAC,UAAUC,KAAK,EAAE;QACpBP,OAAO,CAACQ,QAAQ,CAACD,KAAK,CAAC;MAC3B,CAAC,CAAC;IACV,CAAC,CAAC;IACFE,aAAa,CAACH,KAAK,CAAC,UAAUC,KAAK,EAAE;MACjCP,OAAO,CAACQ,QAAQ,CAACD,KAAK,CAAC;IAC3B,CAAC,CAAC;IAEF,OAAOP,OAAO,CAACA,OAAO;EAC1B,CAAC;EAED9C,EAAE,CAAC6D,KAAK,GAAG,UAAUF,OAAO,EAAE;IAC1B,OAAO1D,OAAO,CAAC4D,KAAK,CAACF,OAAO,CAAC;EACjC,CAAC;EAED,SAASkD,kBAAkBA,CAACC,GAAG,EAAE;IAC7B,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,eAAe,KAAK,WAAW,EAAE;MAChF,MAAM,IAAIpH,KAAK,CAAC,kCAAkC,CAAC;IACvD;IAEA,OAAOmH,MAAM,CAACC,eAAe,CAAC,IAAIC,UAAU,CAACH,GAAG,CAAC,CAAC;EACtD;EAEA,SAASI,oBAAoBA,CAACJ,GAAG,EAAE;IAC/B,OAAOK,oBAAoB,CAACL,GAAG,EAAE,gEAAgE,CAAC;EACtG;EAEA,SAASK,oBAAoBA,CAACL,GAAG,EAAEM,QAAQ,EAAC;IACxC,IAAIC,UAAU,GAAGR,kBAAkB,CAACC,GAAG,CAAC;IACxC,IAAIQ,KAAK,GAAG,IAAIC,KAAK,CAACT,GAAG,CAAC;IAC1B,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,GAAG,EAAEU,CAAC,EAAE,EAAE;MAC1BF,KAAK,CAACE,CAAC,CAAC,GAAGJ,QAAQ,CAACK,UAAU,CAACJ,UAAU,CAACG,CAAC,CAAC,GAAGJ,QAAQ,CAACM,MAAM,CAAC;IACnE;IACA,OAAOC,MAAM,CAACC,YAAY,CAACpC,KAAK,CAAC,IAAI,EAAE8B,KAAK,CAAC;EACjD;EAAC,SAEcO,qBAAqBA,CAAAC,EAAA,EAAAC,GAAA;IAAA,OAAAC,sBAAA,CAAAxC,KAAA,OAAAC,SAAA;EAAA;EAAA,SAAAuC,uBAAA;IAAAA,sBAAA,GAAAhE,iBAAA,CAApC,WAAqCzB,UAAU,EAAE0F,YAAY,EAAE;MAC3D,IAAI1F,UAAU,KAAK,MAAM,EAAE;QACvB,MAAM,IAAIC,SAAS,CAAC,4DAA4DD,UAAU,IAAI,CAAC;MACnG;;MAEA;MACA,MAAM2F,SAAS,GAAG,IAAIjB,UAAU,OAAOkB,YAAY,CAACF,YAAY,CAAC,CAAC;MAClE,MAAMG,WAAW,GAAGC,aAAa,CAACH,SAAS,CAAC,CACvCI,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAEvB,OAAOF,WAAW;IACtB,CAAC;IAAA,OAAAJ,sBAAA,CAAAxC,KAAA,OAAAC,SAAA;EAAA;EAED,SAAS8C,oBAAoBA,CAACC,YAAY,EAAC;IACvC,IAAIC,MAAM,GAAG;MACTC,QAAQ,EAAE;QACNC,GAAG,EAAEH;MACT;IACJ,CAAC;IACD,OAAOI,IAAI,CAACC,SAAS,CAACJ,MAAM,CAAC;EACjC;EAEAzI,EAAE,CAACqE,cAAc;IAAA,IAAAyE,KAAA,GAAA9E,iBAAA,CAAG,WAAeL,OAAO,EAAE;MACxC,IAAIsC,KAAK,GAAG8C,UAAU,CAAC,CAAC;MACxB,IAAIC,KAAK,GAAGD,UAAU,CAAC,CAAC;MAExB,IAAI3G,WAAW,GAAGnC,OAAO,CAACmC,WAAW,CAACuB,OAAO,CAAC;MAE9C,IAAIsF,aAAa,GAAG;QAChBhD,KAAK,EAAEA,KAAK;QACZ+C,KAAK,EAAEA,KAAK;QACZ5G,WAAW,EAAE8G,kBAAkB,CAAC9G,WAAW,CAAC;QAC5C+G,YAAY,EAAExF;MAClB,CAAC;MAED,IAAIA,OAAO,IAAIA,OAAO,CAACD,MAAM,EAAE;QAC3BuF,aAAa,CAACvF,MAAM,GAAGC,OAAO,CAACD,MAAM;MACzC;MAEA,IAAI0F,OAAO;MACX,IAAIzF,OAAO,IAAIA,OAAO,CAAC0F,MAAM,IAAI,UAAU,EAAE;QACzCD,OAAO,GAAGpJ,EAAE,CAACsJ,SAAS,CAACC,QAAQ,CAAC,CAAC;MACrC,CAAC,MAAM;QACHH,OAAO,GAAGpJ,EAAE,CAACsJ,SAAS,CAACE,SAAS,CAAC,CAAC;MACtC;MAEA,IAAI7G,KAAK,GAAGgB,OAAO,IAAIA,OAAO,CAAChB,KAAK,IAAI3C,EAAE,CAAC2C,KAAK;MAChD,IAAI,CAACA,KAAK,EAAE;QACR;QACAA,KAAK,GAAG,QAAQ;MACpB,CAAC,MAAM,IAAIA,KAAK,CAACpB,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QACvC;QACAoB,KAAK,GAAG,SAAS,GAAGA,KAAK;MAC7B;MAEA,IAAI8G,GAAG,GAAGL,OAAO,GACX,aAAa,GAAGF,kBAAkB,CAAClJ,EAAE,CAAC0J,QAAQ,CAAC,GAC/C,gBAAgB,GAAGR,kBAAkB,CAAC9G,WAAW,CAAC,GAClD,SAAS,GAAG8G,kBAAkB,CAACjD,KAAK,CAAC,GACrC,iBAAiB,GAAGiD,kBAAkB,CAAClJ,EAAE,CAACgC,YAAY,CAAC,GACvD,iBAAiB,GAAGkH,kBAAkB,CAAClJ,EAAE,CAACkC,YAAY,CAAC,GACvD,SAAS,GAAGgH,kBAAkB,CAACvG,KAAK,CAAC;MAC3C,IAAIlC,QAAQ,EAAE;QACVgJ,GAAG,GAAGA,GAAG,GAAG,SAAS,GAAGP,kBAAkB,CAACF,KAAK,CAAC;MACrD;MAEA,IAAIrF,OAAO,IAAIA,OAAO,CAACD,MAAM,EAAE;QAC3B+F,GAAG,IAAI,UAAU,GAAGP,kBAAkB,CAACvF,OAAO,CAACD,MAAM,CAAC;MAC1D;MAEA,IAAIC,OAAO,IAAI,OAAOA,OAAO,CAACgG,MAAM,KAAK,QAAQ,EAAE;QAC/CF,GAAG,IAAI,WAAW,GAAGP,kBAAkB,CAACvF,OAAO,CAACgG,MAAM,CAAC;MAC3D;MAEA,IAAIhG,OAAO,IAAIA,OAAO,CAACiG,SAAS,EAAE;QAC9BH,GAAG,IAAI,cAAc,GAAGP,kBAAkB,CAACvF,OAAO,CAACiG,SAAS,CAAC;MACjE;MAEA,IAAIjG,OAAO,IAAIA,OAAO,CAACkG,OAAO,EAAE;QAC5BJ,GAAG,IAAI,eAAe,GAAGP,kBAAkB,CAACvF,OAAO,CAACkG,OAAO,CAAC;MAChE;MAEA,IAAIlG,OAAO,IAAIA,OAAO,CAAC0F,MAAM,IAAI1F,OAAO,CAAC0F,MAAM,IAAI,UAAU,EAAE;QAC3DI,GAAG,IAAI,aAAa,GAAGP,kBAAkB,CAACvF,OAAO,CAAC0F,MAAM,CAAC;MAC7D;MAEA,IAAI1F,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;QAC3B6F,GAAG,IAAI,cAAc,GAAGP,kBAAkB,CAACvF,OAAO,CAACC,MAAM,CAAC;MAC9D;MAEA,IAAID,OAAO,IAAIA,OAAO,CAACgF,GAAG,EAAE;QACxB,IAAImB,eAAe,GAAGvB,oBAAoB,CAAC5E,OAAO,CAACgF,GAAG,CAAC;QACvDc,GAAG,IAAI,UAAU,GAAGP,kBAAkB,CAACY,eAAe,CAAC;MAC3D;MAEA,IAAKnG,OAAO,IAAIA,OAAO,CAACf,SAAS,IAAK5C,EAAE,CAAC4C,SAAS,EAAE;QAChD6G,GAAG,IAAI,cAAc,GAAGP,kBAAkB,CAACvF,OAAO,CAACf,SAAS,IAAI5C,EAAE,CAAC4C,SAAS,CAAC;MACjF;MAEA,IAAI5C,EAAE,CAACuC,UAAU,EAAE;QACf,IAAI;UACA,MAAM0F,YAAY,GAAGf,oBAAoB,CAAC,EAAE,CAAC;UAC7C,MAAM6C,aAAa,SAASlC,qBAAqB,CAAC7H,EAAE,CAACuC,UAAU,EAAE0F,YAAY,CAAC;UAE9EgB,aAAa,CAACe,gBAAgB,GAAG/B,YAAY;UAE7CwB,GAAG,IAAI,kBAAkB,GAAGM,aAAa;UACzCN,GAAG,IAAI,yBAAyB,GAAGzJ,EAAE,CAACuC,UAAU;QACpD,CAAC,CAAC,OAAOc,KAAK,EAAE;UACZ,MAAM,IAAIzD,KAAK,CAAC,oCAAoC,EAAE;YAAEqK,KAAK,EAAE5G;UAAM,CAAC,CAAC;QAC3E;MACJ;MAEAlD,eAAe,CAAC+J,GAAG,CAACjB,aAAa,CAAC;MAElC,OAAOQ,GAAG;IACd,CAAC;IAAA,iBAAAU,GAAA;MAAA,OAAArB,KAAA,CAAAtD,KAAA,OAAAC,SAAA;IAAA;EAAA;EAEDzF,EAAE,CAACoK,MAAM,GAAG,UAASzG,OAAO,EAAE;IAC1B,OAAO1D,OAAO,CAACmK,MAAM,CAACzG,OAAO,CAAC;EAClC,CAAC;EAED3D,EAAE,CAACqK,eAAe,GAAG,UAAS1G,OAAO,EAAE;IAEnC,MAAMjB,YAAY,GAAGiB,OAAO,EAAEjB,YAAY,IAAI1C,EAAE,CAAC0C,YAAY;IAC7D,IAAIA,YAAY,KAAK,MAAM,EAAE;MACzB,OAAO1C,EAAE,CAACsJ,SAAS,CAACc,MAAM,CAAC,CAAC;IAChC;IAEA,IAAIX,GAAG,GAAGzJ,EAAE,CAACsJ,SAAS,CAACc,MAAM,CAAC,CAAC,GACzB,aAAa,GAAGlB,kBAAkB,CAAClJ,EAAE,CAAC0J,QAAQ,CAAC,GAC/C,4BAA4B,GAAGR,kBAAkB,CAACjJ,OAAO,CAACmC,WAAW,CAACuB,OAAO,EAAE,KAAK,CAAC,CAAC;IAE5F,IAAI3D,EAAE,CAACuG,OAAO,EAAE;MACZkD,GAAG,IAAI,iBAAiB,GAAGP,kBAAkB,CAAClJ,EAAE,CAACuG,OAAO,CAAC;IAC7D;IAEA,OAAOkD,GAAG;EACd,CAAC;EAEDzJ,EAAE,CAACuJ,QAAQ,GAAG,UAAU5F,OAAO,EAAE;IAC7B,OAAO1D,OAAO,CAACsJ,QAAQ,CAAC5F,OAAO,CAAC;EACpC,CAAC;EAED3D,EAAE,CAACsK,iBAAiB;IAAA,IAAAC,KAAA,GAAAvG,iBAAA,CAAG,WAAeL,OAAO,EAAE;MAC3C,IAAI,CAACA,OAAO,EAAE;QACVA,OAAO,GAAG,CAAC,CAAC;MAChB;MACAA,OAAO,CAAC0F,MAAM,GAAG,UAAU;MAC3B,aAAarJ,EAAE,CAACqE,cAAc,CAACV,OAAO,CAAC;IAC3C,CAAC;IAAA,iBAAA6G,GAAA;MAAA,OAAAD,KAAA,CAAA/E,KAAA,OAAAC,SAAA;IAAA;EAAA;EAEDzF,EAAE,CAACyK,gBAAgB,GAAG,UAAS9G,OAAO,EAAE;IACpC,IAAI+G,KAAK,GAAGC,WAAW,CAAC,CAAC;IACzB,IAAIlB,GAAG,GAAGmB,SAAS;IACnB,IAAI,OAAOF,KAAK,KAAK,WAAW,EAAE;MAC9BjB,GAAG,GAAGiB,KAAK,GACT,UAAU,GACV,YAAY,GAAGxB,kBAAkB,CAAClJ,EAAE,CAAC0J,QAAQ,CAAC,GAC9C,gBAAgB,GAAGR,kBAAkB,CAACjJ,OAAO,CAACmC,WAAW,CAACuB,OAAO,CAAC,CAAC;IACzE;IACA,OAAO8F,GAAG;EACd,CAAC;EAEDzJ,EAAE,CAAC6K,iBAAiB,GAAG,YAAW;IAC9B,OAAO5K,OAAO,CAAC4K,iBAAiB,CAAC,CAAC;EACtC,CAAC;EAED7K,EAAE,CAAC8K,YAAY,GAAG,UAAUC,IAAI,EAAE;IAC9B,IAAIC,MAAM,GAAGhL,EAAE,CAACiL,WAAW;IAC3B,OAAO,CAAC,CAACD,MAAM,IAAIA,MAAM,CAACE,KAAK,CAAC3J,OAAO,CAACwJ,IAAI,CAAC,IAAI,CAAC;EACtD,CAAC;EAED/K,EAAE,CAACmL,eAAe,GAAG,UAASJ,IAAI,EAAEK,QAAQ,EAAE;IAC1C,IAAI,CAACpL,EAAE,CAACqL,cAAc,EAAE;MACpB,OAAO,KAAK;IAChB;IAEA,IAAIL,MAAM,GAAGhL,EAAE,CAACqL,cAAc,CAACD,QAAQ,IAAIpL,EAAE,CAAC0J,QAAQ,CAAC;IACvD,OAAO,CAAC,CAACsB,MAAM,IAAIA,MAAM,CAACE,KAAK,CAAC3J,OAAO,CAACwJ,IAAI,CAAC,IAAI,CAAC;EACtD,CAAC;EAED/K,EAAE,CAACsL,eAAe,GAAG,YAAW;IAC5B,IAAI7B,GAAG,GAAGkB,WAAW,CAAC,CAAC,GAAG,UAAU;IACpC,IAAIY,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;IAC9BD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAEhC,GAAG,EAAE,IAAI,CAAC;IAC1B8B,GAAG,CAACG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC;IAClDH,GAAG,CAACG,gBAAgB,CAAC,eAAe,EAAE,SAAS,GAAG1L,EAAE,CAACoG,KAAK,CAAC;IAE3D,IAAItD,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7BwI,GAAG,CAACI,kBAAkB,GAAG,YAAY;MACjC,IAAIJ,GAAG,CAACK,UAAU,IAAI,CAAC,EAAE;QACrB,IAAIL,GAAG,CAACM,MAAM,IAAI,GAAG,EAAE;UACnB7L,EAAE,CAAC8L,OAAO,GAAGlD,IAAI,CAACmD,KAAK,CAACR,GAAG,CAACS,YAAY,CAAC;UACzClJ,OAAO,CAACK,UAAU,CAACnD,EAAE,CAAC8L,OAAO,CAAC;QAClC,CAAC,MAAM;UACHhJ,OAAO,CAACQ,QAAQ,CAAC,CAAC;QACtB;MACJ;IACJ,CAAC;IAEDiI,GAAG,CAACU,IAAI,CAAC,CAAC;IAEV,OAAOnJ,OAAO,CAACA,OAAO;EAC1B,CAAC;EAED9C,EAAE,CAACkM,YAAY,GAAG,YAAW;IACzB,IAAIzC,GAAG,GAAGzJ,EAAE,CAACsJ,SAAS,CAAC6C,QAAQ,CAAC,CAAC;IACjC,IAAIZ,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;IAC9BD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAEhC,GAAG,EAAE,IAAI,CAAC;IAC1B8B,GAAG,CAACG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC;IAClDH,GAAG,CAACG,gBAAgB,CAAC,eAAe,EAAE,SAAS,GAAG1L,EAAE,CAACoG,KAAK,CAAC;IAE3D,IAAItD,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7BwI,GAAG,CAACI,kBAAkB,GAAG,YAAY;MACjC,IAAIJ,GAAG,CAACK,UAAU,IAAI,CAAC,EAAE;QACrB,IAAIL,GAAG,CAACM,MAAM,IAAI,GAAG,EAAE;UACnB7L,EAAE,CAACoM,QAAQ,GAAGxD,IAAI,CAACmD,KAAK,CAACR,GAAG,CAACS,YAAY,CAAC;UAC1ClJ,OAAO,CAACK,UAAU,CAACnD,EAAE,CAACoM,QAAQ,CAAC;QACnC,CAAC,MAAM;UACHtJ,OAAO,CAACQ,QAAQ,CAAC,CAAC;QACtB;MACJ;IACJ,CAAC;IAEDiI,GAAG,CAACU,IAAI,CAAC,CAAC;IAEV,OAAOnJ,OAAO,CAACA,OAAO;EAC1B,CAAC;EAED9C,EAAE,CAACqM,cAAc,GAAG,UAASC,WAAW,EAAE;IACtC,IAAI,CAACtM,EAAE,CAACuM,WAAW,IAAK,CAACvM,EAAE,CAACqG,YAAY,IAAIrG,EAAE,CAACiC,IAAI,IAAI,UAAY,EAAE;MACjE,MAAM,mBAAmB;IAC7B;IAEA,IAAIjC,EAAE,CAACmC,QAAQ,IAAI,IAAI,EAAE;MACrBzB,OAAO,CAAC,2EAA2E,CAAC;MACpF,OAAO,IAAI;IACf;IAEA,IAAI8L,SAAS,GAAGxM,EAAE,CAACuM,WAAW,CAAC,KAAK,CAAC,GAAGE,IAAI,CAACC,IAAI,CAAC,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG5M,EAAE,CAACmC,QAAQ;IAC5F,IAAImK,WAAW,EAAE;MACb,IAAIO,KAAK,CAACP,WAAW,CAAC,EAAE;QACpB,MAAM,qBAAqB;MAC/B;MACAE,SAAS,IAAIF,WAAW;IAC5B;IACA,OAAOE,SAAS,GAAG,CAAC;EACxB,CAAC;EAEDxM,EAAE,CAAC0G,WAAW,GAAG,UAAS4F,WAAW,EAAE;IACnC,IAAIxJ,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7B,IAAI,CAAC/C,EAAE,CAACqG,YAAY,EAAE;MAClBvD,OAAO,CAACQ,QAAQ,CAAC,CAAC;MAClB,OAAOR,OAAO,CAACA,OAAO;IAC1B;IAEAwJ,WAAW,GAAGA,WAAW,IAAI,CAAC;IAE9B,IAAIQ,IAAI,GAAG,SAAAA,CAAA,EAAW;MAClB,IAAIzG,YAAY,GAAG,KAAK;MACxB,IAAIiG,WAAW,IAAI,CAAC,CAAC,EAAE;QACnBjG,YAAY,GAAG,IAAI;QACnB3F,OAAO,CAAC,6CAA6C,CAAC;MAC1D,CAAC,MAAM,IAAI,CAACV,EAAE,CAACuM,WAAW,IAAIvM,EAAE,CAACqM,cAAc,CAACC,WAAW,CAAC,EAAE;QAC1DjG,YAAY,GAAG,IAAI;QACnB3F,OAAO,CAAC,4CAA4C,CAAC;MACzD;MAEA,IAAI,CAAC2F,YAAY,EAAE;QACfvD,OAAO,CAACK,UAAU,CAAC,KAAK,CAAC;MAC7B,CAAC,MAAM;QACH,IAAI4J,MAAM,GAAG,2BAA2B,GAAG,gBAAgB,GAAG/M,EAAE,CAACqG,YAAY;QAC7E,IAAIoD,GAAG,GAAGzJ,EAAE,CAACsJ,SAAS,CAAClD,KAAK,CAAC,CAAC;QAE9BlG,YAAY,CAAC8M,IAAI,CAAClK,OAAO,CAAC;QAE1B,IAAI5C,YAAY,CAACwH,MAAM,IAAI,CAAC,EAAE;UAC1B,IAAI6D,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;UAC9BD,GAAG,CAACE,IAAI,CAAC,MAAM,EAAEhC,GAAG,EAAE,IAAI,CAAC;UAC3B8B,GAAG,CAACG,gBAAgB,CAAC,cAAc,EAAE,mCAAmC,CAAC;UACzEH,GAAG,CAAC0B,eAAe,GAAG,IAAI;UAE1BF,MAAM,IAAI,aAAa,GAAG7D,kBAAkB,CAAClJ,EAAE,CAAC0J,QAAQ,CAAC;UAEzD,IAAIwD,SAAS,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;UAEpCrB,GAAG,CAACI,kBAAkB,GAAG,YAAY;YACjC,IAAIJ,GAAG,CAACK,UAAU,IAAI,CAAC,EAAE;cACrB,IAAIL,GAAG,CAACM,MAAM,IAAI,GAAG,EAAE;gBACnBnL,OAAO,CAAC,4BAA4B,CAAC;gBAErCwM,SAAS,GAAG,CAACA,SAAS,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,IAAI,CAAC;gBAElD,IAAIO,aAAa,GAAGvE,IAAI,CAACmD,KAAK,CAACR,GAAG,CAACS,YAAY,CAAC;gBAEhD1F,QAAQ,CAAC6G,aAAa,CAAC,cAAc,CAAC,EAAEA,aAAa,CAAC,eAAe,CAAC,EAAEA,aAAa,CAAC,UAAU,CAAC,EAAED,SAAS,CAAC;gBAE7GlN,EAAE,CAACoN,oBAAoB,IAAIpN,EAAE,CAACoN,oBAAoB,CAAC,CAAC;gBACpD,KAAK,IAAIC,CAAC,GAAGnN,YAAY,CAACoN,GAAG,CAAC,CAAC,EAAED,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAGnN,YAAY,CAACoN,GAAG,CAAC,CAAC,EAAE;kBAChED,CAAC,CAAClK,UAAU,CAAC,IAAI,CAAC;gBACtB;cACJ,CAAC,MAAM;gBACHrC,OAAO,CAAC,oCAAoC,CAAC;gBAE7C,IAAIyK,GAAG,CAACM,MAAM,IAAI,GAAG,EAAE;kBACnB7L,EAAE,CAACuN,UAAU,CAAC,CAAC;gBACnB;gBAEAvN,EAAE,CAACwN,kBAAkB,IAAIxN,EAAE,CAACwN,kBAAkB,CAAC,CAAC;gBAChD,KAAK,IAAIH,CAAC,GAAGnN,YAAY,CAACoN,GAAG,CAAC,CAAC,EAAED,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAGnN,YAAY,CAACoN,GAAG,CAAC,CAAC,EAAE;kBAChED,CAAC,CAAC/J,QAAQ,CAAC,mGAAmG,CAAC;gBACnH;cACJ;YACJ;UACJ,CAAC;UAEDiI,GAAG,CAACU,IAAI,CAACc,MAAM,CAAC;QACpB;MACJ;IACJ,CAAC;IAED,IAAI3M,WAAW,CAACC,MAAM,EAAE;MACpB,IAAIoN,aAAa,GAAG7L,gBAAgB,CAAC,CAAC;MACtC6L,aAAa,CAACxK,IAAI,CAAC,YAAW;QAC1B6J,IAAI,CAAC,CAAC;MACV,CAAC,CAAC,CAAC1J,KAAK,CAAC,UAASC,KAAK,EAAE;QACrBP,OAAO,CAACQ,QAAQ,CAACD,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,MAAM;MACHyJ,IAAI,CAAC,CAAC;IACV;IAEA,OAAOhK,OAAO,CAACA,OAAO;EAC1B,CAAC;EAED9C,EAAE,CAACuN,UAAU,GAAG,YAAW;IACvB,IAAIvN,EAAE,CAACoG,KAAK,EAAE;MACVE,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC1BtG,EAAE,CAAC0N,YAAY,IAAI1N,EAAE,CAAC0N,YAAY,CAAC,CAAC;MACpC,IAAI1N,EAAE,CAAC+B,aAAa,EAAE;QAClB/B,EAAE,CAAC6D,KAAK,CAAC,CAAC;MACd;IACJ;EACJ,CAAC;EAED,SAAS8G,WAAWA,CAAA,EAAG;IACnB,IAAI,OAAO3K,EAAE,CAAC2N,aAAa,KAAK,WAAW,EAAE;MACzC,IAAI3N,EAAE,CAAC2N,aAAa,CAACC,MAAM,CAAC5N,EAAE,CAAC2N,aAAa,CAACjG,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;QAC7D,OAAO1H,EAAE,CAAC2N,aAAa,GAAG,SAAS,GAAGzE,kBAAkB,CAAClJ,EAAE,CAAC0K,KAAK,CAAC;MACtE,CAAC,MAAM;QACH,OAAO1K,EAAE,CAAC2N,aAAa,GAAG,UAAU,GAAGzE,kBAAkB,CAAClJ,EAAE,CAAC0K,KAAK,CAAC;MACvE;IACJ,CAAC,MAAM;MACH,OAAOE,SAAS;IACpB;EACJ;EAEA,SAASiD,SAASA,CAAA,EAAG;IACjB,IAAI,CAACpM,MAAM,CAACqD,QAAQ,CAACD,MAAM,EAAE;MACzB,OAAOpD,MAAM,CAACqD,QAAQ,CAACgJ,QAAQ,GAAG,IAAI,GAAGrM,MAAM,CAACqD,QAAQ,CAACiJ,QAAQ,IAAItM,MAAM,CAACqD,QAAQ,CAACkJ,IAAI,GAAG,GAAG,GAAGvM,MAAM,CAACqD,QAAQ,CAACkJ,IAAI,GAAE,EAAE,CAAC;IAC/H,CAAC,MAAM;MACH,OAAOvM,MAAM,CAACqD,QAAQ,CAACD,MAAM;IACjC;EACJ;EAEA,SAASO,eAAeA,CAACH,KAAK,EAAEnC,OAAO,EAAE;IACrC,IAAImL,IAAI,GAAGhJ,KAAK,CAACgJ,IAAI;IACrB,IAAI5K,KAAK,GAAG4B,KAAK,CAAC5B,KAAK;IACvB,IAAIK,MAAM,GAAGuB,KAAK,CAACvB,MAAM;IAEzB,IAAIwJ,SAAS,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAEpC,IAAI3H,KAAK,CAAC,kBAAkB,CAAC,EAAE;MAC3BjF,EAAE,CAACkO,cAAc,IAAIlO,EAAE,CAACkO,cAAc,CAACjJ,KAAK,CAAC,kBAAkB,CAAC,EAAEA,KAAK,CAAC,WAAW,CAAC,CAAC;IACzF;IAEA,IAAI5B,KAAK,EAAE;MACP,IAAIK,MAAM,IAAI,MAAM,EAAE;QAClB,IAAIuB,KAAK,CAACkJ,iBAAiB,IAAIlJ,KAAK,CAACkJ,iBAAiB,KAAK,wBAAwB,EAAE;UACjFnO,EAAE,CAAC6D,KAAK,CAACoB,KAAK,CAACkE,YAAY,CAAC;QAChC,CAAC,MAAM;UACH,IAAIiF,SAAS,GAAG;YAAE/K,KAAK,EAAEA,KAAK;YAAE8K,iBAAiB,EAAElJ,KAAK,CAACkJ;UAAkB,CAAC;UAC5EnO,EAAE,CAAC2G,WAAW,IAAI3G,EAAE,CAAC2G,WAAW,CAACyH,SAAS,CAAC;UAC3CtL,OAAO,IAAIA,OAAO,CAACQ,QAAQ,CAAC8K,SAAS,CAAC;QAC1C;MACJ,CAAC,MAAM;QACHtL,OAAO,IAAIA,OAAO,CAACK,UAAU,CAAC,CAAC;MACnC;MACA;IACJ,CAAC,MAAM,IAAKnD,EAAE,CAACiC,IAAI,IAAI,UAAU,KAAMgD,KAAK,CAACoJ,YAAY,IAAIpJ,KAAK,CAACyD,QAAQ,CAAC,EAAE;MAC1E4F,WAAW,CAACrJ,KAAK,CAACoJ,YAAY,EAAE,IAAI,EAAEpJ,KAAK,CAACyD,QAAQ,EAAE,IAAI,CAAC;IAC/D;IAEA,IAAK1I,EAAE,CAACiC,IAAI,IAAI,UAAU,IAAKgM,IAAI,EAAE;MACjC,IAAIlB,MAAM,GAAG,OAAO,GAAGkB,IAAI,GAAG,gCAAgC;MAC9D,IAAIxE,GAAG,GAAGzJ,EAAE,CAACsJ,SAAS,CAAClD,KAAK,CAAC,CAAC;MAE9B,IAAImF,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;MAC9BD,GAAG,CAACE,IAAI,CAAC,MAAM,EAAEhC,GAAG,EAAE,IAAI,CAAC;MAC3B8B,GAAG,CAACG,gBAAgB,CAAC,cAAc,EAAE,mCAAmC,CAAC;MAEzEqB,MAAM,IAAI,aAAa,GAAG7D,kBAAkB,CAAClJ,EAAE,CAAC0J,QAAQ,CAAC;MACzDqD,MAAM,IAAI,gBAAgB,GAAG9H,KAAK,CAAC7C,WAAW;MAE9C,IAAI6C,KAAK,CAAC+E,gBAAgB,EAAE;QACxB+C,MAAM,IAAI,iBAAiB,GAAG9H,KAAK,CAAC+E,gBAAgB;MACxD;MAEAuB,GAAG,CAAC0B,eAAe,GAAG,IAAI;MAE1B1B,GAAG,CAACI,kBAAkB,GAAG,YAAW;QAChC,IAAIJ,GAAG,CAACK,UAAU,IAAI,CAAC,EAAE;UACrB,IAAIL,GAAG,CAACM,MAAM,IAAI,GAAG,EAAE;YAEnB,IAAIsB,aAAa,GAAGvE,IAAI,CAACmD,KAAK,CAACR,GAAG,CAACS,YAAY,CAAC;YAChDsC,WAAW,CAACnB,aAAa,CAAC,cAAc,CAAC,EAAEA,aAAa,CAAC,eAAe,CAAC,EAAEA,aAAa,CAAC,UAAU,CAAC,EAAEnN,EAAE,CAACiC,IAAI,KAAK,UAAU,CAAC;YAC7HwE,mBAAmB,CAAC,CAAC;UACzB,CAAC,MAAM;YACHzG,EAAE,CAAC2G,WAAW,IAAI3G,EAAE,CAAC2G,WAAW,CAAC,CAAC;YAClC7D,OAAO,IAAIA,OAAO,CAACQ,QAAQ,CAAC,CAAC;UACjC;QACJ;MACJ,CAAC;MAEDiI,GAAG,CAACU,IAAI,CAACc,MAAM,CAAC;IACpB;IAEA,SAASuB,WAAWA,CAACC,WAAW,EAAElI,YAAY,EAAEE,OAAO,EAAEiI,cAAc,EAAE;MACrEtB,SAAS,GAAG,CAACA,SAAS,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,IAAI,CAAC;MAElDtG,QAAQ,CAACiI,WAAW,EAAElI,YAAY,EAAEE,OAAO,EAAE2G,SAAS,CAAC;MAEvD,IAAIzM,QAAQ,IAAKT,EAAE,CAACyO,aAAa,IAAIzO,EAAE,CAACyO,aAAa,CAACzF,KAAK,IAAI/D,KAAK,CAACyJ,WAAY,EAAE;QAC/EhO,OAAO,CAAC,0CAA0C,CAAC;QACnDV,EAAE,CAACuN,UAAU,CAAC,CAAC;QACfzK,OAAO,IAAIA,OAAO,CAACQ,QAAQ,CAAC,CAAC;MACjC,CAAC,MAAM;QACH,IAAIkL,cAAc,EAAE;UAChBxO,EAAE,CAACwG,aAAa,IAAIxG,EAAE,CAACwG,aAAa,CAAC,CAAC;UACtC1D,OAAO,IAAIA,OAAO,CAACK,UAAU,CAAC,CAAC;QACnC;MACJ;IACJ;EAEJ;EAEA,SAASK,UAAUA,CAAA,EAAG;IAClB,IAAIV,OAAO,GAAGC,aAAa,CAAC,CAAC;IAC7B,IAAI4L,SAAS;IAEb,IAAI,OAAOhP,MAAM,KAAK,QAAQ,EAAE;MAC5BgP,SAAS,GAAGhP,MAAM;IACtB;IAEA,SAASiP,iBAAiBA,CAACC,iBAAiB,EAAE;MAC1C,IAAI,CAAEA,iBAAiB,EAAE;QACrB7O,EAAE,CAACsJ,SAAS,GAAG;UACXE,SAAS,EAAE,SAAAA,CAAA,EAAW;YAClB,OAAOmB,WAAW,CAAC,CAAC,GAAG,+BAA+B;UAC1D,CAAC;UACDvE,KAAK,EAAE,SAAAA,CAAA,EAAW;YACd,OAAOuE,WAAW,CAAC,CAAC,GAAG,gCAAgC;UAC3D,CAAC;UACDP,MAAM,EAAE,SAAAA,CAAA,EAAW;YACf,OAAOO,WAAW,CAAC,CAAC,GAAG,iCAAiC;UAC5D,CAAC;UACDmE,kBAAkB,EAAE,SAAAA,CAAA,EAAW;YAC3B,OAAOnE,WAAW,CAAC,CAAC,GAAG,mDAAmD;UAC9E,CAAC;UACDoE,uBAAuB,EAAE,SAAAA,CAAA,EAAW;YAChC,OAAOpE,WAAW,CAAC,CAAC,GAAG,gDAAgD;UAC3E,CAAC;UACDpB,QAAQ,EAAE,SAAAA,CAAA,EAAW;YACjB,OAAOoB,WAAW,CAAC,CAAC,GAAG,wCAAwC;UACnE,CAAC;UACDwB,QAAQ,EAAE,SAAAA,CAAA,EAAW;YACjB,OAAOxB,WAAW,CAAC,CAAC,GAAG,mCAAmC;UAC9D;QACJ,CAAC;MACL,CAAC,MAAM;QACH3K,EAAE,CAACsJ,SAAS,GAAG;UACXE,SAAS,EAAE,SAAAA,CAAA,EAAW;YAClB,OAAOqF,iBAAiB,CAACG,sBAAsB;UACnD,CAAC;UACD5I,KAAK,EAAE,SAAAA,CAAA,EAAW;YACd,OAAOyI,iBAAiB,CAACI,cAAc;UAC3C,CAAC;UACD7E,MAAM,EAAE,SAAAA,CAAA,EAAW;YACf,IAAI,CAACyE,iBAAiB,CAACK,oBAAoB,EAAE;cACzC,MAAM,kCAAkC;YAC5C;YACA,OAAOL,iBAAiB,CAACK,oBAAoB;UACjD,CAAC;UACDJ,kBAAkB,EAAE,SAAAA,CAAA,EAAW;YAC3B,IAAI,CAACD,iBAAiB,CAACM,oBAAoB,EAAE;cACzC,MAAM,kCAAkC;YAC5C;YACA,OAAON,iBAAiB,CAACM,oBAAoB;UACjD,CAAC;UACD5F,QAAQ,EAAE,SAAAA,CAAA,EAAW;YACjB,MAAM,yEAAyE;UACnF,CAAC;UACD4C,QAAQ,EAAE,SAAAA,CAAA,EAAW;YACjB,IAAI,CAAC0C,iBAAiB,CAACO,iBAAiB,EAAE;cACtC,MAAM,kCAAkC;YAC5C;YACA,OAAOP,iBAAiB,CAACO,iBAAiB;UAC9C;QACJ,CAAC;MACL;IACJ;IAEA,IAAIT,SAAS,EAAE;MACX,IAAIpD,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;MAC9BD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAEkD,SAAS,EAAE,IAAI,CAAC;MAChCpD,GAAG,CAACG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC;MAElDH,GAAG,CAACI,kBAAkB,GAAG,YAAY;QACjC,IAAIJ,GAAG,CAACK,UAAU,IAAI,CAAC,EAAE;UACrB,IAAIL,GAAG,CAACM,MAAM,IAAI,GAAG,IAAIwD,UAAU,CAAC9D,GAAG,CAAC,EAAE;YACtC,IAAI5L,MAAM,GAAGiJ,IAAI,CAACmD,KAAK,CAACR,GAAG,CAACS,YAAY,CAAC;YAEzChM,EAAE,CAAC2N,aAAa,GAAGhO,MAAM,CAAC,iBAAiB,CAAC;YAC5CK,EAAE,CAAC0K,KAAK,GAAG/K,MAAM,CAAC,OAAO,CAAC;YAC1BK,EAAE,CAAC0J,QAAQ,GAAG/J,MAAM,CAAC,UAAU,CAAC;YAChCiP,iBAAiB,CAAC,IAAI,CAAC;YACvB9L,OAAO,CAACK,UAAU,CAAC,CAAC;UACxB,CAAC,MAAM;YACHL,OAAO,CAACQ,QAAQ,CAAC,CAAC;UACtB;QACJ;MACJ,CAAC;MAEDiI,GAAG,CAACU,IAAI,CAAC,CAAC;IACd,CAAC,MAAM;MACHjM,EAAE,CAAC0J,QAAQ,GAAG/J,MAAM,CAAC+J,QAAQ;MAE7B,IAAI4F,YAAY,GAAG3P,MAAM,CAAC,cAAc,CAAC;MACzC,IAAI,CAAC2P,YAAY,EAAE;QACftP,EAAE,CAAC2N,aAAa,GAAGhO,MAAM,CAAC8J,GAAG;QAC7BzJ,EAAE,CAAC0K,KAAK,GAAG/K,MAAM,CAAC+K,KAAK;QACvBkE,iBAAiB,CAAC,IAAI,CAAC;QACvB9L,OAAO,CAACK,UAAU,CAAC,CAAC;MACxB,CAAC,MAAM;QACH,IAAI,OAAOmM,YAAY,KAAK,QAAQ,EAAE;UAClC,IAAIC,qBAAqB;UACzB,IAAID,YAAY,CAAC1B,MAAM,CAAC0B,YAAY,CAAC5H,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;YACrD6H,qBAAqB,GAAGD,YAAY,GAAG,kCAAkC;UAC7E,CAAC,MAAM;YACHC,qBAAqB,GAAGD,YAAY,GAAG,mCAAmC;UAC9E;UACA,IAAI/D,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;UAC9BD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAE8D,qBAAqB,EAAE,IAAI,CAAC;UAC5ChE,GAAG,CAACG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC;UAElDH,GAAG,CAACI,kBAAkB,GAAG,YAAY;YACjC,IAAIJ,GAAG,CAACK,UAAU,IAAI,CAAC,EAAE;cACrB,IAAIL,GAAG,CAACM,MAAM,IAAI,GAAG,IAAIwD,UAAU,CAAC9D,GAAG,CAAC,EAAE;gBACtC,IAAIiE,kBAAkB,GAAG5G,IAAI,CAACmD,KAAK,CAACR,GAAG,CAACS,YAAY,CAAC;gBACrD4C,iBAAiB,CAACY,kBAAkB,CAAC;gBACrC1M,OAAO,CAACK,UAAU,CAAC,CAAC;cACxB,CAAC,MAAM;gBACHL,OAAO,CAACQ,QAAQ,CAAC,CAAC;cACtB;YACJ;UACJ,CAAC;UAEDiI,GAAG,CAACU,IAAI,CAAC,CAAC;QACd,CAAC,MAAM;UACH2C,iBAAiB,CAACU,YAAY,CAAC;UAC/BxM,OAAO,CAACK,UAAU,CAAC,CAAC;QACxB;MACJ;IACJ;IAEA,OAAOL,OAAO,CAACA,OAAO;EAC1B;EAEA,SAASuM,UAAUA,CAACI,GAAG,EAAE;IACrB,OAAOA,GAAG,CAAC5D,MAAM,IAAI,CAAC,IAAI4D,GAAG,CAACzD,YAAY,IAAIyD,GAAG,CAACC,WAAW,CAACC,UAAU,CAAC,OAAO,CAAC;EACrF;EAEA,SAASrJ,QAAQA,CAACF,KAAK,EAAEC,YAAY,EAAEE,OAAO,EAAE2G,SAAS,EAAE;IACvD,IAAIlN,EAAE,CAAC4P,kBAAkB,EAAE;MACvBC,YAAY,CAAC7P,EAAE,CAAC4P,kBAAkB,CAAC;MACnC5P,EAAE,CAAC4P,kBAAkB,GAAG,IAAI;IAChC;IAEA,IAAIvJ,YAAY,EAAE;MACdrG,EAAE,CAACqG,YAAY,GAAGA,YAAY;MAC9BrG,EAAE,CAAC8P,kBAAkB,GAAGC,WAAW,CAAC1J,YAAY,CAAC;IACrD,CAAC,MAAM;MACH,OAAOrG,EAAE,CAACqG,YAAY;MACtB,OAAOrG,EAAE,CAAC8P,kBAAkB;IAChC;IAEA,IAAIvJ,OAAO,EAAE;MACTvG,EAAE,CAACuG,OAAO,GAAGA,OAAO;MACpBvG,EAAE,CAACyO,aAAa,GAAGsB,WAAW,CAACxJ,OAAO,CAAC;IAC3C,CAAC,MAAM;MACH,OAAOvG,EAAE,CAACuG,OAAO;MACjB,OAAOvG,EAAE,CAACyO,aAAa;IAC3B;IAEA,IAAIrI,KAAK,EAAE;MACPpG,EAAE,CAACoG,KAAK,GAAGA,KAAK;MAChBpG,EAAE,CAACuM,WAAW,GAAGwD,WAAW,CAAC3J,KAAK,CAAC;MACnCpG,EAAE,CAACgQ,SAAS,GAAGhQ,EAAE,CAACuM,WAAW,CAAC0D,GAAG;MACjCjQ,EAAE,CAACoB,aAAa,GAAG,IAAI;MACvBpB,EAAE,CAACkQ,OAAO,GAAGlQ,EAAE,CAACuM,WAAW,CAAC4D,GAAG;MAC/BnQ,EAAE,CAACiL,WAAW,GAAGjL,EAAE,CAACuM,WAAW,CAAC6D,YAAY;MAC5CpQ,EAAE,CAACqL,cAAc,GAAGrL,EAAE,CAACuM,WAAW,CAAC8D,eAAe;MAElD,IAAInD,SAAS,EAAE;QACXlN,EAAE,CAACmC,QAAQ,GAAGsK,IAAI,CAAC6D,KAAK,CAACpD,SAAS,GAAG,IAAI,CAAC,GAAGlN,EAAE,CAACuM,WAAW,CAACgE,GAAG;MACnE;MAEA,IAAIvQ,EAAE,CAACmC,QAAQ,IAAI,IAAI,EAAE;QACrBzB,OAAO,CAAC,qEAAqE,GAAGV,EAAE,CAACmC,QAAQ,GAAG,UAAU,CAAC;QAEzG,IAAInC,EAAE,CAACwQ,cAAc,EAAE;UACnB,IAAIhE,SAAS,GAAG,CAACxM,EAAE,CAACuM,WAAW,CAAC,KAAK,CAAC,GAAI,IAAII,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAK,GAAG5M,EAAE,CAACmC,QAAQ,IAAI,IAAI;UAC5FzB,OAAO,CAAC,8BAA8B,GAAG+L,IAAI,CAACgE,KAAK,CAACjE,SAAS,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;UAC7E,IAAIA,SAAS,IAAI,CAAC,EAAE;YAChBxM,EAAE,CAACwQ,cAAc,CAAC,CAAC;UACvB,CAAC,MAAM;YACHxQ,EAAE,CAAC4P,kBAAkB,GAAGc,UAAU,CAAC1Q,EAAE,CAACwQ,cAAc,EAAEhE,SAAS,CAAC;UACpE;QACJ;MACJ;IACJ,CAAC,MAAM;MACH,OAAOxM,EAAE,CAACoG,KAAK;MACf,OAAOpG,EAAE,CAACuM,WAAW;MACrB,OAAOvM,EAAE,CAACkQ,OAAO;MACjB,OAAOlQ,EAAE,CAACiL,WAAW;MACrB,OAAOjL,EAAE,CAACqL,cAAc;MAExBrL,EAAE,CAACoB,aAAa,GAAG,KAAK;IAC5B;EACJ;EAEA,SAAS2H,UAAUA,CAAA,EAAG;IAClB,IAAI,OAAOhC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAAC4J,UAAU,KAAK,WAAW,EAAE;MAC3E,MAAM,IAAI/Q,KAAK,CAAC,kCAAkC,CAAC;IACvD;IAEA,OAAOmH,MAAM,CAAC4J,UAAU,CAAC,CAAC;EAC9B;EAEA,SAASzL,aAAaA,CAACuE,GAAG,EAAE;IACxB,IAAIxE,KAAK,GAAG2L,gBAAgB,CAACnH,GAAG,CAAC;IACjC,IAAI,CAACxE,KAAK,EAAE;MACR;IACJ;IAEA,IAAI4L,UAAU,GAAG1Q,eAAe,CAAC2Q,GAAG,CAAC7L,KAAK,CAACgB,KAAK,CAAC;IAEjD,IAAI4K,UAAU,EAAE;MACZ5L,KAAK,CAACkB,KAAK,GAAG,IAAI;MAClBlB,KAAK,CAAC7C,WAAW,GAAGyO,UAAU,CAACzO,WAAW;MAC1C6C,KAAK,CAACyJ,WAAW,GAAGmC,UAAU,CAAC7H,KAAK;MACpC/D,KAAK,CAACvB,MAAM,GAAGmN,UAAU,CAACnN,MAAM;MAChCuB,KAAK,CAAC+E,gBAAgB,GAAG6G,UAAU,CAAC7G,gBAAgB;MACpD/E,KAAK,CAACkE,YAAY,GAAG0H,UAAU,CAAC1H,YAAY;IAChD;IAEA,OAAOlE,KAAK;EAChB;EAEA,SAAS2L,gBAAgBA,CAACnH,GAAG,EAAE;IAC3B,IAAIsH,eAAe;IACnB,QAAQ/Q,EAAE,CAACiC,IAAI;MACX,KAAK,UAAU;QACX8O,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,kBAAkB,EAAE,WAAW,EAAE,KAAK,CAAC;QAC5F;MACJ,KAAK,UAAU;QACXA,eAAe,GAAG,CAAC,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,kBAAkB,EAAE,WAAW,EAAE,KAAK,CAAC;QAC5I;MACJ,KAAK,QAAQ;QACTA,eAAe,GAAG,CAAC,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,kBAAkB,EAAE,WAAW,EAAE,KAAK,CAAC;QACpJ;IACR;IAEAA,eAAe,CAAC/D,IAAI,CAAC,OAAO,CAAC;IAC7B+D,eAAe,CAAC/D,IAAI,CAAC,mBAAmB,CAAC;IACzC+D,eAAe,CAAC/D,IAAI,CAAC,WAAW,CAAC;IAEjC,IAAIgE,UAAU,GAAGvH,GAAG,CAAClI,OAAO,CAAC,GAAG,CAAC;IACjC,IAAI0P,aAAa,GAAGxH,GAAG,CAAClI,OAAO,CAAC,GAAG,CAAC;IAEpC,IAAI2E,MAAM;IACV,IAAIgL,MAAM;IAEV,IAAIlR,EAAE,CAACgC,YAAY,KAAK,OAAO,IAAIgP,UAAU,KAAK,CAAC,CAAC,EAAE;MAClD9K,MAAM,GAAGuD,GAAG,CAAC0H,SAAS,CAAC,CAAC,EAAEH,UAAU,CAAC;MACrCE,MAAM,GAAGE,mBAAmB,CAAC3H,GAAG,CAAC0H,SAAS,CAACH,UAAU,GAAG,CAAC,EAAEC,aAAa,KAAK,CAAC,CAAC,GAAGA,aAAa,GAAGxH,GAAG,CAAC/B,MAAM,CAAC,EAAEqJ,eAAe,CAAC;MAC/H,IAAIG,MAAM,CAACG,YAAY,KAAK,EAAE,EAAE;QAC5BnL,MAAM,IAAI,GAAG,GAAGgL,MAAM,CAACG,YAAY;MACvC;MACA,IAAIJ,aAAa,KAAK,CAAC,CAAC,EAAE;QACtB/K,MAAM,IAAIuD,GAAG,CAAC0H,SAAS,CAACF,aAAa,CAAC;MAC1C;IACJ,CAAC,MAAM,IAAIjR,EAAE,CAACgC,YAAY,KAAK,UAAU,IAAIiP,aAAa,KAAK,CAAC,CAAC,EAAE;MAC/D/K,MAAM,GAAGuD,GAAG,CAAC0H,SAAS,CAAC,CAAC,EAAEF,aAAa,CAAC;MACxCC,MAAM,GAAGE,mBAAmB,CAAC3H,GAAG,CAAC0H,SAAS,CAACF,aAAa,GAAG,CAAC,CAAC,EAAEF,eAAe,CAAC;MAC/E,IAAIG,MAAM,CAACG,YAAY,KAAK,EAAE,EAAE;QAC5BnL,MAAM,IAAI,GAAG,GAAGgL,MAAM,CAACG,YAAY;MACvC;IACJ;IAEA,IAAIH,MAAM,IAAIA,MAAM,CAACI,WAAW,EAAE;MAC9B,IAAItR,EAAE,CAACiC,IAAI,KAAK,UAAU,IAAIjC,EAAE,CAACiC,IAAI,KAAK,QAAQ,EAAE;QAChD,IAAI,CAACiP,MAAM,CAACI,WAAW,CAACrD,IAAI,IAAIiD,MAAM,CAACI,WAAW,CAACjO,KAAK,KAAK6N,MAAM,CAACI,WAAW,CAACrL,KAAK,EAAE;UACnFiL,MAAM,CAACI,WAAW,CAACpL,MAAM,GAAGA,MAAM;UAClC,OAAOgL,MAAM,CAACI,WAAW;QAC7B;MACJ,CAAC,MAAM,IAAItR,EAAE,CAACiC,IAAI,KAAK,UAAU,EAAE;QAC/B,IAAI,CAACiP,MAAM,CAACI,WAAW,CAACjD,YAAY,IAAI6C,MAAM,CAACI,WAAW,CAACjO,KAAK,KAAK6N,MAAM,CAACI,WAAW,CAACrL,KAAK,EAAE;UAC3FiL,MAAM,CAACI,WAAW,CAACpL,MAAM,GAAGA,MAAM;UAClC,OAAOgL,MAAM,CAACI,WAAW;QAC7B;MACJ;IACJ;EACJ;EAEA,SAASF,mBAAmBA,CAACC,YAAY,EAAEN,eAAe,EAAE;IACxD,IAAI1D,CAAC,GAAGgE,YAAY,CAACE,KAAK,CAAC,GAAG,CAAC;IAC/B,IAAIC,MAAM,GAAG;MACTH,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,CAAC;IAClB,CAAC;IACD,KAAK,IAAI9J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,CAAC,CAAC3F,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC/B,IAAI+J,KAAK,GAAGlE,CAAC,CAAC7F,CAAC,CAAC,CAACjG,OAAO,CAAC,GAAG,CAAC;MAC7B,IAAIkQ,GAAG,GAAGpE,CAAC,CAAC7F,CAAC,CAAC,CAACkK,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC;MAC9B,IAAIR,eAAe,CAACxP,OAAO,CAACkQ,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACrCD,MAAM,CAACF,WAAW,CAACG,GAAG,CAAC,GAAGpE,CAAC,CAAC7F,CAAC,CAAC,CAACkK,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACH,IAAIC,MAAM,CAACH,YAAY,KAAK,EAAE,EAAE;UAC5BG,MAAM,CAACH,YAAY,IAAI,GAAG;QAC9B;QACAG,MAAM,CAACH,YAAY,IAAIhE,CAAC,CAAC7F,CAAC,CAAC;MAC/B;IACJ;IACA,OAAOgK,MAAM;EACjB;EAEA,SAASzO,aAAaA,CAAA,EAAG;IACrB;IACA;IACA,IAAIsK,CAAC,GAAG;MACJlK,UAAU,EAAE,SAAAA,CAASqO,MAAM,EAAE;QACzBnE,CAAC,CAACsE,OAAO,CAACH,MAAM,CAAC;MACrB,CAAC;MAEDlO,QAAQ,EAAE,SAAAA,CAASkO,MAAM,EAAE;QACvBnE,CAAC,CAACuE,MAAM,CAACJ,MAAM,CAAC;MACpB;IACJ,CAAC;IACDnE,CAAC,CAACvK,OAAO,GAAG,IAAI+O,OAAO,CAAC,UAASF,OAAO,EAAEC,MAAM,EAAE;MAC9CvE,CAAC,CAACsE,OAAO,GAAGA,OAAO;MACnBtE,CAAC,CAACuE,MAAM,GAAGA,MAAM;IACrB,CAAC,CAAC;IAEF,OAAOvE,CAAC;EACZ;;EAEA;EACA,SAASyE,qBAAqBA,CAAChP,OAAO,EAAEiP,OAAO,EAAEC,YAAY,EAAE;IAC3D,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAIC,cAAc,GAAG,IAAIL,OAAO,CAAC,UAAUF,OAAO,EAAEC,MAAM,EAAE;MACxDK,aAAa,GAAGvB,UAAU,CAAC,YAAY;QACnCkB,MAAM,CAAC;UAAE,OAAO,EAAEI,YAAY,IAAI,2CAA2C,GAAGD,OAAO,GAAG;QAAK,CAAC,CAAC;MACrG,CAAC,EAAEA,OAAO,CAAC;IACf,CAAC,CAAC;IAEF,OAAOF,OAAO,CAACM,IAAI,CAAC,CAACrP,OAAO,EAAEoP,cAAc,CAAC,CAAC,CAACE,OAAO,CAAC,YAAY;MAC/DvC,YAAY,CAACoC,aAAa,CAAC;IAC/B,CAAC,CAAC;EACN;EAEA,SAASvM,qBAAqBA,CAAA,EAAG;IAC7B,IAAI5C,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7B,IAAI,CAAC3C,WAAW,CAACC,MAAM,EAAE;MACrByC,OAAO,CAACK,UAAU,CAAC,CAAC;MACpB,OAAOL,OAAO,CAACA,OAAO;IAC1B;IAEA,IAAI1C,WAAW,CAACiS,MAAM,EAAE;MACpBvP,OAAO,CAACK,UAAU,CAAC,CAAC;MACpB,OAAOL,OAAO,CAACA,OAAO;IAC1B;IAEA,IAAIuP,MAAM,GAAGnO,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC7C/D,WAAW,CAACiS,MAAM,GAAGA,MAAM;IAE3BA,MAAM,CAACC,MAAM,GAAG,YAAW;MACvB,IAAIC,OAAO,GAAGvS,EAAE,CAACsJ,SAAS,CAACE,SAAS,CAAC,CAAC;MACtC,IAAI+I,OAAO,CAAC3E,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC3BxN,WAAW,CAACoS,YAAY,GAAG3E,SAAS,CAAC,CAAC;MAC1C,CAAC,MAAM;QACHzN,WAAW,CAACoS,YAAY,GAAGD,OAAO,CAACpB,SAAS,CAAC,CAAC,EAAEoB,OAAO,CAAChR,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;MAC5E;MACAuB,OAAO,CAACK,UAAU,CAAC,CAAC;IACxB,CAAC;IAED,IAAIiB,GAAG,GAAGpE,EAAE,CAACsJ,SAAS,CAACwF,kBAAkB,CAAC,CAAC;IAC3CuD,MAAM,CAAC/N,YAAY,CAAC,KAAK,EAAEF,GAAI,CAAC;IAChCiO,MAAM,CAAC/N,YAAY,CAAC,SAAS,EAAE,yEAAyE,CAAC;IACzG+N,MAAM,CAAC/N,YAAY,CAAC,OAAO,EAAE,yBAA0B,CAAC;IACxD+N,MAAM,CAAC9N,KAAK,CAACC,OAAO,GAAG,MAAM;IAC7BN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAAC2N,MAAM,CAAC;IAEjC,IAAI1N,eAAe,GAAG,SAAAA,CAASC,KAAK,EAAE;MAClC,IAAKA,KAAK,CAACC,MAAM,KAAKzE,WAAW,CAACoS,YAAY,IAAMpS,WAAW,CAACiS,MAAM,CAACtN,aAAa,KAAKH,KAAK,CAACI,MAAO,EAAE;QACpG;MACJ;MAEA,IAAI,EAAEJ,KAAK,CAACO,IAAI,IAAI,WAAW,IAAIP,KAAK,CAACO,IAAI,IAAI,SAAS,IAAIP,KAAK,CAACO,IAAI,IAAI,OAAO,CAAC,EAAE;QAClF;MACJ;MAGA,IAAIP,KAAK,CAACO,IAAI,IAAI,WAAW,EAAE;QAC3BnF,EAAE,CAACuN,UAAU,CAAC,CAAC;MACnB;MAEA,IAAIkF,SAAS,GAAGrS,WAAW,CAACE,YAAY,CAACoS,MAAM,CAAC,CAAC,EAAEtS,WAAW,CAACE,YAAY,CAACoH,MAAM,CAAC;MAEnF,KAAK,IAAIF,CAAC,GAAGiL,SAAS,CAAC/K,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QAC5C,IAAI1E,OAAO,GAAG2P,SAAS,CAACjL,CAAC,CAAC;QAC1B,IAAI5C,KAAK,CAACO,IAAI,IAAI,OAAO,EAAE;UACvBrC,OAAO,CAACQ,QAAQ,CAAC,CAAC;QACtB,CAAC,MAAM;UACHR,OAAO,CAACK,UAAU,CAACyB,KAAK,CAACO,IAAI,IAAI,WAAW,CAAC;QACjD;MACJ;IACJ,CAAC;IAED1D,MAAM,CAAC8D,gBAAgB,CAAC,SAAS,EAAEZ,eAAe,EAAE,KAAK,CAAC;IAE1D,OAAO7B,OAAO,CAACA,OAAO;EAC1B;EAEA,SAAS2D,mBAAmBA,CAAA,EAAG;IAC3B,IAAIrG,WAAW,CAACC,MAAM,EAAE;MACpB,IAAIL,EAAE,CAACoG,KAAK,EAAE;QACVsK,UAAU,CAAC,YAAW;UAClB9O,gBAAgB,CAAC,CAAC,CAACqB,IAAI,CAAC,UAAS0C,SAAS,EAAE;YACxC,IAAIA,SAAS,EAAE;cACXc,mBAAmB,CAAC,CAAC;YACzB;UACJ,CAAC,CAAC;QACN,CAAC,EAAErG,WAAW,CAACG,QAAQ,GAAG,IAAI,CAAC;MACnC;IACJ;EACJ;EAEA,SAASqB,gBAAgBA,CAAA,EAAG;IACxB,IAAIkB,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7B,IAAI3C,WAAW,CAACiS,MAAM,IAAIjS,WAAW,CAACoS,YAAY,EAAG;MACjD,IAAIG,GAAG,GAAG3S,EAAE,CAAC0J,QAAQ,GAAG,GAAG,IAAI1J,EAAE,CAACgQ,SAAS,GAAGhQ,EAAE,CAACgQ,SAAS,GAAG,EAAE,CAAC;MAChE5P,WAAW,CAACE,YAAY,CAAC0M,IAAI,CAAClK,OAAO,CAAC;MACtC,IAAI+B,MAAM,GAAGzE,WAAW,CAACoS,YAAY;MACrC,IAAIpS,WAAW,CAACE,YAAY,CAACoH,MAAM,IAAI,CAAC,EAAE;QACtCtH,WAAW,CAACiS,MAAM,CAACtN,aAAa,CAAC6N,WAAW,CAACD,GAAG,EAAE9N,MAAM,CAAC;MAC7D;IACJ,CAAC,MAAM;MACH/B,OAAO,CAACK,UAAU,CAAC,CAAC;IACxB;IAEA,OAAOL,OAAO,CAACA,OAAO;EAC1B;EAEA,SAAS8D,uBAAuBA,CAAA,EAAG;IAC/B,IAAI9D,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7B,IAAI,CAAC3C,WAAW,CAACC,MAAM,IAAIL,EAAE,CAACqC,yBAAyB,KAAK,OAAOrC,EAAE,CAACsJ,SAAS,CAACyF,uBAAuB,KAAK,UAAU,EAAE;MACpH,IAAIsD,MAAM,GAAGnO,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC7CkO,MAAM,CAAC/N,YAAY,CAAC,KAAK,EAAEtE,EAAE,CAACsJ,SAAS,CAACyF,uBAAuB,CAAC,CAAC,CAAC;MAClEsD,MAAM,CAAC/N,YAAY,CAAC,SAAS,EAAE,yEAAyE,CAAC;MACzG+N,MAAM,CAAC/N,YAAY,CAAC,OAAO,EAAE,0BAA2B,CAAC;MACzD+N,MAAM,CAAC9N,KAAK,CAACC,OAAO,GAAG,MAAM;MAC7BN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAAC2N,MAAM,CAAC;MAEjC,IAAI1N,eAAe,GAAG,SAAAA,CAASC,KAAK,EAAE;QAClC,IAAIyN,MAAM,CAACtN,aAAa,KAAKH,KAAK,CAACI,MAAM,EAAE;UACvC;QACJ;QAEA,IAAIJ,KAAK,CAACO,IAAI,KAAK,WAAW,IAAIP,KAAK,CAACO,IAAI,KAAK,aAAa,EAAE;UAC5D;QACJ,CAAC,MAAM,IAAIP,KAAK,CAACO,IAAI,KAAK,aAAa,EAAE;UACrCrE,OAAO,CACH,kFAAkF,GAClF,sIAAsI,GACtI,gIAAgI,GAChI,sGACJ,CAAC;UAEDV,WAAW,CAACC,MAAM,GAAG,KAAK;UAC1B,IAAIL,EAAE,CAACsC,sBAAsB,EAAE;YAC3BtC,EAAE,CAACqC,yBAAyB,GAAG,KAAK;UACxC;QACJ;QAEA6B,QAAQ,CAACO,IAAI,CAACY,WAAW,CAACgN,MAAM,CAAC;QACjC5Q,MAAM,CAAC6D,mBAAmB,CAAC,SAAS,EAAEX,eAAe,CAAC;QACtD7B,OAAO,CAACK,UAAU,CAAC,CAAC;MACxB,CAAC;MAED1B,MAAM,CAAC8D,gBAAgB,CAAC,SAAS,EAAEZ,eAAe,EAAE,KAAK,CAAC;IAC9D,CAAC,MAAM;MACH7B,OAAO,CAACK,UAAU,CAAC,CAAC;IACxB;IAEA,OAAO2O,qBAAqB,CAAChP,OAAO,CAACA,OAAO,EAAE9C,EAAE,CAAC6C,qBAAqB,EAAE,0DAA0D,CAAC;EACvI;EAEA,SAASrB,WAAWA,CAACqR,IAAI,EAAE;IACvB,IAAI,CAACA,IAAI,IAAIA,IAAI,IAAI,SAAS,EAAE;MAC5B,OAAO;QACHhP,KAAK;UAAA,IAAAiP,KAAA,GAAA9O,iBAAA,CAAE,WAAeL,OAAO,EAAE;YAC3BlC,MAAM,CAACqD,QAAQ,CAACiO,MAAM,OAAO/S,EAAE,CAACqE,cAAc,CAACV,OAAO,CAAC,CAAC;YACxD,OAAOZ,aAAa,CAAC,CAAC,CAACD,OAAO;UAClC,CAAC;UAAA,gBAHDe,KAAKA,CAAAmP,GAAA;YAAA,OAAAF,KAAA,CAAAtN,KAAA,OAAAC,SAAA;UAAA;QAAA,GAGJ;QAED2E,MAAM;UAAA,IAAA6I,KAAA,GAAAjP,iBAAA,CAAE,WAAeL,OAAO,EAAE;YAE5B,MAAMjB,YAAY,GAAGiB,OAAO,EAAEjB,YAAY,IAAI1C,EAAE,CAAC0C,YAAY;YAC7D,IAAIA,YAAY,KAAK,KAAK,EAAE;cACxBjB,MAAM,CAACqD,QAAQ,CAACwD,OAAO,CAACtI,EAAE,CAACqK,eAAe,CAAC1G,OAAO,CAAC,CAAC;cACpD;YACJ;;YAEA;YACA,MAAMuP,IAAI,GAAGhP,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;YAE3C+O,IAAI,CAAC5O,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;YACnC4O,IAAI,CAAC5O,YAAY,CAAC,QAAQ,EAAEtE,EAAE,CAACqK,eAAe,CAAC1G,OAAO,CAAC,CAAC;YACxDuP,IAAI,CAAC3O,KAAK,CAACC,OAAO,GAAG,MAAM;;YAE3B;YACA,MAAMW,IAAI,GAAG;cACTgO,aAAa,EAAEnT,EAAE,CAACuG,OAAO;cACzB6M,SAAS,EAAEpT,EAAE,CAAC0J,QAAQ;cACtB2J,wBAAwB,EAAEpT,OAAO,CAACmC,WAAW,CAACuB,OAAO,EAAE,KAAK;YAChE,CAAC;YAED,KAAK,MAAM,CAAC2P,IAAI,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACtO,IAAI,CAAC,EAAE;cAC9C,MAAMuO,KAAK,GAAGxP,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;cAE7CuP,KAAK,CAACpP,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;cACpCoP,KAAK,CAACpP,YAAY,CAAC,MAAM,EAAEgP,IAAI,CAAC;cAChCI,KAAK,CAACpP,YAAY,CAAC,OAAO,EAAEiP,KAAK,CAAC;cAElCL,IAAI,CAACxO,WAAW,CAACgP,KAAK,CAAC;YAC3B;;YAEA;YACAxP,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACwO,IAAI,CAAC;YAC/BA,IAAI,CAACS,MAAM,CAAC,CAAC;UACjB,CAAC;UAAA,gBAnCDvJ,MAAMA,CAAAwJ,GAAA;YAAA,OAAAX,KAAA,CAAAzN,KAAA,OAAAC,SAAA;UAAA;QAAA,GAmCL;QAED8D,QAAQ;UAAA,IAAAsK,KAAA,GAAA7P,iBAAA,CAAE,WAAeL,OAAO,EAAE;YAC9BlC,MAAM,CAACqD,QAAQ,CAACiO,MAAM,OAAO/S,EAAE,CAACsK,iBAAiB,CAAC3G,OAAO,CAAC,CAAC;YAC3D,OAAOZ,aAAa,CAAC,CAAC,CAACD,OAAO;UAClC,CAAC;UAAA,gBAHDyG,QAAQA,CAAAuK,GAAA;YAAA,OAAAD,KAAA,CAAArO,KAAA,OAAAC,SAAA;UAAA;QAAA,GAGP;QAEDoF,iBAAiB,EAAG,SAAAA,CAAA,EAAW;UAC3B,IAAIkJ,UAAU,GAAG/T,EAAE,CAACyK,gBAAgB,CAAC,CAAC;UACtC,IAAI,OAAOsJ,UAAU,KAAK,WAAW,EAAE;YACnCtS,MAAM,CAACqD,QAAQ,CAACgB,IAAI,GAAGiO,UAAU;UACrC,CAAC,MAAM;YACH,MAAM,kCAAkC;UAC5C;UACA,OAAOhR,aAAa,CAAC,CAAC,CAACD,OAAO;QAClC,CAAC;QAEDV,WAAW,EAAE,SAAAA,CAASuB,OAAO,EAAEqQ,UAAU,EAAE;UACvC,IAAIvO,SAAS,CAACiC,MAAM,IAAI,CAAC,EAAE;YACvBsM,UAAU,GAAG,IAAI;UACrB;UAEA,IAAIrQ,OAAO,IAAIA,OAAO,CAACvB,WAAW,EAAE;YAChC,OAAOuB,OAAO,CAACvB,WAAW;UAC9B,CAAC,MAAM,IAAIpC,EAAE,CAACoC,WAAW,EAAE;YACvB,OAAOpC,EAAE,CAACoC,WAAW;UACzB,CAAC,MAAM;YACH,OAAO0C,QAAQ,CAACgB,IAAI;UACxB;QACJ;MACJ,CAAC;IACL;IAEA,IAAI+M,IAAI,IAAI,SAAS,EAAE;MACnBzS,WAAW,CAACC,MAAM,GAAG,KAAK;MAC1B,IAAI4T,wBAAwB,GAAG,SAAAA,CAASC,QAAQ,EAAEC,MAAM,EAAExQ,OAAO,EAAE;QAC/D,IAAIlC,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACE,OAAO,CAACyS,YAAY,EAAE;UAC/C;UACA,OAAO3S,MAAM,CAACE,OAAO,CAACyS,YAAY,CAAC3I,IAAI,CAACyI,QAAQ,EAAEC,MAAM,EAAExQ,OAAO,CAAC;QACtE,CAAC,MAAM;UACH,OAAOlC,MAAM,CAACgK,IAAI,CAACyI,QAAQ,EAAEC,MAAM,EAAExQ,OAAO,CAAC;QACjD;MACJ,CAAC;MAED,IAAI0Q,0BAA0B,GAAG,SAAAA,CAAUC,WAAW,EAAE;QACpD,IAAIA,WAAW,IAAIA,WAAW,CAACC,cAAc,EAAE;UAC3C,OAAOf,MAAM,CAACgB,IAAI,CAACF,WAAW,CAACC,cAAc,CAAC,CAACE,MAAM,CAAC,UAAU9Q,OAAO,EAAE+Q,UAAU,EAAE;YACjF/Q,OAAO,CAAC+Q,UAAU,CAAC,GAAGJ,WAAW,CAACC,cAAc,CAACG,UAAU,CAAC;YAC5D,OAAO/Q,OAAO;UAClB,CAAC,EAAE,CAAC,CAAC,CAAC;QACV,CAAC,MAAM;UACH,OAAO,CAAC,CAAC;QACb;MACJ,CAAC;MAED,IAAIgR,oBAAoB,GAAG,SAAAA,CAAUJ,cAAc,EAAE;QACjD,OAAOf,MAAM,CAACgB,IAAI,CAACD,cAAc,CAAC,CAACE,MAAM,CAAC,UAAU9Q,OAAO,EAAE+Q,UAAU,EAAE;UACrE/Q,OAAO,CAACqJ,IAAI,CAAC0H,UAAU,GAAC,GAAG,GAACH,cAAc,CAACG,UAAU,CAAC,CAAC;UACvD,OAAO/Q,OAAO;QAClB,CAAC,EAAE,EAAE,CAAC,CAACiR,IAAI,CAAC,GAAG,CAAC;MACpB,CAAC;MAED,IAAIC,oBAAoB,GAAG,SAAAA,CAAUP,WAAW,EAAE;QAC9C,IAAIC,cAAc,GAAGF,0BAA0B,CAACC,WAAW,CAAC;QAC5DC,cAAc,CAACzP,QAAQ,GAAG,IAAI;QAC9B,IAAIwP,WAAW,IAAIA,WAAW,CAAC5Q,MAAM,IAAI,MAAM,EAAE;UAC7C6Q,cAAc,CAACO,MAAM,GAAG,KAAK;QACjC;QACA,OAAOH,oBAAoB,CAACJ,cAAc,CAAC;MAC/C,CAAC;MAED,IAAIQ,qBAAqB,GAAG,SAAAA,CAAA,EAAW;QACnC,OAAO/U,EAAE,CAACoC,WAAW,IAAI,kBAAkB;MAC/C,CAAC;MAED,OAAO;QACHyB,KAAK;UAAA,IAAAmR,KAAA,GAAAhR,iBAAA,CAAE,WAAeL,OAAO,EAAE;YAC3B,IAAIb,OAAO,GAAGC,aAAa,CAAC,CAAC;YAE7B,IAAIwR,cAAc,GAAGM,oBAAoB,CAAClR,OAAO,CAAC;YAClD,IAAIuQ,QAAQ,SAASlU,EAAE,CAACqE,cAAc,CAACV,OAAO,CAAC;YAC/C,IAAIsR,GAAG,GAAGhB,wBAAwB,CAACC,QAAQ,EAAE,QAAQ,EAAEK,cAAc,CAAC;YACtE,IAAIW,SAAS,GAAG,KAAK;YAErB,IAAIC,MAAM,GAAG,KAAK;YAClB,IAAIC,YAAY,GAAG,SAAAA,CAAA,EAAW;cAC1BD,MAAM,GAAG,IAAI;cACbF,GAAG,CAACI,KAAK,CAAC,CAAC;YACf,CAAC;YAEDJ,GAAG,CAAC1P,gBAAgB,CAAC,WAAW,EAAE,UAASX,KAAK,EAAE;cAC9C,IAAIA,KAAK,CAAC6E,GAAG,CAAClI,OAAO,CAACwT,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBACjD,IAAIlP,QAAQ,GAAGX,aAAa,CAACN,KAAK,CAAC6E,GAAG,CAAC;gBACvCrE,eAAe,CAACS,QAAQ,EAAE/C,OAAO,CAAC;gBAClCsS,YAAY,CAAC,CAAC;gBACdF,SAAS,GAAG,IAAI;cACpB;YACJ,CAAC,CAAC;YAEFD,GAAG,CAAC1P,gBAAgB,CAAC,WAAW,EAAE,UAASX,KAAK,EAAE;cAC9C,IAAI,CAACsQ,SAAS,EAAE;gBACZ,IAAItQ,KAAK,CAAC6E,GAAG,CAAClI,OAAO,CAACwT,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;kBACjD,IAAIlP,QAAQ,GAAGX,aAAa,CAACN,KAAK,CAAC6E,GAAG,CAAC;kBACvCrE,eAAe,CAACS,QAAQ,EAAE/C,OAAO,CAAC;kBAClCsS,YAAY,CAAC,CAAC;kBACdF,SAAS,GAAG,IAAI;gBACpB,CAAC,MAAM;kBACHpS,OAAO,CAACQ,QAAQ,CAAC,CAAC;kBAClB8R,YAAY,CAAC,CAAC;gBAClB;cACJ;YACJ,CAAC,CAAC;YAEFH,GAAG,CAAC1P,gBAAgB,CAAC,MAAM,EAAE,UAASX,KAAK,EAAE;cACzC,IAAI,CAACuQ,MAAM,EAAE;gBACTrS,OAAO,CAACQ,QAAQ,CAAC;kBACbgS,MAAM,EAAE;gBACZ,CAAC,CAAC;cACN;YACJ,CAAC,CAAC;YAEF,OAAOxS,OAAO,CAACA,OAAO;UAC1B,CAAC;UAAA,gBA9CDe,KAAKA,CAAA0R,GAAA;YAAA,OAAAP,KAAA,CAAAxP,KAAA,OAAAC,SAAA;UAAA;QAAA,GA8CJ;QAED2E,MAAM,EAAE,SAAAA,CAASzG,OAAO,EAAE;UACtB,IAAIb,OAAO,GAAGC,aAAa,CAAC,CAAC;UAE7B,IAAIyS,SAAS,GAAGxV,EAAE,CAACqK,eAAe,CAAC1G,OAAO,CAAC;UAC3C,IAAIsR,GAAG,GAAGhB,wBAAwB,CAACuB,SAAS,EAAE,QAAQ,EAAE,uCAAuC,CAAC;UAEhG,IAAInS,KAAK;UAET4R,GAAG,CAAC1P,gBAAgB,CAAC,WAAW,EAAE,UAASX,KAAK,EAAE;YAC9C,IAAIA,KAAK,CAAC6E,GAAG,CAAClI,OAAO,CAACwT,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;cACjDE,GAAG,CAACI,KAAK,CAAC,CAAC;YACf;UACJ,CAAC,CAAC;UAEFJ,GAAG,CAAC1P,gBAAgB,CAAC,WAAW,EAAE,UAASX,KAAK,EAAE;YAC9C,IAAIA,KAAK,CAAC6E,GAAG,CAAClI,OAAO,CAACwT,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;cACjDE,GAAG,CAACI,KAAK,CAAC,CAAC;YACf,CAAC,MAAM;cACHhS,KAAK,GAAG,IAAI;cACZ4R,GAAG,CAACI,KAAK,CAAC,CAAC;YACf;UACJ,CAAC,CAAC;UAEFJ,GAAG,CAAC1P,gBAAgB,CAAC,MAAM,EAAE,UAASX,KAAK,EAAE;YACzC,IAAIvB,KAAK,EAAE;cACPP,OAAO,CAACQ,QAAQ,CAAC,CAAC;YACtB,CAAC,MAAM;cACHtD,EAAE,CAACuN,UAAU,CAAC,CAAC;cACfzK,OAAO,CAACK,UAAU,CAAC,CAAC;YACxB;UACJ,CAAC,CAAC;UAEF,OAAOL,OAAO,CAACA,OAAO;QAC1B,CAAC;QAEDyG,QAAQ;UAAA,IAAAkM,KAAA,GAAAzR,iBAAA,CAAG,WAAeL,OAAO,EAAE;YAC/B,IAAIb,OAAO,GAAGC,aAAa,CAAC,CAAC;YAC7B,IAAI2S,WAAW,SAAS1V,EAAE,CAACsK,iBAAiB,CAAC,CAAC;YAC9C,IAAIiK,cAAc,GAAGM,oBAAoB,CAAClR,OAAO,CAAC;YAClD,IAAIsR,GAAG,GAAGhB,wBAAwB,CAACyB,WAAW,EAAE,QAAQ,EAAEnB,cAAc,CAAC;YACzEU,GAAG,CAAC1P,gBAAgB,CAAC,WAAW,EAAE,UAASX,KAAK,EAAE;cAC9C,IAAIA,KAAK,CAAC6E,GAAG,CAAClI,OAAO,CAACwT,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBACjDE,GAAG,CAACI,KAAK,CAAC,CAAC;gBACX,IAAIpQ,KAAK,GAAGC,aAAa,CAACN,KAAK,CAAC6E,GAAG,CAAC;gBACpCrE,eAAe,CAACH,KAAK,EAAEnC,OAAO,CAAC;cACnC;YACJ,CAAC,CAAC;YACF,OAAOA,OAAO,CAACA,OAAO;UAC1B,CAAC;UAAA,gBAbDyG,QAAQA,CAAAoM,GAAA;YAAA,OAAAF,KAAA,CAAAjQ,KAAA,OAAAC,SAAA;UAAA;QAAA,GAaP;QAEDoF,iBAAiB,EAAG,SAAAA,CAAA,EAAW;UAC3B,IAAIkJ,UAAU,GAAG/T,EAAE,CAACyK,gBAAgB,CAAC,CAAC;UACtC,IAAI,OAAOsJ,UAAU,KAAK,WAAW,EAAE;YACnC,IAAIkB,GAAG,GAAGhB,wBAAwB,CAACF,UAAU,EAAE,QAAQ,EAAE,aAAa,CAAC;YACvEkB,GAAG,CAAC1P,gBAAgB,CAAC,WAAW,EAAE,UAASX,KAAK,EAAE;cAC9C,IAAIA,KAAK,CAAC6E,GAAG,CAAClI,OAAO,CAACwT,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBACjDE,GAAG,CAACI,KAAK,CAAC,CAAC;cACf;YACJ,CAAC,CAAC;UACN,CAAC,MAAM;YACH,MAAM,kCAAkC;UAC5C;QACJ,CAAC;QAEDjT,WAAW,EAAE,SAAAA,CAASuB,OAAO,EAAE;UAC3B,OAAOoR,qBAAqB,CAAC,CAAC;QAClC;MACJ,CAAC;IACL;IAEA,IAAIlC,IAAI,IAAI,gBAAgB,EAAE;MAC1BzS,WAAW,CAACC,MAAM,GAAG,KAAK;MAE1B,OAAO;QACHwD,KAAK;UAAA,IAAA+R,KAAA,GAAA5R,iBAAA,CAAE,WAAeL,OAAO,EAAE;YAC3B,IAAIb,OAAO,GAAGC,aAAa,CAAC,CAAC;YAC7B,IAAImR,QAAQ,SAASlU,EAAE,CAACqE,cAAc,CAACV,OAAO,CAAC;YAE/CkS,cAAc,CAACC,SAAS,CAAC,UAAU,EAAE,UAASlR,KAAK,EAAE;cACjDiR,cAAc,CAACE,WAAW,CAAC,UAAU,CAAC;cACtCtU,MAAM,CAACE,OAAO,CAACqU,OAAO,CAACC,UAAU,CAACZ,KAAK,CAAC,CAAC;cACzC,IAAIpQ,KAAK,GAAGC,aAAa,CAACN,KAAK,CAAC6E,GAAG,CAAC;cACpCrE,eAAe,CAACH,KAAK,EAAEnC,OAAO,CAAC;YACnC,CAAC,CAAC;YAEFrB,MAAM,CAACE,OAAO,CAACqU,OAAO,CAACC,UAAU,CAACC,OAAO,CAAChC,QAAQ,CAAC;YACnD,OAAOpR,OAAO,CAACA,OAAO;UAC1B,CAAC;UAAA,gBAbDe,KAAKA,CAAAsS,IAAA;YAAA,OAAAP,KAAA,CAAApQ,KAAA,OAAAC,SAAA;UAAA;QAAA,GAaJ;QAED2E,MAAM,EAAE,SAAAA,CAASzG,OAAO,EAAE;UACtB,IAAIb,OAAO,GAAGC,aAAa,CAAC,CAAC;UAC7B,IAAIyS,SAAS,GAAGxV,EAAE,CAACqK,eAAe,CAAC1G,OAAO,CAAC;UAE3CkS,cAAc,CAACC,SAAS,CAAC,UAAU,EAAE,UAASlR,KAAK,EAAE;YACjDiR,cAAc,CAACE,WAAW,CAAC,UAAU,CAAC;YACtCtU,MAAM,CAACE,OAAO,CAACqU,OAAO,CAACC,UAAU,CAACZ,KAAK,CAAC,CAAC;YACzCrV,EAAE,CAACuN,UAAU,CAAC,CAAC;YACfzK,OAAO,CAACK,UAAU,CAAC,CAAC;UACxB,CAAC,CAAC;UAEF1B,MAAM,CAACE,OAAO,CAACqU,OAAO,CAACC,UAAU,CAACC,OAAO,CAACV,SAAS,CAAC;UACpD,OAAO1S,OAAO,CAACA,OAAO;QAC1B,CAAC;QAEDyG,QAAQ;UAAA,IAAA6M,MAAA,GAAApS,iBAAA,CAAG,WAAeL,OAAO,EAAE;YAC/B,IAAIb,OAAO,GAAGC,aAAa,CAAC,CAAC;YAC7B,IAAI2S,WAAW,SAAS1V,EAAE,CAACsK,iBAAiB,CAAC3G,OAAO,CAAC;YACrDkS,cAAc,CAACC,SAAS,CAAC,UAAU,EAAG,UAASlR,KAAK,EAAE;cAClDiR,cAAc,CAACE,WAAW,CAAC,UAAU,CAAC;cACtCtU,MAAM,CAACE,OAAO,CAACqU,OAAO,CAACC,UAAU,CAACZ,KAAK,CAAC,CAAC;cACzC,IAAIpQ,KAAK,GAAGC,aAAa,CAACN,KAAK,CAAC6E,GAAG,CAAC;cACpCrE,eAAe,CAACH,KAAK,EAAEnC,OAAO,CAAC;YACnC,CAAC,CAAC;YACFrB,MAAM,CAACE,OAAO,CAACqU,OAAO,CAACC,UAAU,CAACC,OAAO,CAACR,WAAW,CAAC;YACtD,OAAO5S,OAAO,CAACA,OAAO;UAE1B,CAAC;UAAA,gBAZDyG,QAAQA,CAAA8M,IAAA;YAAA,OAAAD,MAAA,CAAA5Q,KAAA,OAAAC,SAAA;UAAA;QAAA,GAYP;QAEDoF,iBAAiB,EAAG,SAAAA,CAAA,EAAW;UAC3B,IAAIkJ,UAAU,GAAG/T,EAAE,CAACyK,gBAAgB,CAAC,CAAC;UACtC,IAAI,OAAOsJ,UAAU,KAAK,WAAW,EAAE;YACnCtS,MAAM,CAACE,OAAO,CAACqU,OAAO,CAACC,UAAU,CAACC,OAAO,CAACnC,UAAU,CAAC;UACzD,CAAC,MAAM;YACH,MAAM,kCAAkC;UAC5C;QACJ,CAAC;QAED3R,WAAW,EAAE,SAAAA,CAASuB,OAAO,EAAE;UAC3B,IAAIA,OAAO,IAAIA,OAAO,CAACvB,WAAW,EAAE;YAChC,OAAOuB,OAAO,CAACvB,WAAW;UAC9B,CAAC,MAAM,IAAIpC,EAAE,CAACoC,WAAW,EAAE;YACvB,OAAOpC,EAAE,CAACoC,WAAW;UACzB,CAAC,MAAM;YACH,OAAO,kBAAkB;UAC7B;QACJ;MACJ,CAAC;IACL;IAEA,MAAM,wBAAwB,GAAGyQ,IAAI;EACzC;EAEA,MAAMyD,kBAAkB,GAAG,cAAc;EAEzC,IAAIC,YAAY,GAAG,SAAAA,CAAA,EAAW;IAC1B,IAAI,EAAE,IAAI,YAAYA,YAAY,CAAC,EAAE;MACjC,OAAO,IAAIA,YAAY,CAAC,CAAC;IAC7B;IAEAC,YAAY,CAACC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;IACvCD,YAAY,CAACE,UAAU,CAAC,SAAS,CAAC;IAElC,IAAIC,EAAE,GAAG,IAAI;;IAEb;AACR;AACA;IACQ,SAASC,kBAAkBA,CAAA,EAAG;MAC1B,MAAMC,WAAW,GAAGlK,IAAI,CAACmK,GAAG,CAAC,CAAC;MAE9B,KAAK,MAAM,CAACrF,GAAG,EAAE8B,KAAK,CAAC,IAAIwD,gBAAgB,CAAC,CAAC,EAAE;QAC3C;QACA,MAAMC,MAAM,GAAGC,WAAW,CAAC1D,KAAK,CAAC;;QAEjC;QACA,IAAIyD,MAAM,KAAK,IAAI,IAAIA,MAAM,GAAGH,WAAW,EAAE;UACzCL,YAAY,CAACE,UAAU,CAACjF,GAAG,CAAC;QAChC;MACJ;IACJ;;IAEA;AACR;AACA;IACQ,SAASyF,cAAcA,CAAA,EAAG;MACtB,KAAK,MAAM,CAACzF,GAAG,CAAC,IAAIsF,gBAAgB,CAAC,CAAC,EAAE;QACpCP,YAAY,CAACE,UAAU,CAACjF,GAAG,CAAC;MAChC;IACJ;;IAEA;AACR;AACA;AACA;IACQ,SAASsF,gBAAgBA,CAAA,EAAG;MACxB,OAAOvD,MAAM,CAACC,OAAO,CAAC+C,YAAY,CAAC,CAACW,MAAM,CAAC,CAAC,CAAC1F,GAAG,CAAC,KAAKA,GAAG,CAAC9B,UAAU,CAAC2G,kBAAkB,CAAC,CAAC;IAC7F;;IAEA;AACR;AACA;AACA;AACA;IACQ,SAASW,WAAWA,CAAC1D,KAAK,EAAE;MACxB,IAAI6D,WAAW;;MAEf;MACA,IAAI;QACAA,WAAW,GAAGxO,IAAI,CAACmD,KAAK,CAACwH,KAAK,CAAC;MACnC,CAAC,CAAC,OAAOlQ,KAAK,EAAE;QACZ,OAAO,IAAI;MACf;;MAEA;MACA,IAAIxD,QAAQ,CAACuX,WAAW,CAAC,IAAI,SAAS,IAAIA,WAAW,IAAI,OAAOA,WAAW,CAACC,OAAO,KAAK,QAAQ,EAAE;QAC9F,OAAOD,WAAW,CAACC,OAAO;MAC9B;MAEA,OAAO,IAAI;IACf;IAEAV,EAAE,CAAC7F,GAAG,GAAG,UAAS7K,KAAK,EAAE;MACrB,IAAI,CAACA,KAAK,EAAE;QACR;MACJ;MAEA,IAAIwL,GAAG,GAAG6E,kBAAkB,GAAGrQ,KAAK;MACpC,IAAIsN,KAAK,GAAGiD,YAAY,CAACc,OAAO,CAAC7F,GAAG,CAAC;MACrC,IAAI8B,KAAK,EAAE;QACPiD,YAAY,CAACE,UAAU,CAACjF,GAAG,CAAC;QAC5B8B,KAAK,GAAG3K,IAAI,CAACmD,KAAK,CAACwH,KAAK,CAAC;MAC7B;MAEAqD,kBAAkB,CAAC,CAAC;MACpB,OAAOrD,KAAK;IAChB,CAAC;IAEDoD,EAAE,CAACzM,GAAG,GAAG,UAASjE,KAAK,EAAE;MACrB2Q,kBAAkB,CAAC,CAAC;MAEpB,MAAMnF,GAAG,GAAG6E,kBAAkB,GAAGrQ,KAAK,CAACA,KAAK;MAC5C,MAAMsN,KAAK,GAAG3K,IAAI,CAACC,SAAS,CAAC;QACzB,GAAG5C,KAAK;QACR;QACAoR,OAAO,EAAE1K,IAAI,CAACmK,GAAG,CAAC,CAAC,GAAI,EAAE,GAAG,EAAE,GAAG;MACrC,CAAC,CAAC;MAEF,IAAI;QACAN,YAAY,CAACC,OAAO,CAAChF,GAAG,EAAE8B,KAAK,CAAC;MACpC,CAAC,CAAC,OAAOlQ,KAAK,EAAE;QACZ;QACA6T,cAAc,CAAC,CAAC;QAChBV,YAAY,CAACC,OAAO,CAAChF,GAAG,EAAE8B,KAAK,CAAC;MACpC;IACJ,CAAC;EACL,CAAC;EAED,IAAIgE,aAAa,GAAG,SAAAA,CAAA,EAAW;IAC3B,IAAI,EAAE,IAAI,YAAYA,aAAa,CAAC,EAAE;MAClC,OAAO,IAAIA,aAAa,CAAC,CAAC;IAC9B;IAEA,IAAIZ,EAAE,GAAG,IAAI;IAEbA,EAAE,CAAC7F,GAAG,GAAG,UAAS7K,KAAK,EAAE;MACrB,IAAI,CAACA,KAAK,EAAE;QACR;MACJ;MAEA,IAAIsN,KAAK,GAAGiE,SAAS,CAAClB,kBAAkB,GAAGrQ,KAAK,CAAC;MACjDwR,SAAS,CAACnB,kBAAkB,GAAGrQ,KAAK,EAAE,EAAE,EAAEyR,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;MACjE,IAAInE,KAAK,EAAE;QACP,OAAO3K,IAAI,CAACmD,KAAK,CAACwH,KAAK,CAAC;MAC5B;IACJ,CAAC;IAEDoD,EAAE,CAACzM,GAAG,GAAG,UAASjE,KAAK,EAAE;MACrBwR,SAAS,CAACnB,kBAAkB,GAAGrQ,KAAK,CAACA,KAAK,EAAE2C,IAAI,CAACC,SAAS,CAAC5C,KAAK,CAAC,EAAEyR,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAC5F,CAAC;IAEDf,EAAE,CAACD,UAAU,GAAG,UAASjF,GAAG,EAAE;MAC1BgG,SAAS,CAAChG,GAAG,EAAE,EAAE,EAAEiG,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED,IAAIA,gBAAgB,GAAG,SAAAA,CAAUC,OAAO,EAAE;MACtC,IAAIC,GAAG,GAAG,IAAIjL,IAAI,CAAC,CAAC;MACpBiL,GAAG,CAACC,OAAO,CAACD,GAAG,CAAChL,OAAO,CAAC,CAAC,GAAI+K,OAAO,GAAC,EAAE,GAAC,IAAK,CAAC;MAC9C,OAAOC,GAAG;IACd,CAAC;IAED,IAAIJ,SAAS,GAAG,SAAAA,CAAU/F,GAAG,EAAE;MAC3B,IAAI6B,IAAI,GAAG7B,GAAG,GAAG,GAAG;MACpB,IAAIqG,EAAE,GAAG5T,QAAQ,CAAC6T,MAAM,CAACxG,KAAK,CAAC,GAAG,CAAC;MACnC,KAAK,IAAI/J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsQ,EAAE,CAACpQ,MAAM,EAAEF,CAAC,EAAE,EAAE;QAChC,IAAIwQ,CAAC,GAAGF,EAAE,CAACtQ,CAAC,CAAC;QACb,OAAOwQ,CAAC,CAACpK,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;UACvBoK,CAAC,GAAGA,CAAC,CAAC7G,SAAS,CAAC,CAAC,CAAC;QACtB;QACA,IAAI6G,CAAC,CAACzW,OAAO,CAAC+R,IAAI,CAAC,IAAI,CAAC,EAAE;UACtB,OAAO0E,CAAC,CAAC7G,SAAS,CAACmC,IAAI,CAAC5L,MAAM,EAAEsQ,CAAC,CAACtQ,MAAM,CAAC;QAC7C;MACJ;MACA,OAAO,EAAE;IACb,CAAC;IAED,IAAI+P,SAAS,GAAG,SAAAA,CAAUhG,GAAG,EAAE8B,KAAK,EAAE0E,cAAc,EAAE;MAClD,IAAIF,MAAM,GAAGtG,GAAG,GAAG,GAAG,GAAG8B,KAAK,GAAG,IAAI,GAC/B,UAAU,GAAG0E,cAAc,CAACC,WAAW,CAAC,CAAC,GAAG,IAAI;MACtDhU,QAAQ,CAAC6T,MAAM,GAAGA,MAAM;IAC5B,CAAC;EACL,CAAC;EAED,SAAS1W,qBAAqBA,CAAA,EAAG;IAC7B,IAAI;MACA,OAAO,IAAIkV,YAAY,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAO4B,GAAG,EAAE,CACd;IAEA,OAAO,IAAIZ,aAAa,CAAC,CAAC;EAC9B;EAEA,SAAS5W,YAAYA,CAACyX,EAAE,EAAE;IACtB,OAAO,YAAW;MACd,IAAIpY,EAAE,CAACyC,aAAa,EAAE;QAClB2V,EAAE,CAAC5S,KAAK,CAAC5E,OAAO,EAAE2G,KAAK,CAAC8Q,SAAS,CAAC3G,KAAK,CAAC4G,IAAI,CAAC7S,SAAS,CAAC,CAAC;MAC5D;IACJ,CAAC;EACL;AACJ;AAEA,eAAe/F,QAAQ;;AAEvB;AACA;AACA;AACA;AACA,SAAS2I,aAAaA,CAACkQ,KAAK,EAAE;EAC1B,MAAMC,SAAS,GAAG7Q,MAAM,CAAC8Q,aAAa,CAAC,GAAGF,KAAK,CAAC;EAChD,OAAOG,IAAI,CAACF,SAAS,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AAHA,SAIerQ,YAAYA,CAAAwQ,IAAA;EAAA,OAAAC,aAAA,CAAApT,KAAA,OAAAC,SAAA;AAAA;AAW3B;AACA;AACA;AAFA,SAAAmT,cAAA;EAAAA,aAAA,GAAA5U,iBAAA,CAXA,WAA4B6U,OAAO,EAAE;IACjC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,CAAC;IACjC,MAAM5T,IAAI,GAAG2T,OAAO,CAACE,MAAM,CAACH,OAAO,CAAC;IAEpC,IAAI,OAAO9R,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACkS,MAAM,KAAK,WAAW,EAAE;MACvE,MAAM,IAAIrZ,KAAK,CAAC,kCAAkC,CAAC;IACvD;IAEA,aAAamH,MAAM,CAACkS,MAAM,CAACC,MAAM,CAAC,SAAS,EAAE/T,IAAI,CAAC;EACtD,CAAC;EAAA,OAAAyT,aAAA,CAAApT,KAAA,OAAAC,SAAA;AAAA;AAKD,SAASsK,WAAWA,CAAC3J,KAAK,EAAE;EACxB,MAAM,CAAC+S,MAAM,EAAEC,OAAO,CAAC,GAAGhT,KAAK,CAACmL,KAAK,CAAC,GAAG,CAAC;EAE1C,IAAI,OAAO6H,OAAO,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAIxZ,KAAK,CAAC,4CAA4C,CAAC;EACjE;EAEA,IAAIyZ,OAAO;EAEX,IAAI;IACAA,OAAO,GAAGC,eAAe,CAACF,OAAO,CAAC;EACtC,CAAC,CAAC,OAAO/V,KAAK,EAAE;IACZ,MAAM,IAAIzD,KAAK,CAAC,iEAAiE,EAAE;MAAEqK,KAAK,EAAE5G;IAAM,CAAC,CAAC;EACxG;EAEA,IAAI;IACA,OAAOuF,IAAI,CAACmD,KAAK,CAACsN,OAAO,CAAC;EAC9B,CAAC,CAAC,OAAOhW,KAAK,EAAE;IACZ,MAAM,IAAIzD,KAAK,CAAC,4DAA4D,EAAE;MAAEqK,KAAK,EAAE5G;IAAM,CAAC,CAAC;EACnG;AACJ;;AAEA;AACA;AACA;AACA,SAASiW,eAAeA,CAAC5F,KAAK,EAAE;EAC5B,IAAI6F,MAAM,GAAG7F,KAAK,CACb8F,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,CACpBA,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC;EAEzB,QAAQD,MAAM,CAAC7R,MAAM,GAAG,CAAC;IACrB,KAAK,CAAC;MACF;IACJ,KAAK,CAAC;MACF6R,MAAM,IAAI,IAAI;MACd;IACJ,KAAK,CAAC;MACFA,MAAM,IAAI,GAAG;MACb;IACJ;MACI,MAAM,IAAI3Z,KAAK,CAAC,qCAAqC,CAAC;EAC9D;EAEA,IAAI;IACA,OAAO6Z,gBAAgB,CAACF,MAAM,CAAC;EACnC,CAAC,CAAC,OAAOlW,KAAK,EAAE;IACZ,OAAOqW,IAAI,CAACH,MAAM,CAAC;EACvB;AACJ;;AAEA;AACA;AACA;AACA,SAASE,gBAAgBA,CAAC/F,KAAK,EAAE;EAC7B,OAAOiG,kBAAkB,CAACD,IAAI,CAAChG,KAAK,CAAC,CAACpL,OAAO,CAAC,MAAM,EAAE,CAACsR,CAAC,EAAEvM,CAAC,KAAK;IAC5D,IAAIY,IAAI,GAAGZ,CAAC,CAAC5F,UAAU,CAAC,CAAC,CAAC,CAACoS,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;IAErD,IAAI7L,IAAI,CAACvG,MAAM,GAAG,CAAC,EAAE;MACjBuG,IAAI,GAAG,GAAG,GAAGA,IAAI;IACrB;IAEA,OAAO,GAAG,GAAGA,IAAI;EACrB,CAAC,CAAC,CAAC;AACP;;AAEA;AACA;AACA;AACA;AACA,SAASpO,QAAQA,CAAC6T,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}