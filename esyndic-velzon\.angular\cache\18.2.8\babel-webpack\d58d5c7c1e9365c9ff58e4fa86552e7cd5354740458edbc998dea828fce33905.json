{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { each, map } from 'zrender/lib/core/util.js';\nimport { linearMap, getPixelPrecision, round } from '../util/number.js';\nimport { createAxisTicks, createAxisLabels, calculateCategoryInterval } from './axisTickLabelBuilder.js';\nvar NORMALIZED_EXTENT = [0, 1];\n/**\n * Base class of Axis.\n */\nvar Axis = /** @class */function () {\n  function Axis(dim, scale, extent) {\n    this.onBand = false;\n    this.inverse = false;\n    this.dim = dim;\n    this.scale = scale;\n    this._extent = extent || [0, 0];\n  }\n  /**\n   * If axis extent contain given coord\n   */\n  Axis.prototype.contain = function (coord) {\n    var extent = this._extent;\n    var min = Math.min(extent[0], extent[1]);\n    var max = Math.max(extent[0], extent[1]);\n    return coord >= min && coord <= max;\n  };\n  /**\n   * If axis extent contain given data\n   */\n  Axis.prototype.containData = function (data) {\n    return this.scale.contain(data);\n  };\n  /**\n   * Get coord extent.\n   */\n  Axis.prototype.getExtent = function () {\n    return this._extent.slice();\n  };\n  /**\n   * Get precision used for formatting\n   */\n  Axis.prototype.getPixelPrecision = function (dataExtent) {\n    return getPixelPrecision(dataExtent || this.scale.getExtent(), this._extent);\n  };\n  /**\n   * Set coord extent\n   */\n  Axis.prototype.setExtent = function (start, end) {\n    var extent = this._extent;\n    extent[0] = start;\n    extent[1] = end;\n  };\n  /**\n   * Convert data to coord. Data is the rank if it has an ordinal scale\n   */\n  Axis.prototype.dataToCoord = function (data, clamp) {\n    var extent = this._extent;\n    var scale = this.scale;\n    data = scale.normalize(data);\n    if (this.onBand && scale.type === 'ordinal') {\n      extent = extent.slice();\n      fixExtentWithBands(extent, scale.count());\n    }\n    return linearMap(data, NORMALIZED_EXTENT, extent, clamp);\n  };\n  /**\n   * Convert coord to data. Data is the rank if it has an ordinal scale\n   */\n  Axis.prototype.coordToData = function (coord, clamp) {\n    var extent = this._extent;\n    var scale = this.scale;\n    if (this.onBand && scale.type === 'ordinal') {\n      extent = extent.slice();\n      fixExtentWithBands(extent, scale.count());\n    }\n    var t = linearMap(coord, extent, NORMALIZED_EXTENT, clamp);\n    return this.scale.scale(t);\n  };\n  /**\n   * Convert pixel point to data in axis\n   */\n  Axis.prototype.pointToData = function (point, clamp) {\n    // Should be implemented in derived class if necessary.\n    return;\n  };\n  /**\n   * Different from `zrUtil.map(axis.getTicks(), axis.dataToCoord, axis)`,\n   * `axis.getTicksCoords` considers `onBand`, which is used by\n   * `boundaryGap:true` of category axis and splitLine and splitArea.\n   * @param opt.tickModel default: axis.model.getModel('axisTick')\n   * @param opt.clamp If `true`, the first and the last\n   *        tick must be at the axis end points. Otherwise, clip ticks\n   *        that outside the axis extent.\n   */\n  Axis.prototype.getTicksCoords = function (opt) {\n    opt = opt || {};\n    var tickModel = opt.tickModel || this.getTickModel();\n    var result = createAxisTicks(this, tickModel);\n    var ticks = result.ticks;\n    var ticksCoords = map(ticks, function (tickVal) {\n      return {\n        coord: this.dataToCoord(this.scale.type === 'ordinal' ? this.scale.getRawOrdinalNumber(tickVal) : tickVal),\n        tickValue: tickVal\n      };\n    }, this);\n    var alignWithLabel = tickModel.get('alignWithLabel');\n    fixOnBandTicksCoords(this, ticksCoords, alignWithLabel, opt.clamp);\n    return ticksCoords;\n  };\n  Axis.prototype.getMinorTicksCoords = function () {\n    if (this.scale.type === 'ordinal') {\n      // Category axis doesn't support minor ticks\n      return [];\n    }\n    var minorTickModel = this.model.getModel('minorTick');\n    var splitNumber = minorTickModel.get('splitNumber');\n    // Protection.\n    if (!(splitNumber > 0 && splitNumber < 100)) {\n      splitNumber = 5;\n    }\n    var minorTicks = this.scale.getMinorTicks(splitNumber);\n    var minorTicksCoords = map(minorTicks, function (minorTicksGroup) {\n      return map(minorTicksGroup, function (minorTick) {\n        return {\n          coord: this.dataToCoord(minorTick),\n          tickValue: minorTick\n        };\n      }, this);\n    }, this);\n    return minorTicksCoords;\n  };\n  Axis.prototype.getViewLabels = function () {\n    return createAxisLabels(this).labels;\n  };\n  Axis.prototype.getLabelModel = function () {\n    return this.model.getModel('axisLabel');\n  };\n  /**\n   * Notice here we only get the default tick model. For splitLine\n   * or splitArea, we should pass the splitLineModel or splitAreaModel\n   * manually when calling `getTicksCoords`.\n   * In GL, this method may be overridden to:\n   * `axisModel.getModel('axisTick', grid3DModel.getModel('axisTick'));`\n   */\n  Axis.prototype.getTickModel = function () {\n    return this.model.getModel('axisTick');\n  };\n  /**\n   * Get width of band\n   */\n  Axis.prototype.getBandWidth = function () {\n    var axisExtent = this._extent;\n    var dataExtent = this.scale.getExtent();\n    var len = dataExtent[1] - dataExtent[0] + (this.onBand ? 1 : 0);\n    // Fix #2728, avoid NaN when only one data.\n    len === 0 && (len = 1);\n    var size = Math.abs(axisExtent[1] - axisExtent[0]);\n    return Math.abs(size) / len;\n  };\n  /**\n   * Only be called in category axis.\n   * Can be overridden, consider other axes like in 3D.\n   * @return Auto interval for cateogry axis tick and label\n   */\n  Axis.prototype.calculateCategoryInterval = function () {\n    return calculateCategoryInterval(this);\n  };\n  return Axis;\n}();\nfunction fixExtentWithBands(extent, nTick) {\n  var size = extent[1] - extent[0];\n  var len = nTick;\n  var margin = size / len / 2;\n  extent[0] += margin;\n  extent[1] -= margin;\n}\n// If axis has labels [1, 2, 3, 4]. Bands on the axis are\n// |---1---|---2---|---3---|---4---|.\n// So the displayed ticks and splitLine/splitArea should between\n// each data item, otherwise cause misleading (e.g., split tow bars\n// of a single data item when there are two bar series).\n// Also consider if tickCategoryInterval > 0 and onBand, ticks and\n// splitLine/spliteArea should layout appropriately corresponding\n// to displayed labels. (So we should not use `getBandWidth` in this\n// case).\nfunction fixOnBandTicksCoords(axis, ticksCoords, alignWithLabel, clamp) {\n  var ticksLen = ticksCoords.length;\n  if (!axis.onBand || alignWithLabel || !ticksLen) {\n    return;\n  }\n  var axisExtent = axis.getExtent();\n  var last;\n  var diffSize;\n  if (ticksLen === 1) {\n    ticksCoords[0].coord = axisExtent[0];\n    last = ticksCoords[1] = {\n      coord: axisExtent[1]\n    };\n  } else {\n    var crossLen = ticksCoords[ticksLen - 1].tickValue - ticksCoords[0].tickValue;\n    var shift_1 = (ticksCoords[ticksLen - 1].coord - ticksCoords[0].coord) / crossLen;\n    each(ticksCoords, function (ticksItem) {\n      ticksItem.coord -= shift_1 / 2;\n    });\n    var dataExtent = axis.scale.getExtent();\n    diffSize = 1 + dataExtent[1] - ticksCoords[ticksLen - 1].tickValue;\n    last = {\n      coord: ticksCoords[ticksLen - 1].coord + shift_1 * diffSize\n    };\n    ticksCoords.push(last);\n  }\n  var inverse = axisExtent[0] > axisExtent[1];\n  // Handling clamp.\n  if (littleThan(ticksCoords[0].coord, axisExtent[0])) {\n    clamp ? ticksCoords[0].coord = axisExtent[0] : ticksCoords.shift();\n  }\n  if (clamp && littleThan(axisExtent[0], ticksCoords[0].coord)) {\n    ticksCoords.unshift({\n      coord: axisExtent[0]\n    });\n  }\n  if (littleThan(axisExtent[1], last.coord)) {\n    clamp ? last.coord = axisExtent[1] : ticksCoords.pop();\n  }\n  if (clamp && littleThan(last.coord, axisExtent[1])) {\n    ticksCoords.push({\n      coord: axisExtent[1]\n    });\n  }\n  function littleThan(a, b) {\n    // Avoid rounding error cause calculated tick coord different with extent.\n    // It may cause an extra unnecessary tick added.\n    a = round(a);\n    b = round(b);\n    return inverse ? a > b : a < b;\n  }\n}\nexport default Axis;", "map": {"version": 3, "names": ["each", "map", "linearMap", "getPixelPrecision", "round", "createAxisTicks", "createAxisLabels", "calculateCategoryInterval", "NORMALIZED_EXTENT", "Axis", "dim", "scale", "extent", "onBand", "inverse", "_extent", "prototype", "contain", "coord", "min", "Math", "max", "containData", "data", "getExtent", "slice", "dataExtent", "setExtent", "start", "end", "dataToCoord", "clamp", "normalize", "type", "fixExtentWithBands", "count", "coordToData", "t", "pointToData", "point", "getTicksCoords", "opt", "tickModel", "getTickModel", "result", "ticks", "ticksCoords", "tickVal", "getRawOrdinalNumber", "tickValue", "alignWithLabel", "get", "fixOnBandTicksCoords", "getMinorTicksCoords", "minorTickModel", "model", "getModel", "splitNumber", "minorTicks", "getMinorTicks", "minorTicksCoords", "minorTicksGroup", "minor<PERSON><PERSON>", "getViewLabels", "labels", "getLabelModel", "getBandWidth", "axisExtent", "len", "size", "abs", "nTick", "margin", "axis", "ticksLen", "length", "last", "diffSize", "crossLen", "shift_1", "ticksItem", "push", "<PERSON><PERSON><PERSON>", "shift", "unshift", "pop", "a", "b"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/coord/Axis.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { each, map } from 'zrender/lib/core/util.js';\nimport { linearMap, getPixelPrecision, round } from '../util/number.js';\nimport { createAxisTicks, createAxisLabels, calculateCategoryInterval } from './axisTickLabelBuilder.js';\nvar NORMALIZED_EXTENT = [0, 1];\n/**\n * Base class of Axis.\n */\nvar Axis = /** @class */function () {\n  function Axis(dim, scale, extent) {\n    this.onBand = false;\n    this.inverse = false;\n    this.dim = dim;\n    this.scale = scale;\n    this._extent = extent || [0, 0];\n  }\n  /**\n   * If axis extent contain given coord\n   */\n  Axis.prototype.contain = function (coord) {\n    var extent = this._extent;\n    var min = Math.min(extent[0], extent[1]);\n    var max = Math.max(extent[0], extent[1]);\n    return coord >= min && coord <= max;\n  };\n  /**\n   * If axis extent contain given data\n   */\n  Axis.prototype.containData = function (data) {\n    return this.scale.contain(data);\n  };\n  /**\n   * Get coord extent.\n   */\n  Axis.prototype.getExtent = function () {\n    return this._extent.slice();\n  };\n  /**\n   * Get precision used for formatting\n   */\n  Axis.prototype.getPixelPrecision = function (dataExtent) {\n    return getPixelPrecision(dataExtent || this.scale.getExtent(), this._extent);\n  };\n  /**\n   * Set coord extent\n   */\n  Axis.prototype.setExtent = function (start, end) {\n    var extent = this._extent;\n    extent[0] = start;\n    extent[1] = end;\n  };\n  /**\n   * Convert data to coord. Data is the rank if it has an ordinal scale\n   */\n  Axis.prototype.dataToCoord = function (data, clamp) {\n    var extent = this._extent;\n    var scale = this.scale;\n    data = scale.normalize(data);\n    if (this.onBand && scale.type === 'ordinal') {\n      extent = extent.slice();\n      fixExtentWithBands(extent, scale.count());\n    }\n    return linearMap(data, NORMALIZED_EXTENT, extent, clamp);\n  };\n  /**\n   * Convert coord to data. Data is the rank if it has an ordinal scale\n   */\n  Axis.prototype.coordToData = function (coord, clamp) {\n    var extent = this._extent;\n    var scale = this.scale;\n    if (this.onBand && scale.type === 'ordinal') {\n      extent = extent.slice();\n      fixExtentWithBands(extent, scale.count());\n    }\n    var t = linearMap(coord, extent, NORMALIZED_EXTENT, clamp);\n    return this.scale.scale(t);\n  };\n  /**\n   * Convert pixel point to data in axis\n   */\n  Axis.prototype.pointToData = function (point, clamp) {\n    // Should be implemented in derived class if necessary.\n    return;\n  };\n  /**\n   * Different from `zrUtil.map(axis.getTicks(), axis.dataToCoord, axis)`,\n   * `axis.getTicksCoords` considers `onBand`, which is used by\n   * `boundaryGap:true` of category axis and splitLine and splitArea.\n   * @param opt.tickModel default: axis.model.getModel('axisTick')\n   * @param opt.clamp If `true`, the first and the last\n   *        tick must be at the axis end points. Otherwise, clip ticks\n   *        that outside the axis extent.\n   */\n  Axis.prototype.getTicksCoords = function (opt) {\n    opt = opt || {};\n    var tickModel = opt.tickModel || this.getTickModel();\n    var result = createAxisTicks(this, tickModel);\n    var ticks = result.ticks;\n    var ticksCoords = map(ticks, function (tickVal) {\n      return {\n        coord: this.dataToCoord(this.scale.type === 'ordinal' ? this.scale.getRawOrdinalNumber(tickVal) : tickVal),\n        tickValue: tickVal\n      };\n    }, this);\n    var alignWithLabel = tickModel.get('alignWithLabel');\n    fixOnBandTicksCoords(this, ticksCoords, alignWithLabel, opt.clamp);\n    return ticksCoords;\n  };\n  Axis.prototype.getMinorTicksCoords = function () {\n    if (this.scale.type === 'ordinal') {\n      // Category axis doesn't support minor ticks\n      return [];\n    }\n    var minorTickModel = this.model.getModel('minorTick');\n    var splitNumber = minorTickModel.get('splitNumber');\n    // Protection.\n    if (!(splitNumber > 0 && splitNumber < 100)) {\n      splitNumber = 5;\n    }\n    var minorTicks = this.scale.getMinorTicks(splitNumber);\n    var minorTicksCoords = map(minorTicks, function (minorTicksGroup) {\n      return map(minorTicksGroup, function (minorTick) {\n        return {\n          coord: this.dataToCoord(minorTick),\n          tickValue: minorTick\n        };\n      }, this);\n    }, this);\n    return minorTicksCoords;\n  };\n  Axis.prototype.getViewLabels = function () {\n    return createAxisLabels(this).labels;\n  };\n  Axis.prototype.getLabelModel = function () {\n    return this.model.getModel('axisLabel');\n  };\n  /**\n   * Notice here we only get the default tick model. For splitLine\n   * or splitArea, we should pass the splitLineModel or splitAreaModel\n   * manually when calling `getTicksCoords`.\n   * In GL, this method may be overridden to:\n   * `axisModel.getModel('axisTick', grid3DModel.getModel('axisTick'));`\n   */\n  Axis.prototype.getTickModel = function () {\n    return this.model.getModel('axisTick');\n  };\n  /**\n   * Get width of band\n   */\n  Axis.prototype.getBandWidth = function () {\n    var axisExtent = this._extent;\n    var dataExtent = this.scale.getExtent();\n    var len = dataExtent[1] - dataExtent[0] + (this.onBand ? 1 : 0);\n    // Fix #2728, avoid NaN when only one data.\n    len === 0 && (len = 1);\n    var size = Math.abs(axisExtent[1] - axisExtent[0]);\n    return Math.abs(size) / len;\n  };\n  /**\n   * Only be called in category axis.\n   * Can be overridden, consider other axes like in 3D.\n   * @return Auto interval for cateogry axis tick and label\n   */\n  Axis.prototype.calculateCategoryInterval = function () {\n    return calculateCategoryInterval(this);\n  };\n  return Axis;\n}();\nfunction fixExtentWithBands(extent, nTick) {\n  var size = extent[1] - extent[0];\n  var len = nTick;\n  var margin = size / len / 2;\n  extent[0] += margin;\n  extent[1] -= margin;\n}\n// If axis has labels [1, 2, 3, 4]. Bands on the axis are\n// |---1---|---2---|---3---|---4---|.\n// So the displayed ticks and splitLine/splitArea should between\n// each data item, otherwise cause misleading (e.g., split tow bars\n// of a single data item when there are two bar series).\n// Also consider if tickCategoryInterval > 0 and onBand, ticks and\n// splitLine/spliteArea should layout appropriately corresponding\n// to displayed labels. (So we should not use `getBandWidth` in this\n// case).\nfunction fixOnBandTicksCoords(axis, ticksCoords, alignWithLabel, clamp) {\n  var ticksLen = ticksCoords.length;\n  if (!axis.onBand || alignWithLabel || !ticksLen) {\n    return;\n  }\n  var axisExtent = axis.getExtent();\n  var last;\n  var diffSize;\n  if (ticksLen === 1) {\n    ticksCoords[0].coord = axisExtent[0];\n    last = ticksCoords[1] = {\n      coord: axisExtent[1]\n    };\n  } else {\n    var crossLen = ticksCoords[ticksLen - 1].tickValue - ticksCoords[0].tickValue;\n    var shift_1 = (ticksCoords[ticksLen - 1].coord - ticksCoords[0].coord) / crossLen;\n    each(ticksCoords, function (ticksItem) {\n      ticksItem.coord -= shift_1 / 2;\n    });\n    var dataExtent = axis.scale.getExtent();\n    diffSize = 1 + dataExtent[1] - ticksCoords[ticksLen - 1].tickValue;\n    last = {\n      coord: ticksCoords[ticksLen - 1].coord + shift_1 * diffSize\n    };\n    ticksCoords.push(last);\n  }\n  var inverse = axisExtent[0] > axisExtent[1];\n  // Handling clamp.\n  if (littleThan(ticksCoords[0].coord, axisExtent[0])) {\n    clamp ? ticksCoords[0].coord = axisExtent[0] : ticksCoords.shift();\n  }\n  if (clamp && littleThan(axisExtent[0], ticksCoords[0].coord)) {\n    ticksCoords.unshift({\n      coord: axisExtent[0]\n    });\n  }\n  if (littleThan(axisExtent[1], last.coord)) {\n    clamp ? last.coord = axisExtent[1] : ticksCoords.pop();\n  }\n  if (clamp && littleThan(last.coord, axisExtent[1])) {\n    ticksCoords.push({\n      coord: axisExtent[1]\n    });\n  }\n  function littleThan(a, b) {\n    // Avoid rounding error cause calculated tick coord different with extent.\n    // It may cause an extra unnecessary tick added.\n    a = round(a);\n    b = round(b);\n    return inverse ? a > b : a < b;\n  }\n}\nexport default Axis;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,GAAG,QAAQ,0BAA0B;AACpD,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,KAAK,QAAQ,mBAAmB;AACvE,SAASC,eAAe,EAAEC,gBAAgB,EAAEC,yBAAyB,QAAQ,2BAA2B;AACxG,IAAIC,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAC9B;AACA;AACA;AACA,IAAIC,IAAI,GAAG,aAAa,YAAY;EAClC,SAASA,IAAIA,CAACC,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAChC,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACJ,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACI,OAAO,GAAGH,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACjC;EACA;AACF;AACA;EACEH,IAAI,CAACO,SAAS,CAACC,OAAO,GAAG,UAAUC,KAAK,EAAE;IACxC,IAAIN,MAAM,GAAG,IAAI,CAACG,OAAO;IACzB,IAAII,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACP,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACxC,IAAIS,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACT,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACxC,OAAOM,KAAK,IAAIC,GAAG,IAAID,KAAK,IAAIG,GAAG;EACrC,CAAC;EACD;AACF;AACA;EACEZ,IAAI,CAACO,SAAS,CAACM,WAAW,GAAG,UAAUC,IAAI,EAAE;IAC3C,OAAO,IAAI,CAACZ,KAAK,CAACM,OAAO,CAACM,IAAI,CAAC;EACjC,CAAC;EACD;AACF;AACA;EACEd,IAAI,CAACO,SAAS,CAACQ,SAAS,GAAG,YAAY;IACrC,OAAO,IAAI,CAACT,OAAO,CAACU,KAAK,CAAC,CAAC;EAC7B,CAAC;EACD;AACF;AACA;EACEhB,IAAI,CAACO,SAAS,CAACb,iBAAiB,GAAG,UAAUuB,UAAU,EAAE;IACvD,OAAOvB,iBAAiB,CAACuB,UAAU,IAAI,IAAI,CAACf,KAAK,CAACa,SAAS,CAAC,CAAC,EAAE,IAAI,CAACT,OAAO,CAAC;EAC9E,CAAC;EACD;AACF;AACA;EACEN,IAAI,CAACO,SAAS,CAACW,SAAS,GAAG,UAAUC,KAAK,EAAEC,GAAG,EAAE;IAC/C,IAAIjB,MAAM,GAAG,IAAI,CAACG,OAAO;IACzBH,MAAM,CAAC,CAAC,CAAC,GAAGgB,KAAK;IACjBhB,MAAM,CAAC,CAAC,CAAC,GAAGiB,GAAG;EACjB,CAAC;EACD;AACF;AACA;EACEpB,IAAI,CAACO,SAAS,CAACc,WAAW,GAAG,UAAUP,IAAI,EAAEQ,KAAK,EAAE;IAClD,IAAInB,MAAM,GAAG,IAAI,CAACG,OAAO;IACzB,IAAIJ,KAAK,GAAG,IAAI,CAACA,KAAK;IACtBY,IAAI,GAAGZ,KAAK,CAACqB,SAAS,CAACT,IAAI,CAAC;IAC5B,IAAI,IAAI,CAACV,MAAM,IAAIF,KAAK,CAACsB,IAAI,KAAK,SAAS,EAAE;MAC3CrB,MAAM,GAAGA,MAAM,CAACa,KAAK,CAAC,CAAC;MACvBS,kBAAkB,CAACtB,MAAM,EAAED,KAAK,CAACwB,KAAK,CAAC,CAAC,CAAC;IAC3C;IACA,OAAOjC,SAAS,CAACqB,IAAI,EAAEf,iBAAiB,EAAEI,MAAM,EAAEmB,KAAK,CAAC;EAC1D,CAAC;EACD;AACF;AACA;EACEtB,IAAI,CAACO,SAAS,CAACoB,WAAW,GAAG,UAAUlB,KAAK,EAAEa,KAAK,EAAE;IACnD,IAAInB,MAAM,GAAG,IAAI,CAACG,OAAO;IACzB,IAAIJ,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI,IAAI,CAACE,MAAM,IAAIF,KAAK,CAACsB,IAAI,KAAK,SAAS,EAAE;MAC3CrB,MAAM,GAAGA,MAAM,CAACa,KAAK,CAAC,CAAC;MACvBS,kBAAkB,CAACtB,MAAM,EAAED,KAAK,CAACwB,KAAK,CAAC,CAAC,CAAC;IAC3C;IACA,IAAIE,CAAC,GAAGnC,SAAS,CAACgB,KAAK,EAAEN,MAAM,EAAEJ,iBAAiB,EAAEuB,KAAK,CAAC;IAC1D,OAAO,IAAI,CAACpB,KAAK,CAACA,KAAK,CAAC0B,CAAC,CAAC;EAC5B,CAAC;EACD;AACF;AACA;EACE5B,IAAI,CAACO,SAAS,CAACsB,WAAW,GAAG,UAAUC,KAAK,EAAER,KAAK,EAAE;IACnD;IACA;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtB,IAAI,CAACO,SAAS,CAACwB,cAAc,GAAG,UAAUC,GAAG,EAAE;IAC7CA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACf,IAAIC,SAAS,GAAGD,GAAG,CAACC,SAAS,IAAI,IAAI,CAACC,YAAY,CAAC,CAAC;IACpD,IAAIC,MAAM,GAAGvC,eAAe,CAAC,IAAI,EAAEqC,SAAS,CAAC;IAC7C,IAAIG,KAAK,GAAGD,MAAM,CAACC,KAAK;IACxB,IAAIC,WAAW,GAAG7C,GAAG,CAAC4C,KAAK,EAAE,UAAUE,OAAO,EAAE;MAC9C,OAAO;QACL7B,KAAK,EAAE,IAAI,CAACY,WAAW,CAAC,IAAI,CAACnB,KAAK,CAACsB,IAAI,KAAK,SAAS,GAAG,IAAI,CAACtB,KAAK,CAACqC,mBAAmB,CAACD,OAAO,CAAC,GAAGA,OAAO,CAAC;QAC1GE,SAAS,EAAEF;MACb,CAAC;IACH,CAAC,EAAE,IAAI,CAAC;IACR,IAAIG,cAAc,GAAGR,SAAS,CAACS,GAAG,CAAC,gBAAgB,CAAC;IACpDC,oBAAoB,CAAC,IAAI,EAAEN,WAAW,EAAEI,cAAc,EAAET,GAAG,CAACV,KAAK,CAAC;IAClE,OAAOe,WAAW;EACpB,CAAC;EACDrC,IAAI,CAACO,SAAS,CAACqC,mBAAmB,GAAG,YAAY;IAC/C,IAAI,IAAI,CAAC1C,KAAK,CAACsB,IAAI,KAAK,SAAS,EAAE;MACjC;MACA,OAAO,EAAE;IACX;IACA,IAAIqB,cAAc,GAAG,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC;IACrD,IAAIC,WAAW,GAAGH,cAAc,CAACH,GAAG,CAAC,aAAa,CAAC;IACnD;IACA,IAAI,EAAEM,WAAW,GAAG,CAAC,IAAIA,WAAW,GAAG,GAAG,CAAC,EAAE;MAC3CA,WAAW,GAAG,CAAC;IACjB;IACA,IAAIC,UAAU,GAAG,IAAI,CAAC/C,KAAK,CAACgD,aAAa,CAACF,WAAW,CAAC;IACtD,IAAIG,gBAAgB,GAAG3D,GAAG,CAACyD,UAAU,EAAE,UAAUG,eAAe,EAAE;MAChE,OAAO5D,GAAG,CAAC4D,eAAe,EAAE,UAAUC,SAAS,EAAE;QAC/C,OAAO;UACL5C,KAAK,EAAE,IAAI,CAACY,WAAW,CAACgC,SAAS,CAAC;UAClCb,SAAS,EAAEa;QACb,CAAC;MACH,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,EAAE,IAAI,CAAC;IACR,OAAOF,gBAAgB;EACzB,CAAC;EACDnD,IAAI,CAACO,SAAS,CAAC+C,aAAa,GAAG,YAAY;IACzC,OAAOzD,gBAAgB,CAAC,IAAI,CAAC,CAAC0D,MAAM;EACtC,CAAC;EACDvD,IAAI,CAACO,SAAS,CAACiD,aAAa,GAAG,YAAY;IACzC,OAAO,IAAI,CAACV,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC;EACzC,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;EACE/C,IAAI,CAACO,SAAS,CAAC2B,YAAY,GAAG,YAAY;IACxC,OAAO,IAAI,CAACY,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC;EACxC,CAAC;EACD;AACF;AACA;EACE/C,IAAI,CAACO,SAAS,CAACkD,YAAY,GAAG,YAAY;IACxC,IAAIC,UAAU,GAAG,IAAI,CAACpD,OAAO;IAC7B,IAAIW,UAAU,GAAG,IAAI,CAACf,KAAK,CAACa,SAAS,CAAC,CAAC;IACvC,IAAI4C,GAAG,GAAG1C,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAACb,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/D;IACAuD,GAAG,KAAK,CAAC,KAAKA,GAAG,GAAG,CAAC,CAAC;IACtB,IAAIC,IAAI,GAAGjD,IAAI,CAACkD,GAAG,CAACH,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC;IAClD,OAAO/C,IAAI,CAACkD,GAAG,CAACD,IAAI,CAAC,GAAGD,GAAG;EAC7B,CAAC;EACD;AACF;AACA;AACA;AACA;EACE3D,IAAI,CAACO,SAAS,CAACT,yBAAyB,GAAG,YAAY;IACrD,OAAOA,yBAAyB,CAAC,IAAI,CAAC;EACxC,CAAC;EACD,OAAOE,IAAI;AACb,CAAC,CAAC,CAAC;AACH,SAASyB,kBAAkBA,CAACtB,MAAM,EAAE2D,KAAK,EAAE;EACzC,IAAIF,IAAI,GAAGzD,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;EAChC,IAAIwD,GAAG,GAAGG,KAAK;EACf,IAAIC,MAAM,GAAGH,IAAI,GAAGD,GAAG,GAAG,CAAC;EAC3BxD,MAAM,CAAC,CAAC,CAAC,IAAI4D,MAAM;EACnB5D,MAAM,CAAC,CAAC,CAAC,IAAI4D,MAAM;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASpB,oBAAoBA,CAACqB,IAAI,EAAE3B,WAAW,EAAEI,cAAc,EAAEnB,KAAK,EAAE;EACtE,IAAI2C,QAAQ,GAAG5B,WAAW,CAAC6B,MAAM;EACjC,IAAI,CAACF,IAAI,CAAC5D,MAAM,IAAIqC,cAAc,IAAI,CAACwB,QAAQ,EAAE;IAC/C;EACF;EACA,IAAIP,UAAU,GAAGM,IAAI,CAACjD,SAAS,CAAC,CAAC;EACjC,IAAIoD,IAAI;EACR,IAAIC,QAAQ;EACZ,IAAIH,QAAQ,KAAK,CAAC,EAAE;IAClB5B,WAAW,CAAC,CAAC,CAAC,CAAC5B,KAAK,GAAGiD,UAAU,CAAC,CAAC,CAAC;IACpCS,IAAI,GAAG9B,WAAW,CAAC,CAAC,CAAC,GAAG;MACtB5B,KAAK,EAAEiD,UAAU,CAAC,CAAC;IACrB,CAAC;EACH,CAAC,MAAM;IACL,IAAIW,QAAQ,GAAGhC,WAAW,CAAC4B,QAAQ,GAAG,CAAC,CAAC,CAACzB,SAAS,GAAGH,WAAW,CAAC,CAAC,CAAC,CAACG,SAAS;IAC7E,IAAI8B,OAAO,GAAG,CAACjC,WAAW,CAAC4B,QAAQ,GAAG,CAAC,CAAC,CAACxD,KAAK,GAAG4B,WAAW,CAAC,CAAC,CAAC,CAAC5B,KAAK,IAAI4D,QAAQ;IACjF9E,IAAI,CAAC8C,WAAW,EAAE,UAAUkC,SAAS,EAAE;MACrCA,SAAS,CAAC9D,KAAK,IAAI6D,OAAO,GAAG,CAAC;IAChC,CAAC,CAAC;IACF,IAAIrD,UAAU,GAAG+C,IAAI,CAAC9D,KAAK,CAACa,SAAS,CAAC,CAAC;IACvCqD,QAAQ,GAAG,CAAC,GAAGnD,UAAU,CAAC,CAAC,CAAC,GAAGoB,WAAW,CAAC4B,QAAQ,GAAG,CAAC,CAAC,CAACzB,SAAS;IAClE2B,IAAI,GAAG;MACL1D,KAAK,EAAE4B,WAAW,CAAC4B,QAAQ,GAAG,CAAC,CAAC,CAACxD,KAAK,GAAG6D,OAAO,GAAGF;IACrD,CAAC;IACD/B,WAAW,CAACmC,IAAI,CAACL,IAAI,CAAC;EACxB;EACA,IAAI9D,OAAO,GAAGqD,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;EAC3C;EACA,IAAIe,UAAU,CAACpC,WAAW,CAAC,CAAC,CAAC,CAAC5B,KAAK,EAAEiD,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IACnDpC,KAAK,GAAGe,WAAW,CAAC,CAAC,CAAC,CAAC5B,KAAK,GAAGiD,UAAU,CAAC,CAAC,CAAC,GAAGrB,WAAW,CAACqC,KAAK,CAAC,CAAC;EACpE;EACA,IAAIpD,KAAK,IAAImD,UAAU,CAACf,UAAU,CAAC,CAAC,CAAC,EAAErB,WAAW,CAAC,CAAC,CAAC,CAAC5B,KAAK,CAAC,EAAE;IAC5D4B,WAAW,CAACsC,OAAO,CAAC;MAClBlE,KAAK,EAAEiD,UAAU,CAAC,CAAC;IACrB,CAAC,CAAC;EACJ;EACA,IAAIe,UAAU,CAACf,UAAU,CAAC,CAAC,CAAC,EAAES,IAAI,CAAC1D,KAAK,CAAC,EAAE;IACzCa,KAAK,GAAG6C,IAAI,CAAC1D,KAAK,GAAGiD,UAAU,CAAC,CAAC,CAAC,GAAGrB,WAAW,CAACuC,GAAG,CAAC,CAAC;EACxD;EACA,IAAItD,KAAK,IAAImD,UAAU,CAACN,IAAI,CAAC1D,KAAK,EAAEiD,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IAClDrB,WAAW,CAACmC,IAAI,CAAC;MACf/D,KAAK,EAAEiD,UAAU,CAAC,CAAC;IACrB,CAAC,CAAC;EACJ;EACA,SAASe,UAAUA,CAACI,CAAC,EAAEC,CAAC,EAAE;IACxB;IACA;IACAD,CAAC,GAAGlF,KAAK,CAACkF,CAAC,CAAC;IACZC,CAAC,GAAGnF,KAAK,CAACmF,CAAC,CAAC;IACZ,OAAOzE,OAAO,GAAGwE,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAC;EAChC;AACF;AACA,eAAe9E,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}