-- Test data for e-Syndic application

-- Insert test syndics
INSERT INTO syndic (id, name, email, phone, address, created_at, updated_at) VALUES
(1, 'Syndic Central', '<EMAIL>', '+33123456789', '123 Rue de la Paix, Paris', NOW(), NOW()),
(2, 'Gestion Immobilière Pro', '<EMAIL>', '+33987654321', '456 Avenue des Champs, Lyon', NOW(), NOW());

-- Insert test buildings
INSERT INTO building (id, name, address, syndic_id, created_at, updated_at) VALUES
(1, 'Résidence Les Jardins', '10 Rue des Fleurs, Paris 75001', 1, NOW(), NOW()),
(2, 'Immeuble Moderne', '25 Boulevard Central, Lyon 69001', 1, NOW(), NOW()),
(3, 'Villa Prestige', '5 Avenue Luxury, Nice 06000', 2, NOW(), NOW());

-- Insert test apartments
INSERT INTO apartment (id, apartment_number, floor, building_id, owner_name, owner_email, owner_phone, apartment_code, created_at, updated_at) VALUES
(1, 'A101', 1, 1, '<PERSON>', '<EMAIL>', '+33111111111', 'APT001', NOW(), NOW()),
(2, 'A102', 1, 1, 'Marie Martin', '<EMAIL>', '+33222222222', 'APT002', NOW(), NOW()),
(3, 'B201', 2, 1, 'Pierre Durand', '<EMAIL>', '+33333333333', 'APT003', NOW(), NOW()),
(4, 'C301', 3, 2, 'Sophie Leroy', '<EMAIL>', '+33444444444', 'APT004', NOW(), NOW()),
(5, 'D401', 4, 3, 'Michel Bernard', '<EMAIL>', '+33555555555', 'APT005', NOW(), NOW());

-- Insert test users with encrypted passwords (password: 'password123')
INSERT INTO users (id, email, password, first_name, last_name, role, apartment_id, is_active, created_at, updated_at) VALUES
(1, '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imt7IGbTSO8nkL5uP4wyWsn8/efm', 'Admin', 'System', 'ADMIN', NULL, true, NOW(), NOW()),
(2, '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imt7IGbTSO8nkL5uP4wyWsn8/efm', 'Super', 'Admin', 'SUPERADMIN', NULL, true, NOW(), NOW()),
(3, '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imt7IGbTSO8nkL5uP4wyWsn8/efm', 'Jean', 'Dupont', 'OWNER', 1, true, NOW(), NOW()),
(4, '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imt7IGbTSO8nkL5uP4wyWsn8/efm', 'Marie', 'Martin', 'PRESIDENT', 2, true, NOW(), NOW()),
(5, '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imt7IGbTSO8nkL5uP4wyWsn8/efm', 'Pierre', 'Durand', 'RESIDENT', 3, true, NOW(), NOW()),
(6, '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imt7IGbTSO8nkL5uP4wyWsn8/efm', 'Sophie', 'Leroy', 'OWNER', 4, true, NOW(), NOW());

-- Insert test meetings
INSERT INTO meeting (id, title, description, meeting_date, building_id, created_by, created_at, updated_at) VALUES
(1, 'Assemblée Générale 2024', 'Assemblée générale annuelle des copropriétaires', '2024-03-15 14:00:00', 1, 1, NOW(), NOW()),
(2, 'Réunion Travaux', 'Discussion sur les travaux de rénovation', '2024-02-20 18:00:00', 1, 4, NOW(), NOW()),
(3, 'Budget 2024', 'Présentation et vote du budget annuel', '2024-01-10 19:00:00', 2, 1, NOW(), NOW());

-- Insert test incidents
INSERT INTO incident (id, title, description, status, priority, building_id, apartment_id, reported_by, created_at, updated_at) VALUES
(1, 'Fuite d''eau', 'Fuite dans la salle de bain', 'OPEN', 'HIGH', 1, 1, 3, NOW(), NOW()),
(2, 'Problème électrique', 'Panne de courant dans le couloir', 'IN_PROGRESS', 'MEDIUM', 1, NULL, 4, NOW(), NOW()),
(3, 'Ascenseur en panne', 'L''ascenseur ne fonctionne plus', 'RESOLVED', 'HIGH', 2, NULL, 6, NOW(), NOW());

-- Insert test payments
INSERT INTO payment (id, amount, payment_date, payment_type, description, apartment_id, created_at, updated_at) VALUES
(1, 150.00, '2024-01-15', 'CHARGES', 'Charges mensuelles Janvier 2024', 1, NOW(), NOW()),
(2, 150.00, '2024-01-15', 'CHARGES', 'Charges mensuelles Janvier 2024', 2, NOW(), NOW()),
(3, 200.00, '2024-01-20', 'SPECIAL', 'Travaux ascenseur', 3, NOW(), NOW()),
(4, 175.00, '2024-02-15', 'CHARGES', 'Charges mensuelles Février 2024', 4, NOW(), NOW());

-- Reset sequences
SELECT setval('syndic_id_seq', (SELECT MAX(id) FROM syndic));
SELECT setval('building_id_seq', (SELECT MAX(id) FROM building));
SELECT setval('apartment_id_seq', (SELECT MAX(id) FROM apartment));
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));
SELECT setval('meeting_id_seq', (SELECT MAX(id) FROM meeting));
SELECT setval('incident_id_seq', (SELECT MAX(id) FROM incident));
SELECT setval('payment_id_seq', (SELECT MAX(id) FROM payment));
