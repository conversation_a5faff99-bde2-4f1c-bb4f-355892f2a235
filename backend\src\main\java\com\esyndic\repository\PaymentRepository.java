package com.esyndic.repository;

import com.esyndic.entity.Payment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface PaymentRepository extends JpaRepository<Payment, UUID> {

    List<Payment> findByApartmentId(UUID apartmentId);

    List<Payment> findByUserId(UUID userId);

    List<Payment> findByMonthYear(String monthYear);

    List<Payment> findByApartmentIdAndMonthYear(UUID apartmentId, String monthYear);

    List<Payment> findByStatus(Payment.PaymentStatus status);

    List<Payment> findByPaymentType(Payment.PaymentType paymentType);

    @Query("SELECT p FROM Payment p WHERE p.apartment.building.id = :buildingId")
    List<Payment> findByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT p FROM Payment p WHERE p.apartment.building.id = :buildingId AND p.monthYear = :monthYear")
    List<Payment> findByBuildingIdAndMonthYear(@Param("buildingId") UUID buildingId, 
                                             @Param("monthYear") String monthYear);

    @Query("SELECT p FROM Payment p WHERE p.paymentDate >= :startDate AND p.paymentDate <= :endDate")
    List<Payment> findByPaymentDateBetween(@Param("startDate") LocalDateTime startDate, 
                                         @Param("endDate") LocalDateTime endDate);

    @Query("SELECT p FROM Payment p WHERE p.apartment.building.id = :buildingId AND " +
           "p.paymentDate >= :startDate AND p.paymentDate <= :endDate")
    List<Payment> findByBuildingIdAndPaymentDateBetween(@Param("buildingId") UUID buildingId,
                                                      @Param("startDate") LocalDateTime startDate,
                                                      @Param("endDate") LocalDateTime endDate);

    @Query("SELECT SUM(p.amount) FROM Payment p WHERE p.apartment.building.id = :buildingId AND p.status = 'COMPLETED'")
    BigDecimal getTotalPaymentsByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT SUM(p.amount) FROM Payment p WHERE p.apartment.building.id = :buildingId AND " +
           "p.monthYear = :monthYear AND p.status = 'COMPLETED'")
    BigDecimal getTotalPaymentsByBuildingIdAndMonthYear(@Param("buildingId") UUID buildingId, 
                                                      @Param("monthYear") String monthYear);

    @Query("SELECT SUM(p.amount) FROM Payment p WHERE p.apartment.id = :apartmentId AND p.status = 'COMPLETED'")
    BigDecimal getTotalPaymentsByApartmentId(@Param("apartmentId") UUID apartmentId);

    @Query("SELECT COUNT(p) FROM Payment p WHERE p.apartment.building.id = :buildingId AND p.status = 'COMPLETED'")
    long countCompletedPaymentsByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT COUNT(p) FROM Payment p WHERE p.apartment.building.id = :buildingId AND p.status = 'PENDING'")
    long countPendingPaymentsByBuildingId(@Param("buildingId") UUID buildingId);

    Optional<Payment> findByPaymeeTransactionId(String paymeeTransactionId);

    Optional<Payment> findByPaymeeReference(String paymeeReference);

    @Query("SELECT p FROM Payment p WHERE p.dueDate < :currentDate AND p.status != 'COMPLETED'")
    List<Payment> findOverduePayments(@Param("currentDate") LocalDateTime currentDate);

    @Query("SELECT p FROM Payment p WHERE p.apartment.building.id = :buildingId AND " +
           "p.dueDate < :currentDate AND p.status != 'COMPLETED'")
    List<Payment> findOverduePaymentsByBuildingId(@Param("buildingId") UUID buildingId, 
                                                @Param("currentDate") LocalDateTime currentDate);

    @Query("SELECT DISTINCT p.monthYear FROM Payment p WHERE p.apartment.building.id = :buildingId ORDER BY p.monthYear DESC")
    List<String> findDistinctMonthYearsByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT p FROM Payment p WHERE p.apartment.id = :apartmentId ORDER BY p.paymentDate DESC")
    List<Payment> findByApartmentIdOrderByPaymentDateDesc(@Param("apartmentId") UUID apartmentId);
}
