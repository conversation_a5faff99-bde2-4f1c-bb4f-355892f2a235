{"ast": null, "code": "import root from './_root.js';\nimport toInteger from './toInteger.js';\nimport toNumber from './toNumber.js';\nimport toString from './toString.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeIsFinite = root.isFinite,\n  nativeMin = Math.min;\n\n/**\n * Creates a function like `_.round`.\n *\n * @private\n * @param {string} methodName The name of the `Math` method to use when rounding.\n * @returns {Function} Returns the new round function.\n */\nfunction createRound(methodName) {\n  var func = Math[methodName];\n  return function (number, precision) {\n    number = toNumber(number);\n    precision = precision == null ? 0 : nativeMin(toInteger(precision), 292);\n    if (precision && nativeIsFinite(number)) {\n      // Shift with exponential notation to avoid floating-point issues.\n      // See [MDN](https://mdn.io/round#Examples) for more details.\n      var pair = (toString(number) + 'e').split('e'),\n        value = func(pair[0] + 'e' + (+pair[1] + precision));\n      pair = (toString(value) + 'e').split('e');\n      return +(pair[0] + 'e' + (+pair[1] - precision));\n    }\n    return func(number);\n  };\n}\nexport default createRound;", "map": {"version": 3, "names": ["root", "toInteger", "toNumber", "toString", "nativeIsFinite", "isFinite", "nativeMin", "Math", "min", "createRound", "methodName", "func", "number", "precision", "pair", "split", "value"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_createRound.js"], "sourcesContent": ["import root from './_root.js';\nimport toInteger from './toInteger.js';\nimport toNumber from './toNumber.js';\nimport toString from './toString.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeIsFinite = root.isFinite,\n    nativeMin = Math.min;\n\n/**\n * Creates a function like `_.round`.\n *\n * @private\n * @param {string} methodName The name of the `Math` method to use when rounding.\n * @returns {Function} Returns the new round function.\n */\nfunction createRound(methodName) {\n  var func = Math[methodName];\n  return function(number, precision) {\n    number = toNumber(number);\n    precision = precision == null ? 0 : nativeMin(toInteger(precision), 292);\n    if (precision && nativeIsFinite(number)) {\n      // Shift with exponential notation to avoid floating-point issues.\n      // See [MDN](https://mdn.io/round#Examples) for more details.\n      var pair = (toString(number) + 'e').split('e'),\n          value = func(pair[0] + 'e' + (+pair[1] + precision));\n\n      pair = (toString(value) + 'e').split('e');\n      return +(pair[0] + 'e' + (+pair[1] - precision));\n    }\n    return func(number);\n  };\n}\n\nexport default createRound;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAC7B,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,cAAc,GAAGJ,IAAI,CAACK,QAAQ;EAC9BC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,UAAU,EAAE;EAC/B,IAAIC,IAAI,GAAGJ,IAAI,CAACG,UAAU,CAAC;EAC3B,OAAO,UAASE,MAAM,EAAEC,SAAS,EAAE;IACjCD,MAAM,GAAGV,QAAQ,CAACU,MAAM,CAAC;IACzBC,SAAS,GAAGA,SAAS,IAAI,IAAI,GAAG,CAAC,GAAGP,SAAS,CAACL,SAAS,CAACY,SAAS,CAAC,EAAE,GAAG,CAAC;IACxE,IAAIA,SAAS,IAAIT,cAAc,CAACQ,MAAM,CAAC,EAAE;MACvC;MACA;MACA,IAAIE,IAAI,GAAG,CAACX,QAAQ,CAACS,MAAM,CAAC,GAAG,GAAG,EAAEG,KAAK,CAAC,GAAG,CAAC;QAC1CC,KAAK,GAAGL,IAAI,CAACG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,GAAGD,SAAS,CAAC,CAAC;MAExDC,IAAI,GAAG,CAACX,QAAQ,CAACa,KAAK,CAAC,GAAG,GAAG,EAAED,KAAK,CAAC,GAAG,CAAC;MACzC,OAAO,EAAED,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,GAAGD,SAAS,CAAC,CAAC;IAClD;IACA,OAAOF,IAAI,CAACC,MAAM,CAAC;EACrB,CAAC;AACH;AAEA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}