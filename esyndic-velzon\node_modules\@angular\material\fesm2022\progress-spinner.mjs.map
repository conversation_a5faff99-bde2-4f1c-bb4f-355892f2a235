{"version": 3, "file": "progress-spinner.mjs", "sources": ["../../../../../../src/material/progress-spinner/progress-spinner.ts", "../../../../../../src/material/progress-spinner/progress-spinner.html", "../../../../../../src/material/progress-spinner/module.ts", "../../../../../../src/material/progress-spinner/progress-spinner_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  Inject,\n  InjectionToken,\n  Input,\n  Optional,\n  ViewChild,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {CanColor, mixinColor, ThemePalette} from '@angular/material/core';\nimport {ANIMATION_MODULE_TYPE} from '@angular/platform-browser/animations';\nimport {coerceNumberProperty, NumberInput} from '@angular/cdk/coercion';\n\n// Boilerplate for applying mixins to MatProgressBar.\nconst _MatProgressSpinnerBase = mixinColor(\n  class {\n    constructor(public _elementRef: ElementRef) {}\n  },\n  'primary',\n);\n\n/** Possible mode for a progress spinner. */\nexport type ProgressSpinnerMode = 'determinate' | 'indeterminate';\n\n/** Default `mat-progress-spinner` options that can be overridden. */\nexport interface MatProgressSpinnerDefaultOptions {\n  /** Default color of the spinner. */\n  color?: ThemePalette;\n  /** Diameter of the spinner. */\n  diameter?: number;\n  /** Width of the spinner's stroke. */\n  strokeWidth?: number;\n  /**\n   * Whether the animations should be force to be enabled, ignoring if the current environment is\n   * using NoopAnimationsModule.\n   */\n  _forceAnimations?: boolean;\n}\n\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nexport const MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS =\n  new InjectionToken<MatProgressSpinnerDefaultOptions>('mat-progress-spinner-default-options', {\n    providedIn: 'root',\n    factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY,\n  });\n\n/** @docs-private */\nexport function MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY(): MatProgressSpinnerDefaultOptions {\n  return {diameter: BASE_SIZE};\n}\n\n/**\n * Base reference size of the spinner.\n */\nconst BASE_SIZE = 100;\n\n/**\n * Base reference stroke width of the spinner.\n */\nconst BASE_STROKE_WIDTH = 10;\n\n@Component({\n  selector: 'mat-progress-spinner, mat-spinner',\n  exportAs: 'matProgressSpinner',\n  host: {\n    'role': 'progressbar',\n    'class': 'mat-mdc-progress-spinner mdc-circular-progress',\n    // set tab index to -1 so screen readers will read the aria-label\n    // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n    'tabindex': '-1',\n    '[class._mat-animation-noopable]': `_noopAnimations`,\n    '[class.mdc-circular-progress--indeterminate]': 'mode === \"indeterminate\"',\n    '[style.width.px]': 'diameter',\n    '[style.height.px]': 'diameter',\n    '[style.--mdc-circular-progress-size]': 'diameter + \"px\"',\n    '[style.--mdc-circular-progress-active-indicator-width]': 'diameter + \"px\"',\n    '[attr.aria-valuemin]': '0',\n    '[attr.aria-valuemax]': '100',\n    '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n    '[attr.mode]': 'mode',\n  },\n  inputs: ['color'],\n  templateUrl: 'progress-spinner.html',\n  styleUrls: ['progress-spinner.css'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n})\nexport class MatProgressSpinner extends _MatProgressSpinnerBase implements CanColor {\n  /** Whether the _mat-animation-noopable class should be applied, disabling animations.  */\n  _noopAnimations: boolean;\n\n  /** The element of the determinate spinner. */\n  @ViewChild('determinateSpinner') _determinateCircle: ElementRef<HTMLElement>;\n\n  constructor(\n    elementRef: ElementRef<HTMLElement>,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode: string,\n    @Inject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS)\n    defaults?: MatProgressSpinnerDefaultOptions,\n  ) {\n    super(elementRef);\n    this._noopAnimations =\n      animationMode === 'NoopAnimations' && !!defaults && !defaults._forceAnimations;\n\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this.defaultColor = defaults.color;\n      }\n\n      if (defaults.diameter) {\n        this.diameter = defaults.diameter;\n      }\n\n      if (defaults.strokeWidth) {\n        this.strokeWidth = defaults.strokeWidth;\n      }\n    }\n  }\n\n  /**\n   * Mode of the progress bar.\n   *\n   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n   * 'determinate'.\n   * Mirrored to mode attribute.\n   */\n  @Input() mode: ProgressSpinnerMode =\n    this._elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner'\n      ? 'indeterminate'\n      : 'determinate';\n\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  @Input()\n  get value(): number {\n    return this.mode === 'determinate' ? this._value : 0;\n  }\n  set value(v: NumberInput) {\n    this._value = Math.max(0, Math.min(100, coerceNumberProperty(v)));\n  }\n  private _value = 0;\n\n  /** The diameter of the progress spinner (will set width and height of svg). */\n  @Input()\n  get diameter(): number {\n    return this._diameter;\n  }\n  set diameter(size: NumberInput) {\n    this._diameter = coerceNumberProperty(size);\n  }\n  private _diameter = BASE_SIZE;\n\n  /** Stroke width of the progress spinner. */\n  @Input()\n  get strokeWidth(): number {\n    return this._strokeWidth ?? this.diameter / 10;\n  }\n  set strokeWidth(value: NumberInput) {\n    this._strokeWidth = coerceNumberProperty(value);\n  }\n  private _strokeWidth: number;\n\n  /** The radius of the spinner, adjusted for stroke width. */\n  _circleRadius(): number {\n    return (this.diameter - BASE_STROKE_WIDTH) / 2;\n  }\n\n  /** The view box of the spinner's svg element. */\n  _viewBox() {\n    const viewBox = this._circleRadius() * 2 + this.strokeWidth;\n    return `0 0 ${viewBox} ${viewBox}`;\n  }\n\n  /** The stroke circumference of the svg circle. */\n  _strokeCircumference(): number {\n    return 2 * Math.PI * this._circleRadius();\n  }\n\n  /** The dash offset of the svg circle. */\n  _strokeDashOffset() {\n    if (this.mode === 'determinate') {\n      return (this._strokeCircumference() * (100 - this._value)) / 100;\n    }\n    return null;\n  }\n\n  /** Stroke width of the circle in percent. */\n  _circleStrokeWidth() {\n    return (this.strokeWidth / this.diameter) * 100;\n  }\n}\n\n/**\n * @deprecated Import Progress Spinner instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 16.0.0\n */\n// tslint:disable-next-line:variable-name\nexport const MatSpinner = MatProgressSpinner;\n", "<ng-template #circle>\n  <svg [attr.viewBox]=\"_viewBox()\" class=\"mdc-circular-progress__indeterminate-circle-graphic\"\n       xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\">\n    <circle [attr.r]=\"_circleRadius()\"\n            [style.stroke-dasharray.px]=\"_strokeCircumference()\"\n            [style.stroke-dashoffset.px]=\"_strokeCircumference() / 2\"\n            [style.stroke-width.%]=\"_circleStrokeWidth()\"\n            cx=\"50%\" cy=\"50%\"/>\n  </svg>\n</ng-template>\n\n<!--\n  All children need to be hidden for screen readers in order to support ChromeVox.\n  More context in the issue: https://github.com/angular/components/issues/22165.\n-->\n<div class=\"mdc-circular-progress__determinate-container\" aria-hidden=\"true\" #determinateSpinner>\n  <svg [attr.viewBox]=\"_viewBox()\" class=\"mdc-circular-progress__determinate-circle-graphic\"\n       xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\">\n    <circle [attr.r]=\"_circleRadius()\"\n            [style.stroke-dasharray.px]=\"_strokeCircumference()\"\n            [style.stroke-dashoffset.px]=\"_strokeDashOffset()\"\n            [style.stroke-width.%]=\"_circleStrokeWidth()\"\n            class=\"mdc-circular-progress__determinate-circle\"\n            cx=\"50%\" cy=\"50%\"/>\n  </svg>\n</div>\n<!--TODO: figure out why there are 3 separate svgs-->\n<div class=\"mdc-circular-progress__indeterminate-container\" aria-hidden=\"true\">\n  <div class=\"mdc-circular-progress__spinner-layer\">\n    <div class=\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\">\n      <ng-container [ngTemplateOutlet]=\"circle\"></ng-container>\n    </div>\n    <div class=\"mdc-circular-progress__gap-patch\">\n      <ng-container [ngTemplateOutlet]=\"circle\"></ng-container>\n    </div>\n    <div class=\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\">\n      <ng-container [ngTemplateOutlet]=\"circle\"></ng-container>\n    </div>\n  </div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {<PERSON><PERSON><PERSON><PERSON><PERSON>pin<PERSON>, MatSpinner} from './progress-spinner';\nimport {CommonModule} from '@angular/common';\n\n@NgModule({\n  imports: [CommonModule],\n  exports: [Mat<PERSON>rogressSpinner, MatSpinner, MatCommonModule],\n  declarations: [MatProgressSpinner, MatSpinner],\n})\nexport class MatProgressSpinnerModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;AAuBA;AACA,MAAM,uBAAuB,GAAG,UAAU,CACxC,MAAA;AACE,IAAA,WAAA,CAAmB,WAAuB,EAAA;QAAvB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;KAAI;CAC/C,EACD,SAAS,CACV,CAAC;AAoBF;MACa,oCAAoC,GAC/C,IAAI,cAAc,CAAmC,sCAAsC,EAAE;AAC3F,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,4CAA4C;AACtD,CAAA,EAAE;AAEL;SACgB,4CAA4C,GAAA;AAC1D,IAAA,OAAO,EAAC,QAAQ,EAAE,SAAS,EAAC,CAAC;AAC/B,CAAC;AAED;;AAEG;AACH,MAAM,SAAS,GAAG,GAAG,CAAC;AAEtB;;AAEG;AACH,MAAM,iBAAiB,GAAG,EAAE,CAAC;AA4BvB,MAAO,kBAAmB,SAAQ,uBAAuB,CAAA;AAO7D,IAAA,WAAA,CACE,UAAmC,EACQ,aAAqB,EAEhE,QAA2C,EAAA;QAE3C,KAAK,CAAC,UAAU,CAAC,CAAC;AAmBpB;;;;;;AAMG;AACM,QAAA,IAAA,CAAA,IAAI,GACX,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,aAAa;AACrE,cAAE,eAAe;cACf,aAAa,CAAC;QAUZ,IAAM,CAAA,MAAA,GAAG,CAAC,CAAC;QAUX,IAAS,CAAA,SAAA,GAAG,SAAS,CAAC;AAhD5B,QAAA,IAAI,CAAC,eAAe;YAClB,aAAa,KAAK,gBAAgB,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;AAEjF,QAAA,IAAI,QAAQ,EAAE;YACZ,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC;AACjD,aAAA;YAED,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACrB,gBAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;AACnC,aAAA;YAED,IAAI,QAAQ,CAAC,WAAW,EAAE;AACxB,gBAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;AACzC,aAAA;AACF,SAAA;KACF;;AAeD,IAAA,IACI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,IAAI,KAAK,aAAa,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;KACtD;IACD,IAAI,KAAK,CAAC,CAAc,EAAA;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACnE;;AAID,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,IAAiB,EAAA;AAC5B,QAAA,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;KAC7C;;AAID,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;KAChD;IACD,IAAI,WAAW,CAAC,KAAkB,EAAA;AAChC,QAAA,IAAI,CAAC,YAAY,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;KACjD;;IAID,aAAa,GAAA;QACX,OAAO,CAAC,IAAI,CAAC,QAAQ,GAAG,iBAAiB,IAAI,CAAC,CAAC;KAChD;;IAGD,QAAQ,GAAA;AACN,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;AAC5D,QAAA,OAAO,CAAO,IAAA,EAAA,OAAO,CAAI,CAAA,EAAA,OAAO,EAAE,CAAC;KACpC;;IAGD,oBAAoB,GAAA;QAClB,OAAO,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;KAC3C;;IAGD,iBAAiB,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,EAAE;AAC/B,YAAA,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;AAClE,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACb;;IAGD,kBAAkB,GAAA;QAChB,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,CAAC;KACjD;8GArGU,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EASP,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACjC,oCAAoC,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAVnC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,g9BCjG/B,28DAwCA,EAAA,MAAA,EAAA,CAAA,goOAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDyDa,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBA1B9B,SAAS;+BACE,mCAAmC,EAAA,QAAA,EACnC,oBAAoB,EACxB,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,aAAa;AACrB,wBAAA,OAAO,EAAE,gDAAgD;;;AAGzD,wBAAA,UAAU,EAAE,IAAI;AAChB,wBAAA,iCAAiC,EAAE,CAAiB,eAAA,CAAA;AACpD,wBAAA,8CAA8C,EAAE,0BAA0B;AAC1E,wBAAA,kBAAkB,EAAE,UAAU;AAC9B,wBAAA,mBAAmB,EAAE,UAAU;AAC/B,wBAAA,sCAAsC,EAAE,iBAAiB;AACzD,wBAAA,wDAAwD,EAAE,iBAAiB;AAC3E,wBAAA,sBAAsB,EAAE,GAAG;AAC3B,wBAAA,sBAAsB,EAAE,KAAK;AAC7B,wBAAA,sBAAsB,EAAE,uCAAuC;AAC/D,wBAAA,aAAa,EAAE,MAAM;qBACtB,EACO,MAAA,EAAA,CAAC,OAAO,CAAC,EAGA,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,28DAAA,EAAA,MAAA,EAAA,CAAA,goOAAA,CAAA,EAAA,CAAA;;0BAWlC,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,MAAM;2BAAC,oCAAoC,CAAA;4CALb,kBAAkB,EAAA,CAAA;sBAAlD,SAAS;uBAAC,oBAAoB,CAAA;gBAkCtB,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAOF,KAAK,EAAA,CAAA;sBADR,KAAK;gBAWF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAWF,WAAW,EAAA,CAAA;sBADd,KAAK;;AAuCR;;;;AAIG;AACH;AACO,MAAM,UAAU,GAAG;;ME7Lb,wBAAwB,CAAA;8GAAxB,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAxB,wBAAwB,EAAA,YAAA,EAAA,CAFpB,kBAAkB,EAAE,UAAU,CAAA,EAAA,OAAA,EAAA,CAFnC,YAAY,CAAA,EAAA,OAAA,EAAA,CACZ,kBAAkB,EAAE,UAAU,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;+GAG9C,wBAAwB,EAAA,OAAA,EAAA,CAJzB,YAAY,EACoB,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAG9C,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBALpC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,OAAO,EAAE,CAAC,kBAAkB,EAAE,UAAU,EAAE,eAAe,CAAC;AAC1D,oBAAA,YAAY,EAAE,CAAC,kBAAkB,EAAE,UAAU,CAAC;AAC/C,iBAAA,CAAA;;;ACjBD;;AAEG;;;;"}