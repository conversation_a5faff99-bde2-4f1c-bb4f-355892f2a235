{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\n/**\n * Line path for bezier and straight line draw\n */\nimport * as graphic from '../../util/graphic.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nvar straightLineProto = graphic.Line.prototype;\nvar bezierCurveProto = graphic.BezierCurve.prototype;\nvar StraightLineShape = /** @class */function () {\n  function StraightLineShape() {\n    // Start point\n    this.x1 = 0;\n    this.y1 = 0;\n    // End point\n    this.x2 = 0;\n    this.y2 = 0;\n    this.percent = 1;\n  }\n  return StraightLineShape;\n}();\nvar CurveShape = /** @class */function (_super) {\n  __extends(CurveShape, _super);\n  function CurveShape() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return CurveShape;\n}(StraightLineShape);\nfunction isStraightLine(shape) {\n  return isNaN(+shape.cpx1) || isNaN(+shape.cpy1);\n}\nvar ECLinePath = /** @class */function (_super) {\n  __extends(ECLinePath, _super);\n  function ECLinePath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'ec-line';\n    return _this;\n  }\n  ECLinePath.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  ECLinePath.prototype.getDefaultShape = function () {\n    return new StraightLineShape();\n  };\n  ECLinePath.prototype.buildPath = function (ctx, shape) {\n    if (isStraightLine(shape)) {\n      straightLineProto.buildPath.call(this, ctx, shape);\n    } else {\n      bezierCurveProto.buildPath.call(this, ctx, shape);\n    }\n  };\n  ECLinePath.prototype.pointAt = function (t) {\n    if (isStraightLine(this.shape)) {\n      return straightLineProto.pointAt.call(this, t);\n    } else {\n      return bezierCurveProto.pointAt.call(this, t);\n    }\n  };\n  ECLinePath.prototype.tangentAt = function (t) {\n    var shape = this.shape;\n    var p = isStraightLine(shape) ? [shape.x2 - shape.x1, shape.y2 - shape.y1] : bezierCurveProto.tangentAt.call(this, t);\n    return vec2.normalize(p, p);\n  };\n  return ECLinePath;\n}(graphic.Path);\nexport default ECLinePath;", "map": {"version": 3, "names": ["__extends", "graphic", "vec2", "straightLineProto", "Line", "prototype", "bezierCurveProto", "BezierCurve", "StraightLineShape", "x1", "y1", "x2", "y2", "percent", "CurveShape", "_super", "apply", "arguments", "isStraightLine", "shape", "isNaN", "cpx1", "cpy1", "ECLinePath", "opts", "_this", "call", "type", "getDefaultStyle", "stroke", "fill", "getDefaultShape", "buildPath", "ctx", "pointAt", "t", "tangentAt", "p", "normalize", "Path"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/chart/helper/LinePath.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\n/**\n * Line path for bezier and straight line draw\n */\nimport * as graphic from '../../util/graphic.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nvar straightLineProto = graphic.Line.prototype;\nvar bezierCurveProto = graphic.BezierCurve.prototype;\nvar StraightLineShape = /** @class */function () {\n  function StraightLineShape() {\n    // Start point\n    this.x1 = 0;\n    this.y1 = 0;\n    // End point\n    this.x2 = 0;\n    this.y2 = 0;\n    this.percent = 1;\n  }\n  return StraightLineShape;\n}();\nvar CurveShape = /** @class */function (_super) {\n  __extends(CurveShape, _super);\n  function CurveShape() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return CurveShape;\n}(StraightLineShape);\nfunction isStraightLine(shape) {\n  return isNaN(+shape.cpx1) || isNaN(+shape.cpy1);\n}\nvar ECLinePath = /** @class */function (_super) {\n  __extends(ECLinePath, _super);\n  function ECLinePath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'ec-line';\n    return _this;\n  }\n  ECLinePath.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  ECLinePath.prototype.getDefaultShape = function () {\n    return new StraightLineShape();\n  };\n  ECLinePath.prototype.buildPath = function (ctx, shape) {\n    if (isStraightLine(shape)) {\n      straightLineProto.buildPath.call(this, ctx, shape);\n    } else {\n      bezierCurveProto.buildPath.call(this, ctx, shape);\n    }\n  };\n  ECLinePath.prototype.pointAt = function (t) {\n    if (isStraightLine(this.shape)) {\n      return straightLineProto.pointAt.call(this, t);\n    } else {\n      return bezierCurveProto.pointAt.call(this, t);\n    }\n  };\n  ECLinePath.prototype.tangentAt = function (t) {\n    var shape = this.shape;\n    var p = isStraightLine(shape) ? [shape.x2 - shape.x1, shape.y2 - shape.y1] : bezierCurveProto.tangentAt.call(this, t);\n    return vec2.normalize(p, p);\n  };\n  return ECLinePath;\n}(graphic.Path);\nexport default ECLinePath;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA;AACA;AACA,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,IAAI,MAAM,4BAA4B;AAClD,IAAIC,iBAAiB,GAAGF,OAAO,CAACG,IAAI,CAACC,SAAS;AAC9C,IAAIC,gBAAgB,GAAGL,OAAO,CAACM,WAAW,CAACF,SAAS;AACpD,IAAIG,iBAAiB,GAAG,aAAa,YAAY;EAC/C,SAASA,iBAAiBA,CAAA,EAAG;IAC3B;IACA,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX;IACA,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,OAAO,GAAG,CAAC;EAClB;EACA,OAAOL,iBAAiB;AAC1B,CAAC,CAAC,CAAC;AACH,IAAIM,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9Cf,SAAS,CAACc,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IACpB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACA,OAAOH,UAAU;AACnB,CAAC,CAACN,iBAAiB,CAAC;AACpB,SAASU,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAOC,KAAK,CAAC,CAACD,KAAK,CAACE,IAAI,CAAC,IAAID,KAAK,CAAC,CAACD,KAAK,CAACG,IAAI,CAAC;AACjD;AACA,IAAIC,UAAU,GAAG,aAAa,UAAUR,MAAM,EAAE;EAC9Cf,SAAS,CAACuB,UAAU,EAAER,MAAM,CAAC;EAC7B,SAASQ,UAAUA,CAACC,IAAI,EAAE;IACxB,IAAIC,KAAK,GAAGV,MAAM,CAACW,IAAI,CAAC,IAAI,EAAEF,IAAI,CAAC,IAAI,IAAI;IAC3CC,KAAK,CAACE,IAAI,GAAG,SAAS;IACtB,OAAOF,KAAK;EACd;EACAF,UAAU,CAAClB,SAAS,CAACuB,eAAe,GAAG,YAAY;IACjD,OAAO;MACLC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE;IACR,CAAC;EACH,CAAC;EACDP,UAAU,CAAClB,SAAS,CAAC0B,eAAe,GAAG,YAAY;IACjD,OAAO,IAAIvB,iBAAiB,CAAC,CAAC;EAChC,CAAC;EACDe,UAAU,CAAClB,SAAS,CAAC2B,SAAS,GAAG,UAAUC,GAAG,EAAEd,KAAK,EAAE;IACrD,IAAID,cAAc,CAACC,KAAK,CAAC,EAAE;MACzBhB,iBAAiB,CAAC6B,SAAS,CAACN,IAAI,CAAC,IAAI,EAAEO,GAAG,EAAEd,KAAK,CAAC;IACpD,CAAC,MAAM;MACLb,gBAAgB,CAAC0B,SAAS,CAACN,IAAI,CAAC,IAAI,EAAEO,GAAG,EAAEd,KAAK,CAAC;IACnD;EACF,CAAC;EACDI,UAAU,CAAClB,SAAS,CAAC6B,OAAO,GAAG,UAAUC,CAAC,EAAE;IAC1C,IAAIjB,cAAc,CAAC,IAAI,CAACC,KAAK,CAAC,EAAE;MAC9B,OAAOhB,iBAAiB,CAAC+B,OAAO,CAACR,IAAI,CAAC,IAAI,EAAES,CAAC,CAAC;IAChD,CAAC,MAAM;MACL,OAAO7B,gBAAgB,CAAC4B,OAAO,CAACR,IAAI,CAAC,IAAI,EAAES,CAAC,CAAC;IAC/C;EACF,CAAC;EACDZ,UAAU,CAAClB,SAAS,CAAC+B,SAAS,GAAG,UAAUD,CAAC,EAAE;IAC5C,IAAIhB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIkB,CAAC,GAAGnB,cAAc,CAACC,KAAK,CAAC,GAAG,CAACA,KAAK,CAACR,EAAE,GAAGQ,KAAK,CAACV,EAAE,EAAEU,KAAK,CAACP,EAAE,GAAGO,KAAK,CAACT,EAAE,CAAC,GAAGJ,gBAAgB,CAAC8B,SAAS,CAACV,IAAI,CAAC,IAAI,EAAES,CAAC,CAAC;IACrH,OAAOjC,IAAI,CAACoC,SAAS,CAACD,CAAC,EAAEA,CAAC,CAAC;EAC7B,CAAC;EACD,OAAOd,UAAU;AACnB,CAAC,CAACtB,OAAO,CAACsC,IAAI,CAAC;AACf,eAAehB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}