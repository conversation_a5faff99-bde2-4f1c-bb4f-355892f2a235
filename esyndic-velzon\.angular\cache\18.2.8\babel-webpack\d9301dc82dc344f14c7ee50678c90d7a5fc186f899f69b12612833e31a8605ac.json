{"ast": null, "code": "import LodashWrapper from './_LodashWrapper.js';\nimport flatRest from './_flatRest.js';\nimport getData from './_getData.js';\nimport getFuncName from './_getFuncName.js';\nimport isArray from './isArray.js';\nimport isLaziable from './_isLaziable.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_CURRY_FLAG = 8,\n  WRAP_PARTIAL_FLAG = 32,\n  WRAP_ARY_FLAG = 128,\n  WRAP_REARG_FLAG = 256;\n\n/**\n * Creates a `_.flow` or `_.flowRight` function.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new flow function.\n */\nfunction createFlow(fromRight) {\n  return flatRest(function (funcs) {\n    var length = funcs.length,\n      index = length,\n      prereq = LodashWrapper.prototype.thru;\n    if (fromRight) {\n      funcs.reverse();\n    }\n    while (index--) {\n      var func = funcs[index];\n      if (typeof func != 'function') {\n        throw new TypeError(FUNC_ERROR_TEXT);\n      }\n      if (prereq && !wrapper && getFuncName(func) == 'wrapper') {\n        var wrapper = new LodashWrapper([], true);\n      }\n    }\n    index = wrapper ? index : length;\n    while (++index < length) {\n      func = funcs[index];\n      var funcName = getFuncName(func),\n        data = funcName == 'wrapper' ? getData(func) : undefined;\n      if (data && isLaziable(data[0]) && data[1] == (WRAP_ARY_FLAG | WRAP_CURRY_FLAG | WRAP_PARTIAL_FLAG | WRAP_REARG_FLAG) && !data[4].length && data[9] == 1) {\n        wrapper = wrapper[getFuncName(data[0])].apply(wrapper, data[3]);\n      } else {\n        wrapper = func.length == 1 && isLaziable(func) ? wrapper[funcName]() : wrapper.thru(func);\n      }\n    }\n    return function () {\n      var args = arguments,\n        value = args[0];\n      if (wrapper && args.length == 1 && isArray(value)) {\n        return wrapper.plant(value).value();\n      }\n      var index = 0,\n        result = length ? funcs[index].apply(this, args) : value;\n      while (++index < length) {\n        result = funcs[index].call(this, result);\n      }\n      return result;\n    };\n  });\n}\nexport default createFlow;", "map": {"version": 3, "names": ["LodashWrapper", "flatRest", "getData", "getFuncName", "isArray", "isLaziable", "FUNC_ERROR_TEXT", "WRAP_CURRY_FLAG", "WRAP_PARTIAL_FLAG", "WRAP_ARY_FLAG", "WRAP_REARG_FLAG", "createFlow", "fromRight", "funcs", "length", "index", "prereq", "prototype", "thru", "reverse", "func", "TypeError", "wrapper", "funcName", "data", "undefined", "apply", "args", "arguments", "value", "plant", "result", "call"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_createFlow.js"], "sourcesContent": ["import LodashWrapper from './_LodashWrapper.js';\nimport flatRest from './_flatRest.js';\nimport getData from './_getData.js';\nimport getFuncName from './_getFuncName.js';\nimport isArray from './isArray.js';\nimport isLaziable from './_isLaziable.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_CURRY_FLAG = 8,\n    WRAP_PARTIAL_FLAG = 32,\n    WRAP_ARY_FLAG = 128,\n    WRAP_REARG_FLAG = 256;\n\n/**\n * Creates a `_.flow` or `_.flowRight` function.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new flow function.\n */\nfunction createFlow(fromRight) {\n  return flatRest(function(funcs) {\n    var length = funcs.length,\n        index = length,\n        prereq = LodashWrapper.prototype.thru;\n\n    if (fromRight) {\n      funcs.reverse();\n    }\n    while (index--) {\n      var func = funcs[index];\n      if (typeof func != 'function') {\n        throw new TypeError(FUNC_ERROR_TEXT);\n      }\n      if (prereq && !wrapper && getFuncName(func) == 'wrapper') {\n        var wrapper = new LodashWrapper([], true);\n      }\n    }\n    index = wrapper ? index : length;\n    while (++index < length) {\n      func = funcs[index];\n\n      var funcName = getFuncName(func),\n          data = funcName == 'wrapper' ? getData(func) : undefined;\n\n      if (data && isLaziable(data[0]) &&\n            data[1] == (WRAP_ARY_FLAG | WRAP_CURRY_FLAG | WRAP_PARTIAL_FLAG | WRAP_REARG_FLAG) &&\n            !data[4].length && data[9] == 1\n          ) {\n        wrapper = wrapper[getFuncName(data[0])].apply(wrapper, data[3]);\n      } else {\n        wrapper = (func.length == 1 && isLaziable(func))\n          ? wrapper[funcName]()\n          : wrapper.thru(func);\n      }\n    }\n    return function() {\n      var args = arguments,\n          value = args[0];\n\n      if (wrapper && args.length == 1 && isArray(value)) {\n        return wrapper.plant(value).value();\n      }\n      var index = 0,\n          result = length ? funcs[index].apply(this, args) : value;\n\n      while (++index < length) {\n        result = funcs[index].call(this, result);\n      }\n      return result;\n    };\n  });\n}\n\nexport default createFlow;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,UAAU,MAAM,kBAAkB;;AAEzC;AACA,IAAIC,eAAe,GAAG,qBAAqB;;AAE3C;AACA,IAAIC,eAAe,GAAG,CAAC;EACnBC,iBAAiB,GAAG,EAAE;EACtBC,aAAa,GAAG,GAAG;EACnBC,eAAe,GAAG,GAAG;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,SAAS,EAAE;EAC7B,OAAOX,QAAQ,CAAC,UAASY,KAAK,EAAE;IAC9B,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;MACrBC,KAAK,GAAGD,MAAM;MACdE,MAAM,GAAGhB,aAAa,CAACiB,SAAS,CAACC,IAAI;IAEzC,IAAIN,SAAS,EAAE;MACbC,KAAK,CAACM,OAAO,CAAC,CAAC;IACjB;IACA,OAAOJ,KAAK,EAAE,EAAE;MACd,IAAIK,IAAI,GAAGP,KAAK,CAACE,KAAK,CAAC;MACvB,IAAI,OAAOK,IAAI,IAAI,UAAU,EAAE;QAC7B,MAAM,IAAIC,SAAS,CAACf,eAAe,CAAC;MACtC;MACA,IAAIU,MAAM,IAAI,CAACM,OAAO,IAAInB,WAAW,CAACiB,IAAI,CAAC,IAAI,SAAS,EAAE;QACxD,IAAIE,OAAO,GAAG,IAAItB,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC;MAC3C;IACF;IACAe,KAAK,GAAGO,OAAO,GAAGP,KAAK,GAAGD,MAAM;IAChC,OAAO,EAAEC,KAAK,GAAGD,MAAM,EAAE;MACvBM,IAAI,GAAGP,KAAK,CAACE,KAAK,CAAC;MAEnB,IAAIQ,QAAQ,GAAGpB,WAAW,CAACiB,IAAI,CAAC;QAC5BI,IAAI,GAAGD,QAAQ,IAAI,SAAS,GAAGrB,OAAO,CAACkB,IAAI,CAAC,GAAGK,SAAS;MAE5D,IAAID,IAAI,IAAInB,UAAU,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC,IACzBA,IAAI,CAAC,CAAC,CAAC,KAAKf,aAAa,GAAGF,eAAe,GAAGC,iBAAiB,GAAGE,eAAe,CAAC,IAClF,CAACc,IAAI,CAAC,CAAC,CAAC,CAACV,MAAM,IAAIU,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAC/B;QACJF,OAAO,GAAGA,OAAO,CAACnB,WAAW,CAACqB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAACE,KAAK,CAACJ,OAAO,EAAEE,IAAI,CAAC,CAAC,CAAC,CAAC;MACjE,CAAC,MAAM;QACLF,OAAO,GAAIF,IAAI,CAACN,MAAM,IAAI,CAAC,IAAIT,UAAU,CAACe,IAAI,CAAC,GAC3CE,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC,GACnBD,OAAO,CAACJ,IAAI,CAACE,IAAI,CAAC;MACxB;IACF;IACA,OAAO,YAAW;MAChB,IAAIO,IAAI,GAAGC,SAAS;QAChBC,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC;MAEnB,IAAIL,OAAO,IAAIK,IAAI,CAACb,MAAM,IAAI,CAAC,IAAIV,OAAO,CAACyB,KAAK,CAAC,EAAE;QACjD,OAAOP,OAAO,CAACQ,KAAK,CAACD,KAAK,CAAC,CAACA,KAAK,CAAC,CAAC;MACrC;MACA,IAAId,KAAK,GAAG,CAAC;QACTgB,MAAM,GAAGjB,MAAM,GAAGD,KAAK,CAACE,KAAK,CAAC,CAACW,KAAK,CAAC,IAAI,EAAEC,IAAI,CAAC,GAAGE,KAAK;MAE5D,OAAO,EAAEd,KAAK,GAAGD,MAAM,EAAE;QACvBiB,MAAM,GAAGlB,KAAK,CAACE,KAAK,CAAC,CAACiB,IAAI,CAAC,IAAI,EAAED,MAAM,CAAC;MAC1C;MACA,OAAOA,MAAM;IACf,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,eAAepB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}