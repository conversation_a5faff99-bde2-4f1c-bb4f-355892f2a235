{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport { makeInner } from '../util/model.js';\nimport { makeLabelFormatter, getOptionCategoryInterval, shouldShowAllLabels } from './axisHelper.js';\nvar inner = makeInner();\nfunction tickValuesToNumbers(axis, values) {\n  var nums = zrUtil.map(values, function (val) {\n    return axis.scale.parse(val);\n  });\n  if (axis.type === 'time' && nums.length > 0) {\n    // Time axis needs duplicate first/last tick (see TimeScale.getTicks())\n    // The first and last tick/label don't get drawn\n    nums.sort();\n    nums.unshift(nums[0]);\n    nums.push(nums[nums.length - 1]);\n  }\n  return nums;\n}\nexport function createAxisLabels(axis) {\n  var custom = axis.getLabelModel().get('customValues');\n  if (custom) {\n    var labelFormatter_1 = makeLabelFormatter(axis);\n    return {\n      labels: tickValuesToNumbers(axis, custom).map(function (numval) {\n        var tick = {\n          value: numval\n        };\n        return {\n          formattedLabel: labelFormatter_1(tick),\n          rawLabel: axis.scale.getLabel(tick),\n          tickValue: numval\n        };\n      })\n    };\n  }\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryLabels(axis) : makeRealNumberLabels(axis);\n}\n/**\n * @param {module:echats/coord/Axis} axis\n * @param {module:echarts/model/Model} tickModel For example, can be axisTick, splitLine, splitArea.\n * @return {Object} {\n *     ticks: Array.<number>\n *     tickCategoryInterval: number\n * }\n */\nexport function createAxisTicks(axis, tickModel) {\n  var custom = axis.getTickModel().get('customValues');\n  if (custom) {\n    return {\n      ticks: tickValuesToNumbers(axis, custom)\n    };\n  }\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryTicks(axis, tickModel) : {\n    ticks: zrUtil.map(axis.scale.getTicks(), function (tick) {\n      return tick.value;\n    })\n  };\n}\nfunction makeCategoryLabels(axis) {\n  var labelModel = axis.getLabelModel();\n  var result = makeCategoryLabelsActually(axis, labelModel);\n  return !labelModel.get('show') || axis.scale.isBlank() ? {\n    labels: [],\n    labelCategoryInterval: result.labelCategoryInterval\n  } : result;\n}\nfunction makeCategoryLabelsActually(axis, labelModel) {\n  var labelsCache = getListCache(axis, 'labels');\n  var optionLabelInterval = getOptionCategoryInterval(labelModel);\n  var result = listCacheGet(labelsCache, optionLabelInterval);\n  if (result) {\n    return result;\n  }\n  var labels;\n  var numericLabelInterval;\n  if (zrUtil.isFunction(optionLabelInterval)) {\n    labels = makeLabelsByCustomizedCategoryInterval(axis, optionLabelInterval);\n  } else {\n    numericLabelInterval = optionLabelInterval === 'auto' ? makeAutoCategoryInterval(axis) : optionLabelInterval;\n    labels = makeLabelsByNumericCategoryInterval(axis, numericLabelInterval);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(labelsCache, optionLabelInterval, {\n    labels: labels,\n    labelCategoryInterval: numericLabelInterval\n  });\n}\nfunction makeCategoryTicks(axis, tickModel) {\n  var ticksCache = getListCache(axis, 'ticks');\n  var optionTickInterval = getOptionCategoryInterval(tickModel);\n  var result = listCacheGet(ticksCache, optionTickInterval);\n  if (result) {\n    return result;\n  }\n  var ticks;\n  var tickCategoryInterval;\n  // Optimize for the case that large category data and no label displayed,\n  // we should not return all ticks.\n  if (!tickModel.get('show') || axis.scale.isBlank()) {\n    ticks = [];\n  }\n  if (zrUtil.isFunction(optionTickInterval)) {\n    ticks = makeLabelsByCustomizedCategoryInterval(axis, optionTickInterval, true);\n  }\n  // Always use label interval by default despite label show. Consider this\n  // scenario, Use multiple grid with the xAxis sync, and only one xAxis shows\n  // labels. `splitLine` and `axisTick` should be consistent in this case.\n  else if (optionTickInterval === 'auto') {\n    var labelsResult = makeCategoryLabelsActually(axis, axis.getLabelModel());\n    tickCategoryInterval = labelsResult.labelCategoryInterval;\n    ticks = zrUtil.map(labelsResult.labels, function (labelItem) {\n      return labelItem.tickValue;\n    });\n  } else {\n    tickCategoryInterval = optionTickInterval;\n    ticks = makeLabelsByNumericCategoryInterval(axis, tickCategoryInterval, true);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(ticksCache, optionTickInterval, {\n    ticks: ticks,\n    tickCategoryInterval: tickCategoryInterval\n  });\n}\nfunction makeRealNumberLabels(axis) {\n  var ticks = axis.scale.getTicks();\n  var labelFormatter = makeLabelFormatter(axis);\n  return {\n    labels: zrUtil.map(ticks, function (tick, idx) {\n      return {\n        level: tick.level,\n        formattedLabel: labelFormatter(tick, idx),\n        rawLabel: axis.scale.getLabel(tick),\n        tickValue: tick.value\n      };\n    })\n  };\n}\nfunction getListCache(axis, prop) {\n  // Because key can be a function, and cache size always is small, we use array cache.\n  return inner(axis)[prop] || (inner(axis)[prop] = []);\n}\nfunction listCacheGet(cache, key) {\n  for (var i = 0; i < cache.length; i++) {\n    if (cache[i].key === key) {\n      return cache[i].value;\n    }\n  }\n}\nfunction listCacheSet(cache, key, value) {\n  cache.push({\n    key: key,\n    value: value\n  });\n  return value;\n}\nfunction makeAutoCategoryInterval(axis) {\n  var result = inner(axis).autoInterval;\n  return result != null ? result : inner(axis).autoInterval = axis.calculateCategoryInterval();\n}\n/**\n * Calculate interval for category axis ticks and labels.\n * To get precise result, at least one of `getRotate` and `isHorizontal`\n * should be implemented in axis.\n */\nexport function calculateCategoryInterval(axis) {\n  var params = fetchAutoCategoryIntervalCalculationParams(axis);\n  var labelFormatter = makeLabelFormatter(axis);\n  var rotation = (params.axisRotate - params.labelRotate) / 180 * Math.PI;\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  // Providing this method is for optimization:\n  // avoid generating a long array by `getTicks`\n  // in large category data case.\n  var tickCount = ordinalScale.count();\n  if (ordinalExtent[1] - ordinalExtent[0] < 1) {\n    return 0;\n  }\n  var step = 1;\n  // Simple optimization. Empirical value: tick count should less than 40.\n  if (tickCount > 40) {\n    step = Math.max(1, Math.floor(tickCount / 40));\n  }\n  var tickValue = ordinalExtent[0];\n  var unitSpan = axis.dataToCoord(tickValue + 1) - axis.dataToCoord(tickValue);\n  var unitW = Math.abs(unitSpan * Math.cos(rotation));\n  var unitH = Math.abs(unitSpan * Math.sin(rotation));\n  var maxW = 0;\n  var maxH = 0;\n  // Caution: Performance sensitive for large category data.\n  // Consider dataZoom, we should make appropriate step to avoid O(n) loop.\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    var width = 0;\n    var height = 0;\n    // Not precise, do not consider align and vertical align\n    // and each distance from axis line yet.\n    var rect = textContain.getBoundingRect(labelFormatter({\n      value: tickValue\n    }), params.font, 'center', 'top');\n    // Magic number\n    width = rect.width * 1.3;\n    height = rect.height * 1.3;\n    // Min size, void long loop.\n    maxW = Math.max(maxW, width, 7);\n    maxH = Math.max(maxH, height, 7);\n  }\n  var dw = maxW / unitW;\n  var dh = maxH / unitH;\n  // 0/0 is NaN, 1/0 is Infinity.\n  isNaN(dw) && (dw = Infinity);\n  isNaN(dh) && (dh = Infinity);\n  var interval = Math.max(0, Math.floor(Math.min(dw, dh)));\n  var cache = inner(axis.model);\n  var axisExtent = axis.getExtent();\n  var lastAutoInterval = cache.lastAutoInterval;\n  var lastTickCount = cache.lastTickCount;\n  // Use cache to keep interval stable while moving zoom window,\n  // otherwise the calculated interval might jitter when the zoom\n  // window size is close to the interval-changing size.\n  // For example, if all of the axis labels are `a, b, c, d, e, f, g`.\n  // The jitter will cause that sometimes the displayed labels are\n  // `a, d, g` (interval: 2) sometimes `a, c, e`(interval: 1).\n  if (lastAutoInterval != null && lastTickCount != null && Math.abs(lastAutoInterval - interval) <= 1 && Math.abs(lastTickCount - tickCount) <= 1\n  // Always choose the bigger one, otherwise the critical\n  // point is not the same when zooming in or zooming out.\n  && lastAutoInterval > interval\n  // If the axis change is caused by chart resize, the cache should not\n  // be used. Otherwise some hidden labels might not be shown again.\n  && cache.axisExtent0 === axisExtent[0] && cache.axisExtent1 === axisExtent[1]) {\n    interval = lastAutoInterval;\n  }\n  // Only update cache if cache not used, otherwise the\n  // changing of interval is too insensitive.\n  else {\n    cache.lastTickCount = tickCount;\n    cache.lastAutoInterval = interval;\n    cache.axisExtent0 = axisExtent[0];\n    cache.axisExtent1 = axisExtent[1];\n  }\n  return interval;\n}\nfunction fetchAutoCategoryIntervalCalculationParams(axis) {\n  var labelModel = axis.getLabelModel();\n  return {\n    axisRotate: axis.getRotate ? axis.getRotate() : axis.isHorizontal && !axis.isHorizontal() ? 90 : 0,\n    labelRotate: labelModel.get('rotate') || 0,\n    font: labelModel.getFont()\n  };\n}\nfunction makeLabelsByNumericCategoryInterval(axis, categoryInterval, onlyTick) {\n  var labelFormatter = makeLabelFormatter(axis);\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  var labelModel = axis.getLabelModel();\n  var result = [];\n  // TODO: axisType: ordinalTime, pick the tick from each month/day/year/...\n  var step = Math.max((categoryInterval || 0) + 1, 1);\n  var startTick = ordinalExtent[0];\n  var tickCount = ordinalScale.count();\n  // Calculate start tick based on zero if possible to keep label consistent\n  // while zooming and moving while interval > 0. Otherwise the selection\n  // of displayable ticks and symbols probably keep changing.\n  // 3 is empirical value.\n  if (startTick !== 0 && step > 1 && tickCount / step > 2) {\n    startTick = Math.round(Math.ceil(startTick / step) * step);\n  }\n  // (1) Only add min max label here but leave overlap checking\n  // to render stage, which also ensure the returned list\n  // suitable for splitLine and splitArea rendering.\n  // (2) Scales except category always contain min max label so\n  // do not need to perform this process.\n  var showAllLabel = shouldShowAllLabels(axis);\n  var includeMinLabel = labelModel.get('showMinLabel') || showAllLabel;\n  var includeMaxLabel = labelModel.get('showMaxLabel') || showAllLabel;\n  if (includeMinLabel && startTick !== ordinalExtent[0]) {\n    addItem(ordinalExtent[0]);\n  }\n  // Optimize: avoid generating large array by `ordinalScale.getTicks()`.\n  var tickValue = startTick;\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    addItem(tickValue);\n  }\n  if (includeMaxLabel && tickValue - step !== ordinalExtent[1]) {\n    addItem(ordinalExtent[1]);\n  }\n  function addItem(tickValue) {\n    var tickObj = {\n      value: tickValue\n    };\n    result.push(onlyTick ? tickValue : {\n      formattedLabel: labelFormatter(tickObj),\n      rawLabel: ordinalScale.getLabel(tickObj),\n      tickValue: tickValue\n    });\n  }\n  return result;\n}\nfunction makeLabelsByCustomizedCategoryInterval(axis, categoryInterval, onlyTick) {\n  var ordinalScale = axis.scale;\n  var labelFormatter = makeLabelFormatter(axis);\n  var result = [];\n  zrUtil.each(ordinalScale.getTicks(), function (tick) {\n    var rawLabel = ordinalScale.getLabel(tick);\n    var tickValue = tick.value;\n    if (categoryInterval(tick.value, rawLabel)) {\n      result.push(onlyTick ? tickValue : {\n        formattedLabel: labelFormatter(tick),\n        rawLabel: rawLabel,\n        tickValue: tickValue\n      });\n    }\n  });\n  return result;\n}", "map": {"version": 3, "names": ["zrUtil", "textContain", "makeInner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getOptionCategoryInterval", "shouldShowAllLabels", "inner", "tickValuesToNumbers", "axis", "values", "nums", "map", "val", "scale", "parse", "type", "length", "sort", "unshift", "push", "createAxisLabels", "custom", "getLabelModel", "get", "labelFormatter_1", "labels", "numval", "tick", "value", "formattedLabel", "rawLabel", "get<PERSON><PERSON><PERSON>", "tickValue", "makeCategoryLabels", "makeRealNumberLabels", "createAxisTicks", "tickModel", "getTickModel", "ticks", "makeCategoryTicks", "getTicks", "labelModel", "result", "makeCategoryLabelsActually", "isBlank", "labelCategoryInterval", "labelsCache", "getListCache", "optionLabelInterval", "listCacheGet", "numericLabelInterval", "isFunction", "makeLabelsByCustomizedCategoryInterval", "makeAutoCategoryInterval", "makeLabelsByNumericCategoryInterval", "listCacheSet", "ticksCache", "optionTickInterval", "tickCategoryInterval", "labelsResult", "labelItem", "labelFormatter", "idx", "level", "prop", "cache", "key", "i", "autoInterval", "calculateCategoryInterval", "params", "fetchAutoCategoryIntervalCalculationParams", "rotation", "axisRotate", "labelRotate", "Math", "PI", "ordinalScale", "ordinalExtent", "getExtent", "tickCount", "count", "step", "max", "floor", "unitSpan", "dataToCoord", "unitW", "abs", "cos", "unitH", "sin", "maxW", "maxH", "width", "height", "rect", "getBoundingRect", "font", "dw", "dh", "isNaN", "Infinity", "interval", "min", "model", "axisExtent", "lastAutoInterval", "lastTickCount", "axisExtent0", "axisExtent1", "getRotate", "isHorizontal", "getFont", "categoryInterval", "onlyTick", "startTick", "round", "ceil", "showAllLabel", "includeMinLabel", "includeMaxLabel", "addItem", "tickObj", "each"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/coord/axisTickLabelBuilder.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport { makeInner } from '../util/model.js';\nimport { makeLabelFormatter, getOptionCategoryInterval, shouldShowAllLabels } from './axisHelper.js';\nvar inner = makeInner();\nfunction tickValuesToNumbers(axis, values) {\n  var nums = zrUtil.map(values, function (val) {\n    return axis.scale.parse(val);\n  });\n  if (axis.type === 'time' && nums.length > 0) {\n    // Time axis needs duplicate first/last tick (see TimeScale.getTicks())\n    // The first and last tick/label don't get drawn\n    nums.sort();\n    nums.unshift(nums[0]);\n    nums.push(nums[nums.length - 1]);\n  }\n  return nums;\n}\nexport function createAxisLabels(axis) {\n  var custom = axis.getLabelModel().get('customValues');\n  if (custom) {\n    var labelFormatter_1 = makeLabelFormatter(axis);\n    return {\n      labels: tickValuesToNumbers(axis, custom).map(function (numval) {\n        var tick = {\n          value: numval\n        };\n        return {\n          formattedLabel: labelFormatter_1(tick),\n          rawLabel: axis.scale.getLabel(tick),\n          tickValue: numval\n        };\n      })\n    };\n  }\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryLabels(axis) : makeRealNumberLabels(axis);\n}\n/**\n * @param {module:echats/coord/Axis} axis\n * @param {module:echarts/model/Model} tickModel For example, can be axisTick, splitLine, splitArea.\n * @return {Object} {\n *     ticks: Array.<number>\n *     tickCategoryInterval: number\n * }\n */\nexport function createAxisTicks(axis, tickModel) {\n  var custom = axis.getTickModel().get('customValues');\n  if (custom) {\n    return {\n      ticks: tickValuesToNumbers(axis, custom)\n    };\n  }\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryTicks(axis, tickModel) : {\n    ticks: zrUtil.map(axis.scale.getTicks(), function (tick) {\n      return tick.value;\n    })\n  };\n}\nfunction makeCategoryLabels(axis) {\n  var labelModel = axis.getLabelModel();\n  var result = makeCategoryLabelsActually(axis, labelModel);\n  return !labelModel.get('show') || axis.scale.isBlank() ? {\n    labels: [],\n    labelCategoryInterval: result.labelCategoryInterval\n  } : result;\n}\nfunction makeCategoryLabelsActually(axis, labelModel) {\n  var labelsCache = getListCache(axis, 'labels');\n  var optionLabelInterval = getOptionCategoryInterval(labelModel);\n  var result = listCacheGet(labelsCache, optionLabelInterval);\n  if (result) {\n    return result;\n  }\n  var labels;\n  var numericLabelInterval;\n  if (zrUtil.isFunction(optionLabelInterval)) {\n    labels = makeLabelsByCustomizedCategoryInterval(axis, optionLabelInterval);\n  } else {\n    numericLabelInterval = optionLabelInterval === 'auto' ? makeAutoCategoryInterval(axis) : optionLabelInterval;\n    labels = makeLabelsByNumericCategoryInterval(axis, numericLabelInterval);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(labelsCache, optionLabelInterval, {\n    labels: labels,\n    labelCategoryInterval: numericLabelInterval\n  });\n}\nfunction makeCategoryTicks(axis, tickModel) {\n  var ticksCache = getListCache(axis, 'ticks');\n  var optionTickInterval = getOptionCategoryInterval(tickModel);\n  var result = listCacheGet(ticksCache, optionTickInterval);\n  if (result) {\n    return result;\n  }\n  var ticks;\n  var tickCategoryInterval;\n  // Optimize for the case that large category data and no label displayed,\n  // we should not return all ticks.\n  if (!tickModel.get('show') || axis.scale.isBlank()) {\n    ticks = [];\n  }\n  if (zrUtil.isFunction(optionTickInterval)) {\n    ticks = makeLabelsByCustomizedCategoryInterval(axis, optionTickInterval, true);\n  }\n  // Always use label interval by default despite label show. Consider this\n  // scenario, Use multiple grid with the xAxis sync, and only one xAxis shows\n  // labels. `splitLine` and `axisTick` should be consistent in this case.\n  else if (optionTickInterval === 'auto') {\n    var labelsResult = makeCategoryLabelsActually(axis, axis.getLabelModel());\n    tickCategoryInterval = labelsResult.labelCategoryInterval;\n    ticks = zrUtil.map(labelsResult.labels, function (labelItem) {\n      return labelItem.tickValue;\n    });\n  } else {\n    tickCategoryInterval = optionTickInterval;\n    ticks = makeLabelsByNumericCategoryInterval(axis, tickCategoryInterval, true);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(ticksCache, optionTickInterval, {\n    ticks: ticks,\n    tickCategoryInterval: tickCategoryInterval\n  });\n}\nfunction makeRealNumberLabels(axis) {\n  var ticks = axis.scale.getTicks();\n  var labelFormatter = makeLabelFormatter(axis);\n  return {\n    labels: zrUtil.map(ticks, function (tick, idx) {\n      return {\n        level: tick.level,\n        formattedLabel: labelFormatter(tick, idx),\n        rawLabel: axis.scale.getLabel(tick),\n        tickValue: tick.value\n      };\n    })\n  };\n}\nfunction getListCache(axis, prop) {\n  // Because key can be a function, and cache size always is small, we use array cache.\n  return inner(axis)[prop] || (inner(axis)[prop] = []);\n}\nfunction listCacheGet(cache, key) {\n  for (var i = 0; i < cache.length; i++) {\n    if (cache[i].key === key) {\n      return cache[i].value;\n    }\n  }\n}\nfunction listCacheSet(cache, key, value) {\n  cache.push({\n    key: key,\n    value: value\n  });\n  return value;\n}\nfunction makeAutoCategoryInterval(axis) {\n  var result = inner(axis).autoInterval;\n  return result != null ? result : inner(axis).autoInterval = axis.calculateCategoryInterval();\n}\n/**\n * Calculate interval for category axis ticks and labels.\n * To get precise result, at least one of `getRotate` and `isHorizontal`\n * should be implemented in axis.\n */\nexport function calculateCategoryInterval(axis) {\n  var params = fetchAutoCategoryIntervalCalculationParams(axis);\n  var labelFormatter = makeLabelFormatter(axis);\n  var rotation = (params.axisRotate - params.labelRotate) / 180 * Math.PI;\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  // Providing this method is for optimization:\n  // avoid generating a long array by `getTicks`\n  // in large category data case.\n  var tickCount = ordinalScale.count();\n  if (ordinalExtent[1] - ordinalExtent[0] < 1) {\n    return 0;\n  }\n  var step = 1;\n  // Simple optimization. Empirical value: tick count should less than 40.\n  if (tickCount > 40) {\n    step = Math.max(1, Math.floor(tickCount / 40));\n  }\n  var tickValue = ordinalExtent[0];\n  var unitSpan = axis.dataToCoord(tickValue + 1) - axis.dataToCoord(tickValue);\n  var unitW = Math.abs(unitSpan * Math.cos(rotation));\n  var unitH = Math.abs(unitSpan * Math.sin(rotation));\n  var maxW = 0;\n  var maxH = 0;\n  // Caution: Performance sensitive for large category data.\n  // Consider dataZoom, we should make appropriate step to avoid O(n) loop.\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    var width = 0;\n    var height = 0;\n    // Not precise, do not consider align and vertical align\n    // and each distance from axis line yet.\n    var rect = textContain.getBoundingRect(labelFormatter({\n      value: tickValue\n    }), params.font, 'center', 'top');\n    // Magic number\n    width = rect.width * 1.3;\n    height = rect.height * 1.3;\n    // Min size, void long loop.\n    maxW = Math.max(maxW, width, 7);\n    maxH = Math.max(maxH, height, 7);\n  }\n  var dw = maxW / unitW;\n  var dh = maxH / unitH;\n  // 0/0 is NaN, 1/0 is Infinity.\n  isNaN(dw) && (dw = Infinity);\n  isNaN(dh) && (dh = Infinity);\n  var interval = Math.max(0, Math.floor(Math.min(dw, dh)));\n  var cache = inner(axis.model);\n  var axisExtent = axis.getExtent();\n  var lastAutoInterval = cache.lastAutoInterval;\n  var lastTickCount = cache.lastTickCount;\n  // Use cache to keep interval stable while moving zoom window,\n  // otherwise the calculated interval might jitter when the zoom\n  // window size is close to the interval-changing size.\n  // For example, if all of the axis labels are `a, b, c, d, e, f, g`.\n  // The jitter will cause that sometimes the displayed labels are\n  // `a, d, g` (interval: 2) sometimes `a, c, e`(interval: 1).\n  if (lastAutoInterval != null && lastTickCount != null && Math.abs(lastAutoInterval - interval) <= 1 && Math.abs(lastTickCount - tickCount) <= 1\n  // Always choose the bigger one, otherwise the critical\n  // point is not the same when zooming in or zooming out.\n  && lastAutoInterval > interval\n  // If the axis change is caused by chart resize, the cache should not\n  // be used. Otherwise some hidden labels might not be shown again.\n  && cache.axisExtent0 === axisExtent[0] && cache.axisExtent1 === axisExtent[1]) {\n    interval = lastAutoInterval;\n  }\n  // Only update cache if cache not used, otherwise the\n  // changing of interval is too insensitive.\n  else {\n    cache.lastTickCount = tickCount;\n    cache.lastAutoInterval = interval;\n    cache.axisExtent0 = axisExtent[0];\n    cache.axisExtent1 = axisExtent[1];\n  }\n  return interval;\n}\nfunction fetchAutoCategoryIntervalCalculationParams(axis) {\n  var labelModel = axis.getLabelModel();\n  return {\n    axisRotate: axis.getRotate ? axis.getRotate() : axis.isHorizontal && !axis.isHorizontal() ? 90 : 0,\n    labelRotate: labelModel.get('rotate') || 0,\n    font: labelModel.getFont()\n  };\n}\nfunction makeLabelsByNumericCategoryInterval(axis, categoryInterval, onlyTick) {\n  var labelFormatter = makeLabelFormatter(axis);\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  var labelModel = axis.getLabelModel();\n  var result = [];\n  // TODO: axisType: ordinalTime, pick the tick from each month/day/year/...\n  var step = Math.max((categoryInterval || 0) + 1, 1);\n  var startTick = ordinalExtent[0];\n  var tickCount = ordinalScale.count();\n  // Calculate start tick based on zero if possible to keep label consistent\n  // while zooming and moving while interval > 0. Otherwise the selection\n  // of displayable ticks and symbols probably keep changing.\n  // 3 is empirical value.\n  if (startTick !== 0 && step > 1 && tickCount / step > 2) {\n    startTick = Math.round(Math.ceil(startTick / step) * step);\n  }\n  // (1) Only add min max label here but leave overlap checking\n  // to render stage, which also ensure the returned list\n  // suitable for splitLine and splitArea rendering.\n  // (2) Scales except category always contain min max label so\n  // do not need to perform this process.\n  var showAllLabel = shouldShowAllLabels(axis);\n  var includeMinLabel = labelModel.get('showMinLabel') || showAllLabel;\n  var includeMaxLabel = labelModel.get('showMaxLabel') || showAllLabel;\n  if (includeMinLabel && startTick !== ordinalExtent[0]) {\n    addItem(ordinalExtent[0]);\n  }\n  // Optimize: avoid generating large array by `ordinalScale.getTicks()`.\n  var tickValue = startTick;\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    addItem(tickValue);\n  }\n  if (includeMaxLabel && tickValue - step !== ordinalExtent[1]) {\n    addItem(ordinalExtent[1]);\n  }\n  function addItem(tickValue) {\n    var tickObj = {\n      value: tickValue\n    };\n    result.push(onlyTick ? tickValue : {\n      formattedLabel: labelFormatter(tickObj),\n      rawLabel: ordinalScale.getLabel(tickObj),\n      tickValue: tickValue\n    });\n  }\n  return result;\n}\nfunction makeLabelsByCustomizedCategoryInterval(axis, categoryInterval, onlyTick) {\n  var ordinalScale = axis.scale;\n  var labelFormatter = makeLabelFormatter(axis);\n  var result = [];\n  zrUtil.each(ordinalScale.getTicks(), function (tick) {\n    var rawLabel = ordinalScale.getLabel(tick);\n    var tickValue = tick.value;\n    if (categoryInterval(tick.value, rawLabel)) {\n      result.push(onlyTick ? tickValue : {\n        formattedLabel: labelFormatter(tick),\n        rawLabel: rawLabel,\n        tickValue: tickValue\n      });\n    }\n  });\n  return result;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,WAAW,MAAM,6BAA6B;AAC1D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,kBAAkB,EAAEC,yBAAyB,EAAEC,mBAAmB,QAAQ,iBAAiB;AACpG,IAAIC,KAAK,GAAGJ,SAAS,CAAC,CAAC;AACvB,SAASK,mBAAmBA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACzC,IAAIC,IAAI,GAAGV,MAAM,CAACW,GAAG,CAACF,MAAM,EAAE,UAAUG,GAAG,EAAE;IAC3C,OAAOJ,IAAI,CAACK,KAAK,CAACC,KAAK,CAACF,GAAG,CAAC;EAC9B,CAAC,CAAC;EACF,IAAIJ,IAAI,CAACO,IAAI,KAAK,MAAM,IAAIL,IAAI,CAACM,MAAM,GAAG,CAAC,EAAE;IAC3C;IACA;IACAN,IAAI,CAACO,IAAI,CAAC,CAAC;IACXP,IAAI,CAACQ,OAAO,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC;IACrBA,IAAI,CAACS,IAAI,CAACT,IAAI,CAACA,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC,CAAC;EAClC;EACA,OAAON,IAAI;AACb;AACA,OAAO,SAASU,gBAAgBA,CAACZ,IAAI,EAAE;EACrC,IAAIa,MAAM,GAAGb,IAAI,CAACc,aAAa,CAAC,CAAC,CAACC,GAAG,CAAC,cAAc,CAAC;EACrD,IAAIF,MAAM,EAAE;IACV,IAAIG,gBAAgB,GAAGrB,kBAAkB,CAACK,IAAI,CAAC;IAC/C,OAAO;MACLiB,MAAM,EAAElB,mBAAmB,CAACC,IAAI,EAAEa,MAAM,CAAC,CAACV,GAAG,CAAC,UAAUe,MAAM,EAAE;QAC9D,IAAIC,IAAI,GAAG;UACTC,KAAK,EAAEF;QACT,CAAC;QACD,OAAO;UACLG,cAAc,EAAEL,gBAAgB,CAACG,IAAI,CAAC;UACtCG,QAAQ,EAAEtB,IAAI,CAACK,KAAK,CAACkB,QAAQ,CAACJ,IAAI,CAAC;UACnCK,SAAS,EAAEN;QACb,CAAC;MACH,CAAC;IACH,CAAC;EACH;EACA;EACA,OAAOlB,IAAI,CAACO,IAAI,KAAK,UAAU,GAAGkB,kBAAkB,CAACzB,IAAI,CAAC,GAAG0B,oBAAoB,CAAC1B,IAAI,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS2B,eAAeA,CAAC3B,IAAI,EAAE4B,SAAS,EAAE;EAC/C,IAAIf,MAAM,GAAGb,IAAI,CAAC6B,YAAY,CAAC,CAAC,CAACd,GAAG,CAAC,cAAc,CAAC;EACpD,IAAIF,MAAM,EAAE;IACV,OAAO;MACLiB,KAAK,EAAE/B,mBAAmB,CAACC,IAAI,EAAEa,MAAM;IACzC,CAAC;EACH;EACA;EACA,OAAOb,IAAI,CAACO,IAAI,KAAK,UAAU,GAAGwB,iBAAiB,CAAC/B,IAAI,EAAE4B,SAAS,CAAC,GAAG;IACrEE,KAAK,EAAEtC,MAAM,CAACW,GAAG,CAACH,IAAI,CAACK,KAAK,CAAC2B,QAAQ,CAAC,CAAC,EAAE,UAAUb,IAAI,EAAE;MACvD,OAAOA,IAAI,CAACC,KAAK;IACnB,CAAC;EACH,CAAC;AACH;AACA,SAASK,kBAAkBA,CAACzB,IAAI,EAAE;EAChC,IAAIiC,UAAU,GAAGjC,IAAI,CAACc,aAAa,CAAC,CAAC;EACrC,IAAIoB,MAAM,GAAGC,0BAA0B,CAACnC,IAAI,EAAEiC,UAAU,CAAC;EACzD,OAAO,CAACA,UAAU,CAAClB,GAAG,CAAC,MAAM,CAAC,IAAIf,IAAI,CAACK,KAAK,CAAC+B,OAAO,CAAC,CAAC,GAAG;IACvDnB,MAAM,EAAE,EAAE;IACVoB,qBAAqB,EAAEH,MAAM,CAACG;EAChC,CAAC,GAAGH,MAAM;AACZ;AACA,SAASC,0BAA0BA,CAACnC,IAAI,EAAEiC,UAAU,EAAE;EACpD,IAAIK,WAAW,GAAGC,YAAY,CAACvC,IAAI,EAAE,QAAQ,CAAC;EAC9C,IAAIwC,mBAAmB,GAAG5C,yBAAyB,CAACqC,UAAU,CAAC;EAC/D,IAAIC,MAAM,GAAGO,YAAY,CAACH,WAAW,EAAEE,mBAAmB,CAAC;EAC3D,IAAIN,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EACA,IAAIjB,MAAM;EACV,IAAIyB,oBAAoB;EACxB,IAAIlD,MAAM,CAACmD,UAAU,CAACH,mBAAmB,CAAC,EAAE;IAC1CvB,MAAM,GAAG2B,sCAAsC,CAAC5C,IAAI,EAAEwC,mBAAmB,CAAC;EAC5E,CAAC,MAAM;IACLE,oBAAoB,GAAGF,mBAAmB,KAAK,MAAM,GAAGK,wBAAwB,CAAC7C,IAAI,CAAC,GAAGwC,mBAAmB;IAC5GvB,MAAM,GAAG6B,mCAAmC,CAAC9C,IAAI,EAAE0C,oBAAoB,CAAC;EAC1E;EACA;EACA,OAAOK,YAAY,CAACT,WAAW,EAAEE,mBAAmB,EAAE;IACpDvB,MAAM,EAAEA,MAAM;IACdoB,qBAAqB,EAAEK;EACzB,CAAC,CAAC;AACJ;AACA,SAASX,iBAAiBA,CAAC/B,IAAI,EAAE4B,SAAS,EAAE;EAC1C,IAAIoB,UAAU,GAAGT,YAAY,CAACvC,IAAI,EAAE,OAAO,CAAC;EAC5C,IAAIiD,kBAAkB,GAAGrD,yBAAyB,CAACgC,SAAS,CAAC;EAC7D,IAAIM,MAAM,GAAGO,YAAY,CAACO,UAAU,EAAEC,kBAAkB,CAAC;EACzD,IAAIf,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EACA,IAAIJ,KAAK;EACT,IAAIoB,oBAAoB;EACxB;EACA;EACA,IAAI,CAACtB,SAAS,CAACb,GAAG,CAAC,MAAM,CAAC,IAAIf,IAAI,CAACK,KAAK,CAAC+B,OAAO,CAAC,CAAC,EAAE;IAClDN,KAAK,GAAG,EAAE;EACZ;EACA,IAAItC,MAAM,CAACmD,UAAU,CAACM,kBAAkB,CAAC,EAAE;IACzCnB,KAAK,GAAGc,sCAAsC,CAAC5C,IAAI,EAAEiD,kBAAkB,EAAE,IAAI,CAAC;EAChF;EACA;EACA;EACA;EAAA,KACK,IAAIA,kBAAkB,KAAK,MAAM,EAAE;IACtC,IAAIE,YAAY,GAAGhB,0BAA0B,CAACnC,IAAI,EAAEA,IAAI,CAACc,aAAa,CAAC,CAAC,CAAC;IACzEoC,oBAAoB,GAAGC,YAAY,CAACd,qBAAqB;IACzDP,KAAK,GAAGtC,MAAM,CAACW,GAAG,CAACgD,YAAY,CAAClC,MAAM,EAAE,UAAUmC,SAAS,EAAE;MAC3D,OAAOA,SAAS,CAAC5B,SAAS;IAC5B,CAAC,CAAC;EACJ,CAAC,MAAM;IACL0B,oBAAoB,GAAGD,kBAAkB;IACzCnB,KAAK,GAAGgB,mCAAmC,CAAC9C,IAAI,EAAEkD,oBAAoB,EAAE,IAAI,CAAC;EAC/E;EACA;EACA,OAAOH,YAAY,CAACC,UAAU,EAAEC,kBAAkB,EAAE;IAClDnB,KAAK,EAAEA,KAAK;IACZoB,oBAAoB,EAAEA;EACxB,CAAC,CAAC;AACJ;AACA,SAASxB,oBAAoBA,CAAC1B,IAAI,EAAE;EAClC,IAAI8B,KAAK,GAAG9B,IAAI,CAACK,KAAK,CAAC2B,QAAQ,CAAC,CAAC;EACjC,IAAIqB,cAAc,GAAG1D,kBAAkB,CAACK,IAAI,CAAC;EAC7C,OAAO;IACLiB,MAAM,EAAEzB,MAAM,CAACW,GAAG,CAAC2B,KAAK,EAAE,UAAUX,IAAI,EAAEmC,GAAG,EAAE;MAC7C,OAAO;QACLC,KAAK,EAAEpC,IAAI,CAACoC,KAAK;QACjBlC,cAAc,EAAEgC,cAAc,CAAClC,IAAI,EAAEmC,GAAG,CAAC;QACzChC,QAAQ,EAAEtB,IAAI,CAACK,KAAK,CAACkB,QAAQ,CAACJ,IAAI,CAAC;QACnCK,SAAS,EAAEL,IAAI,CAACC;MAClB,CAAC;IACH,CAAC;EACH,CAAC;AACH;AACA,SAASmB,YAAYA,CAACvC,IAAI,EAAEwD,IAAI,EAAE;EAChC;EACA,OAAO1D,KAAK,CAACE,IAAI,CAAC,CAACwD,IAAI,CAAC,KAAK1D,KAAK,CAACE,IAAI,CAAC,CAACwD,IAAI,CAAC,GAAG,EAAE,CAAC;AACtD;AACA,SAASf,YAAYA,CAACgB,KAAK,EAAEC,GAAG,EAAE;EAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACjD,MAAM,EAAEmD,CAAC,EAAE,EAAE;IACrC,IAAIF,KAAK,CAACE,CAAC,CAAC,CAACD,GAAG,KAAKA,GAAG,EAAE;MACxB,OAAOD,KAAK,CAACE,CAAC,CAAC,CAACvC,KAAK;IACvB;EACF;AACF;AACA,SAAS2B,YAAYA,CAACU,KAAK,EAAEC,GAAG,EAAEtC,KAAK,EAAE;EACvCqC,KAAK,CAAC9C,IAAI,CAAC;IACT+C,GAAG,EAAEA,GAAG;IACRtC,KAAK,EAAEA;EACT,CAAC,CAAC;EACF,OAAOA,KAAK;AACd;AACA,SAASyB,wBAAwBA,CAAC7C,IAAI,EAAE;EACtC,IAAIkC,MAAM,GAAGpC,KAAK,CAACE,IAAI,CAAC,CAAC4D,YAAY;EACrC,OAAO1B,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGpC,KAAK,CAACE,IAAI,CAAC,CAAC4D,YAAY,GAAG5D,IAAI,CAAC6D,yBAAyB,CAAC,CAAC;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,yBAAyBA,CAAC7D,IAAI,EAAE;EAC9C,IAAI8D,MAAM,GAAGC,0CAA0C,CAAC/D,IAAI,CAAC;EAC7D,IAAIqD,cAAc,GAAG1D,kBAAkB,CAACK,IAAI,CAAC;EAC7C,IAAIgE,QAAQ,GAAG,CAACF,MAAM,CAACG,UAAU,GAAGH,MAAM,CAACI,WAAW,IAAI,GAAG,GAAGC,IAAI,CAACC,EAAE;EACvE,IAAIC,YAAY,GAAGrE,IAAI,CAACK,KAAK;EAC7B,IAAIiE,aAAa,GAAGD,YAAY,CAACE,SAAS,CAAC,CAAC;EAC5C;EACA;EACA;EACA,IAAIC,SAAS,GAAGH,YAAY,CAACI,KAAK,CAAC,CAAC;EACpC,IAAIH,aAAa,CAAC,CAAC,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;IAC3C,OAAO,CAAC;EACV;EACA,IAAII,IAAI,GAAG,CAAC;EACZ;EACA,IAAIF,SAAS,GAAG,EAAE,EAAE;IAClBE,IAAI,GAAGP,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAER,IAAI,CAACS,KAAK,CAACJ,SAAS,GAAG,EAAE,CAAC,CAAC;EAChD;EACA,IAAIhD,SAAS,GAAG8C,aAAa,CAAC,CAAC,CAAC;EAChC,IAAIO,QAAQ,GAAG7E,IAAI,CAAC8E,WAAW,CAACtD,SAAS,GAAG,CAAC,CAAC,GAAGxB,IAAI,CAAC8E,WAAW,CAACtD,SAAS,CAAC;EAC5E,IAAIuD,KAAK,GAAGZ,IAAI,CAACa,GAAG,CAACH,QAAQ,GAAGV,IAAI,CAACc,GAAG,CAACjB,QAAQ,CAAC,CAAC;EACnD,IAAIkB,KAAK,GAAGf,IAAI,CAACa,GAAG,CAACH,QAAQ,GAAGV,IAAI,CAACgB,GAAG,CAACnB,QAAQ,CAAC,CAAC;EACnD,IAAIoB,IAAI,GAAG,CAAC;EACZ,IAAIC,IAAI,GAAG,CAAC;EACZ;EACA;EACA,OAAO7D,SAAS,IAAI8C,aAAa,CAAC,CAAC,CAAC,EAAE9C,SAAS,IAAIkD,IAAI,EAAE;IACvD,IAAIY,KAAK,GAAG,CAAC;IACb,IAAIC,MAAM,GAAG,CAAC;IACd;IACA;IACA,IAAIC,IAAI,GAAG/F,WAAW,CAACgG,eAAe,CAACpC,cAAc,CAAC;MACpDjC,KAAK,EAAEI;IACT,CAAC,CAAC,EAAEsC,MAAM,CAAC4B,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC;IACjC;IACAJ,KAAK,GAAGE,IAAI,CAACF,KAAK,GAAG,GAAG;IACxBC,MAAM,GAAGC,IAAI,CAACD,MAAM,GAAG,GAAG;IAC1B;IACAH,IAAI,GAAGjB,IAAI,CAACQ,GAAG,CAACS,IAAI,EAAEE,KAAK,EAAE,CAAC,CAAC;IAC/BD,IAAI,GAAGlB,IAAI,CAACQ,GAAG,CAACU,IAAI,EAAEE,MAAM,EAAE,CAAC,CAAC;EAClC;EACA,IAAII,EAAE,GAAGP,IAAI,GAAGL,KAAK;EACrB,IAAIa,EAAE,GAAGP,IAAI,GAAGH,KAAK;EACrB;EACAW,KAAK,CAACF,EAAE,CAAC,KAAKA,EAAE,GAAGG,QAAQ,CAAC;EAC5BD,KAAK,CAACD,EAAE,CAAC,KAAKA,EAAE,GAAGE,QAAQ,CAAC;EAC5B,IAAIC,QAAQ,GAAG5B,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAER,IAAI,CAACS,KAAK,CAACT,IAAI,CAAC6B,GAAG,CAACL,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;EACxD,IAAInC,KAAK,GAAG3D,KAAK,CAACE,IAAI,CAACiG,KAAK,CAAC;EAC7B,IAAIC,UAAU,GAAGlG,IAAI,CAACuE,SAAS,CAAC,CAAC;EACjC,IAAI4B,gBAAgB,GAAG1C,KAAK,CAAC0C,gBAAgB;EAC7C,IAAIC,aAAa,GAAG3C,KAAK,CAAC2C,aAAa;EACvC;EACA;EACA;EACA;EACA;EACA;EACA,IAAID,gBAAgB,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,IAAIjC,IAAI,CAACa,GAAG,CAACmB,gBAAgB,GAAGJ,QAAQ,CAAC,IAAI,CAAC,IAAI5B,IAAI,CAACa,GAAG,CAACoB,aAAa,GAAG5B,SAAS,CAAC,IAAI;EAC9I;EACA;EAAA,GACG2B,gBAAgB,GAAGJ;EACtB;EACA;EAAA,GACGtC,KAAK,CAAC4C,WAAW,KAAKH,UAAU,CAAC,CAAC,CAAC,IAAIzC,KAAK,CAAC6C,WAAW,KAAKJ,UAAU,CAAC,CAAC,CAAC,EAAE;IAC7EH,QAAQ,GAAGI,gBAAgB;EAC7B;EACA;EACA;EAAA,KACK;IACH1C,KAAK,CAAC2C,aAAa,GAAG5B,SAAS;IAC/Bf,KAAK,CAAC0C,gBAAgB,GAAGJ,QAAQ;IACjCtC,KAAK,CAAC4C,WAAW,GAAGH,UAAU,CAAC,CAAC,CAAC;IACjCzC,KAAK,CAAC6C,WAAW,GAAGJ,UAAU,CAAC,CAAC,CAAC;EACnC;EACA,OAAOH,QAAQ;AACjB;AACA,SAAShC,0CAA0CA,CAAC/D,IAAI,EAAE;EACxD,IAAIiC,UAAU,GAAGjC,IAAI,CAACc,aAAa,CAAC,CAAC;EACrC,OAAO;IACLmD,UAAU,EAAEjE,IAAI,CAACuG,SAAS,GAAGvG,IAAI,CAACuG,SAAS,CAAC,CAAC,GAAGvG,IAAI,CAACwG,YAAY,IAAI,CAACxG,IAAI,CAACwG,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IAClGtC,WAAW,EAAEjC,UAAU,CAAClB,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC1C2E,IAAI,EAAEzD,UAAU,CAACwE,OAAO,CAAC;EAC3B,CAAC;AACH;AACA,SAAS3D,mCAAmCA,CAAC9C,IAAI,EAAE0G,gBAAgB,EAAEC,QAAQ,EAAE;EAC7E,IAAItD,cAAc,GAAG1D,kBAAkB,CAACK,IAAI,CAAC;EAC7C,IAAIqE,YAAY,GAAGrE,IAAI,CAACK,KAAK;EAC7B,IAAIiE,aAAa,GAAGD,YAAY,CAACE,SAAS,CAAC,CAAC;EAC5C,IAAItC,UAAU,GAAGjC,IAAI,CAACc,aAAa,CAAC,CAAC;EACrC,IAAIoB,MAAM,GAAG,EAAE;EACf;EACA,IAAIwC,IAAI,GAAGP,IAAI,CAACQ,GAAG,CAAC,CAAC+B,gBAAgB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACnD,IAAIE,SAAS,GAAGtC,aAAa,CAAC,CAAC,CAAC;EAChC,IAAIE,SAAS,GAAGH,YAAY,CAACI,KAAK,CAAC,CAAC;EACpC;EACA;EACA;EACA;EACA,IAAImC,SAAS,KAAK,CAAC,IAAIlC,IAAI,GAAG,CAAC,IAAIF,SAAS,GAAGE,IAAI,GAAG,CAAC,EAAE;IACvDkC,SAAS,GAAGzC,IAAI,CAAC0C,KAAK,CAAC1C,IAAI,CAAC2C,IAAI,CAACF,SAAS,GAAGlC,IAAI,CAAC,GAAGA,IAAI,CAAC;EAC5D;EACA;EACA;EACA;EACA;EACA;EACA,IAAIqC,YAAY,GAAGlH,mBAAmB,CAACG,IAAI,CAAC;EAC5C,IAAIgH,eAAe,GAAG/E,UAAU,CAAClB,GAAG,CAAC,cAAc,CAAC,IAAIgG,YAAY;EACpE,IAAIE,eAAe,GAAGhF,UAAU,CAAClB,GAAG,CAAC,cAAc,CAAC,IAAIgG,YAAY;EACpE,IAAIC,eAAe,IAAIJ,SAAS,KAAKtC,aAAa,CAAC,CAAC,CAAC,EAAE;IACrD4C,OAAO,CAAC5C,aAAa,CAAC,CAAC,CAAC,CAAC;EAC3B;EACA;EACA,IAAI9C,SAAS,GAAGoF,SAAS;EACzB,OAAOpF,SAAS,IAAI8C,aAAa,CAAC,CAAC,CAAC,EAAE9C,SAAS,IAAIkD,IAAI,EAAE;IACvDwC,OAAO,CAAC1F,SAAS,CAAC;EACpB;EACA,IAAIyF,eAAe,IAAIzF,SAAS,GAAGkD,IAAI,KAAKJ,aAAa,CAAC,CAAC,CAAC,EAAE;IAC5D4C,OAAO,CAAC5C,aAAa,CAAC,CAAC,CAAC,CAAC;EAC3B;EACA,SAAS4C,OAAOA,CAAC1F,SAAS,EAAE;IAC1B,IAAI2F,OAAO,GAAG;MACZ/F,KAAK,EAAEI;IACT,CAAC;IACDU,MAAM,CAACvB,IAAI,CAACgG,QAAQ,GAAGnF,SAAS,GAAG;MACjCH,cAAc,EAAEgC,cAAc,CAAC8D,OAAO,CAAC;MACvC7F,QAAQ,EAAE+C,YAAY,CAAC9C,QAAQ,CAAC4F,OAAO,CAAC;MACxC3F,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EACA,OAAOU,MAAM;AACf;AACA,SAASU,sCAAsCA,CAAC5C,IAAI,EAAE0G,gBAAgB,EAAEC,QAAQ,EAAE;EAChF,IAAItC,YAAY,GAAGrE,IAAI,CAACK,KAAK;EAC7B,IAAIgD,cAAc,GAAG1D,kBAAkB,CAACK,IAAI,CAAC;EAC7C,IAAIkC,MAAM,GAAG,EAAE;EACf1C,MAAM,CAAC4H,IAAI,CAAC/C,YAAY,CAACrC,QAAQ,CAAC,CAAC,EAAE,UAAUb,IAAI,EAAE;IACnD,IAAIG,QAAQ,GAAG+C,YAAY,CAAC9C,QAAQ,CAACJ,IAAI,CAAC;IAC1C,IAAIK,SAAS,GAAGL,IAAI,CAACC,KAAK;IAC1B,IAAIsF,gBAAgB,CAACvF,IAAI,CAACC,KAAK,EAAEE,QAAQ,CAAC,EAAE;MAC1CY,MAAM,CAACvB,IAAI,CAACgG,QAAQ,GAAGnF,SAAS,GAAG;QACjCH,cAAc,EAAEgC,cAAc,CAAClC,IAAI,CAAC;QACpCG,QAAQ,EAAEA,QAAQ;QAClBE,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOU,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}