{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { normalizeToArray\n// , MappingExistingItem, setComponentTypeToKeyInfo, mappingToExists\n} from '../util/model.js';\nimport { each, clone, map, isTypedArray, setAsPrimitive, isArray, isObject\n// , HashMap , createHashMap, extend, merge,\n} from 'zrender/lib/core/util.js';\nimport { error } from '../util/log.js';\nvar QUERY_REG = /^(min|max)?(.+)$/;\n// Key: mainType\n// type FakeComponentsMap = HashMap<(MappingExistingItem & { subType: string })[]>;\n/**\n * TERM EXPLANATIONS:\n * See `ECOption` and `ECUnitOption` in `src/util/types.ts`.\n */\nvar OptionManager = /** @class */function () {\n  // timeline.notMerge is not supported in ec3. Firstly there is rearly\n  // case that notMerge is needed. Secondly supporting 'notMerge' requires\n  // rawOption cloned and backuped when timeline changed, which does no\n  // good to performance. What's more, that both timeline and setOption\n  // method supply 'notMerge' brings complex and some problems.\n  // Consider this case:\n  // (step1) chart.setOption({timeline: {notMerge: false}, ...}, false);\n  // (step2) chart.setOption({timeline: {notMerge: true}, ...}, false);\n  function OptionManager(api) {\n    this._timelineOptions = [];\n    this._mediaList = [];\n    /**\n     * -1, means default.\n     * empty means no media.\n     */\n    this._currentMediaIndices = [];\n    this._api = api;\n  }\n  OptionManager.prototype.setOption = function (rawOption, optionPreprocessorFuncs, opt) {\n    if (rawOption) {\n      // That set dat primitive is dangerous if user reuse the data when setOption again.\n      each(normalizeToArray(rawOption.series), function (series) {\n        series && series.data && isTypedArray(series.data) && setAsPrimitive(series.data);\n      });\n      each(normalizeToArray(rawOption.dataset), function (dataset) {\n        dataset && dataset.source && isTypedArray(dataset.source) && setAsPrimitive(dataset.source);\n      });\n    }\n    // Caution: some series modify option data, if do not clone,\n    // it should ensure that the repeat modify correctly\n    // (create a new object when modify itself).\n    rawOption = clone(rawOption);\n    // FIXME\n    // If some property is set in timeline options or media option but\n    // not set in baseOption, a warning should be given.\n    var optionBackup = this._optionBackup;\n    var newParsedOption = parseRawOption(rawOption, optionPreprocessorFuncs, !optionBackup);\n    this._newBaseOption = newParsedOption.baseOption;\n    // For setOption at second time (using merge mode);\n    if (optionBackup) {\n      // FIXME\n      // the restore merge solution is essentially incorrect.\n      // the mapping can not be 100% consistent with ecModel, which probably brings\n      // potential bug!\n      // The first merge is delayed, because in most cases, users do not call `setOption` twice.\n      // let fakeCmptsMap = this._fakeCmptsMap;\n      // if (!fakeCmptsMap) {\n      //     fakeCmptsMap = this._fakeCmptsMap = createHashMap();\n      //     mergeToBackupOption(fakeCmptsMap, null, optionBackup.baseOption, null);\n      // }\n      // mergeToBackupOption(\n      //     fakeCmptsMap, optionBackup.baseOption, newParsedOption.baseOption, opt\n      // );\n      // For simplicity, timeline options and media options do not support merge,\n      // that is, if you `setOption` twice and both has timeline options, the latter\n      // timeline options will not be merged to the former, but just substitute them.\n      if (newParsedOption.timelineOptions.length) {\n        optionBackup.timelineOptions = newParsedOption.timelineOptions;\n      }\n      if (newParsedOption.mediaList.length) {\n        optionBackup.mediaList = newParsedOption.mediaList;\n      }\n      if (newParsedOption.mediaDefault) {\n        optionBackup.mediaDefault = newParsedOption.mediaDefault;\n      }\n    } else {\n      this._optionBackup = newParsedOption;\n    }\n  };\n  OptionManager.prototype.mountOption = function (isRecreate) {\n    var optionBackup = this._optionBackup;\n    this._timelineOptions = optionBackup.timelineOptions;\n    this._mediaList = optionBackup.mediaList;\n    this._mediaDefault = optionBackup.mediaDefault;\n    this._currentMediaIndices = [];\n    return clone(isRecreate\n    // this._optionBackup.baseOption, which is created at the first `setOption`\n    // called, and is merged into every new option by inner method `mergeToBackupOption`\n    // each time `setOption` called, can be only used in `isRecreate`, because\n    // its reliability is under suspicion. In other cases option merge is\n    // performed by `model.mergeOption`.\n    ? optionBackup.baseOption : this._newBaseOption);\n  };\n  OptionManager.prototype.getTimelineOption = function (ecModel) {\n    var option;\n    var timelineOptions = this._timelineOptions;\n    if (timelineOptions.length) {\n      // getTimelineOption can only be called after ecModel inited,\n      // so we can get currentIndex from timelineModel.\n      var timelineModel = ecModel.getComponent('timeline');\n      if (timelineModel) {\n        option = clone(\n        // FIXME:TS as TimelineModel or quivlant interface\n        timelineOptions[timelineModel.getCurrentIndex()]);\n      }\n    }\n    return option;\n  };\n  OptionManager.prototype.getMediaOption = function (ecModel) {\n    var ecWidth = this._api.getWidth();\n    var ecHeight = this._api.getHeight();\n    var mediaList = this._mediaList;\n    var mediaDefault = this._mediaDefault;\n    var indices = [];\n    var result = [];\n    // No media defined.\n    if (!mediaList.length && !mediaDefault) {\n      return result;\n    }\n    // Multi media may be applied, the latter defined media has higher priority.\n    for (var i = 0, len = mediaList.length; i < len; i++) {\n      if (applyMediaQuery(mediaList[i].query, ecWidth, ecHeight)) {\n        indices.push(i);\n      }\n    }\n    // FIXME\n    // Whether mediaDefault should force users to provide? Otherwise\n    // the change by media query can not be recorvered.\n    if (!indices.length && mediaDefault) {\n      indices = [-1];\n    }\n    if (indices.length && !indicesEquals(indices, this._currentMediaIndices)) {\n      result = map(indices, function (index) {\n        return clone(index === -1 ? mediaDefault.option : mediaList[index].option);\n      });\n    }\n    // Otherwise return nothing.\n    this._currentMediaIndices = indices;\n    return result;\n  };\n  return OptionManager;\n}();\n/**\n * [RAW_OPTION_PATTERNS]\n * (Note: \"series: []\" represents all other props in `ECUnitOption`)\n *\n * (1) No prop \"baseOption\" declared:\n * Root option is used as \"baseOption\" (except prop \"options\" and \"media\").\n * ```js\n * option = {\n *     series: [],\n *     timeline: {},\n *     options: [],\n * };\n * option = {\n *     series: [],\n *     media: {},\n * };\n * option = {\n *     series: [],\n *     timeline: {},\n *     options: [],\n *     media: {},\n * }\n * ```\n *\n * (2) Prop \"baseOption\" declared:\n * If \"baseOption\" declared, `ECUnitOption` props can only be declared\n * inside \"baseOption\" except prop \"timeline\" (compat ec2).\n * ```js\n * option = {\n *     baseOption: {\n *         timeline: {},\n *         series: [],\n *     },\n *     options: []\n * };\n * option = {\n *     baseOption: {\n *         series: [],\n *     },\n *     media: []\n * };\n * option = {\n *     baseOption: {\n *         timeline: {},\n *         series: [],\n *     },\n *     options: []\n *     media: []\n * };\n * option = {\n *     // ec3 compat ec2: allow (only) `timeline` declared\n *     // outside baseOption. Keep this setting for compat.\n *     timeline: {},\n *     baseOption: {\n *         series: [],\n *     },\n *     options: [],\n *     media: []\n * };\n * ```\n */\nfunction parseRawOption(\n// `rawOption` May be modified\nrawOption, optionPreprocessorFuncs, isNew) {\n  var mediaList = [];\n  var mediaDefault;\n  var baseOption;\n  var declaredBaseOption = rawOption.baseOption;\n  // Compatible with ec2, [RAW_OPTION_PATTERNS] above.\n  var timelineOnRoot = rawOption.timeline;\n  var timelineOptionsOnRoot = rawOption.options;\n  var mediaOnRoot = rawOption.media;\n  var hasMedia = !!rawOption.media;\n  var hasTimeline = !!(timelineOptionsOnRoot || timelineOnRoot || declaredBaseOption && declaredBaseOption.timeline);\n  if (declaredBaseOption) {\n    baseOption = declaredBaseOption;\n    // For merge option.\n    if (!baseOption.timeline) {\n      baseOption.timeline = timelineOnRoot;\n    }\n  }\n  // For convenience, enable to use the root option as the `baseOption`:\n  // `{ ...normalOptionProps, media: [{ ... }, { ... }] }`\n  else {\n    if (hasTimeline || hasMedia) {\n      rawOption.options = rawOption.media = null;\n    }\n    baseOption = rawOption;\n  }\n  if (hasMedia) {\n    if (isArray(mediaOnRoot)) {\n      each(mediaOnRoot, function (singleMedia) {\n        if (process.env.NODE_ENV !== 'production') {\n          // Real case of wrong config.\n          if (singleMedia && !singleMedia.option && isObject(singleMedia.query) && isObject(singleMedia.query.option)) {\n            error('Illegal media option. Must be like { media: [ { query: {}, option: {} } ] }');\n          }\n        }\n        if (singleMedia && singleMedia.option) {\n          if (singleMedia.query) {\n            mediaList.push(singleMedia);\n          } else if (!mediaDefault) {\n            // Use the first media default.\n            mediaDefault = singleMedia;\n          }\n        }\n      });\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        // Real case of wrong config.\n        error('Illegal media option. Must be an array. Like { media: [ {...}, {...} ] }');\n      }\n    }\n  }\n  doPreprocess(baseOption);\n  each(timelineOptionsOnRoot, function (option) {\n    return doPreprocess(option);\n  });\n  each(mediaList, function (media) {\n    return doPreprocess(media.option);\n  });\n  function doPreprocess(option) {\n    each(optionPreprocessorFuncs, function (preProcess) {\n      preProcess(option, isNew);\n    });\n  }\n  return {\n    baseOption: baseOption,\n    timelineOptions: timelineOptionsOnRoot || [],\n    mediaDefault: mediaDefault,\n    mediaList: mediaList\n  };\n}\n/**\n * @see <http://www.w3.org/TR/css3-mediaqueries/#media1>\n * Support: width, height, aspectRatio\n * Can use max or min as prefix.\n */\nfunction applyMediaQuery(query, ecWidth, ecHeight) {\n  var realMap = {\n    width: ecWidth,\n    height: ecHeight,\n    aspectratio: ecWidth / ecHeight // lower case for convenience.\n  };\n  var applicable = true;\n  each(query, function (value, attr) {\n    var matched = attr.match(QUERY_REG);\n    if (!matched || !matched[1] || !matched[2]) {\n      return;\n    }\n    var operator = matched[1];\n    var realAttr = matched[2].toLowerCase();\n    if (!compare(realMap[realAttr], value, operator)) {\n      applicable = false;\n    }\n  });\n  return applicable;\n}\nfunction compare(real, expect, operator) {\n  if (operator === 'min') {\n    return real >= expect;\n  } else if (operator === 'max') {\n    return real <= expect;\n  } else {\n    // Equals\n    return real === expect;\n  }\n}\nfunction indicesEquals(indices1, indices2) {\n  // indices is always order by asc and has only finite number.\n  return indices1.join(',') === indices2.join(',');\n}\n/**\n * Consider case:\n * `chart.setOption(opt1);`\n * Then user do some interaction like dataZoom, dataView changing.\n * `chart.setOption(opt2);`\n * Then user press 'reset button' in toolbox.\n *\n * After doing that all of the interaction effects should be reset, the\n * chart should be the same as the result of invoke\n * `chart.setOption(opt1); chart.setOption(opt2);`.\n *\n * Although it is not able ensure that\n * `chart.setOption(opt1); chart.setOption(opt2);` is equivalents to\n * `chart.setOption(merge(opt1, opt2));` exactly,\n * this might be the only simple way to implement that feature.\n *\n * MEMO: We've considered some other approaches:\n * 1. Each model handles its self restoration but not uniform treatment.\n *     (Too complex in logic and error-prone)\n * 2. Use a shadow ecModel. (Performance expensive)\n *\n * FIXME: A possible solution:\n * Add a extra level of model for each component model. The inheritance chain would be:\n * ecModel <- componentModel <- componentActionModel <- dataItemModel\n * And all of the actions can only modify the `componentActionModel` rather than\n * `componentModel`. `setOption` will only modify the `ecModel` and `componentModel`.\n * When \"resotre\" action triggered, model from `componentActionModel` will be discarded\n * instead of recreating the \"ecModel\" from the \"_optionBackup\".\n */\n// function mergeToBackupOption(\n//     fakeCmptsMap: FakeComponentsMap,\n//     // `tarOption` Can be null/undefined, means init\n//     tarOption: ECUnitOption,\n//     newOption: ECUnitOption,\n//     // Can be null/undefined\n//     opt: InnerSetOptionOpts\n// ): void {\n//     newOption = newOption || {} as ECUnitOption;\n//     const notInit = !!tarOption;\n//     each(newOption, function (newOptsInMainType, mainType) {\n//         if (newOptsInMainType == null) {\n//             return;\n//         }\n//         if (!ComponentModel.hasClass(mainType)) {\n//             if (tarOption) {\n//                 tarOption[mainType] = merge(tarOption[mainType], newOptsInMainType, true);\n//             }\n//         }\n//         else {\n//             const oldTarOptsInMainType = notInit ? normalizeToArray(tarOption[mainType]) : null;\n//             const oldFakeCmptsInMainType = fakeCmptsMap.get(mainType) || [];\n//             const resultTarOptsInMainType = notInit ? (tarOption[mainType] = [] as ComponentOption[]) : null;\n//             const resultFakeCmptsInMainType = fakeCmptsMap.set(mainType, []);\n//             const mappingResult = mappingToExists(\n//                 oldFakeCmptsInMainType,\n//                 normalizeToArray(newOptsInMainType),\n//                 (opt && opt.replaceMergeMainTypeMap.get(mainType)) ? 'replaceMerge' : 'normalMerge'\n//             );\n//             setComponentTypeToKeyInfo(mappingResult, mainType, ComponentModel as ComponentModelConstructor);\n//             each(mappingResult, function (resultItem, index) {\n//                 // The same logic as `Global.ts#_mergeOption`.\n//                 let fakeCmpt = resultItem.existing;\n//                 const newOption = resultItem.newOption;\n//                 const keyInfo = resultItem.keyInfo;\n//                 let fakeCmptOpt;\n//                 if (!newOption) {\n//                     fakeCmptOpt = oldTarOptsInMainType[index];\n//                 }\n//                 else {\n//                     if (fakeCmpt && fakeCmpt.subType === keyInfo.subType) {\n//                         fakeCmpt.name = keyInfo.name;\n//                         if (notInit) {\n//                             fakeCmptOpt = merge(oldTarOptsInMainType[index], newOption, true);\n//                         }\n//                     }\n//                     else {\n//                         fakeCmpt = extend({}, keyInfo);\n//                         if (notInit) {\n//                             fakeCmptOpt = clone(newOption);\n//                         }\n//                     }\n//                 }\n//                 if (fakeCmpt) {\n//                     notInit && resultTarOptsInMainType.push(fakeCmptOpt);\n//                     resultFakeCmptsInMainType.push(fakeCmpt);\n//                 }\n//                 else {\n//                     notInit && resultTarOptsInMainType.push(void 0);\n//                     resultFakeCmptsInMainType.push(void 0);\n//                 }\n//             });\n//         }\n//     });\n// }\nexport default OptionManager;", "map": {"version": 3, "names": ["normalizeToArray", "each", "clone", "map", "isTypedArray", "setAsPrimitive", "isArray", "isObject", "error", "QUERY_REG", "OptionManager", "api", "_timelineOptions", "_mediaList", "_currentMediaIndices", "_api", "prototype", "setOption", "rawOption", "optionPreprocessorFuncs", "opt", "series", "data", "dataset", "source", "optionBackup", "_optionBackup", "newParsedOption", "parseRawOption", "_newBaseOption", "baseOption", "timelineOptions", "length", "mediaList", "mediaDefault", "mountOption", "isRecreate", "_mediaDefault", "getTimelineOption", "ecModel", "option", "timelineModel", "getComponent", "getCurrentIndex", "getMediaOption", "ec<PERSON><PERSON><PERSON>", "getWidth", "ecHeight", "getHeight", "indices", "result", "i", "len", "applyMediaQuery", "query", "push", "indicesEquals", "index", "isNew", "declaredBaseOption", "timelineOnRoot", "timeline", "timelineOptionsOnRoot", "options", "mediaOnRoot", "media", "hasMedia", "hasTimeline", "singleMedia", "process", "env", "NODE_ENV", "doPreprocess", "preProcess", "realMap", "width", "height", "<PERSON><PERSON><PERSON>", "applicable", "value", "attr", "matched", "match", "operator", "realAttr", "toLowerCase", "compare", "real", "expect", "indices1", "indices2", "join"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/model/OptionManager.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { normalizeToArray\n// , MappingExistingItem, setComponentTypeToKeyInfo, mappingToExists\n} from '../util/model.js';\nimport { each, clone, map, isTypedArray, setAsPrimitive, isArray, isObject\n// , HashMap , createHashMap, extend, merge,\n} from 'zrender/lib/core/util.js';\nimport { error } from '../util/log.js';\nvar QUERY_REG = /^(min|max)?(.+)$/;\n// Key: mainType\n// type FakeComponentsMap = HashMap<(MappingExistingItem & { subType: string })[]>;\n/**\n * TERM EXPLANATIONS:\n * See `ECOption` and `ECUnitOption` in `src/util/types.ts`.\n */\nvar OptionManager = /** @class */function () {\n  // timeline.notMerge is not supported in ec3. Firstly there is rearly\n  // case that notMerge is needed. Secondly supporting 'notMerge' requires\n  // rawOption cloned and backuped when timeline changed, which does no\n  // good to performance. What's more, that both timeline and setOption\n  // method supply 'notMerge' brings complex and some problems.\n  // Consider this case:\n  // (step1) chart.setOption({timeline: {notMerge: false}, ...}, false);\n  // (step2) chart.setOption({timeline: {notMerge: true}, ...}, false);\n  function OptionManager(api) {\n    this._timelineOptions = [];\n    this._mediaList = [];\n    /**\n     * -1, means default.\n     * empty means no media.\n     */\n    this._currentMediaIndices = [];\n    this._api = api;\n  }\n  OptionManager.prototype.setOption = function (rawOption, optionPreprocessorFuncs, opt) {\n    if (rawOption) {\n      // That set dat primitive is dangerous if user reuse the data when setOption again.\n      each(normalizeToArray(rawOption.series), function (series) {\n        series && series.data && isTypedArray(series.data) && setAsPrimitive(series.data);\n      });\n      each(normalizeToArray(rawOption.dataset), function (dataset) {\n        dataset && dataset.source && isTypedArray(dataset.source) && setAsPrimitive(dataset.source);\n      });\n    }\n    // Caution: some series modify option data, if do not clone,\n    // it should ensure that the repeat modify correctly\n    // (create a new object when modify itself).\n    rawOption = clone(rawOption);\n    // FIXME\n    // If some property is set in timeline options or media option but\n    // not set in baseOption, a warning should be given.\n    var optionBackup = this._optionBackup;\n    var newParsedOption = parseRawOption(rawOption, optionPreprocessorFuncs, !optionBackup);\n    this._newBaseOption = newParsedOption.baseOption;\n    // For setOption at second time (using merge mode);\n    if (optionBackup) {\n      // FIXME\n      // the restore merge solution is essentially incorrect.\n      // the mapping can not be 100% consistent with ecModel, which probably brings\n      // potential bug!\n      // The first merge is delayed, because in most cases, users do not call `setOption` twice.\n      // let fakeCmptsMap = this._fakeCmptsMap;\n      // if (!fakeCmptsMap) {\n      //     fakeCmptsMap = this._fakeCmptsMap = createHashMap();\n      //     mergeToBackupOption(fakeCmptsMap, null, optionBackup.baseOption, null);\n      // }\n      // mergeToBackupOption(\n      //     fakeCmptsMap, optionBackup.baseOption, newParsedOption.baseOption, opt\n      // );\n      // For simplicity, timeline options and media options do not support merge,\n      // that is, if you `setOption` twice and both has timeline options, the latter\n      // timeline options will not be merged to the former, but just substitute them.\n      if (newParsedOption.timelineOptions.length) {\n        optionBackup.timelineOptions = newParsedOption.timelineOptions;\n      }\n      if (newParsedOption.mediaList.length) {\n        optionBackup.mediaList = newParsedOption.mediaList;\n      }\n      if (newParsedOption.mediaDefault) {\n        optionBackup.mediaDefault = newParsedOption.mediaDefault;\n      }\n    } else {\n      this._optionBackup = newParsedOption;\n    }\n  };\n  OptionManager.prototype.mountOption = function (isRecreate) {\n    var optionBackup = this._optionBackup;\n    this._timelineOptions = optionBackup.timelineOptions;\n    this._mediaList = optionBackup.mediaList;\n    this._mediaDefault = optionBackup.mediaDefault;\n    this._currentMediaIndices = [];\n    return clone(isRecreate\n    // this._optionBackup.baseOption, which is created at the first `setOption`\n    // called, and is merged into every new option by inner method `mergeToBackupOption`\n    // each time `setOption` called, can be only used in `isRecreate`, because\n    // its reliability is under suspicion. In other cases option merge is\n    // performed by `model.mergeOption`.\n    ? optionBackup.baseOption : this._newBaseOption);\n  };\n  OptionManager.prototype.getTimelineOption = function (ecModel) {\n    var option;\n    var timelineOptions = this._timelineOptions;\n    if (timelineOptions.length) {\n      // getTimelineOption can only be called after ecModel inited,\n      // so we can get currentIndex from timelineModel.\n      var timelineModel = ecModel.getComponent('timeline');\n      if (timelineModel) {\n        option = clone(\n        // FIXME:TS as TimelineModel or quivlant interface\n        timelineOptions[timelineModel.getCurrentIndex()]);\n      }\n    }\n    return option;\n  };\n  OptionManager.prototype.getMediaOption = function (ecModel) {\n    var ecWidth = this._api.getWidth();\n    var ecHeight = this._api.getHeight();\n    var mediaList = this._mediaList;\n    var mediaDefault = this._mediaDefault;\n    var indices = [];\n    var result = [];\n    // No media defined.\n    if (!mediaList.length && !mediaDefault) {\n      return result;\n    }\n    // Multi media may be applied, the latter defined media has higher priority.\n    for (var i = 0, len = mediaList.length; i < len; i++) {\n      if (applyMediaQuery(mediaList[i].query, ecWidth, ecHeight)) {\n        indices.push(i);\n      }\n    }\n    // FIXME\n    // Whether mediaDefault should force users to provide? Otherwise\n    // the change by media query can not be recorvered.\n    if (!indices.length && mediaDefault) {\n      indices = [-1];\n    }\n    if (indices.length && !indicesEquals(indices, this._currentMediaIndices)) {\n      result = map(indices, function (index) {\n        return clone(index === -1 ? mediaDefault.option : mediaList[index].option);\n      });\n    }\n    // Otherwise return nothing.\n    this._currentMediaIndices = indices;\n    return result;\n  };\n  return OptionManager;\n}();\n/**\n * [RAW_OPTION_PATTERNS]\n * (Note: \"series: []\" represents all other props in `ECUnitOption`)\n *\n * (1) No prop \"baseOption\" declared:\n * Root option is used as \"baseOption\" (except prop \"options\" and \"media\").\n * ```js\n * option = {\n *     series: [],\n *     timeline: {},\n *     options: [],\n * };\n * option = {\n *     series: [],\n *     media: {},\n * };\n * option = {\n *     series: [],\n *     timeline: {},\n *     options: [],\n *     media: {},\n * }\n * ```\n *\n * (2) Prop \"baseOption\" declared:\n * If \"baseOption\" declared, `ECUnitOption` props can only be declared\n * inside \"baseOption\" except prop \"timeline\" (compat ec2).\n * ```js\n * option = {\n *     baseOption: {\n *         timeline: {},\n *         series: [],\n *     },\n *     options: []\n * };\n * option = {\n *     baseOption: {\n *         series: [],\n *     },\n *     media: []\n * };\n * option = {\n *     baseOption: {\n *         timeline: {},\n *         series: [],\n *     },\n *     options: []\n *     media: []\n * };\n * option = {\n *     // ec3 compat ec2: allow (only) `timeline` declared\n *     // outside baseOption. Keep this setting for compat.\n *     timeline: {},\n *     baseOption: {\n *         series: [],\n *     },\n *     options: [],\n *     media: []\n * };\n * ```\n */\nfunction parseRawOption(\n// `rawOption` May be modified\nrawOption, optionPreprocessorFuncs, isNew) {\n  var mediaList = [];\n  var mediaDefault;\n  var baseOption;\n  var declaredBaseOption = rawOption.baseOption;\n  // Compatible with ec2, [RAW_OPTION_PATTERNS] above.\n  var timelineOnRoot = rawOption.timeline;\n  var timelineOptionsOnRoot = rawOption.options;\n  var mediaOnRoot = rawOption.media;\n  var hasMedia = !!rawOption.media;\n  var hasTimeline = !!(timelineOptionsOnRoot || timelineOnRoot || declaredBaseOption && declaredBaseOption.timeline);\n  if (declaredBaseOption) {\n    baseOption = declaredBaseOption;\n    // For merge option.\n    if (!baseOption.timeline) {\n      baseOption.timeline = timelineOnRoot;\n    }\n  }\n  // For convenience, enable to use the root option as the `baseOption`:\n  // `{ ...normalOptionProps, media: [{ ... }, { ... }] }`\n  else {\n    if (hasTimeline || hasMedia) {\n      rawOption.options = rawOption.media = null;\n    }\n    baseOption = rawOption;\n  }\n  if (hasMedia) {\n    if (isArray(mediaOnRoot)) {\n      each(mediaOnRoot, function (singleMedia) {\n        if (process.env.NODE_ENV !== 'production') {\n          // Real case of wrong config.\n          if (singleMedia && !singleMedia.option && isObject(singleMedia.query) && isObject(singleMedia.query.option)) {\n            error('Illegal media option. Must be like { media: [ { query: {}, option: {} } ] }');\n          }\n        }\n        if (singleMedia && singleMedia.option) {\n          if (singleMedia.query) {\n            mediaList.push(singleMedia);\n          } else if (!mediaDefault) {\n            // Use the first media default.\n            mediaDefault = singleMedia;\n          }\n        }\n      });\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        // Real case of wrong config.\n        error('Illegal media option. Must be an array. Like { media: [ {...}, {...} ] }');\n      }\n    }\n  }\n  doPreprocess(baseOption);\n  each(timelineOptionsOnRoot, function (option) {\n    return doPreprocess(option);\n  });\n  each(mediaList, function (media) {\n    return doPreprocess(media.option);\n  });\n  function doPreprocess(option) {\n    each(optionPreprocessorFuncs, function (preProcess) {\n      preProcess(option, isNew);\n    });\n  }\n  return {\n    baseOption: baseOption,\n    timelineOptions: timelineOptionsOnRoot || [],\n    mediaDefault: mediaDefault,\n    mediaList: mediaList\n  };\n}\n/**\n * @see <http://www.w3.org/TR/css3-mediaqueries/#media1>\n * Support: width, height, aspectRatio\n * Can use max or min as prefix.\n */\nfunction applyMediaQuery(query, ecWidth, ecHeight) {\n  var realMap = {\n    width: ecWidth,\n    height: ecHeight,\n    aspectratio: ecWidth / ecHeight // lower case for convenience.\n  };\n\n  var applicable = true;\n  each(query, function (value, attr) {\n    var matched = attr.match(QUERY_REG);\n    if (!matched || !matched[1] || !matched[2]) {\n      return;\n    }\n    var operator = matched[1];\n    var realAttr = matched[2].toLowerCase();\n    if (!compare(realMap[realAttr], value, operator)) {\n      applicable = false;\n    }\n  });\n  return applicable;\n}\nfunction compare(real, expect, operator) {\n  if (operator === 'min') {\n    return real >= expect;\n  } else if (operator === 'max') {\n    return real <= expect;\n  } else {\n    // Equals\n    return real === expect;\n  }\n}\nfunction indicesEquals(indices1, indices2) {\n  // indices is always order by asc and has only finite number.\n  return indices1.join(',') === indices2.join(',');\n}\n/**\n * Consider case:\n * `chart.setOption(opt1);`\n * Then user do some interaction like dataZoom, dataView changing.\n * `chart.setOption(opt2);`\n * Then user press 'reset button' in toolbox.\n *\n * After doing that all of the interaction effects should be reset, the\n * chart should be the same as the result of invoke\n * `chart.setOption(opt1); chart.setOption(opt2);`.\n *\n * Although it is not able ensure that\n * `chart.setOption(opt1); chart.setOption(opt2);` is equivalents to\n * `chart.setOption(merge(opt1, opt2));` exactly,\n * this might be the only simple way to implement that feature.\n *\n * MEMO: We've considered some other approaches:\n * 1. Each model handles its self restoration but not uniform treatment.\n *     (Too complex in logic and error-prone)\n * 2. Use a shadow ecModel. (Performance expensive)\n *\n * FIXME: A possible solution:\n * Add a extra level of model for each component model. The inheritance chain would be:\n * ecModel <- componentModel <- componentActionModel <- dataItemModel\n * And all of the actions can only modify the `componentActionModel` rather than\n * `componentModel`. `setOption` will only modify the `ecModel` and `componentModel`.\n * When \"resotre\" action triggered, model from `componentActionModel` will be discarded\n * instead of recreating the \"ecModel\" from the \"_optionBackup\".\n */\n// function mergeToBackupOption(\n//     fakeCmptsMap: FakeComponentsMap,\n//     // `tarOption` Can be null/undefined, means init\n//     tarOption: ECUnitOption,\n//     newOption: ECUnitOption,\n//     // Can be null/undefined\n//     opt: InnerSetOptionOpts\n// ): void {\n//     newOption = newOption || {} as ECUnitOption;\n//     const notInit = !!tarOption;\n//     each(newOption, function (newOptsInMainType, mainType) {\n//         if (newOptsInMainType == null) {\n//             return;\n//         }\n//         if (!ComponentModel.hasClass(mainType)) {\n//             if (tarOption) {\n//                 tarOption[mainType] = merge(tarOption[mainType], newOptsInMainType, true);\n//             }\n//         }\n//         else {\n//             const oldTarOptsInMainType = notInit ? normalizeToArray(tarOption[mainType]) : null;\n//             const oldFakeCmptsInMainType = fakeCmptsMap.get(mainType) || [];\n//             const resultTarOptsInMainType = notInit ? (tarOption[mainType] = [] as ComponentOption[]) : null;\n//             const resultFakeCmptsInMainType = fakeCmptsMap.set(mainType, []);\n//             const mappingResult = mappingToExists(\n//                 oldFakeCmptsInMainType,\n//                 normalizeToArray(newOptsInMainType),\n//                 (opt && opt.replaceMergeMainTypeMap.get(mainType)) ? 'replaceMerge' : 'normalMerge'\n//             );\n//             setComponentTypeToKeyInfo(mappingResult, mainType, ComponentModel as ComponentModelConstructor);\n//             each(mappingResult, function (resultItem, index) {\n//                 // The same logic as `Global.ts#_mergeOption`.\n//                 let fakeCmpt = resultItem.existing;\n//                 const newOption = resultItem.newOption;\n//                 const keyInfo = resultItem.keyInfo;\n//                 let fakeCmptOpt;\n//                 if (!newOption) {\n//                     fakeCmptOpt = oldTarOptsInMainType[index];\n//                 }\n//                 else {\n//                     if (fakeCmpt && fakeCmpt.subType === keyInfo.subType) {\n//                         fakeCmpt.name = keyInfo.name;\n//                         if (notInit) {\n//                             fakeCmptOpt = merge(oldTarOptsInMainType[index], newOption, true);\n//                         }\n//                     }\n//                     else {\n//                         fakeCmpt = extend({}, keyInfo);\n//                         if (notInit) {\n//                             fakeCmptOpt = clone(newOption);\n//                         }\n//                     }\n//                 }\n//                 if (fakeCmpt) {\n//                     notInit && resultTarOptsInMainType.push(fakeCmptOpt);\n//                     resultFakeCmptsInMainType.push(fakeCmpt);\n//                 }\n//                 else {\n//                     notInit && resultTarOptsInMainType.push(void 0);\n//                     resultFakeCmptsInMainType.push(void 0);\n//                 }\n//             });\n//         }\n//     });\n// }\nexport default OptionManager;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA;AACT;AAAA,OACO,kBAAkB;AACzB,SAASC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,YAAY,EAAEC,cAAc,EAAEC,OAAO,EAAEC;AAClE;AAAA,OACO,0BAA0B;AACjC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,IAAIC,SAAS,GAAG,kBAAkB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,aAAa,YAAY;EAC3C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASA,aAAaA,CAACC,GAAG,EAAE;IAC1B,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB;AACJ;AACA;AACA;IACI,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,IAAI,GAAGJ,GAAG;EACjB;EACAD,aAAa,CAACM,SAAS,CAACC,SAAS,GAAG,UAAUC,SAAS,EAAEC,uBAAuB,EAAEC,GAAG,EAAE;IACrF,IAAIF,SAAS,EAAE;MACb;MACAjB,IAAI,CAACD,gBAAgB,CAACkB,SAAS,CAACG,MAAM,CAAC,EAAE,UAAUA,MAAM,EAAE;QACzDA,MAAM,IAAIA,MAAM,CAACC,IAAI,IAAIlB,YAAY,CAACiB,MAAM,CAACC,IAAI,CAAC,IAAIjB,cAAc,CAACgB,MAAM,CAACC,IAAI,CAAC;MACnF,CAAC,CAAC;MACFrB,IAAI,CAACD,gBAAgB,CAACkB,SAAS,CAACK,OAAO,CAAC,EAAE,UAAUA,OAAO,EAAE;QAC3DA,OAAO,IAAIA,OAAO,CAACC,MAAM,IAAIpB,YAAY,CAACmB,OAAO,CAACC,MAAM,CAAC,IAAInB,cAAc,CAACkB,OAAO,CAACC,MAAM,CAAC;MAC7F,CAAC,CAAC;IACJ;IACA;IACA;IACA;IACAN,SAAS,GAAGhB,KAAK,CAACgB,SAAS,CAAC;IAC5B;IACA;IACA;IACA,IAAIO,YAAY,GAAG,IAAI,CAACC,aAAa;IACrC,IAAIC,eAAe,GAAGC,cAAc,CAACV,SAAS,EAAEC,uBAAuB,EAAE,CAACM,YAAY,CAAC;IACvF,IAAI,CAACI,cAAc,GAAGF,eAAe,CAACG,UAAU;IAChD;IACA,IAAIL,YAAY,EAAE;MAChB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIE,eAAe,CAACI,eAAe,CAACC,MAAM,EAAE;QAC1CP,YAAY,CAACM,eAAe,GAAGJ,eAAe,CAACI,eAAe;MAChE;MACA,IAAIJ,eAAe,CAACM,SAAS,CAACD,MAAM,EAAE;QACpCP,YAAY,CAACQ,SAAS,GAAGN,eAAe,CAACM,SAAS;MACpD;MACA,IAAIN,eAAe,CAACO,YAAY,EAAE;QAChCT,YAAY,CAACS,YAAY,GAAGP,eAAe,CAACO,YAAY;MAC1D;IACF,CAAC,MAAM;MACL,IAAI,CAACR,aAAa,GAAGC,eAAe;IACtC;EACF,CAAC;EACDjB,aAAa,CAACM,SAAS,CAACmB,WAAW,GAAG,UAAUC,UAAU,EAAE;IAC1D,IAAIX,YAAY,GAAG,IAAI,CAACC,aAAa;IACrC,IAAI,CAACd,gBAAgB,GAAGa,YAAY,CAACM,eAAe;IACpD,IAAI,CAAClB,UAAU,GAAGY,YAAY,CAACQ,SAAS;IACxC,IAAI,CAACI,aAAa,GAAGZ,YAAY,CAACS,YAAY;IAC9C,IAAI,CAACpB,oBAAoB,GAAG,EAAE;IAC9B,OAAOZ,KAAK,CAACkC;IACb;IACA;IACA;IACA;IACA;IAAA,EACEX,YAAY,CAACK,UAAU,GAAG,IAAI,CAACD,cAAc,CAAC;EAClD,CAAC;EACDnB,aAAa,CAACM,SAAS,CAACsB,iBAAiB,GAAG,UAAUC,OAAO,EAAE;IAC7D,IAAIC,MAAM;IACV,IAAIT,eAAe,GAAG,IAAI,CAACnB,gBAAgB;IAC3C,IAAImB,eAAe,CAACC,MAAM,EAAE;MAC1B;MACA;MACA,IAAIS,aAAa,GAAGF,OAAO,CAACG,YAAY,CAAC,UAAU,CAAC;MACpD,IAAID,aAAa,EAAE;QACjBD,MAAM,GAAGtC,KAAK;QACd;QACA6B,eAAe,CAACU,aAAa,CAACE,eAAe,CAAC,CAAC,CAAC,CAAC;MACnD;IACF;IACA,OAAOH,MAAM;EACf,CAAC;EACD9B,aAAa,CAACM,SAAS,CAAC4B,cAAc,GAAG,UAAUL,OAAO,EAAE;IAC1D,IAAIM,OAAO,GAAG,IAAI,CAAC9B,IAAI,CAAC+B,QAAQ,CAAC,CAAC;IAClC,IAAIC,QAAQ,GAAG,IAAI,CAAChC,IAAI,CAACiC,SAAS,CAAC,CAAC;IACpC,IAAIf,SAAS,GAAG,IAAI,CAACpB,UAAU;IAC/B,IAAIqB,YAAY,GAAG,IAAI,CAACG,aAAa;IACrC,IAAIY,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAG,EAAE;IACf;IACA,IAAI,CAACjB,SAAS,CAACD,MAAM,IAAI,CAACE,YAAY,EAAE;MACtC,OAAOgB,MAAM;IACf;IACA;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGnB,SAAS,CAACD,MAAM,EAAEmB,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACpD,IAAIE,eAAe,CAACpB,SAAS,CAACkB,CAAC,CAAC,CAACG,KAAK,EAAET,OAAO,EAAEE,QAAQ,CAAC,EAAE;QAC1DE,OAAO,CAACM,IAAI,CAACJ,CAAC,CAAC;MACjB;IACF;IACA;IACA;IACA;IACA,IAAI,CAACF,OAAO,CAACjB,MAAM,IAAIE,YAAY,EAAE;MACnCe,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;IAChB;IACA,IAAIA,OAAO,CAACjB,MAAM,IAAI,CAACwB,aAAa,CAACP,OAAO,EAAE,IAAI,CAACnC,oBAAoB,CAAC,EAAE;MACxEoC,MAAM,GAAG/C,GAAG,CAAC8C,OAAO,EAAE,UAAUQ,KAAK,EAAE;QACrC,OAAOvD,KAAK,CAACuD,KAAK,KAAK,CAAC,CAAC,GAAGvB,YAAY,CAACM,MAAM,GAAGP,SAAS,CAACwB,KAAK,CAAC,CAACjB,MAAM,CAAC;MAC5E,CAAC,CAAC;IACJ;IACA;IACA,IAAI,CAAC1B,oBAAoB,GAAGmC,OAAO;IACnC,OAAOC,MAAM;EACf,CAAC;EACD,OAAOxC,aAAa;AACtB,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,cAAcA;AACvB;AACAV,SAAS,EAAEC,uBAAuB,EAAEuC,KAAK,EAAE;EACzC,IAAIzB,SAAS,GAAG,EAAE;EAClB,IAAIC,YAAY;EAChB,IAAIJ,UAAU;EACd,IAAI6B,kBAAkB,GAAGzC,SAAS,CAACY,UAAU;EAC7C;EACA,IAAI8B,cAAc,GAAG1C,SAAS,CAAC2C,QAAQ;EACvC,IAAIC,qBAAqB,GAAG5C,SAAS,CAAC6C,OAAO;EAC7C,IAAIC,WAAW,GAAG9C,SAAS,CAAC+C,KAAK;EACjC,IAAIC,QAAQ,GAAG,CAAC,CAAChD,SAAS,CAAC+C,KAAK;EAChC,IAAIE,WAAW,GAAG,CAAC,EAAEL,qBAAqB,IAAIF,cAAc,IAAID,kBAAkB,IAAIA,kBAAkB,CAACE,QAAQ,CAAC;EAClH,IAAIF,kBAAkB,EAAE;IACtB7B,UAAU,GAAG6B,kBAAkB;IAC/B;IACA,IAAI,CAAC7B,UAAU,CAAC+B,QAAQ,EAAE;MACxB/B,UAAU,CAAC+B,QAAQ,GAAGD,cAAc;IACtC;EACF;EACA;EACA;EAAA,KACK;IACH,IAAIO,WAAW,IAAID,QAAQ,EAAE;MAC3BhD,SAAS,CAAC6C,OAAO,GAAG7C,SAAS,CAAC+C,KAAK,GAAG,IAAI;IAC5C;IACAnC,UAAU,GAAGZ,SAAS;EACxB;EACA,IAAIgD,QAAQ,EAAE;IACZ,IAAI5D,OAAO,CAAC0D,WAAW,CAAC,EAAE;MACxB/D,IAAI,CAAC+D,WAAW,EAAE,UAAUI,WAAW,EAAE;QACvC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC;UACA,IAAIH,WAAW,IAAI,CAACA,WAAW,CAAC5B,MAAM,IAAIjC,QAAQ,CAAC6D,WAAW,CAACd,KAAK,CAAC,IAAI/C,QAAQ,CAAC6D,WAAW,CAACd,KAAK,CAACd,MAAM,CAAC,EAAE;YAC3GhC,KAAK,CAAC,6EAA6E,CAAC;UACtF;QACF;QACA,IAAI4D,WAAW,IAAIA,WAAW,CAAC5B,MAAM,EAAE;UACrC,IAAI4B,WAAW,CAACd,KAAK,EAAE;YACrBrB,SAAS,CAACsB,IAAI,CAACa,WAAW,CAAC;UAC7B,CAAC,MAAM,IAAI,CAAClC,YAAY,EAAE;YACxB;YACAA,YAAY,GAAGkC,WAAW;UAC5B;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC;QACA/D,KAAK,CAAC,0EAA0E,CAAC;MACnF;IACF;EACF;EACAgE,YAAY,CAAC1C,UAAU,CAAC;EACxB7B,IAAI,CAAC6D,qBAAqB,EAAE,UAAUtB,MAAM,EAAE;IAC5C,OAAOgC,YAAY,CAAChC,MAAM,CAAC;EAC7B,CAAC,CAAC;EACFvC,IAAI,CAACgC,SAAS,EAAE,UAAUgC,KAAK,EAAE;IAC/B,OAAOO,YAAY,CAACP,KAAK,CAACzB,MAAM,CAAC;EACnC,CAAC,CAAC;EACF,SAASgC,YAAYA,CAAChC,MAAM,EAAE;IAC5BvC,IAAI,CAACkB,uBAAuB,EAAE,UAAUsD,UAAU,EAAE;MAClDA,UAAU,CAACjC,MAAM,EAAEkB,KAAK,CAAC;IAC3B,CAAC,CAAC;EACJ;EACA,OAAO;IACL5B,UAAU,EAAEA,UAAU;IACtBC,eAAe,EAAE+B,qBAAqB,IAAI,EAAE;IAC5C5B,YAAY,EAAEA,YAAY;IAC1BD,SAAS,EAAEA;EACb,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA,SAASoB,eAAeA,CAACC,KAAK,EAAET,OAAO,EAAEE,QAAQ,EAAE;EACjD,IAAI2B,OAAO,GAAG;IACZC,KAAK,EAAE9B,OAAO;IACd+B,MAAM,EAAE7B,QAAQ;IAChB8B,WAAW,EAAEhC,OAAO,GAAGE,QAAQ,CAAC;EAClC,CAAC;EAED,IAAI+B,UAAU,GAAG,IAAI;EACrB7E,IAAI,CAACqD,KAAK,EAAE,UAAUyB,KAAK,EAAEC,IAAI,EAAE;IACjC,IAAIC,OAAO,GAAGD,IAAI,CAACE,KAAK,CAACzE,SAAS,CAAC;IACnC,IAAI,CAACwE,OAAO,IAAI,CAACA,OAAO,CAAC,CAAC,CAAC,IAAI,CAACA,OAAO,CAAC,CAAC,CAAC,EAAE;MAC1C;IACF;IACA,IAAIE,QAAQ,GAAGF,OAAO,CAAC,CAAC,CAAC;IACzB,IAAIG,QAAQ,GAAGH,OAAO,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;IACvC,IAAI,CAACC,OAAO,CAACZ,OAAO,CAACU,QAAQ,CAAC,EAAEL,KAAK,EAAEI,QAAQ,CAAC,EAAE;MAChDL,UAAU,GAAG,KAAK;IACpB;EACF,CAAC,CAAC;EACF,OAAOA,UAAU;AACnB;AACA,SAASQ,OAAOA,CAACC,IAAI,EAAEC,MAAM,EAAEL,QAAQ,EAAE;EACvC,IAAIA,QAAQ,KAAK,KAAK,EAAE;IACtB,OAAOI,IAAI,IAAIC,MAAM;EACvB,CAAC,MAAM,IAAIL,QAAQ,KAAK,KAAK,EAAE;IAC7B,OAAOI,IAAI,IAAIC,MAAM;EACvB,CAAC,MAAM;IACL;IACA,OAAOD,IAAI,KAAKC,MAAM;EACxB;AACF;AACA,SAAShC,aAAaA,CAACiC,QAAQ,EAAEC,QAAQ,EAAE;EACzC;EACA,OAAOD,QAAQ,CAACE,IAAI,CAAC,GAAG,CAAC,KAAKD,QAAQ,CAACC,IAAI,CAAC,GAAG,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAejF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}