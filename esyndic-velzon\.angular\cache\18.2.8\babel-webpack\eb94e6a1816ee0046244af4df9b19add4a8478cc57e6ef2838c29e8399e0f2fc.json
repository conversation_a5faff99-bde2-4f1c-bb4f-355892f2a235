{"ast": null, "code": "import { RouterModule } from '@angular/router';\n// Component pages\nimport { StatisticsComponent } from './statistics/statistics.component';\nimport { ApplicationComponent } from './application/application.component';\nimport { NewjobComponent } from './newjob/newjob.component';\nimport { CompanieslistComponent } from './companieslist/companieslist.component';\nimport { JobcategoriesComponent } from './jobcategories/jobcategories.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: \"statistics\",\n  component: StatisticsComponent\n}, {\n  path: \"application\",\n  component: ApplicationComponent\n}, {\n  path: \"newjob\",\n  component: NewjobComponent\n}, {\n  path: \"companies-list\",\n  component: CompanieslistComponent\n}, {\n  path: \"job-categories\",\n  component: JobcategoriesComponent\n}, {\n  path: 'job-lists',\n  loadChildren: () => import('./job-lists/job-lists.module').then(m => m.JobListsModule)\n}, {\n  path: 'candidate-lists',\n  loadChildren: () => import('./candidate-lists/candidate-lists.module').then(m => m.CandidateListsModule)\n}];\nexport class JobsRoutingModule {\n  static {\n    this.ɵfac = function JobsRoutingModule_Factory(t) {\n      return new (t || JobsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: JobsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(JobsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "StatisticsComponent", "ApplicationComponent", "NewjobComponent", "CompanieslistComponent", "JobcategoriesComponent", "routes", "path", "component", "loadChildren", "then", "m", "JobListsModule", "CandidateListsModule", "JobsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\jobs-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\n// Component pages\r\nimport { StatisticsComponent } from './statistics/statistics.component';\r\nimport { ApplicationComponent } from './application/application.component';\r\nimport { NewjobComponent } from './newjob/newjob.component';\r\nimport { CompanieslistComponent } from './companieslist/companieslist.component';\r\nimport { JobcategoriesComponent } from './jobcategories/jobcategories.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: \"statistics\",\r\n    component: StatisticsComponent\r\n  },\r\n  {\r\n    path: \"application\",\r\n    component: ApplicationComponent\r\n  },\r\n  {\r\n    path: \"newjob\",\r\n    component: NewjobComponent\r\n  },\r\n  {\r\n    path: \"companies-list\",\r\n    component: CompanieslistComponent\r\n  },\r\n  {\r\n    path: \"job-categories\",\r\n    component: JobcategoriesComponent\r\n  },\r\n  {\r\n    path: 'job-lists', loadChildren: () => import('./job-lists/job-lists.module').then(m => m.JobListsModule)\r\n  },\r\n  {\r\n    path: 'candidate-lists', loadChildren: () => import('./candidate-lists/candidate-lists.module').then(m => m.CandidateListsModule)\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class JobsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AAEtD;AACA,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,sBAAsB,QAAQ,yCAAyC;AAChF,SAASC,sBAAsB,QAAQ,yCAAyC;;;AAEhF,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEP;CACZ,EACD;EACEM,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,WAAW;EAAEE,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc;CACzG,EACD;EACEL,IAAI,EAAE,iBAAiB;EAAEE,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,oBAAoB;CACjI,CACF;AAMD,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBd,YAAY,CAACe,QAAQ,CAACT,MAAM,CAAC,EAC7BN,YAAY;IAAA;EAAA;;;2EAEXc,iBAAiB;IAAAE,OAAA,GAAAC,EAAA,CAAAjB,YAAA;IAAAkB,OAAA,GAFlBlB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}