{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Georgian [ka]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/<PERSON><PERSON><PERSON><PERSON><PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ka = moment.defineLocale('ka', {\n    months: 'იანვარი_თებერვალი_მარტი_აპრილი_მაისი_ივნისი_ივლისი_აგვისტო_სექტემბერი_ოქტომბერი_ნოემბერი_დეკემბერი'.split('_'),\n    monthsShort: 'იან_თებ_მარ_აპრ_მაი_ივნ_ივლ_აგვ_სექ_ოქტ_ნოე_დეკ'.split('_'),\n    weekdays: {\n      standalone: 'კვირა_ორშაბათი_სამშაბათი_ოთხშაბათი_ხუთშაბათი_პარასკევი_შაბათი'.split('_'),\n      format: 'კვირას_ორშაბათს_სამშაბათს_ოთხშაბათს_ხუთშაბათს_პარასკევს_შაბათს'.split('_'),\n      isFormat: /(წინა|შემდეგ)/\n    },\n    weekdaysShort: 'კვი_ორშ_სამ_ოთხ_ხუთ_პარ_შაბ'.split('_'),\n    weekdaysMin: 'კვ_ორ_სა_ოთ_ხუ_პა_შა'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[დღეს] LT[-ზე]',\n      nextDay: '[ხვალ] LT[-ზე]',\n      lastDay: '[გუშინ] LT[-ზე]',\n      nextWeek: '[შემდეგ] dddd LT[-ზე]',\n      lastWeek: '[წინა] dddd LT-ზე',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: function (s) {\n        return s.replace(/(წამ|წუთ|საათ|წელ|დღ|თვ)(ი|ე)/, function ($0, $1, $2) {\n          return $2 === 'ი' ? $1 + 'ში' : $1 + $2 + 'ში';\n        });\n      },\n      past: function (s) {\n        if (/(წამი|წუთი|საათი|დღე|თვე)/.test(s)) {\n          return s.replace(/(ი|ე)$/, 'ის წინ');\n        }\n        if (/წელი/.test(s)) {\n          return s.replace(/წელი$/, 'წლის წინ');\n        }\n        return s;\n      },\n      s: 'რამდენიმე წამი',\n      ss: '%d წამი',\n      m: 'წუთი',\n      mm: '%d წუთი',\n      h: 'საათი',\n      hh: '%d საათი',\n      d: 'დღე',\n      dd: '%d დღე',\n      M: 'თვე',\n      MM: '%d თვე',\n      y: 'წელი',\n      yy: '%d წელი'\n    },\n    dayOfMonthOrdinalParse: /0|1-ლი|მე-\\d{1,2}|\\d{1,2}-ე/,\n    ordinal: function (number) {\n      if (number === 0) {\n        return number;\n      }\n      if (number === 1) {\n        return number + '-ლი';\n      }\n      if (number < 20 || number <= 100 && number % 20 === 0 || number % 100 === 0) {\n        return 'მე-' + number;\n      }\n      return number + '-ე';\n    },\n    week: {\n      dow: 1,\n      doy: 7\n    }\n  });\n  return ka;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "ka", "defineLocale", "months", "split", "monthsShort", "weekdays", "standalone", "format", "isFormat", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "lastDay", "nextWeek", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "s", "replace", "$0", "$1", "$2", "past", "test", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "week", "dow", "doy"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/moment/locale/ka.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Georgian [ka]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/<PERSON><PERSON><PERSON><PERSON><PERSON>\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var ka = moment.defineLocale('ka', {\n        months: 'იანვარი_თებერვალი_მარტი_აპრილი_მაისი_ივნისი_ივლისი_აგვისტო_სექტემბერი_ოქტომბერი_ნოემბერი_დეკემბერი'.split(\n            '_'\n        ),\n        monthsShort: 'იან_თებ_მარ_აპრ_მაი_ივნ_ივლ_აგვ_სექ_ოქტ_ნოე_დეკ'.split('_'),\n        weekdays: {\n            standalone:\n                'კვირა_ორშაბათი_სამშაბათი_ოთხშაბათი_ხუთშაბათი_პარასკევი_შაბათი'.split(\n                    '_'\n                ),\n            format: 'კვირას_ორშაბათს_სამშაბათს_ოთხშაბათს_ხუთშაბათს_პარასკევს_შაბათს'.split(\n                '_'\n            ),\n            isFormat: /(წინა|შემდეგ)/,\n        },\n        weekdaysShort: 'კვი_ორშ_სამ_ოთხ_ხუთ_პარ_შაბ'.split('_'),\n        weekdaysMin: 'კვ_ორ_სა_ოთ_ხუ_პა_შა'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[დღეს] LT[-ზე]',\n            nextDay: '[ხვალ] LT[-ზე]',\n            lastDay: '[გუშინ] LT[-ზე]',\n            nextWeek: '[შემდეგ] dddd LT[-ზე]',\n            lastWeek: '[წინა] dddd LT-ზე',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: function (s) {\n                return s.replace(\n                    /(წამ|წუთ|საათ|წელ|დღ|თვ)(ი|ე)/,\n                    function ($0, $1, $2) {\n                        return $2 === 'ი' ? $1 + 'ში' : $1 + $2 + 'ში';\n                    }\n                );\n            },\n            past: function (s) {\n                if (/(წამი|წუთი|საათი|დღე|თვე)/.test(s)) {\n                    return s.replace(/(ი|ე)$/, 'ის წინ');\n                }\n                if (/წელი/.test(s)) {\n                    return s.replace(/წელი$/, 'წლის წინ');\n                }\n                return s;\n            },\n            s: 'რამდენიმე წამი',\n            ss: '%d წამი',\n            m: 'წუთი',\n            mm: '%d წუთი',\n            h: 'საათი',\n            hh: '%d საათი',\n            d: 'დღე',\n            dd: '%d დღე',\n            M: 'თვე',\n            MM: '%d თვე',\n            y: 'წელი',\n            yy: '%d წელი',\n        },\n        dayOfMonthOrdinalParse: /0|1-ლი|მე-\\d{1,2}|\\d{1,2}-ე/,\n        ordinal: function (number) {\n            if (number === 0) {\n                return number;\n            }\n            if (number === 1) {\n                return number + '-ლი';\n            }\n            if (\n                number < 20 ||\n                (number <= 100 && number % 20 === 0) ||\n                number % 100 === 0\n            ) {\n                return 'მე-' + number;\n            }\n            return number + '-ე';\n        },\n        week: {\n            dow: 1,\n            doy: 7,\n        },\n    });\n\n    return ka;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,oGAAoG,CAACC,KAAK,CAC9G,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE;MACNC,UAAU,EACN,+DAA+D,CAACH,KAAK,CACjE,GACJ,CAAC;MACLI,MAAM,EAAE,gEAAgE,CAACJ,KAAK,CAC1E,GACJ,CAAC;MACDK,QAAQ,EAAE;IACd,CAAC;IACDC,aAAa,EAAE,6BAA6B,CAACN,KAAK,CAAC,GAAG,CAAC;IACvDO,WAAW,EAAE,sBAAsB,CAACP,KAAK,CAAC,GAAG,CAAC;IAC9CQ,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,uBAAuB;MACjCC,QAAQ,EAAE,mBAAmB;MAC7BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAAA,CAAUC,CAAC,EAAE;QACjB,OAAOA,CAAC,CAACC,OAAO,CACZ,+BAA+B,EAC/B,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;UAClB,OAAOA,EAAE,KAAK,GAAG,GAAGD,EAAE,GAAG,IAAI,GAAGA,EAAE,GAAGC,EAAE,GAAG,IAAI;QAClD,CACJ,CAAC;MACL,CAAC;MACDC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;QACf,IAAI,2BAA2B,CAACM,IAAI,CAACN,CAAC,CAAC,EAAE;UACrC,OAAOA,CAAC,CAACC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC;QACxC;QACA,IAAI,MAAM,CAACK,IAAI,CAACN,CAAC,CAAC,EAAE;UAChB,OAAOA,CAAC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC;QACzC;QACA,OAAOD,CAAC;MACZ,CAAC;MACDA,CAAC,EAAE,gBAAgB;MACnBO,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,6BAA6B;IACrDC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,OAAOA,MAAM;MACjB;MACA,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,OAAOA,MAAM,GAAG,KAAK;MACzB;MACA,IACIA,MAAM,GAAG,EAAE,IACVA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,EAAE,KAAK,CAAE,IACpCA,MAAM,GAAG,GAAG,KAAK,CAAC,EACpB;QACE,OAAO,KAAK,GAAGA,MAAM;MACzB;MACA,OAAOA,MAAM,GAAG,IAAI;IACxB,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MACNC,GAAG,EAAE;IACT;EACJ,CAAC,CAAC;EAEF,OAAOlD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}