{"ast": null, "code": "import _asyncToGenerator from \"C:/e-syndic/esyndic-velzon/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Component, ComponentContainer } from '@firebase/component';\nimport { Logger, setUserLogHandler, setLogLevel as setLogLevel$1 } from '@firebase/logger';\nimport { ErrorFactory, getDefaultAppConfig, deepEqual, isBrowser, isWebWorker, FirebaseError, base64urlEncodeWithoutPadding, isIndexedDBAvailable, validateIndexedDBOpenable } from '@firebase/util';\nexport { FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass PlatformLoggerServiceImpl {\n  constructor(container) {\n    this.container = container;\n  }\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString() {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers.map(provider => {\n      if (isVersionServiceProvider(provider)) {\n        const service = provider.getImmediate();\n        return `${service.library}/${service.version}`;\n      } else {\n        return null;\n      }\n    }).filter(logString => logString).join(' ');\n  }\n}\n/**\r\n *\r\n * @param provider check if this provider provides a VersionService\r\n *\r\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\r\n * provides VersionService. The provider is not necessarily a 'app-version'\r\n * provider.\r\n */\nfunction isVersionServiceProvider(provider) {\n  const component = provider.getComponent();\n  return (component === null || component === void 0 ? void 0 : component.type) === \"VERSION\" /* ComponentType.VERSION */;\n}\nconst name$q = \"@firebase/app\";\nconst version$1 = \"0.10.13\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst logger = new Logger('@firebase/app');\nconst name$p = \"@firebase/app-compat\";\nconst name$o = \"@firebase/analytics-compat\";\nconst name$n = \"@firebase/analytics\";\nconst name$m = \"@firebase/app-check-compat\";\nconst name$l = \"@firebase/app-check\";\nconst name$k = \"@firebase/auth\";\nconst name$j = \"@firebase/auth-compat\";\nconst name$i = \"@firebase/database\";\nconst name$h = \"@firebase/data-connect\";\nconst name$g = \"@firebase/database-compat\";\nconst name$f = \"@firebase/functions\";\nconst name$e = \"@firebase/functions-compat\";\nconst name$d = \"@firebase/installations\";\nconst name$c = \"@firebase/installations-compat\";\nconst name$b = \"@firebase/messaging\";\nconst name$a = \"@firebase/messaging-compat\";\nconst name$9 = \"@firebase/performance\";\nconst name$8 = \"@firebase/performance-compat\";\nconst name$7 = \"@firebase/remote-config\";\nconst name$6 = \"@firebase/remote-config-compat\";\nconst name$5 = \"@firebase/storage\";\nconst name$4 = \"@firebase/storage-compat\";\nconst name$3 = \"@firebase/firestore\";\nconst name$2 = \"@firebase/vertexai-preview\";\nconst name$1 = \"@firebase/firestore-compat\";\nconst name = \"firebase\";\nconst version = \"10.14.1\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * The default app name\r\n *\r\n * @internal\r\n */\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\nconst PLATFORM_LOG_STRING = {\n  [name$q]: 'fire-core',\n  [name$p]: 'fire-core-compat',\n  [name$n]: 'fire-analytics',\n  [name$o]: 'fire-analytics-compat',\n  [name$l]: 'fire-app-check',\n  [name$m]: 'fire-app-check-compat',\n  [name$k]: 'fire-auth',\n  [name$j]: 'fire-auth-compat',\n  [name$i]: 'fire-rtdb',\n  [name$h]: 'fire-data-connect',\n  [name$g]: 'fire-rtdb-compat',\n  [name$f]: 'fire-fn',\n  [name$e]: 'fire-fn-compat',\n  [name$d]: 'fire-iid',\n  [name$c]: 'fire-iid-compat',\n  [name$b]: 'fire-fcm',\n  [name$a]: 'fire-fcm-compat',\n  [name$9]: 'fire-perf',\n  [name$8]: 'fire-perf-compat',\n  [name$7]: 'fire-rc',\n  [name$6]: 'fire-rc-compat',\n  [name$5]: 'fire-gcs',\n  [name$4]: 'fire-gcs-compat',\n  [name$3]: 'fire-fst',\n  [name$1]: 'fire-fst-compat',\n  [name$2]: 'fire-vertex',\n  'fire-js': 'fire-js',\n  [name]: 'fire-js-all'\n};\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * @internal\r\n */\nconst _apps = new Map();\n/**\r\n * @internal\r\n */\nconst _serverApps = new Map();\n/**\r\n * Registered components.\r\n *\r\n * @internal\r\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst _components = new Map();\n/**\r\n * @param component - the component being added to this app's container\r\n *\r\n * @internal\r\n */\nfunction _addComponent(app, component) {\n  try {\n    app.container.addComponent(component);\n  } catch (e) {\n    logger.debug(`Component ${component.name} failed to register with FirebaseApp ${app.name}`, e);\n  }\n}\n/**\r\n *\r\n * @internal\r\n */\nfunction _addOrOverwriteComponent(app, component) {\n  app.container.addOrOverwriteComponent(component);\n}\n/**\r\n *\r\n * @param component - the component to register\r\n * @returns whether or not the component is registered successfully\r\n *\r\n * @internal\r\n */\nfunction _registerComponent(component) {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(`There were multiple attempts to register component ${componentName}.`);\n    return false;\n  }\n  _components.set(componentName, component);\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app, component);\n  }\n  for (const serverApp of _serverApps.values()) {\n    _addComponent(serverApp, component);\n  }\n  return true;\n}\n/**\r\n *\r\n * @param app - FirebaseApp instance\r\n * @param name - service name\r\n *\r\n * @returns the provider for the service with the matching name\r\n *\r\n * @internal\r\n */\nfunction _getProvider(app, name) {\n  const heartbeatController = app.container.getProvider('heartbeat').getImmediate({\n    optional: true\n  });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return app.container.getProvider(name);\n}\n/**\r\n *\r\n * @param app - FirebaseApp instance\r\n * @param name - service name\r\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\r\n *\r\n * @internal\r\n */\nfunction _removeServiceInstance(app, name, instanceIdentifier = DEFAULT_ENTRY_NAME) {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n/**\r\n *\r\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\r\n *\r\n * @returns true if the provide object is of type FirebaseApp.\r\n *\r\n * @internal\r\n */\nfunction _isFirebaseApp(obj) {\n  return obj.options !== undefined;\n}\n/**\r\n *\r\n * @param obj - an object of type FirebaseApp.\r\n *\r\n * @returns true if the provided object is of type FirebaseServerAppImpl.\r\n *\r\n * @internal\r\n */\nfunction _isFirebaseServerApp(obj) {\n  return obj.settings !== undefined;\n}\n/**\r\n * Test only\r\n *\r\n * @internal\r\n */\nfunction _clearComponents() {\n  _components.clear();\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst ERRORS = {\n  [\"no-app\" /* AppError.NO_APP */]: \"No Firebase App '{$appName}' has been created - \" + 'call initializeApp() first',\n  [\"bad-app-name\" /* AppError.BAD_APP_NAME */]: \"Illegal App name: '{$appName}'\",\n  [\"duplicate-app\" /* AppError.DUPLICATE_APP */]: \"Firebase App named '{$appName}' already exists with different options or config\",\n  [\"app-deleted\" /* AppError.APP_DELETED */]: \"Firebase App named '{$appName}' already deleted\",\n  [\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */]: 'Firebase Server App has been deleted',\n  [\"no-options\" /* AppError.NO_OPTIONS */]: 'Need to provide options, when not being deployed to hosting via source.',\n  [\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */]: 'firebase.{$appName}() takes either no argument or a ' + 'Firebase App instance.',\n  [\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */]: 'First argument to `onLog` must be null or a function.',\n  [\"idb-open\" /* AppError.IDB_OPEN */]: 'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-get\" /* AppError.IDB_GET */]: 'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-set\" /* AppError.IDB_WRITE */]: 'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-delete\" /* AppError.IDB_DELETE */]: 'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */]: 'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n  [\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */]: 'FirebaseServerApp is not for use in browser environments.'\n};\nconst ERROR_FACTORY = new ErrorFactory('app', 'Firebase', ERRORS);\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass FirebaseAppImpl {\n  constructor(options, config, container) {\n    this._isDeleted = false;\n    this._options = Object.assign({}, options);\n    this._config = Object.assign({}, config);\n    this._name = config.name;\n    this._automaticDataCollectionEnabled = config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(new Component('app', () => this, \"PUBLIC\" /* ComponentType.PUBLIC */));\n  }\n  get automaticDataCollectionEnabled() {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n  set automaticDataCollectionEnabled(val) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n  get name() {\n    this.checkDestroyed();\n    return this._name;\n  }\n  get options() {\n    this.checkDestroyed();\n    return this._options;\n  }\n  get config() {\n    this.checkDestroyed();\n    return this._config;\n  }\n  get container() {\n    return this._container;\n  }\n  get isDeleted() {\n    return this._isDeleted;\n  }\n  set isDeleted(val) {\n    this._isDeleted = val;\n  }\n  /**\r\n   * This function will throw an Error if the App has already been deleted -\r\n   * use before performing API actions on the App.\r\n   */\n  checkDestroyed() {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(\"app-deleted\" /* AppError.APP_DELETED */, {\n        appName: this._name\n      });\n    }\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2023 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass FirebaseServerAppImpl extends FirebaseAppImpl {\n  constructor(options, serverConfig, name, container) {\n    // Build configuration parameters for the FirebaseAppImpl base class.\n    const automaticDataCollectionEnabled = serverConfig.automaticDataCollectionEnabled !== undefined ? serverConfig.automaticDataCollectionEnabled : false;\n    // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n    const config = {\n      name,\n      automaticDataCollectionEnabled\n    };\n    if (options.apiKey !== undefined) {\n      // Construct the parent FirebaseAppImp object.\n      super(options, config, container);\n    } else {\n      const appImpl = options;\n      super(appImpl.options, config, container);\n    }\n    // Now construct the data for the FirebaseServerAppImpl.\n    this._serverConfig = Object.assign({\n      automaticDataCollectionEnabled\n    }, serverConfig);\n    this._finalizationRegistry = null;\n    if (typeof FinalizationRegistry !== 'undefined') {\n      this._finalizationRegistry = new FinalizationRegistry(() => {\n        this.automaticCleanup();\n      });\n    }\n    this._refCount = 0;\n    this.incRefCount(this._serverConfig.releaseOnDeref);\n    // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n    // will never trigger.\n    this._serverConfig.releaseOnDeref = undefined;\n    serverConfig.releaseOnDeref = undefined;\n    registerVersion(name$q, version$1, 'serverapp');\n  }\n  toJSON() {\n    return undefined;\n  }\n  get refCount() {\n    return this._refCount;\n  }\n  // Increment the reference count of this server app. If an object is provided, register it\n  // with the finalization registry.\n  incRefCount(obj) {\n    if (this.isDeleted) {\n      return;\n    }\n    this._refCount++;\n    if (obj !== undefined && this._finalizationRegistry !== null) {\n      this._finalizationRegistry.register(obj, this);\n    }\n  }\n  // Decrement the reference count.\n  decRefCount() {\n    if (this.isDeleted) {\n      return 0;\n    }\n    return --this._refCount;\n  }\n  // Invoked by the FinalizationRegistry callback to note that this app should go through its\n  // reference counts and delete itself if no reference count remain. The coordinating logic that\n  // handles this is in deleteApp(...).\n  automaticCleanup() {\n    void deleteApp(this);\n  }\n  get settings() {\n    this.checkDestroyed();\n    return this._serverConfig;\n  }\n  /**\r\n   * This function will throw an Error if the App has already been deleted -\r\n   * use before performing API actions on the App.\r\n   */\n  checkDestroyed() {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */);\n    }\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * The current SDK version.\r\n *\r\n * @public\r\n */\nconst SDK_VERSION = version;\nfunction initializeApp(_options, rawConfig = {}) {\n  let options = _options;\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = {\n      name\n    };\n  }\n  const config = Object.assign({\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: false\n  }, rawConfig);\n  const name = config.name;\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(\"bad-app-name\" /* AppError.BAD_APP_NAME */, {\n      appName: String(name)\n    });\n  }\n  options || (options = getDefaultAppConfig());\n  if (!options) {\n    throw ERROR_FACTORY.create(\"no-options\" /* AppError.NO_OPTIONS */);\n  }\n  const existingApp = _apps.get(name);\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (deepEqual(options, existingApp.options) && deepEqual(config, existingApp.config)) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(\"duplicate-app\" /* AppError.DUPLICATE_APP */, {\n        appName: name\n      });\n    }\n  }\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n  const newApp = new FirebaseAppImpl(options, config, container);\n  _apps.set(name, newApp);\n  return newApp;\n}\nfunction initializeServerApp(_options, _serverAppConfig) {\n  if (isBrowser() && !isWebWorker()) {\n    // FirebaseServerApp isn't designed to be run in browsers.\n    throw ERROR_FACTORY.create(\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */);\n  }\n  if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\n    _serverAppConfig.automaticDataCollectionEnabled = false;\n  }\n  let appOptions;\n  if (_isFirebaseApp(_options)) {\n    appOptions = _options.options;\n  } else {\n    appOptions = _options;\n  }\n  // Build an app name based on a hash of the configuration options.\n  const nameObj = Object.assign(Object.assign({}, _serverAppConfig), appOptions);\n  // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n  // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n  if (nameObj.releaseOnDeref !== undefined) {\n    delete nameObj.releaseOnDeref;\n  }\n  const hashCode = s => {\n    return [...s].reduce((hash, c) => Math.imul(31, hash) + c.charCodeAt(0) | 0, 0);\n  };\n  if (_serverAppConfig.releaseOnDeref !== undefined) {\n    if (typeof FinalizationRegistry === 'undefined') {\n      throw ERROR_FACTORY.create(\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */, {});\n    }\n  }\n  const nameString = '' + hashCode(JSON.stringify(nameObj));\n  const existingApp = _serverApps.get(nameString);\n  if (existingApp) {\n    existingApp.incRefCount(_serverAppConfig.releaseOnDeref);\n    return existingApp;\n  }\n  const container = new ComponentContainer(nameString);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n  const newApp = new FirebaseServerAppImpl(appOptions, _serverAppConfig, nameString, container);\n  _serverApps.set(nameString, newApp);\n  return newApp;\n}\n/**\r\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\r\n *\r\n * When called with no arguments, the default app is returned. When an app name\r\n * is provided, the app corresponding to that name is returned.\r\n *\r\n * An exception is thrown if the app being retrieved has not yet been\r\n * initialized.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Return the default app\r\n * const app = getApp();\r\n * ```\r\n *\r\n * @example\r\n * ```javascript\r\n * // Return a named app\r\n * const otherApp = getApp(\"otherApp\");\r\n * ```\r\n *\r\n * @param name - Optional name of the app to return. If no name is\r\n *   provided, the default is `\"[DEFAULT]\"`.\r\n *\r\n * @returns The app corresponding to the provided app name.\r\n *   If no app name is provided, the default app is returned.\r\n *\r\n * @public\r\n */\nfunction getApp(name = DEFAULT_ENTRY_NAME) {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(\"no-app\" /* AppError.NO_APP */, {\n      appName: name\n    });\n  }\n  return app;\n}\n/**\r\n * A (read-only) array of all initialized apps.\r\n * @public\r\n */\nfunction getApps() {\n  return Array.from(_apps.values());\n}\n/**\r\n * Renders this app unusable and frees the resources of all associated\r\n * services.\r\n *\r\n * @example\r\n * ```javascript\r\n * deleteApp(app)\r\n *   .then(function() {\r\n *     console.log(\"App deleted successfully\");\r\n *   })\r\n *   .catch(function(error) {\r\n *     console.log(\"Error deleting app:\", error);\r\n *   });\r\n * ```\r\n *\r\n * @public\r\n */\nfunction deleteApp(_x) {\n  return _deleteApp.apply(this, arguments);\n}\n/**\r\n * Registers a library's name and version for platform logging purposes.\r\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\r\n * @param version - Current version of that library.\r\n * @param variant - Bundle variant, e.g., node, rn, etc.\r\n *\r\n * @public\r\n */\nfunction _deleteApp() {\n  _deleteApp = _asyncToGenerator(function* (app) {\n    let cleanupProviders = false;\n    const name = app.name;\n    if (_apps.has(name)) {\n      cleanupProviders = true;\n      _apps.delete(name);\n    } else if (_serverApps.has(name)) {\n      const firebaseServerApp = app;\n      if (firebaseServerApp.decRefCount() <= 0) {\n        _serverApps.delete(name);\n        cleanupProviders = true;\n      }\n    }\n    if (cleanupProviders) {\n      yield Promise.all(app.container.getProviders().map(provider => provider.delete()));\n      app.isDeleted = true;\n    }\n  });\n  return _deleteApp.apply(this, arguments);\n}\nfunction registerVersion(libraryKeyOrName, version, variant) {\n  var _a;\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = (_a = PLATFORM_LOG_STRING[libraryKeyOrName]) !== null && _a !== void 0 ? _a : libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [`Unable to register library \"${library}\" with version \"${version}\":`];\n    if (libraryMismatch) {\n      warning.push(`library name \"${library}\" contains illegal characters (whitespace or \"/\")`);\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(`version name \"${version}\" contains illegal characters (whitespace or \"/\")`);\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(new Component(`${library}-version`, () => ({\n    library,\n    version\n  }), \"VERSION\" /* ComponentType.VERSION */));\n}\n/**\r\n * Sets log handler for all Firebase SDKs.\r\n * @param logCallback - An optional custom log handler that executes user code whenever\r\n * the Firebase SDK makes a logging call.\r\n *\r\n * @public\r\n */\nfunction onLog(logCallback, options) {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */);\n  }\n  setUserLogHandler(logCallback, options);\n}\n/**\r\n * Sets log level for all Firebase SDKs.\r\n *\r\n * All of the log types above the current log level are captured (i.e. if\r\n * you set the log level to `info`, errors are logged, but `debug` and\r\n * `verbose` logs are not).\r\n *\r\n * @public\r\n */\nfunction setLogLevel(logLevel) {\n  setLogLevel$1(logLevel);\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n  if (!dbPromise) {\n    dbPromise = openDB(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            try {\n              db.createObjectStore(STORE_NAME);\n            } catch (e) {\n              // Safari/iOS browsers throw occasional exceptions on\n              // db.createObjectStore() that may be a bug. Avoid blocking\n              // the rest of the app functionality.\n              console.warn(e);\n            }\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(\"idb-open\" /* AppError.IDB_OPEN */, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\nfunction readHeartbeatsFromIndexedDB(_x2) {\n  return _readHeartbeatsFromIndexedDB.apply(this, arguments);\n}\nfunction _readHeartbeatsFromIndexedDB() {\n  _readHeartbeatsFromIndexedDB = _asyncToGenerator(function* (app) {\n    try {\n      const db = yield getDbPromise();\n      const tx = db.transaction(STORE_NAME);\n      const result = yield tx.objectStore(STORE_NAME).get(computeKey(app));\n      // We already have the value but tx.done can throw,\n      // so we need to await it here to catch errors\n      yield tx.done;\n      return result;\n    } catch (e) {\n      if (e instanceof FirebaseError) {\n        logger.warn(e.message);\n      } else {\n        const idbGetError = ERROR_FACTORY.create(\"idb-get\" /* AppError.IDB_GET */, {\n          originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n        });\n        logger.warn(idbGetError.message);\n      }\n    }\n  });\n  return _readHeartbeatsFromIndexedDB.apply(this, arguments);\n}\nfunction writeHeartbeatsToIndexedDB(_x3, _x4) {\n  return _writeHeartbeatsToIndexedDB.apply(this, arguments);\n}\nfunction _writeHeartbeatsToIndexedDB() {\n  _writeHeartbeatsToIndexedDB = _asyncToGenerator(function* (app, heartbeatObject) {\n    try {\n      const db = yield getDbPromise();\n      const tx = db.transaction(STORE_NAME, 'readwrite');\n      const objectStore = tx.objectStore(STORE_NAME);\n      yield objectStore.put(heartbeatObject, computeKey(app));\n      yield tx.done;\n    } catch (e) {\n      if (e instanceof FirebaseError) {\n        logger.warn(e.message);\n      } else {\n        const idbGetError = ERROR_FACTORY.create(\"idb-set\" /* AppError.IDB_WRITE */, {\n          originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n        });\n        logger.warn(idbGetError.message);\n      }\n    }\n  });\n  return _writeHeartbeatsToIndexedDB.apply(this, arguments);\n}\nfunction computeKey(app) {\n  return `${app.name}!${app.options.appId}`;\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst MAX_HEADER_BYTES = 1024;\n// 30 days\nconst STORED_HEARTBEAT_RETENTION_MAX_MILLIS = 30 * 24 * 60 * 60 * 1000;\nclass HeartbeatServiceImpl {\n  constructor(container) {\n    this.container = container;\n    /**\r\n     * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\r\n     * the header string.\r\n     * Stores one record per date. This will be consolidated into the standard\r\n     * format of one record per user agent string before being sent as a header.\r\n     * Populated from indexedDB when the controller is instantiated and should\r\n     * be kept in sync with indexedDB.\r\n     * Leave public for easier testing.\r\n     */\n    this._heartbeatsCache = null;\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n  /**\r\n   * Called to report a heartbeat. The function will generate\r\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\r\n   * to IndexedDB.\r\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\r\n   * already logged, subsequent calls to this function in the same day will be ignored.\r\n   */\n  triggerHeartbeat() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b;\n      try {\n        const platformLogger = _this.container.getProvider('platform-logger').getImmediate();\n        // This is the \"Firebase user agent\" string from the platform logger\n        // service, not the browser user agent.\n        const agent = platformLogger.getPlatformInfoString();\n        const date = getUTCDateString();\n        if (((_a = _this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null) {\n          _this._heartbeatsCache = yield _this._heartbeatsCachePromise;\n          // If we failed to construct a heartbeats cache, then return immediately.\n          if (((_b = _this._heartbeatsCache) === null || _b === void 0 ? void 0 : _b.heartbeats) == null) {\n            return;\n          }\n        }\n        // Do not store a heartbeat if one is already stored for this day\n        // or if a header has already been sent today.\n        if (_this._heartbeatsCache.lastSentHeartbeatDate === date || _this._heartbeatsCache.heartbeats.some(singleDateHeartbeat => singleDateHeartbeat.date === date)) {\n          return;\n        } else {\n          // There is no entry for this date. Create one.\n          _this._heartbeatsCache.heartbeats.push({\n            date,\n            agent\n          });\n        }\n        // Remove entries older than 30 days.\n        _this._heartbeatsCache.heartbeats = _this._heartbeatsCache.heartbeats.filter(singleDateHeartbeat => {\n          const hbTimestamp = new Date(singleDateHeartbeat.date).valueOf();\n          const now = Date.now();\n          return now - hbTimestamp <= STORED_HEARTBEAT_RETENTION_MAX_MILLIS;\n        });\n        return _this._storage.overwrite(_this._heartbeatsCache);\n      } catch (e) {\n        logger.warn(e);\n      }\n    })();\n  }\n  /**\r\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\r\n   * It also clears all heartbeats from memory as well as in IndexedDB.\r\n   *\r\n   * NOTE: Consuming product SDKs should not send the header if this method\r\n   * returns an empty string.\r\n   */\n  getHeartbeatsHeader() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      try {\n        if (_this2._heartbeatsCache === null) {\n          yield _this2._heartbeatsCachePromise;\n        }\n        // If it's still null or the array is empty, there is no data to send.\n        if (((_a = _this2._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null || _this2._heartbeatsCache.heartbeats.length === 0) {\n          return '';\n        }\n        const date = getUTCDateString();\n        // Extract as many heartbeats from the cache as will fit under the size limit.\n        const {\n          heartbeatsToSend,\n          unsentEntries\n        } = extractHeartbeatsForHeader(_this2._heartbeatsCache.heartbeats);\n        const headerString = base64urlEncodeWithoutPadding(JSON.stringify({\n          version: 2,\n          heartbeats: heartbeatsToSend\n        }));\n        // Store last sent date to prevent another being logged/sent for the same day.\n        _this2._heartbeatsCache.lastSentHeartbeatDate = date;\n        if (unsentEntries.length > 0) {\n          // Store any unsent entries if they exist.\n          _this2._heartbeatsCache.heartbeats = unsentEntries;\n          // This seems more likely than emptying the array (below) to lead to some odd state\n          // since the cache isn't empty and this will be called again on the next request,\n          // and is probably safest if we await it.\n          yield _this2._storage.overwrite(_this2._heartbeatsCache);\n        } else {\n          _this2._heartbeatsCache.heartbeats = [];\n          // Do not wait for this, to reduce latency.\n          void _this2._storage.overwrite(_this2._heartbeatsCache);\n        }\n        return headerString;\n      } catch (e) {\n        logger.warn(e);\n        return '';\n      }\n    })();\n  }\n}\nfunction getUTCDateString() {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\nfunction extractHeartbeatsForHeader(heartbeatsCache, maxSize = MAX_HEADER_BYTES) {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(hb => hb.agent === singleDateHeartbeat.agent);\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\nclass HeartbeatStorageImpl {\n  constructor(app) {\n    this.app = app;\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  runIndexedDBEnvironmentCheck() {\n    return _asyncToGenerator(function* () {\n      if (!isIndexedDBAvailable()) {\n        return false;\n      } else {\n        return validateIndexedDBOpenable().then(() => true).catch(() => false);\n      }\n    })();\n  }\n  /**\r\n   * Read all heartbeats.\r\n   */\n  read() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const canUseIndexedDB = yield _this3._canUseIndexedDBPromise;\n      if (!canUseIndexedDB) {\n        return {\n          heartbeats: []\n        };\n      } else {\n        const idbHeartbeatObject = yield readHeartbeatsFromIndexedDB(_this3.app);\n        if (idbHeartbeatObject === null || idbHeartbeatObject === void 0 ? void 0 : idbHeartbeatObject.heartbeats) {\n          return idbHeartbeatObject;\n        } else {\n          return {\n            heartbeats: []\n          };\n        }\n      }\n    })();\n  }\n  // overwrite the storage with the provided heartbeats\n  overwrite(heartbeatsObject) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      const canUseIndexedDB = yield _this4._canUseIndexedDBPromise;\n      if (!canUseIndexedDB) {\n        return;\n      } else {\n        const existingHeartbeatsObject = yield _this4.read();\n        return writeHeartbeatsToIndexedDB(_this4.app, {\n          lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n          heartbeats: heartbeatsObject.heartbeats\n        });\n      }\n    })();\n  }\n  // add heartbeats\n  add(heartbeatsObject) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      const canUseIndexedDB = yield _this5._canUseIndexedDBPromise;\n      if (!canUseIndexedDB) {\n        return;\n      } else {\n        const existingHeartbeatsObject = yield _this5.read();\n        return writeHeartbeatsToIndexedDB(_this5.app, {\n          lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n          heartbeats: [...existingHeartbeatsObject.heartbeats, ...heartbeatsObject.heartbeats]\n        });\n      }\n    })();\n  }\n}\n/**\r\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\r\n * in a platform logging header JSON object, stringified, and converted\r\n * to base 64.\r\n */\nfunction countBytes(heartbeatsCache) {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n  // heartbeatsCache wrapper properties\n  JSON.stringify({\n    version: 2,\n    heartbeats: heartbeatsCache\n  })).length;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction registerCoreComponents(variant) {\n  _registerComponent(new Component('platform-logger', container => new PlatformLoggerServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n  _registerComponent(new Component('heartbeat', container => new HeartbeatServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n  // Register `app` package.\n  registerVersion(name$q, version$1, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name$q, version$1, 'esm2017');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n\n/**\r\n * Firebase App\r\n *\r\n * @remarks This package coordinates the communication between the different Firebase components\r\n * @packageDocumentation\r\n */\nregisterCoreComponents('');\nexport { SDK_VERSION, DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME, _addComponent, _addOrOverwriteComponent, _apps, _clearComponents, _components, _getProvider, _isFirebaseApp, _isFirebaseServerApp, _registerComponent, _removeServiceInstance, _serverApps, deleteApp, getApp, getApps, initializeApp, initializeServerApp, onLog, registerVersion, setLogLevel };", "map": {"version": 3, "names": ["Component", "ComponentContainer", "<PERSON><PERSON>", "setUserLogHandler", "setLogLevel", "setLogLevel$1", "ErrorFactory", "getDefaultAppConfig", "deepEqual", "<PERSON><PERSON><PERSON><PERSON>", "isWebWorker", "FirebaseError", "base64urlEncodeWithoutPadding", "isIndexedDBAvailable", "validateIndexedDBOpenable", "openDB", "PlatformLoggerServiceImpl", "constructor", "container", "getPlatformInfoString", "providers", "getProviders", "map", "provider", "isVersionServiceProvider", "service", "getImmediate", "library", "version", "filter", "logString", "join", "component", "getComponent", "type", "name$q", "version$1", "logger", "name$p", "name$o", "name$n", "name$m", "name$l", "name$k", "name$j", "name$i", "name$h", "name$g", "name$f", "name$e", "name$d", "name$c", "name$b", "name$a", "name$9", "name$8", "name$7", "name$6", "name$5", "name$4", "name$3", "name$2", "name$1", "name", "DEFAULT_ENTRY_NAME", "PLATFORM_LOG_STRING", "_apps", "Map", "_serverApps", "_components", "_addComponent", "app", "addComponent", "e", "debug", "_addOrOverwriteComponent", "addOrOverwriteComponent", "_registerComponent", "componentName", "has", "set", "values", "serverApp", "_get<PERSON><PERSON><PERSON>", "heartbeatController", "get<PERSON><PERSON><PERSON>", "optional", "triggerHeartbeat", "_removeServiceInstance", "instanceIdentifier", "clearInstance", "_isFirebaseApp", "obj", "options", "undefined", "_isFirebaseServerApp", "settings", "_clearComponents", "clear", "ERRORS", "ERROR_FACTORY", "FirebaseAppImpl", "config", "_isDeleted", "_options", "Object", "assign", "_config", "_name", "_automaticDataCollectionEnabled", "automaticDataCollectionEnabled", "_container", "checkDestroyed", "val", "isDeleted", "create", "appName", "FirebaseServerAppImpl", "serverConfig", "<PERSON><PERSON><PERSON><PERSON>", "appImpl", "_serverConfig", "_finalizationRegistry", "FinalizationRegistry", "automaticCleanup", "_refCount", "incRefCount", "releaseOnDeref", "registerVersion", "toJSON", "refCount", "register", "decRefCount", "deleteApp", "SDK_VERSION", "initializeApp", "rawConfig", "String", "existingApp", "get", "newApp", "initializeServerApp", "_serverAppConfig", "appOptions", "nameObj", "hashCode", "s", "reduce", "hash", "c", "Math", "imul", "charCodeAt", "nameString", "JSON", "stringify", "getApp", "getApps", "Array", "from", "_x", "_deleteApp", "apply", "arguments", "_asyncToGenerator", "cleanupProviders", "delete", "firebaseServerApp", "Promise", "all", "libraryKeyOrName", "variant", "_a", "libraryMismatch", "match", "versionMismatch", "warning", "push", "warn", "onLog", "logCallback", "logLevel", "DB_NAME", "DB_VERSION", "STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "upgrade", "db", "oldVersion", "createObjectStore", "console", "catch", "originalErrorMessage", "message", "readHeartbeatsFromIndexedDB", "_x2", "_readHeartbeatsFromIndexedDB", "tx", "transaction", "result", "objectStore", "computeKey", "done", "idbGetError", "writeHeartbeatsToIndexedDB", "_x3", "_x4", "_writeHeartbeatsToIndexedDB", "heartbeatObject", "put", "appId", "MAX_HEADER_BYTES", "STORED_HEARTBEAT_RETENTION_MAX_MILLIS", "HeartbeatServiceImpl", "_heartbeatsCache", "_storage", "HeartbeatStorageImpl", "_heartbeatsCachePromise", "read", "then", "_this", "_b", "platformLogger", "agent", "date", "getUTCDateString", "heartbeats", "lastSentHeartbeatDate", "some", "singleDateHeartbeat", "hbTimestamp", "Date", "valueOf", "now", "overwrite", "getHeartbeatsHeader", "_this2", "length", "heartbeatsToSend", "unsentEntries", "extractHeartbeatsForHeader", "headerString", "today", "toISOString", "substring", "heartbeatsCache", "maxSize", "slice", "heartbeatEntry", "find", "hb", "dates", "countBytes", "pop", "_canUseIndexedDBPromise", "runIndexedDBEnvironmentCheck", "_this3", "canUseIndexedDB", "idbHeartbeatObject", "heartbeatsObject", "_this4", "existingHeartbeatsObject", "add", "_this5", "registerCoreComponents", "_DEFAULT_ENTRY_NAME"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/@firebase/app/dist/esm/index.esm2017.js"], "sourcesContent": ["import { Component, ComponentContainer } from '@firebase/component';\nimport { <PERSON><PERSON>, setUser<PERSON>og<PERSON><PERSON><PERSON>, setLogLevel as setLogLevel$1 } from '@firebase/logger';\nimport { ErrorFactory, getDefaultAppConfig, deepEqual, isBrowser, isWebWorker, FirebaseError, base64urlEncodeWithoutPadding, isIndexedDBAvailable, validateIndexedDBOpenable } from '@firebase/util';\nexport { FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass PlatformLoggerServiceImpl {\r\n    constructor(container) {\r\n        this.container = container;\r\n    }\r\n    // In initial implementation, this will be called by installations on\r\n    // auth token refresh, and installations will send this string.\r\n    getPlatformInfoString() {\r\n        const providers = this.container.getProviders();\r\n        // Loop through providers and get library/version pairs from any that are\r\n        // version components.\r\n        return providers\r\n            .map(provider => {\r\n            if (isVersionServiceProvider(provider)) {\r\n                const service = provider.getImmediate();\r\n                return `${service.library}/${service.version}`;\r\n            }\r\n            else {\r\n                return null;\r\n            }\r\n        })\r\n            .filter(logString => logString)\r\n            .join(' ');\r\n    }\r\n}\r\n/**\r\n *\r\n * @param provider check if this provider provides a VersionService\r\n *\r\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\r\n * provides VersionService. The provider is not necessarily a 'app-version'\r\n * provider.\r\n */\r\nfunction isVersionServiceProvider(provider) {\r\n    const component = provider.getComponent();\r\n    return (component === null || component === void 0 ? void 0 : component.type) === \"VERSION\" /* ComponentType.VERSION */;\r\n}\n\nconst name$q = \"@firebase/app\";\nconst version$1 = \"0.10.13\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst logger = new Logger('@firebase/app');\n\nconst name$p = \"@firebase/app-compat\";\n\nconst name$o = \"@firebase/analytics-compat\";\n\nconst name$n = \"@firebase/analytics\";\n\nconst name$m = \"@firebase/app-check-compat\";\n\nconst name$l = \"@firebase/app-check\";\n\nconst name$k = \"@firebase/auth\";\n\nconst name$j = \"@firebase/auth-compat\";\n\nconst name$i = \"@firebase/database\";\n\nconst name$h = \"@firebase/data-connect\";\n\nconst name$g = \"@firebase/database-compat\";\n\nconst name$f = \"@firebase/functions\";\n\nconst name$e = \"@firebase/functions-compat\";\n\nconst name$d = \"@firebase/installations\";\n\nconst name$c = \"@firebase/installations-compat\";\n\nconst name$b = \"@firebase/messaging\";\n\nconst name$a = \"@firebase/messaging-compat\";\n\nconst name$9 = \"@firebase/performance\";\n\nconst name$8 = \"@firebase/performance-compat\";\n\nconst name$7 = \"@firebase/remote-config\";\n\nconst name$6 = \"@firebase/remote-config-compat\";\n\nconst name$5 = \"@firebase/storage\";\n\nconst name$4 = \"@firebase/storage-compat\";\n\nconst name$3 = \"@firebase/firestore\";\n\nconst name$2 = \"@firebase/vertexai-preview\";\n\nconst name$1 = \"@firebase/firestore-compat\";\n\nconst name = \"firebase\";\nconst version = \"10.14.1\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * The default app name\r\n *\r\n * @internal\r\n */\r\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\r\nconst PLATFORM_LOG_STRING = {\r\n    [name$q]: 'fire-core',\r\n    [name$p]: 'fire-core-compat',\r\n    [name$n]: 'fire-analytics',\r\n    [name$o]: 'fire-analytics-compat',\r\n    [name$l]: 'fire-app-check',\r\n    [name$m]: 'fire-app-check-compat',\r\n    [name$k]: 'fire-auth',\r\n    [name$j]: 'fire-auth-compat',\r\n    [name$i]: 'fire-rtdb',\r\n    [name$h]: 'fire-data-connect',\r\n    [name$g]: 'fire-rtdb-compat',\r\n    [name$f]: 'fire-fn',\r\n    [name$e]: 'fire-fn-compat',\r\n    [name$d]: 'fire-iid',\r\n    [name$c]: 'fire-iid-compat',\r\n    [name$b]: 'fire-fcm',\r\n    [name$a]: 'fire-fcm-compat',\r\n    [name$9]: 'fire-perf',\r\n    [name$8]: 'fire-perf-compat',\r\n    [name$7]: 'fire-rc',\r\n    [name$6]: 'fire-rc-compat',\r\n    [name$5]: 'fire-gcs',\r\n    [name$4]: 'fire-gcs-compat',\r\n    [name$3]: 'fire-fst',\r\n    [name$1]: 'fire-fst-compat',\r\n    [name$2]: 'fire-vertex',\r\n    'fire-js': 'fire-js',\r\n    [name]: 'fire-js-all'\r\n};\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * @internal\r\n */\r\nconst _apps = new Map();\r\n/**\r\n * @internal\r\n */\r\nconst _serverApps = new Map();\r\n/**\r\n * Registered components.\r\n *\r\n * @internal\r\n */\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nconst _components = new Map();\r\n/**\r\n * @param component - the component being added to this app's container\r\n *\r\n * @internal\r\n */\r\nfunction _addComponent(app, component) {\r\n    try {\r\n        app.container.addComponent(component);\r\n    }\r\n    catch (e) {\r\n        logger.debug(`Component ${component.name} failed to register with FirebaseApp ${app.name}`, e);\r\n    }\r\n}\r\n/**\r\n *\r\n * @internal\r\n */\r\nfunction _addOrOverwriteComponent(app, component) {\r\n    app.container.addOrOverwriteComponent(component);\r\n}\r\n/**\r\n *\r\n * @param component - the component to register\r\n * @returns whether or not the component is registered successfully\r\n *\r\n * @internal\r\n */\r\nfunction _registerComponent(component) {\r\n    const componentName = component.name;\r\n    if (_components.has(componentName)) {\r\n        logger.debug(`There were multiple attempts to register component ${componentName}.`);\r\n        return false;\r\n    }\r\n    _components.set(componentName, component);\r\n    // add the component to existing app instances\r\n    for (const app of _apps.values()) {\r\n        _addComponent(app, component);\r\n    }\r\n    for (const serverApp of _serverApps.values()) {\r\n        _addComponent(serverApp, component);\r\n    }\r\n    return true;\r\n}\r\n/**\r\n *\r\n * @param app - FirebaseApp instance\r\n * @param name - service name\r\n *\r\n * @returns the provider for the service with the matching name\r\n *\r\n * @internal\r\n */\r\nfunction _getProvider(app, name) {\r\n    const heartbeatController = app.container\r\n        .getProvider('heartbeat')\r\n        .getImmediate({ optional: true });\r\n    if (heartbeatController) {\r\n        void heartbeatController.triggerHeartbeat();\r\n    }\r\n    return app.container.getProvider(name);\r\n}\r\n/**\r\n *\r\n * @param app - FirebaseApp instance\r\n * @param name - service name\r\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\r\n *\r\n * @internal\r\n */\r\nfunction _removeServiceInstance(app, name, instanceIdentifier = DEFAULT_ENTRY_NAME) {\r\n    _getProvider(app, name).clearInstance(instanceIdentifier);\r\n}\r\n/**\r\n *\r\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\r\n *\r\n * @returns true if the provide object is of type FirebaseApp.\r\n *\r\n * @internal\r\n */\r\nfunction _isFirebaseApp(obj) {\r\n    return obj.options !== undefined;\r\n}\r\n/**\r\n *\r\n * @param obj - an object of type FirebaseApp.\r\n *\r\n * @returns true if the provided object is of type FirebaseServerAppImpl.\r\n *\r\n * @internal\r\n */\r\nfunction _isFirebaseServerApp(obj) {\r\n    return obj.settings !== undefined;\r\n}\r\n/**\r\n * Test only\r\n *\r\n * @internal\r\n */\r\nfunction _clearComponents() {\r\n    _components.clear();\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst ERRORS = {\r\n    [\"no-app\" /* AppError.NO_APP */]: \"No Firebase App '{$appName}' has been created - \" +\r\n        'call initializeApp() first',\r\n    [\"bad-app-name\" /* AppError.BAD_APP_NAME */]: \"Illegal App name: '{$appName}'\",\r\n    [\"duplicate-app\" /* AppError.DUPLICATE_APP */]: \"Firebase App named '{$appName}' already exists with different options or config\",\r\n    [\"app-deleted\" /* AppError.APP_DELETED */]: \"Firebase App named '{$appName}' already deleted\",\r\n    [\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */]: 'Firebase Server App has been deleted',\r\n    [\"no-options\" /* AppError.NO_OPTIONS */]: 'Need to provide options, when not being deployed to hosting via source.',\r\n    [\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */]: 'firebase.{$appName}() takes either no argument or a ' +\r\n        'Firebase App instance.',\r\n    [\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */]: 'First argument to `onLog` must be null or a function.',\r\n    [\"idb-open\" /* AppError.IDB_OPEN */]: 'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\r\n    [\"idb-get\" /* AppError.IDB_GET */]: 'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\r\n    [\"idb-set\" /* AppError.IDB_WRITE */]: 'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\r\n    [\"idb-delete\" /* AppError.IDB_DELETE */]: 'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\r\n    [\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */]: 'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\r\n    [\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */]: 'FirebaseServerApp is not for use in browser environments.'\r\n};\r\nconst ERROR_FACTORY = new ErrorFactory('app', 'Firebase', ERRORS);\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass FirebaseAppImpl {\r\n    constructor(options, config, container) {\r\n        this._isDeleted = false;\r\n        this._options = Object.assign({}, options);\r\n        this._config = Object.assign({}, config);\r\n        this._name = config.name;\r\n        this._automaticDataCollectionEnabled =\r\n            config.automaticDataCollectionEnabled;\r\n        this._container = container;\r\n        this.container.addComponent(new Component('app', () => this, \"PUBLIC\" /* ComponentType.PUBLIC */));\r\n    }\r\n    get automaticDataCollectionEnabled() {\r\n        this.checkDestroyed();\r\n        return this._automaticDataCollectionEnabled;\r\n    }\r\n    set automaticDataCollectionEnabled(val) {\r\n        this.checkDestroyed();\r\n        this._automaticDataCollectionEnabled = val;\r\n    }\r\n    get name() {\r\n        this.checkDestroyed();\r\n        return this._name;\r\n    }\r\n    get options() {\r\n        this.checkDestroyed();\r\n        return this._options;\r\n    }\r\n    get config() {\r\n        this.checkDestroyed();\r\n        return this._config;\r\n    }\r\n    get container() {\r\n        return this._container;\r\n    }\r\n    get isDeleted() {\r\n        return this._isDeleted;\r\n    }\r\n    set isDeleted(val) {\r\n        this._isDeleted = val;\r\n    }\r\n    /**\r\n     * This function will throw an Error if the App has already been deleted -\r\n     * use before performing API actions on the App.\r\n     */\r\n    checkDestroyed() {\r\n        if (this.isDeleted) {\r\n            throw ERROR_FACTORY.create(\"app-deleted\" /* AppError.APP_DELETED */, { appName: this._name });\r\n        }\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2023 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass FirebaseServerAppImpl extends FirebaseAppImpl {\r\n    constructor(options, serverConfig, name, container) {\r\n        // Build configuration parameters for the FirebaseAppImpl base class.\r\n        const automaticDataCollectionEnabled = serverConfig.automaticDataCollectionEnabled !== undefined\r\n            ? serverConfig.automaticDataCollectionEnabled\r\n            : false;\r\n        // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\r\n        const config = {\r\n            name,\r\n            automaticDataCollectionEnabled\r\n        };\r\n        if (options.apiKey !== undefined) {\r\n            // Construct the parent FirebaseAppImp object.\r\n            super(options, config, container);\r\n        }\r\n        else {\r\n            const appImpl = options;\r\n            super(appImpl.options, config, container);\r\n        }\r\n        // Now construct the data for the FirebaseServerAppImpl.\r\n        this._serverConfig = Object.assign({ automaticDataCollectionEnabled }, serverConfig);\r\n        this._finalizationRegistry = null;\r\n        if (typeof FinalizationRegistry !== 'undefined') {\r\n            this._finalizationRegistry = new FinalizationRegistry(() => {\r\n                this.automaticCleanup();\r\n            });\r\n        }\r\n        this._refCount = 0;\r\n        this.incRefCount(this._serverConfig.releaseOnDeref);\r\n        // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\r\n        // will never trigger.\r\n        this._serverConfig.releaseOnDeref = undefined;\r\n        serverConfig.releaseOnDeref = undefined;\r\n        registerVersion(name$q, version$1, 'serverapp');\r\n    }\r\n    toJSON() {\r\n        return undefined;\r\n    }\r\n    get refCount() {\r\n        return this._refCount;\r\n    }\r\n    // Increment the reference count of this server app. If an object is provided, register it\r\n    // with the finalization registry.\r\n    incRefCount(obj) {\r\n        if (this.isDeleted) {\r\n            return;\r\n        }\r\n        this._refCount++;\r\n        if (obj !== undefined && this._finalizationRegistry !== null) {\r\n            this._finalizationRegistry.register(obj, this);\r\n        }\r\n    }\r\n    // Decrement the reference count.\r\n    decRefCount() {\r\n        if (this.isDeleted) {\r\n            return 0;\r\n        }\r\n        return --this._refCount;\r\n    }\r\n    // Invoked by the FinalizationRegistry callback to note that this app should go through its\r\n    // reference counts and delete itself if no reference count remain. The coordinating logic that\r\n    // handles this is in deleteApp(...).\r\n    automaticCleanup() {\r\n        void deleteApp(this);\r\n    }\r\n    get settings() {\r\n        this.checkDestroyed();\r\n        return this._serverConfig;\r\n    }\r\n    /**\r\n     * This function will throw an Error if the App has already been deleted -\r\n     * use before performing API actions on the App.\r\n     */\r\n    checkDestroyed() {\r\n        if (this.isDeleted) {\r\n            throw ERROR_FACTORY.create(\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */);\r\n        }\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * The current SDK version.\r\n *\r\n * @public\r\n */\r\nconst SDK_VERSION = version;\r\nfunction initializeApp(_options, rawConfig = {}) {\r\n    let options = _options;\r\n    if (typeof rawConfig !== 'object') {\r\n        const name = rawConfig;\r\n        rawConfig = { name };\r\n    }\r\n    const config = Object.assign({ name: DEFAULT_ENTRY_NAME, automaticDataCollectionEnabled: false }, rawConfig);\r\n    const name = config.name;\r\n    if (typeof name !== 'string' || !name) {\r\n        throw ERROR_FACTORY.create(\"bad-app-name\" /* AppError.BAD_APP_NAME */, {\r\n            appName: String(name)\r\n        });\r\n    }\r\n    options || (options = getDefaultAppConfig());\r\n    if (!options) {\r\n        throw ERROR_FACTORY.create(\"no-options\" /* AppError.NO_OPTIONS */);\r\n    }\r\n    const existingApp = _apps.get(name);\r\n    if (existingApp) {\r\n        // return the existing app if options and config deep equal the ones in the existing app.\r\n        if (deepEqual(options, existingApp.options) &&\r\n            deepEqual(config, existingApp.config)) {\r\n            return existingApp;\r\n        }\r\n        else {\r\n            throw ERROR_FACTORY.create(\"duplicate-app\" /* AppError.DUPLICATE_APP */, { appName: name });\r\n        }\r\n    }\r\n    const container = new ComponentContainer(name);\r\n    for (const component of _components.values()) {\r\n        container.addComponent(component);\r\n    }\r\n    const newApp = new FirebaseAppImpl(options, config, container);\r\n    _apps.set(name, newApp);\r\n    return newApp;\r\n}\r\nfunction initializeServerApp(_options, _serverAppConfig) {\r\n    if (isBrowser() && !isWebWorker()) {\r\n        // FirebaseServerApp isn't designed to be run in browsers.\r\n        throw ERROR_FACTORY.create(\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */);\r\n    }\r\n    if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\r\n        _serverAppConfig.automaticDataCollectionEnabled = false;\r\n    }\r\n    let appOptions;\r\n    if (_isFirebaseApp(_options)) {\r\n        appOptions = _options.options;\r\n    }\r\n    else {\r\n        appOptions = _options;\r\n    }\r\n    // Build an app name based on a hash of the configuration options.\r\n    const nameObj = Object.assign(Object.assign({}, _serverAppConfig), appOptions);\r\n    // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\r\n    // construction of FirebaseServerApp instances. For example, if the object is the request headers.\r\n    if (nameObj.releaseOnDeref !== undefined) {\r\n        delete nameObj.releaseOnDeref;\r\n    }\r\n    const hashCode = (s) => {\r\n        return [...s].reduce((hash, c) => (Math.imul(31, hash) + c.charCodeAt(0)) | 0, 0);\r\n    };\r\n    if (_serverAppConfig.releaseOnDeref !== undefined) {\r\n        if (typeof FinalizationRegistry === 'undefined') {\r\n            throw ERROR_FACTORY.create(\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */, {});\r\n        }\r\n    }\r\n    const nameString = '' + hashCode(JSON.stringify(nameObj));\r\n    const existingApp = _serverApps.get(nameString);\r\n    if (existingApp) {\r\n        existingApp.incRefCount(_serverAppConfig.releaseOnDeref);\r\n        return existingApp;\r\n    }\r\n    const container = new ComponentContainer(nameString);\r\n    for (const component of _components.values()) {\r\n        container.addComponent(component);\r\n    }\r\n    const newApp = new FirebaseServerAppImpl(appOptions, _serverAppConfig, nameString, container);\r\n    _serverApps.set(nameString, newApp);\r\n    return newApp;\r\n}\r\n/**\r\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\r\n *\r\n * When called with no arguments, the default app is returned. When an app name\r\n * is provided, the app corresponding to that name is returned.\r\n *\r\n * An exception is thrown if the app being retrieved has not yet been\r\n * initialized.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Return the default app\r\n * const app = getApp();\r\n * ```\r\n *\r\n * @example\r\n * ```javascript\r\n * // Return a named app\r\n * const otherApp = getApp(\"otherApp\");\r\n * ```\r\n *\r\n * @param name - Optional name of the app to return. If no name is\r\n *   provided, the default is `\"[DEFAULT]\"`.\r\n *\r\n * @returns The app corresponding to the provided app name.\r\n *   If no app name is provided, the default app is returned.\r\n *\r\n * @public\r\n */\r\nfunction getApp(name = DEFAULT_ENTRY_NAME) {\r\n    const app = _apps.get(name);\r\n    if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\r\n        return initializeApp();\r\n    }\r\n    if (!app) {\r\n        throw ERROR_FACTORY.create(\"no-app\" /* AppError.NO_APP */, { appName: name });\r\n    }\r\n    return app;\r\n}\r\n/**\r\n * A (read-only) array of all initialized apps.\r\n * @public\r\n */\r\nfunction getApps() {\r\n    return Array.from(_apps.values());\r\n}\r\n/**\r\n * Renders this app unusable and frees the resources of all associated\r\n * services.\r\n *\r\n * @example\r\n * ```javascript\r\n * deleteApp(app)\r\n *   .then(function() {\r\n *     console.log(\"App deleted successfully\");\r\n *   })\r\n *   .catch(function(error) {\r\n *     console.log(\"Error deleting app:\", error);\r\n *   });\r\n * ```\r\n *\r\n * @public\r\n */\r\nasync function deleteApp(app) {\r\n    let cleanupProviders = false;\r\n    const name = app.name;\r\n    if (_apps.has(name)) {\r\n        cleanupProviders = true;\r\n        _apps.delete(name);\r\n    }\r\n    else if (_serverApps.has(name)) {\r\n        const firebaseServerApp = app;\r\n        if (firebaseServerApp.decRefCount() <= 0) {\r\n            _serverApps.delete(name);\r\n            cleanupProviders = true;\r\n        }\r\n    }\r\n    if (cleanupProviders) {\r\n        await Promise.all(app.container\r\n            .getProviders()\r\n            .map(provider => provider.delete()));\r\n        app.isDeleted = true;\r\n    }\r\n}\r\n/**\r\n * Registers a library's name and version for platform logging purposes.\r\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\r\n * @param version - Current version of that library.\r\n * @param variant - Bundle variant, e.g., node, rn, etc.\r\n *\r\n * @public\r\n */\r\nfunction registerVersion(libraryKeyOrName, version, variant) {\r\n    var _a;\r\n    // TODO: We can use this check to whitelist strings when/if we set up\r\n    // a good whitelist system.\r\n    let library = (_a = PLATFORM_LOG_STRING[libraryKeyOrName]) !== null && _a !== void 0 ? _a : libraryKeyOrName;\r\n    if (variant) {\r\n        library += `-${variant}`;\r\n    }\r\n    const libraryMismatch = library.match(/\\s|\\//);\r\n    const versionMismatch = version.match(/\\s|\\//);\r\n    if (libraryMismatch || versionMismatch) {\r\n        const warning = [\r\n            `Unable to register library \"${library}\" with version \"${version}\":`\r\n        ];\r\n        if (libraryMismatch) {\r\n            warning.push(`library name \"${library}\" contains illegal characters (whitespace or \"/\")`);\r\n        }\r\n        if (libraryMismatch && versionMismatch) {\r\n            warning.push('and');\r\n        }\r\n        if (versionMismatch) {\r\n            warning.push(`version name \"${version}\" contains illegal characters (whitespace or \"/\")`);\r\n        }\r\n        logger.warn(warning.join(' '));\r\n        return;\r\n    }\r\n    _registerComponent(new Component(`${library}-version`, () => ({ library, version }), \"VERSION\" /* ComponentType.VERSION */));\r\n}\r\n/**\r\n * Sets log handler for all Firebase SDKs.\r\n * @param logCallback - An optional custom log handler that executes user code whenever\r\n * the Firebase SDK makes a logging call.\r\n *\r\n * @public\r\n */\r\nfunction onLog(logCallback, options) {\r\n    if (logCallback !== null && typeof logCallback !== 'function') {\r\n        throw ERROR_FACTORY.create(\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */);\r\n    }\r\n    setUserLogHandler(logCallback, options);\r\n}\r\n/**\r\n * Sets log level for all Firebase SDKs.\r\n *\r\n * All of the log types above the current log level are captured (i.e. if\r\n * you set the log level to `info`, errors are logged, but `debug` and\r\n * `verbose` logs are not).\r\n *\r\n * @public\r\n */\r\nfunction setLogLevel(logLevel) {\r\n    setLogLevel$1(logLevel);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst DB_NAME = 'firebase-heartbeat-database';\r\nconst DB_VERSION = 1;\r\nconst STORE_NAME = 'firebase-heartbeat-store';\r\nlet dbPromise = null;\r\nfunction getDbPromise() {\r\n    if (!dbPromise) {\r\n        dbPromise = openDB(DB_NAME, DB_VERSION, {\r\n            upgrade: (db, oldVersion) => {\r\n                // We don't use 'break' in this switch statement, the fall-through\r\n                // behavior is what we want, because if there are multiple versions between\r\n                // the old version and the current version, we want ALL the migrations\r\n                // that correspond to those versions to run, not only the last one.\r\n                // eslint-disable-next-line default-case\r\n                switch (oldVersion) {\r\n                    case 0:\r\n                        try {\r\n                            db.createObjectStore(STORE_NAME);\r\n                        }\r\n                        catch (e) {\r\n                            // Safari/iOS browsers throw occasional exceptions on\r\n                            // db.createObjectStore() that may be a bug. Avoid blocking\r\n                            // the rest of the app functionality.\r\n                            console.warn(e);\r\n                        }\r\n                }\r\n            }\r\n        }).catch(e => {\r\n            throw ERROR_FACTORY.create(\"idb-open\" /* AppError.IDB_OPEN */, {\r\n                originalErrorMessage: e.message\r\n            });\r\n        });\r\n    }\r\n    return dbPromise;\r\n}\r\nasync function readHeartbeatsFromIndexedDB(app) {\r\n    try {\r\n        const db = await getDbPromise();\r\n        const tx = db.transaction(STORE_NAME);\r\n        const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\r\n        // We already have the value but tx.done can throw,\r\n        // so we need to await it here to catch errors\r\n        await tx.done;\r\n        return result;\r\n    }\r\n    catch (e) {\r\n        if (e instanceof FirebaseError) {\r\n            logger.warn(e.message);\r\n        }\r\n        else {\r\n            const idbGetError = ERROR_FACTORY.create(\"idb-get\" /* AppError.IDB_GET */, {\r\n                originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\r\n            });\r\n            logger.warn(idbGetError.message);\r\n        }\r\n    }\r\n}\r\nasync function writeHeartbeatsToIndexedDB(app, heartbeatObject) {\r\n    try {\r\n        const db = await getDbPromise();\r\n        const tx = db.transaction(STORE_NAME, 'readwrite');\r\n        const objectStore = tx.objectStore(STORE_NAME);\r\n        await objectStore.put(heartbeatObject, computeKey(app));\r\n        await tx.done;\r\n    }\r\n    catch (e) {\r\n        if (e instanceof FirebaseError) {\r\n            logger.warn(e.message);\r\n        }\r\n        else {\r\n            const idbGetError = ERROR_FACTORY.create(\"idb-set\" /* AppError.IDB_WRITE */, {\r\n                originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\r\n            });\r\n            logger.warn(idbGetError.message);\r\n        }\r\n    }\r\n}\r\nfunction computeKey(app) {\r\n    return `${app.name}!${app.options.appId}`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst MAX_HEADER_BYTES = 1024;\r\n// 30 days\r\nconst STORED_HEARTBEAT_RETENTION_MAX_MILLIS = 30 * 24 * 60 * 60 * 1000;\r\nclass HeartbeatServiceImpl {\r\n    constructor(container) {\r\n        this.container = container;\r\n        /**\r\n         * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\r\n         * the header string.\r\n         * Stores one record per date. This will be consolidated into the standard\r\n         * format of one record per user agent string before being sent as a header.\r\n         * Populated from indexedDB when the controller is instantiated and should\r\n         * be kept in sync with indexedDB.\r\n         * Leave public for easier testing.\r\n         */\r\n        this._heartbeatsCache = null;\r\n        const app = this.container.getProvider('app').getImmediate();\r\n        this._storage = new HeartbeatStorageImpl(app);\r\n        this._heartbeatsCachePromise = this._storage.read().then(result => {\r\n            this._heartbeatsCache = result;\r\n            return result;\r\n        });\r\n    }\r\n    /**\r\n     * Called to report a heartbeat. The function will generate\r\n     * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\r\n     * to IndexedDB.\r\n     * Note that we only store one heartbeat per day. So if a heartbeat for today is\r\n     * already logged, subsequent calls to this function in the same day will be ignored.\r\n     */\r\n    async triggerHeartbeat() {\r\n        var _a, _b;\r\n        try {\r\n            const platformLogger = this.container\r\n                .getProvider('platform-logger')\r\n                .getImmediate();\r\n            // This is the \"Firebase user agent\" string from the platform logger\r\n            // service, not the browser user agent.\r\n            const agent = platformLogger.getPlatformInfoString();\r\n            const date = getUTCDateString();\r\n            if (((_a = this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null) {\r\n                this._heartbeatsCache = await this._heartbeatsCachePromise;\r\n                // If we failed to construct a heartbeats cache, then return immediately.\r\n                if (((_b = this._heartbeatsCache) === null || _b === void 0 ? void 0 : _b.heartbeats) == null) {\r\n                    return;\r\n                }\r\n            }\r\n            // Do not store a heartbeat if one is already stored for this day\r\n            // or if a header has already been sent today.\r\n            if (this._heartbeatsCache.lastSentHeartbeatDate === date ||\r\n                this._heartbeatsCache.heartbeats.some(singleDateHeartbeat => singleDateHeartbeat.date === date)) {\r\n                return;\r\n            }\r\n            else {\r\n                // There is no entry for this date. Create one.\r\n                this._heartbeatsCache.heartbeats.push({ date, agent });\r\n            }\r\n            // Remove entries older than 30 days.\r\n            this._heartbeatsCache.heartbeats =\r\n                this._heartbeatsCache.heartbeats.filter(singleDateHeartbeat => {\r\n                    const hbTimestamp = new Date(singleDateHeartbeat.date).valueOf();\r\n                    const now = Date.now();\r\n                    return now - hbTimestamp <= STORED_HEARTBEAT_RETENTION_MAX_MILLIS;\r\n                });\r\n            return this._storage.overwrite(this._heartbeatsCache);\r\n        }\r\n        catch (e) {\r\n            logger.warn(e);\r\n        }\r\n    }\r\n    /**\r\n     * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\r\n     * It also clears all heartbeats from memory as well as in IndexedDB.\r\n     *\r\n     * NOTE: Consuming product SDKs should not send the header if this method\r\n     * returns an empty string.\r\n     */\r\n    async getHeartbeatsHeader() {\r\n        var _a;\r\n        try {\r\n            if (this._heartbeatsCache === null) {\r\n                await this._heartbeatsCachePromise;\r\n            }\r\n            // If it's still null or the array is empty, there is no data to send.\r\n            if (((_a = this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null ||\r\n                this._heartbeatsCache.heartbeats.length === 0) {\r\n                return '';\r\n            }\r\n            const date = getUTCDateString();\r\n            // Extract as many heartbeats from the cache as will fit under the size limit.\r\n            const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(this._heartbeatsCache.heartbeats);\r\n            const headerString = base64urlEncodeWithoutPadding(JSON.stringify({ version: 2, heartbeats: heartbeatsToSend }));\r\n            // Store last sent date to prevent another being logged/sent for the same day.\r\n            this._heartbeatsCache.lastSentHeartbeatDate = date;\r\n            if (unsentEntries.length > 0) {\r\n                // Store any unsent entries if they exist.\r\n                this._heartbeatsCache.heartbeats = unsentEntries;\r\n                // This seems more likely than emptying the array (below) to lead to some odd state\r\n                // since the cache isn't empty and this will be called again on the next request,\r\n                // and is probably safest if we await it.\r\n                await this._storage.overwrite(this._heartbeatsCache);\r\n            }\r\n            else {\r\n                this._heartbeatsCache.heartbeats = [];\r\n                // Do not wait for this, to reduce latency.\r\n                void this._storage.overwrite(this._heartbeatsCache);\r\n            }\r\n            return headerString;\r\n        }\r\n        catch (e) {\r\n            logger.warn(e);\r\n            return '';\r\n        }\r\n    }\r\n}\r\nfunction getUTCDateString() {\r\n    const today = new Date();\r\n    // Returns date format 'YYYY-MM-DD'\r\n    return today.toISOString().substring(0, 10);\r\n}\r\nfunction extractHeartbeatsForHeader(heartbeatsCache, maxSize = MAX_HEADER_BYTES) {\r\n    // Heartbeats grouped by user agent in the standard format to be sent in\r\n    // the header.\r\n    const heartbeatsToSend = [];\r\n    // Single date format heartbeats that are not sent.\r\n    let unsentEntries = heartbeatsCache.slice();\r\n    for (const singleDateHeartbeat of heartbeatsCache) {\r\n        // Look for an existing entry with the same user agent.\r\n        const heartbeatEntry = heartbeatsToSend.find(hb => hb.agent === singleDateHeartbeat.agent);\r\n        if (!heartbeatEntry) {\r\n            // If no entry for this user agent exists, create one.\r\n            heartbeatsToSend.push({\r\n                agent: singleDateHeartbeat.agent,\r\n                dates: [singleDateHeartbeat.date]\r\n            });\r\n            if (countBytes(heartbeatsToSend) > maxSize) {\r\n                // If the header would exceed max size, remove the added heartbeat\r\n                // entry and stop adding to the header.\r\n                heartbeatsToSend.pop();\r\n                break;\r\n            }\r\n        }\r\n        else {\r\n            heartbeatEntry.dates.push(singleDateHeartbeat.date);\r\n            // If the header would exceed max size, remove the added date\r\n            // and stop adding to the header.\r\n            if (countBytes(heartbeatsToSend) > maxSize) {\r\n                heartbeatEntry.dates.pop();\r\n                break;\r\n            }\r\n        }\r\n        // Pop unsent entry from queue. (Skipped if adding the entry exceeded\r\n        // quota and the loop breaks early.)\r\n        unsentEntries = unsentEntries.slice(1);\r\n    }\r\n    return {\r\n        heartbeatsToSend,\r\n        unsentEntries\r\n    };\r\n}\r\nclass HeartbeatStorageImpl {\r\n    constructor(app) {\r\n        this.app = app;\r\n        this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\r\n    }\r\n    async runIndexedDBEnvironmentCheck() {\r\n        if (!isIndexedDBAvailable()) {\r\n            return false;\r\n        }\r\n        else {\r\n            return validateIndexedDBOpenable()\r\n                .then(() => true)\r\n                .catch(() => false);\r\n        }\r\n    }\r\n    /**\r\n     * Read all heartbeats.\r\n     */\r\n    async read() {\r\n        const canUseIndexedDB = await this._canUseIndexedDBPromise;\r\n        if (!canUseIndexedDB) {\r\n            return { heartbeats: [] };\r\n        }\r\n        else {\r\n            const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\r\n            if (idbHeartbeatObject === null || idbHeartbeatObject === void 0 ? void 0 : idbHeartbeatObject.heartbeats) {\r\n                return idbHeartbeatObject;\r\n            }\r\n            else {\r\n                return { heartbeats: [] };\r\n            }\r\n        }\r\n    }\r\n    // overwrite the storage with the provided heartbeats\r\n    async overwrite(heartbeatsObject) {\r\n        var _a;\r\n        const canUseIndexedDB = await this._canUseIndexedDBPromise;\r\n        if (!canUseIndexedDB) {\r\n            return;\r\n        }\r\n        else {\r\n            const existingHeartbeatsObject = await this.read();\r\n            return writeHeartbeatsToIndexedDB(this.app, {\r\n                lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\r\n                heartbeats: heartbeatsObject.heartbeats\r\n            });\r\n        }\r\n    }\r\n    // add heartbeats\r\n    async add(heartbeatsObject) {\r\n        var _a;\r\n        const canUseIndexedDB = await this._canUseIndexedDBPromise;\r\n        if (!canUseIndexedDB) {\r\n            return;\r\n        }\r\n        else {\r\n            const existingHeartbeatsObject = await this.read();\r\n            return writeHeartbeatsToIndexedDB(this.app, {\r\n                lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\r\n                heartbeats: [\r\n                    ...existingHeartbeatsObject.heartbeats,\r\n                    ...heartbeatsObject.heartbeats\r\n                ]\r\n            });\r\n        }\r\n    }\r\n}\r\n/**\r\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\r\n * in a platform logging header JSON object, stringified, and converted\r\n * to base 64.\r\n */\r\nfunction countBytes(heartbeatsCache) {\r\n    // base64 has a restricted set of characters, all of which should be 1 byte.\r\n    return base64urlEncodeWithoutPadding(\r\n    // heartbeatsCache wrapper properties\r\n    JSON.stringify({ version: 2, heartbeats: heartbeatsCache })).length;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction registerCoreComponents(variant) {\r\n    _registerComponent(new Component('platform-logger', container => new PlatformLoggerServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\r\n    _registerComponent(new Component('heartbeat', container => new HeartbeatServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\r\n    // Register `app` package.\r\n    registerVersion(name$q, version$1, variant);\r\n    // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\r\n    registerVersion(name$q, version$1, 'esm2017');\r\n    // Register platform SDK identifier (no version).\r\n    registerVersion('fire-js', '');\r\n}\n\n/**\r\n * Firebase App\r\n *\r\n * @remarks This package coordinates the communication between the different Firebase components\r\n * @packageDocumentation\r\n */\r\nregisterCoreComponents('');\n\nexport { SDK_VERSION, DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME, _addComponent, _addOrOverwriteComponent, _apps, _clearComponents, _components, _getProvider, _isFirebaseApp, _isFirebaseServerApp, _registerComponent, _removeServiceInstance, _serverApps, deleteApp, getApp, getApps, initializeApp, initializeServerApp, onLog, registerVersion, setLogLevel };\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,kBAAkB,QAAQ,qBAAqB;AACnE,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,WAAW,IAAIC,aAAa,QAAQ,kBAAkB;AAC1F,SAASC,YAAY,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEC,aAAa,EAAEC,6BAA6B,EAAEC,oBAAoB,EAAEC,yBAAyB,QAAQ,gBAAgB;AACpM,SAASH,aAAa,QAAQ,gBAAgB;AAC9C,SAASI,MAAM,QAAQ,KAAK;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA;EACA;EACAC,qBAAqBA,CAAA,EAAG;IACpB,MAAMC,SAAS,GAAG,IAAI,CAACF,SAAS,CAACG,YAAY,CAAC,CAAC;IAC/C;IACA;IACA,OAAOD,SAAS,CACXE,GAAG,CAACC,QAAQ,IAAI;MACjB,IAAIC,wBAAwB,CAACD,QAAQ,CAAC,EAAE;QACpC,MAAME,OAAO,GAAGF,QAAQ,CAACG,YAAY,CAAC,CAAC;QACvC,OAAO,GAAGD,OAAO,CAACE,OAAO,IAAIF,OAAO,CAACG,OAAO,EAAE;MAClD,CAAC,MACI;QACD,OAAO,IAAI;MACf;IACJ,CAAC,CAAC,CACGC,MAAM,CAACC,SAAS,IAAIA,SAAS,CAAC,CAC9BC,IAAI,CAAC,GAAG,CAAC;EAClB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASP,wBAAwBA,CAACD,QAAQ,EAAE;EACxC,MAAMS,SAAS,GAAGT,QAAQ,CAACU,YAAY,CAAC,CAAC;EACzC,OAAO,CAACD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,IAAI,MAAM,SAAS,CAAC;AAChG;AAEA,MAAMC,MAAM,GAAG,eAAe;AAC9B,MAAMC,SAAS,GAAG,SAAS;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,IAAInC,MAAM,CAAC,eAAe,CAAC;AAE1C,MAAMoC,MAAM,GAAG,sBAAsB;AAErC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,gBAAgB;AAE/B,MAAMC,MAAM,GAAG,uBAAuB;AAEtC,MAAMC,MAAM,GAAG,oBAAoB;AAEnC,MAAMC,MAAM,GAAG,wBAAwB;AAEvC,MAAMC,MAAM,GAAG,2BAA2B;AAE1C,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,yBAAyB;AAExC,MAAMC,MAAM,GAAG,gCAAgC;AAE/C,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,uBAAuB;AAEtC,MAAMC,MAAM,GAAG,8BAA8B;AAE7C,MAAMC,MAAM,GAAG,yBAAyB;AAExC,MAAMC,MAAM,GAAG,gCAAgC;AAE/C,MAAMC,MAAM,GAAG,mBAAmB;AAElC,MAAMC,MAAM,GAAG,0BAA0B;AAEzC,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,IAAI,GAAG,UAAU;AACvB,MAAMnC,OAAO,GAAG,SAAS;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoC,kBAAkB,GAAG,WAAW;AACtC,MAAMC,mBAAmB,GAAG;EACxB,CAAC9B,MAAM,GAAG,WAAW;EACrB,CAACG,MAAM,GAAG,kBAAkB;EAC5B,CAACE,MAAM,GAAG,gBAAgB;EAC1B,CAACD,MAAM,GAAG,uBAAuB;EACjC,CAACG,MAAM,GAAG,gBAAgB;EAC1B,CAACD,MAAM,GAAG,uBAAuB;EACjC,CAACE,MAAM,GAAG,WAAW;EACrB,CAACC,MAAM,GAAG,kBAAkB;EAC5B,CAACC,MAAM,GAAG,WAAW;EACrB,CAACC,MAAM,GAAG,mBAAmB;EAC7B,CAACC,MAAM,GAAG,kBAAkB;EAC5B,CAACC,MAAM,GAAG,SAAS;EACnB,CAACC,MAAM,GAAG,gBAAgB;EAC1B,CAACC,MAAM,GAAG,UAAU;EACpB,CAACC,MAAM,GAAG,iBAAiB;EAC3B,CAACC,MAAM,GAAG,UAAU;EACpB,CAACC,MAAM,GAAG,iBAAiB;EAC3B,CAACC,MAAM,GAAG,WAAW;EACrB,CAACC,MAAM,GAAG,kBAAkB;EAC5B,CAACC,MAAM,GAAG,SAAS;EACnB,CAACC,MAAM,GAAG,gBAAgB;EAC1B,CAACC,MAAM,GAAG,UAAU;EACpB,CAACC,MAAM,GAAG,iBAAiB;EAC3B,CAACC,MAAM,GAAG,UAAU;EACpB,CAACE,MAAM,GAAG,iBAAiB;EAC3B,CAACD,MAAM,GAAG,aAAa;EACvB,SAAS,EAAE,SAAS;EACpB,CAACE,IAAI,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;AACvB;AACA;AACA;AACA,MAAMC,WAAW,GAAG,IAAID,GAAG,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,WAAW,GAAG,IAAIF,GAAG,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA,SAASG,aAAaA,CAACC,GAAG,EAAEvC,SAAS,EAAE;EACnC,IAAI;IACAuC,GAAG,CAACrD,SAAS,CAACsD,YAAY,CAACxC,SAAS,CAAC;EACzC,CAAC,CACD,OAAOyC,CAAC,EAAE;IACNpC,MAAM,CAACqC,KAAK,CAAC,aAAa1C,SAAS,CAAC+B,IAAI,wCAAwCQ,GAAG,CAACR,IAAI,EAAE,EAAEU,CAAC,CAAC;EAClG;AACJ;AACA;AACA;AACA;AACA;AACA,SAASE,wBAAwBA,CAACJ,GAAG,EAAEvC,SAAS,EAAE;EAC9CuC,GAAG,CAACrD,SAAS,CAAC0D,uBAAuB,CAAC5C,SAAS,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6C,kBAAkBA,CAAC7C,SAAS,EAAE;EACnC,MAAM8C,aAAa,GAAG9C,SAAS,CAAC+B,IAAI;EACpC,IAAIM,WAAW,CAACU,GAAG,CAACD,aAAa,CAAC,EAAE;IAChCzC,MAAM,CAACqC,KAAK,CAAC,sDAAsDI,aAAa,GAAG,CAAC;IACpF,OAAO,KAAK;EAChB;EACAT,WAAW,CAACW,GAAG,CAACF,aAAa,EAAE9C,SAAS,CAAC;EACzC;EACA,KAAK,MAAMuC,GAAG,IAAIL,KAAK,CAACe,MAAM,CAAC,CAAC,EAAE;IAC9BX,aAAa,CAACC,GAAG,EAAEvC,SAAS,CAAC;EACjC;EACA,KAAK,MAAMkD,SAAS,IAAId,WAAW,CAACa,MAAM,CAAC,CAAC,EAAE;IAC1CX,aAAa,CAACY,SAAS,EAAElD,SAAS,CAAC;EACvC;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmD,YAAYA,CAACZ,GAAG,EAAER,IAAI,EAAE;EAC7B,MAAMqB,mBAAmB,GAAGb,GAAG,CAACrD,SAAS,CACpCmE,WAAW,CAAC,WAAW,CAAC,CACxB3D,YAAY,CAAC;IAAE4D,QAAQ,EAAE;EAAK,CAAC,CAAC;EACrC,IAAIF,mBAAmB,EAAE;IACrB,KAAKA,mBAAmB,CAACG,gBAAgB,CAAC,CAAC;EAC/C;EACA,OAAOhB,GAAG,CAACrD,SAAS,CAACmE,WAAW,CAACtB,IAAI,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyB,sBAAsBA,CAACjB,GAAG,EAAER,IAAI,EAAE0B,kBAAkB,GAAGzB,kBAAkB,EAAE;EAChFmB,YAAY,CAACZ,GAAG,EAAER,IAAI,CAAC,CAAC2B,aAAa,CAACD,kBAAkB,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,cAAcA,CAACC,GAAG,EAAE;EACzB,OAAOA,GAAG,CAACC,OAAO,KAAKC,SAAS;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACH,GAAG,EAAE;EAC/B,OAAOA,GAAG,CAACI,QAAQ,KAAKF,SAAS;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,gBAAgBA,CAAA,EAAG;EACxB5B,WAAW,CAAC6B,KAAK,CAAC,CAAC;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG;EACX,CAAC,QAAQ,CAAC,wBAAwB,kDAAkD,GAChF,4BAA4B;EAChC,CAAC,cAAc,CAAC,8BAA8B,gCAAgC;EAC9E,CAAC,eAAe,CAAC,+BAA+B,iFAAiF;EACjI,CAAC,aAAa,CAAC,6BAA6B,iDAAiD;EAC7F,CAAC,oBAAoB,CAAC,oCAAoC,sCAAsC;EAChG,CAAC,YAAY,CAAC,4BAA4B,yEAAyE;EACnH,CAAC,sBAAsB,CAAC,sCAAsC,sDAAsD,GAChH,wBAAwB;EAC5B,CAAC,sBAAsB,CAAC,sCAAsC,uDAAuD;EACrH,CAAC,UAAU,CAAC,0BAA0B,+EAA+E;EACrH,CAAC,SAAS,CAAC,yBAAyB,oFAAoF;EACxH,CAAC,SAAS,CAAC,2BAA2B,kFAAkF;EACxH,CAAC,YAAY,CAAC,4BAA4B,qFAAqF;EAC/H,CAAC,qCAAqC,CAAC,qDAAqD,yGAAyG;EACrM,CAAC,gCAAgC,CAAC,gDAAgD;AACtF,CAAC;AACD,MAAMC,aAAa,GAAG,IAAI9F,YAAY,CAAC,KAAK,EAAE,UAAU,EAAE6F,MAAM,CAAC;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,eAAe,CAAC;EAClBpF,WAAWA,CAAC4E,OAAO,EAAES,MAAM,EAAEpF,SAAS,EAAE;IACpC,IAAI,CAACqF,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,OAAO,CAAC;IAC1C,IAAI,CAACc,OAAO,GAAGF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAAC;IACxC,IAAI,CAACM,KAAK,GAAGN,MAAM,CAACvC,IAAI;IACxB,IAAI,CAAC8C,+BAA+B,GAChCP,MAAM,CAACQ,8BAA8B;IACzC,IAAI,CAACC,UAAU,GAAG7F,SAAS;IAC3B,IAAI,CAACA,SAAS,CAACsD,YAAY,CAAC,IAAIxE,SAAS,CAAC,KAAK,EAAE,MAAM,IAAI,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAAC;EACtG;EACA,IAAI8G,8BAA8BA,CAAA,EAAG;IACjC,IAAI,CAACE,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACH,+BAA+B;EAC/C;EACA,IAAIC,8BAA8BA,CAACG,GAAG,EAAE;IACpC,IAAI,CAACD,cAAc,CAAC,CAAC;IACrB,IAAI,CAACH,+BAA+B,GAAGI,GAAG;EAC9C;EACA,IAAIlD,IAAIA,CAAA,EAAG;IACP,IAAI,CAACiD,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACJ,KAAK;EACrB;EACA,IAAIf,OAAOA,CAAA,EAAG;IACV,IAAI,CAACmB,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACR,QAAQ;EACxB;EACA,IAAIF,MAAMA,CAAA,EAAG;IACT,IAAI,CAACU,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACL,OAAO;EACvB;EACA,IAAIzF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC6F,UAAU;EAC1B;EACA,IAAIG,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACX,UAAU;EAC1B;EACA,IAAIW,SAASA,CAACD,GAAG,EAAE;IACf,IAAI,CAACV,UAAU,GAAGU,GAAG;EACzB;EACA;AACJ;AACA;AACA;EACID,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACE,SAAS,EAAE;MAChB,MAAMd,aAAa,CAACe,MAAM,CAAC,aAAa,CAAC,4BAA4B;QAAEC,OAAO,EAAE,IAAI,CAACR;MAAM,CAAC,CAAC;IACjG;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,qBAAqB,SAAShB,eAAe,CAAC;EAChDpF,WAAWA,CAAC4E,OAAO,EAAEyB,YAAY,EAAEvD,IAAI,EAAE7C,SAAS,EAAE;IAChD;IACA,MAAM4F,8BAA8B,GAAGQ,YAAY,CAACR,8BAA8B,KAAKhB,SAAS,GAC1FwB,YAAY,CAACR,8BAA8B,GAC3C,KAAK;IACX;IACA,MAAMR,MAAM,GAAG;MACXvC,IAAI;MACJ+C;IACJ,CAAC;IACD,IAAIjB,OAAO,CAAC0B,MAAM,KAAKzB,SAAS,EAAE;MAC9B;MACA,KAAK,CAACD,OAAO,EAAES,MAAM,EAAEpF,SAAS,CAAC;IACrC,CAAC,MACI;MACD,MAAMsG,OAAO,GAAG3B,OAAO;MACvB,KAAK,CAAC2B,OAAO,CAAC3B,OAAO,EAAES,MAAM,EAAEpF,SAAS,CAAC;IAC7C;IACA;IACA,IAAI,CAACuG,aAAa,GAAGhB,MAAM,CAACC,MAAM,CAAC;MAAEI;IAA+B,CAAC,EAAEQ,YAAY,CAAC;IACpF,IAAI,CAACI,qBAAqB,GAAG,IAAI;IACjC,IAAI,OAAOC,oBAAoB,KAAK,WAAW,EAAE;MAC7C,IAAI,CAACD,qBAAqB,GAAG,IAAIC,oBAAoB,CAAC,MAAM;QACxD,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN;IACA,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,WAAW,CAAC,IAAI,CAACL,aAAa,CAACM,cAAc,CAAC;IACnD;IACA;IACA,IAAI,CAACN,aAAa,CAACM,cAAc,GAAGjC,SAAS;IAC7CwB,YAAY,CAACS,cAAc,GAAGjC,SAAS;IACvCkC,eAAe,CAAC7F,MAAM,EAAEC,SAAS,EAAE,WAAW,CAAC;EACnD;EACA6F,MAAMA,CAAA,EAAG;IACL,OAAOnC,SAAS;EACpB;EACA,IAAIoC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACL,SAAS;EACzB;EACA;EACA;EACAC,WAAWA,CAAClC,GAAG,EAAE;IACb,IAAI,IAAI,CAACsB,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACW,SAAS,EAAE;IAChB,IAAIjC,GAAG,KAAKE,SAAS,IAAI,IAAI,CAAC4B,qBAAqB,KAAK,IAAI,EAAE;MAC1D,IAAI,CAACA,qBAAqB,CAACS,QAAQ,CAACvC,GAAG,EAAE,IAAI,CAAC;IAClD;EACJ;EACA;EACAwC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAClB,SAAS,EAAE;MAChB,OAAO,CAAC;IACZ;IACA,OAAO,EAAE,IAAI,CAACW,SAAS;EAC3B;EACA;EACA;EACA;EACAD,gBAAgBA,CAAA,EAAG;IACf,KAAKS,SAAS,CAAC,IAAI,CAAC;EACxB;EACA,IAAIrC,QAAQA,CAAA,EAAG;IACX,IAAI,CAACgB,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACS,aAAa;EAC7B;EACA;AACJ;AACA;AACA;EACIT,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACE,SAAS,EAAE;MAChB,MAAMd,aAAa,CAACe,MAAM,CAAC,oBAAoB,CAAC,iCAAiC,CAAC;IACtF;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmB,WAAW,GAAG1G,OAAO;AAC3B,SAAS2G,aAAaA,CAAC/B,QAAQ,EAAEgC,SAAS,GAAG,CAAC,CAAC,EAAE;EAC7C,IAAI3C,OAAO,GAAGW,QAAQ;EACtB,IAAI,OAAOgC,SAAS,KAAK,QAAQ,EAAE;IAC/B,MAAMzE,IAAI,GAAGyE,SAAS;IACtBA,SAAS,GAAG;MAAEzE;IAAK,CAAC;EACxB;EACA,MAAMuC,MAAM,GAAGG,MAAM,CAACC,MAAM,CAAC;IAAE3C,IAAI,EAAEC,kBAAkB;IAAE8C,8BAA8B,EAAE;EAAM,CAAC,EAAE0B,SAAS,CAAC;EAC5G,MAAMzE,IAAI,GAAGuC,MAAM,CAACvC,IAAI;EACxB,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,EAAE;IACnC,MAAMqC,aAAa,CAACe,MAAM,CAAC,cAAc,CAAC,6BAA6B;MACnEC,OAAO,EAAEqB,MAAM,CAAC1E,IAAI;IACxB,CAAC,CAAC;EACN;EACA8B,OAAO,KAAKA,OAAO,GAAGtF,mBAAmB,CAAC,CAAC,CAAC;EAC5C,IAAI,CAACsF,OAAO,EAAE;IACV,MAAMO,aAAa,CAACe,MAAM,CAAC,YAAY,CAAC,yBAAyB,CAAC;EACtE;EACA,MAAMuB,WAAW,GAAGxE,KAAK,CAACyE,GAAG,CAAC5E,IAAI,CAAC;EACnC,IAAI2E,WAAW,EAAE;IACb;IACA,IAAIlI,SAAS,CAACqF,OAAO,EAAE6C,WAAW,CAAC7C,OAAO,CAAC,IACvCrF,SAAS,CAAC8F,MAAM,EAAEoC,WAAW,CAACpC,MAAM,CAAC,EAAE;MACvC,OAAOoC,WAAW;IACtB,CAAC,MACI;MACD,MAAMtC,aAAa,CAACe,MAAM,CAAC,eAAe,CAAC,8BAA8B;QAAEC,OAAO,EAAErD;MAAK,CAAC,CAAC;IAC/F;EACJ;EACA,MAAM7C,SAAS,GAAG,IAAIjB,kBAAkB,CAAC8D,IAAI,CAAC;EAC9C,KAAK,MAAM/B,SAAS,IAAIqC,WAAW,CAACY,MAAM,CAAC,CAAC,EAAE;IAC1C/D,SAAS,CAACsD,YAAY,CAACxC,SAAS,CAAC;EACrC;EACA,MAAM4G,MAAM,GAAG,IAAIvC,eAAe,CAACR,OAAO,EAAES,MAAM,EAAEpF,SAAS,CAAC;EAC9DgD,KAAK,CAACc,GAAG,CAACjB,IAAI,EAAE6E,MAAM,CAAC;EACvB,OAAOA,MAAM;AACjB;AACA,SAASC,mBAAmBA,CAACrC,QAAQ,EAAEsC,gBAAgB,EAAE;EACrD,IAAIrI,SAAS,CAAC,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;IAC/B;IACA,MAAM0F,aAAa,CAACe,MAAM,CAAC,gCAAgC,CAAC,6CAA6C,CAAC;EAC9G;EACA,IAAI2B,gBAAgB,CAAChC,8BAA8B,KAAKhB,SAAS,EAAE;IAC/DgD,gBAAgB,CAAChC,8BAA8B,GAAG,KAAK;EAC3D;EACA,IAAIiC,UAAU;EACd,IAAIpD,cAAc,CAACa,QAAQ,CAAC,EAAE;IAC1BuC,UAAU,GAAGvC,QAAQ,CAACX,OAAO;EACjC,CAAC,MACI;IACDkD,UAAU,GAAGvC,QAAQ;EACzB;EACA;EACA,MAAMwC,OAAO,GAAGvC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEoC,gBAAgB,CAAC,EAAEC,UAAU,CAAC;EAC9E;EACA;EACA,IAAIC,OAAO,CAACjB,cAAc,KAAKjC,SAAS,EAAE;IACtC,OAAOkD,OAAO,CAACjB,cAAc;EACjC;EACA,MAAMkB,QAAQ,GAAIC,CAAC,IAAK;IACpB,OAAO,CAAC,GAAGA,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAMC,IAAI,CAACC,IAAI,CAAC,EAAE,EAAEH,IAAI,CAAC,GAAGC,CAAC,CAACG,UAAU,CAAC,CAAC,CAAC,GAAI,CAAC,EAAE,CAAC,CAAC;EACrF,CAAC;EACD,IAAIV,gBAAgB,CAACf,cAAc,KAAKjC,SAAS,EAAE;IAC/C,IAAI,OAAO6B,oBAAoB,KAAK,WAAW,EAAE;MAC7C,MAAMvB,aAAa,CAACe,MAAM,CAAC,qCAAqC,CAAC,oDAAoD,CAAC,CAAC,CAAC;IAC5H;EACJ;EACA,MAAMsC,UAAU,GAAG,EAAE,GAAGR,QAAQ,CAACS,IAAI,CAACC,SAAS,CAACX,OAAO,CAAC,CAAC;EACzD,MAAMN,WAAW,GAAGtE,WAAW,CAACuE,GAAG,CAACc,UAAU,CAAC;EAC/C,IAAIf,WAAW,EAAE;IACbA,WAAW,CAACZ,WAAW,CAACgB,gBAAgB,CAACf,cAAc,CAAC;IACxD,OAAOW,WAAW;EACtB;EACA,MAAMxH,SAAS,GAAG,IAAIjB,kBAAkB,CAACwJ,UAAU,CAAC;EACpD,KAAK,MAAMzH,SAAS,IAAIqC,WAAW,CAACY,MAAM,CAAC,CAAC,EAAE;IAC1C/D,SAAS,CAACsD,YAAY,CAACxC,SAAS,CAAC;EACrC;EACA,MAAM4G,MAAM,GAAG,IAAIvB,qBAAqB,CAAC0B,UAAU,EAAED,gBAAgB,EAAEW,UAAU,EAAEvI,SAAS,CAAC;EAC7FkD,WAAW,CAACY,GAAG,CAACyE,UAAU,EAAEb,MAAM,CAAC;EACnC,OAAOA,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,MAAMA,CAAC7F,IAAI,GAAGC,kBAAkB,EAAE;EACvC,MAAMO,GAAG,GAAGL,KAAK,CAACyE,GAAG,CAAC5E,IAAI,CAAC;EAC3B,IAAI,CAACQ,GAAG,IAAIR,IAAI,KAAKC,kBAAkB,IAAIzD,mBAAmB,CAAC,CAAC,EAAE;IAC9D,OAAOgI,aAAa,CAAC,CAAC;EAC1B;EACA,IAAI,CAAChE,GAAG,EAAE;IACN,MAAM6B,aAAa,CAACe,MAAM,CAAC,QAAQ,CAAC,uBAAuB;MAAEC,OAAO,EAAErD;IAAK,CAAC,CAAC;EACjF;EACA,OAAOQ,GAAG;AACd;AACA;AACA;AACA;AACA;AACA,SAASsF,OAAOA,CAAA,EAAG;EACf,OAAOC,KAAK,CAACC,IAAI,CAAC7F,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,SAiBeoD,SAASA,CAAA2B,EAAA;EAAA,OAAAC,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAqBxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAF,WAAA;EAAAA,UAAA,GAAAG,iBAAA,CArBA,WAAyB7F,GAAG,EAAE;IAC1B,IAAI8F,gBAAgB,GAAG,KAAK;IAC5B,MAAMtG,IAAI,GAAGQ,GAAG,CAACR,IAAI;IACrB,IAAIG,KAAK,CAACa,GAAG,CAAChB,IAAI,CAAC,EAAE;MACjBsG,gBAAgB,GAAG,IAAI;MACvBnG,KAAK,CAACoG,MAAM,CAACvG,IAAI,CAAC;IACtB,CAAC,MACI,IAAIK,WAAW,CAACW,GAAG,CAAChB,IAAI,CAAC,EAAE;MAC5B,MAAMwG,iBAAiB,GAAGhG,GAAG;MAC7B,IAAIgG,iBAAiB,CAACnC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE;QACtChE,WAAW,CAACkG,MAAM,CAACvG,IAAI,CAAC;QACxBsG,gBAAgB,GAAG,IAAI;MAC3B;IACJ;IACA,IAAIA,gBAAgB,EAAE;MAClB,MAAMG,OAAO,CAACC,GAAG,CAAClG,GAAG,CAACrD,SAAS,CAC1BG,YAAY,CAAC,CAAC,CACdC,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAAC+I,MAAM,CAAC,CAAC,CAAC,CAAC;MACxC/F,GAAG,CAAC2C,SAAS,GAAG,IAAI;IACxB;EACJ,CAAC;EAAA,OAAA+C,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AASD,SAASnC,eAAeA,CAAC0C,gBAAgB,EAAE9I,OAAO,EAAE+I,OAAO,EAAE;EACzD,IAAIC,EAAE;EACN;EACA;EACA,IAAIjJ,OAAO,GAAG,CAACiJ,EAAE,GAAG3G,mBAAmB,CAACyG,gBAAgB,CAAC,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGF,gBAAgB;EAC5G,IAAIC,OAAO,EAAE;IACThJ,OAAO,IAAI,IAAIgJ,OAAO,EAAE;EAC5B;EACA,MAAME,eAAe,GAAGlJ,OAAO,CAACmJ,KAAK,CAAC,OAAO,CAAC;EAC9C,MAAMC,eAAe,GAAGnJ,OAAO,CAACkJ,KAAK,CAAC,OAAO,CAAC;EAC9C,IAAID,eAAe,IAAIE,eAAe,EAAE;IACpC,MAAMC,OAAO,GAAG,CACZ,+BAA+BrJ,OAAO,mBAAmBC,OAAO,IAAI,CACvE;IACD,IAAIiJ,eAAe,EAAE;MACjBG,OAAO,CAACC,IAAI,CAAC,iBAAiBtJ,OAAO,mDAAmD,CAAC;IAC7F;IACA,IAAIkJ,eAAe,IAAIE,eAAe,EAAE;MACpCC,OAAO,CAACC,IAAI,CAAC,KAAK,CAAC;IACvB;IACA,IAAIF,eAAe,EAAE;MACjBC,OAAO,CAACC,IAAI,CAAC,iBAAiBrJ,OAAO,mDAAmD,CAAC;IAC7F;IACAS,MAAM,CAAC6I,IAAI,CAACF,OAAO,CAACjJ,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9B;EACJ;EACA8C,kBAAkB,CAAC,IAAI7E,SAAS,CAAC,GAAG2B,OAAO,UAAU,EAAE,OAAO;IAAEA,OAAO;IAAEC;EAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;AAChI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuJ,KAAKA,CAACC,WAAW,EAAEvF,OAAO,EAAE;EACjC,IAAIuF,WAAW,KAAK,IAAI,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;IAC3D,MAAMhF,aAAa,CAACe,MAAM,CAAC,sBAAsB,CAAC,mCAAmC,CAAC;EAC1F;EACAhH,iBAAiB,CAACiL,WAAW,EAAEvF,OAAO,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASzF,WAAWA,CAACiL,QAAQ,EAAE;EAC3BhL,aAAa,CAACgL,QAAQ,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAG,6BAA6B;AAC7C,MAAMC,UAAU,GAAG,CAAC;AACpB,MAAMC,UAAU,GAAG,0BAA0B;AAC7C,IAAIC,SAAS,GAAG,IAAI;AACpB,SAASC,YAAYA,CAAA,EAAG;EACpB,IAAI,CAACD,SAAS,EAAE;IACZA,SAAS,GAAG1K,MAAM,CAACuK,OAAO,EAAEC,UAAU,EAAE;MACpCI,OAAO,EAAEA,CAACC,EAAE,EAAEC,UAAU,KAAK;QACzB;QACA;QACA;QACA;QACA;QACA,QAAQA,UAAU;UACd,KAAK,CAAC;YACF,IAAI;cACAD,EAAE,CAACE,iBAAiB,CAACN,UAAU,CAAC;YACpC,CAAC,CACD,OAAO/G,CAAC,EAAE;cACN;cACA;cACA;cACAsH,OAAO,CAACb,IAAI,CAACzG,CAAC,CAAC;YACnB;QACR;MACJ;IACJ,CAAC,CAAC,CAACuH,KAAK,CAACvH,CAAC,IAAI;MACV,MAAM2B,aAAa,CAACe,MAAM,CAAC,UAAU,CAAC,yBAAyB;QAC3D8E,oBAAoB,EAAExH,CAAC,CAACyH;MAC5B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA,OAAOT,SAAS;AACpB;AAAC,SACcU,2BAA2BA,CAAAC,GAAA;EAAA,OAAAC,4BAAA,CAAAnC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAkC,6BAAA;EAAAA,4BAAA,GAAAjC,iBAAA,CAA1C,WAA2C7F,GAAG,EAAE;IAC5C,IAAI;MACA,MAAMqH,EAAE,SAASF,YAAY,CAAC,CAAC;MAC/B,MAAMY,EAAE,GAAGV,EAAE,CAACW,WAAW,CAACf,UAAU,CAAC;MACrC,MAAMgB,MAAM,SAASF,EAAE,CAACG,WAAW,CAACjB,UAAU,CAAC,CAAC7C,GAAG,CAAC+D,UAAU,CAACnI,GAAG,CAAC,CAAC;MACpE;MACA;MACA,MAAM+H,EAAE,CAACK,IAAI;MACb,OAAOH,MAAM;IACjB,CAAC,CACD,OAAO/H,CAAC,EAAE;MACN,IAAIA,CAAC,YAAY9D,aAAa,EAAE;QAC5B0B,MAAM,CAAC6I,IAAI,CAACzG,CAAC,CAACyH,OAAO,CAAC;MAC1B,CAAC,MACI;QACD,MAAMU,WAAW,GAAGxG,aAAa,CAACe,MAAM,CAAC,SAAS,CAAC,wBAAwB;UACvE8E,oBAAoB,EAAExH,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACyH;QAClE,CAAC,CAAC;QACF7J,MAAM,CAAC6I,IAAI,CAAC0B,WAAW,CAACV,OAAO,CAAC;MACpC;IACJ;EACJ,CAAC;EAAA,OAAAG,4BAAA,CAAAnC,KAAA,OAAAC,SAAA;AAAA;AAAA,SACc0C,0BAA0BA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,2BAAA,CAAA9C,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA6C,4BAAA;EAAAA,2BAAA,GAAA5C,iBAAA,CAAzC,WAA0C7F,GAAG,EAAE0I,eAAe,EAAE;IAC5D,IAAI;MACA,MAAMrB,EAAE,SAASF,YAAY,CAAC,CAAC;MAC/B,MAAMY,EAAE,GAAGV,EAAE,CAACW,WAAW,CAACf,UAAU,EAAE,WAAW,CAAC;MAClD,MAAMiB,WAAW,GAAGH,EAAE,CAACG,WAAW,CAACjB,UAAU,CAAC;MAC9C,MAAMiB,WAAW,CAACS,GAAG,CAACD,eAAe,EAAEP,UAAU,CAACnI,GAAG,CAAC,CAAC;MACvD,MAAM+H,EAAE,CAACK,IAAI;IACjB,CAAC,CACD,OAAOlI,CAAC,EAAE;MACN,IAAIA,CAAC,YAAY9D,aAAa,EAAE;QAC5B0B,MAAM,CAAC6I,IAAI,CAACzG,CAAC,CAACyH,OAAO,CAAC;MAC1B,CAAC,MACI;QACD,MAAMU,WAAW,GAAGxG,aAAa,CAACe,MAAM,CAAC,SAAS,CAAC,0BAA0B;UACzE8E,oBAAoB,EAAExH,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACyH;QAClE,CAAC,CAAC;QACF7J,MAAM,CAAC6I,IAAI,CAAC0B,WAAW,CAACV,OAAO,CAAC;MACpC;IACJ;EACJ,CAAC;EAAA,OAAAc,2BAAA,CAAA9C,KAAA,OAAAC,SAAA;AAAA;AACD,SAASuC,UAAUA,CAACnI,GAAG,EAAE;EACrB,OAAO,GAAGA,GAAG,CAACR,IAAI,IAAIQ,GAAG,CAACsB,OAAO,CAACsH,KAAK,EAAE;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,IAAI;AAC7B;AACA,MAAMC,qCAAqC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;AACtE,MAAMC,oBAAoB,CAAC;EACvBrM,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACqM,gBAAgB,GAAG,IAAI;IAC5B,MAAMhJ,GAAG,GAAG,IAAI,CAACrD,SAAS,CAACmE,WAAW,CAAC,KAAK,CAAC,CAAC3D,YAAY,CAAC,CAAC;IAC5D,IAAI,CAAC8L,QAAQ,GAAG,IAAIC,oBAAoB,CAAClJ,GAAG,CAAC;IAC7C,IAAI,CAACmJ,uBAAuB,GAAG,IAAI,CAACF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAACC,IAAI,CAACpB,MAAM,IAAI;MAC/D,IAAI,CAACe,gBAAgB,GAAGf,MAAM;MAC9B,OAAOA,MAAM;IACjB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUjH,gBAAgBA,CAAA,EAAG;IAAA,IAAAsI,KAAA;IAAA,OAAAzD,iBAAA;MACrB,IAAIQ,EAAE,EAAEkD,EAAE;MACV,IAAI;QACA,MAAMC,cAAc,GAAGF,KAAI,CAAC3M,SAAS,CAChCmE,WAAW,CAAC,iBAAiB,CAAC,CAC9B3D,YAAY,CAAC,CAAC;QACnB;QACA;QACA,MAAMsM,KAAK,GAAGD,cAAc,CAAC5M,qBAAqB,CAAC,CAAC;QACpD,MAAM8M,IAAI,GAAGC,gBAAgB,CAAC,CAAC;QAC/B,IAAI,CAAC,CAACtD,EAAE,GAAGiD,KAAI,CAACN,gBAAgB,MAAM,IAAI,IAAI3C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuD,UAAU,KAAK,IAAI,EAAE;UAC3FN,KAAI,CAACN,gBAAgB,SAASM,KAAI,CAACH,uBAAuB;UAC1D;UACA,IAAI,CAAC,CAACI,EAAE,GAAGD,KAAI,CAACN,gBAAgB,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,UAAU,KAAK,IAAI,EAAE;YAC3F;UACJ;QACJ;QACA;QACA;QACA,IAAIN,KAAI,CAACN,gBAAgB,CAACa,qBAAqB,KAAKH,IAAI,IACpDJ,KAAI,CAACN,gBAAgB,CAACY,UAAU,CAACE,IAAI,CAACC,mBAAmB,IAAIA,mBAAmB,CAACL,IAAI,KAAKA,IAAI,CAAC,EAAE;UACjG;QACJ,CAAC,MACI;UACD;UACAJ,KAAI,CAACN,gBAAgB,CAACY,UAAU,CAAClD,IAAI,CAAC;YAAEgD,IAAI;YAAED;UAAM,CAAC,CAAC;QAC1D;QACA;QACAH,KAAI,CAACN,gBAAgB,CAACY,UAAU,GAC5BN,KAAI,CAACN,gBAAgB,CAACY,UAAU,CAACtM,MAAM,CAACyM,mBAAmB,IAAI;UAC3D,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAACF,mBAAmB,CAACL,IAAI,CAAC,CAACQ,OAAO,CAAC,CAAC;UAChE,MAAMC,GAAG,GAAGF,IAAI,CAACE,GAAG,CAAC,CAAC;UACtB,OAAOA,GAAG,GAAGH,WAAW,IAAIlB,qCAAqC;QACrE,CAAC,CAAC;QACN,OAAOQ,KAAI,CAACL,QAAQ,CAACmB,SAAS,CAACd,KAAI,CAACN,gBAAgB,CAAC;MACzD,CAAC,CACD,OAAO9I,CAAC,EAAE;QACNpC,MAAM,CAAC6I,IAAI,CAACzG,CAAC,CAAC;MAClB;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUmK,mBAAmBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAzE,iBAAA;MACxB,IAAIQ,EAAE;MACN,IAAI;QACA,IAAIiE,MAAI,CAACtB,gBAAgB,KAAK,IAAI,EAAE;UAChC,MAAMsB,MAAI,CAACnB,uBAAuB;QACtC;QACA;QACA,IAAI,CAAC,CAAC9C,EAAE,GAAGiE,MAAI,CAACtB,gBAAgB,MAAM,IAAI,IAAI3C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuD,UAAU,KAAK,IAAI,IACzFU,MAAI,CAACtB,gBAAgB,CAACY,UAAU,CAACW,MAAM,KAAK,CAAC,EAAE;UAC/C,OAAO,EAAE;QACb;QACA,MAAMb,IAAI,GAAGC,gBAAgB,CAAC,CAAC;QAC/B;QACA,MAAM;UAAEa,gBAAgB;UAAEC;QAAc,CAAC,GAAGC,0BAA0B,CAACJ,MAAI,CAACtB,gBAAgB,CAACY,UAAU,CAAC;QACxG,MAAMe,YAAY,GAAGtO,6BAA6B,CAAC8I,IAAI,CAACC,SAAS,CAAC;UAAE/H,OAAO,EAAE,CAAC;UAAEuM,UAAU,EAAEY;QAAiB,CAAC,CAAC,CAAC;QAChH;QACAF,MAAI,CAACtB,gBAAgB,CAACa,qBAAqB,GAAGH,IAAI;QAClD,IAAIe,aAAa,CAACF,MAAM,GAAG,CAAC,EAAE;UAC1B;UACAD,MAAI,CAACtB,gBAAgB,CAACY,UAAU,GAAGa,aAAa;UAChD;UACA;UACA;UACA,MAAMH,MAAI,CAACrB,QAAQ,CAACmB,SAAS,CAACE,MAAI,CAACtB,gBAAgB,CAAC;QACxD,CAAC,MACI;UACDsB,MAAI,CAACtB,gBAAgB,CAACY,UAAU,GAAG,EAAE;UACrC;UACA,KAAKU,MAAI,CAACrB,QAAQ,CAACmB,SAAS,CAACE,MAAI,CAACtB,gBAAgB,CAAC;QACvD;QACA,OAAO2B,YAAY;MACvB,CAAC,CACD,OAAOzK,CAAC,EAAE;QACNpC,MAAM,CAAC6I,IAAI,CAACzG,CAAC,CAAC;QACd,OAAO,EAAE;MACb;IAAC;EACL;AACJ;AACA,SAASyJ,gBAAgBA,CAAA,EAAG;EACxB,MAAMiB,KAAK,GAAG,IAAIX,IAAI,CAAC,CAAC;EACxB;EACA,OAAOW,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;AAC/C;AACA,SAASJ,0BAA0BA,CAACK,eAAe,EAAEC,OAAO,GAAGnC,gBAAgB,EAAE;EAC7E;EACA;EACA,MAAM2B,gBAAgB,GAAG,EAAE;EAC3B;EACA,IAAIC,aAAa,GAAGM,eAAe,CAACE,KAAK,CAAC,CAAC;EAC3C,KAAK,MAAMlB,mBAAmB,IAAIgB,eAAe,EAAE;IAC/C;IACA,MAAMG,cAAc,GAAGV,gBAAgB,CAACW,IAAI,CAACC,EAAE,IAAIA,EAAE,CAAC3B,KAAK,KAAKM,mBAAmB,CAACN,KAAK,CAAC;IAC1F,IAAI,CAACyB,cAAc,EAAE;MACjB;MACAV,gBAAgB,CAAC9D,IAAI,CAAC;QAClB+C,KAAK,EAAEM,mBAAmB,CAACN,KAAK;QAChC4B,KAAK,EAAE,CAACtB,mBAAmB,CAACL,IAAI;MACpC,CAAC,CAAC;MACF,IAAI4B,UAAU,CAACd,gBAAgB,CAAC,GAAGQ,OAAO,EAAE;QACxC;QACA;QACAR,gBAAgB,CAACe,GAAG,CAAC,CAAC;QACtB;MACJ;IACJ,CAAC,MACI;MACDL,cAAc,CAACG,KAAK,CAAC3E,IAAI,CAACqD,mBAAmB,CAACL,IAAI,CAAC;MACnD;MACA;MACA,IAAI4B,UAAU,CAACd,gBAAgB,CAAC,GAAGQ,OAAO,EAAE;QACxCE,cAAc,CAACG,KAAK,CAACE,GAAG,CAAC,CAAC;QAC1B;MACJ;IACJ;IACA;IACA;IACAd,aAAa,GAAGA,aAAa,CAACQ,KAAK,CAAC,CAAC,CAAC;EAC1C;EACA,OAAO;IACHT,gBAAgB;IAChBC;EACJ,CAAC;AACL;AACA,MAAMvB,oBAAoB,CAAC;EACvBxM,WAAWA,CAACsD,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACwL,uBAAuB,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAAC;EACtE;EACMA,4BAA4BA,CAAA,EAAG;IAAA,OAAA5F,iBAAA;MACjC,IAAI,CAACvJ,oBAAoB,CAAC,CAAC,EAAE;QACzB,OAAO,KAAK;MAChB,CAAC,MACI;QACD,OAAOC,yBAAyB,CAAC,CAAC,CAC7B8M,IAAI,CAAC,MAAM,IAAI,CAAC,CAChB5B,KAAK,CAAC,MAAM,KAAK,CAAC;MAC3B;IAAC;EACL;EACA;AACJ;AACA;EACU2B,IAAIA,CAAA,EAAG;IAAA,IAAAsC,MAAA;IAAA,OAAA7F,iBAAA;MACT,MAAM8F,eAAe,SAASD,MAAI,CAACF,uBAAuB;MAC1D,IAAI,CAACG,eAAe,EAAE;QAClB,OAAO;UAAE/B,UAAU,EAAE;QAAG,CAAC;MAC7B,CAAC,MACI;QACD,MAAMgC,kBAAkB,SAAShE,2BAA2B,CAAC8D,MAAI,CAAC1L,GAAG,CAAC;QACtE,IAAI4L,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAAChC,UAAU,EAAE;UACvG,OAAOgC,kBAAkB;QAC7B,CAAC,MACI;UACD,OAAO;YAAEhC,UAAU,EAAE;UAAG,CAAC;QAC7B;MACJ;IAAC;EACL;EACA;EACMQ,SAASA,CAACyB,gBAAgB,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAjG,iBAAA;MAC9B,IAAIQ,EAAE;MACN,MAAMsF,eAAe,SAASG,MAAI,CAACN,uBAAuB;MAC1D,IAAI,CAACG,eAAe,EAAE;QAClB;MACJ,CAAC,MACI;QACD,MAAMI,wBAAwB,SAASD,MAAI,CAAC1C,IAAI,CAAC,CAAC;QAClD,OAAOd,0BAA0B,CAACwD,MAAI,CAAC9L,GAAG,EAAE;UACxC6J,qBAAqB,EAAE,CAACxD,EAAE,GAAGwF,gBAAgB,CAAChC,qBAAqB,MAAM,IAAI,IAAIxD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG0F,wBAAwB,CAAClC,qBAAqB;UACpJD,UAAU,EAAEiC,gBAAgB,CAACjC;QACjC,CAAC,CAAC;MACN;IAAC;EACL;EACA;EACMoC,GAAGA,CAACH,gBAAgB,EAAE;IAAA,IAAAI,MAAA;IAAA,OAAApG,iBAAA;MACxB,IAAIQ,EAAE;MACN,MAAMsF,eAAe,SAASM,MAAI,CAACT,uBAAuB;MAC1D,IAAI,CAACG,eAAe,EAAE;QAClB;MACJ,CAAC,MACI;QACD,MAAMI,wBAAwB,SAASE,MAAI,CAAC7C,IAAI,CAAC,CAAC;QAClD,OAAOd,0BAA0B,CAAC2D,MAAI,CAACjM,GAAG,EAAE;UACxC6J,qBAAqB,EAAE,CAACxD,EAAE,GAAGwF,gBAAgB,CAAChC,qBAAqB,MAAM,IAAI,IAAIxD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG0F,wBAAwB,CAAClC,qBAAqB;UACpJD,UAAU,EAAE,CACR,GAAGmC,wBAAwB,CAACnC,UAAU,EACtC,GAAGiC,gBAAgB,CAACjC,UAAU;QAEtC,CAAC,CAAC;MACN;IAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0B,UAAUA,CAACP,eAAe,EAAE;EACjC;EACA,OAAO1O,6BAA6B;EACpC;EACA8I,IAAI,CAACC,SAAS,CAAC;IAAE/H,OAAO,EAAE,CAAC;IAAEuM,UAAU,EAAEmB;EAAgB,CAAC,CAAC,CAAC,CAACR,MAAM;AACvE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2B,sBAAsBA,CAAC9F,OAAO,EAAE;EACrC9F,kBAAkB,CAAC,IAAI7E,SAAS,CAAC,iBAAiB,EAAEkB,SAAS,IAAI,IAAIF,yBAAyB,CAACE,SAAS,CAAC,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;EAClJ2D,kBAAkB,CAAC,IAAI7E,SAAS,CAAC,WAAW,EAAEkB,SAAS,IAAI,IAAIoM,oBAAoB,CAACpM,SAAS,CAAC,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;EACvI;EACA8G,eAAe,CAAC7F,MAAM,EAAEC,SAAS,EAAEuI,OAAO,CAAC;EAC3C;EACA3C,eAAe,CAAC7F,MAAM,EAAEC,SAAS,EAAE,SAAS,CAAC;EAC7C;EACA4F,eAAe,CAAC,SAAS,EAAE,EAAE,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAyI,sBAAsB,CAAC,EAAE,CAAC;AAE1B,SAASnI,WAAW,EAAEtE,kBAAkB,IAAI0M,mBAAmB,EAAEpM,aAAa,EAAEK,wBAAwB,EAAET,KAAK,EAAE+B,gBAAgB,EAAE5B,WAAW,EAAEc,YAAY,EAAEQ,cAAc,EAAEI,oBAAoB,EAAElB,kBAAkB,EAAEW,sBAAsB,EAAEpB,WAAW,EAAEiE,SAAS,EAAEuB,MAAM,EAAEC,OAAO,EAAEtB,aAAa,EAAEM,mBAAmB,EAAEsC,KAAK,EAAEnD,eAAe,EAAE5H,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}