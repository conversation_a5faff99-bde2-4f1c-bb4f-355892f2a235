{"ast": null, "code": "import { RouterModule } from '@angular/router';\n// Component pages\nimport { ListComponent } from './list/list.component';\nimport { GridComponent } from './grid/grid.component';\nimport { OverviewComponent } from './overview/overview.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: \"list\",\n  component: ListComponent\n}, {\n  path: \"grid\",\n  component: GridComponent\n}, {\n  path: \"overview\",\n  component: OverviewComponent\n}];\nexport class JobListsRoutingModule {\n  static {\n    this.ɵfac = function JobListsRoutingModule_Factory(t) {\n      return new (t || JobListsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: JobListsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(JobListsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ListComponent", "GridComponent", "OverviewComponent", "routes", "path", "component", "JobListsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\job-lists\\job-lists-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\n// Component pages\r\nimport { ListComponent } from './list/list.component';\r\nimport { GridComponent } from './grid/grid.component';\r\nimport { OverviewComponent } from './overview/overview.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: \"list\",\r\n    component: ListComponent\r\n  },\r\n  {\r\n    path: \"grid\",\r\n    component: GridComponent\r\n  },\r\n  {\r\n    path: \"overview\",\r\n    component: OverviewComponent\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class JobListsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AAEtD;AACA,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,iBAAiB,QAAQ,+BAA+B;;;AAEjE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBP,YAAY,CAACQ,QAAQ,CAACJ,MAAM,CAAC,EAC7BJ,YAAY;IAAA;EAAA;;;2EAEXO,qBAAqB;IAAAE,OAAA,GAAAC,EAAA,CAAAV,YAAA;IAAAW,OAAA,GAFtBX,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}