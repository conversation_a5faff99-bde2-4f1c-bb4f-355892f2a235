{"ast": null, "code": "/**\n * Outlayer Item\n */\n\n(function (window, factory) {\n  // universal module definition\n  /* jshint strict: false */ /* globals define, module, require */\n  if (typeof define == 'function' && define.amd) {\n    // AMD - RequireJS\n    define(['ev-emitter/ev-emitter', 'get-size/get-size'], factory);\n  } else if (typeof module == 'object' && module.exports) {\n    // CommonJS - Browserify, Webpack\n    module.exports = factory(require('ev-emitter'), require('get-size'));\n  } else {\n    // browser global\n    window.Outlayer = {};\n    window.Outlayer.Item = factory(window.EvEmitter, window.getSize);\n  }\n})(window, function factory(EvEmitter, getSize) {\n  'use strict';\n\n  // ----- helpers ----- //\n  function isEmptyObj(obj) {\n    for (var prop in obj) {\n      return false;\n    }\n    prop = null;\n    return true;\n  }\n\n  // -------------------------- CSS3 support -------------------------- //\n\n  var docElemStyle = document.documentElement.style;\n  var transitionProperty = typeof docElemStyle.transition == 'string' ? 'transition' : 'WebkitTransition';\n  var transformProperty = typeof docElemStyle.transform == 'string' ? 'transform' : 'WebkitTransform';\n  var transitionEndEvent = {\n    WebkitTransition: 'webkitTransitionEnd',\n    transition: 'transitionend'\n  }[transitionProperty];\n\n  // cache all vendor properties that could have vendor prefix\n  var vendorProperties = {\n    transform: transformProperty,\n    transition: transitionProperty,\n    transitionDuration: transitionProperty + 'Duration',\n    transitionProperty: transitionProperty + 'Property',\n    transitionDelay: transitionProperty + 'Delay'\n  };\n\n  // -------------------------- Item -------------------------- //\n\n  function Item(element, layout) {\n    if (!element) {\n      return;\n    }\n    this.element = element;\n    // parent layout class, i.e. Masonry, Isotope, or Packery\n    this.layout = layout;\n    this.position = {\n      x: 0,\n      y: 0\n    };\n    this._create();\n  }\n\n  // inherit EvEmitter\n  var proto = Item.prototype = Object.create(EvEmitter.prototype);\n  proto.constructor = Item;\n  proto._create = function () {\n    // transition objects\n    this._transn = {\n      ingProperties: {},\n      clean: {},\n      onEnd: {}\n    };\n    this.css({\n      position: 'absolute'\n    });\n  };\n\n  // trigger specified handler for event type\n  proto.handleEvent = function (event) {\n    var method = 'on' + event.type;\n    if (this[method]) {\n      this[method](event);\n    }\n  };\n  proto.getSize = function () {\n    this.size = getSize(this.element);\n  };\n\n  /**\n   * apply CSS styles to element\n   * @param {Object} style\n   */\n  proto.css = function (style) {\n    var elemStyle = this.element.style;\n    for (var prop in style) {\n      // use vendor property if available\n      var supportedProp = vendorProperties[prop] || prop;\n      elemStyle[supportedProp] = style[prop];\n    }\n  };\n\n  // measure position, and sets it\n  proto.getPosition = function () {\n    var style = getComputedStyle(this.element);\n    var isOriginLeft = this.layout._getOption('originLeft');\n    var isOriginTop = this.layout._getOption('originTop');\n    var xValue = style[isOriginLeft ? 'left' : 'right'];\n    var yValue = style[isOriginTop ? 'top' : 'bottom'];\n    var x = parseFloat(xValue);\n    var y = parseFloat(yValue);\n    // convert percent to pixels\n    var layoutSize = this.layout.size;\n    if (xValue.indexOf('%') != -1) {\n      x = x / 100 * layoutSize.width;\n    }\n    if (yValue.indexOf('%') != -1) {\n      y = y / 100 * layoutSize.height;\n    }\n    // clean up 'auto' or other non-integer values\n    x = isNaN(x) ? 0 : x;\n    y = isNaN(y) ? 0 : y;\n    // remove padding from measurement\n    x -= isOriginLeft ? layoutSize.paddingLeft : layoutSize.paddingRight;\n    y -= isOriginTop ? layoutSize.paddingTop : layoutSize.paddingBottom;\n    this.position.x = x;\n    this.position.y = y;\n  };\n\n  // set settled position, apply padding\n  proto.layoutPosition = function () {\n    var layoutSize = this.layout.size;\n    var style = {};\n    var isOriginLeft = this.layout._getOption('originLeft');\n    var isOriginTop = this.layout._getOption('originTop');\n\n    // x\n    var xPadding = isOriginLeft ? 'paddingLeft' : 'paddingRight';\n    var xProperty = isOriginLeft ? 'left' : 'right';\n    var xResetProperty = isOriginLeft ? 'right' : 'left';\n    var x = this.position.x + layoutSize[xPadding];\n    // set in percentage or pixels\n    style[xProperty] = this.getXValue(x);\n    // reset other property\n    style[xResetProperty] = '';\n\n    // y\n    var yPadding = isOriginTop ? 'paddingTop' : 'paddingBottom';\n    var yProperty = isOriginTop ? 'top' : 'bottom';\n    var yResetProperty = isOriginTop ? 'bottom' : 'top';\n    var y = this.position.y + layoutSize[yPadding];\n    // set in percentage or pixels\n    style[yProperty] = this.getYValue(y);\n    // reset other property\n    style[yResetProperty] = '';\n    this.css(style);\n    this.emitEvent('layout', [this]);\n  };\n  proto.getXValue = function (x) {\n    var isHorizontal = this.layout._getOption('horizontal');\n    return this.layout.options.percentPosition && !isHorizontal ? x / this.layout.size.width * 100 + '%' : x + 'px';\n  };\n  proto.getYValue = function (y) {\n    var isHorizontal = this.layout._getOption('horizontal');\n    return this.layout.options.percentPosition && isHorizontal ? y / this.layout.size.height * 100 + '%' : y + 'px';\n  };\n  proto._transitionTo = function (x, y) {\n    this.getPosition();\n    // get current x & y from top/left\n    var curX = this.position.x;\n    var curY = this.position.y;\n    var didNotMove = x == this.position.x && y == this.position.y;\n\n    // save end position\n    this.setPosition(x, y);\n\n    // if did not move and not transitioning, just go to layout\n    if (didNotMove && !this.isTransitioning) {\n      this.layoutPosition();\n      return;\n    }\n    var transX = x - curX;\n    var transY = y - curY;\n    var transitionStyle = {};\n    transitionStyle.transform = this.getTranslate(transX, transY);\n    this.transition({\n      to: transitionStyle,\n      onTransitionEnd: {\n        transform: this.layoutPosition\n      },\n      isCleaning: true\n    });\n  };\n  proto.getTranslate = function (x, y) {\n    // flip cooridinates if origin on right or bottom\n    var isOriginLeft = this.layout._getOption('originLeft');\n    var isOriginTop = this.layout._getOption('originTop');\n    x = isOriginLeft ? x : -x;\n    y = isOriginTop ? y : -y;\n    return 'translate3d(' + x + 'px, ' + y + 'px, 0)';\n  };\n\n  // non transition + transform support\n  proto.goTo = function (x, y) {\n    this.setPosition(x, y);\n    this.layoutPosition();\n  };\n  proto.moveTo = proto._transitionTo;\n  proto.setPosition = function (x, y) {\n    this.position.x = parseFloat(x);\n    this.position.y = parseFloat(y);\n  };\n\n  // ----- transition ----- //\n\n  /**\n   * @param {Object} style - CSS\n   * @param {Function} onTransitionEnd\n   */\n\n  // non transition, just trigger callback\n  proto._nonTransition = function (args) {\n    this.css(args.to);\n    if (args.isCleaning) {\n      this._removeStyles(args.to);\n    }\n    for (var prop in args.onTransitionEnd) {\n      args.onTransitionEnd[prop].call(this);\n    }\n  };\n\n  /**\n   * proper transition\n   * @param {Object} args - arguments\n   *   @param {Object} to - style to transition to\n   *   @param {Object} from - style to start transition from\n   *   @param {Boolean} isCleaning - removes transition styles after transition\n   *   @param {Function} onTransitionEnd - callback\n   */\n  proto.transition = function (args) {\n    // redirect to nonTransition if no transition duration\n    if (!parseFloat(this.layout.options.transitionDuration)) {\n      this._nonTransition(args);\n      return;\n    }\n    var _transition = this._transn;\n    // keep track of onTransitionEnd callback by css property\n    for (var prop in args.onTransitionEnd) {\n      _transition.onEnd[prop] = args.onTransitionEnd[prop];\n    }\n    // keep track of properties that are transitioning\n    for (prop in args.to) {\n      _transition.ingProperties[prop] = true;\n      // keep track of properties to clean up when transition is done\n      if (args.isCleaning) {\n        _transition.clean[prop] = true;\n      }\n    }\n\n    // set from styles\n    if (args.from) {\n      this.css(args.from);\n      // force redraw. http://blog.alexmaccaw.com/css-transitions\n      var h = this.element.offsetHeight;\n      // hack for JSHint to hush about unused var\n      h = null;\n    }\n    // enable transition\n    this.enableTransition(args.to);\n    // set styles that are transitioning\n    this.css(args.to);\n    this.isTransitioning = true;\n  };\n\n  // dash before all cap letters, including first for\n  // WebkitTransform => -webkit-transform\n  function toDashedAll(str) {\n    return str.replace(/([A-Z])/g, function ($1) {\n      return '-' + $1.toLowerCase();\n    });\n  }\n  var transitionProps = 'opacity,' + toDashedAll(transformProperty);\n  proto.enableTransition = function /* style */\n  () {\n    // HACK changing transitionProperty during a transition\n    // will cause transition to jump\n    if (this.isTransitioning) {\n      return;\n    }\n\n    // make `transition: foo, bar, baz` from style object\n    // HACK un-comment this when enableTransition can work\n    // while a transition is happening\n    // var transitionValues = [];\n    // for ( var prop in style ) {\n    //   // dash-ify camelCased properties like WebkitTransition\n    //   prop = vendorProperties[ prop ] || prop;\n    //   transitionValues.push( toDashedAll( prop ) );\n    // }\n    // munge number to millisecond, to match stagger\n    var duration = this.layout.options.transitionDuration;\n    duration = typeof duration == 'number' ? duration + 'ms' : duration;\n    // enable transition styles\n    this.css({\n      transitionProperty: transitionProps,\n      transitionDuration: duration,\n      transitionDelay: this.staggerDelay || 0\n    });\n    // listen for transition end event\n    this.element.addEventListener(transitionEndEvent, this, false);\n  };\n\n  // ----- events ----- //\n\n  proto.onwebkitTransitionEnd = function (event) {\n    this.ontransitionend(event);\n  };\n  proto.onotransitionend = function (event) {\n    this.ontransitionend(event);\n  };\n\n  // properties that I munge to make my life easier\n  var dashedVendorProperties = {\n    '-webkit-transform': 'transform'\n  };\n  proto.ontransitionend = function (event) {\n    // disregard bubbled events from children\n    if (event.target !== this.element) {\n      return;\n    }\n    var _transition = this._transn;\n    // get property name of transitioned property, convert to prefix-free\n    var propertyName = dashedVendorProperties[event.propertyName] || event.propertyName;\n\n    // remove property that has completed transitioning\n    delete _transition.ingProperties[propertyName];\n    // check if any properties are still transitioning\n    if (isEmptyObj(_transition.ingProperties)) {\n      // all properties have completed transitioning\n      this.disableTransition();\n    }\n    // clean style\n    if (propertyName in _transition.clean) {\n      // clean up style\n      this.element.style[event.propertyName] = '';\n      delete _transition.clean[propertyName];\n    }\n    // trigger onTransitionEnd callback\n    if (propertyName in _transition.onEnd) {\n      var onTransitionEnd = _transition.onEnd[propertyName];\n      onTransitionEnd.call(this);\n      delete _transition.onEnd[propertyName];\n    }\n    this.emitEvent('transitionEnd', [this]);\n  };\n  proto.disableTransition = function () {\n    this.removeTransitionStyles();\n    this.element.removeEventListener(transitionEndEvent, this, false);\n    this.isTransitioning = false;\n  };\n\n  /**\n   * removes style property from element\n   * @param {Object} style\n  **/\n  proto._removeStyles = function (style) {\n    // clean up transition styles\n    var cleanStyle = {};\n    for (var prop in style) {\n      cleanStyle[prop] = '';\n    }\n    this.css(cleanStyle);\n  };\n  var cleanTransitionStyle = {\n    transitionProperty: '',\n    transitionDuration: '',\n    transitionDelay: ''\n  };\n  proto.removeTransitionStyles = function () {\n    // remove transition\n    this.css(cleanTransitionStyle);\n  };\n\n  // ----- stagger ----- //\n\n  proto.stagger = function (delay) {\n    delay = isNaN(delay) ? 0 : delay;\n    this.staggerDelay = delay + 'ms';\n  };\n\n  // ----- show/hide/remove ----- //\n\n  // remove element from DOM\n  proto.removeElem = function () {\n    this.element.parentNode.removeChild(this.element);\n    // remove display: none\n    this.css({\n      display: ''\n    });\n    this.emitEvent('remove', [this]);\n  };\n  proto.remove = function () {\n    // just remove element if no transition support or no transition\n    if (!transitionProperty || !parseFloat(this.layout.options.transitionDuration)) {\n      this.removeElem();\n      return;\n    }\n\n    // start transition\n    this.once('transitionEnd', function () {\n      this.removeElem();\n    });\n    this.hide();\n  };\n  proto.reveal = function () {\n    delete this.isHidden;\n    // remove display: none\n    this.css({\n      display: ''\n    });\n    var options = this.layout.options;\n    var onTransitionEnd = {};\n    var transitionEndProperty = this.getHideRevealTransitionEndProperty('visibleStyle');\n    onTransitionEnd[transitionEndProperty] = this.onRevealTransitionEnd;\n    this.transition({\n      from: options.hiddenStyle,\n      to: options.visibleStyle,\n      isCleaning: true,\n      onTransitionEnd: onTransitionEnd\n    });\n  };\n  proto.onRevealTransitionEnd = function () {\n    // check if still visible\n    // during transition, item may have been hidden\n    if (!this.isHidden) {\n      this.emitEvent('reveal');\n    }\n  };\n\n  /**\n   * get style property use for hide/reveal transition end\n   * @param {String} styleProperty - hiddenStyle/visibleStyle\n   * @returns {String}\n   */\n  proto.getHideRevealTransitionEndProperty = function (styleProperty) {\n    var optionStyle = this.layout.options[styleProperty];\n    // use opacity\n    if (optionStyle.opacity) {\n      return 'opacity';\n    }\n    // get first property\n    for (var prop in optionStyle) {\n      return prop;\n    }\n  };\n  proto.hide = function () {\n    // set flag\n    this.isHidden = true;\n    // remove display: none\n    this.css({\n      display: ''\n    });\n    var options = this.layout.options;\n    var onTransitionEnd = {};\n    var transitionEndProperty = this.getHideRevealTransitionEndProperty('hiddenStyle');\n    onTransitionEnd[transitionEndProperty] = this.onHideTransitionEnd;\n    this.transition({\n      from: options.visibleStyle,\n      to: options.hiddenStyle,\n      // keep hidden stuff hidden\n      isCleaning: true,\n      onTransitionEnd: onTransitionEnd\n    });\n  };\n  proto.onHideTransitionEnd = function () {\n    // check if still hidden\n    // during transition, item may have been un-hidden\n    if (this.isHidden) {\n      this.css({\n        display: 'none'\n      });\n      this.emitEvent('hide');\n    }\n  };\n  proto.destroy = function () {\n    this.css({\n      position: '',\n      left: '',\n      right: '',\n      top: '',\n      bottom: '',\n      transition: '',\n      transform: ''\n    });\n  };\n  return Item;\n});", "map": {"version": 3, "names": ["window", "factory", "define", "amd", "module", "exports", "require", "Outlayer", "<PERSON><PERSON>", "EvEmitter", "getSize", "isEmptyObj", "obj", "prop", "docElemStyle", "document", "documentElement", "style", "transitionProperty", "transition", "transformProperty", "transform", "transitionEndEvent", "WebkitTransition", "vendorProperties", "transitionDuration", "transitionDelay", "element", "layout", "position", "x", "y", "_create", "proto", "prototype", "Object", "create", "constructor", "_transn", "ingProperties", "clean", "onEnd", "css", "handleEvent", "event", "method", "type", "size", "elemStyle", "supportedProp", "getPosition", "getComputedStyle", "isOriginLeft", "_getOption", "isOriginTop", "xValue", "yValue", "parseFloat", "layoutSize", "indexOf", "width", "height", "isNaN", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "layoutPosition", "xPadding", "xProperty", "xResetProperty", "getXValue", "yPadding", "yProperty", "yResetProperty", "getYValue", "emitEvent", "isHorizontal", "options", "percentPosition", "_transitionTo", "curX", "curY", "didNotMove", "setPosition", "isTransitioning", "transX", "transY", "transitionStyle", "getTranslate", "to", "onTransitionEnd", "isCleaning", "goTo", "moveTo", "_nonTransition", "args", "_removeStyles", "call", "_transition", "from", "h", "offsetHeight", "enableTransition", "toDashedAll", "str", "replace", "$1", "toLowerCase", "transitionProps", "duration", "stagger<PERSON><PERSON><PERSON>", "addEventListener", "onwebkitTransitionEnd", "ontransitionend", "onotransitionend", "dashedVendorProperties", "target", "propertyName", "disableTransition", "removeTransitionStyles", "removeEventListener", "cleanStyle", "cleanTransitionStyle", "stagger", "delay", "removeElem", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "display", "remove", "once", "hide", "reveal", "isHidden", "transitionEndProperty", "getHideRevealTransitionEndProperty", "onRevealTransitionEnd", "hiddenStyle", "visibleStyle", "styleProperty", "optionStyle", "opacity", "onHideTransitionEnd", "destroy", "left", "right", "top", "bottom"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/outlayer/item.js"], "sourcesContent": ["/**\n * Outlayer Item\n */\n\n( function( window, factory ) {\n  // universal module definition\n  /* jshint strict: false */ /* globals define, module, require */\n  if ( typeof define == 'function' && define.amd ) {\n    // AMD - RequireJS\n    define( [\n        'ev-emitter/ev-emitter',\n        'get-size/get-size'\n      ],\n      factory\n    );\n  } else if ( typeof module == 'object' && module.exports ) {\n    // CommonJS - Browserify, Webpack\n    module.exports = factory(\n      require('ev-emitter'),\n      require('get-size')\n    );\n  } else {\n    // browser global\n    window.Outlayer = {};\n    window.Outlayer.Item = factory(\n      window.EvEmitter,\n      window.getSize\n    );\n  }\n\n}( window, function factory( EvEmitter, getSize ) {\n'use strict';\n\n// ----- helpers ----- //\n\nfunction isEmptyObj( obj ) {\n  for ( var prop in obj ) {\n    return false;\n  }\n  prop = null;\n  return true;\n}\n\n// -------------------------- CSS3 support -------------------------- //\n\n\nvar docElemStyle = document.documentElement.style;\n\nvar transitionProperty = typeof docElemStyle.transition == 'string' ?\n  'transition' : 'WebkitTransition';\nvar transformProperty = typeof docElemStyle.transform == 'string' ?\n  'transform' : 'WebkitTransform';\n\nvar transitionEndEvent = {\n  WebkitTransition: 'webkitTransitionEnd',\n  transition: 'transitionend'\n}[ transitionProperty ];\n\n// cache all vendor properties that could have vendor prefix\nvar vendorProperties = {\n  transform: transformProperty,\n  transition: transitionProperty,\n  transitionDuration: transitionProperty + 'Duration',\n  transitionProperty: transitionProperty + 'Property',\n  transitionDelay: transitionProperty + 'Delay'\n};\n\n// -------------------------- Item -------------------------- //\n\nfunction Item( element, layout ) {\n  if ( !element ) {\n    return;\n  }\n\n  this.element = element;\n  // parent layout class, i.e. Masonry, Isotope, or Packery\n  this.layout = layout;\n  this.position = {\n    x: 0,\n    y: 0\n  };\n\n  this._create();\n}\n\n// inherit EvEmitter\nvar proto = Item.prototype = Object.create( EvEmitter.prototype );\nproto.constructor = Item;\n\nproto._create = function() {\n  // transition objects\n  this._transn = {\n    ingProperties: {},\n    clean: {},\n    onEnd: {}\n  };\n\n  this.css({\n    position: 'absolute'\n  });\n};\n\n// trigger specified handler for event type\nproto.handleEvent = function( event ) {\n  var method = 'on' + event.type;\n  if ( this[ method ] ) {\n    this[ method ]( event );\n  }\n};\n\nproto.getSize = function() {\n  this.size = getSize( this.element );\n};\n\n/**\n * apply CSS styles to element\n * @param {Object} style\n */\nproto.css = function( style ) {\n  var elemStyle = this.element.style;\n\n  for ( var prop in style ) {\n    // use vendor property if available\n    var supportedProp = vendorProperties[ prop ] || prop;\n    elemStyle[ supportedProp ] = style[ prop ];\n  }\n};\n\n // measure position, and sets it\nproto.getPosition = function() {\n  var style = getComputedStyle( this.element );\n  var isOriginLeft = this.layout._getOption('originLeft');\n  var isOriginTop = this.layout._getOption('originTop');\n  var xValue = style[ isOriginLeft ? 'left' : 'right' ];\n  var yValue = style[ isOriginTop ? 'top' : 'bottom' ];\n  var x = parseFloat( xValue );\n  var y = parseFloat( yValue );\n  // convert percent to pixels\n  var layoutSize = this.layout.size;\n  if ( xValue.indexOf('%') != -1 ) {\n    x = ( x / 100 ) * layoutSize.width;\n  }\n  if ( yValue.indexOf('%') != -1 ) {\n    y = ( y / 100 ) * layoutSize.height;\n  }\n  // clean up 'auto' or other non-integer values\n  x = isNaN( x ) ? 0 : x;\n  y = isNaN( y ) ? 0 : y;\n  // remove padding from measurement\n  x -= isOriginLeft ? layoutSize.paddingLeft : layoutSize.paddingRight;\n  y -= isOriginTop ? layoutSize.paddingTop : layoutSize.paddingBottom;\n\n  this.position.x = x;\n  this.position.y = y;\n};\n\n// set settled position, apply padding\nproto.layoutPosition = function() {\n  var layoutSize = this.layout.size;\n  var style = {};\n  var isOriginLeft = this.layout._getOption('originLeft');\n  var isOriginTop = this.layout._getOption('originTop');\n\n  // x\n  var xPadding = isOriginLeft ? 'paddingLeft' : 'paddingRight';\n  var xProperty = isOriginLeft ? 'left' : 'right';\n  var xResetProperty = isOriginLeft ? 'right' : 'left';\n\n  var x = this.position.x + layoutSize[ xPadding ];\n  // set in percentage or pixels\n  style[ xProperty ] = this.getXValue( x );\n  // reset other property\n  style[ xResetProperty ] = '';\n\n  // y\n  var yPadding = isOriginTop ? 'paddingTop' : 'paddingBottom';\n  var yProperty = isOriginTop ? 'top' : 'bottom';\n  var yResetProperty = isOriginTop ? 'bottom' : 'top';\n\n  var y = this.position.y + layoutSize[ yPadding ];\n  // set in percentage or pixels\n  style[ yProperty ] = this.getYValue( y );\n  // reset other property\n  style[ yResetProperty ] = '';\n\n  this.css( style );\n  this.emitEvent( 'layout', [ this ] );\n};\n\nproto.getXValue = function( x ) {\n  var isHorizontal = this.layout._getOption('horizontal');\n  return this.layout.options.percentPosition && !isHorizontal ?\n    ( ( x / this.layout.size.width ) * 100 ) + '%' : x + 'px';\n};\n\nproto.getYValue = function( y ) {\n  var isHorizontal = this.layout._getOption('horizontal');\n  return this.layout.options.percentPosition && isHorizontal ?\n    ( ( y / this.layout.size.height ) * 100 ) + '%' : y + 'px';\n};\n\nproto._transitionTo = function( x, y ) {\n  this.getPosition();\n  // get current x & y from top/left\n  var curX = this.position.x;\n  var curY = this.position.y;\n\n  var didNotMove = x == this.position.x && y == this.position.y;\n\n  // save end position\n  this.setPosition( x, y );\n\n  // if did not move and not transitioning, just go to layout\n  if ( didNotMove && !this.isTransitioning ) {\n    this.layoutPosition();\n    return;\n  }\n\n  var transX = x - curX;\n  var transY = y - curY;\n  var transitionStyle = {};\n  transitionStyle.transform = this.getTranslate( transX, transY );\n\n  this.transition({\n    to: transitionStyle,\n    onTransitionEnd: {\n      transform: this.layoutPosition\n    },\n    isCleaning: true\n  });\n};\n\nproto.getTranslate = function( x, y ) {\n  // flip cooridinates if origin on right or bottom\n  var isOriginLeft = this.layout._getOption('originLeft');\n  var isOriginTop = this.layout._getOption('originTop');\n  x = isOriginLeft ? x : -x;\n  y = isOriginTop ? y : -y;\n  return 'translate3d(' + x + 'px, ' + y + 'px, 0)';\n};\n\n// non transition + transform support\nproto.goTo = function( x, y ) {\n  this.setPosition( x, y );\n  this.layoutPosition();\n};\n\nproto.moveTo = proto._transitionTo;\n\nproto.setPosition = function( x, y ) {\n  this.position.x = parseFloat( x );\n  this.position.y = parseFloat( y );\n};\n\n// ----- transition ----- //\n\n/**\n * @param {Object} style - CSS\n * @param {Function} onTransitionEnd\n */\n\n// non transition, just trigger callback\nproto._nonTransition = function( args ) {\n  this.css( args.to );\n  if ( args.isCleaning ) {\n    this._removeStyles( args.to );\n  }\n  for ( var prop in args.onTransitionEnd ) {\n    args.onTransitionEnd[ prop ].call( this );\n  }\n};\n\n/**\n * proper transition\n * @param {Object} args - arguments\n *   @param {Object} to - style to transition to\n *   @param {Object} from - style to start transition from\n *   @param {Boolean} isCleaning - removes transition styles after transition\n *   @param {Function} onTransitionEnd - callback\n */\nproto.transition = function( args ) {\n  // redirect to nonTransition if no transition duration\n  if ( !parseFloat( this.layout.options.transitionDuration ) ) {\n    this._nonTransition( args );\n    return;\n  }\n\n  var _transition = this._transn;\n  // keep track of onTransitionEnd callback by css property\n  for ( var prop in args.onTransitionEnd ) {\n    _transition.onEnd[ prop ] = args.onTransitionEnd[ prop ];\n  }\n  // keep track of properties that are transitioning\n  for ( prop in args.to ) {\n    _transition.ingProperties[ prop ] = true;\n    // keep track of properties to clean up when transition is done\n    if ( args.isCleaning ) {\n      _transition.clean[ prop ] = true;\n    }\n  }\n\n  // set from styles\n  if ( args.from ) {\n    this.css( args.from );\n    // force redraw. http://blog.alexmaccaw.com/css-transitions\n    var h = this.element.offsetHeight;\n    // hack for JSHint to hush about unused var\n    h = null;\n  }\n  // enable transition\n  this.enableTransition( args.to );\n  // set styles that are transitioning\n  this.css( args.to );\n\n  this.isTransitioning = true;\n\n};\n\n// dash before all cap letters, including first for\n// WebkitTransform => -webkit-transform\nfunction toDashedAll( str ) {\n  return str.replace( /([A-Z])/g, function( $1 ) {\n    return '-' + $1.toLowerCase();\n  });\n}\n\nvar transitionProps = 'opacity,' + toDashedAll( transformProperty );\n\nproto.enableTransition = function(/* style */) {\n  // HACK changing transitionProperty during a transition\n  // will cause transition to jump\n  if ( this.isTransitioning ) {\n    return;\n  }\n\n  // make `transition: foo, bar, baz` from style object\n  // HACK un-comment this when enableTransition can work\n  // while a transition is happening\n  // var transitionValues = [];\n  // for ( var prop in style ) {\n  //   // dash-ify camelCased properties like WebkitTransition\n  //   prop = vendorProperties[ prop ] || prop;\n  //   transitionValues.push( toDashedAll( prop ) );\n  // }\n  // munge number to millisecond, to match stagger\n  var duration = this.layout.options.transitionDuration;\n  duration = typeof duration == 'number' ? duration + 'ms' : duration;\n  // enable transition styles\n  this.css({\n    transitionProperty: transitionProps,\n    transitionDuration: duration,\n    transitionDelay: this.staggerDelay || 0\n  });\n  // listen for transition end event\n  this.element.addEventListener( transitionEndEvent, this, false );\n};\n\n// ----- events ----- //\n\nproto.onwebkitTransitionEnd = function( event ) {\n  this.ontransitionend( event );\n};\n\nproto.onotransitionend = function( event ) {\n  this.ontransitionend( event );\n};\n\n// properties that I munge to make my life easier\nvar dashedVendorProperties = {\n  '-webkit-transform': 'transform'\n};\n\nproto.ontransitionend = function( event ) {\n  // disregard bubbled events from children\n  if ( event.target !== this.element ) {\n    return;\n  }\n  var _transition = this._transn;\n  // get property name of transitioned property, convert to prefix-free\n  var propertyName = dashedVendorProperties[ event.propertyName ] || event.propertyName;\n\n  // remove property that has completed transitioning\n  delete _transition.ingProperties[ propertyName ];\n  // check if any properties are still transitioning\n  if ( isEmptyObj( _transition.ingProperties ) ) {\n    // all properties have completed transitioning\n    this.disableTransition();\n  }\n  // clean style\n  if ( propertyName in _transition.clean ) {\n    // clean up style\n    this.element.style[ event.propertyName ] = '';\n    delete _transition.clean[ propertyName ];\n  }\n  // trigger onTransitionEnd callback\n  if ( propertyName in _transition.onEnd ) {\n    var onTransitionEnd = _transition.onEnd[ propertyName ];\n    onTransitionEnd.call( this );\n    delete _transition.onEnd[ propertyName ];\n  }\n\n  this.emitEvent( 'transitionEnd', [ this ] );\n};\n\nproto.disableTransition = function() {\n  this.removeTransitionStyles();\n  this.element.removeEventListener( transitionEndEvent, this, false );\n  this.isTransitioning = false;\n};\n\n/**\n * removes style property from element\n * @param {Object} style\n**/\nproto._removeStyles = function( style ) {\n  // clean up transition styles\n  var cleanStyle = {};\n  for ( var prop in style ) {\n    cleanStyle[ prop ] = '';\n  }\n  this.css( cleanStyle );\n};\n\nvar cleanTransitionStyle = {\n  transitionProperty: '',\n  transitionDuration: '',\n  transitionDelay: ''\n};\n\nproto.removeTransitionStyles = function() {\n  // remove transition\n  this.css( cleanTransitionStyle );\n};\n\n// ----- stagger ----- //\n\nproto.stagger = function( delay ) {\n  delay = isNaN( delay ) ? 0 : delay;\n  this.staggerDelay = delay + 'ms';\n};\n\n// ----- show/hide/remove ----- //\n\n// remove element from DOM\nproto.removeElem = function() {\n  this.element.parentNode.removeChild( this.element );\n  // remove display: none\n  this.css({ display: '' });\n  this.emitEvent( 'remove', [ this ] );\n};\n\nproto.remove = function() {\n  // just remove element if no transition support or no transition\n  if ( !transitionProperty || !parseFloat( this.layout.options.transitionDuration ) ) {\n    this.removeElem();\n    return;\n  }\n\n  // start transition\n  this.once( 'transitionEnd', function() {\n    this.removeElem();\n  });\n  this.hide();\n};\n\nproto.reveal = function() {\n  delete this.isHidden;\n  // remove display: none\n  this.css({ display: '' });\n\n  var options = this.layout.options;\n\n  var onTransitionEnd = {};\n  var transitionEndProperty = this.getHideRevealTransitionEndProperty('visibleStyle');\n  onTransitionEnd[ transitionEndProperty ] = this.onRevealTransitionEnd;\n\n  this.transition({\n    from: options.hiddenStyle,\n    to: options.visibleStyle,\n    isCleaning: true,\n    onTransitionEnd: onTransitionEnd\n  });\n};\n\nproto.onRevealTransitionEnd = function() {\n  // check if still visible\n  // during transition, item may have been hidden\n  if ( !this.isHidden ) {\n    this.emitEvent('reveal');\n  }\n};\n\n/**\n * get style property use for hide/reveal transition end\n * @param {String} styleProperty - hiddenStyle/visibleStyle\n * @returns {String}\n */\nproto.getHideRevealTransitionEndProperty = function( styleProperty ) {\n  var optionStyle = this.layout.options[ styleProperty ];\n  // use opacity\n  if ( optionStyle.opacity ) {\n    return 'opacity';\n  }\n  // get first property\n  for ( var prop in optionStyle ) {\n    return prop;\n  }\n};\n\nproto.hide = function() {\n  // set flag\n  this.isHidden = true;\n  // remove display: none\n  this.css({ display: '' });\n\n  var options = this.layout.options;\n\n  var onTransitionEnd = {};\n  var transitionEndProperty = this.getHideRevealTransitionEndProperty('hiddenStyle');\n  onTransitionEnd[ transitionEndProperty ] = this.onHideTransitionEnd;\n\n  this.transition({\n    from: options.visibleStyle,\n    to: options.hiddenStyle,\n    // keep hidden stuff hidden\n    isCleaning: true,\n    onTransitionEnd: onTransitionEnd\n  });\n};\n\nproto.onHideTransitionEnd = function() {\n  // check if still hidden\n  // during transition, item may have been un-hidden\n  if ( this.isHidden ) {\n    this.css({ display: 'none' });\n    this.emitEvent('hide');\n  }\n};\n\nproto.destroy = function() {\n  this.css({\n    position: '',\n    left: '',\n    right: '',\n    top: '',\n    bottom: '',\n    transition: '',\n    transform: ''\n  });\n};\n\nreturn Item;\n\n}));\n"], "mappings": "AAAA;AACA;AACA;;AAEE,WAAUA,MAAM,EAAEC,OAAO,EAAG;EAC5B;EACA,2BAA2B;EAC3B,IAAK,OAAOC,MAAM,IAAI,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAG;IAC/C;IACAD,MAAM,CAAE,CACJ,uBAAuB,EACvB,mBAAmB,CACpB,EACDD,OACF,CAAC;EACH,CAAC,MAAM,IAAK,OAAOG,MAAM,IAAI,QAAQ,IAAIA,MAAM,CAACC,OAAO,EAAG;IACxD;IACAD,MAAM,CAACC,OAAO,GAAGJ,OAAO,CACtBK,OAAO,CAAC,YAAY,CAAC,EACrBA,OAAO,CAAC,UAAU,CACpB,CAAC;EACH,CAAC,MAAM;IACL;IACAN,MAAM,CAACO,QAAQ,GAAG,CAAC,CAAC;IACpBP,MAAM,CAACO,QAAQ,CAACC,IAAI,GAAGP,OAAO,CAC5BD,MAAM,CAACS,SAAS,EAChBT,MAAM,CAACU,OACT,CAAC;EACH;AAEF,CAAC,EAAEV,MAAM,EAAE,SAASC,OAAOA,CAAEQ,SAAS,EAAEC,OAAO,EAAG;EAClD,YAAY;;EAEZ;EAEA,SAASC,UAAUA,CAAEC,GAAG,EAAG;IACzB,KAAM,IAAIC,IAAI,IAAID,GAAG,EAAG;MACtB,OAAO,KAAK;IACd;IACAC,IAAI,GAAG,IAAI;IACX,OAAO,IAAI;EACb;;EAEA;;EAGA,IAAIC,YAAY,GAAGC,QAAQ,CAACC,eAAe,CAACC,KAAK;EAEjD,IAAIC,kBAAkB,GAAG,OAAOJ,YAAY,CAACK,UAAU,IAAI,QAAQ,GACjE,YAAY,GAAG,kBAAkB;EACnC,IAAIC,iBAAiB,GAAG,OAAON,YAAY,CAACO,SAAS,IAAI,QAAQ,GAC/D,WAAW,GAAG,iBAAiB;EAEjC,IAAIC,kBAAkB,GAAG;IACvBC,gBAAgB,EAAE,qBAAqB;IACvCJ,UAAU,EAAE;EACd,CAAC,CAAED,kBAAkB,CAAE;;EAEvB;EACA,IAAIM,gBAAgB,GAAG;IACrBH,SAAS,EAAED,iBAAiB;IAC5BD,UAAU,EAAED,kBAAkB;IAC9BO,kBAAkB,EAAEP,kBAAkB,GAAG,UAAU;IACnDA,kBAAkB,EAAEA,kBAAkB,GAAG,UAAU;IACnDQ,eAAe,EAAER,kBAAkB,GAAG;EACxC,CAAC;;EAED;;EAEA,SAASV,IAAIA,CAAEmB,OAAO,EAAEC,MAAM,EAAG;IAC/B,IAAK,CAACD,OAAO,EAAG;MACd;IACF;IAEA,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAG;MACdC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;IAED,IAAI,CAACC,OAAO,CAAC,CAAC;EAChB;;EAEA;EACA,IAAIC,KAAK,GAAGzB,IAAI,CAAC0B,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAE3B,SAAS,CAACyB,SAAU,CAAC;EACjED,KAAK,CAACI,WAAW,GAAG7B,IAAI;EAExByB,KAAK,CAACD,OAAO,GAAG,YAAW;IACzB;IACA,IAAI,CAACM,OAAO,GAAG;MACbC,aAAa,EAAE,CAAC,CAAC;MACjBC,KAAK,EAAE,CAAC,CAAC;MACTC,KAAK,EAAE,CAAC;IACV,CAAC;IAED,IAAI,CAACC,GAAG,CAAC;MACPb,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACAI,KAAK,CAACU,WAAW,GAAG,UAAUC,KAAK,EAAG;IACpC,IAAIC,MAAM,GAAG,IAAI,GAAGD,KAAK,CAACE,IAAI;IAC9B,IAAK,IAAI,CAAED,MAAM,CAAE,EAAG;MACpB,IAAI,CAAEA,MAAM,CAAE,CAAED,KAAM,CAAC;IACzB;EACF,CAAC;EAEDX,KAAK,CAACvB,OAAO,GAAG,YAAW;IACzB,IAAI,CAACqC,IAAI,GAAGrC,OAAO,CAAE,IAAI,CAACiB,OAAQ,CAAC;EACrC,CAAC;;EAED;AACA;AACA;AACA;EACAM,KAAK,CAACS,GAAG,GAAG,UAAUzB,KAAK,EAAG;IAC5B,IAAI+B,SAAS,GAAG,IAAI,CAACrB,OAAO,CAACV,KAAK;IAElC,KAAM,IAAIJ,IAAI,IAAII,KAAK,EAAG;MACxB;MACA,IAAIgC,aAAa,GAAGzB,gBAAgB,CAAEX,IAAI,CAAE,IAAIA,IAAI;MACpDmC,SAAS,CAAEC,aAAa,CAAE,GAAGhC,KAAK,CAAEJ,IAAI,CAAE;IAC5C;EACF,CAAC;;EAEA;EACDoB,KAAK,CAACiB,WAAW,GAAG,YAAW;IAC7B,IAAIjC,KAAK,GAAGkC,gBAAgB,CAAE,IAAI,CAACxB,OAAQ,CAAC;IAC5C,IAAIyB,YAAY,GAAG,IAAI,CAACxB,MAAM,CAACyB,UAAU,CAAC,YAAY,CAAC;IACvD,IAAIC,WAAW,GAAG,IAAI,CAAC1B,MAAM,CAACyB,UAAU,CAAC,WAAW,CAAC;IACrD,IAAIE,MAAM,GAAGtC,KAAK,CAAEmC,YAAY,GAAG,MAAM,GAAG,OAAO,CAAE;IACrD,IAAII,MAAM,GAAGvC,KAAK,CAAEqC,WAAW,GAAG,KAAK,GAAG,QAAQ,CAAE;IACpD,IAAIxB,CAAC,GAAG2B,UAAU,CAAEF,MAAO,CAAC;IAC5B,IAAIxB,CAAC,GAAG0B,UAAU,CAAED,MAAO,CAAC;IAC5B;IACA,IAAIE,UAAU,GAAG,IAAI,CAAC9B,MAAM,CAACmB,IAAI;IACjC,IAAKQ,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAG;MAC/B7B,CAAC,GAAKA,CAAC,GAAG,GAAG,GAAK4B,UAAU,CAACE,KAAK;IACpC;IACA,IAAKJ,MAAM,CAACG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAG;MAC/B5B,CAAC,GAAKA,CAAC,GAAG,GAAG,GAAK2B,UAAU,CAACG,MAAM;IACrC;IACA;IACA/B,CAAC,GAAGgC,KAAK,CAAEhC,CAAE,CAAC,GAAG,CAAC,GAAGA,CAAC;IACtBC,CAAC,GAAG+B,KAAK,CAAE/B,CAAE,CAAC,GAAG,CAAC,GAAGA,CAAC;IACtB;IACAD,CAAC,IAAIsB,YAAY,GAAGM,UAAU,CAACK,WAAW,GAAGL,UAAU,CAACM,YAAY;IACpEjC,CAAC,IAAIuB,WAAW,GAAGI,UAAU,CAACO,UAAU,GAAGP,UAAU,CAACQ,aAAa;IAEnE,IAAI,CAACrC,QAAQ,CAACC,CAAC,GAAGA,CAAC;IACnB,IAAI,CAACD,QAAQ,CAACE,CAAC,GAAGA,CAAC;EACrB,CAAC;;EAED;EACAE,KAAK,CAACkC,cAAc,GAAG,YAAW;IAChC,IAAIT,UAAU,GAAG,IAAI,CAAC9B,MAAM,CAACmB,IAAI;IACjC,IAAI9B,KAAK,GAAG,CAAC,CAAC;IACd,IAAImC,YAAY,GAAG,IAAI,CAACxB,MAAM,CAACyB,UAAU,CAAC,YAAY,CAAC;IACvD,IAAIC,WAAW,GAAG,IAAI,CAAC1B,MAAM,CAACyB,UAAU,CAAC,WAAW,CAAC;;IAErD;IACA,IAAIe,QAAQ,GAAGhB,YAAY,GAAG,aAAa,GAAG,cAAc;IAC5D,IAAIiB,SAAS,GAAGjB,YAAY,GAAG,MAAM,GAAG,OAAO;IAC/C,IAAIkB,cAAc,GAAGlB,YAAY,GAAG,OAAO,GAAG,MAAM;IAEpD,IAAItB,CAAC,GAAG,IAAI,CAACD,QAAQ,CAACC,CAAC,GAAG4B,UAAU,CAAEU,QAAQ,CAAE;IAChD;IACAnD,KAAK,CAAEoD,SAAS,CAAE,GAAG,IAAI,CAACE,SAAS,CAAEzC,CAAE,CAAC;IACxC;IACAb,KAAK,CAAEqD,cAAc,CAAE,GAAG,EAAE;;IAE5B;IACA,IAAIE,QAAQ,GAAGlB,WAAW,GAAG,YAAY,GAAG,eAAe;IAC3D,IAAImB,SAAS,GAAGnB,WAAW,GAAG,KAAK,GAAG,QAAQ;IAC9C,IAAIoB,cAAc,GAAGpB,WAAW,GAAG,QAAQ,GAAG,KAAK;IAEnD,IAAIvB,CAAC,GAAG,IAAI,CAACF,QAAQ,CAACE,CAAC,GAAG2B,UAAU,CAAEc,QAAQ,CAAE;IAChD;IACAvD,KAAK,CAAEwD,SAAS,CAAE,GAAG,IAAI,CAACE,SAAS,CAAE5C,CAAE,CAAC;IACxC;IACAd,KAAK,CAAEyD,cAAc,CAAE,GAAG,EAAE;IAE5B,IAAI,CAAChC,GAAG,CAAEzB,KAAM,CAAC;IACjB,IAAI,CAAC2D,SAAS,CAAE,QAAQ,EAAE,CAAE,IAAI,CAAG,CAAC;EACtC,CAAC;EAED3C,KAAK,CAACsC,SAAS,GAAG,UAAUzC,CAAC,EAAG;IAC9B,IAAI+C,YAAY,GAAG,IAAI,CAACjD,MAAM,CAACyB,UAAU,CAAC,YAAY,CAAC;IACvD,OAAO,IAAI,CAACzB,MAAM,CAACkD,OAAO,CAACC,eAAe,IAAI,CAACF,YAAY,GACrD/C,CAAC,GAAG,IAAI,CAACF,MAAM,CAACmB,IAAI,CAACa,KAAK,GAAK,GAAG,GAAK,GAAG,GAAG9B,CAAC,GAAG,IAAI;EAC7D,CAAC;EAEDG,KAAK,CAAC0C,SAAS,GAAG,UAAU5C,CAAC,EAAG;IAC9B,IAAI8C,YAAY,GAAG,IAAI,CAACjD,MAAM,CAACyB,UAAU,CAAC,YAAY,CAAC;IACvD,OAAO,IAAI,CAACzB,MAAM,CAACkD,OAAO,CAACC,eAAe,IAAIF,YAAY,GACpD9C,CAAC,GAAG,IAAI,CAACH,MAAM,CAACmB,IAAI,CAACc,MAAM,GAAK,GAAG,GAAK,GAAG,GAAG9B,CAAC,GAAG,IAAI;EAC9D,CAAC;EAEDE,KAAK,CAAC+C,aAAa,GAAG,UAAUlD,CAAC,EAAEC,CAAC,EAAG;IACrC,IAAI,CAACmB,WAAW,CAAC,CAAC;IAClB;IACA,IAAI+B,IAAI,GAAG,IAAI,CAACpD,QAAQ,CAACC,CAAC;IAC1B,IAAIoD,IAAI,GAAG,IAAI,CAACrD,QAAQ,CAACE,CAAC;IAE1B,IAAIoD,UAAU,GAAGrD,CAAC,IAAI,IAAI,CAACD,QAAQ,CAACC,CAAC,IAAIC,CAAC,IAAI,IAAI,CAACF,QAAQ,CAACE,CAAC;;IAE7D;IACA,IAAI,CAACqD,WAAW,CAAEtD,CAAC,EAAEC,CAAE,CAAC;;IAExB;IACA,IAAKoD,UAAU,IAAI,CAAC,IAAI,CAACE,eAAe,EAAG;MACzC,IAAI,CAAClB,cAAc,CAAC,CAAC;MACrB;IACF;IAEA,IAAImB,MAAM,GAAGxD,CAAC,GAAGmD,IAAI;IACrB,IAAIM,MAAM,GAAGxD,CAAC,GAAGmD,IAAI;IACrB,IAAIM,eAAe,GAAG,CAAC,CAAC;IACxBA,eAAe,CAACnE,SAAS,GAAG,IAAI,CAACoE,YAAY,CAAEH,MAAM,EAAEC,MAAO,CAAC;IAE/D,IAAI,CAACpE,UAAU,CAAC;MACduE,EAAE,EAAEF,eAAe;MACnBG,eAAe,EAAE;QACftE,SAAS,EAAE,IAAI,CAAC8C;MAClB,CAAC;MACDyB,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC;EAED3D,KAAK,CAACwD,YAAY,GAAG,UAAU3D,CAAC,EAAEC,CAAC,EAAG;IACpC;IACA,IAAIqB,YAAY,GAAG,IAAI,CAACxB,MAAM,CAACyB,UAAU,CAAC,YAAY,CAAC;IACvD,IAAIC,WAAW,GAAG,IAAI,CAAC1B,MAAM,CAACyB,UAAU,CAAC,WAAW,CAAC;IACrDvB,CAAC,GAAGsB,YAAY,GAAGtB,CAAC,GAAG,CAACA,CAAC;IACzBC,CAAC,GAAGuB,WAAW,GAAGvB,CAAC,GAAG,CAACA,CAAC;IACxB,OAAO,cAAc,GAAGD,CAAC,GAAG,MAAM,GAAGC,CAAC,GAAG,QAAQ;EACnD,CAAC;;EAED;EACAE,KAAK,CAAC4D,IAAI,GAAG,UAAU/D,CAAC,EAAEC,CAAC,EAAG;IAC5B,IAAI,CAACqD,WAAW,CAAEtD,CAAC,EAAEC,CAAE,CAAC;IACxB,IAAI,CAACoC,cAAc,CAAC,CAAC;EACvB,CAAC;EAEDlC,KAAK,CAAC6D,MAAM,GAAG7D,KAAK,CAAC+C,aAAa;EAElC/C,KAAK,CAACmD,WAAW,GAAG,UAAUtD,CAAC,EAAEC,CAAC,EAAG;IACnC,IAAI,CAACF,QAAQ,CAACC,CAAC,GAAG2B,UAAU,CAAE3B,CAAE,CAAC;IACjC,IAAI,CAACD,QAAQ,CAACE,CAAC,GAAG0B,UAAU,CAAE1B,CAAE,CAAC;EACnC,CAAC;;EAED;;EAEA;AACA;AACA;AACA;;EAEA;EACAE,KAAK,CAAC8D,cAAc,GAAG,UAAUC,IAAI,EAAG;IACtC,IAAI,CAACtD,GAAG,CAAEsD,IAAI,CAACN,EAAG,CAAC;IACnB,IAAKM,IAAI,CAACJ,UAAU,EAAG;MACrB,IAAI,CAACK,aAAa,CAAED,IAAI,CAACN,EAAG,CAAC;IAC/B;IACA,KAAM,IAAI7E,IAAI,IAAImF,IAAI,CAACL,eAAe,EAAG;MACvCK,IAAI,CAACL,eAAe,CAAE9E,IAAI,CAAE,CAACqF,IAAI,CAAE,IAAK,CAAC;IAC3C;EACF,CAAC;;EAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACAjE,KAAK,CAACd,UAAU,GAAG,UAAU6E,IAAI,EAAG;IAClC;IACA,IAAK,CAACvC,UAAU,CAAE,IAAI,CAAC7B,MAAM,CAACkD,OAAO,CAACrD,kBAAmB,CAAC,EAAG;MAC3D,IAAI,CAACsE,cAAc,CAAEC,IAAK,CAAC;MAC3B;IACF;IAEA,IAAIG,WAAW,GAAG,IAAI,CAAC7D,OAAO;IAC9B;IACA,KAAM,IAAIzB,IAAI,IAAImF,IAAI,CAACL,eAAe,EAAG;MACvCQ,WAAW,CAAC1D,KAAK,CAAE5B,IAAI,CAAE,GAAGmF,IAAI,CAACL,eAAe,CAAE9E,IAAI,CAAE;IAC1D;IACA;IACA,KAAMA,IAAI,IAAImF,IAAI,CAACN,EAAE,EAAG;MACtBS,WAAW,CAAC5D,aAAa,CAAE1B,IAAI,CAAE,GAAG,IAAI;MACxC;MACA,IAAKmF,IAAI,CAACJ,UAAU,EAAG;QACrBO,WAAW,CAAC3D,KAAK,CAAE3B,IAAI,CAAE,GAAG,IAAI;MAClC;IACF;;IAEA;IACA,IAAKmF,IAAI,CAACI,IAAI,EAAG;MACf,IAAI,CAAC1D,GAAG,CAAEsD,IAAI,CAACI,IAAK,CAAC;MACrB;MACA,IAAIC,CAAC,GAAG,IAAI,CAAC1E,OAAO,CAAC2E,YAAY;MACjC;MACAD,CAAC,GAAG,IAAI;IACV;IACA;IACA,IAAI,CAACE,gBAAgB,CAAEP,IAAI,CAACN,EAAG,CAAC;IAChC;IACA,IAAI,CAAChD,GAAG,CAAEsD,IAAI,CAACN,EAAG,CAAC;IAEnB,IAAI,CAACL,eAAe,GAAG,IAAI;EAE7B,CAAC;;EAED;EACA;EACA,SAASmB,WAAWA,CAAEC,GAAG,EAAG;IAC1B,OAAOA,GAAG,CAACC,OAAO,CAAE,UAAU,EAAE,UAAUC,EAAE,EAAG;MAC7C,OAAO,GAAG,GAAGA,EAAE,CAACC,WAAW,CAAC,CAAC;IAC/B,CAAC,CAAC;EACJ;EAEA,IAAIC,eAAe,GAAG,UAAU,GAAGL,WAAW,CAAEpF,iBAAkB,CAAC;EAEnEa,KAAK,CAACsE,gBAAgB,GAAG,SAAS;EAAA,GAAa;IAC7C;IACA;IACA,IAAK,IAAI,CAAClB,eAAe,EAAG;MAC1B;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIyB,QAAQ,GAAG,IAAI,CAAClF,MAAM,CAACkD,OAAO,CAACrD,kBAAkB;IACrDqF,QAAQ,GAAG,OAAOA,QAAQ,IAAI,QAAQ,GAAGA,QAAQ,GAAG,IAAI,GAAGA,QAAQ;IACnE;IACA,IAAI,CAACpE,GAAG,CAAC;MACPxB,kBAAkB,EAAE2F,eAAe;MACnCpF,kBAAkB,EAAEqF,QAAQ;MAC5BpF,eAAe,EAAE,IAAI,CAACqF,YAAY,IAAI;IACxC,CAAC,CAAC;IACF;IACA,IAAI,CAACpF,OAAO,CAACqF,gBAAgB,CAAE1F,kBAAkB,EAAE,IAAI,EAAE,KAAM,CAAC;EAClE,CAAC;;EAED;;EAEAW,KAAK,CAACgF,qBAAqB,GAAG,UAAUrE,KAAK,EAAG;IAC9C,IAAI,CAACsE,eAAe,CAAEtE,KAAM,CAAC;EAC/B,CAAC;EAEDX,KAAK,CAACkF,gBAAgB,GAAG,UAAUvE,KAAK,EAAG;IACzC,IAAI,CAACsE,eAAe,CAAEtE,KAAM,CAAC;EAC/B,CAAC;;EAED;EACA,IAAIwE,sBAAsB,GAAG;IAC3B,mBAAmB,EAAE;EACvB,CAAC;EAEDnF,KAAK,CAACiF,eAAe,GAAG,UAAUtE,KAAK,EAAG;IACxC;IACA,IAAKA,KAAK,CAACyE,MAAM,KAAK,IAAI,CAAC1F,OAAO,EAAG;MACnC;IACF;IACA,IAAIwE,WAAW,GAAG,IAAI,CAAC7D,OAAO;IAC9B;IACA,IAAIgF,YAAY,GAAGF,sBAAsB,CAAExE,KAAK,CAAC0E,YAAY,CAAE,IAAI1E,KAAK,CAAC0E,YAAY;;IAErF;IACA,OAAOnB,WAAW,CAAC5D,aAAa,CAAE+E,YAAY,CAAE;IAChD;IACA,IAAK3G,UAAU,CAAEwF,WAAW,CAAC5D,aAAc,CAAC,EAAG;MAC7C;MACA,IAAI,CAACgF,iBAAiB,CAAC,CAAC;IAC1B;IACA;IACA,IAAKD,YAAY,IAAInB,WAAW,CAAC3D,KAAK,EAAG;MACvC;MACA,IAAI,CAACb,OAAO,CAACV,KAAK,CAAE2B,KAAK,CAAC0E,YAAY,CAAE,GAAG,EAAE;MAC7C,OAAOnB,WAAW,CAAC3D,KAAK,CAAE8E,YAAY,CAAE;IAC1C;IACA;IACA,IAAKA,YAAY,IAAInB,WAAW,CAAC1D,KAAK,EAAG;MACvC,IAAIkD,eAAe,GAAGQ,WAAW,CAAC1D,KAAK,CAAE6E,YAAY,CAAE;MACvD3B,eAAe,CAACO,IAAI,CAAE,IAAK,CAAC;MAC5B,OAAOC,WAAW,CAAC1D,KAAK,CAAE6E,YAAY,CAAE;IAC1C;IAEA,IAAI,CAAC1C,SAAS,CAAE,eAAe,EAAE,CAAE,IAAI,CAAG,CAAC;EAC7C,CAAC;EAED3C,KAAK,CAACsF,iBAAiB,GAAG,YAAW;IACnC,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC7F,OAAO,CAAC8F,mBAAmB,CAAEnG,kBAAkB,EAAE,IAAI,EAAE,KAAM,CAAC;IACnE,IAAI,CAAC+D,eAAe,GAAG,KAAK;EAC9B,CAAC;;EAED;AACA;AACA;AACA;EACApD,KAAK,CAACgE,aAAa,GAAG,UAAUhF,KAAK,EAAG;IACtC;IACA,IAAIyG,UAAU,GAAG,CAAC,CAAC;IACnB,KAAM,IAAI7G,IAAI,IAAII,KAAK,EAAG;MACxByG,UAAU,CAAE7G,IAAI,CAAE,GAAG,EAAE;IACzB;IACA,IAAI,CAAC6B,GAAG,CAAEgF,UAAW,CAAC;EACxB,CAAC;EAED,IAAIC,oBAAoB,GAAG;IACzBzG,kBAAkB,EAAE,EAAE;IACtBO,kBAAkB,EAAE,EAAE;IACtBC,eAAe,EAAE;EACnB,CAAC;EAEDO,KAAK,CAACuF,sBAAsB,GAAG,YAAW;IACxC;IACA,IAAI,CAAC9E,GAAG,CAAEiF,oBAAqB,CAAC;EAClC,CAAC;;EAED;;EAEA1F,KAAK,CAAC2F,OAAO,GAAG,UAAUC,KAAK,EAAG;IAChCA,KAAK,GAAG/D,KAAK,CAAE+D,KAAM,CAAC,GAAG,CAAC,GAAGA,KAAK;IAClC,IAAI,CAACd,YAAY,GAAGc,KAAK,GAAG,IAAI;EAClC,CAAC;;EAED;;EAEA;EACA5F,KAAK,CAAC6F,UAAU,GAAG,YAAW;IAC5B,IAAI,CAACnG,OAAO,CAACoG,UAAU,CAACC,WAAW,CAAE,IAAI,CAACrG,OAAQ,CAAC;IACnD;IACA,IAAI,CAACe,GAAG,CAAC;MAAEuF,OAAO,EAAE;IAAG,CAAC,CAAC;IACzB,IAAI,CAACrD,SAAS,CAAE,QAAQ,EAAE,CAAE,IAAI,CAAG,CAAC;EACtC,CAAC;EAED3C,KAAK,CAACiG,MAAM,GAAG,YAAW;IACxB;IACA,IAAK,CAAChH,kBAAkB,IAAI,CAACuC,UAAU,CAAE,IAAI,CAAC7B,MAAM,CAACkD,OAAO,CAACrD,kBAAmB,CAAC,EAAG;MAClF,IAAI,CAACqG,UAAU,CAAC,CAAC;MACjB;IACF;;IAEA;IACA,IAAI,CAACK,IAAI,CAAE,eAAe,EAAE,YAAW;MACrC,IAAI,CAACL,UAAU,CAAC,CAAC;IACnB,CAAC,CAAC;IACF,IAAI,CAACM,IAAI,CAAC,CAAC;EACb,CAAC;EAEDnG,KAAK,CAACoG,MAAM,GAAG,YAAW;IACxB,OAAO,IAAI,CAACC,QAAQ;IACpB;IACA,IAAI,CAAC5F,GAAG,CAAC;MAAEuF,OAAO,EAAE;IAAG,CAAC,CAAC;IAEzB,IAAInD,OAAO,GAAG,IAAI,CAAClD,MAAM,CAACkD,OAAO;IAEjC,IAAIa,eAAe,GAAG,CAAC,CAAC;IACxB,IAAI4C,qBAAqB,GAAG,IAAI,CAACC,kCAAkC,CAAC,cAAc,CAAC;IACnF7C,eAAe,CAAE4C,qBAAqB,CAAE,GAAG,IAAI,CAACE,qBAAqB;IAErE,IAAI,CAACtH,UAAU,CAAC;MACdiF,IAAI,EAAEtB,OAAO,CAAC4D,WAAW;MACzBhD,EAAE,EAAEZ,OAAO,CAAC6D,YAAY;MACxB/C,UAAU,EAAE,IAAI;MAChBD,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ,CAAC;EAED1D,KAAK,CAACwG,qBAAqB,GAAG,YAAW;IACvC;IACA;IACA,IAAK,CAAC,IAAI,CAACH,QAAQ,EAAG;MACpB,IAAI,CAAC1D,SAAS,CAAC,QAAQ,CAAC;IAC1B;EACF,CAAC;;EAED;AACA;AACA;AACA;AACA;EACA3C,KAAK,CAACuG,kCAAkC,GAAG,UAAUI,aAAa,EAAG;IACnE,IAAIC,WAAW,GAAG,IAAI,CAACjH,MAAM,CAACkD,OAAO,CAAE8D,aAAa,CAAE;IACtD;IACA,IAAKC,WAAW,CAACC,OAAO,EAAG;MACzB,OAAO,SAAS;IAClB;IACA;IACA,KAAM,IAAIjI,IAAI,IAAIgI,WAAW,EAAG;MAC9B,OAAOhI,IAAI;IACb;EACF,CAAC;EAEDoB,KAAK,CAACmG,IAAI,GAAG,YAAW;IACtB;IACA,IAAI,CAACE,QAAQ,GAAG,IAAI;IACpB;IACA,IAAI,CAAC5F,GAAG,CAAC;MAAEuF,OAAO,EAAE;IAAG,CAAC,CAAC;IAEzB,IAAInD,OAAO,GAAG,IAAI,CAAClD,MAAM,CAACkD,OAAO;IAEjC,IAAIa,eAAe,GAAG,CAAC,CAAC;IACxB,IAAI4C,qBAAqB,GAAG,IAAI,CAACC,kCAAkC,CAAC,aAAa,CAAC;IAClF7C,eAAe,CAAE4C,qBAAqB,CAAE,GAAG,IAAI,CAACQ,mBAAmB;IAEnE,IAAI,CAAC5H,UAAU,CAAC;MACdiF,IAAI,EAAEtB,OAAO,CAAC6D,YAAY;MAC1BjD,EAAE,EAAEZ,OAAO,CAAC4D,WAAW;MACvB;MACA9C,UAAU,EAAE,IAAI;MAChBD,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ,CAAC;EAED1D,KAAK,CAAC8G,mBAAmB,GAAG,YAAW;IACrC;IACA;IACA,IAAK,IAAI,CAACT,QAAQ,EAAG;MACnB,IAAI,CAAC5F,GAAG,CAAC;QAAEuF,OAAO,EAAE;MAAO,CAAC,CAAC;MAC7B,IAAI,CAACrD,SAAS,CAAC,MAAM,CAAC;IACxB;EACF,CAAC;EAED3C,KAAK,CAAC+G,OAAO,GAAG,YAAW;IACzB,IAAI,CAACtG,GAAG,CAAC;MACPb,QAAQ,EAAE,EAAE;MACZoH,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVjI,UAAU,EAAE,EAAE;MACdE,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,OAAOb,IAAI;AAEX,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}