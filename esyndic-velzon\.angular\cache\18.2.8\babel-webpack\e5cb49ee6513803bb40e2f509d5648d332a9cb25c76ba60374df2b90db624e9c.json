{"ast": null, "code": "import _asyncToGenerator from \"C:/e-syndic/esyndic-velzon/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { Injectable, NgModule } from '@angular/core';\nimport { HttpHeaders, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { Subject, from, of, combineLatest } from 'rxjs';\nimport { map, mergeMap } from 'rxjs/operators';\nimport Keycloak from 'keycloak-js';\nimport { CommonModule } from '@angular/common';\nvar KeycloakEventType;\n(function (KeycloakEventType) {\n  KeycloakEventType[KeycloakEventType[\"OnAuthError\"] = 0] = \"OnAuthError\";\n  KeycloakEventType[KeycloakEventType[\"OnAuthLogout\"] = 1] = \"OnAuthLogout\";\n  KeycloakEventType[KeycloakEventType[\"OnAuthRefreshError\"] = 2] = \"OnAuthRefreshError\";\n  KeycloakEventType[KeycloakEventType[\"OnAuthRefreshSuccess\"] = 3] = \"OnAuthRefreshSuccess\";\n  KeycloakEventType[KeycloakEventType[\"OnAuthSuccess\"] = 4] = \"OnAuthSuccess\";\n  KeycloakEventType[KeycloakEventType[\"OnReady\"] = 5] = \"OnReady\";\n  KeycloakEventType[KeycloakEventType[\"OnTokenExpired\"] = 6] = \"OnTokenExpired\";\n  KeycloakEventType[KeycloakEventType[\"OnActionUpdate\"] = 7] = \"OnActionUpdate\";\n})(KeycloakEventType || (KeycloakEventType = {}));\nclass KeycloakAuthGuard {\n  constructor(router, keycloakAngular) {\n    this.router = router;\n    this.keycloakAngular = keycloakAngular;\n  }\n  canActivate(route, state) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.authenticated = yield _this.keycloakAngular.isLoggedIn();\n        _this.roles = yield _this.keycloakAngular.getUserRoles(true);\n        return yield _this.isAccessAllowed(route, state);\n      } catch (error) {\n        throw new Error('An error happened during access validation. Details:' + error);\n      }\n    })();\n  }\n}\nclass KeycloakService {\n  constructor() {\n    this._keycloakEvents$ = new Subject();\n  }\n  bindsKeycloakEvents() {\n    this._instance.onAuthError = errorData => {\n      this._keycloakEvents$.next({\n        args: errorData,\n        type: KeycloakEventType.OnAuthError\n      });\n    };\n    this._instance.onAuthLogout = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnAuthLogout\n      });\n    };\n    this._instance.onAuthRefreshSuccess = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnAuthRefreshSuccess\n      });\n    };\n    this._instance.onAuthRefreshError = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnAuthRefreshError\n      });\n    };\n    this._instance.onAuthSuccess = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnAuthSuccess\n      });\n    };\n    this._instance.onTokenExpired = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnTokenExpired\n      });\n    };\n    this._instance.onActionUpdate = state => {\n      this._keycloakEvents$.next({\n        args: state,\n        type: KeycloakEventType.OnActionUpdate\n      });\n    };\n    this._instance.onReady = authenticated => {\n      this._keycloakEvents$.next({\n        args: authenticated,\n        type: KeycloakEventType.OnReady\n      });\n    };\n  }\n  loadExcludedUrls(bearerExcludedUrls) {\n    const excludedUrls = [];\n    for (const item of bearerExcludedUrls) {\n      let excludedUrl;\n      if (typeof item === 'string') {\n        excludedUrl = {\n          urlPattern: new RegExp(item, 'i'),\n          httpMethods: []\n        };\n      } else {\n        excludedUrl = {\n          urlPattern: new RegExp(item.url, 'i'),\n          httpMethods: item.httpMethods\n        };\n      }\n      excludedUrls.push(excludedUrl);\n    }\n    return excludedUrls;\n  }\n  initServiceValues({\n    enableBearerInterceptor = true,\n    loadUserProfileAtStartUp = false,\n    bearerExcludedUrls = [],\n    authorizationHeaderName = 'Authorization',\n    bearerPrefix = 'Bearer',\n    initOptions,\n    updateMinValidity = 20,\n    shouldAddToken = () => true,\n    shouldUpdateToken = () => true\n  }) {\n    this._enableBearerInterceptor = enableBearerInterceptor;\n    this._loadUserProfileAtStartUp = loadUserProfileAtStartUp;\n    this._authorizationHeaderName = authorizationHeaderName;\n    this._bearerPrefix = bearerPrefix.trim().concat(' ');\n    this._excludedUrls = this.loadExcludedUrls(bearerExcludedUrls);\n    this._silentRefresh = initOptions ? initOptions.flow === 'implicit' : false;\n    this._updateMinValidity = updateMinValidity;\n    this.shouldAddToken = shouldAddToken;\n    this.shouldUpdateToken = shouldUpdateToken;\n  }\n  init(options = {}) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.initServiceValues(options);\n      const {\n        config,\n        initOptions\n      } = options;\n      _this2._instance = new Keycloak(config);\n      _this2.bindsKeycloakEvents();\n      const authenticated = yield _this2._instance.init(initOptions);\n      if (authenticated && _this2._loadUserProfileAtStartUp) {\n        yield _this2.loadUserProfile();\n      }\n      return authenticated;\n    })();\n  }\n  login(options = {}) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      yield _this3._instance.login(options);\n      if (_this3._loadUserProfileAtStartUp) {\n        yield _this3.loadUserProfile();\n      }\n    })();\n  }\n  logout(redirectUri) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const options = {\n        redirectUri\n      };\n      yield _this4._instance.logout(options);\n      _this4._userProfile = undefined;\n    })();\n  }\n  register(options = {\n    action: 'register'\n  }) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      yield _this5._instance.register(options);\n    })();\n  }\n  isUserInRole(role, resource) {\n    let hasRole;\n    hasRole = this._instance.hasResourceRole(role, resource);\n    if (!hasRole) {\n      hasRole = this._instance.hasRealmRole(role);\n    }\n    return hasRole;\n  }\n  getUserRoles(realmRoles = true, resource) {\n    let roles = [];\n    if (this._instance.resourceAccess) {\n      Object.keys(this._instance.resourceAccess).forEach(key => {\n        if (resource && resource !== key) {\n          return;\n        }\n        const resourceAccess = this._instance.resourceAccess[key];\n        const clientRoles = resourceAccess['roles'] || [];\n        roles = roles.concat(clientRoles);\n      });\n    }\n    if (realmRoles && this._instance.realmAccess) {\n      const realmRoles = this._instance.realmAccess['roles'] || [];\n      roles.push(...realmRoles);\n    }\n    return roles;\n  }\n  isLoggedIn() {\n    if (!this._instance) {\n      return false;\n    }\n    return this._instance.authenticated;\n  }\n  isTokenExpired(minValidity = 0) {\n    return this._instance.isTokenExpired(minValidity);\n  }\n  updateToken(minValidity = this._updateMinValidity) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (_this6._silentRefresh) {\n        if (_this6.isTokenExpired()) {\n          throw new Error('Failed to refresh the token, or the session is expired');\n        }\n        return true;\n      }\n      if (!_this6._instance) {\n        throw new Error('Keycloak Angular library is not initialized.');\n      }\n      try {\n        return yield _this6._instance.updateToken(minValidity);\n      } catch (error) {\n        return false;\n      }\n    })();\n  }\n  loadUserProfile(forceReload = false) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (_this7._userProfile && !forceReload) {\n        return _this7._userProfile;\n      }\n      if (!_this7._instance.authenticated) {\n        throw new Error('The user profile was not loaded as the user is not logged in.');\n      }\n      return _this7._userProfile = yield _this7._instance.loadUserProfile();\n    })();\n  }\n  getToken() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      return _this8._instance.token;\n    })();\n  }\n  getUsername() {\n    if (!this._userProfile) {\n      throw new Error('User not logged in or user profile was not loaded.');\n    }\n    return this._userProfile.username;\n  }\n  clearToken() {\n    this._instance.clearToken();\n  }\n  addTokenToHeader(headers = new HttpHeaders()) {\n    return from(this.getToken()).pipe(map(token => token ? headers.set(this._authorizationHeaderName, this._bearerPrefix + token) : headers));\n  }\n  getKeycloakInstance() {\n    return this._instance;\n  }\n  get excludedUrls() {\n    return this._excludedUrls;\n  }\n  get enableBearerInterceptor() {\n    return this._enableBearerInterceptor;\n  }\n  get keycloakEvents$() {\n    return this._keycloakEvents$;\n  }\n  static {\n    this.ɵfac = function KeycloakService_Factory(t) {\n      return new (t || KeycloakService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: KeycloakService,\n      factory: KeycloakService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeycloakService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass KeycloakBearerInterceptor {\n  constructor(keycloak) {\n    this.keycloak = keycloak;\n  }\n  conditionallyUpdateToken(req) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      if (_this9.keycloak.shouldUpdateToken(req)) {\n        return yield _this9.keycloak.updateToken();\n      }\n      return true;\n    })();\n  }\n  isUrlExcluded({\n    method,\n    url\n  }, {\n    urlPattern,\n    httpMethods\n  }) {\n    const httpTest = httpMethods.length === 0 || httpMethods.join().indexOf(method.toUpperCase()) > -1;\n    const urlTest = urlPattern.test(url);\n    return httpTest && urlTest;\n  }\n  intercept(req, next) {\n    const {\n      enableBearerInterceptor,\n      excludedUrls\n    } = this.keycloak;\n    if (!enableBearerInterceptor) {\n      return next.handle(req);\n    }\n    const shallPass = !this.keycloak.shouldAddToken(req) || excludedUrls.findIndex(item => this.isUrlExcluded(req, item)) > -1;\n    if (shallPass) {\n      return next.handle(req);\n    }\n    return combineLatest([from(this.conditionallyUpdateToken(req)), of(this.keycloak.isLoggedIn())]).pipe(mergeMap(([_, isLoggedIn]) => isLoggedIn ? this.handleRequestWithTokenHeader(req, next) : next.handle(req)));\n  }\n  handleRequestWithTokenHeader(req, next) {\n    return this.keycloak.addTokenToHeader(req.headers).pipe(mergeMap(headersWithBearer => {\n      const kcReq = req.clone({\n        headers: headersWithBearer\n      });\n      return next.handle(kcReq);\n    }));\n  }\n  static {\n    this.ɵfac = function KeycloakBearerInterceptor_Factory(t) {\n      return new (t || KeycloakBearerInterceptor)(i0.ɵɵinject(KeycloakService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: KeycloakBearerInterceptor,\n      factory: KeycloakBearerInterceptor.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeycloakBearerInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: KeycloakService\n  }], null);\n})();\nclass CoreModule {\n  static {\n    this.ɵfac = function CoreModule_Factory(t) {\n      return new (t || CoreModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CoreModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [KeycloakService, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: KeycloakBearerInterceptor,\n        multi: true\n      }],\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CoreModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      providers: [KeycloakService, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: KeycloakBearerInterceptor,\n        multi: true\n      }]\n    }]\n  }], null, null);\n})();\nclass KeycloakAngularModule {\n  static {\n    this.ɵfac = function KeycloakAngularModule_Factory(t) {\n      return new (t || KeycloakAngularModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: KeycloakAngularModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CoreModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeycloakAngularModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CoreModule]\n    }]\n  }], null, null);\n})();\nexport { CoreModule, KeycloakAngularModule, KeycloakAuthGuard, KeycloakBearerInterceptor, KeycloakEventType, KeycloakService };", "map": {"version": 3, "names": ["i0", "Injectable", "NgModule", "HttpHeaders", "HTTP_INTERCEPTORS", "Subject", "from", "of", "combineLatest", "map", "mergeMap", "Keycloak", "CommonModule", "KeycloakEventType", "KeycloakAuthGuard", "constructor", "router", "keycloakAngular", "canActivate", "route", "state", "_this", "_asyncToGenerator", "authenticated", "isLoggedIn", "roles", "getUserRoles", "isAccessAllowed", "error", "Error", "KeycloakService", "_keycloakEvents$", "bindsKeycloakEvents", "_instance", "onAuthError", "errorData", "next", "args", "type", "OnAuthError", "onAuthLogout", "OnAuthLogout", "onAuthRefreshSuccess", "OnAuthRefreshSuccess", "onAuthRefreshError", "OnAuthRefreshError", "onAuthSuccess", "OnAuthSuccess", "onTokenExpired", "OnTokenExpired", "onActionUpdate", "OnActionUpdate", "onReady", "OnReady", "loadExcludedUrls", "bearerExcludedUrls", "excludedUrls", "item", "excludedUrl", "urlPattern", "RegExp", "httpMethods", "url", "push", "initServiceValues", "enableBearerInterceptor", "loadUserProfileAtStartUp", "authorizationHeaderName", "bearerPrefix", "initOptions", "updateMinValidity", "shouldAddToken", "shouldUpdateToken", "_enableBearerInterceptor", "_loadUserProfileAtStartUp", "_authorizationHeaderName", "_bearerPrefix", "trim", "concat", "_excludedUrls", "_silentRefresh", "flow", "_updateMinValidity", "init", "options", "_this2", "config", "loadUserProfile", "login", "_this3", "logout", "redirectUri", "_this4", "_userProfile", "undefined", "register", "action", "_this5", "isUserInRole", "role", "resource", "hasRole", "hasResourceRole", "hasRealmRole", "realmRoles", "resourceAccess", "Object", "keys", "for<PERSON>ach", "key", "clientRoles", "realmAccess", "isTokenExpired", "minValidity", "updateToken", "_this6", "forceReload", "_this7", "getToken", "_this8", "token", "getUsername", "username", "clearToken", "addTokenToHeader", "headers", "pipe", "set", "getKeycloakInstance", "keycloakEvents$", "ɵfac", "KeycloakService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "factory", "ngDevMode", "ɵsetClassMetadata", "KeycloakBearerInterceptor", "keycloak", "conditionallyUpdateToken", "req", "_this9", "isUrlExcluded", "method", "httpTest", "length", "join", "indexOf", "toUpperCase", "urlTest", "test", "intercept", "handle", "shallPass", "findIndex", "_", "handleRequestWithTokenHeader", "headers<PERSON><PERSON><PERSON><PERSON><PERSON>", "kcReq", "clone", "KeycloakBearerInterceptor_Factory", "ɵɵinject", "CoreModule", "CoreModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "provide", "useClass", "multi", "imports", "KeycloakAngularModule", "KeycloakAngularModule_Factory"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/keycloak-angular/fesm2022/keycloak-angular.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, NgModule } from '@angular/core';\nimport { HttpHeaders, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { Subject, from, of, combineLatest } from 'rxjs';\nimport { map, mergeMap } from 'rxjs/operators';\nimport Keycloak from 'keycloak-js';\nimport { CommonModule } from '@angular/common';\n\nvar KeycloakEventType;\n(function (KeycloakEventType) {\n    KeycloakEventType[KeycloakEventType[\"OnAuthError\"] = 0] = \"OnAuthError\";\n    KeycloakEventType[KeycloakEventType[\"OnAuthLogout\"] = 1] = \"OnAuthLogout\";\n    KeycloakEventType[KeycloakEventType[\"OnAuthRefreshError\"] = 2] = \"OnAuthRefreshError\";\n    KeycloakEventType[KeycloakEventType[\"OnAuthRefreshSuccess\"] = 3] = \"OnAuthRefreshSuccess\";\n    KeycloakEventType[KeycloakEventType[\"OnAuthSuccess\"] = 4] = \"OnAuthSuccess\";\n    KeycloakEventType[KeycloakEventType[\"OnReady\"] = 5] = \"OnReady\";\n    KeycloakEventType[KeycloakEventType[\"OnTokenExpired\"] = 6] = \"OnTokenExpired\";\n    KeycloakEventType[KeycloakEventType[\"OnActionUpdate\"] = 7] = \"OnActionUpdate\";\n})(KeycloakEventType || (KeycloakEventType = {}));\n\nclass KeycloakAuthGuard {\n    constructor(router, keycloakAngular) {\n        this.router = router;\n        this.keycloakAngular = keycloakAngular;\n    }\n    async canActivate(route, state) {\n        try {\n            this.authenticated = await this.keycloakAngular.isLoggedIn();\n            this.roles = await this.keycloakAngular.getUserRoles(true);\n            return await this.isAccessAllowed(route, state);\n        }\n        catch (error) {\n            throw new Error('An error happened during access validation. Details:' + error);\n        }\n    }\n}\n\nclass KeycloakService {\n    constructor() {\n        this._keycloakEvents$ = new Subject();\n    }\n    bindsKeycloakEvents() {\n        this._instance.onAuthError = (errorData) => {\n            this._keycloakEvents$.next({\n                args: errorData,\n                type: KeycloakEventType.OnAuthError\n            });\n        };\n        this._instance.onAuthLogout = () => {\n            this._keycloakEvents$.next({ type: KeycloakEventType.OnAuthLogout });\n        };\n        this._instance.onAuthRefreshSuccess = () => {\n            this._keycloakEvents$.next({\n                type: KeycloakEventType.OnAuthRefreshSuccess\n            });\n        };\n        this._instance.onAuthRefreshError = () => {\n            this._keycloakEvents$.next({\n                type: KeycloakEventType.OnAuthRefreshError\n            });\n        };\n        this._instance.onAuthSuccess = () => {\n            this._keycloakEvents$.next({ type: KeycloakEventType.OnAuthSuccess });\n        };\n        this._instance.onTokenExpired = () => {\n            this._keycloakEvents$.next({\n                type: KeycloakEventType.OnTokenExpired\n            });\n        };\n        this._instance.onActionUpdate = (state) => {\n            this._keycloakEvents$.next({\n                args: state,\n                type: KeycloakEventType.OnActionUpdate\n            });\n        };\n        this._instance.onReady = (authenticated) => {\n            this._keycloakEvents$.next({\n                args: authenticated,\n                type: KeycloakEventType.OnReady\n            });\n        };\n    }\n    loadExcludedUrls(bearerExcludedUrls) {\n        const excludedUrls = [];\n        for (const item of bearerExcludedUrls) {\n            let excludedUrl;\n            if (typeof item === 'string') {\n                excludedUrl = { urlPattern: new RegExp(item, 'i'), httpMethods: [] };\n            }\n            else {\n                excludedUrl = {\n                    urlPattern: new RegExp(item.url, 'i'),\n                    httpMethods: item.httpMethods\n                };\n            }\n            excludedUrls.push(excludedUrl);\n        }\n        return excludedUrls;\n    }\n    initServiceValues({ enableBearerInterceptor = true, loadUserProfileAtStartUp = false, bearerExcludedUrls = [], authorizationHeaderName = 'Authorization', bearerPrefix = 'Bearer', initOptions, updateMinValidity = 20, shouldAddToken = () => true, shouldUpdateToken = () => true }) {\n        this._enableBearerInterceptor = enableBearerInterceptor;\n        this._loadUserProfileAtStartUp = loadUserProfileAtStartUp;\n        this._authorizationHeaderName = authorizationHeaderName;\n        this._bearerPrefix = bearerPrefix.trim().concat(' ');\n        this._excludedUrls = this.loadExcludedUrls(bearerExcludedUrls);\n        this._silentRefresh = initOptions ? initOptions.flow === 'implicit' : false;\n        this._updateMinValidity = updateMinValidity;\n        this.shouldAddToken = shouldAddToken;\n        this.shouldUpdateToken = shouldUpdateToken;\n    }\n    async init(options = {}) {\n        this.initServiceValues(options);\n        const { config, initOptions } = options;\n        this._instance = new Keycloak(config);\n        this.bindsKeycloakEvents();\n        const authenticated = await this._instance.init(initOptions);\n        if (authenticated && this._loadUserProfileAtStartUp) {\n            await this.loadUserProfile();\n        }\n        return authenticated;\n    }\n    async login(options = {}) {\n        await this._instance.login(options);\n        if (this._loadUserProfileAtStartUp) {\n            await this.loadUserProfile();\n        }\n    }\n    async logout(redirectUri) {\n        const options = {\n            redirectUri\n        };\n        await this._instance.logout(options);\n        this._userProfile = undefined;\n    }\n    async register(options = { action: 'register' }) {\n        await this._instance.register(options);\n    }\n    isUserInRole(role, resource) {\n        let hasRole;\n        hasRole = this._instance.hasResourceRole(role, resource);\n        if (!hasRole) {\n            hasRole = this._instance.hasRealmRole(role);\n        }\n        return hasRole;\n    }\n    getUserRoles(realmRoles = true, resource) {\n        let roles = [];\n        if (this._instance.resourceAccess) {\n            Object.keys(this._instance.resourceAccess).forEach((key) => {\n                if (resource && resource !== key) {\n                    return;\n                }\n                const resourceAccess = this._instance.resourceAccess[key];\n                const clientRoles = resourceAccess['roles'] || [];\n                roles = roles.concat(clientRoles);\n            });\n        }\n        if (realmRoles && this._instance.realmAccess) {\n            const realmRoles = this._instance.realmAccess['roles'] || [];\n            roles.push(...realmRoles);\n        }\n        return roles;\n    }\n    isLoggedIn() {\n        if (!this._instance) {\n            return false;\n        }\n        return this._instance.authenticated;\n    }\n    isTokenExpired(minValidity = 0) {\n        return this._instance.isTokenExpired(minValidity);\n    }\n    async updateToken(minValidity = this._updateMinValidity) {\n        if (this._silentRefresh) {\n            if (this.isTokenExpired()) {\n                throw new Error('Failed to refresh the token, or the session is expired');\n            }\n            return true;\n        }\n        if (!this._instance) {\n            throw new Error('Keycloak Angular library is not initialized.');\n        }\n        try {\n            return await this._instance.updateToken(minValidity);\n        }\n        catch (error) {\n            return false;\n        }\n    }\n    async loadUserProfile(forceReload = false) {\n        if (this._userProfile && !forceReload) {\n            return this._userProfile;\n        }\n        if (!this._instance.authenticated) {\n            throw new Error('The user profile was not loaded as the user is not logged in.');\n        }\n        return (this._userProfile = await this._instance.loadUserProfile());\n    }\n    async getToken() {\n        return this._instance.token;\n    }\n    getUsername() {\n        if (!this._userProfile) {\n            throw new Error('User not logged in or user profile was not loaded.');\n        }\n        return this._userProfile.username;\n    }\n    clearToken() {\n        this._instance.clearToken();\n    }\n    addTokenToHeader(headers = new HttpHeaders()) {\n        return from(this.getToken()).pipe(map((token) => token\n            ? headers.set(this._authorizationHeaderName, this._bearerPrefix + token)\n            : headers));\n    }\n    getKeycloakInstance() {\n        return this._instance;\n    }\n    get excludedUrls() {\n        return this._excludedUrls;\n    }\n    get enableBearerInterceptor() {\n        return this._enableBearerInterceptor;\n    }\n    get keycloakEvents$() {\n        return this._keycloakEvents$;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.3\", ngImport: i0, type: KeycloakService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.3\", ngImport: i0, type: KeycloakService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.3\", ngImport: i0, type: KeycloakService, decorators: [{\n            type: Injectable\n        }] });\n\nclass KeycloakBearerInterceptor {\n    constructor(keycloak) {\n        this.keycloak = keycloak;\n    }\n    async conditionallyUpdateToken(req) {\n        if (this.keycloak.shouldUpdateToken(req)) {\n            return await this.keycloak.updateToken();\n        }\n        return true;\n    }\n    isUrlExcluded({ method, url }, { urlPattern, httpMethods }) {\n        const httpTest = httpMethods.length === 0 ||\n            httpMethods.join().indexOf(method.toUpperCase()) > -1;\n        const urlTest = urlPattern.test(url);\n        return httpTest && urlTest;\n    }\n    intercept(req, next) {\n        const { enableBearerInterceptor, excludedUrls } = this.keycloak;\n        if (!enableBearerInterceptor) {\n            return next.handle(req);\n        }\n        const shallPass = !this.keycloak.shouldAddToken(req) ||\n            excludedUrls.findIndex((item) => this.isUrlExcluded(req, item)) > -1;\n        if (shallPass) {\n            return next.handle(req);\n        }\n        return combineLatest([\n            from(this.conditionallyUpdateToken(req)),\n            of(this.keycloak.isLoggedIn())\n        ]).pipe(mergeMap(([_, isLoggedIn]) => isLoggedIn\n            ? this.handleRequestWithTokenHeader(req, next)\n            : next.handle(req)));\n    }\n    handleRequestWithTokenHeader(req, next) {\n        return this.keycloak.addTokenToHeader(req.headers).pipe(mergeMap((headersWithBearer) => {\n            const kcReq = req.clone({ headers: headersWithBearer });\n            return next.handle(kcReq);\n        }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.3\", ngImport: i0, type: KeycloakBearerInterceptor, deps: [{ token: KeycloakService }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.3\", ngImport: i0, type: KeycloakBearerInterceptor }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.3\", ngImport: i0, type: KeycloakBearerInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: KeycloakService }] });\n\nclass CoreModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.3\", ngImport: i0, type: CoreModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.3\", ngImport: i0, type: CoreModule, imports: [CommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.3\", ngImport: i0, type: CoreModule, providers: [\n            KeycloakService,\n            {\n                provide: HTTP_INTERCEPTORS,\n                useClass: KeycloakBearerInterceptor,\n                multi: true\n            }\n        ], imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.3\", ngImport: i0, type: CoreModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    providers: [\n                        KeycloakService,\n                        {\n                            provide: HTTP_INTERCEPTORS,\n                            useClass: KeycloakBearerInterceptor,\n                            multi: true\n                        }\n                    ]\n                }]\n        }] });\n\nclass KeycloakAngularModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.3\", ngImport: i0, type: KeycloakAngularModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.3\", ngImport: i0, type: KeycloakAngularModule, imports: [CoreModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.3\", ngImport: i0, type: KeycloakAngularModule, imports: [CoreModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.3\", ngImport: i0, type: KeycloakAngularModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CoreModule]\n                }]\n        }] });\n\nexport { CoreModule, KeycloakAngularModule, KeycloakAuthGuard, KeycloakBearerInterceptor, KeycloakEventType, KeycloakService };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AACpD,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,sBAAsB;AACrE,SAASC,OAAO,EAAEC,IAAI,EAAEC,EAAE,EAAEC,aAAa,QAAQ,MAAM;AACvD,SAASC,GAAG,EAAEC,QAAQ,QAAQ,gBAAgB;AAC9C,OAAOC,QAAQ,MAAM,aAAa;AAClC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,IAAIC,iBAAiB;AACrB,CAAC,UAAUA,iBAAiB,EAAE;EAC1BA,iBAAiB,CAACA,iBAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EACvEA,iBAAiB,CAACA,iBAAiB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACzEA,iBAAiB,CAACA,iBAAiB,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB;EACrFA,iBAAiB,CAACA,iBAAiB,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,GAAG,sBAAsB;EACzFA,iBAAiB,CAACA,iBAAiB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;EAC3EA,iBAAiB,CAACA,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAC/DA,iBAAiB,CAACA,iBAAiB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EAC7EA,iBAAiB,CAACA,iBAAiB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;AACjF,CAAC,EAAEA,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AAEjD,MAAMC,iBAAiB,CAAC;EACpBC,WAAWA,CAACC,MAAM,EAAEC,eAAe,EAAE;IACjC,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,eAAe,GAAGA,eAAe;EAC1C;EACMC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC5B,IAAI;QACAD,KAAI,CAACE,aAAa,SAASF,KAAI,CAACJ,eAAe,CAACO,UAAU,CAAC,CAAC;QAC5DH,KAAI,CAACI,KAAK,SAASJ,KAAI,CAACJ,eAAe,CAACS,YAAY,CAAC,IAAI,CAAC;QAC1D,aAAaL,KAAI,CAACM,eAAe,CAACR,KAAK,EAAEC,KAAK,CAAC;MACnD,CAAC,CACD,OAAOQ,KAAK,EAAE;QACV,MAAM,IAAIC,KAAK,CAAC,sDAAsD,GAAGD,KAAK,CAAC;MACnF;IAAC;EACL;AACJ;AAEA,MAAME,eAAe,CAAC;EAClBf,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgB,gBAAgB,GAAG,IAAI1B,OAAO,CAAC,CAAC;EACzC;EACA2B,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACC,SAAS,CAACC,WAAW,GAAIC,SAAS,IAAK;MACxC,IAAI,CAACJ,gBAAgB,CAACK,IAAI,CAAC;QACvBC,IAAI,EAAEF,SAAS;QACfG,IAAI,EAAEzB,iBAAiB,CAAC0B;MAC5B,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACN,SAAS,CAACO,YAAY,GAAG,MAAM;MAChC,IAAI,CAACT,gBAAgB,CAACK,IAAI,CAAC;QAAEE,IAAI,EAAEzB,iBAAiB,CAAC4B;MAAa,CAAC,CAAC;IACxE,CAAC;IACD,IAAI,CAACR,SAAS,CAACS,oBAAoB,GAAG,MAAM;MACxC,IAAI,CAACX,gBAAgB,CAACK,IAAI,CAAC;QACvBE,IAAI,EAAEzB,iBAAiB,CAAC8B;MAC5B,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACV,SAAS,CAACW,kBAAkB,GAAG,MAAM;MACtC,IAAI,CAACb,gBAAgB,CAACK,IAAI,CAAC;QACvBE,IAAI,EAAEzB,iBAAiB,CAACgC;MAC5B,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACZ,SAAS,CAACa,aAAa,GAAG,MAAM;MACjC,IAAI,CAACf,gBAAgB,CAACK,IAAI,CAAC;QAAEE,IAAI,EAAEzB,iBAAiB,CAACkC;MAAc,CAAC,CAAC;IACzE,CAAC;IACD,IAAI,CAACd,SAAS,CAACe,cAAc,GAAG,MAAM;MAClC,IAAI,CAACjB,gBAAgB,CAACK,IAAI,CAAC;QACvBE,IAAI,EAAEzB,iBAAiB,CAACoC;MAC5B,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAChB,SAAS,CAACiB,cAAc,GAAI9B,KAAK,IAAK;MACvC,IAAI,CAACW,gBAAgB,CAACK,IAAI,CAAC;QACvBC,IAAI,EAAEjB,KAAK;QACXkB,IAAI,EAAEzB,iBAAiB,CAACsC;MAC5B,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAClB,SAAS,CAACmB,OAAO,GAAI7B,aAAa,IAAK;MACxC,IAAI,CAACQ,gBAAgB,CAACK,IAAI,CAAC;QACvBC,IAAI,EAAEd,aAAa;QACnBe,IAAI,EAAEzB,iBAAiB,CAACwC;MAC5B,CAAC,CAAC;IACN,CAAC;EACL;EACAC,gBAAgBA,CAACC,kBAAkB,EAAE;IACjC,MAAMC,YAAY,GAAG,EAAE;IACvB,KAAK,MAAMC,IAAI,IAAIF,kBAAkB,EAAE;MACnC,IAAIG,WAAW;MACf,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;QAC1BC,WAAW,GAAG;UAAEC,UAAU,EAAE,IAAIC,MAAM,CAACH,IAAI,EAAE,GAAG,CAAC;UAAEI,WAAW,EAAE;QAAG,CAAC;MACxE,CAAC,MACI;QACDH,WAAW,GAAG;UACVC,UAAU,EAAE,IAAIC,MAAM,CAACH,IAAI,CAACK,GAAG,EAAE,GAAG,CAAC;UACrCD,WAAW,EAAEJ,IAAI,CAACI;QACtB,CAAC;MACL;MACAL,YAAY,CAACO,IAAI,CAACL,WAAW,CAAC;IAClC;IACA,OAAOF,YAAY;EACvB;EACAQ,iBAAiBA,CAAC;IAAEC,uBAAuB,GAAG,IAAI;IAAEC,wBAAwB,GAAG,KAAK;IAAEX,kBAAkB,GAAG,EAAE;IAAEY,uBAAuB,GAAG,eAAe;IAAEC,YAAY,GAAG,QAAQ;IAAEC,WAAW;IAAEC,iBAAiB,GAAG,EAAE;IAAEC,cAAc,GAAGA,CAAA,KAAM,IAAI;IAAEC,iBAAiB,GAAGA,CAAA,KAAM;EAAK,CAAC,EAAE;IACnR,IAAI,CAACC,wBAAwB,GAAGR,uBAAuB;IACvD,IAAI,CAACS,yBAAyB,GAAGR,wBAAwB;IACzD,IAAI,CAACS,wBAAwB,GAAGR,uBAAuB;IACvD,IAAI,CAACS,aAAa,GAAGR,YAAY,CAACS,IAAI,CAAC,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC;IACpD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACzB,gBAAgB,CAACC,kBAAkB,CAAC;IAC9D,IAAI,CAACyB,cAAc,GAAGX,WAAW,GAAGA,WAAW,CAACY,IAAI,KAAK,UAAU,GAAG,KAAK;IAC3E,IAAI,CAACC,kBAAkB,GAAGZ,iBAAiB;IAC3C,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;EAC9C;EACMW,IAAIA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA/D,iBAAA;MACrB+D,MAAI,CAACrB,iBAAiB,CAACoB,OAAO,CAAC;MAC/B,MAAM;QAAEE,MAAM;QAAEjB;MAAY,CAAC,GAAGe,OAAO;MACvCC,MAAI,CAACpD,SAAS,GAAG,IAAItB,QAAQ,CAAC2E,MAAM,CAAC;MACrCD,MAAI,CAACrD,mBAAmB,CAAC,CAAC;MAC1B,MAAMT,aAAa,SAAS8D,MAAI,CAACpD,SAAS,CAACkD,IAAI,CAACd,WAAW,CAAC;MAC5D,IAAI9C,aAAa,IAAI8D,MAAI,CAACX,yBAAyB,EAAE;QACjD,MAAMW,MAAI,CAACE,eAAe,CAAC,CAAC;MAChC;MACA,OAAOhE,aAAa;IAAC;EACzB;EACMiE,KAAKA,CAACJ,OAAO,GAAG,CAAC,CAAC,EAAE;IAAA,IAAAK,MAAA;IAAA,OAAAnE,iBAAA;MACtB,MAAMmE,MAAI,CAACxD,SAAS,CAACuD,KAAK,CAACJ,OAAO,CAAC;MACnC,IAAIK,MAAI,CAACf,yBAAyB,EAAE;QAChC,MAAMe,MAAI,CAACF,eAAe,CAAC,CAAC;MAChC;IAAC;EACL;EACMG,MAAMA,CAACC,WAAW,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAtE,iBAAA;MACtB,MAAM8D,OAAO,GAAG;QACZO;MACJ,CAAC;MACD,MAAMC,MAAI,CAAC3D,SAAS,CAACyD,MAAM,CAACN,OAAO,CAAC;MACpCQ,MAAI,CAACC,YAAY,GAAGC,SAAS;IAAC;EAClC;EACMC,QAAQA,CAACX,OAAO,GAAG;IAAEY,MAAM,EAAE;EAAW,CAAC,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA3E,iBAAA;MAC7C,MAAM2E,MAAI,CAAChE,SAAS,CAAC8D,QAAQ,CAACX,OAAO,CAAC;IAAC;EAC3C;EACAc,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IACzB,IAAIC,OAAO;IACXA,OAAO,GAAG,IAAI,CAACpE,SAAS,CAACqE,eAAe,CAACH,IAAI,EAAEC,QAAQ,CAAC;IACxD,IAAI,CAACC,OAAO,EAAE;MACVA,OAAO,GAAG,IAAI,CAACpE,SAAS,CAACsE,YAAY,CAACJ,IAAI,CAAC;IAC/C;IACA,OAAOE,OAAO;EAClB;EACA3E,YAAYA,CAAC8E,UAAU,GAAG,IAAI,EAAEJ,QAAQ,EAAE;IACtC,IAAI3E,KAAK,GAAG,EAAE;IACd,IAAI,IAAI,CAACQ,SAAS,CAACwE,cAAc,EAAE;MAC/BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1E,SAAS,CAACwE,cAAc,CAAC,CAACG,OAAO,CAAEC,GAAG,IAAK;QACxD,IAAIT,QAAQ,IAAIA,QAAQ,KAAKS,GAAG,EAAE;UAC9B;QACJ;QACA,MAAMJ,cAAc,GAAG,IAAI,CAACxE,SAAS,CAACwE,cAAc,CAACI,GAAG,CAAC;QACzD,MAAMC,WAAW,GAAGL,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE;QACjDhF,KAAK,GAAGA,KAAK,CAACqD,MAAM,CAACgC,WAAW,CAAC;MACrC,CAAC,CAAC;IACN;IACA,IAAIN,UAAU,IAAI,IAAI,CAACvE,SAAS,CAAC8E,WAAW,EAAE;MAC1C,MAAMP,UAAU,GAAG,IAAI,CAACvE,SAAS,CAAC8E,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE;MAC5DtF,KAAK,CAACsC,IAAI,CAAC,GAAGyC,UAAU,CAAC;IAC7B;IACA,OAAO/E,KAAK;EAChB;EACAD,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACS,SAAS,EAAE;MACjB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACA,SAAS,CAACV,aAAa;EACvC;EACAyF,cAAcA,CAACC,WAAW,GAAG,CAAC,EAAE;IAC5B,OAAO,IAAI,CAAChF,SAAS,CAAC+E,cAAc,CAACC,WAAW,CAAC;EACrD;EACMC,WAAWA,CAACD,WAAW,GAAG,IAAI,CAAC/B,kBAAkB,EAAE;IAAA,IAAAiC,MAAA;IAAA,OAAA7F,iBAAA;MACrD,IAAI6F,MAAI,CAACnC,cAAc,EAAE;QACrB,IAAImC,MAAI,CAACH,cAAc,CAAC,CAAC,EAAE;UACvB,MAAM,IAAInF,KAAK,CAAC,wDAAwD,CAAC;QAC7E;QACA,OAAO,IAAI;MACf;MACA,IAAI,CAACsF,MAAI,CAAClF,SAAS,EAAE;QACjB,MAAM,IAAIJ,KAAK,CAAC,8CAA8C,CAAC;MACnE;MACA,IAAI;QACA,aAAasF,MAAI,CAAClF,SAAS,CAACiF,WAAW,CAACD,WAAW,CAAC;MACxD,CAAC,CACD,OAAOrF,KAAK,EAAE;QACV,OAAO,KAAK;MAChB;IAAC;EACL;EACM2D,eAAeA,CAAC6B,WAAW,GAAG,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA/F,iBAAA;MACvC,IAAI+F,MAAI,CAACxB,YAAY,IAAI,CAACuB,WAAW,EAAE;QACnC,OAAOC,MAAI,CAACxB,YAAY;MAC5B;MACA,IAAI,CAACwB,MAAI,CAACpF,SAAS,CAACV,aAAa,EAAE;QAC/B,MAAM,IAAIM,KAAK,CAAC,+DAA+D,CAAC;MACpF;MACA,OAAQwF,MAAI,CAACxB,YAAY,SAASwB,MAAI,CAACpF,SAAS,CAACsD,eAAe,CAAC,CAAC;IAAE;EACxE;EACM+B,QAAQA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAjG,iBAAA;MACb,OAAOiG,MAAI,CAACtF,SAAS,CAACuF,KAAK;IAAC;EAChC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAAC5B,YAAY,EAAE;MACpB,MAAM,IAAIhE,KAAK,CAAC,oDAAoD,CAAC;IACzE;IACA,OAAO,IAAI,CAACgE,YAAY,CAAC6B,QAAQ;EACrC;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC1F,SAAS,CAAC0F,UAAU,CAAC,CAAC;EAC/B;EACAC,gBAAgBA,CAACC,OAAO,GAAG,IAAI1H,WAAW,CAAC,CAAC,EAAE;IAC1C,OAAOG,IAAI,CAAC,IAAI,CAACgH,QAAQ,CAAC,CAAC,CAAC,CAACQ,IAAI,CAACrH,GAAG,CAAE+G,KAAK,IAAKA,KAAK,GAChDK,OAAO,CAACE,GAAG,CAAC,IAAI,CAACpD,wBAAwB,EAAE,IAAI,CAACC,aAAa,GAAG4C,KAAK,CAAC,GACtEK,OAAO,CAAC,CAAC;EACnB;EACAG,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC/F,SAAS;EACzB;EACA,IAAIuB,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACuB,aAAa;EAC7B;EACA,IAAId,uBAAuBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAACQ,wBAAwB;EACxC;EACA,IAAIwD,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAClG,gBAAgB;EAChC;EACA;IAAS,IAAI,CAACmG,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFtG,eAAe;IAAA,CAAoD;EAAE;EAC/K;IAAS,IAAI,CAACuG,KAAK,kBAD6ErI,EAAE,CAAAsI,kBAAA;MAAAd,KAAA,EACY1F,eAAe;MAAAyG,OAAA,EAAfzG,eAAe,CAAAoG;IAAA,EAAG;EAAE;AACtI;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAHoGxI,EAAE,CAAAyI,iBAAA,CAGX3G,eAAe,EAAc,CAAC;IAC7GQ,IAAI,EAAErC;EACV,CAAC,CAAC;AAAA;AAEV,MAAMyI,yBAAyB,CAAC;EAC5B3H,WAAWA,CAAC4H,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACMC,wBAAwBA,CAACC,GAAG,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAxH,iBAAA;MAChC,IAAIwH,MAAI,CAACH,QAAQ,CAACnE,iBAAiB,CAACqE,GAAG,CAAC,EAAE;QACtC,aAAaC,MAAI,CAACH,QAAQ,CAACzB,WAAW,CAAC,CAAC;MAC5C;MACA,OAAO,IAAI;IAAC;EAChB;EACA6B,aAAaA,CAAC;IAAEC,MAAM;IAAElF;EAAI,CAAC,EAAE;IAAEH,UAAU;IAAEE;EAAY,CAAC,EAAE;IACxD,MAAMoF,QAAQ,GAAGpF,WAAW,CAACqF,MAAM,KAAK,CAAC,IACrCrF,WAAW,CAACsF,IAAI,CAAC,CAAC,CAACC,OAAO,CAACJ,MAAM,CAACK,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACzD,MAAMC,OAAO,GAAG3F,UAAU,CAAC4F,IAAI,CAACzF,GAAG,CAAC;IACpC,OAAOmF,QAAQ,IAAIK,OAAO;EAC9B;EACAE,SAASA,CAACX,GAAG,EAAEzG,IAAI,EAAE;IACjB,MAAM;MAAE6B,uBAAuB;MAAET;IAAa,CAAC,GAAG,IAAI,CAACmF,QAAQ;IAC/D,IAAI,CAAC1E,uBAAuB,EAAE;MAC1B,OAAO7B,IAAI,CAACqH,MAAM,CAACZ,GAAG,CAAC;IAC3B;IACA,MAAMa,SAAS,GAAG,CAAC,IAAI,CAACf,QAAQ,CAACpE,cAAc,CAACsE,GAAG,CAAC,IAChDrF,YAAY,CAACmG,SAAS,CAAElG,IAAI,IAAK,IAAI,CAACsF,aAAa,CAACF,GAAG,EAAEpF,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACxE,IAAIiG,SAAS,EAAE;MACX,OAAOtH,IAAI,CAACqH,MAAM,CAACZ,GAAG,CAAC;IAC3B;IACA,OAAOrI,aAAa,CAAC,CACjBF,IAAI,CAAC,IAAI,CAACsI,wBAAwB,CAACC,GAAG,CAAC,CAAC,EACxCtI,EAAE,CAAC,IAAI,CAACoI,QAAQ,CAACnH,UAAU,CAAC,CAAC,CAAC,CACjC,CAAC,CAACsG,IAAI,CAACpH,QAAQ,CAAC,CAAC,CAACkJ,CAAC,EAAEpI,UAAU,CAAC,KAAKA,UAAU,GAC1C,IAAI,CAACqI,4BAA4B,CAAChB,GAAG,EAAEzG,IAAI,CAAC,GAC5CA,IAAI,CAACqH,MAAM,CAACZ,GAAG,CAAC,CAAC,CAAC;EAC5B;EACAgB,4BAA4BA,CAAChB,GAAG,EAAEzG,IAAI,EAAE;IACpC,OAAO,IAAI,CAACuG,QAAQ,CAACf,gBAAgB,CAACiB,GAAG,CAAChB,OAAO,CAAC,CAACC,IAAI,CAACpH,QAAQ,CAAEoJ,iBAAiB,IAAK;MACpF,MAAMC,KAAK,GAAGlB,GAAG,CAACmB,KAAK,CAAC;QAAEnC,OAAO,EAAEiC;MAAkB,CAAC,CAAC;MACvD,OAAO1H,IAAI,CAACqH,MAAM,CAACM,KAAK,CAAC;IAC7B,CAAC,CAAC,CAAC;EACP;EACA;IAAS,IAAI,CAAC7B,IAAI,YAAA+B,kCAAA7B,CAAA;MAAA,YAAAA,CAAA,IAAwFM,yBAAyB,EA9CnC1I,EAAE,CAAAkK,QAAA,CA8CmDpI,eAAe;IAAA,CAA6C;EAAE;EACnN;IAAS,IAAI,CAACuG,KAAK,kBA/C6ErI,EAAE,CAAAsI,kBAAA;MAAAd,KAAA,EA+CYkB,yBAAyB;MAAAH,OAAA,EAAzBG,yBAAyB,CAAAR;IAAA,EAAG;EAAE;AAChJ;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAjDoGxI,EAAE,CAAAyI,iBAAA,CAiDXC,yBAAyB,EAAc,CAAC;IACvHpG,IAAI,EAAErC;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEqC,IAAI,EAAER;EAAgB,CAAC,CAAC;AAAA;AAE7D,MAAMqI,UAAU,CAAC;EACb;IAAS,IAAI,CAACjC,IAAI,YAAAkC,mBAAAhC,CAAA;MAAA,YAAAA,CAAA,IAAwF+B,UAAU;IAAA,CAAkD;EAAE;EACxK;IAAS,IAAI,CAACE,IAAI,kBAvD8ErK,EAAE,CAAAsK,gBAAA;MAAAhI,IAAA,EAuDS6H;IAAU,EAA4B;EAAE;EACnJ;IAAS,IAAI,CAACI,IAAI,kBAxD8EvK,EAAE,CAAAwK,gBAAA;MAAAC,SAAA,EAwDgC,CAC1H3I,eAAe,EACf;QACI4I,OAAO,EAAEtK,iBAAiB;QAC1BuK,QAAQ,EAAEjC,yBAAyB;QACnCkC,KAAK,EAAE;MACX,CAAC,CACJ;MAAAC,OAAA,GAAYjK,YAAY;IAAA,EAAI;EAAE;AACvC;AACA;EAAA,QAAA4H,SAAA,oBAAAA,SAAA,KAjEoGxI,EAAE,CAAAyI,iBAAA,CAiEX0B,UAAU,EAAc,CAAC;IACxG7H,IAAI,EAAEpC,QAAQ;IACdmC,IAAI,EAAE,CAAC;MACCwI,OAAO,EAAE,CAACjK,YAAY,CAAC;MACvB6J,SAAS,EAAE,CACP3I,eAAe,EACf;QACI4I,OAAO,EAAEtK,iBAAiB;QAC1BuK,QAAQ,EAAEjC,yBAAyB;QACnCkC,KAAK,EAAE;MACX,CAAC;IAET,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAME,qBAAqB,CAAC;EACxB;IAAS,IAAI,CAAC5C,IAAI,YAAA6C,8BAAA3C,CAAA;MAAA,YAAAA,CAAA,IAAwF0C,qBAAqB;IAAA,CAAkD;EAAE;EACnL;IAAS,IAAI,CAACT,IAAI,kBAlF8ErK,EAAE,CAAAsK,gBAAA;MAAAhI,IAAA,EAkFSwI;IAAqB,EAA0B;EAAE;EAC5J;IAAS,IAAI,CAACP,IAAI,kBAnF8EvK,EAAE,CAAAwK,gBAAA;MAAAK,OAAA,GAmF0CV,UAAU;IAAA,EAAI;EAAE;AAChK;AACA;EAAA,QAAA3B,SAAA,oBAAAA,SAAA,KArFoGxI,EAAE,CAAAyI,iBAAA,CAqFXqC,qBAAqB,EAAc,CAAC;IACnHxI,IAAI,EAAEpC,QAAQ;IACdmC,IAAI,EAAE,CAAC;MACCwI,OAAO,EAAE,CAACV,UAAU;IACxB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASA,UAAU,EAAEW,qBAAqB,EAAEhK,iBAAiB,EAAE4H,yBAAyB,EAAE7H,iBAAiB,EAAEiB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}