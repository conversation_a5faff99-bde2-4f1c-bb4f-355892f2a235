{"ast": null, "code": "import { candidatelist } from 'src/app/core/data';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/pagination.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../../../shared/breadcrumbs/breadcrumbs.component\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"bg-danger-subtle text-danger\": a0,\n  \"bg-success-subtle text-success\": a1,\n  \"bg-secondary-subtle text-secondary\": a2\n});\nfunction ListViewComponent_For_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 26)(2, \"div\", 27)(3, \"div\", 28)(4, \"div\", 29)(5, \"div\", 30);\n    i0.ɵɵelement(6, \"img\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 32)(8, \"a\", 33)(9, \"h5\", 34);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"p\", 35);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 36)(14, \"div\");\n    i0.ɵɵelement(15, \"i\", 37);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\");\n    i0.ɵɵelement(18, \"i\", 38);\n    i0.ɵɵelementStart(19, \"span\", 39);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 40)(22, \"div\", 41);\n    i0.ɵɵelement(23, \"i\", 42);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 43);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 44)(28, \"a\", 45);\n    i0.ɵɵtext(29, \"View Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"a\", 46)(31, \"span\", 47);\n    i0.ɵɵlistener(\"click\", function ListViewComponent_For_29_Template_span_click_31_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.bookmarklist($index_r2));\n    });\n    i0.ɵɵelement(32, \"i\", 48);\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const list_r4 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵpropertyInterpolate(\"src\", list_r4.img, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(list_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(list_r4.designation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", list_r4.location, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(9, _c0, list_r4.type == \"Part Time\", list_r4.type == \"Full Time\", list_r4.type == \"Freelancer\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(list_r4.type);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", list_r4.rating, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", list_r4.ratingCount, \" Ratings\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", list_r4.bookmark == true ? \"ri-bookmark-3-fill align-bottom\" : \"ri-bookmark-line align-bottom\");\n  }\n}\nfunction ListViewComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 49);\n    i0.ɵɵtext(1, \" Prev \");\n  }\n}\nfunction ListViewComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Next \");\n    i0.ɵɵelement(1, \"i\", 50);\n  }\n}\nexport class ListViewComponent {\n  constructor(service) {\n    this.service = service;\n    this.service.pageSize = 8;\n  }\n  ngOnInit() {\n    /**\n    * BreadCrumb\n    */\n    this.breadCrumbItems = [{\n      label: 'Candidate Lists'\n    }, {\n      label: 'List View',\n      active: true\n    }];\n    // Fetch Data\n    setTimeout(() => {\n      this.listview = this.service.changePage(candidatelist);\n      this.alllistview = candidatelist;\n      document.getElementById('elmLoader')?.classList.add('d-none');\n    }, 1200);\n  }\n  bookmarklist(id) {\n    if (this.listview[id].bookmark == true) {\n      this.listview[id].bookmark = false;\n    } else {\n      this.listview[id].bookmark = true;\n    }\n  }\n  // Pagination\n  changePage() {\n    this.listview = this.service.changePage(this.alllistview);\n  }\n  // Search Data\n  performSearch() {\n    this.searchResults = this.alllistview.filter(item => {\n      return item.name.toLowerCase().includes(this.searchTerm.toLowerCase()) || item.designation.toLowerCase().includes(this.searchTerm.toLowerCase()) || item.location.toLowerCase().includes(this.searchTerm.toLowerCase()) || item.type.toLowerCase().includes(this.searchTerm.toLowerCase()) || item.rating.toLowerCase().includes(this.searchTerm.toLowerCase()) || item.ratingCount.toLowerCase().includes(this.searchTerm.toLowerCase());\n    });\n    this.listview = this.service.changePage(this.searchResults);\n  }\n  static {\n    this.ɵfac = function ListViewComponent_Factory(t) {\n      return new (t || ListViewComponent)(i0.ɵɵdirectiveInject(i1.PaginationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ListViewComponent,\n      selectors: [[\"app-list-view\"]],\n      decls: 37,\n      vars: 5,\n      consts: [[\"title\", \"List View\", 3, \"breadcrumbItems\"], [1, \"row\", \"g-4\", \"mb-4\"], [1, \"col-sm\"], [\"href\", \"javascript:void(0);\", 1, \"btn\", \"btn-primary\"], [1, \"ri-add-line\", \"align-bottom\", \"me-1\"], [1, \"col-sm-auto\"], [1, \"d-md-flex\", \"justify-content-sm-end\", \"gap-2\"], [1, \"search-box\", \"ms-md-2\", \"flex-shrink-0\", \"mb-3\", \"mb-md-0\"], [\"type\", \"text\", \"id\", \"searchJob\", \"autocomplete\", \"off\", \"placeholder\", \"Search for candidate name or designation...\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"ri-search-line\", \"search-icon\"], [\"data-choices\", \"\", \"data-choices-search-false\", \"\", 1, \"form-control\", \"w-md\", \"choices__inner\"], [\"value\", \"All\"], [\"value\", \"Today\"], [\"value\", \"Yesterday\", \"selected\", \"\"], [\"value\", \"Last 7 Days\"], [\"value\", \"Last 30 Days\"], [\"value\", \"This Month\"], [\"value\", \"Last Year\"], [\"id\", \"candidate-list\", 1, \"row\", \"gy-2\", \"mb-2\"], [1, \"col-md-6\", \"col-lg-12\"], [\"aria-label\", \"Custom pagination\", 1, \"d-flex\", \"justify-content-end\", \"pt-2\", 3, \"pageChange\", \"collectionSize\", \"page\", \"pageSize\"], [\"ngbPaginationPrevious\", \"\"], [\"ngbPaginationNext\", \"\"], [\"id\", \"elmLoader\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"avatar-sm\"], [1, \"visually-hidden\"], [1, \"card\", \"mb-0\"], [1, \"card-body\"], [1, \"d-lg-flex\", \"align-items-center\"], [1, \"flex-shrink-0\"], [1, \"avatar-sm\", \"rounded\"], [\"alt\", \"\", 1, \"member-img\", \"img-fluid\", \"d-block\", \"rounded\", 3, \"src\"], [1, \"ms-lg-3\", \"my-3\", \"my-lg-0\"], [\"routerLink\", \"/pages/profile\"], [1, \"fs-16\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"], [1, \"d-flex\", \"gap-4\", \"mt-0\", \"text-muted\", \"mx-auto\"], [1, \"ri-map-pin-2-line\", \"text-primary\", \"me-1\", \"align-bottom\"], [1, \"ri-time-line\", \"text-primary\", \"me-1\", \"align-bottom\"], [1, \"badge\", 3, \"ngClass\"], [1, \"d-flex\", \"flex-wrap\", \"gap-2\", \"align-items-center\", \"mx-auto\", \"my-3\", \"my-lg-0\"], [1, \"badge\", \"text-bg-success\"], [1, \"mdi\", \"mdi-star\", \"me-1\"], [1, \"text-muted\"], [1, \"d-flex\", \"gap-1\", \"mt-0\"], [\"href\", \"javascript:void(0);\", 1, \"btn\", \"btn-soft-success\"], [\"href\", \"javascript:void(0);\", \"data-bs-toggle\", \"button\", 1, \"btn\", \"btn-ghost-danger\", \"btn-icon\", \"custom-toggle\", \"active\"], [1, \"icon-off\", 3, \"click\"], [3, \"ngClass\"], [1, \"ci-arrow-left\", \"me-2\"], [1, \"ci-arrow-right\", \"ms-2\"]],\n      template: function ListViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-breadcrumbs\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\")(4, \"a\", 3);\n          i0.ɵɵelement(5, \"i\", 4);\n          i0.ɵɵtext(6, \" Add Candidate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"div\", 7)(10, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ListViewComponent_Template_input_ngModelChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function ListViewComponent_Template_input_ngModelChange_10_listener() {\n            return ctx.performSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"select\", 10)(13, \"option\", 11);\n          i0.ɵɵtext(14, \"All\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"option\", 12);\n          i0.ɵɵtext(16, \"Today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"option\", 13);\n          i0.ɵɵtext(18, \"Yesterday\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"option\", 14);\n          i0.ɵɵtext(20, \"Last 7 Days\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"option\", 15);\n          i0.ɵɵtext(22, \"Last 30 Days\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"option\", 16);\n          i0.ɵɵtext(24, \"This Month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"option\", 17);\n          i0.ɵɵtext(26, \"Last Year\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(27, \"div\", 18);\n          i0.ɵɵrepeaterCreate(28, ListViewComponent_For_29_Template, 33, 13, \"div\", 19, i0.ɵɵrepeaterTrackByIndex);\n          i0.ɵɵelementStart(30, \"ngb-pagination\", 20);\n          i0.ɵɵtwoWayListener(\"pageChange\", function ListViewComponent_Template_ngb_pagination_pageChange_30_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.service.page, $event) || (ctx.service.page = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"pageChange\", function ListViewComponent_Template_ngb_pagination_pageChange_30_listener() {\n            return ctx.changePage();\n          });\n          i0.ɵɵtemplate(31, ListViewComponent_ng_template_31_Template, 2, 0, \"ng-template\", 21)(32, ListViewComponent_ng_template_32_Template, 2, 0, \"ng-template\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 23)(34, \"div\", 24)(35, \"span\", 25);\n          i0.ɵɵtext(36, \"Loading...\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"breadcrumbItems\", ctx.breadCrumbItems);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(18);\n          i0.ɵɵrepeater(ctx.listview);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"collectionSize\", ctx.alllistview == null ? null : ctx.alllistview.length);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.service.page);\n          i0.ɵɵproperty(\"pageSize\", ctx.service.pageSize);\n        }\n      },\n      dependencies: [i2.NgClass, i3.RouterLink, i4.BreadcrumbsComponent, i5.NgbPagination, i5.NgbPaginationNext, i5.NgbPaginationPrevious, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["candidatelist", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "ListViewComponent_For_29_Template_span_click_31_listener", "$index_r2", "ɵɵrestoreView", "_r1", "$index", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "bookmarklist", "ɵɵadvance", "ɵɵpropertyInterpolate", "list_r4", "img", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "name", "designation", "ɵɵtextInterpolate1", "location", "ɵɵproperty", "ɵɵpureFunction3", "_c0", "type", "rating", "ratingCount", "bookmark", "ListViewComponent", "constructor", "service", "pageSize", "ngOnInit", "breadCrumbItems", "label", "active", "setTimeout", "listview", "changePage", "alllistview", "document", "getElementById", "classList", "add", "id", "performSearch", "searchResults", "filter", "item", "toLowerCase", "includes", "searchTerm", "ɵɵdirectiveInject", "i1", "PaginationService", "selectors", "decls", "vars", "consts", "template", "ListViewComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "ListViewComponent_Template_input_ngModelChange_10_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵrepeaterCreate", "ListViewComponent_For_29_Template", "ɵɵrepeaterTrackByIndex", "ListViewComponent_Template_ngb_pagination_pageChange_30_listener", "page", "ɵɵtemplate", "ListViewComponent_ng_template_31_Template", "ListViewComponent_ng_template_32_Template", "ɵɵtwoWayProperty", "ɵɵrepeater", "length"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\candidate-lists\\list-view\\list-view.component.ts", "C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\candidate-lists\\list-view\\list-view.component.html"], "sourcesContent": ["import { DecimalPipe } from '@angular/common';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { candidatelist } from 'src/app/core/data';\r\n\r\n// Data Get\r\nimport { PaginationService } from 'src/app/core/services/pagination.service';\r\n\r\n\r\n@Component({\r\n  selector: 'app-list-view',\r\n  templateUrl: './list-view.component.html',\r\n  styleUrls: ['./list-view.component.scss']\r\n})\r\nexport class ListViewComponent implements OnInit {\r\n\r\n  // bread crumb items\r\n  breadCrumbItems!: Array<{}>;\r\n  listview: any;\r\n  alllistview: any;\r\n  searchResults: any;\r\n  searchTerm: any;\r\n\r\n  constructor(public service: PaginationService) {\r\n    this.service.pageSize = 8\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    /**\r\n* BreadCrumb\r\n*/\r\n    this.breadCrumbItems = [\r\n      { label: 'Candidate Lists' },\r\n      { label: 'List View', active: true }\r\n    ];\r\n\r\n    // Fetch Data\r\n    setTimeout(() => {\r\n      this.listview = this.service.changePage(candidatelist);\r\n      this.alllistview = candidatelist;\r\n      document.getElementById('elmLoader')?.classList.add('d-none')\r\n    }, 1200)\r\n  }\r\n\r\n  bookmarklist(id: any) {\r\n    if (this.listview[id].bookmark == true) {\r\n      this.listview[id].bookmark = false\r\n    } else {\r\n      this.listview[id].bookmark = true\r\n    }\r\n  }\r\n\r\n  // Pagination\r\n  changePage() {\r\n    this.listview = this.service.changePage(this.alllistview)\r\n  }\r\n\r\n\r\n  // Search Data\r\n  performSearch(): void {\r\n    this.searchResults = this.alllistview.filter((item: any) => {\r\n      return (\r\n        item.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\r\n        item.designation.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\r\n        item.location.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\r\n        item.type.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\r\n        item.rating.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\r\n        item.ratingCount.toLowerCase().includes(this.searchTerm.toLowerCase())\r\n      );\r\n    });\r\n    this.listview = this.service.changePage(this.searchResults)\r\n  }\r\n\r\n}\r\n", "<!-- Start Breadcrumbs -->\r\n<app-breadcrumbs title=\"List View\" [breadcrumbItems]=\"breadCrumbItems\"></app-breadcrumbs>\r\n<!-- End Breadcrumbs -->\r\n<div class=\"row g-4 mb-4\">\r\n    <div class=\"col-sm\">\r\n        <div>\r\n            <a href=\"javascript:void(0);\" class=\"btn btn-primary\"><i class=\"ri-add-line align-bottom me-1\"></i> Add\r\n                Candidate</a>\r\n        </div>\r\n    </div>\r\n    <div class=\"col-sm-auto\">\r\n        <div class=\"d-md-flex justify-content-sm-end gap-2\">\r\n            <div class=\"search-box ms-md-2 flex-shrink-0 mb-3 mb-md-0\">\r\n                <input type=\"text\" class=\"form-control\" id=\"searchJob\" autocomplete=\"off\" placeholder=\"Search for candidate name or designation...\" [(ngModel)]=\"searchTerm\" (ngModelChange)=\"performSearch()\">\r\n                <i class=\"ri-search-line search-icon\"></i>\r\n            </div>\r\n\r\n            <select class=\"form-control w-md choices__inner\" data-choices data-choices-search-false>\r\n                <option value=\"All\">All</option>\r\n                <option value=\"Today\">Today</option>\r\n                <option value=\"Yesterday\" selected>Yesterday</option>\r\n                <option value=\"Last 7 Days\">Last 7 Days</option>\r\n                <option value=\"Last 30 Days\">Last 30 Days</option>\r\n                <option value=\"This Month\">This Month</option>\r\n                <option value=\"Last Year\">Last Year</option>\r\n            </select>\r\n        </div>\r\n    </div>\r\n</div>\r\n<div class=\"row gy-2 mb-2\" id=\"candidate-list\">\r\n    @for(list of listview;track $index){\r\n    <div class=\"col-md-6 col-lg-12\">\r\n        <div class=\"card mb-0\">\r\n            <div class=\"card-body\">\r\n                <div class=\"d-lg-flex align-items-center\">\r\n                    <div class=\"flex-shrink-0\">\r\n                        <div class=\"avatar-sm rounded\"><img src=\"{{list.img}}\" alt=\"\" class=\"member-img img-fluid d-block rounded\"></div>\r\n                    </div>\r\n                    <div class=\"ms-lg-3 my-3 my-lg-0\"> <a routerLink=\"/pages/profile\">\r\n                            <h5 class=\"fs-16 mb-2\">{{list.name}}</h5>\r\n                        </a>\r\n                        <p class=\"text-muted mb-0\">{{list.designation}}</p>\r\n                    </div>\r\n                    <div class=\"d-flex gap-4 mt-0 text-muted mx-auto\">\r\n                        <div><i class=\"ri-map-pin-2-line text-primary me-1 align-bottom\"></i> {{list.location}}</div>\r\n                        <div><i class=\"ri-time-line text-primary me-1 align-bottom\"></i>\r\n                            <span class=\"badge\" [ngClass]=\"{'bg-danger-subtle text-danger': list.type == 'Part Time' ,\r\n                            'bg-success-subtle text-success': list.type == 'Full Time',\r\n                            'bg-secondary-subtle text-secondary': list.type == 'Freelancer' }\">{{list.type}}</span>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"d-flex flex-wrap gap-2 align-items-center mx-auto my-3 my-lg-0\">\r\n                        <div class=\"badge text-bg-success\"> <i class=\"mdi mdi-star me-1\"></i>{{list.rating}} </div>\r\n                        <div class=\"text-muted\">{{list.ratingCount}} Ratings</div>\r\n                    </div>\r\n                    <div class=\"d-flex gap-1 mt-0 \">\r\n                        <a href=\"javascript:void(0);\" class=\"btn btn-soft-success\">View Details</a> <a href=\"javascript:void(0);\" class=\"btn btn-ghost-danger btn-icon custom-toggle active\" data-bs-toggle=\"button\">\r\n                            <!-- <span class=\"icon-on\"><i class=\"ri-bookmark-line align-bottom\"></i></span>\r\n                            <span class=\"icon-off\"><i class=\"ri-bookmark-3-fill align-bottom\"></i></span> -->\r\n                            <span class=\"icon-off\" (click)=\"bookmarklist($index)\">\r\n                                <i [ngClass]=\"(list.bookmark == true)?'ri-bookmark-3-fill align-bottom':'ri-bookmark-line align-bottom'\"></i>\r\n                            </span>\r\n                        </a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    }\r\n\r\n    <!-- pagination -->\r\n    <ngb-pagination class=\"d-flex justify-content-end pt-2\" [collectionSize]=\"alllistview?.length\" [(page)]=\"service.page\" [pageSize]=\"service.pageSize\" (pageChange)=\"changePage()\" aria-label=\"Custom pagination\">\r\n        <ng-template ngbPaginationPrevious let-page let-pages=\"pages\">\r\n            <i class=\"ci-arrow-left me-2\"></i>\r\n            Prev\r\n        </ng-template>\r\n        <ng-template ngbPaginationNext>\r\n            Next\r\n            <i class=\"ci-arrow-right ms-2\"></i>\r\n        </ng-template>\r\n    </ngb-pagination>\r\n\r\n\r\n    <div id=\"elmLoader\">\r\n        <div class=\"spinner-border text-primary avatar-sm\" role=\"status\">\r\n            <span class=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,aAAa,QAAQ,mBAAmB;;;;;;;;;;;;;;;;ICkCzBC,EALpB,CAAAC,cAAA,cAAgC,cACL,cACI,cACuB,cACX,cACQ;IAAAD,EAAA,CAAAE,SAAA,cAA4E;IAC/GF,EAD+G,CAAAG,YAAA,EAAM,EAC/G;IAEEH,EADR,CAAAC,cAAA,cAAkC,YAAgC,aACnC;IAAAD,EAAA,CAAAI,MAAA,IAAa;IACxCJ,EADwC,CAAAG,YAAA,EAAK,EACzC;IACJH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAI,MAAA,IAAoB;IACnDJ,EADmD,CAAAG,YAAA,EAAI,EACjD;IAEFH,EADJ,CAAAC,cAAA,eAAkD,WACzC;IAAAD,EAAA,CAAAE,SAAA,aAAgE;IAACF,EAAA,CAAAI,MAAA,IAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC7FH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,SAAA,aAA2D;IAC5DF,EAAA,CAAAC,cAAA,gBAEmE;IAAAD,EAAA,CAAAI,MAAA,IAAa;IAExFJ,EAFwF,CAAAG,YAAA,EAAO,EACrF,EACJ;IAEFH,EADJ,CAAAC,cAAA,eAA4E,eACrC;IAACD,EAAA,CAAAE,SAAA,aAAiC;IAAAF,EAAA,CAAAI,MAAA,IAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC3FH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAI,MAAA,IAA4B;IACxDJ,EADwD,CAAAG,YAAA,EAAM,EACxD;IAEFH,EADJ,CAAAC,cAAA,eAAgC,aAC+B;IAAAD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGvEH,EAHwE,CAAAC,cAAA,aAAiH,gBAGnI;IAA/BD,EAAA,CAAAK,UAAA,mBAAAC,yDAAA;MAAA,MAAAC,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAP,SAAA,CAAoB;IAAA,EAAC;IACjDP,EAAA,CAAAE,SAAA,aAA6G;IAOzIF,EANwB,CAAAG,YAAA,EAAO,EACP,EACF,EACJ,EACJ,EACJ,EACJ;;;;IA/BkDH,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,qBAAA,QAAAC,OAAA,CAAAC,GAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAkB;IAG3BnB,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAI,IAAA,CAAa;IAEbrB,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAK,WAAA,CAAoB;IAGuBtB,EAAA,CAAAe,SAAA,GAAiB;IAAjBf,EAAA,CAAAuB,kBAAA,MAAAN,OAAA,CAAAO,QAAA,KAAiB;IAE/DxB,EAAA,CAAAe,SAAA,GAE8C;IAF9Cf,EAAA,CAAAyB,UAAA,YAAAzB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAAV,OAAA,CAAAW,IAAA,iBAAAX,OAAA,CAAAW,IAAA,iBAAAX,OAAA,CAAAW,IAAA,kBAE8C;IAAC5B,EAAA,CAAAe,SAAA,EAAa;IAAbf,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAW,IAAA,CAAa;IAIf5B,EAAA,CAAAe,SAAA,GAAgB;IAAhBf,EAAA,CAAAuB,kBAAA,KAAAN,OAAA,CAAAY,MAAA,MAAgB;IAC7D7B,EAAA,CAAAe,SAAA,GAA4B;IAA5Bf,EAAA,CAAAuB,kBAAA,KAAAN,OAAA,CAAAa,WAAA,aAA4B;IAOzC9B,EAAA,CAAAe,SAAA,GAAqG;IAArGf,EAAA,CAAAyB,UAAA,YAAAR,OAAA,CAAAc,QAAA,+EAAqG;;;;;IAa5H/B,EAAA,CAAAE,SAAA,YAAkC;IAClCF,EAAA,CAAAI,MAAA,aACJ;;;;;IAEIJ,EAAA,CAAAI,MAAA,aACA;IAAAJ,EAAA,CAAAE,SAAA,YAAmC;;;ADjE/C,OAAM,MAAO8B,iBAAiB;EAS5BC,YAAmBC,OAA0B;IAA1B,KAAAA,OAAO,GAAPA,OAAO;IACxB,IAAI,CAACA,OAAO,CAACC,QAAQ,GAAG,CAAC;EAC3B;EAEAC,QAAQA,CAAA;IACN;;;IAGA,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE;IAAiB,CAAE,EAC5B;MAAEA,KAAK,EAAE,WAAW;MAAEC,MAAM,EAAE;IAAI,CAAE,CACrC;IAED;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACP,OAAO,CAACQ,UAAU,CAAC3C,aAAa,CAAC;MACtD,IAAI,CAAC4C,WAAW,GAAG5C,aAAa;MAChC6C,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC,EAAEC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;IAC/D,CAAC,EAAE,IAAI,CAAC;EACV;EAEAjC,YAAYA,CAACkC,EAAO;IAClB,IAAI,IAAI,CAACP,QAAQ,CAACO,EAAE,CAAC,CAACjB,QAAQ,IAAI,IAAI,EAAE;MACtC,IAAI,CAACU,QAAQ,CAACO,EAAE,CAAC,CAACjB,QAAQ,GAAG,KAAK;IACpC,CAAC,MAAM;MACL,IAAI,CAACU,QAAQ,CAACO,EAAE,CAAC,CAACjB,QAAQ,GAAG,IAAI;IACnC;EACF;EAEA;EACAW,UAAUA,CAAA;IACR,IAAI,CAACD,QAAQ,GAAG,IAAI,CAACP,OAAO,CAACQ,UAAU,CAAC,IAAI,CAACC,WAAW,CAAC;EAC3D;EAGA;EACAM,aAAaA,CAAA;IACX,IAAI,CAACC,aAAa,GAAG,IAAI,CAACP,WAAW,CAACQ,MAAM,CAAEC,IAAS,IAAI;MACzD,OACEA,IAAI,CAAC/B,IAAI,CAACgC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACC,UAAU,CAACF,WAAW,EAAE,CAAC,IAC/DD,IAAI,CAAC9B,WAAW,CAAC+B,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACC,UAAU,CAACF,WAAW,EAAE,CAAC,IACtED,IAAI,CAAC5B,QAAQ,CAAC6B,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACC,UAAU,CAACF,WAAW,EAAE,CAAC,IACnED,IAAI,CAACxB,IAAI,CAACyB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACC,UAAU,CAACF,WAAW,EAAE,CAAC,IAC/DD,IAAI,CAACvB,MAAM,CAACwB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACC,UAAU,CAACF,WAAW,EAAE,CAAC,IACjED,IAAI,CAACtB,WAAW,CAACuB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACC,UAAU,CAACF,WAAW,EAAE,CAAC;IAE1E,CAAC,CAAC;IACF,IAAI,CAACZ,QAAQ,GAAG,IAAI,CAACP,OAAO,CAACQ,UAAU,CAAC,IAAI,CAACQ,aAAa,CAAC;EAC7D;;;uBAzDWlB,iBAAiB,EAAAhC,EAAA,CAAAwD,iBAAA,CAAAC,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAjB1B,iBAAiB;MAAA2B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ9BjE,EAAA,CAAAE,SAAA,yBAAyF;UAK7EF,EAHZ,CAAAC,cAAA,aAA0B,aACF,UACX,WACqD;UAAAD,EAAA,CAAAE,SAAA,WAA6C;UAACF,EAAA,CAAAI,MAAA,qBACvF;UAErBJ,EAFqB,CAAAG,YAAA,EAAI,EACf,EACJ;UAIMH,EAHZ,CAAAC,cAAA,aAAyB,aAC+B,aACW,gBACwI;UAA3DD,EAAA,CAAAmE,gBAAA,2BAAAC,2DAAAC,MAAA;YAAArE,EAAA,CAAAsE,kBAAA,CAAAJ,GAAA,CAAAX,UAAA,EAAAc,MAAA,MAAAH,GAAA,CAAAX,UAAA,GAAAc,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAACrE,EAAA,CAAAK,UAAA,2BAAA+D,2DAAA;YAAA,OAAiBF,GAAA,CAAAjB,aAAA,EAAe;UAAA,EAAC;UAA9LjD,EAAA,CAAAG,YAAA,EAA+L;UAC/LH,EAAA,CAAAE,SAAA,YAA0C;UAC9CF,EAAA,CAAAG,YAAA,EAAM;UAGFH,EADJ,CAAAC,cAAA,kBAAwF,kBAChE;UAAAD,EAAA,CAAAI,MAAA,WAAG;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAChCH,EAAA,CAAAC,cAAA,kBAAsB;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UACpCH,EAAA,CAAAC,cAAA,kBAAmC;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UACrDH,EAAA,CAAAC,cAAA,kBAA4B;UAAAD,EAAA,CAAAI,MAAA,mBAAW;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAChDH,EAAA,CAAAC,cAAA,kBAA6B;UAAAD,EAAA,CAAAI,MAAA,oBAAY;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAClDH,EAAA,CAAAC,cAAA,kBAA2B;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAC9CH,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAInDJ,EAJmD,CAAAG,YAAA,EAAS,EACvC,EACP,EACJ,EACJ;UACNH,EAAA,CAAAC,cAAA,eAA+C;UAC3CD,EAAA,CAAAuE,gBAAA,KAAAC,iCAAA,qBAAAxE,EAAA,CAAAyE,sBAAA,CAsCC;UAGDzE,EAAA,CAAAC,cAAA,0BAAgN;UAAjHD,EAAA,CAAAmE,gBAAA,wBAAAO,iEAAAL,MAAA;YAAArE,EAAA,CAAAsE,kBAAA,CAAAJ,GAAA,CAAAhC,OAAA,CAAAyC,IAAA,EAAAN,MAAA,MAAAH,GAAA,CAAAhC,OAAA,CAAAyC,IAAA,GAAAN,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAuB;UAA+BrE,EAAA,CAAAK,UAAA,wBAAAqE,iEAAA;YAAA,OAAcR,GAAA,CAAAxB,UAAA,EAAY;UAAA,EAAC;UAK5K1C,EAJA,CAAA4E,UAAA,KAAAC,yCAAA,0BAA8D,KAAAC,yCAAA,0BAI/B;UAInC9E,EAAA,CAAAG,YAAA,EAAiB;UAKTH,EAFR,CAAAC,cAAA,eAAoB,eACiD,gBAC/B;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAGpDJ,EAHoD,CAAAG,YAAA,EAAO,EAC7C,EACJ,EACJ;;;UAvF6BH,EAAA,CAAAyB,UAAA,oBAAAyC,GAAA,CAAA7B,eAAA,CAAmC;UAY8ErC,EAAA,CAAAe,SAAA,IAAwB;UAAxBf,EAAA,CAAA+E,gBAAA,YAAAb,GAAA,CAAAX,UAAA,CAAwB;UAiBxKvD,EAAA,CAAAe,SAAA,IAsCC;UAtCDf,EAAA,CAAAgF,UAAA,CAAAd,GAAA,CAAAzB,QAAA,CAsCC;UAGuDzC,EAAA,CAAAe,SAAA,GAAsC;UAAtCf,EAAA,CAAAyB,UAAA,mBAAAyC,GAAA,CAAAvB,WAAA,kBAAAuB,GAAA,CAAAvB,WAAA,CAAAsC,MAAA,CAAsC;UAACjF,EAAA,CAAA+E,gBAAA,SAAAb,GAAA,CAAAhC,OAAA,CAAAyC,IAAA,CAAuB;UAAC3E,EAAA,CAAAyB,UAAA,aAAAyC,GAAA,CAAAhC,OAAA,CAAAC,QAAA,CAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}