{"ast": null, "code": "import memoizeCapped from './_memoizeCapped.js';\n\n/** Used to match property names within property paths. */\nvar rePropName = /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g;\n\n/** Used to match backslashes in property paths. */\nvar reEscapeChar = /\\\\(\\\\)?/g;\n\n/**\n * Converts `string` to a property path array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the property path array.\n */\nvar stringToPath = memoizeCapped(function (string) {\n  var result = [];\n  if (string.charCodeAt(0) === 46 /* . */) {\n    result.push('');\n  }\n  string.replace(rePropName, function (match, number, quote, subString) {\n    result.push(quote ? subString.replace(reEscapeChar, '$1') : number || match);\n  });\n  return result;\n});\nexport default stringToPath;", "map": {"version": 3, "names": ["memoizeCapped", "rePropName", "reEscapeChar", "stringToPath", "string", "result", "charCodeAt", "push", "replace", "match", "number", "quote", "subString"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_stringToPath.js"], "sourcesContent": ["import memoizeCapped from './_memoizeCapped.js';\n\n/** Used to match property names within property paths. */\nvar rePropName = /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g;\n\n/** Used to match backslashes in property paths. */\nvar reEscapeChar = /\\\\(\\\\)?/g;\n\n/**\n * Converts `string` to a property path array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the property path array.\n */\nvar stringToPath = memoizeCapped(function(string) {\n  var result = [];\n  if (string.charCodeAt(0) === 46 /* . */) {\n    result.push('');\n  }\n  string.replace(rePropName, function(match, number, quote, subString) {\n    result.push(quote ? subString.replace(reEscapeChar, '$1') : (number || match));\n  });\n  return result;\n});\n\nexport default stringToPath;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;;AAE/C;AACA,IAAIC,UAAU,GAAG,kGAAkG;;AAEnH;AACA,IAAIC,YAAY,GAAG,UAAU;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAGH,aAAa,CAAC,UAASI,MAAM,EAAE;EAChD,IAAIC,MAAM,GAAG,EAAE;EACf,IAAID,MAAM,CAACE,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS;IACvCD,MAAM,CAACE,IAAI,CAAC,EAAE,CAAC;EACjB;EACAH,MAAM,CAACI,OAAO,CAACP,UAAU,EAAE,UAASQ,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAE;IACnEP,MAAM,CAACE,IAAI,CAACI,KAAK,GAAGC,SAAS,CAACJ,OAAO,CAACN,YAAY,EAAE,IAAI,CAAC,GAAIQ,MAAM,IAAID,KAAM,CAAC;EAChF,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf,CAAC,CAAC;AAEF,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}