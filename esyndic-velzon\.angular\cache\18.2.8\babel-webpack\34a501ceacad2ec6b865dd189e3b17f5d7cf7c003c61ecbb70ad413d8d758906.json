{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport { getDataItemValue } from '../../util/model.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nimport { getCoordSysInfoBySeries } from '../../model/referHelper.js';\nimport { createSourceFromSeriesDataOption } from '../../data/Source.js';\nimport { enableDataStack } from '../../data/helper/dataStackHelper.js';\nimport { makeSeriesEncodeForAxisCoordSys } from '../../data/helper/sourceHelper.js';\nimport { SOURCE_FORMAT_ORIGINAL } from '../../util/types.js';\nfunction getCoordSysDimDefs(seriesModel, coordSysInfo) {\n  var coordSysName = seriesModel.get('coordinateSystem');\n  var registeredCoordSys = CoordinateSystem.get(coordSysName);\n  var coordSysDimDefs;\n  if (coordSysInfo && coordSysInfo.coordSysDims) {\n    coordSysDimDefs = zrUtil.map(coordSysInfo.coordSysDims, function (dim) {\n      var dimInfo = {\n        name: dim\n      };\n      var axisModel = coordSysInfo.axisMap.get(dim);\n      if (axisModel) {\n        var axisType = axisModel.get('type');\n        dimInfo.type = getDimensionTypeByAxis(axisType);\n      }\n      return dimInfo;\n    });\n  }\n  if (!coordSysDimDefs) {\n    // Get dimensions from registered coordinate system\n    coordSysDimDefs = registeredCoordSys && (registeredCoordSys.getDimensionsInfo ? registeredCoordSys.getDimensionsInfo() : registeredCoordSys.dimensions.slice()) || ['x', 'y'];\n  }\n  return coordSysDimDefs;\n}\nfunction injectOrdinalMeta(dimInfoList, createInvertedIndices, coordSysInfo) {\n  var firstCategoryDimIndex;\n  var hasNameEncode;\n  coordSysInfo && zrUtil.each(dimInfoList, function (dimInfo, dimIndex) {\n    var coordDim = dimInfo.coordDim;\n    var categoryAxisModel = coordSysInfo.categoryAxisMap.get(coordDim);\n    if (categoryAxisModel) {\n      if (firstCategoryDimIndex == null) {\n        firstCategoryDimIndex = dimIndex;\n      }\n      dimInfo.ordinalMeta = categoryAxisModel.getOrdinalMeta();\n      if (createInvertedIndices) {\n        dimInfo.createInvertedIndices = true;\n      }\n    }\n    if (dimInfo.otherDims.itemName != null) {\n      hasNameEncode = true;\n    }\n  });\n  if (!hasNameEncode && firstCategoryDimIndex != null) {\n    dimInfoList[firstCategoryDimIndex].otherDims.itemName = 0;\n  }\n  return firstCategoryDimIndex;\n}\n/**\n * Caution: there are side effects to `sourceManager` in this method.\n * Should better only be called in `Series['getInitialData']`.\n */\nfunction createSeriesData(sourceRaw, seriesModel, opt) {\n  opt = opt || {};\n  var sourceManager = seriesModel.getSourceManager();\n  var source;\n  var isOriginalSource = false;\n  if (sourceRaw) {\n    isOriginalSource = true;\n    source = createSourceFromSeriesDataOption(sourceRaw);\n  } else {\n    source = sourceManager.getSource();\n    // Is series.data. not dataset.\n    isOriginalSource = source.sourceFormat === SOURCE_FORMAT_ORIGINAL;\n  }\n  var coordSysInfo = getCoordSysInfoBySeries(seriesModel);\n  var coordSysDimDefs = getCoordSysDimDefs(seriesModel, coordSysInfo);\n  var useEncodeDefaulter = opt.useEncodeDefaulter;\n  var encodeDefaulter = zrUtil.isFunction(useEncodeDefaulter) ? useEncodeDefaulter : useEncodeDefaulter ? zrUtil.curry(makeSeriesEncodeForAxisCoordSys, coordSysDimDefs, seriesModel) : null;\n  var createDimensionOptions = {\n    coordDimensions: coordSysDimDefs,\n    generateCoord: opt.generateCoord,\n    encodeDefine: seriesModel.getEncode(),\n    encodeDefaulter: encodeDefaulter,\n    canOmitUnusedDimensions: !isOriginalSource\n  };\n  var schema = prepareSeriesDataSchema(source, createDimensionOptions);\n  var firstCategoryDimIndex = injectOrdinalMeta(schema.dimensions, opt.createInvertedIndices, coordSysInfo);\n  var store = !isOriginalSource ? sourceManager.getSharedDataStore(schema) : null;\n  var stackCalculationInfo = enableDataStack(seriesModel, {\n    schema: schema,\n    store: store\n  });\n  var data = new SeriesData(schema, seriesModel);\n  data.setCalculationInfo(stackCalculationInfo);\n  var dimValueGetter = firstCategoryDimIndex != null && isNeedCompleteOrdinalData(source) ? function (itemOpt, dimName, dataIndex, dimIndex) {\n    // Use dataIndex as ordinal value in categoryAxis\n    return dimIndex === firstCategoryDimIndex ? dataIndex : this.defaultDimValueGetter(itemOpt, dimName, dataIndex, dimIndex);\n  } : null;\n  data.hasItemOption = false;\n  data.initData(\n  // Try to reuse the data store in sourceManager if using dataset.\n  isOriginalSource ? source : store, null, dimValueGetter);\n  return data;\n}\nfunction isNeedCompleteOrdinalData(source) {\n  if (source.sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var sampleItem = firstDataNotNull(source.data || []);\n    return !zrUtil.isArray(getDataItemValue(sampleItem));\n  }\n}\nfunction firstDataNotNull(arr) {\n  var i = 0;\n  while (i < arr.length && arr[i] == null) {\n    i++;\n  }\n  return arr[i];\n}\nexport default createSeriesData;", "map": {"version": 3, "names": ["zrUtil", "SeriesData", "prepareSeriesDataSchema", "getDimensionTypeByAxis", "getDataItemValue", "CoordinateSystem", "getCoordSysInfoBySeries", "createSourceFromSeriesDataOption", "enableDataStack", "makeSeriesEncodeForAxisCoordSys", "SOURCE_FORMAT_ORIGINAL", "getCoordSysDimDefs", "seriesModel", "coordSysInfo", "coordSysName", "get", "registeredCoordSys", "coordSysDimDefs", "coordSysDims", "map", "dim", "dimInfo", "name", "axisModel", "axisMap", "axisType", "type", "getDimensionsInfo", "dimensions", "slice", "injectOrdinalMeta", "dimInfoList", "createInvertedIndices", "firstCategoryDimIndex", "hasNameEncode", "each", "dimIndex", "coordDim", "categoryAxisModel", "categoryAxisMap", "ordinalMeta", "getOrdinalMeta", "otherDims", "itemName", "createSeriesData", "sourceRaw", "opt", "sourceManager", "getSourceManager", "source", "isOriginalSource", "getSource", "sourceFormat", "useEncodeDefaulter", "encodeDefaulter", "isFunction", "curry", "createDimensionOptions", "coordDimensions", "generateCoord", "encodeDefine", "getEncode", "canOmitUnusedDimensions", "schema", "store", "getSharedDataStore", "stackCalculationInfo", "data", "setCalculationInfo", "dimValueGetter", "isNeedCompleteOrdinalData", "itemOpt", "dimName", "dataIndex", "defaultDimValueGetter", "hasItemOption", "initData", "sampleItem", "firstDataNotNull", "isArray", "arr", "i", "length"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/chart/helper/createSeriesData.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport { getDataItemValue } from '../../util/model.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nimport { getCoordSysInfoBySeries } from '../../model/referHelper.js';\nimport { createSourceFromSeriesDataOption } from '../../data/Source.js';\nimport { enableDataStack } from '../../data/helper/dataStackHelper.js';\nimport { makeSeriesEncodeForAxisCoordSys } from '../../data/helper/sourceHelper.js';\nimport { SOURCE_FORMAT_ORIGINAL } from '../../util/types.js';\nfunction getCoordSysDimDefs(seriesModel, coordSysInfo) {\n  var coordSysName = seriesModel.get('coordinateSystem');\n  var registeredCoordSys = CoordinateSystem.get(coordSysName);\n  var coordSysDimDefs;\n  if (coordSysInfo && coordSysInfo.coordSysDims) {\n    coordSysDimDefs = zrUtil.map(coordSysInfo.coordSysDims, function (dim) {\n      var dimInfo = {\n        name: dim\n      };\n      var axisModel = coordSysInfo.axisMap.get(dim);\n      if (axisModel) {\n        var axisType = axisModel.get('type');\n        dimInfo.type = getDimensionTypeByAxis(axisType);\n      }\n      return dimInfo;\n    });\n  }\n  if (!coordSysDimDefs) {\n    // Get dimensions from registered coordinate system\n    coordSysDimDefs = registeredCoordSys && (registeredCoordSys.getDimensionsInfo ? registeredCoordSys.getDimensionsInfo() : registeredCoordSys.dimensions.slice()) || ['x', 'y'];\n  }\n  return coordSysDimDefs;\n}\nfunction injectOrdinalMeta(dimInfoList, createInvertedIndices, coordSysInfo) {\n  var firstCategoryDimIndex;\n  var hasNameEncode;\n  coordSysInfo && zrUtil.each(dimInfoList, function (dimInfo, dimIndex) {\n    var coordDim = dimInfo.coordDim;\n    var categoryAxisModel = coordSysInfo.categoryAxisMap.get(coordDim);\n    if (categoryAxisModel) {\n      if (firstCategoryDimIndex == null) {\n        firstCategoryDimIndex = dimIndex;\n      }\n      dimInfo.ordinalMeta = categoryAxisModel.getOrdinalMeta();\n      if (createInvertedIndices) {\n        dimInfo.createInvertedIndices = true;\n      }\n    }\n    if (dimInfo.otherDims.itemName != null) {\n      hasNameEncode = true;\n    }\n  });\n  if (!hasNameEncode && firstCategoryDimIndex != null) {\n    dimInfoList[firstCategoryDimIndex].otherDims.itemName = 0;\n  }\n  return firstCategoryDimIndex;\n}\n/**\n * Caution: there are side effects to `sourceManager` in this method.\n * Should better only be called in `Series['getInitialData']`.\n */\nfunction createSeriesData(sourceRaw, seriesModel, opt) {\n  opt = opt || {};\n  var sourceManager = seriesModel.getSourceManager();\n  var source;\n  var isOriginalSource = false;\n  if (sourceRaw) {\n    isOriginalSource = true;\n    source = createSourceFromSeriesDataOption(sourceRaw);\n  } else {\n    source = sourceManager.getSource();\n    // Is series.data. not dataset.\n    isOriginalSource = source.sourceFormat === SOURCE_FORMAT_ORIGINAL;\n  }\n  var coordSysInfo = getCoordSysInfoBySeries(seriesModel);\n  var coordSysDimDefs = getCoordSysDimDefs(seriesModel, coordSysInfo);\n  var useEncodeDefaulter = opt.useEncodeDefaulter;\n  var encodeDefaulter = zrUtil.isFunction(useEncodeDefaulter) ? useEncodeDefaulter : useEncodeDefaulter ? zrUtil.curry(makeSeriesEncodeForAxisCoordSys, coordSysDimDefs, seriesModel) : null;\n  var createDimensionOptions = {\n    coordDimensions: coordSysDimDefs,\n    generateCoord: opt.generateCoord,\n    encodeDefine: seriesModel.getEncode(),\n    encodeDefaulter: encodeDefaulter,\n    canOmitUnusedDimensions: !isOriginalSource\n  };\n  var schema = prepareSeriesDataSchema(source, createDimensionOptions);\n  var firstCategoryDimIndex = injectOrdinalMeta(schema.dimensions, opt.createInvertedIndices, coordSysInfo);\n  var store = !isOriginalSource ? sourceManager.getSharedDataStore(schema) : null;\n  var stackCalculationInfo = enableDataStack(seriesModel, {\n    schema: schema,\n    store: store\n  });\n  var data = new SeriesData(schema, seriesModel);\n  data.setCalculationInfo(stackCalculationInfo);\n  var dimValueGetter = firstCategoryDimIndex != null && isNeedCompleteOrdinalData(source) ? function (itemOpt, dimName, dataIndex, dimIndex) {\n    // Use dataIndex as ordinal value in categoryAxis\n    return dimIndex === firstCategoryDimIndex ? dataIndex : this.defaultDimValueGetter(itemOpt, dimName, dataIndex, dimIndex);\n  } : null;\n  data.hasItemOption = false;\n  data.initData(\n  // Try to reuse the data store in sourceManager if using dataset.\n  isOriginalSource ? source : store, null, dimValueGetter);\n  return data;\n}\nfunction isNeedCompleteOrdinalData(source) {\n  if (source.sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var sampleItem = firstDataNotNull(source.data || []);\n    return !zrUtil.isArray(getDataItemValue(sampleItem));\n  }\n}\nfunction firstDataNotNull(arr) {\n  var i = 0;\n  while (i < arr.length && arr[i] == null) {\n    i++;\n  }\n  return arr[i];\n}\nexport default createSeriesData;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,uBAAuB,MAAM,uCAAuC;AAC3E,SAASC,sBAAsB,QAAQ,sCAAsC;AAC7E,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,SAASC,gCAAgC,QAAQ,sBAAsB;AACvE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,+BAA+B,QAAQ,mCAAmC;AACnF,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,kBAAkBA,CAACC,WAAW,EAAEC,YAAY,EAAE;EACrD,IAAIC,YAAY,GAAGF,WAAW,CAACG,GAAG,CAAC,kBAAkB,CAAC;EACtD,IAAIC,kBAAkB,GAAGX,gBAAgB,CAACU,GAAG,CAACD,YAAY,CAAC;EAC3D,IAAIG,eAAe;EACnB,IAAIJ,YAAY,IAAIA,YAAY,CAACK,YAAY,EAAE;IAC7CD,eAAe,GAAGjB,MAAM,CAACmB,GAAG,CAACN,YAAY,CAACK,YAAY,EAAE,UAAUE,GAAG,EAAE;MACrE,IAAIC,OAAO,GAAG;QACZC,IAAI,EAAEF;MACR,CAAC;MACD,IAAIG,SAAS,GAAGV,YAAY,CAACW,OAAO,CAACT,GAAG,CAACK,GAAG,CAAC;MAC7C,IAAIG,SAAS,EAAE;QACb,IAAIE,QAAQ,GAAGF,SAAS,CAACR,GAAG,CAAC,MAAM,CAAC;QACpCM,OAAO,CAACK,IAAI,GAAGvB,sBAAsB,CAACsB,QAAQ,CAAC;MACjD;MACA,OAAOJ,OAAO;IAChB,CAAC,CAAC;EACJ;EACA,IAAI,CAACJ,eAAe,EAAE;IACpB;IACAA,eAAe,GAAGD,kBAAkB,KAAKA,kBAAkB,CAACW,iBAAiB,GAAGX,kBAAkB,CAACW,iBAAiB,CAAC,CAAC,GAAGX,kBAAkB,CAACY,UAAU,CAACC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;EAC/K;EACA,OAAOZ,eAAe;AACxB;AACA,SAASa,iBAAiBA,CAACC,WAAW,EAAEC,qBAAqB,EAAEnB,YAAY,EAAE;EAC3E,IAAIoB,qBAAqB;EACzB,IAAIC,aAAa;EACjBrB,YAAY,IAAIb,MAAM,CAACmC,IAAI,CAACJ,WAAW,EAAE,UAAUV,OAAO,EAAEe,QAAQ,EAAE;IACpE,IAAIC,QAAQ,GAAGhB,OAAO,CAACgB,QAAQ;IAC/B,IAAIC,iBAAiB,GAAGzB,YAAY,CAAC0B,eAAe,CAACxB,GAAG,CAACsB,QAAQ,CAAC;IAClE,IAAIC,iBAAiB,EAAE;MACrB,IAAIL,qBAAqB,IAAI,IAAI,EAAE;QACjCA,qBAAqB,GAAGG,QAAQ;MAClC;MACAf,OAAO,CAACmB,WAAW,GAAGF,iBAAiB,CAACG,cAAc,CAAC,CAAC;MACxD,IAAIT,qBAAqB,EAAE;QACzBX,OAAO,CAACW,qBAAqB,GAAG,IAAI;MACtC;IACF;IACA,IAAIX,OAAO,CAACqB,SAAS,CAACC,QAAQ,IAAI,IAAI,EAAE;MACtCT,aAAa,GAAG,IAAI;IACtB;EACF,CAAC,CAAC;EACF,IAAI,CAACA,aAAa,IAAID,qBAAqB,IAAI,IAAI,EAAE;IACnDF,WAAW,CAACE,qBAAqB,CAAC,CAACS,SAAS,CAACC,QAAQ,GAAG,CAAC;EAC3D;EACA,OAAOV,qBAAqB;AAC9B;AACA;AACA;AACA;AACA;AACA,SAASW,gBAAgBA,CAACC,SAAS,EAAEjC,WAAW,EAAEkC,GAAG,EAAE;EACrDA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACf,IAAIC,aAAa,GAAGnC,WAAW,CAACoC,gBAAgB,CAAC,CAAC;EAClD,IAAIC,MAAM;EACV,IAAIC,gBAAgB,GAAG,KAAK;EAC5B,IAAIL,SAAS,EAAE;IACbK,gBAAgB,GAAG,IAAI;IACvBD,MAAM,GAAG1C,gCAAgC,CAACsC,SAAS,CAAC;EACtD,CAAC,MAAM;IACLI,MAAM,GAAGF,aAAa,CAACI,SAAS,CAAC,CAAC;IAClC;IACAD,gBAAgB,GAAGD,MAAM,CAACG,YAAY,KAAK1C,sBAAsB;EACnE;EACA,IAAIG,YAAY,GAAGP,uBAAuB,CAACM,WAAW,CAAC;EACvD,IAAIK,eAAe,GAAGN,kBAAkB,CAACC,WAAW,EAAEC,YAAY,CAAC;EACnE,IAAIwC,kBAAkB,GAAGP,GAAG,CAACO,kBAAkB;EAC/C,IAAIC,eAAe,GAAGtD,MAAM,CAACuD,UAAU,CAACF,kBAAkB,CAAC,GAAGA,kBAAkB,GAAGA,kBAAkB,GAAGrD,MAAM,CAACwD,KAAK,CAAC/C,+BAA+B,EAAEQ,eAAe,EAAEL,WAAW,CAAC,GAAG,IAAI;EAC1L,IAAI6C,sBAAsB,GAAG;IAC3BC,eAAe,EAAEzC,eAAe;IAChC0C,aAAa,EAAEb,GAAG,CAACa,aAAa;IAChCC,YAAY,EAAEhD,WAAW,CAACiD,SAAS,CAAC,CAAC;IACrCP,eAAe,EAAEA,eAAe;IAChCQ,uBAAuB,EAAE,CAACZ;EAC5B,CAAC;EACD,IAAIa,MAAM,GAAG7D,uBAAuB,CAAC+C,MAAM,EAAEQ,sBAAsB,CAAC;EACpE,IAAIxB,qBAAqB,GAAGH,iBAAiB,CAACiC,MAAM,CAACnC,UAAU,EAAEkB,GAAG,CAACd,qBAAqB,EAAEnB,YAAY,CAAC;EACzG,IAAImD,KAAK,GAAG,CAACd,gBAAgB,GAAGH,aAAa,CAACkB,kBAAkB,CAACF,MAAM,CAAC,GAAG,IAAI;EAC/E,IAAIG,oBAAoB,GAAG1D,eAAe,CAACI,WAAW,EAAE;IACtDmD,MAAM,EAAEA,MAAM;IACdC,KAAK,EAAEA;EACT,CAAC,CAAC;EACF,IAAIG,IAAI,GAAG,IAAIlE,UAAU,CAAC8D,MAAM,EAAEnD,WAAW,CAAC;EAC9CuD,IAAI,CAACC,kBAAkB,CAACF,oBAAoB,CAAC;EAC7C,IAAIG,cAAc,GAAGpC,qBAAqB,IAAI,IAAI,IAAIqC,yBAAyB,CAACrB,MAAM,CAAC,GAAG,UAAUsB,OAAO,EAAEC,OAAO,EAAEC,SAAS,EAAErC,QAAQ,EAAE;IACzI;IACA,OAAOA,QAAQ,KAAKH,qBAAqB,GAAGwC,SAAS,GAAG,IAAI,CAACC,qBAAqB,CAACH,OAAO,EAAEC,OAAO,EAAEC,SAAS,EAAErC,QAAQ,CAAC;EAC3H,CAAC,GAAG,IAAI;EACR+B,IAAI,CAACQ,aAAa,GAAG,KAAK;EAC1BR,IAAI,CAACS,QAAQ;EACb;EACA1B,gBAAgB,GAAGD,MAAM,GAAGe,KAAK,EAAE,IAAI,EAAEK,cAAc,CAAC;EACxD,OAAOF,IAAI;AACb;AACA,SAASG,yBAAyBA,CAACrB,MAAM,EAAE;EACzC,IAAIA,MAAM,CAACG,YAAY,KAAK1C,sBAAsB,EAAE;IAClD,IAAImE,UAAU,GAAGC,gBAAgB,CAAC7B,MAAM,CAACkB,IAAI,IAAI,EAAE,CAAC;IACpD,OAAO,CAACnE,MAAM,CAAC+E,OAAO,CAAC3E,gBAAgB,CAACyE,UAAU,CAAC,CAAC;EACtD;AACF;AACA,SAASC,gBAAgBA,CAACE,GAAG,EAAE;EAC7B,IAAIC,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGD,GAAG,CAACE,MAAM,IAAIF,GAAG,CAACC,CAAC,CAAC,IAAI,IAAI,EAAE;IACvCA,CAAC,EAAE;EACL;EACA,OAAOD,GAAG,CAACC,CAAC,CAAC;AACf;AACA,eAAerC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}