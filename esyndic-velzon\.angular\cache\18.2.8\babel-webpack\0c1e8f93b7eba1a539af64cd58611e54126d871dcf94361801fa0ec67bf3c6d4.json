{"ast": null, "code": "import { companieslist } from 'src/app/core/data';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/pagination.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../../shared/breadcrumbs/breadcrumbs.component\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"angularx-flatpickr\";\nimport * as i7 from \"@ng-bootstrap/ng-bootstrap\";\nfunction CompanieslistComponent_For_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 29)(2, \"div\", 4)(3, \"div\", 30)(4, \"div\", 31);\n    i0.ɵɵelement(5, \"img\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 33)(7, \"a\", 34)(8, \"h5\", 35);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 36);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 37);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 38)(15, \"span\", 39);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 40);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 41);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 42);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 43);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 44);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\")(28, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function CompanieslistComponent_For_31_Template_button_click_28_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.companydetail($index_r2));\n    });\n    i0.ɵɵelementStart(29, \"span\", 46);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" Jobs Available\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const data_r4 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"src\", data_r4.image_src, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(data_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r4.company_info);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r4.industry_type);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(data_r4.employee);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r4.location);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r4.rating);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r4.website);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r4.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r4.since);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(data_r4.vacancy);\n  }\n}\nfunction CompanieslistComponent_Conditional_32_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 50);\n    i0.ɵɵtext(1, \" Prev \");\n  }\n}\nfunction CompanieslistComponent_Conditional_32_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Next \");\n    i0.ɵɵelement(1, \"i\", 51);\n  }\n}\nfunction CompanieslistComponent_Conditional_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngb-pagination\", 47);\n    i0.ɵɵtwoWayListener(\"pageChange\", function CompanieslistComponent_Conditional_32_Template_ngb_pagination_pageChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.service.page, $event) || (ctx_r2.service.page = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function CompanieslistComponent_Conditional_32_Template_ngb_pagination_pageChange_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.changePage());\n    });\n    i0.ɵɵtemplate(1, CompanieslistComponent_Conditional_32_ng_template_1_Template, 2, 0, \"ng-template\", 48)(2, CompanieslistComponent_Conditional_32_ng_template_2_Template, 2, 0, \"ng-template\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"collectionSize\", ctx_r2.allcompanies.length);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r2.service.page);\n    i0.ɵɵproperty(\"pageSize\", ctx_r2.service.pageSize);\n  }\n}\nfunction CompanieslistComponent_Conditional_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 52)(2, \"div\", 4)(3, \"div\", 53)(4, \"div\", 31);\n    i0.ɵɵelement(5, \"img\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 33)(7, \"a\", 34)(8, \"h5\", 55);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"p\", 56);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"ul\", 57)(13, \"li\", 58)(14, \"a\", 59);\n    i0.ɵɵelement(15, \"i\", 60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"li\", 58)(17, \"a\", 61);\n    i0.ɵɵelement(18, \"i\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"li\", 58)(20, \"a\", 63);\n    i0.ɵɵelement(21, \"i\", 64);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(22, \"div\", 4)(23, \"h6\", 65);\n    i0.ɵɵtext(24, \"Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p\", 66);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 67)(28, \"table\", 68)(29, \"tbody\")(30, \"tr\")(31, \"td\", 69);\n    i0.ɵɵtext(32, \"Industry Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"td\", 70);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"tr\")(36, \"td\", 69);\n    i0.ɵɵtext(37, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"td\", 71);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"tr\")(41, \"td\", 69);\n    i0.ɵɵtext(42, \"Employee\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"td\", 72);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"tr\")(46, \"td\", 69);\n    i0.ɵɵtext(47, \"Vacancy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"td\", 73);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"tr\")(51, \"td\", 69);\n    i0.ɵɵtext(52, \"Rating\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"td\")(54, \"span\", 74);\n    i0.ɵɵtext(55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"i\", 75);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"tr\")(58, \"td\", 69);\n    i0.ɵɵtext(59, \"Website\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"td\")(61, \"a\", 76);\n    i0.ɵɵtext(62);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(63, \"tr\")(64, \"td\", 69);\n    i0.ɵɵtext(65, \"Contact Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"td\", 77);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"tr\")(69, \"td\", 69);\n    i0.ɵɵtext(70, \"Since\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"td\", 78);\n    i0.ɵɵtext(72);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(73, \"div\", 79)(74, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function CompanieslistComponent_Conditional_33_Template_button_click_74_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.followClick($event));\n    });\n    i0.ɵɵelementStart(75, \"span\", 81);\n    i0.ɵɵelement(76, \"i\", 82);\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"a\", 83);\n    i0.ɵɵtext(79, \"More View \");\n    i0.ɵɵelement(80, \"i\", 84);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(81, \"div\", 85)(82, \"div\", 86)(83, \"div\", 87)(84, \"div\", 88)(85, \"div\", 89)(86, \"div\", 90);\n    i0.ɵɵelement(87, \"i\", 91);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(88, \"div\", 92)(89, \"h6\", 93);\n    i0.ɵɵtext(90, \"Free trial\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"p\", 94);\n    i0.ɵɵtext(92, \"28 days left\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(93, \"div\")(94, \"a\", 95);\n    i0.ɵɵtext(95, \"Upgrade\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(96, \"div\", 96)(97, \"a\", 97)(98, \"span\");\n    i0.ɵɵtext(99, \"See benefits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(100, \"i\", 98);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r2.jobdetail.image_src, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.jobdetail.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.jobdetail.industry_type);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r2.jobdetail.company_info);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.jobdetail.industry_type);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.jobdetail.location);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.jobdetail.employee);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.jobdetail.vacancy);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.jobdetail.rating);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.jobdetail.website);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.jobdetail.email);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.jobdetail.since);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.followbtn == \"1\" ? \"ri-add-line align-bottom\" : \"ri-user-unfollow-line align-bottom\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.followtxt, \"\");\n  }\n}\nexport class CompanieslistComponent {\n  constructor(service) {\n    this.service = service;\n    this.followbtn = 1;\n    this.followtxt = 'Follow';\n    this.service.pageSize = 16;\n  }\n  ngOnInit() {\n    /**\n    * BreadCrumb\n    */\n    this.breadCrumbItems = [{\n      label: 'Companies'\n    }, {\n      label: 'Companies List',\n      active: true\n    }];\n    // Fetch Data\n    setTimeout(() => {\n      this.companies = this.service.changePage(companieslist);\n      this.allcompanies = companieslist;\n      this.jobdetail = this.companies[0];\n      document.getElementById('elmLoader')?.classList.add('d-none');\n      document.getElementById('job-overview')?.classList.remove('d-none');\n    }, 1200);\n  }\n  ngOnDestroy() {\n    this.service.pageSize = 8;\n  }\n  // Pagination\n  changePage() {\n    this.companies = this.service.changePage(this.allcompanies);\n  }\n  // Go Detail\n  companydetail(id) {\n    this.jobdetail = this.companies[id];\n  }\n  // Follow - unfollow\n  followClick(ev) {\n    if (this.followbtn == '1') {\n      this.followbtn = '2';\n      this.followtxt = 'Unfollow';\n      document.getElementById('togglefollow')?.classList.replace('btn-soft-success', 'btn-success');\n    } else {\n      this.followbtn = '1';\n      this.followtxt = 'Follow';\n      document.getElementById('togglefollow')?.classList.replace('btn-success', 'btn-soft-success');\n    }\n  }\n  static {\n    this.ɵfac = function CompanieslistComponent_Factory(t) {\n      return new (t || CompanieslistComponent)(i0.ɵɵdirectiveInject(i1.PaginationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CompanieslistComponent,\n      selectors: [[\"app-companieslist\"]],\n      decls: 38,\n      vars: 7,\n      consts: [[\"title\", \"Companies List\", 3, \"breadcrumbItems\"], [1, \"row\"], [1, \"col-xxl-9\"], [1, \"card\"], [1, \"card-body\"], [1, \"row\", \"g-3\"], [1, \"col-xxl-5\", \"col-sm-6\"], [1, \"search-box\"], [\"type\", \"text\", \"id\", \"searchCompany\", \"placeholder\", \"Search for company, industry type...\", 1, \"form-control\", \"search\", \"bg-light\", \"border-light\", 3, \"ngModelChange\", \"ngModel\"], [1, \"ri-search-line\", \"search-icon\"], [1, \"col-xxl-3\", \"col-sm-6\"], [\"type\", \"text\", \"id\", \"datepicker\", \"mwlFlatpickr\", \"\", \"data-date-format\", \"d M, Y\", \"placeholder\", \"Select date\", 1, \"form-control\", \"bg-light\", \"border-light\", 3, \"ngModelChange\", \"convertModelValue\", \"ngModel\"], [1, \"col-xxl-2\", \"col-sm-4\"], [1, \"input-light\"], [\"data-choices\", \"\", \"data-choices-search-false\", \"\", \"name\", \"choices-single-default\", \"id\", \"idType\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"\", \"selected\", \"\"], [\"value\", \"Full Time\"], [\"value\", \"Part Time\"], [\"value\", \"Internship\"], [\"value\", \"Freelance\"], [\"type\", \"button\", \"onclick\", \"filterData();\", 1, \"btn\", \"btn-danger\", \"w-100\"], [1, \"ri-equalizer-fill\", \"me-1\", \"align-bottom\"], [\"id\", \"companies-list\", 1, \"row\", \"job-list-row\"], [1, \"col-xxl-3\", \"col-md-6\"], [\"aria-label\", \"Custom pagination\", 1, \"d-flex\", \"justify-content-end\", \"pt-2\", 3, \"collectionSize\", \"page\", \"pageSize\"], [1, \"col-xxl-3\"], [\"id\", \"elmLoader\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"avatar-sm\"], [1, \"visually-hidden\"], [1, \"card\", \"companiesList-card\"], [1, \"avatar-sm\", \"mx-auto\"], [1, \"avatar-title\", \"bg-light\", \"rounded\"], [\"alt\", \"\", 1, \"avatar-xxs\", \"companyLogo-img\", 3, \"src\"], [1, \"text-center\"], [\"href\", \"javascript:void(0);\"], [1, \"mt-3\", \"company-name\"], [1, \"d-none\", \"company-desc\"], [1, \"text-muted\", \"industry-type\"], [1, \"d-none\"], [1, \"employee\"], [1, \"location\"], [1, \"rating\"], [1, \"website\"], [1, \"email\"], [1, \"since\"], [\"type\", \"button\", 1, \"btn\", \"btn-soft-primary\", \"w-100\", \"viewcompany-list\", 3, \"click\"], [1, \"vacancy\"], [\"aria-label\", \"Custom pagination\", 1, \"d-flex\", \"justify-content-end\", \"pt-2\", 3, \"pageChange\", \"collectionSize\", \"page\", \"pageSize\"], [\"ngbPaginationPrevious\", \"\"], [\"ngbPaginationNext\", \"\"], [1, \"ci-arrow-left\", \"me-2\"], [1, \"ci-arrow-right\", \"ms-2\"], [\"id\", \"company-overview\", 1, \"card\"], [1, \"avatar-lg\", \"mx-auto\", \"mb-3\"], [\"alt\", \"\", 1, \"avatar-sm\", \"company-logo\", 3, \"src\"], [1, \"overview-companyname\"], [1, \"text-muted\", \"overview-industryType\"], [1, \"list-inline\", \"mb-0\"], [1, \"list-inline-item\", \"avatar-xs\"], [\"href\", \"javascript:void(0);\", 1, \"avatar-title\", \"bg-success-subtle\", \"text-success\", \"fs-15\", \"rounded\"], [1, \"ri-global-line\"], [\"href\", \"javascript:void(0);\", 1, \"avatar-title\", \"bg-danger-subtle\", \"text-danger\", \"fs-15\", \"rounded\"], [1, \"ri-mail-line\"], [\"href\", \"javascript:void(0);\", 1, \"avatar-title\", \"bg-warning-subtle\", \"text-warning\", \"fs-15\", \"rounded\"], [1, \"ri-question-answer-line\"], [1, \"text-muted\", \"text-uppercase\", \"fw-semibold\", \"mb-3\"], [1, \"text-muted\", \"mb-4\", \"overview-companydesc\"], [1, \"table-responsive\", \"table-card\"], [1, \"table\", \"table-borderless\", \"mb-4\"], [\"scope\", \"row\", 1, \"fw-medium\"], [1, \"overview-industryType\"], [1, \"overview-company_location\"], [1, \"overview-employee\"], [1, \"overview-vacancy\"], [1, \"overview-rating\"], [1, \"ri-star-fill\", \"text-warning\", \"align-bottom\"], [\"href\", \"javascript:void(0);\", 1, \"link-primary\", \"text-decoration-underline\", \"overview-website\"], [1, \"overview-email\"], [1, \"overview-since\"], [1, \"hstack\", \"gap-3\"], [\"type\", \"button\", \"id\", \"togglefollow\", 1, \"btn\", \"btn-soft-success\", \"custom-toggle\", \"w-100\", 3, \"click\"], [1, \"icon-on\"], [1, \"me-1\", 3, \"ngClass\"], [\"href\", \"javascript:void(0);\", 1, \"btn\", \"btn-info\", \"w-100\"], [1, \"ri-arrow-right-line\", \"align-bottom\"], [1, \"card\", \"overflow-hidden\", \"shadow-none\"], [1, \"card-body\", \"bg-danger-subtle\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-shrink-0\"], [1, \"avatar-sm\"], [1, \"avatar-title\", \"bg-danger-subtle\", \"text-danger\", \"rounded-circle\", \"fs-17\"], [1, \"ri-gift-line\"], [1, \"flex-grow-1\", \"ms-2\"], [1, \"fs-16\"], [1, \"text-muted\", \"mb-0\"], [\"routerLink\", \"/pages/pricing\", 1, \"btn\", \"btn-danger\"], [1, \"card-body\", \"bg-danger-subtle\", \"border-top\", \"border-danger\", \"border-opacity-25\", \"border-top-dashed\"], [\"href\", \"javascript:void(0);\", 1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"text-body\"], [1, \"ri-arrow-right-s-line\", \"fs-18\"]],\n      template: function CompanieslistComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-breadcrumbs\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CompanieslistComponent_Template_input_ngModelChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"input\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CompanieslistComponent_Template_input_ngModelChange_11_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 12)(13, \"div\", 13)(14, \"select\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CompanieslistComponent_Template_select_ngModelChange_14_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(15, \"option\", 15);\n          i0.ɵɵtext(16, \"All\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"option\", 16);\n          i0.ɵɵtext(18, \"Full Time\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"option\", 17);\n          i0.ɵɵtext(20, \"Part Time\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"option\", 18);\n          i0.ɵɵtext(22, \"Internship\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"option\", 19);\n          i0.ɵɵtext(24, \"Freelance\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 12)(26, \"button\", 20);\n          i0.ɵɵelement(27, \"i\", 21);\n          i0.ɵɵtext(28, \" Filters \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(29, \"div\", 22);\n          i0.ɵɵrepeaterCreate(30, CompanieslistComponent_For_31_Template, 32, 11, \"div\", 23, i0.ɵɵrepeaterTrackByIndex);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, CompanieslistComponent_Conditional_32_Template, 3, 3, \"ngb-pagination\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(33, CompanieslistComponent_Conditional_33_Template, 101, 14, \"div\", 25);\n          i0.ɵɵelementStart(34, \"div\", 26)(35, \"div\", 27)(36, \"span\", 28);\n          i0.ɵɵtext(37, \"Loading...\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"breadcrumbItems\", ctx.breadCrumbItems);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"convertModelValue\", true);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(16);\n          i0.ɵɵrepeater(ctx.companies);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.jobdetail ? 32 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.jobdetail ? 33 : -1);\n        }\n      },\n      dependencies: [i2.NgClass, i3.RouterLink, i4.BreadcrumbsComponent, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, i6.FlatpickrDirective, i7.NgbPagination, i7.NgbPaginationNext, i7.NgbPaginationPrevious],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["companieslist", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "CompanieslistComponent_For_31_Template_button_click_28_listener", "$index_r2", "ɵɵrestoreView", "_r1", "$index", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "companydetail", "ɵɵadvance", "ɵɵpropertyInterpolate", "data_r4", "image_src", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "name", "company_info", "industry_type", "employee", "location", "rating", "website", "email", "since", "vacancy", "ɵɵtwoWayListener", "CompanieslistComponent_Conditional_32_Template_ngb_pagination_pageChange_0_listener", "$event", "_r5", "ɵɵtwoWayBindingSet", "service", "page", "changePage", "ɵɵtemplate", "CompanieslistComponent_Conditional_32_ng_template_1_Template", "CompanieslistComponent_Conditional_32_ng_template_2_Template", "ɵɵproperty", "allcompanies", "length", "ɵɵtwoWayProperty", "pageSize", "CompanieslistComponent_Conditional_33_Template_button_click_74_listener", "_r6", "followClick", "jobdetail", "followbtn", "ɵɵtextInterpolate1", "followtxt", "CompanieslistComponent", "constructor", "ngOnInit", "breadCrumbItems", "label", "active", "setTimeout", "companies", "document", "getElementById", "classList", "add", "remove", "ngOnDestroy", "id", "ev", "replace", "ɵɵdirectiveInject", "i1", "PaginationService", "selectors", "decls", "vars", "consts", "template", "CompanieslistComponent_Template", "rf", "ctx", "CompanieslistComponent_Template_input_ngModelChange_8_listener", "searchTerm", "CompanieslistComponent_Template_input_ngModelChange_11_listener", "CompanieslistComponent_Template_select_ngModelChange_14_listener", "ɵɵrepeaterCreate", "CompanieslistComponent_For_31_Template", "ɵɵrepeaterTrackByIndex", "CompanieslistComponent_Conditional_32_Template", "CompanieslistComponent_Conditional_33_Template", "ɵɵrepeater", "ɵɵconditional"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\companieslist\\companieslist.component.ts", "C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\companieslist\\companieslist.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { PaginationService } from 'src/app/core/services/pagination.service';\r\nimport { companieslist } from 'src/app/core/data';\r\n\r\n@Component({\r\n  selector: 'app-companieslist',\r\n  templateUrl: './companieslist.component.html',\r\n  styleUrls: ['./companieslist.component.scss']\r\n})\r\nexport class CompanieslistComponent implements OnInit {\r\n\r\n  // bread crumb items\r\n  breadCrumbItems!: Array<{}>;\r\n  companies: any;\r\n  allcompanies: any;\r\n  followbtn: any = 1;\r\n  followtxt: any = 'Follow';\r\n  searchTerm: any;\r\n  jobdetail: any;\r\n  searchResults: any;\r\n  date: any;\r\n\r\n  constructor(public service: PaginationService) {\r\n    this.service.pageSize = 16\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    /**\r\n  * BreadCrumb\r\n  */\r\n    this.breadCrumbItems = [\r\n      { label: 'Companies' },\r\n      { label: 'Companies List', active: true }\r\n    ];\r\n\r\n    // Fetch Data\r\n    setTimeout(() => {\r\n      this.companies = this.service.changePage(companieslist);\r\n      this.allcompanies = companieslist\r\n      this.jobdetail = this.companies[0]\r\n      document.getElementById('elmLoader')?.classList.add('d-none')\r\n      document.getElementById('job-overview')?.classList.remove('d-none')\r\n    }, 1200)\r\n\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.service.pageSize = 8\r\n  }\r\n\r\n  // Pagination\r\n  changePage() {\r\n    this.companies = this.service.changePage(this.allcompanies)\r\n  }\r\n\r\n  // Go Detail\r\n  companydetail(id: any) {\r\n    this.jobdetail = this.companies[id]\r\n  }\r\n\r\n  // Follow - unfollow\r\n  followClick(ev: any) {\r\n    if (this.followbtn == '1') {\r\n      this.followbtn = '2'\r\n      this.followtxt = 'Unfollow'\r\n      document.getElementById('togglefollow')?.classList.replace('btn-soft-success', 'btn-success')\r\n    } else {\r\n      this.followbtn = '1'\r\n      this.followtxt = 'Follow'\r\n      document.getElementById('togglefollow')?.classList.replace('btn-success', 'btn-soft-success')\r\n    }\r\n  }\r\n\r\n}\r\n", "<!-- Start Breadcrumbs -->\r\n<app-breadcrumbs title=\"Companies List\" [breadcrumbItems]=\"breadCrumbItems\"></app-breadcrumbs>\r\n<!-- End Breadcrumbs -->\r\n<div class=\"row\">\r\n    <div class=\"col-xxl-9\">\r\n        <div class=\"card\">\r\n            <div class=\"card-body\">\r\n                <div class=\"row g-3\">\r\n                    <div class=\"col-xxl-5 col-sm-6\">\r\n                        <div class=\"search-box\">\r\n                            <input type=\"text\" class=\"form-control search bg-light border-light\" id=\"searchCompany\" placeholder=\"Search for company, industry type...\" [(ngModel)]=\"searchTerm\">\r\n                            <i class=\"ri-search-line search-icon\"></i>\r\n                        </div>\r\n                    </div>\r\n                    <!--end col-->\r\n                    <div class=\"col-xxl-3 col-sm-6\">\r\n                        <input type=\"text\" class=\"form-control bg-light border-light\" id=\"datepicker\" mwlFlatpickr [convertModelValue]=\"true\" data-date-format=\"d M, Y\" placeholder=\"Select date\" [(ngModel)]=\"searchTerm\">\r\n                    </div>\r\n                    <!--end col-->\r\n                    <div class=\"col-xxl-2 col-sm-4\">\r\n                        <div class=\"input-light\">\r\n                            <select class=\"form-control\" data-choices data-choices-search-false name=\"choices-single-default\" id=\"idType\" [(ngModel)]=\"searchTerm\">\r\n                                <option value=\"\" selected>All</option>\r\n                                <option value=\"Full Time\">Full Time</option>\r\n                                <option value=\"Part Time\">Part Time</option>\r\n                                <option value=\"Internship\">Internship</option>\r\n                                <option value=\"Freelance\">Freelance</option>\r\n                            </select>\r\n                        </div>\r\n                    </div>\r\n                    <!--end col-->\r\n\r\n                    <div class=\"col-xxl-2 col-sm-4\">\r\n                        <button type=\"button\" class=\"btn btn-danger w-100\" onclick=\"filterData();\">\r\n                            <i class=\"ri-equalizer-fill me-1 align-bottom\"></i> Filters\r\n                        </button>\r\n                    </div>\r\n                    <!--end col-->\r\n                </div>\r\n                <!--end row-->\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"row job-list-row\" id=\"companies-list\">\r\n            @for(data of companies;track $index){\r\n            <div class=\"col-xxl-3 col-md-6\">\r\n                <div class=\"card companiesList-card\">\r\n                    <div class=\"card-body\">\r\n                        <div class=\"avatar-sm mx-auto\">\r\n                            <div class=\"avatar-title bg-light rounded\"> <img src=\"{{data.image_src}}\" alt=\"\" class=\"avatar-xxs companyLogo-img\"> </div>\r\n                        </div>\r\n                        <div class=\"text-center\"> <a href=\"javascript:void(0);\">\r\n                                <h5 class=\"mt-3 company-name\">{{data.name}}</h5>\r\n                            </a>\r\n                            <div class=\"d-none company-desc\">{{data.company_info}}</div>\r\n                            <p class=\"text-muted industry-type\">{{data.industry_type}}</p>\r\n                            <div class=\"d-none\"> <span class=\"employee\">{{data.employee}}</span>\r\n                                <span class=\"location\">{{data.location}}</span> <span class=\"rating\">{{data.rating}}</span>\r\n                                <span class=\"website\">{{data.website}}</span>\r\n                                <span class=\"email\">{{data.email}}</span>\r\n                                <span class=\"since\">{{data.since}}</span>\r\n                            </div>\r\n                        </div>\r\n                        <div> <button type=\"button\" class=\"btn btn-soft-primary w-100 viewcompany-list\" (click)=\"companydetail($index)\"><span class=\"vacancy\">{{data.vacancy}}</span> Jobs\r\n                                Available</button> </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            }\r\n        </div>\r\n\r\n        <!-- pagination -->\r\n        @if(jobdetail){\r\n        <ngb-pagination class=\"d-flex justify-content-end pt-2\" [collectionSize]=\"allcompanies.length\" [(page)]=\"service.page\" [pageSize]=\"service.pageSize\" (pageChange)=\"changePage()\" aria-label=\"Custom pagination\">\r\n            <ng-template ngbPaginationPrevious let-page let-pages=\"pages\">\r\n                <i class=\"ci-arrow-left me-2\"></i>\r\n                Prev\r\n            </ng-template>\r\n            <ng-template ngbPaginationNext>\r\n                Next\r\n                <i class=\"ci-arrow-right ms-2\"></i>\r\n            </ng-template>\r\n        </ngb-pagination>\r\n        }\r\n    </div>\r\n\r\n    @if(jobdetail){\r\n    <div class=\"col-xxl-3\">\r\n        <div class=\"card\" id=\"company-overview\">\r\n            <div class=\"card-body\">\r\n                <div class=\"avatar-lg mx-auto mb-3\">\r\n                    <div class=\"avatar-title bg-light rounded\">\r\n                        <img src=\"{{jobdetail.image_src}}\" alt=\"\" class=\"avatar-sm company-logo\">\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"text-center\">\r\n                    <a href=\"javascript:void(0);\">\r\n                        <h5 class=\"overview-companyname\">{{jobdetail.name}}</h5>\r\n                    </a>\r\n                    <p class=\"text-muted overview-industryType\">{{jobdetail.industry_type}}</p>\r\n\r\n                    <ul class=\"list-inline mb-0\">\r\n                        <li class=\"list-inline-item avatar-xs\">\r\n                            <a href=\"javascript:void(0);\" class=\"avatar-title bg-success-subtle text-success fs-15 rounded\">\r\n                                <i class=\"ri-global-line\"></i>\r\n                            </a>\r\n                        </li>\r\n                        <li class=\"list-inline-item avatar-xs\">\r\n                            <a href=\"javascript:void(0);\" class=\"avatar-title bg-danger-subtle text-danger fs-15 rounded\">\r\n                                <i class=\"ri-mail-line\"></i>\r\n                            </a>\r\n                        </li>\r\n                        <li class=\"list-inline-item avatar-xs\">\r\n                            <a href=\"javascript:void(0);\" class=\"avatar-title bg-warning-subtle text-warning fs-15 rounded\">\r\n                                <i class=\"ri-question-answer-line\"></i>\r\n                            </a>\r\n                        </li>\r\n                    </ul>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"card-body\">\r\n                <h6 class=\"text-muted text-uppercase fw-semibold mb-3\">Information</h6>\r\n                <p class=\"text-muted mb-4 overview-companydesc\">{{jobdetail.company_info}}</p>\r\n\r\n                <div class=\"table-responsive table-card\">\r\n                    <table class=\"table table-borderless mb-4\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"fw-medium\" scope=\"row\">Industry Type</td>\r\n                                <td class=\"overview-industryType\">{{jobdetail.industry_type}}</td>\r\n                            </tr>\r\n                            <tr>\r\n                                <td class=\"fw-medium\" scope=\"row\">Location</td>\r\n                                <td class=\"overview-company_location\">{{jobdetail.location}}</td>\r\n                            </tr>\r\n                            <tr>\r\n                                <td class=\"fw-medium\" scope=\"row\">Employee</td>\r\n                                <td class=\"overview-employee\">{{jobdetail.employee}}</td>\r\n                            </tr>\r\n                            <tr>\r\n                                <td class=\"fw-medium\" scope=\"row\">Vacancy</td>\r\n                                <td class=\"overview-vacancy\">{{jobdetail.vacancy}}</td>\r\n                            </tr>\r\n                            <tr>\r\n                                <td class=\"fw-medium\" scope=\"row\">Rating</td>\r\n                                <td><span class=\"overview-rating\">{{jobdetail.rating}}</span> <i class=\"ri-star-fill text-warning align-bottom\"></i></td>\r\n                            </tr>\r\n                            <tr>\r\n                                <td class=\"fw-medium\" scope=\"row\">Website</td>\r\n                                <td>\r\n                                    <a href=\"javascript:void(0);\" class=\"link-primary text-decoration-underline overview-website\">{{jobdetail.website}}</a>\r\n                                </td>\r\n                            </tr>\r\n                            <tr>\r\n                                <td class=\"fw-medium\" scope=\"row\">Contact Email</td>\r\n                                <td class=\"overview-email\">{{jobdetail.email}}</td>\r\n                            </tr>\r\n                            <tr>\r\n                                <td class=\"fw-medium\" scope=\"row\">Since</td>\r\n                                <td class=\"overview-since\">{{jobdetail.since}}</td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                </div>\r\n\r\n                <div class=\"hstack gap-3\">\r\n                    <button type=\"button\" class=\"btn btn-soft-success custom-toggle w-100\" id=\"togglefollow\" (click)=\"followClick($event)\">\r\n                        <span class=\"icon-on\">\r\n                            <i class=\"me-1\" [ngClass]=\"(followbtn == '1')?'ri-add-line align-bottom':'ri-user-unfollow-line align-bottom'\"></i>\r\n                            {{followtxt}}</span>\r\n                    </button>\r\n                    <a href=\"javascript:void(0);\" class=\"btn btn-info w-100\">More View <i class=\"ri-arrow-right-line align-bottom\"></i></a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"card overflow-hidden shadow-none\">\r\n            <div class=\"card-body bg-danger-subtle\">\r\n                <div class=\"d-flex align-items-center\">\r\n                    <div class=\"flex-shrink-0\">\r\n                        <div class=\"avatar-sm\">\r\n                            <div class=\"avatar-title bg-danger-subtle text-danger rounded-circle fs-17\">\r\n                                <i class=\"ri-gift-line\"></i>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"flex-grow-1 ms-2\">\r\n                        <h6 class=\"fs-16\">Free trial</h6>\r\n                        <p class=\"text-muted mb-0\">28 days left</p>\r\n                    </div>\r\n                    <div>\r\n                        <a routerLink=\"/pages/pricing\" class=\"btn btn-danger\">Upgrade</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"card-body bg-danger-subtle border-top border-danger border-opacity-25 border-top-dashed\">\r\n                <a href=\"javascript:void(0);\" class=\"d-flex justify-content-between align-items-center text-body\">\r\n                    <span>See benefits</span>\r\n                    <i class=\"ri-arrow-right-s-line fs-18\"></i>\r\n                </a>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    }\r\n    <div id=\"elmLoader\">\r\n        <div class=\"spinner-border text-primary avatar-sm\" role=\"status\">\r\n            <span class=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,aAAa,QAAQ,mBAAmB;;;;;;;;;;;;IC+CrBC,EAJhB,CAAAC,cAAA,cAAgC,cACS,aACV,cACY,cACgB;IAACD,EAAA,CAAAE,SAAA,cAAwE;IACxHF,EADyH,CAAAG,YAAA,EAAM,EACzH;IAEEH,EADR,CAAAC,cAAA,cAAyB,YAA+B,aAClB;IAAAD,EAAA,CAAAI,MAAA,GAAa;IAC/CJ,EAD+C,CAAAG,YAAA,EAAK,EAChD;IACJH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAI,MAAA,IAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAI,MAAA,IAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACzCH,EAArB,CAAAC,cAAA,eAAoB,gBAAwB;IAAAD,EAAA,CAAAI,MAAA,IAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAChEH,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAI,MAAA,IAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAC,cAAA,gBAAqB;IAAAD,EAAA,CAAAI,MAAA,IAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC3FH,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAI,MAAA,IAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC7CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAI,MAAA,IAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAI,MAAA,IAAc;IAE1CJ,EAF0C,CAAAG,YAAA,EAAO,EACvC,EACJ;IACAH,EAAN,CAAAC,cAAA,WAAK,kBAA2G;IAAhCD,EAAA,CAAAK,UAAA,mBAAAC,gEAAA;MAAA,MAAAC,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,aAAA,CAAAP,SAAA,CAAqB;IAAA,EAAC;IAACP,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAI,MAAA,IAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,uBAC7I;IAG7BJ,EAH6B,CAAAG,YAAA,EAAS,EAAO,EAC/B,EACJ,EACJ;;;;IAlB2DH,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAgB,qBAAA,QAAAC,OAAA,CAAAC,SAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAwB;IAGvCnB,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAI,IAAA,CAAa;IAEdrB,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAK,YAAA,CAAqB;IAClBtB,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAM,aAAA,CAAsB;IACdvB,EAAA,CAAAe,SAAA,GAAiB;IAAjBf,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAO,QAAA,CAAiB;IAClCxB,EAAA,CAAAe,SAAA,GAAiB;IAAjBf,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAQ,QAAA,CAAiB;IAA6BzB,EAAA,CAAAe,SAAA,GAAe;IAAff,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAS,MAAA,CAAe;IAC9D1B,EAAA,CAAAe,SAAA,GAAgB;IAAhBf,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAU,OAAA,CAAgB;IAClB3B,EAAA,CAAAe,SAAA,GAAc;IAAdf,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAW,KAAA,CAAc;IACd5B,EAAA,CAAAe,SAAA,GAAc;IAAdf,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAY,KAAA,CAAc;IAG4F7B,EAAA,CAAAe,SAAA,GAAgB;IAAhBf,EAAA,CAAAoB,iBAAA,CAAAH,OAAA,CAAAa,OAAA,CAAgB;;;;;IAY9J9B,EAAA,CAAAE,SAAA,YAAkC;IAClCF,EAAA,CAAAI,MAAA,aACJ;;;;;IAEIJ,EAAA,CAAAI,MAAA,aACA;IAAAJ,EAAA,CAAAE,SAAA,YAAmC;;;;;;IAP3CF,EAAA,CAAAC,cAAA,yBAAgN;IAAjHD,EAAA,CAAA+B,gBAAA,wBAAAC,oFAAAC,MAAA;MAAAjC,EAAA,CAAAQ,aAAA,CAAA0B,GAAA;MAAA,MAAAvB,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAAZ,EAAA,CAAAmC,kBAAA,CAAAxB,MAAA,CAAAyB,OAAA,CAAAC,IAAA,EAAAJ,MAAA,MAAAtB,MAAA,CAAAyB,OAAA,CAAAC,IAAA,GAAAJ,MAAA;MAAA,OAAAjC,EAAA,CAAAa,WAAA,CAAAoB,MAAA;IAAA,EAAuB;IAA+BjC,EAAA,CAAAK,UAAA,wBAAA2B,oFAAA;MAAAhC,EAAA,CAAAQ,aAAA,CAAA0B,GAAA;MAAA,MAAAvB,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAcF,MAAA,CAAA2B,UAAA,EAAY;IAAA,EAAC;IAK5KtC,EAJA,CAAAuC,UAAA,IAAAC,4DAAA,0BAA8D,IAAAC,4DAAA,0BAI/B;IAInCzC,EAAA,CAAAG,YAAA,EAAiB;;;;IATuCH,EAAA,CAAA0C,UAAA,mBAAA/B,MAAA,CAAAgC,YAAA,CAAAC,MAAA,CAAsC;IAAC5C,EAAA,CAAA6C,gBAAA,SAAAlC,MAAA,CAAAyB,OAAA,CAAAC,IAAA,CAAuB;IAACrC,EAAA,CAAA0C,UAAA,aAAA/B,MAAA,CAAAyB,OAAA,CAAAU,QAAA,CAA6B;;;;;;IAkBxI9C,EAJhB,CAAAC,cAAA,cAAuB,cACqB,aACb,cACiB,cACW;IACvCD,EAAA,CAAAE,SAAA,cAAyE;IAEjFF,EADI,CAAAG,YAAA,EAAM,EACJ;IAIEH,EAFR,CAAAC,cAAA,cAAyB,YACS,aACO;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IACvDJ,EADuD,CAAAG,YAAA,EAAK,EACxD;IACJH,EAAA,CAAAC,cAAA,aAA4C;IAAAD,EAAA,CAAAI,MAAA,IAA2B;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAInEH,EAFR,CAAAC,cAAA,cAA6B,cACc,aAC6D;IAC5FD,EAAA,CAAAE,SAAA,aAA8B;IAEtCF,EADI,CAAAG,YAAA,EAAI,EACH;IAEDH,EADJ,CAAAC,cAAA,cAAuC,aAC2D;IAC1FD,EAAA,CAAAE,SAAA,aAA4B;IAEpCF,EADI,CAAAG,YAAA,EAAI,EACH;IAEDH,EADJ,CAAAC,cAAA,cAAuC,aAC6D;IAC5FD,EAAA,CAAAE,SAAA,aAAuC;IAK3DF,EAJgB,CAAAG,YAAA,EAAI,EACH,EACJ,EACH,EACJ;IAGFH,EADJ,CAAAC,cAAA,cAAuB,cACoC;IAAAD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACvEH,EAAA,CAAAC,cAAA,aAAgD;IAAAD,EAAA,CAAAI,MAAA,IAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAM9DH,EAJhB,CAAAC,cAAA,eAAyC,iBACM,aAChC,UACC,cACkC;IAAAD,EAAA,CAAAI,MAAA,qBAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAI,MAAA,IAA2B;IACjEJ,EADiE,CAAAG,YAAA,EAAK,EACjE;IAEDH,EADJ,CAAAC,cAAA,UAAI,cACkC;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,cAAsC;IAAAD,EAAA,CAAAI,MAAA,IAAsB;IAChEJ,EADgE,CAAAG,YAAA,EAAK,EAChE;IAEDH,EADJ,CAAAC,cAAA,UAAI,cACkC;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAI,MAAA,IAAsB;IACxDJ,EADwD,CAAAG,YAAA,EAAK,EACxD;IAEDH,EADJ,CAAAC,cAAA,UAAI,cACkC;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAI,MAAA,IAAqB;IACtDJ,EADsD,CAAAG,YAAA,EAAK,EACtD;IAEDH,EADJ,CAAAC,cAAA,UAAI,cACkC;IAAAD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAJ,CAAAC,cAAA,UAAI,gBAA8B;IAAAD,EAAA,CAAAI,MAAA,IAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,SAAA,aAAsD;IACxHF,EADwH,CAAAG,YAAA,EAAK,EACxH;IAEDH,EADJ,CAAAC,cAAA,UAAI,cACkC;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAE1CH,EADJ,CAAAC,cAAA,UAAI,aAC8F;IAAAD,EAAA,CAAAI,MAAA,IAAqB;IAE3HJ,EAF2H,CAAAG,YAAA,EAAI,EACtH,EACJ;IAEDH,EADJ,CAAAC,cAAA,UAAI,cACkC;IAAAD,EAAA,CAAAI,MAAA,qBAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAI,MAAA,IAAmB;IAClDJ,EADkD,CAAAG,YAAA,EAAK,EAClD;IAEDH,EADJ,CAAAC,cAAA,UAAI,cACkC;IAAAD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAI,MAAA,IAAmB;IAI9DJ,EAJ8D,CAAAG,YAAA,EAAK,EAClD,EACD,EACJ,EACN;IAGFH,EADJ,CAAAC,cAAA,eAA0B,kBACiG;IAA9BD,EAAA,CAAAK,UAAA,mBAAA0C,wEAAAd,MAAA;MAAAjC,EAAA,CAAAQ,aAAA,CAAAwC,GAAA;MAAA,MAAArC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAsC,WAAA,CAAAhB,MAAA,CAAmB;IAAA,EAAC;IAClHjC,EAAA,CAAAC,cAAA,gBAAsB;IAClBD,EAAA,CAAAE,SAAA,aAAmH;IACnHF,EAAA,CAAAI,MAAA,IAAa;IACrBJ,EADqB,CAAAG,YAAA,EAAO,EACnB;IACTH,EAAA,CAAAC,cAAA,aAAyD;IAAAD,EAAA,CAAAI,MAAA,kBAAU;IAAAJ,EAAA,CAAAE,SAAA,aAAgD;IAG/HF,EAH+H,CAAAG,YAAA,EAAI,EACrH,EACJ,EACJ;IAOcH,EALpB,CAAAC,cAAA,eAA8C,eACF,eACG,eACR,eACA,eACyD;IACxED,EAAA,CAAAE,SAAA,aAA4B;IAGxCF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAEFH,EADJ,CAAAC,cAAA,eAA8B,cACR;IAAAD,EAAA,CAAAI,MAAA,kBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAI,MAAA,oBAAY;IAC3CJ,EAD2C,CAAAG,YAAA,EAAI,EACzC;IAEFH,EADJ,CAAAC,cAAA,WAAK,aACqD;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAGzEJ,EAHyE,CAAAG,YAAA,EAAI,EAC/D,EACJ,EACJ;IAGEH,EAFR,CAAAC,cAAA,eAAqG,aACC,YACxF;IAAAD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACzBH,EAAA,CAAAE,SAAA,cAA2C;IAI3DF,EAHY,CAAAG,YAAA,EAAI,EACF,EACJ,EACJ;;;;IAhHmBH,EAAA,CAAAe,SAAA,GAA6B;IAA7Bf,EAAA,CAAAgB,qBAAA,QAAAL,MAAA,CAAAuC,SAAA,CAAAhC,SAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAA6B;IAMDnB,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAuC,SAAA,CAAA7B,IAAA,CAAkB;IAEXrB,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAuC,SAAA,CAAA3B,aAAA,CAA2B;IAwB3BvB,EAAA,CAAAe,SAAA,IAA0B;IAA1Bf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAuC,SAAA,CAAA5B,YAAA,CAA0B;IAOxBtB,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAuC,SAAA,CAAA3B,aAAA,CAA2B;IAIvBvB,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAuC,SAAA,CAAAzB,QAAA,CAAsB;IAI9BzB,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAuC,SAAA,CAAA1B,QAAA,CAAsB;IAIvBxB,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAuC,SAAA,CAAApB,OAAA,CAAqB;IAIhB9B,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAuC,SAAA,CAAAxB,MAAA,CAAoB;IAK4C1B,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAuC,SAAA,CAAAvB,OAAA,CAAqB;IAK5F3B,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAuC,SAAA,CAAAtB,KAAA,CAAmB;IAInB5B,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAuC,SAAA,CAAArB,KAAA,CAAmB;IASlC7B,EAAA,CAAAe,SAAA,GAA8F;IAA9Ff,EAAA,CAAA0C,UAAA,YAAA/B,MAAA,CAAAwC,SAAA,4EAA8F;IAC9GnD,EAAA,CAAAe,SAAA,EAAa;IAAbf,EAAA,CAAAoD,kBAAA,MAAAzC,MAAA,CAAA0C,SAAA,KAAa;;;ADlKzC,OAAM,MAAOC,sBAAsB;EAajCC,YAAmBnB,OAA0B;IAA1B,KAAAA,OAAO,GAAPA,OAAO;IAP1B,KAAAe,SAAS,GAAQ,CAAC;IAClB,KAAAE,SAAS,GAAQ,QAAQ;IAOvB,IAAI,CAACjB,OAAO,CAACU,QAAQ,GAAG,EAAE;EAC5B;EAEAU,QAAQA,CAAA;IACN;;;IAGA,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE;IAAW,CAAE,EACtB;MAAEA,KAAK,EAAE,gBAAgB;MAAEC,MAAM,EAAE;IAAI,CAAE,CAC1C;IAED;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,SAAS,GAAG,IAAI,CAACzB,OAAO,CAACE,UAAU,CAACvC,aAAa,CAAC;MACvD,IAAI,CAAC4C,YAAY,GAAG5C,aAAa;MACjC,IAAI,CAACmD,SAAS,GAAG,IAAI,CAACW,SAAS,CAAC,CAAC,CAAC;MAClCC,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC,EAAEC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAC7DH,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC,EAAEC,SAAS,CAACE,MAAM,CAAC,QAAQ,CAAC;IACrE,CAAC,EAAE,IAAI,CAAC;EAEV;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC/B,OAAO,CAACU,QAAQ,GAAG,CAAC;EAC3B;EAEA;EACAR,UAAUA,CAAA;IACR,IAAI,CAACuB,SAAS,GAAG,IAAI,CAACzB,OAAO,CAACE,UAAU,CAAC,IAAI,CAACK,YAAY,CAAC;EAC7D;EAEA;EACA7B,aAAaA,CAACsD,EAAO;IACnB,IAAI,CAAClB,SAAS,GAAG,IAAI,CAACW,SAAS,CAACO,EAAE,CAAC;EACrC;EAEA;EACAnB,WAAWA,CAACoB,EAAO;IACjB,IAAI,IAAI,CAAClB,SAAS,IAAI,GAAG,EAAE;MACzB,IAAI,CAACA,SAAS,GAAG,GAAG;MACpB,IAAI,CAACE,SAAS,GAAG,UAAU;MAC3BS,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC,EAAEC,SAAS,CAACM,OAAO,CAAC,kBAAkB,EAAE,aAAa,CAAC;IAC/F,CAAC,MAAM;MACL,IAAI,CAACnB,SAAS,GAAG,GAAG;MACpB,IAAI,CAACE,SAAS,GAAG,QAAQ;MACzBS,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC,EAAEC,SAAS,CAACM,OAAO,CAAC,aAAa,EAAE,kBAAkB,CAAC;IAC/F;EACF;;;uBA9DWhB,sBAAsB,EAAAtD,EAAA,CAAAuE,iBAAA,CAAAC,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAtBnB,sBAAsB;MAAAoB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRnChF,EAAA,CAAAE,SAAA,yBAA8F;UASlEF,EAP5B,CAAAC,cAAA,aAAiB,aACU,aACD,aACS,aACE,aACe,aACJ,eACgJ;UAAzBD,EAAA,CAAA+B,gBAAA,2BAAAmD,+DAAAjD,MAAA;YAAAjC,EAAA,CAAAmC,kBAAA,CAAA8C,GAAA,CAAAE,UAAA,EAAAlD,MAAA,MAAAgD,GAAA,CAAAE,UAAA,GAAAlD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAAnKjC,EAAA,CAAAG,YAAA,EAAoK;UACpKH,EAAA,CAAAE,SAAA,WAA0C;UAElDF,EADI,CAAAG,YAAA,EAAM,EACJ;UAGFH,EADJ,CAAAC,cAAA,eAAgC,iBACuK;UAAzBD,EAAA,CAAA+B,gBAAA,2BAAAqD,gEAAAnD,MAAA;YAAAjC,EAAA,CAAAmC,kBAAA,CAAA8C,GAAA,CAAAE,UAAA,EAAAlD,MAAA,MAAAgD,GAAA,CAAAE,UAAA,GAAAlD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UACtMjC,EADI,CAAAG,YAAA,EAAmM,EACjM;UAIEH,EAFR,CAAAC,cAAA,eAAgC,eACH,kBACkH;UAAzBD,EAAA,CAAA+B,gBAAA,2BAAAsD,iEAAApD,MAAA;YAAAjC,EAAA,CAAAmC,kBAAA,CAAA8C,GAAA,CAAAE,UAAA,EAAAlD,MAAA,MAAAgD,GAAA,CAAAE,UAAA,GAAAlD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAClIjC,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAI,MAAA,WAAG;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UACtCH,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAC5CH,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAC5CH,EAAA,CAAAC,cAAA,kBAA2B;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAC9CH,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAG/CJ,EAH+C,CAAAG,YAAA,EAAS,EACvC,EACP,EACJ;UAIFH,EADJ,CAAAC,cAAA,eAAgC,kBAC+C;UACvED,EAAA,CAAAE,SAAA,aAAmD;UAACF,EAAA,CAAAI,MAAA,iBACxD;UAMhBJ,EANgB,CAAAG,YAAA,EAAS,EACP,EAEJ,EAEJ,EACJ;UAENH,EAAA,CAAAC,cAAA,eAAkD;UAC9CD,EAAA,CAAAsF,gBAAA,KAAAC,sCAAA,qBAAAvF,EAAA,CAAAwF,sBAAA,CAwBC;UACLxF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAuC,UAAA,KAAAkD,8CAAA,6BAAe;UAYnBzF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAuC,UAAA,KAAAmD,8CAAA,qBAAe;UA0HP1F,EAFR,CAAAC,cAAA,eAAoB,eACiD,gBAC/B;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAGpDJ,EAHoD,CAAAG,YAAA,EAAO,EAC7C,EACJ,EACJ;;;UAlNkCH,EAAA,CAAA0C,UAAA,oBAAAuC,GAAA,CAAAxB,eAAA,CAAmC;UAS4FzD,EAAA,CAAAe,SAAA,GAAwB;UAAxBf,EAAA,CAAA6C,gBAAA,YAAAoC,GAAA,CAAAE,UAAA,CAAwB;UAM5EnF,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAA0C,UAAA,2BAA0B;UAAqD1C,EAAA,CAAA6C,gBAAA,YAAAoC,GAAA,CAAAE,UAAA,CAAwB;UAKhFnF,EAAA,CAAAe,SAAA,GAAwB;UAAxBf,EAAA,CAAA6C,gBAAA,YAAAoC,GAAA,CAAAE,UAAA,CAAwB;UAuBtJnF,EAAA,CAAAe,SAAA,IAwBC;UAxBDf,EAAA,CAAA2F,UAAA,CAAAV,GAAA,CAAApB,SAAA,CAwBC;UAIL7D,EAAA,CAAAe,SAAA,GAWC;UAXDf,EAAA,CAAA4F,aAAA,CAAAX,GAAA,CAAA/B,SAAA,WAWC;UAGLlD,EAAA,CAAAe,SAAA,EAuHC;UAvHDf,EAAA,CAAA4F,aAAA,CAAAX,GAAA,CAAA/B,SAAA,WAuHC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}