{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Javanese [jv]\n//! author : <PERSON><PERSON> : https://github.com/lantip\n//! reference: http://jv.wikipedia.org/wiki/Ba<PERSON>_<PERSON>awa\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var jv = moment.defineLocale('jv', {\n    months: 'Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_Nopember_Desember'.split('_'),\n    monthsShort: 'Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nop_Des'.split('_'),\n    weekdays: '<PERSON><PERSON>_<PERSON><PERSON>_Se<PERSON>o_Re<PERSON>_Ke<PERSON>_Jemuwah_Septu'.split('_'),\n    weekdaysShort: 'Min_Sen_Sel_Reb_Kem_Jem_Sep'.split('_'),\n    weekdaysMin: 'Mg_Sn_Sl_Rb_Km_Jm_Sp'.split('_'),\n    longDateFormat: {\n      LT: 'HH.mm',\n      LTS: 'HH.mm.ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY [pukul] HH.mm',\n      LLLL: 'dddd, D MMMM YYYY [pukul] HH.mm'\n    },\n    meridiemParse: /enjing|siyang|sonten|ndalu/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'enjing') {\n        return hour;\n      } else if (meridiem === 'siyang') {\n        return hour >= 11 ? hour : hour + 12;\n      } else if (meridiem === 'sonten' || meridiem === 'ndalu') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 11) {\n        return 'enjing';\n      } else if (hours < 15) {\n        return 'siyang';\n      } else if (hours < 19) {\n        return 'sonten';\n      } else {\n        return 'ndalu';\n      }\n    },\n    calendar: {\n      sameDay: '[Dinten puniko pukul] LT',\n      nextDay: '[Mbenjang pukul] LT',\n      nextWeek: 'dddd [pukul] LT',\n      lastDay: '[Kala wingi pukul] LT',\n      lastWeek: 'dddd [kepengker pukul] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'wonten ing %s',\n      past: '%s ingkang kepengker',\n      s: 'sawetawis detik',\n      ss: '%d detik',\n      m: 'setunggal menit',\n      mm: '%d menit',\n      h: 'setunggal jam',\n      hh: '%d jam',\n      d: 'sedinten',\n      dd: '%d dinten',\n      M: 'sewulan',\n      MM: '%d wulan',\n      y: 'setaun',\n      yy: '%d taun'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return jv;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "jv", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "meridiemHour", "hour", "meridiem", "hours", "minutes", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "week", "dow", "doy"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/moment/locale/jv.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Javanese [jv]\n//! author : <PERSON><PERSON> : https://github.com/lantip\n//! reference: http://jv.wikipedia.org/wiki/Ba<PERSON>_<PERSON>awa\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var jv = moment.defineLocale('jv', {\n        months: 'Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_Nopember_Desember'.split(\n            '_'\n        ),\n        monthsShort: 'Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nop_Des'.split('_'),\n        weekdays: '<PERSON><PERSON>_<PERSON><PERSON>_Seloso_Rebu_Ke<PERSON>_Jemuwah_Septu'.split('_'),\n        weekdaysShort: 'Min_Sen_Sel_Reb_Kem_Jem_Sep'.split('_'),\n        weekdaysMin: 'Mg_Sn_Sl_Rb_Km_Jm_Sp'.split('_'),\n        longDateFormat: {\n            LT: 'HH.mm',\n            LTS: 'HH.mm.ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY [pukul] HH.mm',\n            LLLL: 'dddd, D MMMM YYYY [pukul] HH.mm',\n        },\n        meridiemParse: /enjing|siyang|sonten|ndalu/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (meridiem === 'enjing') {\n                return hour;\n            } else if (meridiem === 'siyang') {\n                return hour >= 11 ? hour : hour + 12;\n            } else if (meridiem === 'sonten' || meridiem === 'ndalu') {\n                return hour + 12;\n            }\n        },\n        meridiem: function (hours, minutes, isLower) {\n            if (hours < 11) {\n                return 'enjing';\n            } else if (hours < 15) {\n                return 'siyang';\n            } else if (hours < 19) {\n                return 'sonten';\n            } else {\n                return 'ndalu';\n            }\n        },\n        calendar: {\n            sameDay: '[Dinten puniko pukul] LT',\n            nextDay: '[Mbenjang pukul] LT',\n            nextWeek: 'dddd [pukul] LT',\n            lastDay: '[Kala wingi pukul] LT',\n            lastWeek: 'dddd [kepengker pukul] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'wonten ing %s',\n            past: '%s ingkang kepengker',\n            s: 'sawetawis detik',\n            ss: '%d detik',\n            m: 'setunggal menit',\n            mm: '%d menit',\n            h: 'setunggal jam',\n            hh: '%d jam',\n            d: 'sedinten',\n            dd: '%d dinten',\n            M: 'sewulan',\n            MM: '%d wulan',\n            y: 'setaun',\n            yy: '%d taun',\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return jv;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,wFAAwF,CAACC,KAAK,CAClG,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,8CAA8C,CAACF,KAAK,CAAC,GAAG,CAAC;IACnEG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,2BAA2B;MAChCC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,4BAA4B;IAC3CC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QACvB,OAAOD,IAAI;MACf,CAAC,MAAM,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QAC9B,OAAOD,IAAI,IAAI,EAAE,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACxC,CAAC,MAAM,IAAIC,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,OAAO,EAAE;QACtD,OAAOD,IAAI,GAAG,EAAE;MACpB;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACzC,IAAIF,KAAK,GAAG,EAAE,EAAE;QACZ,OAAO,QAAQ;MACnB,CAAC,MAAM,IAAIA,KAAK,GAAG,EAAE,EAAE;QACnB,OAAO,QAAQ;MACnB,CAAC,MAAM,IAAIA,KAAK,GAAG,EAAE,EAAE;QACnB,OAAO,QAAQ;MACnB,CAAC,MAAM;QACH,OAAO,OAAO;MAClB;IACJ,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,0BAA0B;MACnCC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,iBAAiB;MAC3BC,OAAO,EAAE,uBAAuB;MAChCC,QAAQ,EAAE,2BAA2B;MACrCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,eAAe;MACvBC,IAAI,EAAE,sBAAsB;MAC5BC,CAAC,EAAE,iBAAiB;MACpBC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,iBAAiB;MACpBC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO9C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}