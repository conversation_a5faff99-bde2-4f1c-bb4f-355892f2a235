package com.esyndic.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "assemblies")
public class Assembly {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(nullable = false)
    @NotBlank(message = "Assembly title is required")
    private String title;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(name = "scheduled_date", nullable = false)
    @NotNull(message = "Scheduled date is required")
    private LocalDateTime scheduledDate;

    private String location;

    @Column(columnDefinition = "TEXT")
    private String agenda;

    @Column(name = "quorum_required")
    @Min(value = 1, message = "Quorum required must be at least 1%")
    @Max(value = 100, message = "Quorum required cannot exceed 100%")
    private Integer quorumRequired = 50;

    @Column(name = "quorum_achieved")
    @Min(value = 0, message = "Quorum achieved cannot be negative")
    @Max(value = 100, message = "Quorum achieved cannot exceed 100%")
    private Integer quorumAchieved = 0;

    @Enumerated(EnumType.STRING)
    private AssemblyStatus status = AssemblyStatus.SCHEDULED;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "building_id", nullable = false)
    @NotNull(message = "Building is required")
    private Building building;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by")
    private User createdBy;

    @OneToMany(mappedBy = "assembly", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<AssemblyAttendance> attendances;

    @OneToMany(mappedBy = "assembly", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<AssemblyVote> votes;

    // Enums
    public enum AssemblyStatus {
        SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED
    }

    // Constructors
    public Assembly() {}

    public Assembly(String title, LocalDateTime scheduledDate, Building building) {
        this.title = title;
        this.scheduledDate = scheduledDate;
        this.building = building;
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getScheduledDate() {
        return scheduledDate;
    }

    public void setScheduledDate(LocalDateTime scheduledDate) {
        this.scheduledDate = scheduledDate;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getAgenda() {
        return agenda;
    }

    public void setAgenda(String agenda) {
        this.agenda = agenda;
    }

    public Integer getQuorumRequired() {
        return quorumRequired;
    }

    public void setQuorumRequired(Integer quorumRequired) {
        this.quorumRequired = quorumRequired;
    }

    public Integer getQuorumAchieved() {
        return quorumAchieved;
    }

    public void setQuorumAchieved(Integer quorumAchieved) {
        this.quorumAchieved = quorumAchieved;
    }

    public AssemblyStatus getStatus() {
        return status;
    }

    public void setStatus(AssemblyStatus status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Building getBuilding() {
        return building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }

    public User getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(User createdBy) {
        this.createdBy = createdBy;
    }

    public List<AssemblyAttendance> getAttendances() {
        return attendances;
    }

    public void setAttendances(List<AssemblyAttendance> attendances) {
        this.attendances = attendances;
    }

    public List<AssemblyVote> getVotes() {
        return votes;
    }

    public void setVotes(List<AssemblyVote> votes) {
        this.votes = votes;
    }

    // Helper methods
    public boolean isQuorumMet() {
        return quorumAchieved >= quorumRequired;
    }

    public boolean isActive() {
        return status == AssemblyStatus.SCHEDULED || status == AssemblyStatus.IN_PROGRESS;
    }

    public boolean isCompleted() {
        return status == AssemblyStatus.COMPLETED;
    }

    @Override
    public String toString() {
        return "Assembly{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", scheduledDate=" + scheduledDate +
                ", status=" + status +
                ", quorumRequired=" + quorumRequired +
                ", quorumAchieved=" + quorumAchieved +
                '}';
    }
}
