export var KeycloakEventType;
(function (KeycloakEventType) {
    KeycloakEventType[KeycloakEventType["OnAuthError"] = 0] = "OnAuthError";
    KeycloakEventType[KeycloakEventType["OnAuthLogout"] = 1] = "OnAuthLogout";
    KeycloakEventType[KeycloakEventType["OnAuthRefreshError"] = 2] = "OnAuthRefreshError";
    KeycloakEventType[KeycloakEventType["OnAuthRefreshSuccess"] = 3] = "OnAuthRefreshSuccess";
    KeycloakEventType[KeycloakEventType["OnAuthSuccess"] = 4] = "OnAuthSuccess";
    KeycloakEventType[KeycloakEventType["OnReady"] = 5] = "OnReady";
    KeycloakEventType[KeycloakEventType["OnTokenExpired"] = 6] = "OnTokenExpired";
    KeycloakEventType[KeycloakEventType["OnActionUpdate"] = 7] = "OnActionUpdate";
})(KeycloakEventType || (KeycloakEventType = {}));
//# sourceMappingURL=data:application/json;base64,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