{"version": 3, "file": "radio.mjs", "sources": ["../../../../../../src/material/radio/radio.ts", "../../../../../../src/material/radio/radio.html", "../../../../../../src/material/radio/module.ts", "../../../../../../src/material/radio/radio_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  AfterContentInit,\n  AfterViewInit,\n  Attribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  Directive,\n  DoCheck,\n  ElementRef,\n  EventEmitter,\n  forwardRef,\n  Inject,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  OnInit,\n  Optional,\n  Output,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {\n  CanDisableRipple,\n  HasTabIndex,\n  mixinDisableRipple,\n  mixinTabIndex,\n  ThemePalette,\n} from '@angular/material/core';\nimport {FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {BooleanInput, coerceBooleanProperty, coerceNumberProperty} from '@angular/cdk/coercion';\nimport {UniqueSelectionDispatcher} from '@angular/cdk/collections';\nimport {ANIMATION_MODULE_TYPE} from '@angular/platform-browser/animations';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';\nimport {Subscription} from 'rxjs';\n\n// Increasing integer for generating unique ids for radio components.\nlet nextUniqueId = 0;\n\n/** Change event object emitted by radio button and radio group. */\nexport class MatRadioChange {\n  constructor(\n    /** The radio button that emits the change event. */\n    public source: _MatRadioButtonBase,\n    /** The value of the radio button. */\n    public value: any,\n  ) {}\n}\n\n/**\n * Provider Expression that allows mat-radio-group to register as a ControlValueAccessor. This\n * allows it to support [(ngModel)] and ngControl.\n * @docs-private\n */\nexport const MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatRadioGroup),\n  multi: true,\n};\n\n/**\n * Injection token that can be used to inject instances of `MatRadioGroup`. It serves as\n * alternative token to the actual `MatRadioGroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nexport const MAT_RADIO_GROUP = new InjectionToken<_MatRadioGroupBase<_MatRadioButtonBase>>(\n  'MatRadioGroup',\n);\n\nexport interface MatRadioDefaultOptions {\n  color: ThemePalette;\n}\n\nexport const MAT_RADIO_DEFAULT_OPTIONS = new InjectionToken<MatRadioDefaultOptions>(\n  'mat-radio-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_RADIO_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\nexport function MAT_RADIO_DEFAULT_OPTIONS_FACTORY(): MatRadioDefaultOptions {\n  return {\n    color: 'accent',\n  };\n}\n\n/**\n * Base class with all of the `MatRadioGroup` functionality.\n * @docs-private\n */\n@Directive()\nexport abstract class _MatRadioGroupBase<T extends _MatRadioButtonBase>\n  implements AfterContentInit, OnDestroy, ControlValueAccessor\n{\n  /** Selected value for the radio group. */\n  private _value: any = null;\n\n  /** The HTML name attribute applied to radio buttons in this group. */\n  private _name: string = `mat-radio-group-${nextUniqueId++}`;\n\n  /** The currently selected radio button. Should match value. */\n  private _selected: T | null = null;\n\n  /** Whether the `value` has been set to its initial value. */\n  private _isInitialized: boolean = false;\n\n  /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n  private _labelPosition: 'before' | 'after' = 'after';\n\n  /** Whether the radio group is disabled. */\n  private _disabled: boolean = false;\n\n  /** Whether the radio group is required. */\n  private _required: boolean = false;\n\n  /** Subscription to changes in amount of radio buttons. */\n  private _buttonChanges: Subscription;\n\n  /** The method to be called in order to update ngModel */\n  _controlValueAccessorChangeFn: (value: any) => void = () => {};\n\n  /**\n   * onTouch function registered via registerOnTouch (ControlValueAccessor).\n   * @docs-private\n   */\n  onTouched: () => any = () => {};\n\n  /**\n   * Event emitted when the group value changes.\n   * Change events are only emitted when the value changes due to user interaction with\n   * a radio button (the same behavior as `<input type-\"radio\">`).\n   */\n  @Output() readonly change: EventEmitter<MatRadioChange> = new EventEmitter<MatRadioChange>();\n\n  /** Child radio buttons. */\n  abstract _radios: QueryList<T>;\n\n  /** Theme color for all of the radio buttons in the group. */\n  @Input() color: ThemePalette;\n\n  /** Name of the radio button group. All radio buttons inside this group will use this name. */\n  @Input()\n  get name(): string {\n    return this._name;\n  }\n  set name(value: string) {\n    this._name = value;\n    this._updateRadioButtonNames();\n  }\n\n  /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n  @Input()\n  get labelPosition(): 'before' | 'after' {\n    return this._labelPosition;\n  }\n  set labelPosition(v) {\n    this._labelPosition = v === 'before' ? 'before' : 'after';\n    this._markRadiosForCheck();\n  }\n\n  /**\n   * Value for the radio-group. Should equal the value of the selected radio button if there is\n   * a corresponding radio button with a matching value. If there is not such a corresponding\n   * radio button, this value persists to be applied in case a new radio button is added with a\n   * matching value.\n   */\n  @Input()\n  get value(): any {\n    return this._value;\n  }\n  set value(newValue: any) {\n    if (this._value !== newValue) {\n      // Set this before proceeding to ensure no circular loop occurs with selection.\n      this._value = newValue;\n\n      this._updateSelectedRadioFromValue();\n      this._checkSelectedRadioButton();\n    }\n  }\n\n  _checkSelectedRadioButton() {\n    if (this._selected && !this._selected.checked) {\n      this._selected.checked = true;\n    }\n  }\n\n  /**\n   * The currently selected radio button. If set to a new radio button, the radio group value\n   * will be updated to match the new selected button.\n   */\n  @Input()\n  get selected() {\n    return this._selected;\n  }\n  set selected(selected: T | null) {\n    this._selected = selected;\n    this.value = selected ? selected.value : null;\n    this._checkSelectedRadioButton();\n  }\n\n  /** Whether the radio group is disabled */\n  @Input()\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: BooleanInput) {\n    this._disabled = coerceBooleanProperty(value);\n    this._markRadiosForCheck();\n  }\n\n  /** Whether the radio group is required */\n  @Input()\n  get required(): boolean {\n    return this._required;\n  }\n  set required(value: BooleanInput) {\n    this._required = coerceBooleanProperty(value);\n    this._markRadiosForCheck();\n  }\n\n  constructor(private _changeDetector: ChangeDetectorRef) {}\n\n  /**\n   * Initialize properties once content children are available.\n   * This allows us to propagate relevant attributes to associated buttons.\n   */\n  ngAfterContentInit() {\n    // Mark this component as initialized in AfterContentInit because the initial value can\n    // possibly be set by NgModel on MatRadioGroup, and it is possible that the OnInit of the\n    // NgModel occurs *after* the OnInit of the MatRadioGroup.\n    this._isInitialized = true;\n\n    // Clear the `selected` button when it's destroyed since the tabindex of the rest of the\n    // buttons depends on it. Note that we don't clear the `value`, because the radio button\n    // may be swapped out with a similar one and there are some internal apps that depend on\n    // that behavior.\n    this._buttonChanges = this._radios.changes.subscribe(() => {\n      if (this.selected && !this._radios.find(radio => radio === this.selected)) {\n        this._selected = null;\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._buttonChanges?.unsubscribe();\n  }\n\n  /**\n   * Mark this group as being \"touched\" (for ngModel). Meant to be called by the contained\n   * radio buttons upon their blur.\n   */\n  _touch() {\n    if (this.onTouched) {\n      this.onTouched();\n    }\n  }\n\n  private _updateRadioButtonNames(): void {\n    if (this._radios) {\n      this._radios.forEach(radio => {\n        radio.name = this.name;\n        radio._markForCheck();\n      });\n    }\n  }\n\n  /** Updates the `selected` radio button from the internal _value state. */\n  private _updateSelectedRadioFromValue(): void {\n    // If the value already matches the selected radio, do nothing.\n    const isAlreadySelected = this._selected !== null && this._selected.value === this._value;\n\n    if (this._radios && !isAlreadySelected) {\n      this._selected = null;\n      this._radios.forEach(radio => {\n        radio.checked = this.value === radio.value;\n        if (radio.checked) {\n          this._selected = radio;\n        }\n      });\n    }\n  }\n\n  /** Dispatch change event with current selection and group value. */\n  _emitChangeEvent(): void {\n    if (this._isInitialized) {\n      this.change.emit(new MatRadioChange(this._selected!, this._value));\n    }\n  }\n\n  _markRadiosForCheck() {\n    if (this._radios) {\n      this._radios.forEach(radio => radio._markForCheck());\n    }\n  }\n\n  /**\n   * Sets the model value. Implemented as part of ControlValueAccessor.\n   * @param value\n   */\n  writeValue(value: any) {\n    this.value = value;\n    this._changeDetector.markForCheck();\n  }\n\n  /**\n   * Registers a callback to be triggered when the model value changes.\n   * Implemented as part of ControlValueAccessor.\n   * @param fn Callback to be registered.\n   */\n  registerOnChange(fn: (value: any) => void) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n\n  /**\n   * Registers a callback to be triggered when the control is touched.\n   * Implemented as part of ControlValueAccessor.\n   * @param fn Callback to be registered.\n   */\n  registerOnTouched(fn: any) {\n    this.onTouched = fn;\n  }\n\n  /**\n   * Sets the disabled state of the control. Implemented as a part of ControlValueAccessor.\n   * @param isDisabled Whether the control should be disabled.\n   */\n  setDisabledState(isDisabled: boolean) {\n    this.disabled = isDisabled;\n    this._changeDetector.markForCheck();\n  }\n}\n\n// Boilerplate for applying mixins to MatRadioButton.\n/** @docs-private */\nabstract class MatRadioButtonBase {\n  // Since the disabled property is manually defined for the MatRadioButton and isn't set up in\n  // the mixin base class. To be able to use the tabindex mixin, a disabled property must be\n  // defined to properly work.\n  abstract disabled: boolean;\n  constructor(public _elementRef: ElementRef) {}\n}\n\nconst _MatRadioButtonMixinBase = mixinDisableRipple(mixinTabIndex(MatRadioButtonBase));\n\n/**\n * Base class with all of the `MatRadioButton` functionality.\n * @docs-private\n */\n@Directive()\nexport abstract class _MatRadioButtonBase\n  extends _MatRadioButtonMixinBase\n  implements OnInit, AfterViewInit, DoCheck, OnDestroy, CanDisableRipple, HasTabIndex\n{\n  private _uniqueId: string = `mat-radio-${++nextUniqueId}`;\n\n  /** The unique ID for the radio button. */\n  @Input() id: string = this._uniqueId;\n\n  /** Analog to HTML 'name' attribute used to group radios for unique selection. */\n  @Input() name: string;\n\n  /** Used to set the 'aria-label' attribute on the underlying input element. */\n  @Input('aria-label') ariaLabel: string;\n\n  /** The 'aria-labelledby' attribute takes precedence as the element's text alternative. */\n  @Input('aria-labelledby') ariaLabelledby: string;\n\n  /** The 'aria-describedby' attribute is read after the element's label and field type. */\n  @Input('aria-describedby') ariaDescribedby: string;\n\n  /** Whether this radio button is checked. */\n  @Input()\n  get checked(): boolean {\n    return this._checked;\n  }\n  set checked(value: BooleanInput) {\n    const newCheckedState = coerceBooleanProperty(value);\n    if (this._checked !== newCheckedState) {\n      this._checked = newCheckedState;\n      if (newCheckedState && this.radioGroup && this.radioGroup.value !== this.value) {\n        this.radioGroup.selected = this;\n      } else if (!newCheckedState && this.radioGroup && this.radioGroup.value === this.value) {\n        // When unchecking the selected radio button, update the selected radio\n        // property on the group.\n        this.radioGroup.selected = null;\n      }\n\n      if (newCheckedState) {\n        // Notify all radio buttons with the same name to un-check.\n        this._radioDispatcher.notify(this.id, this.name);\n      }\n      this._changeDetector.markForCheck();\n    }\n  }\n\n  /** The value of this radio button. */\n  @Input()\n  get value(): any {\n    return this._value;\n  }\n  set value(value: any) {\n    if (this._value !== value) {\n      this._value = value;\n      if (this.radioGroup !== null) {\n        if (!this.checked) {\n          // Update checked when the value changed to match the radio group's value\n          this.checked = this.radioGroup.value === value;\n        }\n        if (this.checked) {\n          this.radioGroup.selected = this;\n        }\n      }\n    }\n  }\n\n  /** Whether the label should appear after or before the radio button. Defaults to 'after' */\n  @Input()\n  get labelPosition(): 'before' | 'after' {\n    return this._labelPosition || (this.radioGroup && this.radioGroup.labelPosition) || 'after';\n  }\n  set labelPosition(value) {\n    this._labelPosition = value;\n  }\n  private _labelPosition: 'before' | 'after';\n\n  /** Whether the radio button is disabled. */\n  @Input()\n  get disabled(): boolean {\n    return this._disabled || (this.radioGroup !== null && this.radioGroup.disabled);\n  }\n  set disabled(value: BooleanInput) {\n    this._setDisabled(coerceBooleanProperty(value));\n  }\n\n  /** Whether the radio button is required. */\n  @Input()\n  get required(): boolean {\n    return this._required || (this.radioGroup && this.radioGroup.required);\n  }\n  set required(value: BooleanInput) {\n    this._required = coerceBooleanProperty(value);\n  }\n\n  /** Theme color of the radio button. */\n  @Input()\n  get color(): ThemePalette {\n    // As per Material design specifications the selection control radio should use the accent color\n    // palette by default. https://material.io/guidelines/components/selection-controls.html\n    return (\n      this._color ||\n      (this.radioGroup && this.radioGroup.color) ||\n      (this._providerOverride && this._providerOverride.color) ||\n      'accent'\n    );\n  }\n  set color(newValue: ThemePalette) {\n    this._color = newValue;\n  }\n  private _color: ThemePalette;\n\n  /**\n   * Event emitted when the checked state of this radio button changes.\n   * Change events are only emitted when the value changes due to user interaction with\n   * the radio button (the same behavior as `<input type-\"radio\">`).\n   */\n  @Output() readonly change: EventEmitter<MatRadioChange> = new EventEmitter<MatRadioChange>();\n\n  /** The parent radio group. May or may not be present. */\n  radioGroup: _MatRadioGroupBase<_MatRadioButtonBase>;\n\n  /** ID of the native input element inside `<mat-radio-button>` */\n  get inputId(): string {\n    return `${this.id || this._uniqueId}-input`;\n  }\n\n  /** Whether this radio is checked. */\n  private _checked: boolean = false;\n\n  /** Whether this radio is disabled. */\n  private _disabled: boolean;\n\n  /** Whether this radio is required. */\n  private _required: boolean;\n\n  /** Value assigned to this radio. */\n  private _value: any = null;\n\n  /** Unregister function for _radioDispatcher */\n  private _removeUniqueSelectionListener: () => void = () => {};\n\n  /** Previous value of the input's tabindex. */\n  private _previousTabIndex: number | undefined;\n\n  /** The native `<input type=radio>` element */\n  @ViewChild('input') _inputElement: ElementRef<HTMLInputElement>;\n\n  /** Whether animations are disabled. */\n  _noopAnimations: boolean;\n\n  constructor(\n    radioGroup: _MatRadioGroupBase<_MatRadioButtonBase>,\n    elementRef: ElementRef,\n    protected _changeDetector: ChangeDetectorRef,\n    private _focusMonitor: FocusMonitor,\n    private _radioDispatcher: UniqueSelectionDispatcher,\n    animationMode?: string,\n    private _providerOverride?: MatRadioDefaultOptions,\n    tabIndex?: string,\n  ) {\n    super(elementRef);\n\n    // Assertions. Ideally these should be stripped out by the compiler.\n    // TODO(jelbourn): Assert that there's no name binding AND a parent radio group.\n    this.radioGroup = radioGroup;\n    this._noopAnimations = animationMode === 'NoopAnimations';\n\n    if (tabIndex) {\n      this.tabIndex = coerceNumberProperty(tabIndex, 0);\n    }\n  }\n\n  /** Focuses the radio button. */\n  focus(options?: FocusOptions, origin?: FocusOrigin): void {\n    if (origin) {\n      this._focusMonitor.focusVia(this._inputElement, origin, options);\n    } else {\n      this._inputElement.nativeElement.focus(options);\n    }\n  }\n\n  /**\n   * Marks the radio button as needing checking for change detection.\n   * This method is exposed because the parent radio group will directly\n   * update bound properties of the radio button.\n   */\n  _markForCheck() {\n    // When group value changes, the button will not be notified. Use `markForCheck` to explicit\n    // update radio button's status\n    this._changeDetector.markForCheck();\n  }\n\n  ngOnInit() {\n    if (this.radioGroup) {\n      // If the radio is inside a radio group, determine if it should be checked\n      this.checked = this.radioGroup.value === this._value;\n\n      if (this.checked) {\n        this.radioGroup.selected = this;\n      }\n\n      // Copy name from parent radio group\n      this.name = this.radioGroup.name;\n    }\n\n    this._removeUniqueSelectionListener = this._radioDispatcher.listen((id, name) => {\n      if (id !== this.id && name === this.name) {\n        this.checked = false;\n      }\n    });\n  }\n\n  ngDoCheck(): void {\n    this._updateTabIndex();\n  }\n\n  ngAfterViewInit() {\n    this._updateTabIndex();\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n      if (!focusOrigin && this.radioGroup) {\n        this.radioGroup._touch();\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._removeUniqueSelectionListener();\n  }\n\n  /** Dispatch change event with current value. */\n  private _emitChangeEvent(): void {\n    this.change.emit(new MatRadioChange(this, this._value));\n  }\n\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n\n  _onInputClick(event: Event) {\n    // We have to stop propagation for click events on the visual hidden input element.\n    // By default, when a user clicks on a label element, a generated click event will be\n    // dispatched on the associated input element. Since we are using a label element as our\n    // root container, the click event on the `radio-button` will be executed twice.\n    // The real click event will bubble up, and the generated click event also tries to bubble up.\n    // This will lead to multiple click events.\n    // Preventing bubbling for the second event will solve that issue.\n    event.stopPropagation();\n  }\n\n  /** Triggered when the radio button receives an interaction from the user. */\n  _onInputInteraction(event: Event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n\n    if (!this.checked && !this.disabled) {\n      const groupValueChanged = this.radioGroup && this.value !== this.radioGroup.value;\n      this.checked = true;\n      this._emitChangeEvent();\n\n      if (this.radioGroup) {\n        this.radioGroup._controlValueAccessorChangeFn(this.value);\n        if (groupValueChanged) {\n          this.radioGroup._emitChangeEvent();\n        }\n      }\n    }\n  }\n\n  /** Triggered when the user clicks on the touch target. */\n  _onTouchTargetClick(event: Event) {\n    this._onInputInteraction(event);\n\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n\n  /** Sets the disabled state and marks for check if a change occurred. */\n  protected _setDisabled(value: boolean) {\n    if (this._disabled !== value) {\n      this._disabled = value;\n      this._changeDetector.markForCheck();\n    }\n  }\n\n  /** Gets the tabindex for the underlying input element. */\n  private _updateTabIndex() {\n    const group = this.radioGroup;\n    let value: number;\n\n    // Implement a roving tabindex if the button is inside a group. For most cases this isn't\n    // necessary, because the browser handles the tab order for inputs inside a group automatically,\n    // but we need an explicitly higher tabindex for the selected button in order for things like\n    // the focus trap to pick it up correctly.\n    if (!group || !group.selected || this.disabled) {\n      value = this.tabIndex;\n    } else {\n      value = group.selected === this ? this.tabIndex : -1;\n    }\n\n    if (value !== this._previousTabIndex) {\n      // We have to set the tabindex directly on the DOM node, because it depends on\n      // the selected state which is prone to \"changed after checked errors\".\n      const input: HTMLInputElement | undefined = this._inputElement?.nativeElement;\n\n      if (input) {\n        input.setAttribute('tabindex', value + '');\n        this._previousTabIndex = value;\n      }\n    }\n  }\n}\n\n/**\n * A group of radio buttons. May contain one or more `<mat-radio-button>` elements.\n */\n@Directive({\n  selector: 'mat-radio-group',\n  exportAs: 'matRadioGroup',\n  providers: [\n    MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR,\n    {provide: MAT_RADIO_GROUP, useExisting: MatRadioGroup},\n  ],\n  host: {\n    'role': 'radiogroup',\n    'class': 'mat-mdc-radio-group',\n  },\n})\nexport class MatRadioGroup extends _MatRadioGroupBase<MatRadioButton> {\n  /** Child radio buttons. */\n  @ContentChildren(forwardRef(() => MatRadioButton), {descendants: true})\n  _radios: QueryList<MatRadioButton>;\n}\n\n@Component({\n  selector: 'mat-radio-button',\n  templateUrl: 'radio.html',\n  styleUrls: ['radio.css'],\n  host: {\n    'class': 'mat-mdc-radio-button',\n    '[attr.id]': 'id',\n    '[class.mat-primary]': 'color === \"primary\"',\n    '[class.mat-accent]': 'color === \"accent\"',\n    '[class.mat-warn]': 'color === \"warn\"',\n    '[class.mat-mdc-radio-checked]': 'checked',\n    '[class._mat-animation-noopable]': '_noopAnimations',\n    // Needs to be removed since it causes some a11y issues (see #21266).\n    '[attr.tabindex]': 'null',\n    '[attr.aria-label]': 'null',\n    '[attr.aria-labelledby]': 'null',\n    '[attr.aria-describedby]': 'null',\n    // Note: under normal conditions focus shouldn't land on this element, however it may be\n    // programmatically set, for example inside of a focus trap, in this case we want to forward\n    // the focus to the native element.\n    '(focus)': '_inputElement.nativeElement.focus()',\n  },\n  inputs: ['disableRipple', 'tabIndex'],\n  exportAs: 'matRadioButton',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatRadioButton extends _MatRadioButtonBase {\n  constructor(\n    @Optional() @Inject(MAT_RADIO_GROUP) radioGroup: MatRadioGroup,\n    elementRef: ElementRef,\n    _changeDetector: ChangeDetectorRef,\n    _focusMonitor: FocusMonitor,\n    _radioDispatcher: UniqueSelectionDispatcher,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n    @Optional()\n    @Inject(MAT_RADIO_DEFAULT_OPTIONS)\n    _providerOverride?: MatRadioDefaultOptions,\n    @Attribute('tabindex') tabIndex?: string,\n  ) {\n    super(\n      radioGroup,\n      elementRef,\n      _changeDetector,\n      _focusMonitor,\n      _radioDispatcher,\n      animationMode,\n      _providerOverride,\n      tabIndex,\n    );\n  }\n}\n", "<div class=\"mdc-form-field\" #formField\n     [class.mdc-form-field--align-end]=\"labelPosition == 'before'\">\n  <div class=\"mdc-radio\" [class.mdc-radio--disabled]=\"disabled\">\n    <!-- Render this element first so the input is on top. -->\n    <div class=\"mat-mdc-radio-touch-target\" (click)=\"_onTouchTargetClick($event)\"></div>\n    <input #input class=\"mdc-radio__native-control\" type=\"radio\"\n           [id]=\"inputId\"\n           [checked]=\"checked\"\n           [disabled]=\"disabled\"\n           [attr.name]=\"name\"\n           [attr.value]=\"value\"\n           [required]=\"required\"\n           [attr.aria-label]=\"ariaLabel\"\n           [attr.aria-labelledby]=\"ariaLabelledby\"\n           [attr.aria-describedby]=\"ariaDescribedby\"\n           (change)=\"_onInputInteraction($event)\">\n    <div class=\"mdc-radio__background\">\n      <div class=\"mdc-radio__outer-circle\"></div>\n      <div class=\"mdc-radio__inner-circle\"></div>\n    </div>\n    <div mat-ripple class=\"mat-radio-ripple mat-mdc-focus-indicator\"\n         [matRippleTrigger]=\"formField\"\n         [matRippleDisabled]=\"_isRippleDisabled()\"\n         [matRippleCentered]=\"true\">\n      <div class=\"mat-ripple-element mat-radio-persistent-ripple\"></div>\n    </div>\n  </div>\n  <label class=\"mdc-label\" [for]=\"inputId\">\n    <ng-content></ng-content>\n  </label>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '@angular/material/core';\nimport {MatRadioButton, MatRadioGroup} from './radio';\n\n@NgModule({\n  imports: [MatCommonModule, CommonModule, MatRippleModule],\n  exports: [MatCommonModule, MatRadioGroup, MatRadioButton],\n  declarations: [MatRadioGroup, MatRadioButton],\n})\nexport class MatRadioModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;;;AA8CA;AACA,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB;MACa,cAAc,CAAA;AACzB,IAAA,WAAA;;IAES,MAA2B;;IAE3B,KAAU,EAAA;QAFV,IAAM,CAAA,MAAA,GAAN,MAAM,CAAqB;QAE3B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAK;KACf;AACL,CAAA;AAED;;;;AAIG;AACU,MAAA,sCAAsC,GAAQ;AACzD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,aAAa,CAAC;AAC5C,IAAA,KAAK,EAAE,IAAI;EACX;AAEF;;;;AAIG;MACU,eAAe,GAAG,IAAI,cAAc,CAC/C,eAAe,EACf;MAMW,yBAAyB,GAAG,IAAI,cAAc,CACzD,2BAA2B,EAC3B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,iCAAiC;AAC3C,CAAA,EACD;SAEc,iCAAiC,GAAA;IAC/C,OAAO;AACL,QAAA,KAAK,EAAE,QAAQ;KAChB,CAAC;AACJ,CAAC;AAED;;;AAGG;MAEmB,kBAAkB,CAAA;;AAkDtC,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IACD,IAAI,IAAI,CAAC,KAAa,EAAA;AACpB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,uBAAuB,EAAE,CAAC;KAChC;;AAGD,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;IACD,IAAI,aAAa,CAAC,CAAC,EAAA;AACjB,QAAA,IAAI,CAAC,cAAc,GAAG,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC;QAC1D,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;AAED;;;;;AAKG;AACH,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IACD,IAAI,KAAK,CAAC,QAAa,EAAA;AACrB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;;AAE5B,YAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;YAEvB,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACrC,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAClC,SAAA;KACF;IAED,yBAAyB,GAAA;QACvB,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AAC7C,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;AAC/B,SAAA;KACF;AAED;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,QAAkB,EAAA;AAC7B,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,QAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;QAC9C,IAAI,CAAC,yBAAyB,EAAE,CAAC;KAClC;;AAGD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;;AAGD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;AAED,IAAA,WAAA,CAAoB,eAAkC,EAAA;QAAlC,IAAe,CAAA,eAAA,GAAf,eAAe,CAAmB;;QA7H9C,IAAM,CAAA,MAAA,GAAQ,IAAI,CAAC;;AAGnB,QAAA,IAAA,CAAA,KAAK,GAAW,CAAA,gBAAA,EAAmB,YAAY,EAAE,EAAE,CAAC;;QAGpD,IAAS,CAAA,SAAA,GAAa,IAAI,CAAC;;QAG3B,IAAc,CAAA,cAAA,GAAY,KAAK,CAAC;;QAGhC,IAAc,CAAA,cAAA,GAAuB,OAAO,CAAC;;QAG7C,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;;QAG3B,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;;AAMnC,QAAA,IAAA,CAAA,6BAA6B,GAAyB,MAAK,GAAG,CAAC;AAE/D;;;AAGG;AACH,QAAA,IAAA,CAAA,SAAS,GAAc,MAAK,GAAG,CAAC;AAEhC;;;;AAIG;AACgB,QAAA,IAAA,CAAA,MAAM,GAAiC,IAAI,YAAY,EAAkB,CAAC;KAwFnC;AAE1D;;;AAGG;IACH,kBAAkB,GAAA;;;;AAIhB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;;;;;AAM3B,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;YACxD,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE;AACzE,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACvB,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,EAAE,WAAW,EAAE,CAAC;KACpC;AAED;;;AAGG;IACH,MAAM,GAAA;QACJ,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,EAAE,CAAC;AAClB,SAAA;KACF;IAEO,uBAAuB,GAAA;QAC7B,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAG;AAC3B,gBAAA,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gBACvB,KAAK,CAAC,aAAa,EAAE,CAAC;AACxB,aAAC,CAAC,CAAC;AACJ,SAAA;KACF;;IAGO,6BAA6B,GAAA;;AAEnC,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC;AAE1F,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,iBAAiB,EAAE;AACtC,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAG;gBAC3B,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC;gBAC3C,IAAI,KAAK,CAAC,OAAO,EAAE;AACjB,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACxB,iBAAA;AACH,aAAC,CAAC,CAAC;AACJ,SAAA;KACF;;IAGD,gBAAgB,GAAA;QACd,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,SAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACpE,SAAA;KACF;IAED,mBAAmB,GAAA;QACjB,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;AACtD,SAAA;KACF;AAED;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;KACrC;AAED;;;;AAIG;AACH,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,6BAA6B,GAAG,EAAE,CAAC;KACzC;AAED;;;;AAIG;AACH,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;KACrB;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;AAC3B,QAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;KACrC;8GA9OmB,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAlB,kBAAkB,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,aAAA,EAAA,eAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBADvC,SAAS;wGA0CW,MAAM,EAAA,CAAA;sBAAxB,MAAM;gBAME,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAIF,IAAI,EAAA,CAAA;sBADP,KAAK;gBAWF,aAAa,EAAA,CAAA;sBADhB,KAAK;gBAgBF,KAAK,EAAA,CAAA;sBADR,KAAK;gBAyBF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAYF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAWF,QAAQ,EAAA,CAAA;sBADX,KAAK;;AAyHR;AACA;AACA,MAAe,kBAAkB,CAAA;AAK/B,IAAA,WAAA,CAAmB,WAAuB,EAAA;QAAvB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;KAAI;AAC/C,CAAA;AAED,MAAM,wBAAwB,GAAG,kBAAkB,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC;AAEvF;;;AAGG;AAEG,MAAgB,mBACpB,SAAQ,wBAAwB,CAAA;;AAqBhC,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,KAAmB,EAAA;AAC7B,QAAA,MAAM,eAAe,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;AACrD,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,eAAe,EAAE;AACrC,YAAA,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC;AAChC,YAAA,IAAI,eAAe,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AAC9E,gBAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;AACjC,aAAA;AAAM,iBAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;;;AAGtF,gBAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;AACjC,aAAA;AAED,YAAA,IAAI,eAAe,EAAE;;AAEnB,gBAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAClD,aAAA;AACD,YAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;AACrC,SAAA;KACF;;AAGD,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IACD,IAAI,KAAK,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;AACzB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;AAC5B,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;;oBAEjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC;AAChD,iBAAA;gBACD,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,oBAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;AACjC,iBAAA;AACF,aAAA;AACF,SAAA;KACF;;AAGD,IAAA,IACI,aAAa,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC;KAC7F;IACD,IAAI,aAAa,CAAC,KAAK,EAAA;AACrB,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;KAC7B;;AAID,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;KACjF;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;QAC9B,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC;KACjD;;AAGD,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;KACxE;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC/C;;AAGD,IAAA,IACI,KAAK,GAAA;;;QAGP,QACE,IAAI,CAAC,MAAM;aACV,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;aACzC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;AACxD,YAAA,QAAQ,EACR;KACH;IACD,IAAI,KAAK,CAAC,QAAsB,EAAA;AAC9B,QAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;KACxB;;AAcD,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,CAAA,EAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,SAAS,CAAA,MAAA,CAAQ,CAAC;KAC7C;AA0BD,IAAA,WAAA,CACE,UAAmD,EACnD,UAAsB,EACZ,eAAkC,EACpC,aAA2B,EAC3B,gBAA2C,EACnD,aAAsB,EACd,iBAA0C,EAClD,QAAiB,EAAA;QAEjB,KAAK,CAAC,UAAU,CAAC,CAAC;QAPR,IAAe,CAAA,eAAA,GAAf,eAAe,CAAmB;QACpC,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;QAC3B,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAA2B;QAE3C,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAyB;AAzJ5C,QAAA,IAAA,CAAA,SAAS,GAAW,CAAA,UAAA,EAAa,EAAE,YAAY,EAAE,CAAC;;AAGjD,QAAA,IAAA,CAAA,EAAE,GAAW,IAAI,CAAC,SAAS,CAAC;AAwGrC;;;;AAIG;AACgB,QAAA,IAAA,CAAA,MAAM,GAAiC,IAAI,YAAY,EAAkB,CAAC;;QAWrF,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;;QAS1B,IAAM,CAAA,MAAA,GAAQ,IAAI,CAAC;;AAGnB,QAAA,IAAA,CAAA,8BAA8B,GAAe,MAAK,GAAG,CAAC;;;AAyB5D,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,IAAI,CAAC,eAAe,GAAG,aAAa,KAAK,gBAAgB,CAAC;AAE1D,QAAA,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACnD,SAAA;KACF;;IAGD,KAAK,CAAC,OAAsB,EAAE,MAAoB,EAAA;AAChD,QAAA,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAClE,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACjD,SAAA;KACF;AAED;;;;AAIG;IACH,aAAa,GAAA;;;AAGX,QAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;KACrC;IAED,QAAQ,GAAA;QACN,IAAI,IAAI,CAAC,UAAU,EAAE;;AAEnB,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC;YAErD,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,gBAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;AACjC,aAAA;;YAGD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AAClC,SAAA;AAED,QAAA,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,IAAI,KAAI;YAC9E,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;AACxC,gBAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACtB,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;IAED,SAAS,GAAA;QACP,IAAI,CAAC,eAAe,EAAE,CAAC;KACxB;IAED,eAAe,GAAA;QACb,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,IAAG;AACzE,YAAA,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE;AACnC,gBAAA,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;AAC1B,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,8BAA8B,EAAE,CAAC;KACvC;;IAGO,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;KACzD;IAED,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC;KAC5C;AAED,IAAA,aAAa,CAAC,KAAY,EAAA;;;;;;;;QAQxB,KAAK,CAAC,eAAe,EAAE,CAAC;KACzB;;AAGD,IAAA,mBAAmB,CAAC,KAAY,EAAA;;;;QAI9B,KAAK,CAAC,eAAe,EAAE,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACnC,YAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;AAClF,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAExB,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,UAAU,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1D,gBAAA,IAAI,iBAAiB,EAAE;AACrB,oBAAA,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;AACpC,iBAAA;AACF,aAAA;AACF,SAAA;KACF;;AAGD,IAAA,mBAAmB,CAAC,KAAY,EAAA;AAC9B,QAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAEhC,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;;AAGlB,YAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAC1C,SAAA;KACF;;AAGS,IAAA,YAAY,CAAC,KAAc,EAAA;AACnC,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,YAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;AACrC,SAAA;KACF;;IAGO,eAAe,GAAA;AACrB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;AAC9B,QAAA,IAAI,KAAa,CAAC;;;;;QAMlB,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC9C,YAAA,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC;AACvB,SAAA;AAAM,aAAA;AACL,YAAA,KAAK,GAAG,KAAK,CAAC,QAAQ,KAAK,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AACtD,SAAA;AAED,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,iBAAiB,EAAE;;;AAGpC,YAAA,MAAM,KAAK,GAAiC,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC;AAE9E,YAAA,IAAI,KAAK,EAAE;gBACT,KAAK,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC;AAC3C,gBAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;AAChC,aAAA;AACF,SAAA;KACF;8GA3TmB,mBAAmB,EAAA,IAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAnB,mBAAmB,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,eAAA,EAAA,CAAA,kBAAA,EAAA,iBAAA,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,eAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBADxC,SAAS;iSAQC,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAGG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAGe,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY,CAAA;gBAGO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB,CAAA;gBAGG,eAAe,EAAA,CAAA;sBAAzC,KAAK;uBAAC,kBAAkB,CAAA;gBAIrB,OAAO,EAAA,CAAA;sBADV,KAAK;gBA0BF,KAAK,EAAA,CAAA;sBADR,KAAK;gBAqBF,aAAa,EAAA,CAAA;sBADhB,KAAK;gBAWF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAUF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAUF,KAAK,EAAA,CAAA;sBADR,KAAK;gBAqBa,MAAM,EAAA,CAAA;sBAAxB,MAAM;gBA6Ba,aAAa,EAAA,CAAA;sBAAhC,SAAS;uBAAC,OAAO,CAAA;;AA6KpB;;AAEG;AAaG,MAAO,aAAc,SAAQ,kBAAkC,CAAA;8GAAxD,aAAa,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EATb,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,SAAA,EAAA;YACT,sCAAsC;AACtC,YAAA,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAC;AACvD,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAAA,EAAA,CAAA,UAAA,CAAA,YAAA,EAAA,OAQiC,cAAc,CAAA,EAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAFrC,aAAa,EAAA,UAAA,EAAA,CAAA;kBAZzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,SAAS,EAAE;wBACT,sCAAsC;AACtC,wBAAA,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,eAAe,EAAC;AACvD,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,YAAY;AACpB,wBAAA,OAAO,EAAE,qBAAqB;AAC/B,qBAAA;AACF,iBAAA,CAAA;8BAIC,OAAO,EAAA,CAAA;sBADN,eAAe;uBAAC,UAAU,CAAC,MAAM,cAAc,CAAC,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;;AA+BlE,MAAO,cAAe,SAAQ,mBAAmB,CAAA;AACrD,IAAA,WAAA,CACuC,UAAyB,EAC9D,UAAsB,EACtB,eAAkC,EAClC,aAA2B,EAC3B,gBAA2C,EACA,aAAsB,EAGjE,iBAA0C,EACnB,QAAiB,EAAA;AAExC,QAAA,KAAK,CACH,UAAU,EACV,UAAU,EACV,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,aAAa,EACb,iBAAiB,EACjB,QAAQ,CACT,CAAC;KACH;AAvBU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,kBAEH,eAAe,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,yBAAA,EAAA,EAAA,EAAA,KAAA,EAKf,qBAAqB,EAEjC,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,yBAAyB,6BAEtB,UAAU,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAXZ,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,spBCttB3B,g3CA+BA,EAAA,MAAA,EAAA,CAAA,8/WAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDurBa,cAAc,EAAA,UAAA,EAAA,CAAA;kBA3B1B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,kBAAkB,EAGtB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,qBAAqB,EAAE,qBAAqB;AAC5C,wBAAA,oBAAoB,EAAE,oBAAoB;AAC1C,wBAAA,kBAAkB,EAAE,kBAAkB;AACtC,wBAAA,+BAA+B,EAAE,SAAS;AAC1C,wBAAA,iCAAiC,EAAE,iBAAiB;;AAEpD,wBAAA,iBAAiB,EAAE,MAAM;AACzB,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,wBAAwB,EAAE,MAAM;AAChC,wBAAA,yBAAyB,EAAE,MAAM;;;;AAIjC,wBAAA,SAAS,EAAE,qCAAqC;AACjD,qBAAA,EAAA,MAAA,EACO,CAAC,eAAe,EAAE,UAAU,CAAC,EAC3B,QAAA,EAAA,gBAAgB,EACX,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,g3CAAA,EAAA,MAAA,EAAA,CAAA,8/WAAA,CAAA,EAAA,CAAA;;0BAI5C,QAAQ;;0BAAI,MAAM;2BAAC,eAAe,CAAA;;0BAKlC,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,QAAQ;;0BACR,MAAM;2BAAC,yBAAyB,CAAA;;0BAEhC,SAAS;2BAAC,UAAU,CAAA;;;ME/sBZ,cAAc,CAAA;8GAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAd,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAFV,YAAA,EAAA,CAAA,aAAa,EAAE,cAAc,aAFlC,eAAe,EAAE,YAAY,EAAE,eAAe,CAC9C,EAAA,OAAA,EAAA,CAAA,eAAe,EAAE,aAAa,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA,EAAA;AAG7C,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAJf,eAAe,EAAE,YAAY,EAAE,eAAe,EAC9C,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAGd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,eAAe,CAAC;AACzD,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,aAAa,EAAE,cAAc,CAAC;AACzD,oBAAA,YAAY,EAAE,CAAC,aAAa,EAAE,cAAc,CAAC;AAC9C,iBAAA,CAAA;;;ACjBD;;AAEG;;;;"}