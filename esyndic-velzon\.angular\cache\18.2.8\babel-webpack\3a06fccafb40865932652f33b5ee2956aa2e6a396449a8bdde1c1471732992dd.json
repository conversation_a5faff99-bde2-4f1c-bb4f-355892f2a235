{"ast": null, "code": "//! moment.js locale configuration\n//! locale : English (United Kingdom) [en-gb]\n//! author : <PERSON> : https://github.com/chrisgedrim\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var enGb = moment.defineLocale('en-gb', {\n    months: 'January_February_March_April_May_June_July_August_September_October_November_December'.split('_'),\n    monthsShort: 'Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec'.split('_'),\n    weekdays: 'Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday'.split('_'),\n    weekdaysShort: 'Sun_Mon_Tue_Wed_Thu_Fri_Sat'.split('_'),\n    weekdaysMin: 'Su_Mo_Tu_We_Th_Fr_Sa'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Today at] LT',\n      nextDay: '[Tomorrow at] LT',\n      nextWeek: 'dddd [at] LT',\n      lastDay: '[Yesterday at] LT',\n      lastWeek: '[Last] dddd [at] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'in %s',\n      past: '%s ago',\n      s: 'a few seconds',\n      ss: '%d seconds',\n      m: 'a minute',\n      mm: '%d minutes',\n      h: 'an hour',\n      hh: '%d hours',\n      d: 'a day',\n      dd: '%d days',\n      M: 'a month',\n      MM: '%d months',\n      y: 'a year',\n      yy: '%d years'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(st|nd|rd|th)/,\n    ordinal: function (number) {\n      var b = number % 10,\n        output = ~~(number % 100 / 10) === 1 ? 'th' : b === 1 ? 'st' : b === 2 ? 'nd' : b === 3 ? 'rd' : 'th';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return enGb;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "enGb", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "b", "output", "week", "dow", "doy"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/moment/locale/en-gb.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : English (United Kingdom) [en-gb]\n//! author : <PERSON> : https://github.com/chrisgedrim\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var enGb = moment.defineLocale('en-gb', {\n        months: 'January_February_March_April_May_June_July_August_September_October_November_December'.split(\n            '_'\n        ),\n        monthsShort: 'Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec'.split('_'),\n        weekdays: 'Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday'.split(\n            '_'\n        ),\n        weekdaysShort: 'Sun_Mon_Tue_Wed_Thu_Fri_Sat'.split('_'),\n        weekdaysMin: 'Su_Mo_Tu_We_Th_Fr_Sa'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Today at] LT',\n            nextDay: '[Tomorrow at] LT',\n            nextWeek: 'dddd [at] LT',\n            lastDay: '[Yesterday at] LT',\n            lastWeek: '[Last] dddd [at] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'in %s',\n            past: '%s ago',\n            s: 'a few seconds',\n            ss: '%d seconds',\n            m: 'a minute',\n            mm: '%d minutes',\n            h: 'an hour',\n            hh: '%d hours',\n            d: 'a day',\n            dd: '%d days',\n            M: 'a month',\n            MM: '%d months',\n            y: 'a year',\n            yy: '%d years',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(st|nd|rd|th)/,\n        ordinal: function (number) {\n            var b = number % 10,\n                output =\n                    ~~((number % 100) / 10) === 1\n                        ? 'th'\n                        : b === 1\n                          ? 'st'\n                          : b === 2\n                            ? 'nd'\n                            : b === 3\n                              ? 'rd'\n                              : 'th';\n            return number + output;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return enGb;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,IAAI,GAAGD,MAAM,CAACE,YAAY,CAAC,OAAO,EAAE;IACpCC,MAAM,EAAE,uFAAuF,CAACC,KAAK,CACjG,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,0DAA0D,CAACF,KAAK,CACtE,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,mBAAmB;MAC5BC,QAAQ,EAAE,qBAAqB;MAC/BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,sBAAsB;IAC9CC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,IAAIC,CAAC,GAAGD,MAAM,GAAG,EAAE;QACfE,MAAM,GACF,CAAC,EAAGF,MAAM,GAAG,GAAG,GAAI,EAAE,CAAC,KAAK,CAAC,GACvB,IAAI,GACJC,CAAC,KAAK,CAAC,GACL,IAAI,GACJA,CAAC,KAAK,CAAC,GACL,IAAI,GACJA,CAAC,KAAK,CAAC,GACL,IAAI,GACJ,IAAI;MACxB,OAAOD,MAAM,GAAGE,MAAM;IAC1B,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO5C,IAAI;AAEf,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}