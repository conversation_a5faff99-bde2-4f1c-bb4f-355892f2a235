<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Project List" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row g-4 mb-3">
    <div class="col-sm-auto">
        <div>
            <a routerLink="/projects/create" class="btn btn-success"><i class="ri-add-line align-bottom me-1"></i> Add
                New</a>
        </div>
    </div>
    <div class="col-sm">
        <div class="d-flex justify-content-sm-end gap-2">
            <div class="search-box ms-2">
                <input type="text" class="form-control" placeholder="Search..." [(ngModel)]="searchTerm" (ngModelChange)="performSearch()">
                <i class="ri-search-line search-icon"></i>
            </div>

            <div class="w-md">
                <select class="form-control" data-choices data-choices-search-false>
                    <option value="All">All</option>
                    <option value="Today">Today</option>
                    <option value="Yesterday" selected>Yesterday</option>
                    <option value="Last 7 Days">Last 7 Days</option>
                    <option value="Last 30 Days">Last 30 Days</option>
                    <option value="This Month">This Month</option>
                    <option value="Last Year">Last Year</option>
                </select>
            </div>
        </div>
    </div>
</div>

<div class="row">
    @for(data of projectListWidgets;track $index){
    @if(data.isDesign1){
    <div class="col-xxl-3 col-sm-6 project-card" id="pl1_{{data.id}}">
        <div class="card card-height-100">
            <div class="card-body">
                <div class="d-flex flex-column h-100">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-muted mb-4">{{data.time}}</p>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="d-flex gap-1 align-items-center">
                                <button type="button" class="btn avatar-xs mt-n1 p-0 favourite-btn heart_icon_{{data.id}}" [ngClass]="{'active': data.isIcon !== true}" (click)="activeMenu(data.id)">
                                    <span class="avatar-title bg-transparent fs-15">
                                        <i class="ri-star-fill"></i>
                                    </span>
                                </button>
                                <div class="dropdown" ngbDropdown>
                                    <button class="btn btn-link text-muted p-1 mt-n2 py-0 text-decoration-none fs-15 arrow-none" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="true" ngbDropdownToggle>
                                        <i-feather name="more-horizontal" class="icon-sm"></i-feather>
                                    </button>

                                    <div class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                                        <a class="dropdown-item" routerLink="/projects/overview"><i class="ri-eye-fill align-bottom me-2 text-muted float-start"></i>
                                            View</a>
                                        <a class="dropdown-item" routerLink="/projects/create"><i class="ri-pencil-fill align-bottom me-2 text-muted float-start"></i>
                                            Edit</a>
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item" href="javascript:void(0);" data-bs-toggle="modal" data-bs-target="#removeProjectModal" (click)="confirm(deleteModel,data.id)"><i class="ri-delete-bin-fill align-bottom me-2 text-muted float-start"></i>
                                            Remove</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex mb-2">
                        <div class="flex-shrink-0 me-3">
                            <div class="avatar-sm">
                                <span class="avatar-title bg-warning-subtle rounded p-2">
                                    <img src="{{data.img}}" alt="" class="img-fluid p-1">
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1 fs-15"><a routerLink="/projects/overview" class="text-body">{{data.label}}</a></h5>
                            <p class="text-muted text-truncate-two-lines mb-3">{{data.caption}}</p>
                        </div>
                    </div>
                    <div class="mt-auto">
                        <div class="d-flex mb-2">
                            <div class="flex-grow-1">
                                <div>Tasks</div>
                            </div>
                            <div class="flex-shrink-0">
                                <div><i class="ri-list-check align-bottom me-1 text-muted"></i>{{data.number}}</div>
                            </div>
                        </div>
                        <div class="progress-sm animated-progess">
                            <ngb-progressbar [value]="data.progressBar" type="success" class="progress-sm"></ngb-progressbar>
                        </div><!-- /.progress -->
                    </div>
                </div>

            </div>
            <!-- end card body -->
            <div class="card-footer bg-transparent border-top-dashed py-2">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="avatar-group">
                            @for(user of data.users; track $index){
                            <div class="avatar-group-item">
                                @if(user.profile) {<a href="javascript: void(0);" class="d-block" ngbTooltip="{{user.name}}" placement="top">
                                    <img :src="{{user.profile}}" alt="" class="rounded-circle avatar-xxs">
                                </a>}@else{
                                <a href="javascript: void(0);" class="d-block" ngbTooltip="{{user.name}}" placement="top">
                                    <div class="avatar-xxs">
                                        <span class="avatar-title rounded-circle {{user.variant}}">
                                            {{user.text}}
                                        </span>
                                    </div>
                                </a>
                                }


                            </div>
                            }
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="text-muted">
                            <i class="ri-calendar-event-fill mx-1 align-bottom"></i>{{data.date}}
                        </div>
                    </div>

                </div>

            </div>
            <!-- end card footer -->
        </div>
        <!-- end card -->
    </div>
    }
    @if(data.isDesign2){
    <div class="col-xxl-3 col-sm-6 project-card" id="pl1_{{data.id}}">
        <div class="card">
            <div class="card-body">
                <div class="p-3 mt-n3 mx-n3 bg-{{data.bg_color}}-subtle text-{{data.bg_color}} rounded-top">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="mb-0 fs-14"><a routerLink="/projects/overview" class="text-body">{{data.label}}</a></h5>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="d-flex gap-1 align-items-center my-n2">
                                <button type="button" class="btn avatar-xs p-0 favourite-btn heart_icon_{{data.id}}" [ngClass]="{'active': data.isIcon !== true}" (click)="activeMenu(data.id)">
                                    <span class="avatar-title bg-transparent fs-15">
                                        <i class="ri-star-fill"></i>
                                    </span>
                                </button>
                                <div class="dropdown" ngbDropdown>
                                    <button class="btn btn-link text-muted p-1 mt-n2 py-0 text-decoration-none fs-15 arrow-none" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="true" ngbDropdownToggle>
                                        <i-feather name="more-horizontal" class="icon-sm"></i-feather>
                                    </button>

                                    <div class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                                        <a class="dropdown-item" routerLink="/projects/overview"><i class="ri-eye-fill align-bottom me-2 text-muted float-start"></i>
                                            View</a>
                                        <a class="dropdown-item" routerLink="/projects/create"><i class="ri-pencil-fill align-bottom me-2 text-muted float-start"></i>
                                            Edit</a>
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item" href="javascript:void(0);" data-bs-toggle="modal" data-bs-target="#removeProjectModal" (click)="confirm(deleteModel,data.id)"><i class="ri-delete-bin-fill align-bottom me-2 text-muted float-start"></i>
                                            Remove</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="py-3">
                    <div class="row gy-3">
                        <div class="col-6">
                            <div>
                                <p class="text-muted mb-1">Status</p>
                                <div class="badge bg-{{data.statusClass}}-subtle text-{{data.statusClass}} fs-12">
                                    {{data.status}}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div>
                                <p class="text-muted mb-1">Deadline</p>
                                <h5 class="fs-14">{{data.deadline}}</h5>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex align-items-center mt-3">
                        <p class="text-muted mb-0 me-2">Team :</p>
                        <div class="avatar-group">
                            @for(user of data.users;track $index){

                            <div class="avatar-group-item">
                                @if (user.profile) {
                                <a href="javascript: void(0);" class="d-block" ngbTooltip="{{user.name}}" placement="top">
                                    <img :src="{{user.profile}}" alt="" class="rounded-circle avatar-xxs">
                                </a>
                                }@else(){
                                <a href="javascript: void(0);" class="d-block" ngbTooltip="{{user.name}}" placement="top">
                                    <div class="avatar-xxs">
                                        <span class="avatar-title rounded-circle {{user.variant}}">
                                            {{user.text}}
                                        </span>
                                    </div>
                                </a>
                                }

                            </div>
                            }
                        </div>
                    </div>
                </div>
                <div>
                    <div class="d-flex mb-2">
                        <div class="flex-grow-1">
                            <div>Progress</div>
                        </div>
                        <div class="flex-shrink-0">
                            <div>{{data.progressBar}}%</div>
                        </div>
                    </div>
                    <ngb-progressbar [value]="data.progressBar" type="success" class="progress-sm"></ngb-progressbar>
                </div>

            </div>
            <!-- end card body -->
        </div>
        <!-- end card -->
    </div>
    }
    @if(data.isDesign3){
    <div class="col-xxl-3 col-sm-6 project-card" id="pl1_{{data.id}}">
        <div class="card">
            <div class="card-body">
                <div class="p-3 mt-n3 mx-n3 {{data.bg_color}} rounded-top">
                    <div class="d-flex gap-1 align-items-center justify-content-end my-n2">
                        <button type="button" class="btn avatar-xs p-0 favourite-btn heart_icon_{{data.id}}" [ngClass]="{'active': data.isIcon !== true}" (click)="activeMenu(data.id)">
                            <span class="avatar-title bg-transparent fs-15">
                                <i class="ri-star-fill"></i>
                            </span>
                        </button>
                        <div class="dropdown" ngbDropdown>
                            <button class="btn btn-link text-muted p-1 mt-n2 py-0 text-decoration-none fs-15 arrow-none" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="true" ngbDropdownToggle>
                                <i-feather name="more-horizontal" class="icon-sm"></i-feather>
                            </button>

                            <div class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                                <a class="dropdown-item" routerLink="/projects/overview"><i class="ri-eye-fill align-bottom me-2 text-muted float-start"></i> View</a>
                                <a class="dropdown-item" routerLink="/projects/create"><i class="ri-pencil-fill align-bottom me-2 text-muted float-start"></i> Edit</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="javascript:void(0);" data-bs-toggle="modal" data-bs-target="#removeProjectModal" (click)="confirm(deleteModel,data.id)"><i class="ri-delete-bin-fill align-bottom me-2 text-muted float-start"></i>
                                    Remove</a>
                            </div>
                        </div>
                    </div>
                    <div class="text-center pb-3">
                        <img src="{{data.img}}" alt="" height="32">
                    </div>
                </div>

                <div class="py-3">
                    <h5 class="fs-14 mb-3"><a routerLink="/projects/overview" class="text-body">{{data.label}}</a></h5>
                    <div class="row gy-3">
                        <div class="col-6">
                            <div>
                                <p class="text-muted mb-1">Status</p>
                                <div class="badge bg-{{data.statusClass}}-subtle text-{{data.statusClass}} fs-12">
                                    {{data.status}}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div>
                                <p class="text-muted mb-1">Deadline</p>
                                <h5 class="fs-14">{{data.deadline}}</h5>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex align-items-center mt-3">
                        <p class="text-muted mb-0 me-2">Team :</p>
                        <div class="avatar-group">
                            @for (user of data.users; track $index) {
                            <div class="avatar-group-item">
                                @if(user.profile){
                                <a href="javascript: void(0);" class="d-block" ngbTooltip="{{user.name}}" placement="top">
                                    <img :src="{{user.profile}}" alt="" class="rounded-circle avatar-xxs">
                                </a>
                                }@else(){
                                <a href="javascript: void(0);" class="d-block" ngbTooltip="{{user.name}}" placement="top">
                                    <div class="avatar-xxs">
                                        <span class="avatar-title rounded-circle {{user.variant}}">
                                            {{user.text}}
                                        </span>
                                    </div>
                                </a>}
                            </div>
                            }
                        </div>
                    </div>
                </div>
                <div>
                    <div class="d-flex mb-2">
                        <div class="flex-grow-1">
                            <div>Tasks</div>
                        </div>
                        <div class="flex-shrink-0">
                            <div><i class="ri-list-check align-bottom me-1 text-muted"></i> {{data.number}}</div>
                        </div>
                    </div>
                    <ngb-progressbar [value]="data.progressBar" type="success" class="progress-sm"></ngb-progressbar>
                </div>

            </div>
            <!-- end card body -->
        </div>
        <!-- end card -->
    </div>
    }
    }
    <!-- end col -->
</div>
<!-- end row -->


<div class="row g-0 text-center text-sm-start align-items-center mb-4">
    <div class="col-sm-6">
        <div>
            <p class="mb-sm-0 text-muted">Showing <span class="fw-semibold">1</span> to <span class="fw-semibold">10</span> of <span class="fw-semibold text-decoration-underline">12</span>
                entries</p>
        </div>
    </div>
    <!-- end col -->
    <div class="col-sm-6">
        <ul class="pagination pagination-separated justify-content-center justify-content-sm-end mb-sm-0">
            <li class="page-item disabled">
                <a href="javascript:void(0);" class="page-link">Previous</a>
            </li>
            <li class="page-item active">
                <a href="javascript:void(0);" class="page-link">1</a>
            </li>
            <li class="page-item ">
                <a href="javascript:void(0);" class="page-link">2</a>
            </li>
            <li class="page-item">
                <a href="javascript:void(0);" class="page-link">3</a>
            </li>
            <li class="page-item">
                <a href="javascript:void(0);" class="page-link">4</a>
            </li>
            <li class="page-item">
                <a href="javascript:void(0);" class="page-link">5</a>
            </li>
            <li class="page-item">
                <a href="javascript:void(0);" class="page-link">Next</a>
            </li>
        </ul>
    </div><!-- end col -->
</div><!-- end row -->


<!-- removeItemModal -->
<ng-template #deleteModel let-modal>
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="btn-close" (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body">
            <div class="mt-2 text-center">
                <lord-icon src="https://cdn.lordicon.com/gsqxdxog.json" trigger="loop" colors="primary:#f7b84b,secondary:#f06548" style="width:100px;height:100px"></lord-icon>
                <div class="mt-4 pt-2 fs-15 mx-4 mx-sm-5">
                    <h4>Are you sure ?</h4>
                    <p class="text-muted mx-4 mb-0">Are you sure you want to remove this record ?</p>
                </div>
            </div>
            <div class="d-flex gap-2 justify-content-center mt-4 mb-2">
                <button type="button" class="btn w-sm btn-light" data-bs-dismiss="modal" (click)="modal.close('Close click')">Close</button>
                <button type="button" class="btn w-sm btn-danger" id="delete-product" (click)="deleteData(deleteId)" (click)="modal.close('Close click')">Yes, Delete It!</button>
            </div>
        </div>
    </div><!-- /.modal-content -->
</ng-template>