{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as visualSolution from '../../visual/visualSolution.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nexport var visualMapEncodingHandlers = [{\n  createOnAllSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var resetDefines = [];\n    ecModel.eachComponent('visualMap', function (visualMapModel) {\n      var pipelineContext = seriesModel.pipelineContext;\n      if (!visualMapModel.isTargetSeries(seriesModel) || pipelineContext && pipelineContext.large) {\n        return;\n      }\n      resetDefines.push(visualSolution.incrementalApplyVisual(visualMapModel.stateList, visualMapModel.targetVisuals, zrUtil.bind(visualMapModel.getValueState, visualMapModel), visualMapModel.getDataDimensionIndex(seriesModel.getData())));\n    });\n    return resetDefines;\n  }\n},\n// Only support color.\n{\n  createOnAllSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    var visualMetaList = [];\n    ecModel.eachComponent('visualMap', function (visualMapModel) {\n      if (visualMapModel.isTargetSeries(seriesModel)) {\n        var visualMeta = visualMapModel.getVisualMeta(zrUtil.bind(getColorVisual, null, seriesModel, visualMapModel)) || {\n          stops: [],\n          outerColors: []\n        };\n        var dimIdx = visualMapModel.getDataDimensionIndex(data);\n        if (dimIdx >= 0) {\n          // visualMeta.dimension should be dimension index, but not concrete dimension.\n          visualMeta.dimension = dimIdx;\n          visualMetaList.push(visualMeta);\n        }\n      }\n    });\n    // console.log(JSON.stringify(visualMetaList.map(a => a.stops)));\n    seriesModel.getData().setVisual('visualMeta', visualMetaList);\n  }\n}];\n// FIXME\n// performance and export for heatmap?\n// value can be Infinity or -Infinity\nfunction getColorVisual(seriesModel, visualMapModel, value, valueState) {\n  var mappings = visualMapModel.targetVisuals[valueState];\n  var visualTypes = VisualMapping.prepareVisualTypes(mappings);\n  var resultVisual = {\n    color: getVisualFromData(seriesModel.getData(), 'color') // default color.\n  };\n  for (var i = 0, len = visualTypes.length; i < len; i++) {\n    var type = visualTypes[i];\n    var mapping = mappings[type === 'opacity' ? '__alphaForOpacity' : type];\n    mapping && mapping.applyVisual(value, getVisual, setVisual);\n  }\n  return resultVisual.color;\n  function getVisual(key) {\n    return resultVisual[key];\n  }\n  function setVisual(key, value) {\n    resultVisual[key] = value;\n  }\n}", "map": {"version": 3, "names": ["zrUtil", "visualSolution", "VisualMapping", "getVisualFromData", "visualMapEncodingHandlers", "createOnAllSeries", "reset", "seriesModel", "ecModel", "resetDefines", "eachComponent", "visualMapModel", "pipelineContext", "isTargetSeries", "large", "push", "incrementalApplyVisual", "stateList", "targetVisuals", "bind", "getValueState", "getDataDimensionIndex", "getData", "data", "visualMetaList", "visualMeta", "getVisualMeta", "getColorVisual", "stops", "outerColors", "dimIdx", "dimension", "setVisual", "value", "valueState", "mappings", "visualTypes", "prepareVisualTypes", "resultVisual", "color", "i", "len", "length", "type", "mapping", "applyVisual", "getVisual", "key"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/component/visualMap/visualEncoding.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as visualSolution from '../../visual/visualSolution.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nexport var visualMapEncodingHandlers = [{\n  createOnAllSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var resetDefines = [];\n    ecModel.eachComponent('visualMap', function (visualMapModel) {\n      var pipelineContext = seriesModel.pipelineContext;\n      if (!visualMapModel.isTargetSeries(seriesModel) || pipelineContext && pipelineContext.large) {\n        return;\n      }\n      resetDefines.push(visualSolution.incrementalApplyVisual(visualMapModel.stateList, visualMapModel.targetVisuals, zrUtil.bind(visualMapModel.getValueState, visualMapModel), visualMapModel.getDataDimensionIndex(seriesModel.getData())));\n    });\n    return resetDefines;\n  }\n},\n// Only support color.\n{\n  createOnAllSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    var visualMetaList = [];\n    ecModel.eachComponent('visualMap', function (visualMapModel) {\n      if (visualMapModel.isTargetSeries(seriesModel)) {\n        var visualMeta = visualMapModel.getVisualMeta(zrUtil.bind(getColorVisual, null, seriesModel, visualMapModel)) || {\n          stops: [],\n          outerColors: []\n        };\n        var dimIdx = visualMapModel.getDataDimensionIndex(data);\n        if (dimIdx >= 0) {\n          // visualMeta.dimension should be dimension index, but not concrete dimension.\n          visualMeta.dimension = dimIdx;\n          visualMetaList.push(visualMeta);\n        }\n      }\n    });\n    // console.log(JSON.stringify(visualMetaList.map(a => a.stops)));\n    seriesModel.getData().setVisual('visualMeta', visualMetaList);\n  }\n}];\n// FIXME\n// performance and export for heatmap?\n// value can be Infinity or -Infinity\nfunction getColorVisual(seriesModel, visualMapModel, value, valueState) {\n  var mappings = visualMapModel.targetVisuals[valueState];\n  var visualTypes = VisualMapping.prepareVisualTypes(mappings);\n  var resultVisual = {\n    color: getVisualFromData(seriesModel.getData(), 'color') // default color.\n  };\n\n  for (var i = 0, len = visualTypes.length; i < len; i++) {\n    var type = visualTypes[i];\n    var mapping = mappings[type === 'opacity' ? '__alphaForOpacity' : type];\n    mapping && mapping.applyVisual(value, getVisual, setVisual);\n  }\n  return resultVisual.color;\n  function getVisual(key) {\n    return resultVisual[key];\n  }\n  function setVisual(key, value) {\n    resultVisual[key] = value;\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,cAAc,MAAM,gCAAgC;AAChE,OAAOC,aAAa,MAAM,+BAA+B;AACzD,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,OAAO,IAAIC,yBAAyB,GAAG,CAAC;EACtCC,iBAAiB,EAAE,IAAI;EACvBC,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAEC,OAAO,EAAE;IACrC,IAAIC,YAAY,GAAG,EAAE;IACrBD,OAAO,CAACE,aAAa,CAAC,WAAW,EAAE,UAAUC,cAAc,EAAE;MAC3D,IAAIC,eAAe,GAAGL,WAAW,CAACK,eAAe;MACjD,IAAI,CAACD,cAAc,CAACE,cAAc,CAACN,WAAW,CAAC,IAAIK,eAAe,IAAIA,eAAe,CAACE,KAAK,EAAE;QAC3F;MACF;MACAL,YAAY,CAACM,IAAI,CAACd,cAAc,CAACe,sBAAsB,CAACL,cAAc,CAACM,SAAS,EAAEN,cAAc,CAACO,aAAa,EAAElB,MAAM,CAACmB,IAAI,CAACR,cAAc,CAACS,aAAa,EAAET,cAAc,CAAC,EAAEA,cAAc,CAACU,qBAAqB,CAACd,WAAW,CAACe,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1O,CAAC,CAAC;IACF,OAAOb,YAAY;EACrB;AACF,CAAC;AACD;AACA;EACEJ,iBAAiB,EAAE,IAAI;EACvBC,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAEC,OAAO,EAAE;IACrC,IAAIe,IAAI,GAAGhB,WAAW,CAACe,OAAO,CAAC,CAAC;IAChC,IAAIE,cAAc,GAAG,EAAE;IACvBhB,OAAO,CAACE,aAAa,CAAC,WAAW,EAAE,UAAUC,cAAc,EAAE;MAC3D,IAAIA,cAAc,CAACE,cAAc,CAACN,WAAW,CAAC,EAAE;QAC9C,IAAIkB,UAAU,GAAGd,cAAc,CAACe,aAAa,CAAC1B,MAAM,CAACmB,IAAI,CAACQ,cAAc,EAAE,IAAI,EAAEpB,WAAW,EAAEI,cAAc,CAAC,CAAC,IAAI;UAC/GiB,KAAK,EAAE,EAAE;UACTC,WAAW,EAAE;QACf,CAAC;QACD,IAAIC,MAAM,GAAGnB,cAAc,CAACU,qBAAqB,CAACE,IAAI,CAAC;QACvD,IAAIO,MAAM,IAAI,CAAC,EAAE;UACf;UACAL,UAAU,CAACM,SAAS,GAAGD,MAAM;UAC7BN,cAAc,CAACT,IAAI,CAACU,UAAU,CAAC;QACjC;MACF;IACF,CAAC,CAAC;IACF;IACAlB,WAAW,CAACe,OAAO,CAAC,CAAC,CAACU,SAAS,CAAC,YAAY,EAAER,cAAc,CAAC;EAC/D;AACF,CAAC,CAAC;AACF;AACA;AACA;AACA,SAASG,cAAcA,CAACpB,WAAW,EAAEI,cAAc,EAAEsB,KAAK,EAAEC,UAAU,EAAE;EACtE,IAAIC,QAAQ,GAAGxB,cAAc,CAACO,aAAa,CAACgB,UAAU,CAAC;EACvD,IAAIE,WAAW,GAAGlC,aAAa,CAACmC,kBAAkB,CAACF,QAAQ,CAAC;EAC5D,IAAIG,YAAY,GAAG;IACjBC,KAAK,EAAEpC,iBAAiB,CAACI,WAAW,CAACe,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EAC3D,CAAC;EAED,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGL,WAAW,CAACM,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IACtD,IAAIG,IAAI,GAAGP,WAAW,CAACI,CAAC,CAAC;IACzB,IAAII,OAAO,GAAGT,QAAQ,CAACQ,IAAI,KAAK,SAAS,GAAG,mBAAmB,GAAGA,IAAI,CAAC;IACvEC,OAAO,IAAIA,OAAO,CAACC,WAAW,CAACZ,KAAK,EAAEa,SAAS,EAAEd,SAAS,CAAC;EAC7D;EACA,OAAOM,YAAY,CAACC,KAAK;EACzB,SAASO,SAASA,CAACC,GAAG,EAAE;IACtB,OAAOT,YAAY,CAACS,GAAG,CAAC;EAC1B;EACA,SAASf,SAASA,CAACe,GAAG,EAAEd,KAAK,EAAE;IAC7BK,YAAY,CAACS,GAAG,CAAC,GAAGd,KAAK;EAC3B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}