{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nvar HeatmapSeriesModel = /** @class */function (_super) {\n  __extends(HeatmapSeriesModel, _super);\n  function HeatmapSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = HeatmapSeriesModel.type;\n    return _this;\n  }\n  HeatmapSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      generateCoord: 'value'\n    });\n  };\n  HeatmapSeriesModel.prototype.preventIncremental = function () {\n    var coordSysCreator = CoordinateSystem.get(this.get('coordinateSystem'));\n    if (coordSysCreator && coordSysCreator.dimensions) {\n      return coordSysCreator.dimensions[0] === 'lng' && coordSysCreator.dimensions[1] === 'lat';\n    }\n  };\n  HeatmapSeriesModel.type = 'series.heatmap';\n  HeatmapSeriesModel.dependencies = ['grid', 'geo', 'calendar'];\n  HeatmapSeriesModel.defaultOption = {\n    coordinateSystem: 'cartesian2d',\n    // zlevel: 0,\n    z: 2,\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    // Geo coordinate system\n    geoIndex: 0,\n    blurSize: 30,\n    pointSize: 20,\n    maxOpacity: 1,\n    minOpacity: 0,\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    }\n  };\n  return HeatmapSeriesModel;\n}(SeriesModel);\nexport default HeatmapSeriesModel;", "map": {"version": 3, "names": ["__extends", "SeriesModel", "createSeriesData", "CoordinateSystem", "HeatmapSeriesModel", "_super", "_this", "apply", "arguments", "type", "prototype", "getInitialData", "option", "ecModel", "generateCoord", "preventIncremental", "coordSysCreator", "get", "dimensions", "dependencies", "defaultOption", "coordinateSystem", "z", "geoIndex", "blurSize", "pointSize", "maxOpacity", "minOpacity", "select", "itemStyle", "borderColor"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/chart/heatmap/HeatmapSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nvar HeatmapSeriesModel = /** @class */function (_super) {\n  __extends(HeatmapSeriesModel, _super);\n  function HeatmapSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = HeatmapSeriesModel.type;\n    return _this;\n  }\n  HeatmapSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      generateCoord: 'value'\n    });\n  };\n  HeatmapSeriesModel.prototype.preventIncremental = function () {\n    var coordSysCreator = CoordinateSystem.get(this.get('coordinateSystem'));\n    if (coordSysCreator && coordSysCreator.dimensions) {\n      return coordSysCreator.dimensions[0] === 'lng' && coordSysCreator.dimensions[1] === 'lat';\n    }\n  };\n  HeatmapSeriesModel.type = 'series.heatmap';\n  HeatmapSeriesModel.dependencies = ['grid', 'geo', 'calendar'];\n  HeatmapSeriesModel.defaultOption = {\n    coordinateSystem: 'cartesian2d',\n    // zlevel: 0,\n    z: 2,\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    // Geo coordinate system\n    geoIndex: 0,\n    blurSize: 30,\n    pointSize: 20,\n    maxOpacity: 1,\n    minOpacity: 0,\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    }\n  };\n  return HeatmapSeriesModel;\n}(SeriesModel);\nexport default HeatmapSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,IAAIC,kBAAkB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACtDL,SAAS,CAACI,kBAAkB,EAAEC,MAAM,CAAC;EACrC,SAASD,kBAAkBA,CAAA,EAAG;IAC5B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,kBAAkB,CAACK,IAAI;IACpC,OAAOH,KAAK;EACd;EACAF,kBAAkB,CAACM,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACvE,OAAOX,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE;MAClCY,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC;EACDV,kBAAkB,CAACM,SAAS,CAACK,kBAAkB,GAAG,YAAY;IAC5D,IAAIC,eAAe,GAAGb,gBAAgB,CAACc,GAAG,CAAC,IAAI,CAACA,GAAG,CAAC,kBAAkB,CAAC,CAAC;IACxE,IAAID,eAAe,IAAIA,eAAe,CAACE,UAAU,EAAE;MACjD,OAAOF,eAAe,CAACE,UAAU,CAAC,CAAC,CAAC,KAAK,KAAK,IAAIF,eAAe,CAACE,UAAU,CAAC,CAAC,CAAC,KAAK,KAAK;IAC3F;EACF,CAAC;EACDd,kBAAkB,CAACK,IAAI,GAAG,gBAAgB;EAC1CL,kBAAkB,CAACe,YAAY,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC;EAC7Df,kBAAkB,CAACgB,aAAa,GAAG;IACjCC,gBAAgB,EAAE,aAAa;IAC/B;IACAC,CAAC,EAAE,CAAC;IACJ;IACA;IACA;IACA;IACAC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE;MACNC,SAAS,EAAE;QACTC,WAAW,EAAE;MACf;IACF;EACF,CAAC;EACD,OAAO1B,kBAAkB;AAC3B,CAAC,CAACH,WAAW,CAAC;AACd,eAAeG,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}