{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Serbian Cyrillic [sr-cyrl]\n//! author : <PERSON><<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com> : https://github.com/milan-j\n//! author : <PERSON> <<EMAIL>> : https://github.com/c<PERSON><PERSON><PERSON><PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var translator = {\n    words: {\n      //Different grammatical cases\n      ss: ['секунда', 'секунде', 'секунди'],\n      m: ['један минут', 'једног минута'],\n      mm: ['минут', 'минута', 'минута'],\n      h: ['један сат', 'једног сата'],\n      hh: ['сат', 'сата', 'сати'],\n      d: ['један дан', 'једног дана'],\n      dd: ['дан', 'дана', 'дана'],\n      M: ['један месец', 'једног месеца'],\n      MM: ['месец', 'месеца', 'месеци'],\n      y: ['једну годину', 'једне године'],\n      yy: ['годину', 'године', 'година']\n    },\n    correctGrammaticalCase: function (number, wordKey) {\n      if (number % 10 >= 1 && number % 10 <= 4 && (number % 100 < 10 || number % 100 >= 20)) {\n        return number % 10 === 1 ? wordKey[0] : wordKey[1];\n      }\n      return wordKey[2];\n    },\n    translate: function (number, withoutSuffix, key, isFuture) {\n      var wordKey = translator.words[key],\n        word;\n      if (key.length === 1) {\n        // Nominativ\n        if (key === 'y' && withoutSuffix) return 'једна година';\n        return isFuture || withoutSuffix ? wordKey[0] : wordKey[1];\n      }\n      word = translator.correctGrammaticalCase(number, wordKey);\n      // Nominativ\n      if (key === 'yy' && withoutSuffix && word === 'годину') {\n        return number + ' година';\n      }\n      return number + ' ' + word;\n    }\n  };\n  var srCyrl = moment.defineLocale('sr-cyrl', {\n    months: 'јануар_фебруар_март_април_мај_јун_јул_август_септембар_октобар_новембар_децембар'.split('_'),\n    monthsShort: 'јан._феб._мар._апр._мај_јун_јул_авг._сеп._окт._нов._дец.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'недеља_понедељак_уторак_среда_четвртак_петак_субота'.split('_'),\n    weekdaysShort: 'нед._пон._уто._сре._чет._пет._суб.'.split('_'),\n    weekdaysMin: 'не_по_ут_ср_че_пе_су'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'D. M. YYYY.',\n      LL: 'D. MMMM YYYY.',\n      LLL: 'D. MMMM YYYY. H:mm',\n      LLLL: 'dddd, D. MMMM YYYY. H:mm'\n    },\n    calendar: {\n      sameDay: '[данас у] LT',\n      nextDay: '[сутра у] LT',\n      nextWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[у] [недељу] [у] LT';\n          case 3:\n            return '[у] [среду] [у] LT';\n          case 6:\n            return '[у] [суботу] [у] LT';\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[у] dddd [у] LT';\n        }\n      },\n      lastDay: '[јуче у] LT',\n      lastWeek: function () {\n        var lastWeekDays = ['[прошле] [недеље] [у] LT', '[прошлог] [понедељка] [у] LT', '[прошлог] [уторка] [у] LT', '[прошле] [среде] [у] LT', '[прошлог] [четвртка] [у] LT', '[прошлог] [петка] [у] LT', '[прошле] [суботе] [у] LT'];\n        return lastWeekDays[this.day()];\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'за %s',\n      past: 'пре %s',\n      s: 'неколико секунди',\n      ss: translator.translate,\n      m: translator.translate,\n      mm: translator.translate,\n      h: translator.translate,\n      hh: translator.translate,\n      d: translator.translate,\n      dd: translator.translate,\n      M: translator.translate,\n      MM: translator.translate,\n      y: translator.translate,\n      yy: translator.translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 1st is the first week of the year.\n    }\n  });\n  return srCyrl;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "translator", "words", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "correctGrammaticalCase", "number", "wordKey", "translate", "withoutSuffix", "key", "isFuture", "word", "length", "srCyrl", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "day", "lastDay", "lastWeek", "lastWeekDays", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/moment/locale/sr-cyrl.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Serbian Cyrillic [sr-cyrl]\n//! author : <PERSON><mi<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com> : https://github.com/milan-j\n//! author : <PERSON> <<EMAIL>> : https://github.com/crn<PERSON><PERSON><PERSON>\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var translator = {\n        words: {\n            //Different grammatical cases\n            ss: ['секунда', 'секунде', 'секунди'],\n            m: ['један минут', 'једног минута'],\n            mm: ['минут', 'минута', 'минута'],\n            h: ['један сат', 'једног сата'],\n            hh: ['сат', 'сата', 'сати'],\n            d: ['један дан', 'једног дана'],\n            dd: ['дан', 'дана', 'дана'],\n            M: ['један месец', 'једног месеца'],\n            MM: ['месец', 'месеца', 'месеци'],\n            y: ['једну годину', 'једне године'],\n            yy: ['годину', 'године', 'година'],\n        },\n        correctGrammaticalCase: function (number, wordKey) {\n            if (\n                number % 10 >= 1 &&\n                number % 10 <= 4 &&\n                (number % 100 < 10 || number % 100 >= 20)\n            ) {\n                return number % 10 === 1 ? wordKey[0] : wordKey[1];\n            }\n            return wordKey[2];\n        },\n        translate: function (number, withoutSuffix, key, isFuture) {\n            var wordKey = translator.words[key],\n                word;\n\n            if (key.length === 1) {\n                // Nominativ\n                if (key === 'y' && withoutSuffix) return 'једна година';\n                return isFuture || withoutSuffix ? wordKey[0] : wordKey[1];\n            }\n\n            word = translator.correctGrammaticalCase(number, wordKey);\n            // Nominativ\n            if (key === 'yy' && withoutSuffix && word === 'годину') {\n                return number + ' година';\n            }\n\n            return number + ' ' + word;\n        },\n    };\n\n    var srCyrl = moment.defineLocale('sr-cyrl', {\n        months: 'јануар_фебруар_март_април_мај_јун_јул_август_септембар_октобар_новембар_децембар'.split(\n            '_'\n        ),\n        monthsShort:\n            'јан._феб._мар._апр._мај_јун_јул_авг._сеп._окт._нов._дец.'.split('_'),\n        monthsParseExact: true,\n        weekdays: 'недеља_понедељак_уторак_среда_четвртак_петак_субота'.split('_'),\n        weekdaysShort: 'нед._пон._уто._сре._чет._пет._суб.'.split('_'),\n        weekdaysMin: 'не_по_ут_ср_че_пе_су'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'D. M. YYYY.',\n            LL: 'D. MMMM YYYY.',\n            LLL: 'D. MMMM YYYY. H:mm',\n            LLLL: 'dddd, D. MMMM YYYY. H:mm',\n        },\n        calendar: {\n            sameDay: '[данас у] LT',\n            nextDay: '[сутра у] LT',\n            nextWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                        return '[у] [недељу] [у] LT';\n                    case 3:\n                        return '[у] [среду] [у] LT';\n                    case 6:\n                        return '[у] [суботу] [у] LT';\n                    case 1:\n                    case 2:\n                    case 4:\n                    case 5:\n                        return '[у] dddd [у] LT';\n                }\n            },\n            lastDay: '[јуче у] LT',\n            lastWeek: function () {\n                var lastWeekDays = [\n                    '[прошле] [недеље] [у] LT',\n                    '[прошлог] [понедељка] [у] LT',\n                    '[прошлог] [уторка] [у] LT',\n                    '[прошле] [среде] [у] LT',\n                    '[прошлог] [четвртка] [у] LT',\n                    '[прошлог] [петка] [у] LT',\n                    '[прошле] [суботе] [у] LT',\n                ];\n                return lastWeekDays[this.day()];\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'за %s',\n            past: 'пре %s',\n            s: 'неколико секунди',\n            ss: translator.translate,\n            m: translator.translate,\n            mm: translator.translate,\n            h: translator.translate,\n            hh: translator.translate,\n            d: translator.translate,\n            dd: translator.translate,\n            M: translator.translate,\n            MM: translator.translate,\n            y: translator.translate,\n            yy: translator.translate,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 1st is the first week of the year.\n        },\n    });\n\n    return srCyrl;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,UAAU,GAAG;IACbC,KAAK,EAAE;MACH;MACAC,EAAE,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACrCC,CAAC,EAAE,CAAC,aAAa,EAAE,eAAe,CAAC;MACnCC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;MACjCC,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;MAC/BC,EAAE,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;MAC3BC,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;MAC/BC,EAAE,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;MAC3BC,CAAC,EAAE,CAAC,aAAa,EAAE,eAAe,CAAC;MACnCC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;MACjCC,CAAC,EAAE,CAAC,cAAc,EAAE,cAAc,CAAC;MACnCC,EAAE,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ;IACrC,CAAC;IACDC,sBAAsB,EAAE,SAAAA,CAAUC,MAAM,EAAEC,OAAO,EAAE;MAC/C,IACID,MAAM,GAAG,EAAE,IAAI,CAAC,IAChBA,MAAM,GAAG,EAAE,IAAI,CAAC,KACfA,MAAM,GAAG,GAAG,GAAG,EAAE,IAAIA,MAAM,GAAG,GAAG,IAAI,EAAE,CAAC,EAC3C;QACE,OAAOA,MAAM,GAAG,EAAE,KAAK,CAAC,GAAGC,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;MACtD;MACA,OAAOA,OAAO,CAAC,CAAC,CAAC;IACrB,CAAC;IACDC,SAAS,EAAE,SAAAA,CAAUF,MAAM,EAAEG,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;MACvD,IAAIJ,OAAO,GAAGf,UAAU,CAACC,KAAK,CAACiB,GAAG,CAAC;QAC/BE,IAAI;MAER,IAAIF,GAAG,CAACG,MAAM,KAAK,CAAC,EAAE;QAClB;QACA,IAAIH,GAAG,KAAK,GAAG,IAAID,aAAa,EAAE,OAAO,cAAc;QACvD,OAAOE,QAAQ,IAAIF,aAAa,GAAGF,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;MAC9D;MAEAK,IAAI,GAAGpB,UAAU,CAACa,sBAAsB,CAACC,MAAM,EAAEC,OAAO,CAAC;MACzD;MACA,IAAIG,GAAG,KAAK,IAAI,IAAID,aAAa,IAAIG,IAAI,KAAK,QAAQ,EAAE;QACpD,OAAON,MAAM,GAAG,SAAS;MAC7B;MAEA,OAAOA,MAAM,GAAG,GAAG,GAAGM,IAAI;IAC9B;EACJ,CAAC;EAED,IAAIE,MAAM,GAAGvB,MAAM,CAACwB,YAAY,CAAC,SAAS,EAAE;IACxCC,MAAM,EAAE,kFAAkF,CAACC,KAAK,CAC5F,GACJ,CAAC;IACDC,WAAW,EACP,0DAA0D,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,qDAAqD,CAACH,KAAK,CAAC,GAAG,CAAC;IAC1EI,aAAa,EAAE,oCAAoC,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9DK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,eAAe;MACnBC,GAAG,EAAE,oBAAoB;MACzBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,cAAc;MACvBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;YACF,OAAO,qBAAqB;UAChC,KAAK,CAAC;YACF,OAAO,oBAAoB;UAC/B,KAAK,CAAC;YACF,OAAO,qBAAqB;UAChC,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,iBAAiB;QAChC;MACJ,CAAC;MACDC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,IAAIC,YAAY,GAAG,CACf,0BAA0B,EAC1B,8BAA8B,EAC9B,2BAA2B,EAC3B,yBAAyB,EACzB,6BAA6B,EAC7B,0BAA0B,EAC1B,0BAA0B,CAC7B;QACD,OAAOA,YAAY,CAAC,IAAI,CAACH,GAAG,CAAC,CAAC,CAAC;MACnC,CAAC;MACDI,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,kBAAkB;MACrBjD,EAAE,EAAEF,UAAU,CAACgB,SAAS;MACxBb,CAAC,EAAEH,UAAU,CAACgB,SAAS;MACvBZ,EAAE,EAAEJ,UAAU,CAACgB,SAAS;MACxBX,CAAC,EAAEL,UAAU,CAACgB,SAAS;MACvBV,EAAE,EAAEN,UAAU,CAACgB,SAAS;MACxBT,CAAC,EAAEP,UAAU,CAACgB,SAAS;MACvBR,EAAE,EAAER,UAAU,CAACgB,SAAS;MACxBP,CAAC,EAAET,UAAU,CAACgB,SAAS;MACvBN,EAAE,EAAEV,UAAU,CAACgB,SAAS;MACxBL,CAAC,EAAEX,UAAU,CAACgB,SAAS;MACvBJ,EAAE,EAAEZ,UAAU,CAACgB;IACnB,CAAC;IACDoC,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOlC,MAAM;AAEjB,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}