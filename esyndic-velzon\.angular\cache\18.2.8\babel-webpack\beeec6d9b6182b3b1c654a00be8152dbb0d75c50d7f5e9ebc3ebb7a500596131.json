{"ast": null, "code": "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * This function is like `composeArgs` except that the arguments composition\n * is tailored for `_.partialRight`.\n *\n * @private\n * @param {Array} args The provided arguments.\n * @param {Array} partials The arguments to append to those provided.\n * @param {Array} holders The `partials` placeholder indexes.\n * @params {boolean} [isCurried] Specify composing for a curried function.\n * @returns {Array} Returns the new array of composed arguments.\n */\nfunction composeArgsRight(args, partials, holders, isCurried) {\n  var argsIndex = -1,\n    argsLength = args.length,\n    holdersIndex = -1,\n    holdersLength = holders.length,\n    rightIndex = -1,\n    rightLength = partials.length,\n    rangeLength = nativeMax(argsLength - holdersLength, 0),\n    result = Array(rangeLength + rightLength),\n    isUncurried = !isCurried;\n  while (++argsIndex < rangeLength) {\n    result[argsIndex] = args[argsIndex];\n  }\n  var offset = argsIndex;\n  while (++rightIndex < rightLength) {\n    result[offset + rightIndex] = partials[rightIndex];\n  }\n  while (++holdersIndex < holdersLength) {\n    if (isUncurried || argsIndex < argsLength) {\n      result[offset + holders[holdersIndex]] = args[argsIndex++];\n    }\n  }\n  return result;\n}\nexport default composeArgsRight;", "map": {"version": 3, "names": ["nativeMax", "Math", "max", "composeArgsRight", "args", "partials", "holders", "is<PERSON><PERSON><PERSON>", "argsIndex", "arg<PERSON><PERSON><PERSON><PERSON>", "length", "holdersIndex", "holders<PERSON><PERSON><PERSON>", "rightIndex", "<PERSON><PERSON><PERSON><PERSON>", "rangeLength", "result", "Array", "isUncurried", "offset"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_composeArgsRight.js"], "sourcesContent": ["/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * This function is like `composeArgs` except that the arguments composition\n * is tailored for `_.partialRight`.\n *\n * @private\n * @param {Array} args The provided arguments.\n * @param {Array} partials The arguments to append to those provided.\n * @param {Array} holders The `partials` placeholder indexes.\n * @params {boolean} [isCurried] Specify composing for a curried function.\n * @returns {Array} Returns the new array of composed arguments.\n */\nfunction composeArgsRight(args, partials, holders, isCurried) {\n  var argsIndex = -1,\n      argsLength = args.length,\n      holdersIndex = -1,\n      holdersLength = holders.length,\n      rightIndex = -1,\n      rightLength = partials.length,\n      rangeLength = nativeMax(argsLength - holdersLength, 0),\n      result = Array(rangeLength + rightLength),\n      isUncurried = !isCurried;\n\n  while (++argsIndex < rangeLength) {\n    result[argsIndex] = args[argsIndex];\n  }\n  var offset = argsIndex;\n  while (++rightIndex < rightLength) {\n    result[offset + rightIndex] = partials[rightIndex];\n  }\n  while (++holdersIndex < holdersLength) {\n    if (isUncurried || argsIndex < argsLength) {\n      result[offset + holders[holdersIndex]] = args[argsIndex++];\n    }\n  }\n  return result;\n}\n\nexport default composeArgsRight;\n"], "mappings": "AAAA;AACA,IAAIA,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAE;EAC5D,IAAIC,SAAS,GAAG,CAAC,CAAC;IACdC,UAAU,GAAGL,IAAI,CAACM,MAAM;IACxBC,YAAY,GAAG,CAAC,CAAC;IACjBC,aAAa,GAAGN,OAAO,CAACI,MAAM;IAC9BG,UAAU,GAAG,CAAC,CAAC;IACfC,WAAW,GAAGT,QAAQ,CAACK,MAAM;IAC7BK,WAAW,GAAGf,SAAS,CAACS,UAAU,GAAGG,aAAa,EAAE,CAAC,CAAC;IACtDI,MAAM,GAAGC,KAAK,CAACF,WAAW,GAAGD,WAAW,CAAC;IACzCI,WAAW,GAAG,CAACX,SAAS;EAE5B,OAAO,EAAEC,SAAS,GAAGO,WAAW,EAAE;IAChCC,MAAM,CAACR,SAAS,CAAC,GAAGJ,IAAI,CAACI,SAAS,CAAC;EACrC;EACA,IAAIW,MAAM,GAAGX,SAAS;EACtB,OAAO,EAAEK,UAAU,GAAGC,WAAW,EAAE;IACjCE,MAAM,CAACG,MAAM,GAAGN,UAAU,CAAC,GAAGR,QAAQ,CAACQ,UAAU,CAAC;EACpD;EACA,OAAO,EAAEF,YAAY,GAAGC,aAAa,EAAE;IACrC,IAAIM,WAAW,IAAIV,SAAS,GAAGC,UAAU,EAAE;MACzCO,MAAM,CAACG,MAAM,GAAGb,OAAO,CAACK,YAAY,CAAC,CAAC,GAAGP,IAAI,CAACI,SAAS,EAAE,CAAC;IAC5D;EACF;EACA,OAAOQ,MAAM;AACf;AAEA,eAAeb,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}