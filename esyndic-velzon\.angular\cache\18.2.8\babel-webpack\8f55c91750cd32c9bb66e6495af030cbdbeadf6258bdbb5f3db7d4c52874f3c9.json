{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { installTreemapAction } from './treemapAction.js';\nimport TreemapSeriesModel from './TreemapSeries.js';\nimport TreemapView from './TreemapView.js';\nimport treemapVisual from './treemapVisual.js';\nimport treemapLayout from './treemapLayout.js';\nexport function install(registers) {\n  registers.registerSeriesModel(TreemapSeriesModel);\n  registers.registerChartView(TreemapView);\n  registers.registerVisual(treemapVisual);\n  registers.registerLayout(treemapLayout);\n  installTreemapAction(registers);\n}", "map": {"version": 3, "names": ["installTreemapAction", "TreemapSeriesModel", "TreemapView", "treemapVisual", "treemapLayout", "install", "registers", "registerSeriesModel", "registerChartView", "registerVisual", "registerLayout"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/chart/treemap/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { installTreemapAction } from './treemapAction.js';\nimport TreemapSeriesModel from './TreemapSeries.js';\nimport TreemapView from './TreemapView.js';\nimport treemapVisual from './treemapVisual.js';\nimport treemapLayout from './treemapLayout.js';\nexport function install(registers) {\n  registers.registerSeriesModel(TreemapSeriesModel);\n  registers.registerChartView(TreemapView);\n  registers.registerVisual(treemapVisual);\n  registers.registerLayout(treemapLayout);\n  installTreemapAction(registers);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,oBAAoB,QAAQ,oBAAoB;AACzD,OAAOC,kBAAkB,MAAM,oBAAoB;AACnD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,mBAAmB,CAACN,kBAAkB,CAAC;EACjDK,SAAS,CAACE,iBAAiB,CAACN,WAAW,CAAC;EACxCI,SAAS,CAACG,cAAc,CAACN,aAAa,CAAC;EACvCG,SAAS,CAACI,cAAc,CAACN,aAAa,CAAC;EACvCJ,oBAAoB,CAACM,SAAS,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}