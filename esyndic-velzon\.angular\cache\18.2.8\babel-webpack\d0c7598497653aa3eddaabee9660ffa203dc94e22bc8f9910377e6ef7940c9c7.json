{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, each } from 'zrender/lib/core/util.js';\nimport { getAxisMainType } from './helper.js';\nimport AxisProxy from './AxisProxy.js';\nvar dataZoomProcessor = {\n  // `dataZoomProcessor` will only be performed in needed series. Consider if\n  // there is a line series and a pie series, it is better not to update the\n  // line series if only pie series is needed to be updated.\n  getTargetSeries: function (ecModel) {\n    function eachAxisModel(cb) {\n      ecModel.eachComponent('dataZoom', function (dataZoomModel) {\n        dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n          var axisModel = ecModel.getComponent(getAxisMainType(axisDim), axisIndex);\n          cb(axisDim, axisIndex, axisModel, dataZoomModel);\n        });\n      });\n    }\n    // FIXME: it brings side-effect to `getTargetSeries`.\n    // Prepare axis proxies.\n    eachAxisModel(function (axisDim, axisIndex, axisModel, dataZoomModel) {\n      // dispose all last axis proxy, in case that some axis are deleted.\n      axisModel.__dzAxisProxy = null;\n    });\n    var proxyList = [];\n    eachAxisModel(function (axisDim, axisIndex, axisModel, dataZoomModel) {\n      // Different dataZooms may constrol the same axis. In that case,\n      // an axisProxy serves both of them.\n      if (!axisModel.__dzAxisProxy) {\n        // Use the first dataZoomModel as the main model of axisProxy.\n        axisModel.__dzAxisProxy = new AxisProxy(axisDim, axisIndex, dataZoomModel, ecModel);\n        proxyList.push(axisModel.__dzAxisProxy);\n      }\n    });\n    var seriesModelMap = createHashMap();\n    each(proxyList, function (axisProxy) {\n      each(axisProxy.getTargetSeriesModels(), function (seriesModel) {\n        seriesModelMap.set(seriesModel.uid, seriesModel);\n      });\n    });\n    return seriesModelMap;\n  },\n  // Consider appendData, where filter should be performed. Because data process is\n  // in block mode currently, it is not need to worry about that the overallProgress\n  // execute every frame.\n  overallReset: function (ecModel, api) {\n    ecModel.eachComponent('dataZoom', function (dataZoomModel) {\n      // We calculate window and reset axis here but not in model\n      // init stage and not after action dispatch handler, because\n      // reset should be called after seriesData.restoreData.\n      dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n        dataZoomModel.getAxisProxy(axisDim, axisIndex).reset(dataZoomModel);\n      });\n      // Caution: data zoom filtering is order sensitive when using\n      // percent range and no min/max/scale set on axis.\n      // For example, we have dataZoom definition:\n      // [\n      //      {xAxisIndex: 0, start: 30, end: 70},\n      //      {yAxisIndex: 0, start: 20, end: 80}\n      // ]\n      // In this case, [20, 80] of y-dataZoom should be based on data\n      // that have filtered by x-dataZoom using range of [30, 70],\n      // but should not be based on full raw data. Thus sliding\n      // x-dataZoom will change both ranges of xAxis and yAxis,\n      // while sliding y-dataZoom will only change the range of yAxis.\n      // So we should filter x-axis after reset x-axis immediately,\n      // and then reset y-axis and filter y-axis.\n      dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n        dataZoomModel.getAxisProxy(axisDim, axisIndex).filterData(dataZoomModel, api);\n      });\n    });\n    ecModel.eachComponent('dataZoom', function (dataZoomModel) {\n      // Fullfill all of the range props so that user\n      // is able to get them from chart.getOption().\n      var axisProxy = dataZoomModel.findRepresentativeAxisProxy();\n      if (axisProxy) {\n        var percentRange = axisProxy.getDataPercentWindow();\n        var valueRange = axisProxy.getDataValueWindow();\n        dataZoomModel.setCalculatedRange({\n          start: percentRange[0],\n          end: percentRange[1],\n          startValue: valueRange[0],\n          endValue: valueRange[1]\n        });\n      }\n    });\n  }\n};\nexport default dataZoomProcessor;", "map": {"version": 3, "names": ["createHashMap", "each", "getAxisMainType", "AxisProxy", "dataZoomProcessor", "getTargetSeries", "ecModel", "eachAxisModel", "cb", "eachComponent", "dataZoomModel", "eachTargetAxis", "axisDim", "axisIndex", "axisModel", "getComponent", "__dzAxisProxy", "proxyList", "push", "seriesModelMap", "axisProxy", "getTargetSeriesModels", "seriesModel", "set", "uid", "<PERSON><PERSON><PERSON><PERSON>", "api", "getAxisProxy", "reset", "filterData", "findRepresentativeAxisProxy", "percentRange", "getDataPercentWindow", "valueRange", "getDataValueWindow", "setCalculatedRange", "start", "end", "startValue", "endValue"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/component/dataZoom/dataZoomProcessor.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, each } from 'zrender/lib/core/util.js';\nimport { getAxisMainType } from './helper.js';\nimport AxisProxy from './AxisProxy.js';\nvar dataZoomProcessor = {\n  // `dataZoomProcessor` will only be performed in needed series. Consider if\n  // there is a line series and a pie series, it is better not to update the\n  // line series if only pie series is needed to be updated.\n  getTargetSeries: function (ecModel) {\n    function eachAxisModel(cb) {\n      ecModel.eachComponent('dataZoom', function (dataZoomModel) {\n        dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n          var axisModel = ecModel.getComponent(getAxisMainType(axisDim), axisIndex);\n          cb(axisDim, axisIndex, axisModel, dataZoomModel);\n        });\n      });\n    }\n    // FIXME: it brings side-effect to `getTargetSeries`.\n    // Prepare axis proxies.\n    eachAxisModel(function (axisDim, axisIndex, axisModel, dataZoomModel) {\n      // dispose all last axis proxy, in case that some axis are deleted.\n      axisModel.__dzAxisProxy = null;\n    });\n    var proxyList = [];\n    eachAxisModel(function (axisDim, axisIndex, axisModel, dataZoomModel) {\n      // Different dataZooms may constrol the same axis. In that case,\n      // an axisProxy serves both of them.\n      if (!axisModel.__dzAxisProxy) {\n        // Use the first dataZoomModel as the main model of axisProxy.\n        axisModel.__dzAxisProxy = new AxisProxy(axisDim, axisIndex, dataZoomModel, ecModel);\n        proxyList.push(axisModel.__dzAxisProxy);\n      }\n    });\n    var seriesModelMap = createHashMap();\n    each(proxyList, function (axisProxy) {\n      each(axisProxy.getTargetSeriesModels(), function (seriesModel) {\n        seriesModelMap.set(seriesModel.uid, seriesModel);\n      });\n    });\n    return seriesModelMap;\n  },\n  // Consider appendData, where filter should be performed. Because data process is\n  // in block mode currently, it is not need to worry about that the overallProgress\n  // execute every frame.\n  overallReset: function (ecModel, api) {\n    ecModel.eachComponent('dataZoom', function (dataZoomModel) {\n      // We calculate window and reset axis here but not in model\n      // init stage and not after action dispatch handler, because\n      // reset should be called after seriesData.restoreData.\n      dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n        dataZoomModel.getAxisProxy(axisDim, axisIndex).reset(dataZoomModel);\n      });\n      // Caution: data zoom filtering is order sensitive when using\n      // percent range and no min/max/scale set on axis.\n      // For example, we have dataZoom definition:\n      // [\n      //      {xAxisIndex: 0, start: 30, end: 70},\n      //      {yAxisIndex: 0, start: 20, end: 80}\n      // ]\n      // In this case, [20, 80] of y-dataZoom should be based on data\n      // that have filtered by x-dataZoom using range of [30, 70],\n      // but should not be based on full raw data. Thus sliding\n      // x-dataZoom will change both ranges of xAxis and yAxis,\n      // while sliding y-dataZoom will only change the range of yAxis.\n      // So we should filter x-axis after reset x-axis immediately,\n      // and then reset y-axis and filter y-axis.\n      dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n        dataZoomModel.getAxisProxy(axisDim, axisIndex).filterData(dataZoomModel, api);\n      });\n    });\n    ecModel.eachComponent('dataZoom', function (dataZoomModel) {\n      // Fullfill all of the range props so that user\n      // is able to get them from chart.getOption().\n      var axisProxy = dataZoomModel.findRepresentativeAxisProxy();\n      if (axisProxy) {\n        var percentRange = axisProxy.getDataPercentWindow();\n        var valueRange = axisProxy.getDataValueWindow();\n        dataZoomModel.setCalculatedRange({\n          start: percentRange[0],\n          end: percentRange[1],\n          startValue: valueRange[0],\n          endValue: valueRange[1]\n        });\n      }\n    });\n  }\n};\nexport default dataZoomProcessor;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,IAAI,QAAQ,0BAA0B;AAC9D,SAASC,eAAe,QAAQ,aAAa;AAC7C,OAAOC,SAAS,MAAM,gBAAgB;AACtC,IAAIC,iBAAiB,GAAG;EACtB;EACA;EACA;EACAC,eAAe,EAAE,SAAAA,CAAUC,OAAO,EAAE;IAClC,SAASC,aAAaA,CAACC,EAAE,EAAE;MACzBF,OAAO,CAACG,aAAa,CAAC,UAAU,EAAE,UAAUC,aAAa,EAAE;QACzDA,aAAa,CAACC,cAAc,CAAC,UAAUC,OAAO,EAAEC,SAAS,EAAE;UACzD,IAAIC,SAAS,GAAGR,OAAO,CAACS,YAAY,CAACb,eAAe,CAACU,OAAO,CAAC,EAAEC,SAAS,CAAC;UACzEL,EAAE,CAACI,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEJ,aAAa,CAAC;QAClD,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA;IACA;IACAH,aAAa,CAAC,UAAUK,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEJ,aAAa,EAAE;MACpE;MACAI,SAAS,CAACE,aAAa,GAAG,IAAI;IAChC,CAAC,CAAC;IACF,IAAIC,SAAS,GAAG,EAAE;IAClBV,aAAa,CAAC,UAAUK,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEJ,aAAa,EAAE;MACpE;MACA;MACA,IAAI,CAACI,SAAS,CAACE,aAAa,EAAE;QAC5B;QACAF,SAAS,CAACE,aAAa,GAAG,IAAIb,SAAS,CAACS,OAAO,EAAEC,SAAS,EAAEH,aAAa,EAAEJ,OAAO,CAAC;QACnFW,SAAS,CAACC,IAAI,CAACJ,SAAS,CAACE,aAAa,CAAC;MACzC;IACF,CAAC,CAAC;IACF,IAAIG,cAAc,GAAGnB,aAAa,CAAC,CAAC;IACpCC,IAAI,CAACgB,SAAS,EAAE,UAAUG,SAAS,EAAE;MACnCnB,IAAI,CAACmB,SAAS,CAACC,qBAAqB,CAAC,CAAC,EAAE,UAAUC,WAAW,EAAE;QAC7DH,cAAc,CAACI,GAAG,CAACD,WAAW,CAACE,GAAG,EAAEF,WAAW,CAAC;MAClD,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOH,cAAc;EACvB,CAAC;EACD;EACA;EACA;EACAM,YAAY,EAAE,SAAAA,CAAUnB,OAAO,EAAEoB,GAAG,EAAE;IACpCpB,OAAO,CAACG,aAAa,CAAC,UAAU,EAAE,UAAUC,aAAa,EAAE;MACzD;MACA;MACA;MACAA,aAAa,CAACC,cAAc,CAAC,UAAUC,OAAO,EAAEC,SAAS,EAAE;QACzDH,aAAa,CAACiB,YAAY,CAACf,OAAO,EAAEC,SAAS,CAAC,CAACe,KAAK,CAAClB,aAAa,CAAC;MACrE,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAA,aAAa,CAACC,cAAc,CAAC,UAAUC,OAAO,EAAEC,SAAS,EAAE;QACzDH,aAAa,CAACiB,YAAY,CAACf,OAAO,EAAEC,SAAS,CAAC,CAACgB,UAAU,CAACnB,aAAa,EAAEgB,GAAG,CAAC;MAC/E,CAAC,CAAC;IACJ,CAAC,CAAC;IACFpB,OAAO,CAACG,aAAa,CAAC,UAAU,EAAE,UAAUC,aAAa,EAAE;MACzD;MACA;MACA,IAAIU,SAAS,GAAGV,aAAa,CAACoB,2BAA2B,CAAC,CAAC;MAC3D,IAAIV,SAAS,EAAE;QACb,IAAIW,YAAY,GAAGX,SAAS,CAACY,oBAAoB,CAAC,CAAC;QACnD,IAAIC,UAAU,GAAGb,SAAS,CAACc,kBAAkB,CAAC,CAAC;QAC/CxB,aAAa,CAACyB,kBAAkB,CAAC;UAC/BC,KAAK,EAAEL,YAAY,CAAC,CAAC,CAAC;UACtBM,GAAG,EAAEN,YAAY,CAAC,CAAC,CAAC;UACpBO,UAAU,EAAEL,UAAU,CAAC,CAAC,CAAC;UACzBM,QAAQ,EAAEN,UAAU,CAAC,CAAC;QACxB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AACD,eAAe7B,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}