{"ast": null, "code": "import baseSlice from './_baseSlice.js';\n\n/**\n * The base implementation of methods like `_.dropWhile` and `_.takeWhile`\n * without support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to query.\n * @param {Function} predicate The function invoked per iteration.\n * @param {boolean} [isDrop] Specify dropping elements instead of taking them.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Array} Returns the slice of `array`.\n */\nfunction baseWhile(array, predicate, isDrop, fromRight) {\n  var length = array.length,\n    index = fromRight ? length : -1;\n  while ((fromRight ? index-- : ++index < length) && predicate(array[index], index, array)) {}\n  return isDrop ? baseSlice(array, fromRight ? 0 : index, fromRight ? index + 1 : length) : baseSlice(array, fromRight ? index + 1 : 0, fromRight ? length : index);\n}\nexport default baseWhile;", "map": {"version": 3, "names": ["baseSlice", "<PERSON><PERSON><PERSON><PERSON>", "array", "predicate", "isDrop", "fromRight", "length", "index"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_baseWhile.js"], "sourcesContent": ["import baseSlice from './_baseSlice.js';\n\n/**\n * The base implementation of methods like `_.dropWhile` and `_.takeWhile`\n * without support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to query.\n * @param {Function} predicate The function invoked per iteration.\n * @param {boolean} [isDrop] Specify dropping elements instead of taking them.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Array} Returns the slice of `array`.\n */\nfunction baseWhile(array, predicate, isDrop, fromRight) {\n  var length = array.length,\n      index = fromRight ? length : -1;\n\n  while ((fromRight ? index-- : ++index < length) &&\n    predicate(array[index], index, array)) {}\n\n  return isDrop\n    ? baseSlice(array, (fromRight ? 0 : index), (fromRight ? index + 1 : length))\n    : baseSlice(array, (fromRight ? index + 1 : 0), (fromRight ? length : index));\n}\n\nexport default baseWhile;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAE;EACtD,IAAIC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,KAAK,GAAGF,SAAS,GAAGC,MAAM,GAAG,CAAC,CAAC;EAEnC,OAAO,CAACD,SAAS,GAAGE,KAAK,EAAE,GAAG,EAAEA,KAAK,GAAGD,MAAM,KAC5CH,SAAS,CAACD,KAAK,CAACK,KAAK,CAAC,EAAEA,KAAK,EAAEL,KAAK,CAAC,EAAE,CAAC;EAE1C,OAAOE,MAAM,GACTJ,SAAS,CAACE,KAAK,EAAGG,SAAS,GAAG,CAAC,GAAGE,KAAK,EAAIF,SAAS,GAAGE,KAAK,GAAG,CAAC,GAAGD,MAAO,CAAC,GAC3EN,SAAS,CAACE,KAAK,EAAGG,SAAS,GAAGE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAIF,SAAS,GAAGC,MAAM,GAAGC,KAAM,CAAC;AACjF;AAEA,eAAeN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}