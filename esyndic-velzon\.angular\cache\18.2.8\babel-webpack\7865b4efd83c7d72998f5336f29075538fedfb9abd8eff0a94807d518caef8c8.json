{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { visualMapActionInfo, visualMapActionHander } from './visualMapAction.js';\nimport { visualMapEncodingHandlers } from './visualEncoding.js';\nimport { each } from 'zrender/lib/core/util.js';\nimport preprocessor from './preprocessor.js';\nvar installed = false;\nexport default function installCommon(registers) {\n  if (installed) {\n    return;\n  }\n  installed = true;\n  registers.registerSubTypeDefaulter('visualMap', function (option) {\n    // Compatible with ec2, when splitNumber === 0, continuous visualMap will be used.\n    return !option.categories && (!(option.pieces ? option.pieces.length > 0 : option.splitNumber > 0) || option.calculable) ? 'continuous' : 'piecewise';\n  });\n  registers.registerAction(visualMapActionInfo, visualMapActionHander);\n  each(visualMapEncodingHandlers, function (handler) {\n    registers.registerVisual(registers.PRIORITY.VISUAL.COMPONENT, handler);\n  });\n  registers.registerPreprocessor(preprocessor);\n}", "map": {"version": 3, "names": ["visualMapActionInfo", "visualMapActionHander", "visualMapEncodingHandlers", "each", "preprocessor", "installed", "installCommon", "registers", "registerSubTypeDefaulter", "option", "categories", "pieces", "length", "splitNumber", "calculable", "registerAction", "handler", "registerVisual", "PRIORITY", "VISUAL", "COMPONENT", "registerPreprocessor"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/component/visualMap/installCommon.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { visualMapActionInfo, visualMapActionHander } from './visualMapAction.js';\nimport { visualMapEncodingHandlers } from './visualEncoding.js';\nimport { each } from 'zrender/lib/core/util.js';\nimport preprocessor from './preprocessor.js';\nvar installed = false;\nexport default function installCommon(registers) {\n  if (installed) {\n    return;\n  }\n  installed = true;\n  registers.registerSubTypeDefaulter('visualMap', function (option) {\n    // Compatible with ec2, when splitNumber === 0, continuous visualMap will be used.\n    return !option.categories && (!(option.pieces ? option.pieces.length > 0 : option.splitNumber > 0) || option.calculable) ? 'continuous' : 'piecewise';\n  });\n  registers.registerAction(visualMapActionInfo, visualMapActionHander);\n  each(visualMapEncodingHandlers, function (handler) {\n    registers.registerVisual(registers.PRIORITY.VISUAL.COMPONENT, handler);\n  });\n  registers.registerPreprocessor(preprocessor);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,EAAEC,qBAAqB,QAAQ,sBAAsB;AACjF,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,IAAIC,SAAS,GAAG,KAAK;AACrB,eAAe,SAASC,aAAaA,CAACC,SAAS,EAAE;EAC/C,IAAIF,SAAS,EAAE;IACb;EACF;EACAA,SAAS,GAAG,IAAI;EAChBE,SAAS,CAACC,wBAAwB,CAAC,WAAW,EAAE,UAAUC,MAAM,EAAE;IAChE;IACA,OAAO,CAACA,MAAM,CAACC,UAAU,KAAK,EAAED,MAAM,CAACE,MAAM,GAAGF,MAAM,CAACE,MAAM,CAACC,MAAM,GAAG,CAAC,GAAGH,MAAM,CAACI,WAAW,GAAG,CAAC,CAAC,IAAIJ,MAAM,CAACK,UAAU,CAAC,GAAG,YAAY,GAAG,WAAW;EACvJ,CAAC,CAAC;EACFP,SAAS,CAACQ,cAAc,CAACf,mBAAmB,EAAEC,qBAAqB,CAAC;EACpEE,IAAI,CAACD,yBAAyB,EAAE,UAAUc,OAAO,EAAE;IACjDT,SAAS,CAACU,cAAc,CAACV,SAAS,CAACW,QAAQ,CAACC,MAAM,CAACC,SAAS,EAAEJ,OAAO,CAAC;EACxE,CAAC,CAAC;EACFT,SAAS,CAACc,oBAAoB,CAACjB,YAAY,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}