{"version": 3, "file": "legacy-dialog.mjs", "sources": ["../../../../../../src/material/legacy-dialog/dialog-animations.ts", "../../../../../../src/material/legacy-dialog/dialog-config.ts", "../../../../../../src/material/legacy-dialog/dialog-container.ts", "../../../../../../src/material/legacy-dialog/dialog-container.html", "../../../../../../src/material/legacy-dialog/dialog-ref.ts", "../../../../../../src/material/legacy-dialog/dialog.ts", "../../../../../../src/material/legacy-dialog/dialog-content-directives.ts", "../../../../../../src/material/legacy-dialog/dialog-module.ts", "../../../../../../src/material/legacy-dialog/legacy-dialog_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Default parameters for the animation for backwards compatibility.\n * @docs-private\n * @deprecated Use `defaultParams` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport const defaultParams = {\n  params: {enterAnimationDuration: '150ms', exitAnimationDuration: '75ms'},\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {MatDialogConfig as DialogConfigBase, _defaultParams} from '@angular/material/dialog';\n\n/**\n * @deprecated Use `MatDialogConfig` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport class MatLegacyDialogConfig<D = any> extends DialogConfigBase<D> {\n  /** Duration of the enter animation. Has to be a valid CSS value (e.g. 100ms). */\n  override enterAnimationDuration?: string = _defaultParams.params.enterAnimationDuration;\n\n  /** Duration of the exit animation. Has to be a valid CSS value (e.g. 50ms). */\n  override exitAnimationDuration?: string = _defaultParams.params.exitAnimationDuration;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationEvent} from '@angular/animations';\nimport {FocusMonitor, FocusTrapFactory, InteractivityChecker} from '@angular/cdk/a11y';\nimport {OverlayRef} from '@angular/cdk/overlay';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  Inject,\n  NgZone,\n  Optional,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {defaultParams} from './dialog-animations';\nimport {MatLegacyDialogConfig} from './dialog-config';\nimport {_MatDialogContainerBase, matDialogAnimations} from '@angular/material/dialog';\n\n/**\n * Internal component that wraps user-provided dialog content.\n * Animation is based on https://material.io/guidelines/motion/choreography.html.\n * @docs-private\n * @deprecated Use `MatDialogContainer` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Component({\n  selector: 'mat-dialog-container',\n  templateUrl: 'dialog-container.html',\n  styleUrls: ['dialog.css'],\n  encapsulation: ViewEncapsulation.None,\n  // Using OnPush for dialogs caused some G3 sync issues. Disabled until we can track them down.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  animations: [matDialogAnimations.dialogContainer],\n  host: {\n    'class': 'mat-dialog-container',\n    'tabindex': '-1',\n    '[attr.aria-modal]': '_config.ariaModal',\n    '[id]': '_config.id',\n    '[attr.role]': '_config.role',\n    '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n    '[attr.aria-label]': '_config.ariaLabel',\n    '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n    '[@dialogContainer]': `_getAnimationState()`,\n    '(@dialogContainer.start)': '_onAnimationStart($event)',\n    '(@dialogContainer.done)': '_onAnimationDone($event)',\n  },\n})\nexport class MatLegacyDialogContainer extends _MatDialogContainerBase {\n  /** State of the dialog animation. */\n  _state: 'void' | 'enter' | 'exit' = 'enter';\n\n  /** Callback, invoked whenever an animation on the host completes. */\n  _onAnimationDone({toState, totalTime}: AnimationEvent) {\n    if (toState === 'enter') {\n      this._openAnimationDone(totalTime);\n    } else if (toState === 'exit') {\n      this._animationStateChanged.next({state: 'closed', totalTime});\n    }\n  }\n\n  /** Callback, invoked when an animation on the host starts. */\n  _onAnimationStart({toState, totalTime}: AnimationEvent) {\n    if (toState === 'enter') {\n      this._animationStateChanged.next({state: 'opening', totalTime});\n    } else if (toState === 'exit' || toState === 'void') {\n      this._animationStateChanged.next({state: 'closing', totalTime});\n    }\n  }\n\n  /** Starts the dialog exit animation. */\n  _startExitAnimation(): void {\n    this._state = 'exit';\n\n    // Mark the container for check so it can react if the\n    // view container is using OnPush change detection.\n    this._changeDetectorRef.markForCheck();\n  }\n\n  constructor(\n    elementRef: ElementRef,\n    focusTrapFactory: FocusTrapFactory,\n    @Optional() @Inject(DOCUMENT) document: any,\n    dialogConfig: MatLegacyDialogConfig,\n    checker: InteractivityChecker,\n    ngZone: NgZone,\n    overlayRef: OverlayRef,\n    private _changeDetectorRef: ChangeDetectorRef,\n    focusMonitor?: FocusMonitor,\n  ) {\n    super(\n      elementRef,\n      focusTrapFactory,\n      document,\n      dialogConfig,\n      checker,\n      ngZone,\n      overlayRef,\n      focusMonitor,\n    );\n  }\n\n  _getAnimationState() {\n    return {\n      value: this._state,\n      params: {\n        'enterAnimationDuration':\n          this._config.enterAnimationDuration || defaultParams.params.enterAnimationDuration,\n        'exitAnimationDuration':\n          this._config.exitAnimationDuration || defaultParams.params.exitAnimationDuration,\n      },\n    };\n  }\n}\n", "<ng-template cdkPortalOutlet></ng-template>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {MatDialogRef as NewDialogRef} from '@angular/material/dialog';\n\n/**\n * Reference to a dialog opened via the MatDialog service.\n * @deprecated Use `MatDialogRef` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport class MatLegacyDialogRef<T, R = any> extends NewDialogRef<T, R> {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Overlay, OverlayContainer, ScrollStrategy} from '@angular/cdk/overlay';\nimport {Location} from '@angular/common';\nimport {Inject, Injectable, InjectionToken, Injector, Optional, SkipSelf} from '@angular/core';\nimport {MatLegacyDialogContainer} from './dialog-container';\nimport {ANIMATION_MODULE_TYPE} from '@angular/platform-browser/animations';\nimport {_MatDialogBase} from '@angular/material/dialog';\nimport {MatLegacyDialogRef} from './dialog-ref';\nimport {MatLegacyDialogConfig} from './dialog-config';\n\n/**\n * Injection token that can be used to access the data that was passed in to a dialog.\n * @deprecated Use `MAT_DIALOG_DATA` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport const MAT_LEGACY_DIALOG_DATA = new InjectionToken<any>('MatDialogData');\n\n/**\n * Injection token that can be used to specify default dialog options.\n * @deprecated Use `MAT_DIALOG_DEFAULT_OPTIONS` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport const MAT_LEGACY_DIALOG_DEFAULT_OPTIONS = new InjectionToken<MatLegacyDialogConfig>(\n  'mat-dialog-default-options',\n);\n\n/**\n * Injection token that determines the scroll handling while the dialog is open.\n * @deprecated Use `MAT_DIALOG_SCROLL_STRATEGY` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport const MAT_LEGACY_DIALOG_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'mat-dialog-scroll-strategy',\n);\n\n/**\n * @docs-private\n * @deprecated Use `MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport function MAT_LEGACY_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(\n  overlay: Overlay,\n): () => ScrollStrategy {\n  return () => overlay.scrollStrategies.block();\n}\n\n/**\n * @docs-private\n * @deprecated Use `MAT_DIALOG_SCROLL_STRATEGY_PROVIDER` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport const MAT_LEGACY_DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_LEGACY_DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_LEGACY_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\n/**\n * Service to open Material Design modal dialogs.\n * @deprecated Use `MatDialog` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Injectable()\nexport class MatLegacyDialog extends _MatDialogBase<MatLegacyDialogContainer> {\n  protected override dialogConfigClass = MatLegacyDialogConfig;\n\n  constructor(\n    overlay: Overlay,\n    injector: Injector,\n    /**\n     * @deprecated `_location` parameter to be removed.\n     * @breaking-change 10.0.0\n     */\n    @Optional() _location: Location,\n    @Optional() @Inject(MAT_LEGACY_DIALOG_DEFAULT_OPTIONS) defaultOptions: MatLegacyDialogConfig,\n    @Inject(MAT_LEGACY_DIALOG_SCROLL_STRATEGY) scrollStrategy: any,\n    @Optional() @SkipSelf() parentDialog: MatLegacyDialog,\n    /**\n     * @deprecated No longer used. To be removed.\n     * @breaking-change 15.0.0\n     */\n    overlayContainer: OverlayContainer,\n    /**\n     * @deprecated No longer used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    @Optional()\n    @Inject(ANIMATION_MODULE_TYPE)\n    animationMode?: 'NoopAnimations' | 'BrowserAnimations',\n  ) {\n    super(\n      overlay,\n      injector,\n      defaultOptions,\n      parentDialog,\n      overlayContainer,\n      scrollStrategy,\n      MatLegacyDialogRef,\n      MatLegacyDialogContainer,\n      MAT_LEGACY_DIALOG_DATA,\n      animationMode,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  Input,\n  OnChanges,\n  OnInit,\n  Optional,\n  SimpleChanges,\n  ElementRef,\n  OnDestroy,\n} from '@angular/core';\nimport {MatLegacyDialog} from './dialog';\nimport {MatLegacyDialogRef} from './dialog-ref';\nimport {_closeDialogVia} from '@angular/material/dialog';\n\n/** Counter used to generate unique IDs for dialog elements. */\nlet dialogElementUid = 0;\n\n/**\n * Button that will close the current dialog.\n * @deprecated Use `MatDialogClose` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: '[mat-dialog-close], [matDialogClose]',\n  exportAs: 'matDialogClose',\n  host: {\n    '(click)': '_onButtonClick($event)',\n    '[attr.aria-label]': 'ariaLabel || null',\n    '[attr.type]': 'type',\n  },\n})\nexport class MatLegacyDialogClose implements OnInit, OnChanges {\n  /** Screen reader label for the button. */\n  @Input('aria-label') ariaLabel: string;\n\n  /** Default to \"button\" to prevents accidental form submits. */\n  @Input() type: 'submit' | 'button' | 'reset' = 'button';\n\n  /** Dialog close input. */\n  @Input('mat-dialog-close') dialogResult: any;\n\n  @Input('matDialogClose') _matDialogClose: any;\n\n  constructor(\n    /**\n     * Reference to the containing dialog.\n     * @deprecated `dialogRef` property to become private.\n     * @breaking-change 13.0.0\n     */\n    // The dialog title directive is always used in combination with a `MatDialogRef`.\n    // tslint:disable-next-line: lightweight-tokens\n    @Optional() public dialogRef: MatLegacyDialogRef<any>,\n    private _elementRef: ElementRef<HTMLElement>,\n    private _dialog: MatLegacyDialog,\n  ) {}\n\n  ngOnInit() {\n    if (!this.dialogRef) {\n      // When this directive is included in a dialog via TemplateRef (rather than being\n      // in a Component), the DialogRef isn't available via injection because embedded\n      // views cannot be given a custom injector. Instead, we look up the DialogRef by\n      // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n      // be resolved at constructor time.\n      this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs)!;\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n\n    if (proxiedChange) {\n      this.dialogResult = proxiedChange.currentValue;\n    }\n  }\n\n  _onButtonClick(event: MouseEvent) {\n    // Determinate the focus origin using the click event, because using the FocusMonitor will\n    // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n    // dialog, and therefore clicking the button won't result in a focus change. This means that\n    // the FocusMonitor won't detect any origin change, and will always output `program`.\n    _closeDialogVia(\n      this.dialogRef,\n      event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse',\n      this.dialogResult,\n    );\n  }\n}\n\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n * @deprecated Use `MatDialogTitle` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: '[mat-dialog-title], [matDialogTitle]',\n  exportAs: 'matDialogTitle',\n  host: {\n    'class': 'mat-dialog-title',\n    '[id]': 'id',\n  },\n})\nexport class MatLegacyDialogTitle implements OnInit, OnDestroy {\n  /** Unique id for the dialog title. If none is supplied, it will be auto-generated. */\n  @Input() id: string = `mat-dialog-title-${dialogElementUid++}`;\n\n  constructor(\n    // The dialog title directive is always used in combination with a `MatDialogRef`.\n    // tslint:disable-next-line: lightweight-tokens\n    @Optional() private _dialogRef: MatLegacyDialogRef<any>,\n    private _elementRef: ElementRef<HTMLElement>,\n    private _dialog: MatLegacyDialog,\n  ) {}\n\n  ngOnInit() {\n    if (!this._dialogRef) {\n      this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs)!;\n    }\n\n    if (this._dialogRef) {\n      Promise.resolve().then(() => {\n        // Note: we null check the queue, because there are some internal\n        // tests that are mocking out `MatDialogRef` incorrectly.\n        this._dialogRef._containerInstance?._ariaLabelledByQueue?.push(this.id);\n      });\n    }\n  }\n\n  ngOnDestroy() {\n    // Note: we null check the queue, because there are some internal\n    // tests that are mocking out `MatDialogRef` incorrectly.\n    const queue = this._dialogRef?._containerInstance?._ariaLabelledByQueue;\n\n    if (queue) {\n      Promise.resolve().then(() => {\n        const index = queue.indexOf(this.id);\n\n        if (index > -1) {\n          queue.splice(index, 1);\n        }\n      });\n    }\n  }\n}\n\n/**\n * Scrollable content container of a dialog.\n * @deprecated Use `MatDialogContent` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: `[mat-dialog-content], mat-dialog-content, [matDialogContent]`,\n  host: {'class': 'mat-dialog-content'},\n})\nexport class MatLegacyDialogContent {}\n\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n * @deprecated Use `MatDialogActions` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: `[mat-dialog-actions], mat-dialog-actions, [matDialogActions]`,\n  host: {\n    'class': 'mat-dialog-actions',\n    '[class.mat-dialog-actions-align-center]': 'align === \"center\"',\n    '[class.mat-dialog-actions-align-end]': 'align === \"end\"',\n  },\n})\nexport class MatLegacyDialogActions {\n  /**\n   * Horizontal alignment of action buttons.\n   */\n  @Input() align?: 'start' | 'center' | 'end' = 'start';\n}\n\n// TODO(crisbeto): this utility shouldn't be necessary anymore, because the dialog ref is provided\n// both to component and template dialogs through DI. We need to keep it around, because there are\n// some internal wrappers around `MatDialog` that happened to work by accident, because we had this\n// fallback logic in place.\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(\n  element: ElementRef<HTMLElement>,\n  openDialogs: MatLegacyDialogRef<any>[],\n) {\n  let parent: HTMLElement | null = element.nativeElement.parentElement;\n\n  while (parent && !parent.classList.contains('mat-dialog-container')) {\n    parent = parent.parentElement;\n  }\n\n  return parent ? openDialogs.find(dialog => dialog.id === parent!.id) : null;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DialogModule} from '@angular/cdk/dialog';\nimport {OverlayModule} from '@angular/cdk/overlay';\nimport {PortalModule} from '@angular/cdk/portal';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {MAT_LEGACY_DIALOG_SCROLL_STRATEGY_PROVIDER, MatLegacyDialog} from './dialog';\nimport {MatLegacyDialogContainer} from './dialog-container';\nimport {\n  MatLegacyDialogActions,\n  MatLegacyDialogClose,\n  MatLegacyDialogContent,\n  MatLegacyDialogTitle,\n} from './dialog-content-directives';\n\n/**\n * @deprecated Use `MatDialogModule` from `@angular/material/dialog` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@NgModule({\n  imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule],\n  exports: [\n    MatLegacyDialogContainer,\n    MatLegacyDialogClose,\n    MatLegacyDialogTitle,\n    MatLegacyDialogContent,\n    MatLegacyDialogActions,\n    MatCommonModule,\n  ],\n  declarations: [\n    MatLegacyDialogContainer,\n    MatLegacyDialogClose,\n    MatLegacyDialogTitle,\n    MatLegacyDialogActions,\n    MatLegacyDialogContent,\n  ],\n  providers: [MatLegacyDialog, MAT_LEGACY_DIALOG_SCROLL_STRATEGY_PROVIDER],\n})\nexport class MatLegacyDialogModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["DialogConfigBase", "i2.MatLegacyDialogConfig", "i3", "NewDialogRef", "i1", "i1.MatLegacyDialogRef", "i2.MatLegacyDialog"], "mappings": ";;;;;;;;;;;;;;;AAQA;;;;;AAKG;AACI,MAAM,aAAa,GAAG;IAC3B,MAAM,EAAE,EAAC,sBAAsB,EAAE,OAAO,EAAE,qBAAqB,EAAE,MAAM,EAAC;CACzE;;ACND;;;AAGG;AACG,MAAO,qBAA+B,SAAQA,eAAmB,CAAA;AAAvE,IAAA,WAAA,GAAA;;;AAEW,QAAA,IAAA,CAAA,sBAAsB,GAAY,cAAc,CAAC,MAAM,CAAC,sBAAsB,CAAC;;AAG/E,QAAA,IAAA,CAAA,qBAAqB,GAAY,cAAc,CAAC,MAAM,CAAC,qBAAqB,CAAC;KACvF;AAAA;;ACMD;;;;;;AAMG;AAwBG,MAAO,wBAAyB,SAAQ,uBAAuB,CAAA;;AAKnE,IAAA,gBAAgB,CAAC,EAAC,OAAO,EAAE,SAAS,EAAiB,EAAA;QACnD,IAAI,OAAO,KAAK,OAAO,EAAE;AACvB,YAAA,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;AACpC,SAAA;aAAM,IAAI,OAAO,KAAK,MAAM,EAAE;AAC7B,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAC,CAAC,CAAC;AAChE,SAAA;KACF;;AAGD,IAAA,iBAAiB,CAAC,EAAC,OAAO,EAAE,SAAS,EAAiB,EAAA;QACpD,IAAI,OAAO,KAAK,OAAO,EAAE;AACvB,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAC,CAAC,CAAC;AACjE,SAAA;AAAM,aAAA,IAAI,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,EAAE;AACnD,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAC,CAAC,CAAC;AACjE,SAAA;KACF;;IAGD,mBAAmB,GAAA;AACjB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;;;AAIrB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;AAED,IAAA,WAAA,CACE,UAAsB,EACtB,gBAAkC,EACJ,QAAa,EAC3C,YAAmC,EACnC,OAA6B,EAC7B,MAAc,EACd,UAAsB,EACd,kBAAqC,EAC7C,YAA2B,EAAA;AAE3B,QAAA,KAAK,CACH,UAAU,EACV,gBAAgB,EAChB,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,MAAM,EACN,UAAU,EACV,YAAY,CACb,CAAC;QAZM,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;;QArC/C,IAAM,CAAA,MAAA,GAA8B,OAAO,CAAC;KAkD3C;IAED,kBAAkB,GAAA;QAChB,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,MAAM;AAClB,YAAA,MAAM,EAAE;gBACN,wBAAwB,EACtB,IAAI,CAAC,OAAO,CAAC,sBAAsB,IAAI,aAAa,CAAC,MAAM,CAAC,sBAAsB;gBACpF,uBAAuB,EACrB,IAAI,CAAC,OAAO,CAAC,qBAAqB,IAAI,aAAa,CAAC,MAAM,CAAC,qBAAqB;AACnF,aAAA;SACF,CAAC;KACH;AAhEU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,wBAAwB,4EAkCb,QAAQ,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,qBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,oBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAlCnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,wBAAwB,ymBCxDrC,+CACA,EAAA,MAAA,EAAA,CAAA,8iCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EDwCc,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAetC,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBAvBpC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,sBAAsB,EAGjB,aAAA,EAAA,iBAAiB,CAAC,IAAI,mBAGpB,uBAAuB,CAAC,OAAO,EAAA,UAAA,EACpC,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAC3C,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,UAAU,EAAE,IAAI;AAChB,wBAAA,mBAAmB,EAAE,mBAAmB;AACxC,wBAAA,MAAM,EAAE,YAAY;AACpB,wBAAA,aAAa,EAAE,cAAc;AAC7B,wBAAA,wBAAwB,EAAE,oDAAoD;AAC9E,wBAAA,mBAAmB,EAAE,mBAAmB;AACxC,wBAAA,yBAAyB,EAAE,iCAAiC;AAC5D,wBAAA,oBAAoB,EAAE,CAAsB,oBAAA,CAAA;AAC5C,wBAAA,0BAA0B,EAAE,2BAA2B;AACvD,wBAAA,yBAAyB,EAAE,0BAA0B;AACtD,qBAAA,EAAA,QAAA,EAAA,+CAAA,EAAA,MAAA,EAAA,CAAA,8iCAAA,CAAA,EAAA,CAAA;;0BAoCE,QAAQ;;0BAAI,MAAM;2BAAC,QAAQ,CAAA;;;AEhFhC;;;;AAIG;AACG,MAAO,kBAA+B,SAAQC,YAAkB,CAAA;AAAG;;ACEzE;;;;AAIG;MACU,sBAAsB,GAAG,IAAI,cAAc,CAAM,eAAe,EAAE;AAE/E;;;;AAIG;MACU,iCAAiC,GAAG,IAAI,cAAc,CACjE,4BAA4B,EAC5B;AAEF;;;;AAIG;MACU,iCAAiC,GAAG,IAAI,cAAc,CACjE,4BAA4B,EAC5B;AAEF;;;;AAIG;AACG,SAAU,kDAAkD,CAChE,OAAgB,EAAA;IAEhB,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;AAChD,CAAC;AAED;;;;AAIG;AACU,MAAA,0CAA0C,GAAG;AACxD,IAAA,OAAO,EAAE,iCAAiC;IAC1C,IAAI,EAAE,CAAC,OAAO,CAAC;AACf,IAAA,UAAU,EAAE,kDAAkD;EAC9D;AAEF;;;;AAIG;AAEG,MAAO,eAAgB,SAAQ,cAAwC,CAAA;IAG3E,WACE,CAAA,OAAgB,EAChB,QAAkB;AAClB;;;AAGG;AACS,IAAA,SAAmB,EACwB,cAAqC,EACjD,cAAmB,EACtC,YAA6B;AACrD;;;AAGG;IACH,gBAAkC;AAClC;;;AAGG;IAGH,aAAsD,EAAA;QAEtD,KAAK,CACH,OAAO,EACP,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,wBAAwB,EACxB,sBAAsB,EACtB,aAAa,CACd,CAAC;QArCe,IAAiB,CAAA,iBAAA,GAAG,qBAAqB,CAAC;KAsC5D;AAvCU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAWJ,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,OAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,iCAAiC,EAC7C,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,iCAAiC,2GAYjC,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;kHAxBpB,eAAe,EAAA,CAAA,CAAA,EAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;;0BAWN,QAAQ;;0BACR,QAAQ;;0BAAI,MAAM;2BAAC,iCAAiC,CAAA;;0BACpD,MAAM;2BAAC,iCAAiC,CAAA;;0BACxC,QAAQ;;0BAAI,QAAQ;;0BAUpB,QAAQ;;0BACR,MAAM;2BAAC,qBAAqB,CAAA;;;ACxEjC;AACA,IAAI,gBAAgB,GAAG,CAAC,CAAC;AAEzB;;;;AAIG;MAUU,oBAAoB,CAAA;AAY/B,IAAA,WAAA;AACE;;;;AAIG;;;IAGgB,SAAkC,EAC7C,WAAoC,EACpC,OAAwB,EAAA;QAFb,IAAS,CAAA,SAAA,GAAT,SAAS,CAAyB;QAC7C,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACpC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;;QAjBzB,IAAI,CAAA,IAAA,GAAkC,QAAQ,CAAC;KAkBpD;IAEJ,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;;;;;;AAMnB,YAAA,IAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAE,CAAC;AAChF,SAAA;KACF;AAED,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,aAAa,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAErF,QAAA,IAAI,aAAa,EAAE;AACjB,YAAA,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;AAChD,SAAA;KACF;AAED,IAAA,cAAc,CAAC,KAAiB,EAAA;;;;;AAK9B,QAAA,eAAe,CACb,IAAI,CAAC,SAAS,EACd,KAAK,CAAC,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,EACjE,IAAI,CAAC,YAAY,CAClB,CAAC;KACH;8GAtDU,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,kBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAApB,oBAAoB,EAAA,QAAA,EAAA,sCAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,YAAA,EAAA,CAAA,kBAAA,EAAA,cAAA,CAAA,EAAA,eAAA,EAAA,CAAA,gBAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAThC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,SAAS,EAAE,wBAAwB;AACnC,wBAAA,mBAAmB,EAAE,mBAAmB;AACxC,wBAAA,aAAa,EAAE,MAAM;AACtB,qBAAA;AACF,iBAAA,CAAA;;0BAqBI,QAAQ;gGAlBU,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY,CAAA;gBAGV,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAGqB,YAAY,EAAA,CAAA;sBAAtC,KAAK;uBAAC,kBAAkB,CAAA;gBAEA,eAAe,EAAA,CAAA;sBAAvC,KAAK;uBAAC,gBAAgB,CAAA;;AA+CzB;;;;AAIG;MASU,oBAAoB,CAAA;AAI/B,IAAA,WAAA;;;IAGsB,UAAmC,EAC/C,WAAoC,EACpC,OAAwB,EAAA;QAFZ,IAAU,CAAA,UAAA,GAAV,UAAU,CAAyB;QAC/C,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACpC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;;AAPzB,QAAA,IAAA,CAAA,EAAE,GAAW,CAAA,iBAAA,EAAoB,gBAAgB,EAAE,EAAE,CAAC;KAQ3D;IAEJ,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAE,CAAC;AACjF,SAAA;QAED,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;;;AAG1B,gBAAA,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1E,aAAC,CAAC,CAAC;AACJ,SAAA;KACF;IAED,WAAW,GAAA;;;QAGT,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,kBAAkB,EAAE,oBAAoB,CAAC;AAExE,QAAA,IAAI,KAAK,EAAE;AACT,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;gBAC1B,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAErC,gBAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACd,oBAAA,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACxB,iBAAA;AACH,aAAC,CAAC,CAAC;AACJ,SAAA;KACF;8GAxCU,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAD,kBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAApB,oBAAoB,EAAA,QAAA,EAAA,sCAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBARhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,kBAAkB;AAC3B,wBAAA,MAAM,EAAE,IAAI;AACb,qBAAA;AACF,iBAAA,CAAA;;0BAQI,QAAQ;gGALF,EAAE,EAAA,CAAA;sBAAV,KAAK;;AAyCR;;;;AAIG;MAKU,sBAAsB,CAAA;8GAAtB,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAtB,sBAAsB,EAAA,QAAA,EAAA,8DAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAtB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAJlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAA8D,4DAAA,CAAA;AACxE,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,oBAAoB,EAAC;AACtC,iBAAA,CAAA;;AAGD;;;;;AAKG;MASU,sBAAsB,CAAA;AARnC,IAAA,WAAA,GAAA;AASE;;AAEG;QACM,IAAK,CAAA,KAAA,GAAgC,OAAO,CAAC;AACvD,KAAA;8GALY,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAtB,sBAAsB,EAAA,QAAA,EAAA,8DAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,uCAAA,EAAA,sBAAA,EAAA,oCAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAtB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBARlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAA8D,4DAAA,CAAA;AACxE,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,oBAAoB;AAC7B,wBAAA,yCAAyC,EAAE,oBAAoB;AAC/D,wBAAA,sCAAsC,EAAE,iBAAiB;AAC1D,qBAAA;AACF,iBAAA,CAAA;8BAKU,KAAK,EAAA,CAAA;sBAAb,KAAK;;AAGR;AACA;AACA;AACA;AACA;;;;AAIG;AACH,SAAS,gBAAgB,CACvB,OAAgC,EAChC,WAAsC,EAAA;AAEtC,IAAA,IAAI,MAAM,GAAuB,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC;IAErE,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE;AACnE,QAAA,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC;AAC/B,KAAA;IAED,OAAO,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK,MAAO,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;AAC9E;;ACtLA;;;AAGG;MAoBU,qBAAqB,CAAA;8GAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAArB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,iBAR9B,wBAAwB;YACxB,oBAAoB;YACpB,oBAAoB;YACpB,sBAAsB;YACtB,sBAAsB,CAAA,EAAA,OAAA,EAAA,CAdd,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,CAAA,EAAA,OAAA,EAAA,CAElE,wBAAwB;YACxB,oBAAoB;YACpB,oBAAoB;YACpB,sBAAsB;YACtB,sBAAsB;YACtB,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;AAWN,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,EAFrB,SAAA,EAAA,CAAC,eAAe,EAAE,0CAA0C,CAAC,EAAA,OAAA,EAAA,CAhB9D,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAOlE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAWN,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAnBjC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,CAAC;AACrE,oBAAA,OAAO,EAAE;wBACP,wBAAwB;wBACxB,oBAAoB;wBACpB,oBAAoB;wBACpB,sBAAsB;wBACtB,sBAAsB;wBACtB,eAAe;AAChB,qBAAA;AACD,oBAAA,YAAY,EAAE;wBACZ,wBAAwB;wBACxB,oBAAoB;wBACpB,oBAAoB;wBACpB,sBAAsB;wBACtB,sBAAsB;AACvB,qBAAA;AACD,oBAAA,SAAS,EAAE,CAAC,eAAe,EAAE,0CAA0C,CAAC;AACzE,iBAAA,CAAA;;;AC5CD;;AAEG;;;;"}