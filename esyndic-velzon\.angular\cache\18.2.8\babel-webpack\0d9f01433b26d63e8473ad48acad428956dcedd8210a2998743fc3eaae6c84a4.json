{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Thai [th]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/sirn\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var th = moment.defineLocale('th', {\n    months: 'มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม'.split('_'),\n    monthsShort: 'ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์'.split('_'),\n    weekdaysShort: 'อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัส_ศุกร์_เสาร์'.split('_'),\n    // yes, three characters difference\n    weekdaysMin: 'อา._จ._อ._พ._พฤ._ศ._ส.'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY เวลา H:mm',\n      LLLL: 'วันddddที่ D MMMM YYYY เวลา H:mm'\n    },\n    meridiemParse: /ก่อนเที่ยง|หลังเที่ยง/,\n    isPM: function (input) {\n      return input === 'หลังเที่ยง';\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'ก่อนเที่ยง';\n      } else {\n        return 'หลังเที่ยง';\n      }\n    },\n    calendar: {\n      sameDay: '[วันนี้ เวลา] LT',\n      nextDay: '[พรุ่งนี้ เวลา] LT',\n      nextWeek: 'dddd[หน้า เวลา] LT',\n      lastDay: '[เมื่อวานนี้ เวลา] LT',\n      lastWeek: '[วัน]dddd[ที่แล้ว เวลา] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'อีก %s',\n      past: '%sที่แล้ว',\n      s: 'ไม่กี่วินาที',\n      ss: '%d วินาที',\n      m: '1 นาที',\n      mm: '%d นาที',\n      h: '1 ชั่วโมง',\n      hh: '%d ชั่วโมง',\n      d: '1 วัน',\n      dd: '%d วัน',\n      w: '1 สัปดาห์',\n      ww: '%d สัปดาห์',\n      M: '1 เดือน',\n      MM: '%d เดือน',\n      y: '1 ปี',\n      yy: '%d ปี'\n    }\n  });\n  return th;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "th", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "meridiem", "hour", "minute", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/moment/locale/th.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Thai [th]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/sirn\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var th = moment.defineLocale('th', {\n        months: 'มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม'.split(\n            '_'\n        ),\n        monthsShort:\n            'ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays: 'อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์'.split('_'),\n        weekdaysShort: 'อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัส_ศุกร์_เสาร์'.split('_'), // yes, three characters difference\n        weekdaysMin: 'อา._จ._อ._พ._พฤ._ศ._ส.'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY เวลา H:mm',\n            LLLL: 'วันddddที่ D MMMM YYYY เวลา H:mm',\n        },\n        meridiemParse: /ก่อนเที่ยง|หลังเที่ยง/,\n        isPM: function (input) {\n            return input === 'หลังเที่ยง';\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 12) {\n                return 'ก่อนเที่ยง';\n            } else {\n                return 'หลังเที่ยง';\n            }\n        },\n        calendar: {\n            sameDay: '[วันนี้ เวลา] LT',\n            nextDay: '[พรุ่งนี้ เวลา] LT',\n            nextWeek: 'dddd[หน้า เวลา] LT',\n            lastDay: '[เมื่อวานนี้ เวลา] LT',\n            lastWeek: '[วัน]dddd[ที่แล้ว เวลา] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'อีก %s',\n            past: '%sที่แล้ว',\n            s: 'ไม่กี่วินาที',\n            ss: '%d วินาที',\n            m: '1 นาที',\n            mm: '%d นาที',\n            h: '1 ชั่วโมง',\n            hh: '%d ชั่วโมง',\n            d: '1 วัน',\n            dd: '%d วัน',\n            w: '1 สัปดาห์',\n            ww: '%d สัปดาห์',\n            M: '1 เดือน',\n            MM: '%d เดือน',\n            y: '1 ปี',\n            yy: '%d ปี',\n        },\n    });\n\n    return th;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,mGAAmG,CAACC,KAAK,CAC7G,GACJ,CAAC;IACDC,WAAW,EACP,gEAAgE,CAACD,KAAK,CAClE,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,gDAAgD,CAACH,KAAK,CAAC,GAAG,CAAC;IACrEI,aAAa,EAAE,6CAA6C,CAACJ,KAAK,CAAC,GAAG,CAAC;IAAE;IACzEK,WAAW,EAAE,wBAAwB,CAACL,KAAK,CAAC,GAAG,CAAC;IAChDM,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,uBAAuB;MAC5BC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,uBAAuB;IACtCC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAOA,KAAK,KAAK,YAAY;IACjC,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACX,OAAO,YAAY;MACvB,CAAC,MAAM;QACH,OAAO,YAAY;MACvB;IACJ,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,kBAAkB;MAC3BC,OAAO,EAAE,oBAAoB;MAC7BC,QAAQ,EAAE,oBAAoB;MAC9BC,OAAO,EAAE,uBAAuB;MAChCC,QAAQ,EAAE,4BAA4B;MACtCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,WAAW;MACjBC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE;IACR;EACJ,CAAC,CAAC;EAEF,OAAO/C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}