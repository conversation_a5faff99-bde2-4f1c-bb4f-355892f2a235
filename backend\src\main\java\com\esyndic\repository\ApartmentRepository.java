package com.esyndic.repository;

import com.esyndic.entity.Apartment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ApartmentRepository extends JpaRepository<Apartment, UUID> {

    List<Apartment> findByBuildingId(UUID buildingId);

    List<Apartment> findByBuildingIdAndIsActiveTrue(UUID buildingId);

    List<Apartment> findByOwnerId(UUID ownerId);

    List<Apartment> findByResidentId(UUID residentId);

    Optional<Apartment> findByBuildingIdAndApartmentNumber(UUID buildingId, String apartmentNumber);

    List<Apartment> findByFloorNumber(Integer floorNumber);

    List<Apartment> findByBuildingIdAndFloorNumber(UUID buildingId, Integer floorNumber);

    @Query("SELECT a FROM Apartment a WHERE a.building.id = :buildingId AND a.owner IS NULL")
    List<Apartment> findUnownedApartmentsByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT a FROM Apartment a WHERE a.building.id = :buildingId AND a.resident IS NULL")
    List<Apartment> findUnoccupiedApartmentsByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT a FROM Apartment a WHERE a.monthlyDues >= :minDues AND a.monthlyDues <= :maxDues")
    List<Apartment> findByMonthlyDuesRange(@Param("minDues") BigDecimal minDues, 
                                         @Param("maxDues") BigDecimal maxDues);

    @Query("SELECT a FROM Apartment a WHERE a.areaSqm >= :minArea AND a.areaSqm <= :maxArea")
    List<Apartment> findByAreaRange(@Param("minArea") BigDecimal minArea, 
                                  @Param("maxArea") BigDecimal maxArea);

    @Query("SELECT COUNT(a) FROM Apartment a WHERE a.building.id = :buildingId AND a.isActive = true")
    long countActiveApartmentsByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT COUNT(a) FROM Apartment a WHERE a.building.id = :buildingId AND a.owner IS NOT NULL")
    long countOwnedApartmentsByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT COUNT(a) FROM Apartment a WHERE a.building.id = :buildingId AND a.resident IS NOT NULL")
    long countOccupiedApartmentsByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT SUM(a.monthlyDues) FROM Apartment a WHERE a.building.id = :buildingId AND a.isActive = true")
    BigDecimal getTotalMonthlyDuesByBuildingId(@Param("buildingId") UUID buildingId);

    boolean existsByBuildingIdAndApartmentNumber(UUID buildingId, String apartmentNumber);

    @Query("SELECT a FROM Apartment a WHERE a.apartmentNumber LIKE %:number%")
    List<Apartment> findByApartmentNumberContaining(@Param("number") String number);
}
