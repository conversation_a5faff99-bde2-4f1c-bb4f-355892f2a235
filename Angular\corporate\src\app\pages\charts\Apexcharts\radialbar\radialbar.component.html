<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Radialbar Charts" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
  <div class="col-xl-6">
      <div class="card">
          <div class="card-header">
              <h4 class="card-title mb-0">Simple Radialbar Chart</h4>
          </div><!-- end card header -->

          <div class="card-body">
            <apx-chart [series]="basicRadialbarChart.series" [chart]="basicRadialbarChart.chart"
            [plotOptions]="basicRadialbarChart.plotOptions" [labels]="basicRadialbarChart.labels"
            [colors]="basicRadialbarChart.colors" dir="ltr"></apx-chart>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->

  <div class="col-xl-6">
      <div class="card">
          <div class="card-header">
              <h4 class="card-title mb-0">Multiple Radialbar</h4>
          </div><!-- end card header -->

          <div class="card-body">
            <apx-chart [series]="multipleRadialbarChart.series" [chart]="multipleRadialbarChart.chart"
            [plotOptions]="multipleRadialbarChart.plotOptions" [labels]="multipleRadialbarChart.labels"
            [colors]="multipleRadialbarChart.colors" dir="ltr"></apx-chart>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->
</div>
<!-- end row -->

<div class="row">
  <div class="col-xl-6">
      <div class="card">
          <div class="card-header">
              <h4 class="card-title mb-0">Circle Chart - Custom Angle</h4>
          </div><!-- end card header -->

          <div class="card-body">
            <apx-chart [series]="customAngleChart.series" [chart]="customAngleChart.chart"
            [plotOptions]="customAngleChart.plotOptions" [labels]="customAngleChart.labels"
            [legend]="customAngleChart.legend" [colors]="customAngleChart.colors"
            [responsive]="customAngleChart.responsive" dir="ltr"></apx-chart>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->

  <div class="col-xl-6">
      <div class="card">
          <div class="card-header">
              <h4 class="card-title mb-0">Gradient Circle Chart</h4>
          </div><!-- end card header -->

          <div class="card-body">
            <apx-chart [series]="gradientCircleChart.series" [chart]="gradientCircleChart.chart"
            [plotOptions]="gradientCircleChart.plotOptions" [labels]="gradientCircleChart.labels"
            [stroke]="gradientCircleChart.stroke" [fill]="gradientCircleChart.fill" dir="ltr"></apx-chart>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->
</div>
<!-- end row -->

<div class="row">
  <div class="col-xl-6">
      <div class="card">
          <div class="card-header">
              <h4 class="card-title mb-0">Stroked Circle Chart</h4>
          </div><!-- end card header -->

          <div class="card-body">
            <apx-chart [series]="strokedCircleChart.series" [chart]="strokedCircleChart.chart"
            [plotOptions]="strokedCircleChart.plotOptions" [labels]="strokedCircleChart.labels"
            [fill]="strokedCircleChart.fill" [stroke]="strokedCircleChart.stroke" [colors]="strokedCircleChart.colors" dir="ltr"></apx-chart>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->

  <div class="col-xl-6">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title mb-0">Radialbars with Image</h4>
        </div><!-- end card header -->

        <div class="card-body">
            <apx-chart [series]="radialbarsChart.series" [chart]="radialbarsChart.chart"
            [plotOptions]="radialbarsChart.plotOptions" [fill]="radialbarsChart.fill"
            [stroke]="radialbarsChart.stroke" [labels]="radialbarsChart.labels" dir="ltr"></apx-chart>
        </div><!-- end card-body -->
    </div><!-- end card -->
  </div>
 <!-- end col -->

</div>
<!-- end row -->

<div class="row">
  <div class="col-xl-6">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title mb-0">Semi Circular Chart</h4>
        </div><!-- end card header -->

        <div class="card-body">
          <apx-chart [series]="semiCircleChart.series" [chart]="semiCircleChart.chart"
          [plotOptions]="semiCircleChart.plotOptions" [labels]="semiCircleChart.labels"
          [fill]="semiCircleChart.fill" [colors]="semiCircleChart.colors" dir="ltr"></apx-chart>
        </div><!-- end card-body -->
    </div><!-- end card -->
</div>
<!-- end col -->
</div>
