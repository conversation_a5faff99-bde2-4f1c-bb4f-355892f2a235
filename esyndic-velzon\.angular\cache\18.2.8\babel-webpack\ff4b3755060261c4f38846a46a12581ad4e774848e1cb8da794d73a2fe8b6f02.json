{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport checkMarkerInSeries from './checkMarkerInSeries.js';\nimport MarkLineModel from './MarkLineModel.js';\nimport MarkLineView from './MarkLineView.js';\nexport function install(registers) {\n  registers.registerComponentModel(MarkLineModel);\n  registers.registerComponentView(MarkLineView);\n  registers.registerPreprocessor(function (opt) {\n    if (checkMarkerInSeries(opt.series, 'markLine')) {\n      // Make sure markLine component is enabled\n      opt.markLine = opt.markLine || {};\n    }\n  });\n}", "map": {"version": 3, "names": ["checkMarkerInSeries", "MarkLineModel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "install", "registers", "registerComponentModel", "registerComponentView", "registerPreprocessor", "opt", "series", "markLine"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/component/marker/installMarkLine.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport checkMarkerInSeries from './checkMarkerInSeries.js';\nimport MarkLineModel from './MarkLineModel.js';\nimport MarkLineView from './MarkLineView.js';\nexport function install(registers) {\n  registers.registerComponentModel(MarkLineModel);\n  registers.registerComponentView(MarkLineView);\n  registers.registerPreprocessor(function (opt) {\n    if (checkMarkerInSeries(opt.series, 'markLine')) {\n      // Make sure markLine component is enabled\n      opt.markLine = opt.markLine || {};\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA,OAAOA,mBAAmB,MAAM,0BAA0B;AAC1D,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,sBAAsB,CAACJ,aAAa,CAAC;EAC/CG,SAAS,CAACE,qBAAqB,CAACJ,YAAY,CAAC;EAC7CE,SAAS,CAACG,oBAAoB,CAAC,UAAUC,GAAG,EAAE;IAC5C,IAAIR,mBAAmB,CAACQ,GAAG,CAACC,MAAM,EAAE,UAAU,CAAC,EAAE;MAC/C;MACAD,GAAG,CAACE,QAAQ,GAAGF,GAAG,CAACE,QAAQ,IAAI,CAAC,CAAC;IACnC;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}