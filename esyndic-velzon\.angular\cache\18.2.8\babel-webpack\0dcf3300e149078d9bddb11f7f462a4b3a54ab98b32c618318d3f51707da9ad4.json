{"ast": null, "code": "import * as i0 from \"@angular/core\";\n/**\n * Offline Component\n */\nexport class OfflineComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function OfflineComponent_Factory(t) {\n      return new (t || OfflineComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OfflineComponent,\n      selectors: [[\"app-offline\"]],\n      decls: 17,\n      vars: 0,\n      consts: [[1, \"auth-page-wrapper\", \"auth-bg-cover\", \"py-5\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"min-vh-100\"], [1, \"bg-overlay\"], [1, \"auth-page-content\", \"overflow-hidden\", \"pt-lg-5\"], [1, \"container\"], [1, \"row\", \"justify-content-center\"], [1, \"col-xl-5\"], [1, \"card\", \"overflow-hidden\"], [1, \"card-body\", \"p-4\"], [1, \"text-center\"], [\"src\", \"https://img.themesbrand.com/velzon/images/auth-offline.gif\", \"alt\", \"\", \"height\", \"210\"], [1, \"mt-4\", \"fw-semibold\"], [1, \"text-muted\", \"mb-4\", \"fs-14\"], [\"onClick\", \"window.location.href=window.location.href\", 1, \"btn\", \"btn-success\", \"btn-border\"], [1, \"ri-refresh-line\", \"align-bottom\"]],\n      template: function OfflineComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelement(9, \"img\", 9);\n          i0.ɵɵelementStart(10, \"h3\", 10);\n          i0.ɵɵtext(11, \"We're currently offline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\", 11);\n          i0.ɵɵtext(13, \"We can't show you this images because you aren't connected to the internet. When you\\u2019re back online refresh the page or hit the button below\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 12);\n          i0.ɵɵelement(15, \"i\", 13);\n          i0.ɵɵtext(16, \" Refresh\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["OfflineComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "consts", "template", "OfflineComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\account\\auth\\errors\\offline\\offline.component.ts", "C:\\e-syndic\\esyndic-velzon\\src\\app\\account\\auth\\errors\\offline\\offline.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-offline',\r\n  templateUrl: './offline.component.html',\r\n  styleUrls: ['./offline.component.scss']\r\n})\r\n\r\n/**\r\n * Offline Component\r\n */\r\nexport class OfflineComponent implements OnInit {\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n}\r\n", "<!-- auth-page wrapper -->\r\n<div class=\"auth-page-wrapper auth-bg-cover py-5 d-flex justify-content-center align-items-center min-vh-100\">\r\n    <div class=\"bg-overlay\"></div>\r\n    <!-- auth-page content -->\r\n    <div class=\"auth-page-content overflow-hidden pt-lg-5\">\r\n        <div class=\"container\">\r\n            <div class=\"row justify-content-center\">\r\n                <div class=\"col-xl-5\">\r\n                    <div class=\"card overflow-hidden\">\r\n                        <div class=\"card-body p-4\">\r\n                            <div class=\"text-center\">\r\n                                <img src=\"https://img.themesbrand.com/velzon/images/auth-offline.gif\" alt=\"\"\r\n                                    height=\"210\">\r\n                                <h3 class=\"mt-4 fw-semibold\">We're currently offline</h3>\r\n                                <p class=\"text-muted mb-4 fs-14\">We can't show you this images because you aren't\r\n                                    connected to the internet. When you’re back online refresh the page or hit the\r\n                                    button below</p>\r\n                                <button class=\"btn btn-success btn-border\"\r\n                                    onClick=\"window.location.href=window.location.href\"><i\r\n                                        class=\"ri-refresh-line align-bottom\"></i> Refresh</button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <!-- end card -->\r\n                </div>\r\n                <!-- end col -->\r\n\r\n            </div>\r\n            <!-- end row -->\r\n        </div>\r\n        <!-- end container -->\r\n    </div>\r\n    <!-- end auth page content -->\r\n</div>\r\n<!-- end auth-page-wrapper -->"], "mappings": ";AAQA;;;AAGA,OAAM,MAAOA,gBAAgB;EAE3BC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;;;uBALWF,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV7BE,EAAA,CAAAC,cAAA,aAA8G;UAC1GD,EAAA,CAAAE,SAAA,aAA8B;UAQNF,EANxB,CAAAC,cAAA,aAAuD,aAC5B,aACqB,aACd,aACgB,aACH,aACE;UACrBD,EAAA,CAAAE,SAAA,aACiB;UACjBF,EAAA,CAAAC,cAAA,cAA6B;UAAAD,EAAA,CAAAG,MAAA,+BAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzDJ,EAAA,CAAAC,cAAA,aAAiC;UAAAD,EAAA,CAAAG,MAAA,yJAEjB;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpBJ,EAAA,CAAAC,cAAA,kBACwD;UAAAD,EAAA,CAAAE,SAAA,aACP;UAACF,EAAA,CAAAG,MAAA,gBAAO;UAczFH,EAdyF,CAAAI,YAAA,EAAS,EAChE,EACJ,EACJ,EAEJ,EAGJ,EAEJ,EAEJ,EAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}