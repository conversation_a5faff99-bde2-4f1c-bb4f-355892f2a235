{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/**\n * Parse and decode geo json\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { GeoJSONLineStringGeometry, GeoJSONPolygonGeometry, GeoJSONRegion } from './Region.js';\nfunction decode(json) {\n  if (!json.UTF8Encoding) {\n    return json;\n  }\n  var jsonCompressed = json;\n  var encodeScale = jsonCompressed.UTF8Scale;\n  if (encodeScale == null) {\n    encodeScale = 1024;\n  }\n  var features = jsonCompressed.features;\n  zrUtil.each(features, function (feature) {\n    var geometry = feature.geometry;\n    var encodeOffsets = geometry.encodeOffsets;\n    var coordinates = geometry.coordinates;\n    // Geometry may be appeded manually in the script after json loaded.\n    // In this case this geometry is usually not encoded.\n    if (!encodeOffsets) {\n      return;\n    }\n    switch (geometry.type) {\n      case 'LineString':\n        geometry.coordinates = decodeRing(coordinates, encodeOffsets, encodeScale);\n        break;\n      case 'Polygon':\n        decodeRings(coordinates, encodeOffsets, encodeScale);\n        break;\n      case 'MultiLineString':\n        decodeRings(coordinates, encodeOffsets, encodeScale);\n        break;\n      case 'MultiPolygon':\n        zrUtil.each(coordinates, function (rings, idx) {\n          return decodeRings(rings, encodeOffsets[idx], encodeScale);\n        });\n    }\n  });\n  // Has been decoded\n  jsonCompressed.UTF8Encoding = false;\n  return jsonCompressed;\n}\nfunction decodeRings(rings, encodeOffsets, encodeScale) {\n  for (var c = 0; c < rings.length; c++) {\n    rings[c] = decodeRing(rings[c], encodeOffsets[c], encodeScale);\n  }\n}\nfunction decodeRing(coordinate, encodeOffsets, encodeScale) {\n  var result = [];\n  var prevX = encodeOffsets[0];\n  var prevY = encodeOffsets[1];\n  for (var i = 0; i < coordinate.length; i += 2) {\n    var x = coordinate.charCodeAt(i) - 64;\n    var y = coordinate.charCodeAt(i + 1) - 64;\n    // ZigZag decoding\n    x = x >> 1 ^ -(x & 1);\n    y = y >> 1 ^ -(y & 1);\n    // Delta deocding\n    x += prevX;\n    y += prevY;\n    prevX = x;\n    prevY = y;\n    // Dequantize\n    result.push([x / encodeScale, y / encodeScale]);\n  }\n  return result;\n}\nexport default function parseGeoJSON(geoJson, nameProperty) {\n  geoJson = decode(geoJson);\n  return zrUtil.map(zrUtil.filter(geoJson.features, function (featureObj) {\n    // Output of mapshaper may have geometry null\n    return featureObj.geometry && featureObj.properties && featureObj.geometry.coordinates.length > 0;\n  }), function (featureObj) {\n    var properties = featureObj.properties;\n    var geo = featureObj.geometry;\n    var geometries = [];\n    switch (geo.type) {\n      case 'Polygon':\n        var coordinates = geo.coordinates;\n        // According to the GeoJSON specification.\n        // First must be exterior, and the rest are all interior(holes).\n        geometries.push(new GeoJSONPolygonGeometry(coordinates[0], coordinates.slice(1)));\n        break;\n      case 'MultiPolygon':\n        zrUtil.each(geo.coordinates, function (item) {\n          if (item[0]) {\n            geometries.push(new GeoJSONPolygonGeometry(item[0], item.slice(1)));\n          }\n        });\n        break;\n      case 'LineString':\n        geometries.push(new GeoJSONLineStringGeometry([geo.coordinates]));\n        break;\n      case 'MultiLineString':\n        geometries.push(new GeoJSONLineStringGeometry(geo.coordinates));\n    }\n    var region = new GeoJSONRegion(properties[nameProperty || 'name'], geometries, properties.cp);\n    region.properties = properties;\n    return region;\n  });\n}", "map": {"version": 3, "names": ["zrUtil", "GeoJSONLineStringGeometry", "GeoJSONPolygonGeometry", "GeoJSONRegion", "decode", "json", "UTF8Encoding", "jsonCompressed", "encodeScale", "UTF8Scale", "features", "each", "feature", "geometry", "encodeOffsets", "coordinates", "type", "decodeRing", "decodeRings", "rings", "idx", "c", "length", "coordinate", "result", "prevX", "prevY", "i", "x", "charCodeAt", "y", "push", "parseGeoJSON", "geoJson", "nameProperty", "map", "filter", "featureObj", "properties", "geo", "geometries", "slice", "item", "region", "cp"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/coord/geo/parseGeoJson.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/**\n * Parse and decode geo json\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { GeoJSONLineStringGeometry, GeoJSONPolygonGeometry, GeoJSONRegion } from './Region.js';\nfunction decode(json) {\n  if (!json.UTF8Encoding) {\n    return json;\n  }\n  var jsonCompressed = json;\n  var encodeScale = jsonCompressed.UTF8Scale;\n  if (encodeScale == null) {\n    encodeScale = 1024;\n  }\n  var features = jsonCompressed.features;\n  zrUtil.each(features, function (feature) {\n    var geometry = feature.geometry;\n    var encodeOffsets = geometry.encodeOffsets;\n    var coordinates = geometry.coordinates;\n    // Geometry may be appeded manually in the script after json loaded.\n    // In this case this geometry is usually not encoded.\n    if (!encodeOffsets) {\n      return;\n    }\n    switch (geometry.type) {\n      case 'LineString':\n        geometry.coordinates = decodeRing(coordinates, encodeOffsets, encodeScale);\n        break;\n      case 'Polygon':\n        decodeRings(coordinates, encodeOffsets, encodeScale);\n        break;\n      case 'MultiLineString':\n        decodeRings(coordinates, encodeOffsets, encodeScale);\n        break;\n      case 'MultiPolygon':\n        zrUtil.each(coordinates, function (rings, idx) {\n          return decodeRings(rings, encodeOffsets[idx], encodeScale);\n        });\n    }\n  });\n  // Has been decoded\n  jsonCompressed.UTF8Encoding = false;\n  return jsonCompressed;\n}\nfunction decodeRings(rings, encodeOffsets, encodeScale) {\n  for (var c = 0; c < rings.length; c++) {\n    rings[c] = decodeRing(rings[c], encodeOffsets[c], encodeScale);\n  }\n}\nfunction decodeRing(coordinate, encodeOffsets, encodeScale) {\n  var result = [];\n  var prevX = encodeOffsets[0];\n  var prevY = encodeOffsets[1];\n  for (var i = 0; i < coordinate.length; i += 2) {\n    var x = coordinate.charCodeAt(i) - 64;\n    var y = coordinate.charCodeAt(i + 1) - 64;\n    // ZigZag decoding\n    x = x >> 1 ^ -(x & 1);\n    y = y >> 1 ^ -(y & 1);\n    // Delta deocding\n    x += prevX;\n    y += prevY;\n    prevX = x;\n    prevY = y;\n    // Dequantize\n    result.push([x / encodeScale, y / encodeScale]);\n  }\n  return result;\n}\nexport default function parseGeoJSON(geoJson, nameProperty) {\n  geoJson = decode(geoJson);\n  return zrUtil.map(zrUtil.filter(geoJson.features, function (featureObj) {\n    // Output of mapshaper may have geometry null\n    return featureObj.geometry && featureObj.properties && featureObj.geometry.coordinates.length > 0;\n  }), function (featureObj) {\n    var properties = featureObj.properties;\n    var geo = featureObj.geometry;\n    var geometries = [];\n    switch (geo.type) {\n      case 'Polygon':\n        var coordinates = geo.coordinates;\n        // According to the GeoJSON specification.\n        // First must be exterior, and the rest are all interior(holes).\n        geometries.push(new GeoJSONPolygonGeometry(coordinates[0], coordinates.slice(1)));\n        break;\n      case 'MultiPolygon':\n        zrUtil.each(geo.coordinates, function (item) {\n          if (item[0]) {\n            geometries.push(new GeoJSONPolygonGeometry(item[0], item.slice(1)));\n          }\n        });\n        break;\n      case 'LineString':\n        geometries.push(new GeoJSONLineStringGeometry([geo.coordinates]));\n        break;\n      case 'MultiLineString':\n        geometries.push(new GeoJSONLineStringGeometry(geo.coordinates));\n    }\n    var region = new GeoJSONRegion(properties[nameProperty || 'name'], geometries, properties.cp);\n    region.properties = properties;\n    return region;\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,yBAAyB,EAAEC,sBAAsB,EAAEC,aAAa,QAAQ,aAAa;AAC9F,SAASC,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAI,CAACA,IAAI,CAACC,YAAY,EAAE;IACtB,OAAOD,IAAI;EACb;EACA,IAAIE,cAAc,GAAGF,IAAI;EACzB,IAAIG,WAAW,GAAGD,cAAc,CAACE,SAAS;EAC1C,IAAID,WAAW,IAAI,IAAI,EAAE;IACvBA,WAAW,GAAG,IAAI;EACpB;EACA,IAAIE,QAAQ,GAAGH,cAAc,CAACG,QAAQ;EACtCV,MAAM,CAACW,IAAI,CAACD,QAAQ,EAAE,UAAUE,OAAO,EAAE;IACvC,IAAIC,QAAQ,GAAGD,OAAO,CAACC,QAAQ;IAC/B,IAAIC,aAAa,GAAGD,QAAQ,CAACC,aAAa;IAC1C,IAAIC,WAAW,GAAGF,QAAQ,CAACE,WAAW;IACtC;IACA;IACA,IAAI,CAACD,aAAa,EAAE;MAClB;IACF;IACA,QAAQD,QAAQ,CAACG,IAAI;MACnB,KAAK,YAAY;QACfH,QAAQ,CAACE,WAAW,GAAGE,UAAU,CAACF,WAAW,EAAED,aAAa,EAAEN,WAAW,CAAC;QAC1E;MACF,KAAK,SAAS;QACZU,WAAW,CAACH,WAAW,EAAED,aAAa,EAAEN,WAAW,CAAC;QACpD;MACF,KAAK,iBAAiB;QACpBU,WAAW,CAACH,WAAW,EAAED,aAAa,EAAEN,WAAW,CAAC;QACpD;MACF,KAAK,cAAc;QACjBR,MAAM,CAACW,IAAI,CAACI,WAAW,EAAE,UAAUI,KAAK,EAAEC,GAAG,EAAE;UAC7C,OAAOF,WAAW,CAACC,KAAK,EAAEL,aAAa,CAACM,GAAG,CAAC,EAAEZ,WAAW,CAAC;QAC5D,CAAC,CAAC;IACN;EACF,CAAC,CAAC;EACF;EACAD,cAAc,CAACD,YAAY,GAAG,KAAK;EACnC,OAAOC,cAAc;AACvB;AACA,SAASW,WAAWA,CAACC,KAAK,EAAEL,aAAa,EAAEN,WAAW,EAAE;EACtD,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACrCF,KAAK,CAACE,CAAC,CAAC,GAAGJ,UAAU,CAACE,KAAK,CAACE,CAAC,CAAC,EAAEP,aAAa,CAACO,CAAC,CAAC,EAAEb,WAAW,CAAC;EAChE;AACF;AACA,SAASS,UAAUA,CAACM,UAAU,EAAET,aAAa,EAAEN,WAAW,EAAE;EAC1D,IAAIgB,MAAM,GAAG,EAAE;EACf,IAAIC,KAAK,GAAGX,aAAa,CAAC,CAAC,CAAC;EAC5B,IAAIY,KAAK,GAAGZ,aAAa,CAAC,CAAC,CAAC;EAC5B,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,CAACD,MAAM,EAAEK,CAAC,IAAI,CAAC,EAAE;IAC7C,IAAIC,CAAC,GAAGL,UAAU,CAACM,UAAU,CAACF,CAAC,CAAC,GAAG,EAAE;IACrC,IAAIG,CAAC,GAAGP,UAAU,CAACM,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;IACzC;IACAC,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,EAAEA,CAAC,GAAG,CAAC,CAAC;IACrBE,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,EAAEA,CAAC,GAAG,CAAC,CAAC;IACrB;IACAF,CAAC,IAAIH,KAAK;IACVK,CAAC,IAAIJ,KAAK;IACVD,KAAK,GAAGG,CAAC;IACTF,KAAK,GAAGI,CAAC;IACT;IACAN,MAAM,CAACO,IAAI,CAAC,CAACH,CAAC,GAAGpB,WAAW,EAAEsB,CAAC,GAAGtB,WAAW,CAAC,CAAC;EACjD;EACA,OAAOgB,MAAM;AACf;AACA,eAAe,SAASQ,YAAYA,CAACC,OAAO,EAAEC,YAAY,EAAE;EAC1DD,OAAO,GAAG7B,MAAM,CAAC6B,OAAO,CAAC;EACzB,OAAOjC,MAAM,CAACmC,GAAG,CAACnC,MAAM,CAACoC,MAAM,CAACH,OAAO,CAACvB,QAAQ,EAAE,UAAU2B,UAAU,EAAE;IACtE;IACA,OAAOA,UAAU,CAACxB,QAAQ,IAAIwB,UAAU,CAACC,UAAU,IAAID,UAAU,CAACxB,QAAQ,CAACE,WAAW,CAACO,MAAM,GAAG,CAAC;EACnG,CAAC,CAAC,EAAE,UAAUe,UAAU,EAAE;IACxB,IAAIC,UAAU,GAAGD,UAAU,CAACC,UAAU;IACtC,IAAIC,GAAG,GAAGF,UAAU,CAACxB,QAAQ;IAC7B,IAAI2B,UAAU,GAAG,EAAE;IACnB,QAAQD,GAAG,CAACvB,IAAI;MACd,KAAK,SAAS;QACZ,IAAID,WAAW,GAAGwB,GAAG,CAACxB,WAAW;QACjC;QACA;QACAyB,UAAU,CAACT,IAAI,CAAC,IAAI7B,sBAAsB,CAACa,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC0B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACjF;MACF,KAAK,cAAc;QACjBzC,MAAM,CAACW,IAAI,CAAC4B,GAAG,CAACxB,WAAW,EAAE,UAAU2B,IAAI,EAAE;UAC3C,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE;YACXF,UAAU,CAACT,IAAI,CAAC,IAAI7B,sBAAsB,CAACwC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UACrE;QACF,CAAC,CAAC;QACF;MACF,KAAK,YAAY;QACfD,UAAU,CAACT,IAAI,CAAC,IAAI9B,yBAAyB,CAAC,CAACsC,GAAG,CAACxB,WAAW,CAAC,CAAC,CAAC;QACjE;MACF,KAAK,iBAAiB;QACpByB,UAAU,CAACT,IAAI,CAAC,IAAI9B,yBAAyB,CAACsC,GAAG,CAACxB,WAAW,CAAC,CAAC;IACnE;IACA,IAAI4B,MAAM,GAAG,IAAIxC,aAAa,CAACmC,UAAU,CAACJ,YAAY,IAAI,MAAM,CAAC,EAAEM,UAAU,EAAEF,UAAU,CAACM,EAAE,CAAC;IAC7FD,MAAM,CAACL,UAAU,GAAGA,UAAU;IAC9B,OAAOK,MAAM;EACf,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}