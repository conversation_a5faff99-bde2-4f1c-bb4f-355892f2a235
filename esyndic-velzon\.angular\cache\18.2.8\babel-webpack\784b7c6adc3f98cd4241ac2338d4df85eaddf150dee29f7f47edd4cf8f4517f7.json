{"ast": null, "code": "import smoothBezier from './smoothBezier.js';\nexport function buildPath(ctx, shape, closePath) {\n  var smooth = shape.smooth;\n  var points = shape.points;\n  if (points && points.length >= 2) {\n    if (smooth) {\n      var controlPoints = smoothBezier(points, smooth, closePath, shape.smoothConstraint);\n      ctx.moveTo(points[0][0], points[0][1]);\n      var len = points.length;\n      for (var i = 0; i < (closePath ? len : len - 1); i++) {\n        var cp1 = controlPoints[i * 2];\n        var cp2 = controlPoints[i * 2 + 1];\n        var p = points[(i + 1) % len];\n        ctx.bezierCurveTo(cp1[0], cp1[1], cp2[0], cp2[1], p[0], p[1]);\n      }\n    } else {\n      ctx.moveTo(points[0][0], points[0][1]);\n      for (var i = 1, l = points.length; i < l; i++) {\n        ctx.lineTo(points[i][0], points[i][1]);\n      }\n    }\n    closePath && ctx.closePath();\n  }\n}", "map": {"version": 3, "names": ["smoothBezier", "buildPath", "ctx", "shape", "closePath", "smooth", "points", "length", "controlPoints", "smoothConstraint", "moveTo", "len", "i", "cp1", "cp2", "p", "bezierCurveTo", "l", "lineTo"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/zrender/lib/graphic/helper/poly.js"], "sourcesContent": ["import smoothBezier from './smoothBezier.js';\nexport function buildPath(ctx, shape, closePath) {\n    var smooth = shape.smooth;\n    var points = shape.points;\n    if (points && points.length >= 2) {\n        if (smooth) {\n            var controlPoints = smoothBezier(points, smooth, closePath, shape.smoothConstraint);\n            ctx.moveTo(points[0][0], points[0][1]);\n            var len = points.length;\n            for (var i = 0; i < (closePath ? len : len - 1); i++) {\n                var cp1 = controlPoints[i * 2];\n                var cp2 = controlPoints[i * 2 + 1];\n                var p = points[(i + 1) % len];\n                ctx.bezierCurveTo(cp1[0], cp1[1], cp2[0], cp2[1], p[0], p[1]);\n            }\n        }\n        else {\n            ctx.moveTo(points[0][0], points[0][1]);\n            for (var i = 1, l = points.length; i < l; i++) {\n                ctx.lineTo(points[i][0], points[i][1]);\n            }\n        }\n        closePath && ctx.closePath();\n    }\n}\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,mBAAmB;AAC5C,OAAO,SAASC,SAASA,CAACC,GAAG,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC7C,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAM;EACzB,IAAIC,MAAM,GAAGH,KAAK,CAACG,MAAM;EACzB,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,IAAI,CAAC,EAAE;IAC9B,IAAIF,MAAM,EAAE;MACR,IAAIG,aAAa,GAAGR,YAAY,CAACM,MAAM,EAAED,MAAM,EAAED,SAAS,EAAED,KAAK,CAACM,gBAAgB,CAAC;MACnFP,GAAG,CAACQ,MAAM,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,IAAIK,GAAG,GAAGL,MAAM,CAACC,MAAM;MACvB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIR,SAAS,GAAGO,GAAG,GAAGA,GAAG,GAAG,CAAC,CAAC,EAAEC,CAAC,EAAE,EAAE;QAClD,IAAIC,GAAG,GAAGL,aAAa,CAACI,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAIE,GAAG,GAAGN,aAAa,CAACI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClC,IAAIG,CAAC,GAAGT,MAAM,CAAC,CAACM,CAAC,GAAG,CAAC,IAAID,GAAG,CAAC;QAC7BT,GAAG,CAACc,aAAa,CAACH,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;MACjE;IACJ,CAAC,MACI;MACDb,GAAG,CAACQ,MAAM,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEK,CAAC,GAAGX,MAAM,CAACC,MAAM,EAAEK,CAAC,GAAGK,CAAC,EAAEL,CAAC,EAAE,EAAE;QAC3CV,GAAG,CAACgB,MAAM,CAACZ,MAAM,CAACM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEN,MAAM,CAACM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C;IACJ;IACAR,SAAS,IAAIF,GAAG,CAACE,SAAS,CAAC,CAAC;EAChC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}