{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function timelinePreprocessor(option) {\n  var timelineOpt = option && option.timeline;\n  if (!zrUtil.isArray(timelineOpt)) {\n    timelineOpt = timelineOpt ? [timelineOpt] : [];\n  }\n  zrUtil.each(timelineOpt, function (opt) {\n    if (!opt) {\n      return;\n    }\n    compatibleEC2(opt);\n  });\n}\nfunction compatibleEC2(opt) {\n  var type = opt.type;\n  var ec2Types = {\n    'number': 'value',\n    'time': 'time'\n  };\n  // Compatible with ec2\n  if (ec2Types[type]) {\n    opt.axisType = ec2Types[type];\n    delete opt.type;\n  }\n  transferItem(opt);\n  if (has(opt, 'controlPosition')) {\n    var controlStyle = opt.controlStyle || (opt.controlStyle = {});\n    if (!has(controlStyle, 'position')) {\n      controlStyle.position = opt.controlPosition;\n    }\n    if (controlStyle.position === 'none' && !has(controlStyle, 'show')) {\n      controlStyle.show = false;\n      delete controlStyle.position;\n    }\n    delete opt.controlPosition;\n  }\n  zrUtil.each(opt.data || [], function (dataItem) {\n    if (zrUtil.isObject(dataItem) && !zrUtil.isArray(dataItem)) {\n      if (!has(dataItem, 'value') && has(dataItem, 'name')) {\n        // In ec2, using name as value.\n        dataItem.value = dataItem.name;\n      }\n      transferItem(dataItem);\n    }\n  });\n}\nfunction transferItem(opt) {\n  var itemStyle = opt.itemStyle || (opt.itemStyle = {});\n  var itemStyleEmphasis = itemStyle.emphasis || (itemStyle.emphasis = {});\n  // Transfer label out\n  var label = opt.label || opt.label || {};\n  var labelNormal = label.normal || (label.normal = {});\n  var excludeLabelAttr = {\n    normal: 1,\n    emphasis: 1\n  };\n  zrUtil.each(label, function (value, name) {\n    if (!excludeLabelAttr[name] && !has(labelNormal, name)) {\n      labelNormal[name] = value;\n    }\n  });\n  if (itemStyleEmphasis.label && !has(label, 'emphasis')) {\n    label.emphasis = itemStyleEmphasis.label;\n    delete itemStyleEmphasis.label;\n  }\n}\nfunction has(obj, attr) {\n  return obj.hasOwnProperty(attr);\n}", "map": {"version": 3, "names": ["zrUtil", "timelinePreprocessor", "option", "timelineOpt", "timeline", "isArray", "each", "opt", "compatibleEC2", "type", "ec2Types", "axisType", "transferItem", "has", "controlStyle", "position", "controlPosition", "show", "data", "dataItem", "isObject", "value", "name", "itemStyle", "itemStyleEmphasis", "emphasis", "label", "labelNormal", "normal", "excludeLabelAttr", "obj", "attr", "hasOwnProperty"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/component/timeline/preprocessor.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function timelinePreprocessor(option) {\n  var timelineOpt = option && option.timeline;\n  if (!zrUtil.isArray(timelineOpt)) {\n    timelineOpt = timelineOpt ? [timelineOpt] : [];\n  }\n  zrUtil.each(timelineOpt, function (opt) {\n    if (!opt) {\n      return;\n    }\n    compatibleEC2(opt);\n  });\n}\nfunction compatibleEC2(opt) {\n  var type = opt.type;\n  var ec2Types = {\n    'number': 'value',\n    'time': 'time'\n  };\n  // Compatible with ec2\n  if (ec2Types[type]) {\n    opt.axisType = ec2Types[type];\n    delete opt.type;\n  }\n  transferItem(opt);\n  if (has(opt, 'controlPosition')) {\n    var controlStyle = opt.controlStyle || (opt.controlStyle = {});\n    if (!has(controlStyle, 'position')) {\n      controlStyle.position = opt.controlPosition;\n    }\n    if (controlStyle.position === 'none' && !has(controlStyle, 'show')) {\n      controlStyle.show = false;\n      delete controlStyle.position;\n    }\n    delete opt.controlPosition;\n  }\n  zrUtil.each(opt.data || [], function (dataItem) {\n    if (zrUtil.isObject(dataItem) && !zrUtil.isArray(dataItem)) {\n      if (!has(dataItem, 'value') && has(dataItem, 'name')) {\n        // In ec2, using name as value.\n        dataItem.value = dataItem.name;\n      }\n      transferItem(dataItem);\n    }\n  });\n}\nfunction transferItem(opt) {\n  var itemStyle = opt.itemStyle || (opt.itemStyle = {});\n  var itemStyleEmphasis = itemStyle.emphasis || (itemStyle.emphasis = {});\n  // Transfer label out\n  var label = opt.label || opt.label || {};\n  var labelNormal = label.normal || (label.normal = {});\n  var excludeLabelAttr = {\n    normal: 1,\n    emphasis: 1\n  };\n  zrUtil.each(label, function (value, name) {\n    if (!excludeLabelAttr[name] && !has(labelNormal, name)) {\n      labelNormal[name] = value;\n    }\n  });\n  if (itemStyleEmphasis.label && !has(label, 'emphasis')) {\n    label.emphasis = itemStyleEmphasis.label;\n    delete itemStyleEmphasis.label;\n  }\n}\nfunction has(obj, attr) {\n  return obj.hasOwnProperty(attr);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,eAAe,SAASC,oBAAoBA,CAACC,MAAM,EAAE;EACnD,IAAIC,WAAW,GAAGD,MAAM,IAAIA,MAAM,CAACE,QAAQ;EAC3C,IAAI,CAACJ,MAAM,CAACK,OAAO,CAACF,WAAW,CAAC,EAAE;IAChCA,WAAW,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC,GAAG,EAAE;EAChD;EACAH,MAAM,CAACM,IAAI,CAACH,WAAW,EAAE,UAAUI,GAAG,EAAE;IACtC,IAAI,CAACA,GAAG,EAAE;MACR;IACF;IACAC,aAAa,CAACD,GAAG,CAAC;EACpB,CAAC,CAAC;AACJ;AACA,SAASC,aAAaA,CAACD,GAAG,EAAE;EAC1B,IAAIE,IAAI,GAAGF,GAAG,CAACE,IAAI;EACnB,IAAIC,QAAQ,GAAG;IACb,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE;EACV,CAAC;EACD;EACA,IAAIA,QAAQ,CAACD,IAAI,CAAC,EAAE;IAClBF,GAAG,CAACI,QAAQ,GAAGD,QAAQ,CAACD,IAAI,CAAC;IAC7B,OAAOF,GAAG,CAACE,IAAI;EACjB;EACAG,YAAY,CAACL,GAAG,CAAC;EACjB,IAAIM,GAAG,CAACN,GAAG,EAAE,iBAAiB,CAAC,EAAE;IAC/B,IAAIO,YAAY,GAAGP,GAAG,CAACO,YAAY,KAAKP,GAAG,CAACO,YAAY,GAAG,CAAC,CAAC,CAAC;IAC9D,IAAI,CAACD,GAAG,CAACC,YAAY,EAAE,UAAU,CAAC,EAAE;MAClCA,YAAY,CAACC,QAAQ,GAAGR,GAAG,CAACS,eAAe;IAC7C;IACA,IAAIF,YAAY,CAACC,QAAQ,KAAK,MAAM,IAAI,CAACF,GAAG,CAACC,YAAY,EAAE,MAAM,CAAC,EAAE;MAClEA,YAAY,CAACG,IAAI,GAAG,KAAK;MACzB,OAAOH,YAAY,CAACC,QAAQ;IAC9B;IACA,OAAOR,GAAG,CAACS,eAAe;EAC5B;EACAhB,MAAM,CAACM,IAAI,CAACC,GAAG,CAACW,IAAI,IAAI,EAAE,EAAE,UAAUC,QAAQ,EAAE;IAC9C,IAAInB,MAAM,CAACoB,QAAQ,CAACD,QAAQ,CAAC,IAAI,CAACnB,MAAM,CAACK,OAAO,CAACc,QAAQ,CAAC,EAAE;MAC1D,IAAI,CAACN,GAAG,CAACM,QAAQ,EAAE,OAAO,CAAC,IAAIN,GAAG,CAACM,QAAQ,EAAE,MAAM,CAAC,EAAE;QACpD;QACAA,QAAQ,CAACE,KAAK,GAAGF,QAAQ,CAACG,IAAI;MAChC;MACAV,YAAY,CAACO,QAAQ,CAAC;IACxB;EACF,CAAC,CAAC;AACJ;AACA,SAASP,YAAYA,CAACL,GAAG,EAAE;EACzB,IAAIgB,SAAS,GAAGhB,GAAG,CAACgB,SAAS,KAAKhB,GAAG,CAACgB,SAAS,GAAG,CAAC,CAAC,CAAC;EACrD,IAAIC,iBAAiB,GAAGD,SAAS,CAACE,QAAQ,KAAKF,SAAS,CAACE,QAAQ,GAAG,CAAC,CAAC,CAAC;EACvE;EACA,IAAIC,KAAK,GAAGnB,GAAG,CAACmB,KAAK,IAAInB,GAAG,CAACmB,KAAK,IAAI,CAAC,CAAC;EACxC,IAAIC,WAAW,GAAGD,KAAK,CAACE,MAAM,KAAKF,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC;EACrD,IAAIC,gBAAgB,GAAG;IACrBD,MAAM,EAAE,CAAC;IACTH,QAAQ,EAAE;EACZ,CAAC;EACDzB,MAAM,CAACM,IAAI,CAACoB,KAAK,EAAE,UAAUL,KAAK,EAAEC,IAAI,EAAE;IACxC,IAAI,CAACO,gBAAgB,CAACP,IAAI,CAAC,IAAI,CAACT,GAAG,CAACc,WAAW,EAAEL,IAAI,CAAC,EAAE;MACtDK,WAAW,CAACL,IAAI,CAAC,GAAGD,KAAK;IAC3B;EACF,CAAC,CAAC;EACF,IAAIG,iBAAiB,CAACE,KAAK,IAAI,CAACb,GAAG,CAACa,KAAK,EAAE,UAAU,CAAC,EAAE;IACtDA,KAAK,CAACD,QAAQ,GAAGD,iBAAiB,CAACE,KAAK;IACxC,OAAOF,iBAAiB,CAACE,KAAK;EAChC;AACF;AACA,SAASb,GAAGA,CAACiB,GAAG,EAAEC,IAAI,EAAE;EACtB,OAAOD,GAAG,CAACE,cAAc,CAACD,IAAI,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}