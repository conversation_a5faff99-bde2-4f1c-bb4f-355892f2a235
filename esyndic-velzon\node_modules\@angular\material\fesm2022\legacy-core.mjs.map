{"version": 3, "file": "legacy-core.mjs", "sources": ["../../../../../../src/material/legacy-core/option/optgroup.ts", "../../../../../../src/material/legacy-core/option/optgroup.html", "../../../../../../src/material/legacy-core/option/option.ts", "../../../../../../src/material/legacy-core/option/option.html", "../../../../../../src/material/legacy-core/option/index.ts", "../../../../../../src/material/legacy-core/legacy-core_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ChangeDetectionStrategy, Component, ViewEncapsulation} from '@angular/core';\nimport {MAT_OPTGROUP, _MatOptgroupBase} from '@angular/material/core';\n\n/**\n * Component that is used to group instances of `mat-option`.\n * @deprecated Use `MatOptgroup` from `@angular/material/core` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Component({\n  selector: 'mat-optgroup',\n  exportAs: 'matOptgroup',\n  templateUrl: 'optgroup.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  inputs: ['disabled'],\n  styleUrls: ['optgroup.css'],\n  host: {\n    'class': 'mat-optgroup',\n    '[attr.role]': '_inert ? null : \"group\"',\n    '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n    '[attr.aria-labelledby]': '_inert ? null : _labelId',\n    '[class.mat-optgroup-disabled]': 'disabled',\n  },\n  providers: [{provide: MAT_OPTGROUP, useExisting: MatLegacyOptgroup}],\n})\nexport class MatLegacyOptgroup extends _MatOptgroupBase {}\n", "<span class=\"mat-optgroup-label\" role=\"presentation\" [id]=\"_labelId\">{{ label }} <ng-content></ng-content></span>\n<ng-content select=\"mat-option, ng-container\"></ng-content>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  Inject,\n  Optional,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {\n  MAT_OPTION_PARENT_COMPONENT,\n  _MatOptionBase,\n  MatOptionParentComponent,\n  MAT_OPTGROUP,\n} from '@angular/material/core';\nimport {MatLegacyOptgroup} from './optgroup';\n\n/**\n * Single option inside of a `<mat-select>` element.\n * @deprecated Use `MatOption` from `@angular/material/core` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Component({\n  selector: 'mat-option',\n  exportAs: 'matOption',\n  host: {\n    'role': 'option',\n    '[attr.tabindex]': '_getTabIndex()',\n    '[class.mat-selected]': 'selected',\n    '[class.mat-option-multiple]': 'multiple',\n    '[class.mat-active]': 'active',\n    '[id]': 'id',\n    '[attr.aria-selected]': 'selected',\n    '[attr.aria-disabled]': 'disabled.toString()',\n    '[class.mat-option-disabled]': 'disabled',\n    '(click)': '_selectViaInteraction()',\n    '(keydown)': '_handleKeydown($event)',\n    'class': 'mat-option mat-focus-indicator',\n  },\n  styleUrls: ['option.css'],\n  templateUrl: 'option.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatLegacyOption<T = any> extends _MatOptionBase<T> {\n  constructor(\n    element: ElementRef<HTMLElement>,\n    changeDetectorRef: ChangeDetectorRef,\n    @Optional() @Inject(MAT_OPTION_PARENT_COMPONENT) parent: MatOptionParentComponent,\n    @Optional() @Inject(MAT_OPTGROUP) group: MatLegacyOptgroup,\n  ) {\n    super(element, changeDetectorRef, parent, group);\n  }\n}\n", "<mat-pseudo-checkbox *ngIf=\"multiple\" class=\"mat-option-pseudo-checkbox\"\n    [state]=\"selected ? 'checked' : 'unchecked'\" [disabled]=\"disabled\"></mat-pseudo-checkbox>\n\n<span class=\"mat-option-text\" #text><ng-content></ng-content></span>\n\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\n<span class=\"cdk-visually-hidden\" *ngIf=\"group && group._inert\">({{ group.label }})</span>\n\n<div class=\"mat-option-ripple\" mat-ripple\n     [matRippleTrigger]=\"_getHostElement()\"\n     [matRippleDisabled]=\"disabled || disableRipple\">\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CommonModule} from '@angular/common';\nimport {MatRippleModule, MatPseudoCheckboxModule, MatCommonModule} from '@angular/material/core';\nimport {MatLegacyOption} from './option';\nimport {MatLegacyOptgroup} from './optgroup';\n\n/**\n * @deprecated Use `MatOptionModule` from `@angular/material/core` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@NgModule({\n  imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule],\n  exports: [MatLegacyOption, MatLegacyOptgroup],\n  declarations: [MatLegacyOption, MatLegacyOptgroup],\n})\nexport class MatLegacyOptionModule {}\n\nexport * from './option';\nexport * from './optgroup';\n\nexport {\n  /**\n   * @deprecated Use `MAT_OPTGROUP` from `@angular/material/core` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n   * @breaking-change 17.0.0\n   */\n  MAT_OPTGROUP as MAT_LEGACY_OPTGROUP,\n\n  /**\n   * @deprecated Use `MatOptionSelectionChange` from `@angular/material/core` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n   * @breaking-change 17.0.0\n   */\n  MatOptionSelectionChange as MatLegacyOptionSelectionChange,\n\n  /**\n   * @deprecated Use `MatOptionParentComponent` from `@angular/material/core` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n   * @breaking-change 17.0.0\n   */\n  MatOptionParentComponent as MatLegacyOptionParentComponent,\n\n  /**\n   * @deprecated Use `MAT_OPTION_PARENT_COMPONENT` from `@angular/material/core` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n   * @breaking-change 17.0.0\n   */\n  MAT_OPTION_PARENT_COMPONENT as MAT_LEGACY_OPTION_PARENT_COMPONENT,\n\n  /**\n   * @deprecated Use `_countGroupLabelsBeforeOption` from `@angular/material/core` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n   * @breaking-change 17.0.0\n   */\n  _countGroupLabelsBeforeOption as _countGroupLabelsBeforeLegacyOption,\n\n  /**\n   * @deprecated Use `_getOptionScrollPosition` from `@angular/material/core` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n   * @breaking-change 17.0.0\n   */\n  _getOptionScrollPosition as _getLegacyOptionScrollPosition,\n\n  /**\n   * @deprecated Use `_MatOptionBase` from `@angular/material/core` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n   * @breaking-change 17.0.0\n   */\n  _MatOptionBase as _MatLegacyOptionBase,\n\n  /**\n   * @deprecated Use `_MatOptgroupBase` from `@angular/material/core` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n   * @breaking-change 17.0.0\n   */\n  _MatOptgroupBase as _MatLegacyOptgroupBase,\n} from '@angular/material/core';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;AAWA;;;;AAIG;AAkBG,MAAO,iBAAkB,SAAQ,gBAAgB,CAAA;8GAA1C,iBAAiB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAjB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,EAFjB,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,2BAAA,EAAA,oBAAA,EAAA,qCAAA,EAAA,sBAAA,EAAA,0BAAA,EAAA,6BAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,cAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAC,CAAC,4EC/BtE,0LAEA,EAAA,MAAA,EAAA,CAAA,shBAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FD+Ba,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAjB7B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EACd,QAAA,EAAA,aAAa,EAER,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,MAAA,EACvC,CAAC,UAAU,CAAC,EAEd,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,cAAc;AACvB,wBAAA,aAAa,EAAE,yBAAyB;AACxC,wBAAA,sBAAsB,EAAE,qCAAqC;AAC7D,wBAAA,wBAAwB,EAAE,0BAA0B;AACpD,wBAAA,+BAA+B,EAAE,UAAU;qBAC5C,EACU,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAmB,iBAAA,EAAC,CAAC,EAAA,QAAA,EAAA,0LAAA,EAAA,MAAA,EAAA,CAAA,shBAAA,CAAA,EAAA,CAAA;;;AENtE;;;;AAIG;AAuBG,MAAO,eAAyB,SAAQ,cAAiB,CAAA;AAC7D,IAAA,WAAA,CACE,OAAgC,EAChC,iBAAoC,EACa,MAAgC,EAC/C,KAAwB,EAAA;QAE1D,KAAK,CAAC,OAAO,EAAE,iBAAiB,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;KAClD;8GARU,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAIJ,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAC3B,YAAY,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AALvB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,wjBCpD5B,2kBAYA,EAAA,MAAA,EAAA,CAAA,klDAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,UAAA,EAAA,YAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDwCa,eAAe,EAAA,UAAA,EAAA,CAAA;kBAtB3B,SAAS;+BACE,YAAY,EAAA,QAAA,EACZ,WAAW,EACf,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,QAAQ;AAChB,wBAAA,iBAAiB,EAAE,gBAAgB;AACnC,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,6BAA6B,EAAE,UAAU;AACzC,wBAAA,oBAAoB,EAAE,QAAQ;AAC9B,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,sBAAsB,EAAE,qBAAqB;AAC7C,wBAAA,6BAA6B,EAAE,UAAU;AACzC,wBAAA,SAAS,EAAE,yBAAyB;AACpC,wBAAA,WAAW,EAAE,wBAAwB;AACrC,wBAAA,OAAO,EAAE,gCAAgC;AAC1C,qBAAA,EAAA,aAAA,EAGc,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,2kBAAA,EAAA,MAAA,EAAA,CAAA,klDAAA,CAAA,EAAA,CAAA;;0BAM5C,QAAQ;;0BAAI,MAAM;2BAAC,2BAA2B,CAAA;;0BAC9C,QAAQ;;0BAAI,MAAM;2BAAC,YAAY,CAAA;;;AE3CpC;;;AAGG;MAMU,qBAAqB,CAAA;8GAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAArB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,EAFjB,YAAA,EAAA,CAAA,eAAe,EAAE,iBAAiB,aAFvC,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,uBAAuB,CACvE,EAAA,OAAA,EAAA,CAAA,eAAe,EAAE,iBAAiB,CAAA,EAAA,CAAA,CAAA,EAAA;AAGjC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,YAJtB,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,uBAAuB,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAItE,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBALjC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,uBAAuB,CAAC;AAClF,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,iBAAiB,CAAC;AAC7C,oBAAA,YAAY,EAAE,CAAC,eAAe,EAAE,iBAAiB,CAAC;AACnD,iBAAA,CAAA;;;ACtBD;;AAEG;;;;"}