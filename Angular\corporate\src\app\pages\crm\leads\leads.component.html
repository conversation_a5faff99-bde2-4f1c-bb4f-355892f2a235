<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Leads" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
    <div class="col-lg-12">
        <div class="card" id="leadsList">
            <div class="card-header border-0">

                <div class="row g-4 align-items-center">
                    <div class="col-sm-3">
                        <div class="search-box">
                            <input type="text" name="searchTerm" class="form-control" placeholder="Search for..."
                            [(ngModel)]="searchTerm" (ngModelChange)="performSearch()">
                            <i class="ri-search-line search-icon"></i>
                        </div>
                    </div>
                    <div class="col-sm-auto ms-auto">
                        <div class="hstack gap-2">
                            <button class="btn btn-soft-danger" id="remove-actions" style="display: none"
                                (click)="deleteMultiple(deleteModel)"><i class="ri-delete-bin-2-line"></i></button>
                            <button type="button" class="btn btn-primary" data-bs-toggle="offcanvas"
                                href="javascript:void(0);" (click)="openEnd(filtetcontent)"><i
                                    class="ri-filter-3-line align-bottom me-1"></i> Fliters</button>
                            <button type="button" class="btn btn-secondary add-btn" data-bs-toggle="modal"
                                id="create-btn" data-bs-target="#showModal" (click)="openModal(content)"><i
                                    class="ri-add-line align-bottom me-1"></i> Add Leads</button>
                            <span class="dropdown" ngbDropdown>
                                <button class="btn btn-soft-info btn-icon fs-14 arrow-none d-block" type="button"
                                    id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false"
                                    ngbDropdownToggle>
                                    <i class="ri-settings-4-line"></i>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1" ngbDropdownMenu>
                                    <li><a class="dropdown-item" href="javascript:void(0);">Copy</a></li>
                                    <li><a class="dropdown-item" href="javascript:void(0);">Move to pipline</a></li>
                                    <li><a class="dropdown-item" href="javascript:void(0);">Add to exceptions</a></li>
                                    <li><a class="dropdown-item" href="javascript:void(0);">Switch to common form
                                            view</a></li>
                                    <li><a class="dropdown-item" href="javascript:void(0);">Reset form view to
                                            default</a></li>
                                </ul>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div>
                    <div class="table-responsive table-card">
                        <table class="table">
                            <thead>
                                <tr class="bg-light text-muted">
                                    <th scope="col" style="width: 50px;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkAll" value="option"
                                                [(ngModel)]="masterSelected" (change)="checkUncheckAll($event)">
                                        </div>
                                    </th>
                                    <th class="sort" (click)="onSort('name')">Name</th>
                                    <th class="sort" (click)="onSort('name')">Company</th>
                                    <th class="sort" (click)="onSort('score')">Leads Score</th>
                                    <th class="sort" (click)="onSort('phone')">Phone</th>
                                    <th class="sort" (click)="onSort('location')">Location</th>
                                    <th class="sort" (click)="onSort('tags')">Tags</th>
                                    <th class="sort" (click)="onSort('date')">Create Date</th>
                                    <th class="sort">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(data of leads;track $index){
                                <tr id="l_{{data._id}}">
                                    <th scope="row">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="checkAll"
                                                value="{{data._id}}" [(ngModel)]="data.state"
                                                (change)="onCheckboxChange($event)">
                                        </div>
                                    </th>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0">
                                                <img src="assets/images/users/{{data.image_src}}" alt=""
                                                    class="avatar-xxs rounded-circle image_src object-fit-cover">
                                            </div>
                                            <div class="flex-grow-1 ms-2 name">{{data.name}}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.company" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.score" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.phone" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.location" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td class="tags">
                                        <div class="d-flex gap-1">
                                            @for(tag of data.tags;track $index){
                                            <span class="badge bg-primary-subtle text-primary">{{tag}}</span>
                                            }
                                        </div>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.date | date :'longDate'"
                                            [term]="searchTerm"></ngb-highlight>
                                    </td>
                                    <td>
                                        <ul class="list-inline hstack gap-2 mb-0">
                                            <li class="list-inline-item edit" ngbTooltip="Call" placement="top">
                                                <a href="javascript:void(0);" class="text-muted d-inline-block">
                                                    <i class="ri-phone-line fs-16"></i>
                                                </a>
                                            </li>
                                            <li class="list-inline-item edit" ngbTooltip="Message" placement="top">
                                                <a href="javascript:void(0);" class="text-muted d-inline-block">
                                                    <i class="ri-question-answer-line fs-16"></i>
                                                </a>
                                            </li>
                                            <li class="list-inline-item" ngbTooltip="View" placement="top">
                                                <a href="javascript:void(0);"><i
                                                        class="ri-eye-fill align-bottom text-muted"></i></a>
                                            </li>
                                            <li class="list-inline-item" ngbTooltip="Edit" placement="top">
                                                <a class="edit-item-btn" data-bs-toggle="modal"
                                                    (click)="editDataGet($index,content)"><i
                                                        class="ri-pencil-fill align-bottom text-muted"></i></a>
                                            </li>
                                            <li class="list-inline-item me-0" ngbTooltip="Delete" placement="top">
                                                <a class="remove-item-btn" data-bs-toggle="modal"
                                                    (click)="confirm(deleteModel,data._id)">
                                                    <i class="ri-delete-bin-fill align-bottom text-muted"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </td>
                                </tr>
                            }
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-end mt-3">
                        <!-- Pagination -->
                        <ngb-pagination [collectionSize]="allleads?.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                        </ngb-pagination>
                        <!-- End Pagination -->
                    </div>
                    <div id="elmLoader">
                        <div class="spinner-border text-primary avatar-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <ng-template #content role="document" let-modal>
                    <div class="modal-header bg-light p-3">
                        <h5 class="modal-title" id="exampleModalLabel">Add Lead</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
                    </div>
                    <form (ngSubmit)="saveUser()" [formGroup]="leadsForm" class="tablelist-form" autocomplete="off">
                        <div class="modal-body">
                            <input type="hidden" name="id" formControlName="_id" />
                            <div class="row g-3">
                                <div class="col-lg-12">
                                    <div class="text-center">
                                        <div class="position-relative d-inline-block">
                                            <div class="position-absolute bottom-0 end-0">
                                                <label for="lead-image-input" class="mb-0" data-bs-toggle="tooltip"
                                                    data-bs-placement="right" title="Select Image">
                                                    <div class="avatar-xs cursor-pointer">
                                                        <div
                                                            class="avatar-title bg-light border rounded-circle text-muted">
                                                            <i class="ri-image-fill"></i>
                                                        </div>
                                                    </div>
                                                </label>
                                                <input class="form-control d-none" value="" id="lead-image-input"
                                                    type="file" accept="image/png, image/gif, image/jpeg"
                                                    (change)="fileChange($event)">
                                            </div>
                                            <div class="avatar-lg p-1">
                                                <div class="avatar-title bg-light rounded-circle">
                                                    <img src="assets/images/users/user-dummy-img.jpg" id="lead-img"
                                                        class="avatar-md rounded-circle object-fit-cover" />
                                                </div>
                                            </div>
                                        </div>
                                        <h5 class="fs-13 mt-3">Lead Image</h5>
                                    </div>
                                    <div>
                                        <label for="name-field" class="form-label">Name</label>
                                        <input type="text" id="customername-field" class="form-control"
                                            placeholder="Enter Name" required formControlName="name"
                                            [ngClass]="{ 'is-invalid': submitted && form['name'].errors }" />
                                        @if(submitted && form['name'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['name'].errors['required']){
                                            <div>Name is required</div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-12">
                                    <div>
                                        <label for="company_name-field" class="form-label">Company Name</label>
                                        <input type="text" id="company_name-field" class="form-control"
                                            placeholder="Enter company name" required formControlName="company"
                                            [ngClass]="{ 'is-invalid': submitted && form['company'].errors }" />
                                        @if(submitted && form['company'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['company'].errors['required']){
                                            <div>Company Name is required </div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-6">
                                    <div>
                                        <label for="leads_score-field" class="form-label">Leads Score</label>
                                        <input type="text" id="leads_score-field" class="form-control"
                                            placeholder="Enter lead score" required formControlName="score"
                                            [ngClass]="{ 'is-invalid': submitted && form['score'].errors }" />
                                        @if(submitted && form['score'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['score'].errors['required']){
                                            <div>Leads Score is required</div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-6">
                                    <div>
                                        <label for="phone-field" class="form-label">Phone</label>
                                        <input type="text" id="phone-field" class="form-control"
                                            placeholder="Enter phone no" required formControlName="phone"
                                            [ngClass]="{ 'is-invalid': submitted && form['phone'].errors }" />
                                        @if(submitted && form['phone'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['phone'].errors['required']){
                                            <div>Phone is required</div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-12">
                                    <div>
                                        <label for="location-field" class="form-label">Location</label>
                                        <input type="text" id="location-field" class="form-control"
                                            placeholder="Enter location" required formControlName="location"
                                            [ngClass]="{ 'is-invalid': submitted && form['location'].errors }" />
                                        @if(submitted && form['location'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['location'].errors['required']){
                                            <div>Location is required</div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-12">
                                    <div>
                                        <label for="taginput-choices" class="form-label">Tags</label>
                                        <ng-select [items]="selectValue" [multiple]="true" formControlName="tags"
                                            [ngClass]="{ 'is-invalid': submitted && form['tags'].errors }"></ng-select>
                                        @if(submitted && form['tags'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['tags'].errors['required']){
                                            <div>Tags is required</div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div>
                                        <label for="date-field" class="form-label">Created Date</label>
                                        <input class="form-control flatpickr-input" type="text"
                                            placeholder="Select Date" mwlFlatpickr [altInput]="true" [enableTime]="true"
                                            [convertModelValue]="true" [dateFormat]="'Y-m-d H:i'" formControlName="date"
                                            [ngClass]="{ 'is-invalid': submitted && form['date'].errors }">
                                        @if(submitted && form['date'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['date'].errors['required']){
                                            <div>Created Date is required</div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <!--end col-->
                            </div>
                            <!--end row-->
                        </div>
                        <div class="modal-footer">
                            <div class="hstack gap-2 justify-content-end">
                                <button type="button" class="btn btn-light" data-bs-dismiss="modal"
                                    (click)="modal.close('Close click')">Close</button>
                                <button type="submit" class="btn btn-primary" id="add-btn">Add leads</button>
                            </div>
                        </div>
                    </form>
                </ng-template>


                <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasExample"
                    aria-labelledby="offcanvasExampleLabel">
                    <div class="offcanvas-header bg-light">
                        <h5 class="offcanvas-title" id="offcanvasExampleLabel">Leads Fliters</h5>
                        <button type="button" class="btn-close text-body" data-bs-dismiss="offcanvas"
                            aria-label="Close"></button>
                    </div>
                    <!--end offcanvas-header-->
                    <form action="" class="d-flex flex-column justify-content-end h-100">
                        <div class="offcanvas-body">
                            <div class="mb-4">
                                <label for="datepicker-range"
                                    class="form-label text-muted text-uppercase fw-semibold mb-3">Date</label>
                                <input type="date" class="form-control" id="datepicker-range" data-provider="flatpickr"
                                    data-range="true" placeholder="Select date">
                            </div>
                            <div class="mb-4">
                                <label for="country-select"
                                    class="form-label text-muted text-uppercase fw-semibold mb-3">Country</label>
                                <select class="form-control" data-choices data-choices-multiple-remove="true"
                                    name="country-select" id="country-select" multiple>
                                    <option value="">Select country</option>
                                    <option value="Argentina">Argentina</option>
                                    <option value="Belgium">Belgium</option>
                                    <option value="Brazil" selected>Brazil</option>
                                    <option value="Colombia">Colombia</option>
                                    <option value="Denmark">Denmark</option>
                                    <option value="France">France</option>
                                    <option value="Germany">Germany</option>
                                    <option value="Mexico">Mexico</option>
                                    <option value="Russia">Russia</option>
                                    <option value="Spain">Spain</option>
                                    <option value="Syria">Syria</option>
                                    <option value="United Kingdom" selected>United Kingdom</option>
                                    <option value="United States of America">United States of America</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label for="status-select"
                                    class="form-label text-muted text-uppercase fw-semibold mb-3">Status</label>
                                <div class="row g-2">
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="inlineCheckbox1"
                                                value="option1">
                                            <label class="form-check-label" for="inlineCheckbox1">New Leads</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="inlineCheckbox2"
                                                value="option2">
                                            <label class="form-check-label" for="inlineCheckbox2">Old Leads</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="inlineCheckbox3"
                                                value="option3">
                                            <label class="form-check-label" for="inlineCheckbox3">Loss Leads</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="inlineCheckbox4"
                                                value="option4">
                                            <label class="form-check-label" for="inlineCheckbox4">Follow Up</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label for="leadscore"
                                    class="form-label text-muted text-uppercase fw-semibold mb-3">Lead Score</label>
                                <div class="row g-2 align-items-center">
                                    <div class="col-lg">
                                        <input type="number" class="form-control" id="leadscore" placeholder="0">
                                    </div>
                                    <div class="col-lg-auto">
                                        To
                                    </div>
                                    <div class="col-lg">
                                        <input type="number" class="form-control" id="leadscore" placeholder="0">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label for="leads-tags"
                                    class="form-label text-muted text-uppercase fw-semibold mb-3">Tags</label>
                                <div class="row g-3">
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="marketing"
                                                value="marketing">
                                            <label class="form-check-label" for="marketing">Marketing</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="management"
                                                value="management">
                                            <label class="form-check-label" for="management">Management</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="business"
                                                value="business">
                                            <label class="form-check-label" for="business">Business</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="investing"
                                                value="investing">
                                            <label class="form-check-label" for="investing">Investing</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="partner"
                                                value="partner">
                                            <label class="form-check-label" for="partner">Partner</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="lead" value="lead">
                                            <label class="form-check-label" for="lead">Leads</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="sale" value="sale">
                                            <label class="form-check-label" for="sale">Sale</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="owner" value="owner">
                                            <label class="form-check-label" for="owner">Owner</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="banking"
                                                value="banking">
                                            <label class="form-check-label" for="banking">Banking</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="banking"
                                                value="banking">
                                            <label class="form-check-label" for="banking">Exiting</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="banking"
                                                value="banking">
                                            <label class="form-check-label" for="banking">Finance</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="banking"
                                                value="banking">
                                            <label class="form-check-label" for="banking">Fashion</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--end offcanvas-body-->
                        <div class="offcanvas-footer border-top p-3 text-center hstack gap-2">
                            <button class="btn btn-light w-100">Clear Filter</button>
                            <button type="submit" class="btn btn-primary w-100">Filters</button>
                        </div>
                        <!--end offcanvas-footer-->
                    </form>
                </div>
                <!--end offcanvas-->

            </div>
        </div>

    </div>
    <!--end col-->
</div>
<!--end row-->

<!-- removeItemModal -->
<ng-template #deleteModel let-modal>
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="btn-close"
                (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body">
            <div class="mt-2 text-center">
                <lord-icon src="https://cdn.lordicon.com/gsqxdxog.json" trigger="loop"
                    colors="primary:#405189,secondary:#f06548" style="width:90px;height:90px"></lord-icon>
                <div class="mt-4 pt-2 fs-15 mx-4 mx-sm-5">
                    <h4>You are about to delete a contact ?</h4>
                    <p class="text-muted mx-4 mb-0">Deleting your contact will remove all of your information from our
                        database.</p>
                </div>
            </div>
            <div class="d-flex gap-2 justify-content-center mt-4 mb-2">
                <button class="btn btn-link link-success fw-medium text-decoration-none" data-bs-dismiss="modal"
                    id="deleteRecord-close" (click)="modal.close('Close click')"><i
                        class="ri-close-line me-1 align-middle"></i> Close</button>
                <button type="button" class="btn w-sm btn-danger " id="delete-product" (click)="deleteData(deleteId)"
                    (click)="modal.close('Close click')">Yes, Delete It!</button>
            </div>
        </div>
    </div><!-- /.modal-content -->
</ng-template>

<!-- Right Sidebar Filter Section  -->
<ng-template #filtetcontent let-offcanvas>
    <div class="offcanvas-header bg-light">
        <h5 class="offcanvas-title" id="offcanvasExampleLabel">Leads Fliters</h5>
        <button type="button" class="btn-close text-body" data-bs-dismiss="offcanvas" aria-label="Close"
            (click)="offcanvas.dismiss('Cross click')"></button>
    </div>
    <!--end offcanvas-header-->
    <form action="" class="d-flex flex-column justify-content-end h-100">
        <div class="offcanvas-body">
            <div class="mb-4">
                <label for="datepicker-range" class="form-label text-muted text-uppercase fw-semibold mb-3">Date</label>
                <input class="form-control flatpickr-input" type="text" placeholder="Select Date" mwlFlatpickr
                    [altInput]="true" id="isDate">
            </div>
            <div class="mb-4">
                <label for="country-select"
                    class="form-label text-muted text-uppercase fw-semibold mb-3">Country</label>
                <select class="form-control" data-choices data-choices-multiple-remove="true" name="country-select"
                    id="country-select">
                    <option value="" selected>Select country</option>
                    <option value="Argentina">Argentina</option>
                    <option value="Belgium">Belgium</option>
                    <option value="Brazil">Brazil</option>
                    <option value="Colombia">Colombia</option>
                    <option value="Denmark">Denmark</option>
                    <option value="France">France</option>
                    <option value="Germany">Germany</option>
                    <option value="Mexico">Mexico</option>
                    <option value="Russia">Russia</option>
                    <option value="Spain">Spain</option>
                    <option value="Syria">Syria</option>
                    <option value="United Kingdom">United Kingdom</option>
                    <option value="United States of America">United States of America</option>
                </select>
            </div>
            <div class="mb-4">
                <label for="status-select" class="form-label text-muted text-uppercase fw-semibold mb-3">Status</label>
                <div class="row g-2">
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="inlineCheckbox1" value="option1">
                            <label class="form-check-label" for="inlineCheckbox1">New Leads</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="inlineCheckbox2" value="option2">
                            <label class="form-check-label" for="inlineCheckbox2">Old Leads</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="inlineCheckbox3" value="option3">
                            <label class="form-check-label" for="inlineCheckbox3">Loss Leads</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="inlineCheckbox4" value="option4">
                            <label class="form-check-label" for="inlineCheckbox4">Follow Up</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mb-4">
                <label for="leadscore" class="form-label text-muted text-uppercase fw-semibold mb-3">Lead Score</label>
                <div class="row g-2 align-items-center">
                    <div class="col-lg">
                        <input type="number" class="form-control" id="leadscore" placeholder="0">
                    </div>
                    <div class="col-lg-auto">
                        To
                    </div>
                    <div class="col-lg">
                        <input type="number" class="form-control" id="leadscore" placeholder="0">
                    </div>
                </div>
            </div>
            <div>
                <label for="leads-tags" class="form-label text-muted text-uppercase fw-semibold mb-3">Tags</label>
                <div class="row g-3">
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="marketing" value="marketing"
                                name="checkAll">
                            <label class="form-check-label" for="marketing">Marketing</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="management" value="management"
                                name="checkAll">
                            <label class="form-check-label" for="management">Management</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="business" value="business"
                                name="checkAll">
                            <label class="form-check-label" for="business">Business</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="investing" value="investing"
                                name="checkAll">
                            <label class="form-check-label" for="investing">Investing</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="partner" value="partner"
                                name="checkAll">
                            <label class="form-check-label" for="partner">Partner</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="lead" value="lead" name="checkAll">
                            <label class="form-check-label" for="lead">Leads</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="sale" value="sale" name="checkAll">
                            <label class="form-check-label" for="sale">Sale</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="owner" value="owner" name="checkAll">
                            <label class="form-check-label" for="owner">Owner</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="banking" value="banking"
                                name="checkAll">
                            <label class="form-check-label" for="banking">Banking</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="banking" value="banking"
                                name="checkAll">
                            <label class="form-check-label" for="banking">Exiting</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="banking" value="banking"
                                name="checkAll">
                            <label class="form-check-label" for="banking">Finance</label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="banking" value="banking"
                                name="checkAll">
                            <label class="form-check-label" for="banking">Fashion</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end offcanvas-body-->
        <div class="offcanvas-footer border-top p-3 text-center hstack gap-2">
            <button class="btn btn-light w-100">Clear Filter</button>
            <button type="submit" class="btn btn-success w-100" (click)="SearchData();">Filters</button>
        </div>
        <!--end offcanvas-footer-->
    </form>
</ng-template>
<!--end offcanvas-->