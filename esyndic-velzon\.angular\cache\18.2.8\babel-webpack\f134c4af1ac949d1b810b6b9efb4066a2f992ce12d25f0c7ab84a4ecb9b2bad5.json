{"ast": null, "code": "export const GlobalComponent = {\n  // e-Syndic Backend API\n  API_URL: 'http://localhost:8080/api/',\n  headerToken: {\n    'Authorization': `Bearer ${sessionStorage.getItem('token')}`\n  },\n  // Auth Api\n  AUTH_API: \"http://localhost:8080/api/auth/\",\n  // e-Syndic APIs\n  // Buildings Api\n  building: 'buildings',\n  buildingDelete: 'buildings/',\n  // Apartments Api\n  apartment: 'apartments',\n  apartmentDelete: 'apartments/',\n  // Users Api\n  user: 'users',\n  userDelete: 'users/',\n  // Meetings Api\n  meeting: 'meetings',\n  meetingDelete: 'meetings/',\n  // Incidents Api\n  incident: 'incidents',\n  incidentDelete: 'incidents/',\n  // Payments Api\n  payment: 'payments',\n  paymentDelete: 'payments/',\n  // Syndics Api\n  syndic: 'syndics',\n  syndicDelete: 'syndics/',\n  // Legacy endpoints for compatibility\n  product: 'products',\n  productDelete: 'products/',\n  order: 'orders',\n  orderId: 'orders/',\n  customer: 'customers'\n};", "map": {"version": 3, "names": ["GlobalComponent", "API_URL", "headerToken", "sessionStorage", "getItem", "AUTH_API", "building", "buildingDelete", "apartment", "apartmentDelete", "user", "userDelete", "meeting", "meetingDelete", "incident", "incidentDelete", "payment", "paymentDelete", "syndic", "syndicDelete", "product", "productDelete", "order", "orderId", "customer"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\global-component.ts"], "sourcesContent": ["export const GlobalComponent = {\r\n    // e-Syndic Backend API\r\n    API_URL: 'http://localhost:8080/api/',\r\n    headerToken: { 'Authorization': `Bearer ${sessionStorage.getItem('token')}` },\r\n\r\n    // Auth Api\r\n    AUTH_API: \"http://localhost:8080/api/auth/\",\r\n\r\n    // e-Syndic APIs\r\n    // Buildings Api\r\n    building: 'buildings',\r\n    buildingDelete: 'buildings/',\r\n\r\n    // Apartments Api\r\n    apartment: 'apartments',\r\n    apartmentDelete: 'apartments/',\r\n\r\n    // Users Api\r\n    user: 'users',\r\n    userDelete: 'users/',\r\n\r\n    // Meetings Api\r\n    meeting: 'meetings',\r\n    meetingDelete: 'meetings/',\r\n\r\n    // Incidents Api\r\n    incident: 'incidents',\r\n    incidentDelete: 'incidents/',\r\n\r\n    // Payments Api\r\n    payment: 'payments',\r\n    paymentDelete: 'payments/',\r\n\r\n    // Syndics Api\r\n    syndic: 'syndics',\r\n    syndicDelete: 'syndics/',\r\n\r\n    // Legacy endpoints for compatibility\r\n    product: 'products',\r\n    productDelete: 'products/',\r\n    order: 'orders',\r\n    orderId: 'orders/',\r\n    customer: 'customers',\r\n\r\n}"], "mappings": "AAAA,OAAO,MAAMA,eAAe,GAAG;EAC3B;EACAC,OAAO,EAAE,4BAA4B;EACrCC,WAAW,EAAE;IAAE,eAAe,EAAE,UAAUC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;EAAE,CAAE;EAE7E;EACAC,QAAQ,EAAE,iCAAiC;EAE3C;EACA;EACAC,QAAQ,EAAE,WAAW;EACrBC,cAAc,EAAE,YAAY;EAE5B;EACAC,SAAS,EAAE,YAAY;EACvBC,eAAe,EAAE,aAAa;EAE9B;EACAC,IAAI,EAAE,OAAO;EACbC,UAAU,EAAE,QAAQ;EAEpB;EACAC,OAAO,EAAE,UAAU;EACnBC,aAAa,EAAE,WAAW;EAE1B;EACAC,QAAQ,EAAE,WAAW;EACrBC,cAAc,EAAE,YAAY;EAE5B;EACAC,OAAO,EAAE,UAAU;EACnBC,aAAa,EAAE,WAAW;EAE1B;EACAC,MAAM,EAAE,SAAS;EACjBC,YAAY,EAAE,UAAU;EAExB;EACAC,OAAO,EAAE,UAAU;EACnBC,aAAa,EAAE,WAAW;EAC1BC,KAAK,EAAE,QAAQ;EACfC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE;CAEb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}