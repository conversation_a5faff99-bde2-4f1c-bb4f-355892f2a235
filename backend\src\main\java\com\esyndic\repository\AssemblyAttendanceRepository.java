package com.esyndic.repository;

import com.esyndic.entity.AssemblyAttendance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface AssemblyAttendanceRepository extends JpaRepository<AssemblyAttendance, UUID> {

    List<AssemblyAttendance> findByAssemblyId(UUID assemblyId);

    List<AssemblyAttendance> findByUserId(UUID userId);

    List<AssemblyAttendance> findByApartmentId(UUID apartmentId);

    Optional<AssemblyAttendance> findByAssemblyIdAndUserId(UUID assemblyId, UUID userId);

    List<AssemblyAttendance> findByAssemblyIdAndAttended(UUID assemblyId, <PERSON><PERSON><PERSON> attended);

    @Query("SELECT COUNT(aa) FROM AssemblyAttendance aa WHERE aa.assembly.id = :assemblyId AND aa.attended = true")
    long countAttendeesByAssemblyId(@Param("assemblyId") UUID assemblyId);

    @Query("SELECT COUNT(aa) FROM AssemblyAttendance aa WHERE aa.assembly.id = :assemblyId")
    long countTotalInviteesByAssemblyId(@Param("assemblyId") UUID assemblyId);

    @Query("SELECT aa FROM AssemblyAttendance aa WHERE aa.assembly.building.id = :buildingId")
    List<AssemblyAttendance> findByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT aa FROM AssemblyAttendance aa WHERE aa.user.id = :userId AND aa.attended = true")
    List<AssemblyAttendance> findAttendedAssembliesByUserId(@Param("userId") UUID userId);

    @Query("SELECT aa FROM AssemblyAttendance aa WHERE aa.user.id = :userId AND aa.attended = false")
    List<AssemblyAttendance> findMissedAssembliesByUserId(@Param("userId") UUID userId);

    @Query("SELECT (COUNT(aa) * 100.0 / (SELECT COUNT(aa2) FROM AssemblyAttendance aa2 WHERE aa2.assembly.id = :assemblyId)) " +
           "FROM AssemblyAttendance aa WHERE aa.assembly.id = :assemblyId AND aa.attended = true")
    Double getAttendancePercentageByAssemblyId(@Param("assemblyId") UUID assemblyId);

    boolean existsByAssemblyIdAndUserId(UUID assemblyId, UUID userId);
}
