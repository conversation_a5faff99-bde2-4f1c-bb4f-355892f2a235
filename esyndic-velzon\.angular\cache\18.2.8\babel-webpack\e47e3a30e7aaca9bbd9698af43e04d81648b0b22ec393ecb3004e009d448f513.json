{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\n/**\n * Provide effect for line\n */\nimport * as graphic from '../../util/graphic.js';\nimport Line from './Line.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport * as curveUtil from 'zrender/lib/core/curve.js';\nvar EffectLine = /** @class */function (_super) {\n  __extends(EffectLine, _super);\n  function EffectLine(lineData, idx, seriesScope) {\n    var _this = _super.call(this) || this;\n    _this.add(_this.createLine(lineData, idx, seriesScope));\n    _this._updateEffectSymbol(lineData, idx);\n    return _this;\n  }\n  EffectLine.prototype.createLine = function (lineData, idx, seriesScope) {\n    return new Line(lineData, idx, seriesScope);\n  };\n  EffectLine.prototype._updateEffectSymbol = function (lineData, idx) {\n    var itemModel = lineData.getItemModel(idx);\n    var effectModel = itemModel.getModel('effect');\n    var size = effectModel.get('symbolSize');\n    var symbolType = effectModel.get('symbol');\n    if (!zrUtil.isArray(size)) {\n      size = [size, size];\n    }\n    var lineStyle = lineData.getItemVisual(idx, 'style');\n    var color = effectModel.get('color') || lineStyle && lineStyle.stroke;\n    var symbol = this.childAt(1);\n    if (this._symbolType !== symbolType) {\n      // Remove previous\n      this.remove(symbol);\n      symbol = createSymbol(symbolType, -0.5, -0.5, 1, 1, color);\n      symbol.z2 = 100;\n      symbol.culling = true;\n      this.add(symbol);\n    }\n    // Symbol may be removed if loop is false\n    if (!symbol) {\n      return;\n    }\n    // Shadow color is same with color in default\n    symbol.setStyle('shadowColor', color);\n    symbol.setStyle(effectModel.getItemStyle(['color']));\n    symbol.scaleX = size[0];\n    symbol.scaleY = size[1];\n    symbol.setColor(color);\n    this._symbolType = symbolType;\n    this._symbolScale = size;\n    this._updateEffectAnimation(lineData, effectModel, idx);\n  };\n  EffectLine.prototype._updateEffectAnimation = function (lineData, effectModel, idx) {\n    var symbol = this.childAt(1);\n    if (!symbol) {\n      return;\n    }\n    var points = lineData.getItemLayout(idx);\n    var period = effectModel.get('period') * 1000;\n    var loop = effectModel.get('loop');\n    var roundTrip = effectModel.get('roundTrip');\n    var constantSpeed = effectModel.get('constantSpeed');\n    var delayExpr = zrUtil.retrieve(effectModel.get('delay'), function (idx) {\n      return idx / lineData.count() * period / 3;\n    });\n    // Ignore when updating\n    symbol.ignore = true;\n    this._updateAnimationPoints(symbol, points);\n    if (constantSpeed > 0) {\n      period = this._getLineLength(symbol) / constantSpeed * 1000;\n    }\n    if (period !== this._period || loop !== this._loop || roundTrip !== this._roundTrip) {\n      symbol.stopAnimation();\n      var delayNum = void 0;\n      if (zrUtil.isFunction(delayExpr)) {\n        delayNum = delayExpr(idx);\n      } else {\n        delayNum = delayExpr;\n      }\n      if (symbol.__t > 0) {\n        delayNum = -period * symbol.__t;\n      }\n      this._animateSymbol(symbol, period, delayNum, loop, roundTrip);\n    }\n    this._period = period;\n    this._loop = loop;\n    this._roundTrip = roundTrip;\n  };\n  EffectLine.prototype._animateSymbol = function (symbol, period, delayNum, loop, roundTrip) {\n    if (period > 0) {\n      symbol.__t = 0;\n      var self_1 = this;\n      var animator = symbol.animate('', loop).when(roundTrip ? period * 2 : period, {\n        __t: roundTrip ? 2 : 1\n      }).delay(delayNum).during(function () {\n        self_1._updateSymbolPosition(symbol);\n      });\n      if (!loop) {\n        animator.done(function () {\n          self_1.remove(symbol);\n        });\n      }\n      animator.start();\n    }\n  };\n  EffectLine.prototype._getLineLength = function (symbol) {\n    // Not so accurate\n    return vec2.dist(symbol.__p1, symbol.__cp1) + vec2.dist(symbol.__cp1, symbol.__p2);\n  };\n  EffectLine.prototype._updateAnimationPoints = function (symbol, points) {\n    symbol.__p1 = points[0];\n    symbol.__p2 = points[1];\n    symbol.__cp1 = points[2] || [(points[0][0] + points[1][0]) / 2, (points[0][1] + points[1][1]) / 2];\n  };\n  EffectLine.prototype.updateData = function (lineData, idx, seriesScope) {\n    this.childAt(0).updateData(lineData, idx, seriesScope);\n    this._updateEffectSymbol(lineData, idx);\n  };\n  EffectLine.prototype._updateSymbolPosition = function (symbol) {\n    var p1 = symbol.__p1;\n    var p2 = symbol.__p2;\n    var cp1 = symbol.__cp1;\n    var t = symbol.__t < 1 ? symbol.__t : 2 - symbol.__t;\n    var pos = [symbol.x, symbol.y];\n    var lastPos = pos.slice();\n    var quadraticAt = curveUtil.quadraticAt;\n    var quadraticDerivativeAt = curveUtil.quadraticDerivativeAt;\n    pos[0] = quadraticAt(p1[0], cp1[0], p2[0], t);\n    pos[1] = quadraticAt(p1[1], cp1[1], p2[1], t);\n    // Tangent\n    var tx = symbol.__t < 1 ? quadraticDerivativeAt(p1[0], cp1[0], p2[0], t) : quadraticDerivativeAt(p2[0], cp1[0], p1[0], 1 - t);\n    var ty = symbol.__t < 1 ? quadraticDerivativeAt(p1[1], cp1[1], p2[1], t) : quadraticDerivativeAt(p2[1], cp1[1], p1[1], 1 - t);\n    symbol.rotation = -Math.atan2(ty, tx) - Math.PI / 2;\n    // enable continuity trail for 'line', 'rect', 'roundRect' symbolType\n    if (this._symbolType === 'line' || this._symbolType === 'rect' || this._symbolType === 'roundRect') {\n      if (symbol.__lastT !== undefined && symbol.__lastT < symbol.__t) {\n        symbol.scaleY = vec2.dist(lastPos, pos) * 1.05;\n        // make sure the last segment render within endPoint\n        if (t === 1) {\n          pos[0] = lastPos[0] + (pos[0] - lastPos[0]) / 2;\n          pos[1] = lastPos[1] + (pos[1] - lastPos[1]) / 2;\n        }\n      } else if (symbol.__lastT === 1) {\n        // After first loop, symbol.__t does NOT start with 0, so connect p1 to pos directly.\n        symbol.scaleY = 2 * vec2.dist(p1, pos);\n      } else {\n        symbol.scaleY = this._symbolScale[1];\n      }\n    }\n    symbol.__lastT = symbol.__t;\n    symbol.ignore = false;\n    symbol.x = pos[0];\n    symbol.y = pos[1];\n  };\n  EffectLine.prototype.updateLayout = function (lineData, idx) {\n    this.childAt(0).updateLayout(lineData, idx);\n    var effectModel = lineData.getItemModel(idx).getModel('effect');\n    this._updateEffectAnimation(lineData, effectModel, idx);\n  };\n  return EffectLine;\n}(graphic.Group);\nexport default EffectLine;", "map": {"version": 3, "names": ["__extends", "graphic", "Line", "zrUtil", "createSymbol", "vec2", "curveUtil", "EffectLine", "_super", "lineData", "idx", "seriesScope", "_this", "call", "add", "createLine", "_updateEffectSymbol", "prototype", "itemModel", "getItemModel", "effectModel", "getModel", "size", "get", "symbolType", "isArray", "lineStyle", "getItemVisual", "color", "stroke", "symbol", "childAt", "_symbolType", "remove", "z2", "culling", "setStyle", "getItemStyle", "scaleX", "scaleY", "setColor", "_symbolScale", "_updateEffectAnimation", "points", "getItemLayout", "period", "loop", "roundTrip", "constantSpeed", "delayExpr", "retrieve", "count", "ignore", "_updateAnimationPoints", "_getLine<PERSON>ength", "_period", "_loop", "_roundTrip", "stopAnimation", "delayNum", "isFunction", "__t", "_animateSymbol", "self_1", "animator", "animate", "when", "delay", "during", "_updateSymbolPosition", "done", "start", "dist", "__p1", "__cp1", "__p2", "updateData", "p1", "p2", "cp1", "t", "pos", "x", "y", "lastPos", "slice", "quadraticAt", "quadraticDerivativeAt", "tx", "ty", "rotation", "Math", "atan2", "PI", "__lastT", "undefined", "updateLayout", "Group"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/chart/helper/EffectLine.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\n/**\n * Provide effect for line\n */\nimport * as graphic from '../../util/graphic.js';\nimport Line from './Line.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport * as curveUtil from 'zrender/lib/core/curve.js';\nvar EffectLine = /** @class */function (_super) {\n  __extends(EffectLine, _super);\n  function EffectLine(lineData, idx, seriesScope) {\n    var _this = _super.call(this) || this;\n    _this.add(_this.createLine(lineData, idx, seriesScope));\n    _this._updateEffectSymbol(lineData, idx);\n    return _this;\n  }\n  EffectLine.prototype.createLine = function (lineData, idx, seriesScope) {\n    return new Line(lineData, idx, seriesScope);\n  };\n  EffectLine.prototype._updateEffectSymbol = function (lineData, idx) {\n    var itemModel = lineData.getItemModel(idx);\n    var effectModel = itemModel.getModel('effect');\n    var size = effectModel.get('symbolSize');\n    var symbolType = effectModel.get('symbol');\n    if (!zrUtil.isArray(size)) {\n      size = [size, size];\n    }\n    var lineStyle = lineData.getItemVisual(idx, 'style');\n    var color = effectModel.get('color') || lineStyle && lineStyle.stroke;\n    var symbol = this.childAt(1);\n    if (this._symbolType !== symbolType) {\n      // Remove previous\n      this.remove(symbol);\n      symbol = createSymbol(symbolType, -0.5, -0.5, 1, 1, color);\n      symbol.z2 = 100;\n      symbol.culling = true;\n      this.add(symbol);\n    }\n    // Symbol may be removed if loop is false\n    if (!symbol) {\n      return;\n    }\n    // Shadow color is same with color in default\n    symbol.setStyle('shadowColor', color);\n    symbol.setStyle(effectModel.getItemStyle(['color']));\n    symbol.scaleX = size[0];\n    symbol.scaleY = size[1];\n    symbol.setColor(color);\n    this._symbolType = symbolType;\n    this._symbolScale = size;\n    this._updateEffectAnimation(lineData, effectModel, idx);\n  };\n  EffectLine.prototype._updateEffectAnimation = function (lineData, effectModel, idx) {\n    var symbol = this.childAt(1);\n    if (!symbol) {\n      return;\n    }\n    var points = lineData.getItemLayout(idx);\n    var period = effectModel.get('period') * 1000;\n    var loop = effectModel.get('loop');\n    var roundTrip = effectModel.get('roundTrip');\n    var constantSpeed = effectModel.get('constantSpeed');\n    var delayExpr = zrUtil.retrieve(effectModel.get('delay'), function (idx) {\n      return idx / lineData.count() * period / 3;\n    });\n    // Ignore when updating\n    symbol.ignore = true;\n    this._updateAnimationPoints(symbol, points);\n    if (constantSpeed > 0) {\n      period = this._getLineLength(symbol) / constantSpeed * 1000;\n    }\n    if (period !== this._period || loop !== this._loop || roundTrip !== this._roundTrip) {\n      symbol.stopAnimation();\n      var delayNum = void 0;\n      if (zrUtil.isFunction(delayExpr)) {\n        delayNum = delayExpr(idx);\n      } else {\n        delayNum = delayExpr;\n      }\n      if (symbol.__t > 0) {\n        delayNum = -period * symbol.__t;\n      }\n      this._animateSymbol(symbol, period, delayNum, loop, roundTrip);\n    }\n    this._period = period;\n    this._loop = loop;\n    this._roundTrip = roundTrip;\n  };\n  EffectLine.prototype._animateSymbol = function (symbol, period, delayNum, loop, roundTrip) {\n    if (period > 0) {\n      symbol.__t = 0;\n      var self_1 = this;\n      var animator = symbol.animate('', loop).when(roundTrip ? period * 2 : period, {\n        __t: roundTrip ? 2 : 1\n      }).delay(delayNum).during(function () {\n        self_1._updateSymbolPosition(symbol);\n      });\n      if (!loop) {\n        animator.done(function () {\n          self_1.remove(symbol);\n        });\n      }\n      animator.start();\n    }\n  };\n  EffectLine.prototype._getLineLength = function (symbol) {\n    // Not so accurate\n    return vec2.dist(symbol.__p1, symbol.__cp1) + vec2.dist(symbol.__cp1, symbol.__p2);\n  };\n  EffectLine.prototype._updateAnimationPoints = function (symbol, points) {\n    symbol.__p1 = points[0];\n    symbol.__p2 = points[1];\n    symbol.__cp1 = points[2] || [(points[0][0] + points[1][0]) / 2, (points[0][1] + points[1][1]) / 2];\n  };\n  EffectLine.prototype.updateData = function (lineData, idx, seriesScope) {\n    this.childAt(0).updateData(lineData, idx, seriesScope);\n    this._updateEffectSymbol(lineData, idx);\n  };\n  EffectLine.prototype._updateSymbolPosition = function (symbol) {\n    var p1 = symbol.__p1;\n    var p2 = symbol.__p2;\n    var cp1 = symbol.__cp1;\n    var t = symbol.__t < 1 ? symbol.__t : 2 - symbol.__t;\n    var pos = [symbol.x, symbol.y];\n    var lastPos = pos.slice();\n    var quadraticAt = curveUtil.quadraticAt;\n    var quadraticDerivativeAt = curveUtil.quadraticDerivativeAt;\n    pos[0] = quadraticAt(p1[0], cp1[0], p2[0], t);\n    pos[1] = quadraticAt(p1[1], cp1[1], p2[1], t);\n    // Tangent\n    var tx = symbol.__t < 1 ? quadraticDerivativeAt(p1[0], cp1[0], p2[0], t) : quadraticDerivativeAt(p2[0], cp1[0], p1[0], 1 - t);\n    var ty = symbol.__t < 1 ? quadraticDerivativeAt(p1[1], cp1[1], p2[1], t) : quadraticDerivativeAt(p2[1], cp1[1], p1[1], 1 - t);\n    symbol.rotation = -Math.atan2(ty, tx) - Math.PI / 2;\n    // enable continuity trail for 'line', 'rect', 'roundRect' symbolType\n    if (this._symbolType === 'line' || this._symbolType === 'rect' || this._symbolType === 'roundRect') {\n      if (symbol.__lastT !== undefined && symbol.__lastT < symbol.__t) {\n        symbol.scaleY = vec2.dist(lastPos, pos) * 1.05;\n        // make sure the last segment render within endPoint\n        if (t === 1) {\n          pos[0] = lastPos[0] + (pos[0] - lastPos[0]) / 2;\n          pos[1] = lastPos[1] + (pos[1] - lastPos[1]) / 2;\n        }\n      } else if (symbol.__lastT === 1) {\n        // After first loop, symbol.__t does NOT start with 0, so connect p1 to pos directly.\n        symbol.scaleY = 2 * vec2.dist(p1, pos);\n      } else {\n        symbol.scaleY = this._symbolScale[1];\n      }\n    }\n    symbol.__lastT = symbol.__t;\n    symbol.ignore = false;\n    symbol.x = pos[0];\n    symbol.y = pos[1];\n  };\n  EffectLine.prototype.updateLayout = function (lineData, idx) {\n    this.childAt(0).updateLayout(lineData, idx);\n    var effectModel = lineData.getItemModel(idx).getModel('effect');\n    this._updateEffectAnimation(lineData, effectModel, idx);\n  };\n  return EffectLine;\n}(graphic.Group);\nexport default EffectLine;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA;AACA;AACA,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,YAAY,QAAQ,sBAAsB;AACnD,OAAO,KAAKC,IAAI,MAAM,4BAA4B;AAClD,OAAO,KAAKC,SAAS,MAAM,2BAA2B;AACtD,IAAIC,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9CR,SAAS,CAACO,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAACE,QAAQ,EAAEC,GAAG,EAAEC,WAAW,EAAE;IAC9C,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,GAAG,CAACF,KAAK,CAACG,UAAU,CAACN,QAAQ,EAAEC,GAAG,EAAEC,WAAW,CAAC,CAAC;IACvDC,KAAK,CAACI,mBAAmB,CAACP,QAAQ,EAAEC,GAAG,CAAC;IACxC,OAAOE,KAAK;EACd;EACAL,UAAU,CAACU,SAAS,CAACF,UAAU,GAAG,UAAUN,QAAQ,EAAEC,GAAG,EAAEC,WAAW,EAAE;IACtE,OAAO,IAAIT,IAAI,CAACO,QAAQ,EAAEC,GAAG,EAAEC,WAAW,CAAC;EAC7C,CAAC;EACDJ,UAAU,CAACU,SAAS,CAACD,mBAAmB,GAAG,UAAUP,QAAQ,EAAEC,GAAG,EAAE;IAClE,IAAIQ,SAAS,GAAGT,QAAQ,CAACU,YAAY,CAACT,GAAG,CAAC;IAC1C,IAAIU,WAAW,GAAGF,SAAS,CAACG,QAAQ,CAAC,QAAQ,CAAC;IAC9C,IAAIC,IAAI,GAAGF,WAAW,CAACG,GAAG,CAAC,YAAY,CAAC;IACxC,IAAIC,UAAU,GAAGJ,WAAW,CAACG,GAAG,CAAC,QAAQ,CAAC;IAC1C,IAAI,CAACpB,MAAM,CAACsB,OAAO,CAACH,IAAI,CAAC,EAAE;MACzBA,IAAI,GAAG,CAACA,IAAI,EAAEA,IAAI,CAAC;IACrB;IACA,IAAII,SAAS,GAAGjB,QAAQ,CAACkB,aAAa,CAACjB,GAAG,EAAE,OAAO,CAAC;IACpD,IAAIkB,KAAK,GAAGR,WAAW,CAACG,GAAG,CAAC,OAAO,CAAC,IAAIG,SAAS,IAAIA,SAAS,CAACG,MAAM;IACrE,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;IAC5B,IAAI,IAAI,CAACC,WAAW,KAAKR,UAAU,EAAE;MACnC;MACA,IAAI,CAACS,MAAM,CAACH,MAAM,CAAC;MACnBA,MAAM,GAAG1B,YAAY,CAACoB,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEI,KAAK,CAAC;MAC1DE,MAAM,CAACI,EAAE,GAAG,GAAG;MACfJ,MAAM,CAACK,OAAO,GAAG,IAAI;MACrB,IAAI,CAACrB,GAAG,CAACgB,MAAM,CAAC;IAClB;IACA;IACA,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IACA;IACAA,MAAM,CAACM,QAAQ,CAAC,aAAa,EAAER,KAAK,CAAC;IACrCE,MAAM,CAACM,QAAQ,CAAChB,WAAW,CAACiB,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IACpDP,MAAM,CAACQ,MAAM,GAAGhB,IAAI,CAAC,CAAC,CAAC;IACvBQ,MAAM,CAACS,MAAM,GAAGjB,IAAI,CAAC,CAAC,CAAC;IACvBQ,MAAM,CAACU,QAAQ,CAACZ,KAAK,CAAC;IACtB,IAAI,CAACI,WAAW,GAAGR,UAAU;IAC7B,IAAI,CAACiB,YAAY,GAAGnB,IAAI;IACxB,IAAI,CAACoB,sBAAsB,CAACjC,QAAQ,EAAEW,WAAW,EAAEV,GAAG,CAAC;EACzD,CAAC;EACDH,UAAU,CAACU,SAAS,CAACyB,sBAAsB,GAAG,UAAUjC,QAAQ,EAAEW,WAAW,EAAEV,GAAG,EAAE;IAClF,IAAIoB,MAAM,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACD,MAAM,EAAE;MACX;IACF;IACA,IAAIa,MAAM,GAAGlC,QAAQ,CAACmC,aAAa,CAAClC,GAAG,CAAC;IACxC,IAAImC,MAAM,GAAGzB,WAAW,CAACG,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI;IAC7C,IAAIuB,IAAI,GAAG1B,WAAW,CAACG,GAAG,CAAC,MAAM,CAAC;IAClC,IAAIwB,SAAS,GAAG3B,WAAW,CAACG,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAIyB,aAAa,GAAG5B,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC;IACpD,IAAI0B,SAAS,GAAG9C,MAAM,CAAC+C,QAAQ,CAAC9B,WAAW,CAACG,GAAG,CAAC,OAAO,CAAC,EAAE,UAAUb,GAAG,EAAE;MACvE,OAAOA,GAAG,GAAGD,QAAQ,CAAC0C,KAAK,CAAC,CAAC,GAAGN,MAAM,GAAG,CAAC;IAC5C,CAAC,CAAC;IACF;IACAf,MAAM,CAACsB,MAAM,GAAG,IAAI;IACpB,IAAI,CAACC,sBAAsB,CAACvB,MAAM,EAAEa,MAAM,CAAC;IAC3C,IAAIK,aAAa,GAAG,CAAC,EAAE;MACrBH,MAAM,GAAG,IAAI,CAACS,cAAc,CAACxB,MAAM,CAAC,GAAGkB,aAAa,GAAG,IAAI;IAC7D;IACA,IAAIH,MAAM,KAAK,IAAI,CAACU,OAAO,IAAIT,IAAI,KAAK,IAAI,CAACU,KAAK,IAAIT,SAAS,KAAK,IAAI,CAACU,UAAU,EAAE;MACnF3B,MAAM,CAAC4B,aAAa,CAAC,CAAC;MACtB,IAAIC,QAAQ,GAAG,KAAK,CAAC;MACrB,IAAIxD,MAAM,CAACyD,UAAU,CAACX,SAAS,CAAC,EAAE;QAChCU,QAAQ,GAAGV,SAAS,CAACvC,GAAG,CAAC;MAC3B,CAAC,MAAM;QACLiD,QAAQ,GAAGV,SAAS;MACtB;MACA,IAAInB,MAAM,CAAC+B,GAAG,GAAG,CAAC,EAAE;QAClBF,QAAQ,GAAG,CAACd,MAAM,GAAGf,MAAM,CAAC+B,GAAG;MACjC;MACA,IAAI,CAACC,cAAc,CAAChC,MAAM,EAAEe,MAAM,EAAEc,QAAQ,EAAEb,IAAI,EAAEC,SAAS,CAAC;IAChE;IACA,IAAI,CAACQ,OAAO,GAAGV,MAAM;IACrB,IAAI,CAACW,KAAK,GAAGV,IAAI;IACjB,IAAI,CAACW,UAAU,GAAGV,SAAS;EAC7B,CAAC;EACDxC,UAAU,CAACU,SAAS,CAAC6C,cAAc,GAAG,UAAUhC,MAAM,EAAEe,MAAM,EAAEc,QAAQ,EAAEb,IAAI,EAAEC,SAAS,EAAE;IACzF,IAAIF,MAAM,GAAG,CAAC,EAAE;MACdf,MAAM,CAAC+B,GAAG,GAAG,CAAC;MACd,IAAIE,MAAM,GAAG,IAAI;MACjB,IAAIC,QAAQ,GAAGlC,MAAM,CAACmC,OAAO,CAAC,EAAE,EAAEnB,IAAI,CAAC,CAACoB,IAAI,CAACnB,SAAS,GAAGF,MAAM,GAAG,CAAC,GAAGA,MAAM,EAAE;QAC5EgB,GAAG,EAAEd,SAAS,GAAG,CAAC,GAAG;MACvB,CAAC,CAAC,CAACoB,KAAK,CAACR,QAAQ,CAAC,CAACS,MAAM,CAAC,YAAY;QACpCL,MAAM,CAACM,qBAAqB,CAACvC,MAAM,CAAC;MACtC,CAAC,CAAC;MACF,IAAI,CAACgB,IAAI,EAAE;QACTkB,QAAQ,CAACM,IAAI,CAAC,YAAY;UACxBP,MAAM,CAAC9B,MAAM,CAACH,MAAM,CAAC;QACvB,CAAC,CAAC;MACJ;MACAkC,QAAQ,CAACO,KAAK,CAAC,CAAC;IAClB;EACF,CAAC;EACDhE,UAAU,CAACU,SAAS,CAACqC,cAAc,GAAG,UAAUxB,MAAM,EAAE;IACtD;IACA,OAAOzB,IAAI,CAACmE,IAAI,CAAC1C,MAAM,CAAC2C,IAAI,EAAE3C,MAAM,CAAC4C,KAAK,CAAC,GAAGrE,IAAI,CAACmE,IAAI,CAAC1C,MAAM,CAAC4C,KAAK,EAAE5C,MAAM,CAAC6C,IAAI,CAAC;EACpF,CAAC;EACDpE,UAAU,CAACU,SAAS,CAACoC,sBAAsB,GAAG,UAAUvB,MAAM,EAAEa,MAAM,EAAE;IACtEb,MAAM,CAAC2C,IAAI,GAAG9B,MAAM,CAAC,CAAC,CAAC;IACvBb,MAAM,CAAC6C,IAAI,GAAGhC,MAAM,CAAC,CAAC,CAAC;IACvBb,MAAM,CAAC4C,KAAK,GAAG/B,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACpG,CAAC;EACDpC,UAAU,CAACU,SAAS,CAAC2D,UAAU,GAAG,UAAUnE,QAAQ,EAAEC,GAAG,EAAEC,WAAW,EAAE;IACtE,IAAI,CAACoB,OAAO,CAAC,CAAC,CAAC,CAAC6C,UAAU,CAACnE,QAAQ,EAAEC,GAAG,EAAEC,WAAW,CAAC;IACtD,IAAI,CAACK,mBAAmB,CAACP,QAAQ,EAAEC,GAAG,CAAC;EACzC,CAAC;EACDH,UAAU,CAACU,SAAS,CAACoD,qBAAqB,GAAG,UAAUvC,MAAM,EAAE;IAC7D,IAAI+C,EAAE,GAAG/C,MAAM,CAAC2C,IAAI;IACpB,IAAIK,EAAE,GAAGhD,MAAM,CAAC6C,IAAI;IACpB,IAAII,GAAG,GAAGjD,MAAM,CAAC4C,KAAK;IACtB,IAAIM,CAAC,GAAGlD,MAAM,CAAC+B,GAAG,GAAG,CAAC,GAAG/B,MAAM,CAAC+B,GAAG,GAAG,CAAC,GAAG/B,MAAM,CAAC+B,GAAG;IACpD,IAAIoB,GAAG,GAAG,CAACnD,MAAM,CAACoD,CAAC,EAAEpD,MAAM,CAACqD,CAAC,CAAC;IAC9B,IAAIC,OAAO,GAAGH,GAAG,CAACI,KAAK,CAAC,CAAC;IACzB,IAAIC,WAAW,GAAGhF,SAAS,CAACgF,WAAW;IACvC,IAAIC,qBAAqB,GAAGjF,SAAS,CAACiF,qBAAqB;IAC3DN,GAAG,CAAC,CAAC,CAAC,GAAGK,WAAW,CAACT,EAAE,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,CAAC,EAAEE,CAAC,CAAC;IAC7CC,GAAG,CAAC,CAAC,CAAC,GAAGK,WAAW,CAACT,EAAE,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,CAAC,EAAEE,CAAC,CAAC;IAC7C;IACA,IAAIQ,EAAE,GAAG1D,MAAM,CAAC+B,GAAG,GAAG,CAAC,GAAG0B,qBAAqB,CAACV,EAAE,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,CAAC,EAAEE,CAAC,CAAC,GAAGO,qBAAqB,CAACT,EAAE,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC,EAAEF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGG,CAAC,CAAC;IAC7H,IAAIS,EAAE,GAAG3D,MAAM,CAAC+B,GAAG,GAAG,CAAC,GAAG0B,qBAAqB,CAACV,EAAE,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,CAAC,EAAEE,CAAC,CAAC,GAAGO,qBAAqB,CAACT,EAAE,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC,EAAEF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGG,CAAC,CAAC;IAC7HlD,MAAM,CAAC4D,QAAQ,GAAG,CAACC,IAAI,CAACC,KAAK,CAACH,EAAE,EAAED,EAAE,CAAC,GAAGG,IAAI,CAACE,EAAE,GAAG,CAAC;IACnD;IACA,IAAI,IAAI,CAAC7D,WAAW,KAAK,MAAM,IAAI,IAAI,CAACA,WAAW,KAAK,MAAM,IAAI,IAAI,CAACA,WAAW,KAAK,WAAW,EAAE;MAClG,IAAIF,MAAM,CAACgE,OAAO,KAAKC,SAAS,IAAIjE,MAAM,CAACgE,OAAO,GAAGhE,MAAM,CAAC+B,GAAG,EAAE;QAC/D/B,MAAM,CAACS,MAAM,GAAGlC,IAAI,CAACmE,IAAI,CAACY,OAAO,EAAEH,GAAG,CAAC,GAAG,IAAI;QAC9C;QACA,IAAID,CAAC,KAAK,CAAC,EAAE;UACXC,GAAG,CAAC,CAAC,CAAC,GAAGG,OAAO,CAAC,CAAC,CAAC,GAAG,CAACH,GAAG,CAAC,CAAC,CAAC,GAAGG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;UAC/CH,GAAG,CAAC,CAAC,CAAC,GAAGG,OAAO,CAAC,CAAC,CAAC,GAAG,CAACH,GAAG,CAAC,CAAC,CAAC,GAAGG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;QACjD;MACF,CAAC,MAAM,IAAItD,MAAM,CAACgE,OAAO,KAAK,CAAC,EAAE;QAC/B;QACAhE,MAAM,CAACS,MAAM,GAAG,CAAC,GAAGlC,IAAI,CAACmE,IAAI,CAACK,EAAE,EAAEI,GAAG,CAAC;MACxC,CAAC,MAAM;QACLnD,MAAM,CAACS,MAAM,GAAG,IAAI,CAACE,YAAY,CAAC,CAAC,CAAC;MACtC;IACF;IACAX,MAAM,CAACgE,OAAO,GAAGhE,MAAM,CAAC+B,GAAG;IAC3B/B,MAAM,CAACsB,MAAM,GAAG,KAAK;IACrBtB,MAAM,CAACoD,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC;IACjBnD,MAAM,CAACqD,CAAC,GAAGF,GAAG,CAAC,CAAC,CAAC;EACnB,CAAC;EACD1E,UAAU,CAACU,SAAS,CAAC+E,YAAY,GAAG,UAAUvF,QAAQ,EAAEC,GAAG,EAAE;IAC3D,IAAI,CAACqB,OAAO,CAAC,CAAC,CAAC,CAACiE,YAAY,CAACvF,QAAQ,EAAEC,GAAG,CAAC;IAC3C,IAAIU,WAAW,GAAGX,QAAQ,CAACU,YAAY,CAACT,GAAG,CAAC,CAACW,QAAQ,CAAC,QAAQ,CAAC;IAC/D,IAAI,CAACqB,sBAAsB,CAACjC,QAAQ,EAAEW,WAAW,EAAEV,GAAG,CAAC;EACzD,CAAC;EACD,OAAOH,UAAU;AACnB,CAAC,CAACN,OAAO,CAACgG,KAAK,CAAC;AAChB,eAAe1F,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}