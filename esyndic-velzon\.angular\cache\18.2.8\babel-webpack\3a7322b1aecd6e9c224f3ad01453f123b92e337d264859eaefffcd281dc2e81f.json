{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { separateMorph, combineMorph, morphPath, isCombineMorphing } from 'zrender/lib/tool/morphPath.js';\nimport { Path } from '../util/graphic.js';\nimport { defaults, isArray } from 'zrender/lib/core/util.js';\nimport { getAnimationConfig } from './basicTransition.js';\nimport { clonePath } from 'zrender/lib/tool/path.js';\nfunction isMultiple(elements) {\n  return isArray(elements[0]);\n}\nfunction prepareMorphBatches(one, many) {\n  var batches = [];\n  var batchCount = one.length;\n  for (var i = 0; i < batchCount; i++) {\n    batches.push({\n      one: one[i],\n      many: []\n    });\n  }\n  for (var i = 0; i < many.length; i++) {\n    var len = many[i].length;\n    var k = void 0;\n    for (k = 0; k < len; k++) {\n      batches[k % batchCount].many.push(many[i][k]);\n    }\n  }\n  var off = 0;\n  // If one has more paths than each one of many. average them.\n  for (var i = batchCount - 1; i >= 0; i--) {\n    if (!batches[i].many.length) {\n      var moveFrom = batches[off].many;\n      if (moveFrom.length <= 1) {\n        // Not enough\n        // Start from the first one.\n        if (off) {\n          off = 0;\n        } else {\n          return batches;\n        }\n      }\n      var len = moveFrom.length;\n      var mid = Math.ceil(len / 2);\n      batches[i].many = moveFrom.slice(mid, len);\n      batches[off].many = moveFrom.slice(0, mid);\n      off++;\n    }\n  }\n  return batches;\n}\nvar pathDividers = {\n  clone: function (params) {\n    var ret = [];\n    // Fitting the alpha\n    var approxOpacity = 1 - Math.pow(1 - params.path.style.opacity, 1 / params.count);\n    for (var i = 0; i < params.count; i++) {\n      var cloned = clonePath(params.path);\n      cloned.setStyle('opacity', approxOpacity);\n      ret.push(cloned);\n    }\n    return ret;\n  },\n  // Use the default divider\n  split: null\n};\nexport function applyMorphAnimation(from, to, divideShape, seriesModel, dataIndex, animateOtherProps) {\n  if (!from.length || !to.length) {\n    return;\n  }\n  var updateAnimationCfg = getAnimationConfig('update', seriesModel, dataIndex);\n  if (!(updateAnimationCfg && updateAnimationCfg.duration > 0)) {\n    return;\n  }\n  var animationDelay = seriesModel.getModel('universalTransition').get('delay');\n  var animationCfg = Object.assign({\n    // Need to setToFinal so the further calculation based on the style can be correct.\n    // Like emphasis color.\n    setToFinal: true\n  }, updateAnimationCfg);\n  var many;\n  var one;\n  if (isMultiple(from)) {\n    // manyToOne\n    many = from;\n    one = to;\n  }\n  if (isMultiple(to)) {\n    // oneToMany\n    many = to;\n    one = from;\n  }\n  function morphOneBatch(batch, fromIsMany, animateIndex, animateCount, forceManyOne) {\n    var batchMany = batch.many;\n    var batchOne = batch.one;\n    if (batchMany.length === 1 && !forceManyOne) {\n      // Is one to one\n      var batchFrom = fromIsMany ? batchMany[0] : batchOne;\n      var batchTo = fromIsMany ? batchOne : batchMany[0];\n      if (isCombineMorphing(batchFrom)) {\n        // Keep doing combine animation.\n        morphOneBatch({\n          many: [batchFrom],\n          one: batchTo\n        }, true, animateIndex, animateCount, true);\n      } else {\n        var individualAnimationCfg = animationDelay ? defaults({\n          delay: animationDelay(animateIndex, animateCount)\n        }, animationCfg) : animationCfg;\n        morphPath(batchFrom, batchTo, individualAnimationCfg);\n        animateOtherProps(batchFrom, batchTo, batchFrom, batchTo, individualAnimationCfg);\n      }\n    } else {\n      var separateAnimationCfg = defaults({\n        dividePath: pathDividers[divideShape],\n        individualDelay: animationDelay && function (idx, count, fromPath, toPath) {\n          return animationDelay(idx + animateIndex, animateCount);\n        }\n      }, animationCfg);\n      var _a = fromIsMany ? combineMorph(batchMany, batchOne, separateAnimationCfg) : separateMorph(batchOne, batchMany, separateAnimationCfg),\n        fromIndividuals = _a.fromIndividuals,\n        toIndividuals = _a.toIndividuals;\n      var count = fromIndividuals.length;\n      for (var k = 0; k < count; k++) {\n        var individualAnimationCfg = animationDelay ? defaults({\n          delay: animationDelay(k, count)\n        }, animationCfg) : animationCfg;\n        animateOtherProps(fromIndividuals[k], toIndividuals[k], fromIsMany ? batchMany[k] : batch.one, fromIsMany ? batch.one : batchMany[k], individualAnimationCfg);\n      }\n    }\n  }\n  var fromIsMany = many ? many === from\n  // Is one to one. If the path number not match. also needs do merge and separate morphing.\n  : from.length > to.length;\n  var morphBatches = many ? prepareMorphBatches(one, many) : prepareMorphBatches(fromIsMany ? to : from, [fromIsMany ? from : to]);\n  var animateCount = 0;\n  for (var i = 0; i < morphBatches.length; i++) {\n    animateCount += morphBatches[i].many.length;\n  }\n  var animateIndex = 0;\n  for (var i = 0; i < morphBatches.length; i++) {\n    morphOneBatch(morphBatches[i], fromIsMany, animateIndex, animateCount);\n    animateIndex += morphBatches[i].many.length;\n  }\n}\nexport function getPathList(elements) {\n  if (!elements) {\n    return [];\n  }\n  if (isArray(elements)) {\n    var pathList_1 = [];\n    for (var i = 0; i < elements.length; i++) {\n      pathList_1.push(getPathList(elements[i]));\n    }\n    return pathList_1;\n  }\n  var pathList = [];\n  elements.traverse(function (el) {\n    if (el instanceof Path && !el.disableMorphing && !el.invisible && !el.ignore) {\n      pathList.push(el);\n    }\n  });\n  return pathList;\n}", "map": {"version": 3, "names": ["separateMorph", "combineMorph", "morph<PERSON>ath", "isCombineMorphing", "Path", "defaults", "isArray", "getAnimationConfig", "<PERSON><PERSON><PERSON>", "isMultiple", "elements", "prepareMorphBatches", "one", "many", "batches", "batchCount", "length", "i", "push", "len", "k", "off", "moveFrom", "mid", "Math", "ceil", "slice", "pathDividers", "clone", "params", "ret", "approxOpacity", "pow", "path", "style", "opacity", "count", "cloned", "setStyle", "split", "applyMorphAnimation", "from", "to", "divideShape", "seriesModel", "dataIndex", "animateOtherProps", "updateAnimationCfg", "duration", "animationDelay", "getModel", "get", "animationCfg", "Object", "assign", "setToFinal", "morphOneBatch", "batch", "fromIsMany", "animateIndex", "animateCount", "forceManyOne", "batchMany", "batchOne", "batchFrom", "batchTo", "individualAnimationCfg", "delay", "separateAnimationCfg", "dividePath", "individualDelay", "idx", "fromPath", "to<PERSON><PERSON>", "_a", "fromIndividuals", "toIndividuals", "morphBatches", "getPathList", "pathList_1", "pathList", "traverse", "el", "disableMorphing", "invisible", "ignore"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/animation/morphTransitionHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { separateMorph, combineMorph, morphPath, isCombineMorphing } from 'zrender/lib/tool/morphPath.js';\nimport { Path } from '../util/graphic.js';\nimport { defaults, isArray } from 'zrender/lib/core/util.js';\nimport { getAnimationConfig } from './basicTransition.js';\nimport { clonePath } from 'zrender/lib/tool/path.js';\nfunction isMultiple(elements) {\n  return isArray(elements[0]);\n}\nfunction prepareMorphBatches(one, many) {\n  var batches = [];\n  var batchCount = one.length;\n  for (var i = 0; i < batchCount; i++) {\n    batches.push({\n      one: one[i],\n      many: []\n    });\n  }\n  for (var i = 0; i < many.length; i++) {\n    var len = many[i].length;\n    var k = void 0;\n    for (k = 0; k < len; k++) {\n      batches[k % batchCount].many.push(many[i][k]);\n    }\n  }\n  var off = 0;\n  // If one has more paths than each one of many. average them.\n  for (var i = batchCount - 1; i >= 0; i--) {\n    if (!batches[i].many.length) {\n      var moveFrom = batches[off].many;\n      if (moveFrom.length <= 1) {\n        // Not enough\n        // Start from the first one.\n        if (off) {\n          off = 0;\n        } else {\n          return batches;\n        }\n      }\n      var len = moveFrom.length;\n      var mid = Math.ceil(len / 2);\n      batches[i].many = moveFrom.slice(mid, len);\n      batches[off].many = moveFrom.slice(0, mid);\n      off++;\n    }\n  }\n  return batches;\n}\nvar pathDividers = {\n  clone: function (params) {\n    var ret = [];\n    // Fitting the alpha\n    var approxOpacity = 1 - Math.pow(1 - params.path.style.opacity, 1 / params.count);\n    for (var i = 0; i < params.count; i++) {\n      var cloned = clonePath(params.path);\n      cloned.setStyle('opacity', approxOpacity);\n      ret.push(cloned);\n    }\n    return ret;\n  },\n  // Use the default divider\n  split: null\n};\nexport function applyMorphAnimation(from, to, divideShape, seriesModel, dataIndex, animateOtherProps) {\n  if (!from.length || !to.length) {\n    return;\n  }\n  var updateAnimationCfg = getAnimationConfig('update', seriesModel, dataIndex);\n  if (!(updateAnimationCfg && updateAnimationCfg.duration > 0)) {\n    return;\n  }\n  var animationDelay = seriesModel.getModel('universalTransition').get('delay');\n  var animationCfg = Object.assign({\n    // Need to setToFinal so the further calculation based on the style can be correct.\n    // Like emphasis color.\n    setToFinal: true\n  }, updateAnimationCfg);\n  var many;\n  var one;\n  if (isMultiple(from)) {\n    // manyToOne\n    many = from;\n    one = to;\n  }\n  if (isMultiple(to)) {\n    // oneToMany\n    many = to;\n    one = from;\n  }\n  function morphOneBatch(batch, fromIsMany, animateIndex, animateCount, forceManyOne) {\n    var batchMany = batch.many;\n    var batchOne = batch.one;\n    if (batchMany.length === 1 && !forceManyOne) {\n      // Is one to one\n      var batchFrom = fromIsMany ? batchMany[0] : batchOne;\n      var batchTo = fromIsMany ? batchOne : batchMany[0];\n      if (isCombineMorphing(batchFrom)) {\n        // Keep doing combine animation.\n        morphOneBatch({\n          many: [batchFrom],\n          one: batchTo\n        }, true, animateIndex, animateCount, true);\n      } else {\n        var individualAnimationCfg = animationDelay ? defaults({\n          delay: animationDelay(animateIndex, animateCount)\n        }, animationCfg) : animationCfg;\n        morphPath(batchFrom, batchTo, individualAnimationCfg);\n        animateOtherProps(batchFrom, batchTo, batchFrom, batchTo, individualAnimationCfg);\n      }\n    } else {\n      var separateAnimationCfg = defaults({\n        dividePath: pathDividers[divideShape],\n        individualDelay: animationDelay && function (idx, count, fromPath, toPath) {\n          return animationDelay(idx + animateIndex, animateCount);\n        }\n      }, animationCfg);\n      var _a = fromIsMany ? combineMorph(batchMany, batchOne, separateAnimationCfg) : separateMorph(batchOne, batchMany, separateAnimationCfg),\n        fromIndividuals = _a.fromIndividuals,\n        toIndividuals = _a.toIndividuals;\n      var count = fromIndividuals.length;\n      for (var k = 0; k < count; k++) {\n        var individualAnimationCfg = animationDelay ? defaults({\n          delay: animationDelay(k, count)\n        }, animationCfg) : animationCfg;\n        animateOtherProps(fromIndividuals[k], toIndividuals[k], fromIsMany ? batchMany[k] : batch.one, fromIsMany ? batch.one : batchMany[k], individualAnimationCfg);\n      }\n    }\n  }\n  var fromIsMany = many ? many === from\n  // Is one to one. If the path number not match. also needs do merge and separate morphing.\n  : from.length > to.length;\n  var morphBatches = many ? prepareMorphBatches(one, many) : prepareMorphBatches(fromIsMany ? to : from, [fromIsMany ? from : to]);\n  var animateCount = 0;\n  for (var i = 0; i < morphBatches.length; i++) {\n    animateCount += morphBatches[i].many.length;\n  }\n  var animateIndex = 0;\n  for (var i = 0; i < morphBatches.length; i++) {\n    morphOneBatch(morphBatches[i], fromIsMany, animateIndex, animateCount);\n    animateIndex += morphBatches[i].many.length;\n  }\n}\nexport function getPathList(elements) {\n  if (!elements) {\n    return [];\n  }\n  if (isArray(elements)) {\n    var pathList_1 = [];\n    for (var i = 0; i < elements.length; i++) {\n      pathList_1.push(getPathList(elements[i]));\n    }\n    return pathList_1;\n  }\n  var pathList = [];\n  elements.traverse(function (el) {\n    if (el instanceof Path && !el.disableMorphing && !el.invisible && !el.ignore) {\n      pathList.push(el);\n    }\n  });\n  return pathList;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,YAAY,EAAEC,SAAS,EAAEC,iBAAiB,QAAQ,+BAA+B;AACzG,SAASC,IAAI,QAAQ,oBAAoB;AACzC,SAASC,QAAQ,EAAEC,OAAO,QAAQ,0BAA0B;AAC5D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,UAAUA,CAACC,QAAQ,EAAE;EAC5B,OAAOJ,OAAO,CAACI,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7B;AACA,SAASC,mBAAmBA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACtC,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,UAAU,GAAGH,GAAG,CAACI,MAAM;EAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,EAAEE,CAAC,EAAE,EAAE;IACnCH,OAAO,CAACI,IAAI,CAAC;MACXN,GAAG,EAAEA,GAAG,CAACK,CAAC,CAAC;MACXJ,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;EACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAE;IACpC,IAAIE,GAAG,GAAGN,IAAI,CAACI,CAAC,CAAC,CAACD,MAAM;IACxB,IAAII,CAAC,GAAG,KAAK,CAAC;IACd,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAEC,CAAC,EAAE,EAAE;MACxBN,OAAO,CAACM,CAAC,GAAGL,UAAU,CAAC,CAACF,IAAI,CAACK,IAAI,CAACL,IAAI,CAACI,CAAC,CAAC,CAACG,CAAC,CAAC,CAAC;IAC/C;EACF;EACA,IAAIC,GAAG,GAAG,CAAC;EACX;EACA,KAAK,IAAIJ,CAAC,GAAGF,UAAU,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxC,IAAI,CAACH,OAAO,CAACG,CAAC,CAAC,CAACJ,IAAI,CAACG,MAAM,EAAE;MAC3B,IAAIM,QAAQ,GAAGR,OAAO,CAACO,GAAG,CAAC,CAACR,IAAI;MAChC,IAAIS,QAAQ,CAACN,MAAM,IAAI,CAAC,EAAE;QACxB;QACA;QACA,IAAIK,GAAG,EAAE;UACPA,GAAG,GAAG,CAAC;QACT,CAAC,MAAM;UACL,OAAOP,OAAO;QAChB;MACF;MACA,IAAIK,GAAG,GAAGG,QAAQ,CAACN,MAAM;MACzB,IAAIO,GAAG,GAAGC,IAAI,CAACC,IAAI,CAACN,GAAG,GAAG,CAAC,CAAC;MAC5BL,OAAO,CAACG,CAAC,CAAC,CAACJ,IAAI,GAAGS,QAAQ,CAACI,KAAK,CAACH,GAAG,EAAEJ,GAAG,CAAC;MAC1CL,OAAO,CAACO,GAAG,CAAC,CAACR,IAAI,GAAGS,QAAQ,CAACI,KAAK,CAAC,CAAC,EAAEH,GAAG,CAAC;MAC1CF,GAAG,EAAE;IACP;EACF;EACA,OAAOP,OAAO;AAChB;AACA,IAAIa,YAAY,GAAG;EACjBC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;IACvB,IAAIC,GAAG,GAAG,EAAE;IACZ;IACA,IAAIC,aAAa,GAAG,CAAC,GAAGP,IAAI,CAACQ,GAAG,CAAC,CAAC,GAAGH,MAAM,CAACI,IAAI,CAACC,KAAK,CAACC,OAAO,EAAE,CAAC,GAAGN,MAAM,CAACO,KAAK,CAAC;IACjF,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,MAAM,CAACO,KAAK,EAAEnB,CAAC,EAAE,EAAE;MACrC,IAAIoB,MAAM,GAAG7B,SAAS,CAACqB,MAAM,CAACI,IAAI,CAAC;MACnCI,MAAM,CAACC,QAAQ,CAAC,SAAS,EAAEP,aAAa,CAAC;MACzCD,GAAG,CAACZ,IAAI,CAACmB,MAAM,CAAC;IAClB;IACA,OAAOP,GAAG;EACZ,CAAC;EACD;EACAS,KAAK,EAAE;AACT,CAAC;AACD,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAEC,EAAE,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,iBAAiB,EAAE;EACpG,IAAI,CAACL,IAAI,CAACzB,MAAM,IAAI,CAAC0B,EAAE,CAAC1B,MAAM,EAAE;IAC9B;EACF;EACA,IAAI+B,kBAAkB,GAAGxC,kBAAkB,CAAC,QAAQ,EAAEqC,WAAW,EAAEC,SAAS,CAAC;EAC7E,IAAI,EAAEE,kBAAkB,IAAIA,kBAAkB,CAACC,QAAQ,GAAG,CAAC,CAAC,EAAE;IAC5D;EACF;EACA,IAAIC,cAAc,GAAGL,WAAW,CAACM,QAAQ,CAAC,qBAAqB,CAAC,CAACC,GAAG,CAAC,OAAO,CAAC;EAC7E,IAAIC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC;IAC/B;IACA;IACAC,UAAU,EAAE;EACd,CAAC,EAAER,kBAAkB,CAAC;EACtB,IAAIlC,IAAI;EACR,IAAID,GAAG;EACP,IAAIH,UAAU,CAACgC,IAAI,CAAC,EAAE;IACpB;IACA5B,IAAI,GAAG4B,IAAI;IACX7B,GAAG,GAAG8B,EAAE;EACV;EACA,IAAIjC,UAAU,CAACiC,EAAE,CAAC,EAAE;IAClB;IACA7B,IAAI,GAAG6B,EAAE;IACT9B,GAAG,GAAG6B,IAAI;EACZ;EACA,SAASe,aAAaA,CAACC,KAAK,EAAEC,UAAU,EAAEC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAE;IAClF,IAAIC,SAAS,GAAGL,KAAK,CAAC5C,IAAI;IAC1B,IAAIkD,QAAQ,GAAGN,KAAK,CAAC7C,GAAG;IACxB,IAAIkD,SAAS,CAAC9C,MAAM,KAAK,CAAC,IAAI,CAAC6C,YAAY,EAAE;MAC3C;MACA,IAAIG,SAAS,GAAGN,UAAU,GAAGI,SAAS,CAAC,CAAC,CAAC,GAAGC,QAAQ;MACpD,IAAIE,OAAO,GAAGP,UAAU,GAAGK,QAAQ,GAAGD,SAAS,CAAC,CAAC,CAAC;MAClD,IAAI3D,iBAAiB,CAAC6D,SAAS,CAAC,EAAE;QAChC;QACAR,aAAa,CAAC;UACZ3C,IAAI,EAAE,CAACmD,SAAS,CAAC;UACjBpD,GAAG,EAAEqD;QACP,CAAC,EAAE,IAAI,EAAEN,YAAY,EAAEC,YAAY,EAAE,IAAI,CAAC;MAC5C,CAAC,MAAM;QACL,IAAIM,sBAAsB,GAAGjB,cAAc,GAAG5C,QAAQ,CAAC;UACrD8D,KAAK,EAAElB,cAAc,CAACU,YAAY,EAAEC,YAAY;QAClD,CAAC,EAAER,YAAY,CAAC,GAAGA,YAAY;QAC/BlD,SAAS,CAAC8D,SAAS,EAAEC,OAAO,EAAEC,sBAAsB,CAAC;QACrDpB,iBAAiB,CAACkB,SAAS,EAAEC,OAAO,EAAED,SAAS,EAAEC,OAAO,EAAEC,sBAAsB,CAAC;MACnF;IACF,CAAC,MAAM;MACL,IAAIE,oBAAoB,GAAG/D,QAAQ,CAAC;QAClCgE,UAAU,EAAE1C,YAAY,CAACgB,WAAW,CAAC;QACrC2B,eAAe,EAAErB,cAAc,IAAI,UAAUsB,GAAG,EAAEnC,KAAK,EAAEoC,QAAQ,EAAEC,MAAM,EAAE;UACzE,OAAOxB,cAAc,CAACsB,GAAG,GAAGZ,YAAY,EAAEC,YAAY,CAAC;QACzD;MACF,CAAC,EAAER,YAAY,CAAC;MAChB,IAAIsB,EAAE,GAAGhB,UAAU,GAAGzD,YAAY,CAAC6D,SAAS,EAAEC,QAAQ,EAAEK,oBAAoB,CAAC,GAAGpE,aAAa,CAAC+D,QAAQ,EAAED,SAAS,EAAEM,oBAAoB,CAAC;QACtIO,eAAe,GAAGD,EAAE,CAACC,eAAe;QACpCC,aAAa,GAAGF,EAAE,CAACE,aAAa;MAClC,IAAIxC,KAAK,GAAGuC,eAAe,CAAC3D,MAAM;MAClC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,KAAK,EAAEhB,CAAC,EAAE,EAAE;QAC9B,IAAI8C,sBAAsB,GAAGjB,cAAc,GAAG5C,QAAQ,CAAC;UACrD8D,KAAK,EAAElB,cAAc,CAAC7B,CAAC,EAAEgB,KAAK;QAChC,CAAC,EAAEgB,YAAY,CAAC,GAAGA,YAAY;QAC/BN,iBAAiB,CAAC6B,eAAe,CAACvD,CAAC,CAAC,EAAEwD,aAAa,CAACxD,CAAC,CAAC,EAAEsC,UAAU,GAAGI,SAAS,CAAC1C,CAAC,CAAC,GAAGqC,KAAK,CAAC7C,GAAG,EAAE8C,UAAU,GAAGD,KAAK,CAAC7C,GAAG,GAAGkD,SAAS,CAAC1C,CAAC,CAAC,EAAE8C,sBAAsB,CAAC;MAC/J;IACF;EACF;EACA,IAAIR,UAAU,GAAG7C,IAAI,GAAGA,IAAI,KAAK4B;EACjC;EAAA,EACEA,IAAI,CAACzB,MAAM,GAAG0B,EAAE,CAAC1B,MAAM;EACzB,IAAI6D,YAAY,GAAGhE,IAAI,GAAGF,mBAAmB,CAACC,GAAG,EAAEC,IAAI,CAAC,GAAGF,mBAAmB,CAAC+C,UAAU,GAAGhB,EAAE,GAAGD,IAAI,EAAE,CAACiB,UAAU,GAAGjB,IAAI,GAAGC,EAAE,CAAC,CAAC;EAChI,IAAIkB,YAAY,GAAG,CAAC;EACpB,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4D,YAAY,CAAC7D,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC5C2C,YAAY,IAAIiB,YAAY,CAAC5D,CAAC,CAAC,CAACJ,IAAI,CAACG,MAAM;EAC7C;EACA,IAAI2C,YAAY,GAAG,CAAC;EACpB,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4D,YAAY,CAAC7D,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC5CuC,aAAa,CAACqB,YAAY,CAAC5D,CAAC,CAAC,EAAEyC,UAAU,EAAEC,YAAY,EAAEC,YAAY,CAAC;IACtED,YAAY,IAAIkB,YAAY,CAAC5D,CAAC,CAAC,CAACJ,IAAI,CAACG,MAAM;EAC7C;AACF;AACA,OAAO,SAAS8D,WAAWA,CAACpE,QAAQ,EAAE;EACpC,IAAI,CAACA,QAAQ,EAAE;IACb,OAAO,EAAE;EACX;EACA,IAAIJ,OAAO,CAACI,QAAQ,CAAC,EAAE;IACrB,IAAIqE,UAAU,GAAG,EAAE;IACnB,KAAK,IAAI9D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,QAAQ,CAACM,MAAM,EAAEC,CAAC,EAAE,EAAE;MACxC8D,UAAU,CAAC7D,IAAI,CAAC4D,WAAW,CAACpE,QAAQ,CAACO,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,OAAO8D,UAAU;EACnB;EACA,IAAIC,QAAQ,GAAG,EAAE;EACjBtE,QAAQ,CAACuE,QAAQ,CAAC,UAAUC,EAAE,EAAE;IAC9B,IAAIA,EAAE,YAAY9E,IAAI,IAAI,CAAC8E,EAAE,CAACC,eAAe,IAAI,CAACD,EAAE,CAACE,SAAS,IAAI,CAACF,EAAE,CAACG,MAAM,EAAE;MAC5EL,QAAQ,CAAC9D,IAAI,CAACgE,EAAE,CAAC;IACnB;EACF,CAAC,CAAC;EACF,OAAOF,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}