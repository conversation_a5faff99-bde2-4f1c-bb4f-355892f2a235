"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isDevkitMigration = exports.createMigrationSchematicRule = exports.cdkMigrations = void 0;
const tasks_1 = require("@angular-devkit/schematics/tasks");
const update_tool_1 = require("../update-tool");
const project_tsconfig_paths_1 = require("../utils/project-tsconfig-paths");
const devkit_file_system_1 = require("./devkit-file-system");
const devkit_migration_1 = require("./devkit-migration");
const find_stylesheets_1 = require("./find-stylesheets");
const attribute_selectors_1 = require("./migrations/attribute-selectors");
const class_inheritance_1 = require("./migrations/class-inheritance");
const class_names_1 = require("./migrations/class-names");
const constructor_signature_1 = require("./migrations/constructor-signature");
const css_selectors_1 = require("./migrations/css-selectors");
const css_tokens_1 = require("./migrations/css-tokens");
const element_selectors_1 = require("./migrations/element-selectors");
const input_names_1 = require("./migrations/input-names");
const method_call_arguments_1 = require("./migrations/method-call-arguments");
const misc_template_1 = require("./migrations/misc-template");
const output_names_1 = require("./migrations/output-names");
const property_names_1 = require("./migrations/property-names");
const symbol_removal_1 = require("./migrations/symbol-removal");
/** List of migrations which run for the CDK update. */
exports.cdkMigrations = [
    attribute_selectors_1.AttributeSelectorsMigration,
    class_inheritance_1.ClassInheritanceMigration,
    class_names_1.ClassNamesMigration,
    constructor_signature_1.ConstructorSignatureMigration,
    css_selectors_1.CssSelectorsMigration,
    css_tokens_1.CssTokensMigration,
    element_selectors_1.ElementSelectorsMigration,
    input_names_1.InputNamesMigration,
    method_call_arguments_1.MethodCallArgumentsMigration,
    misc_template_1.MiscTemplateMigration,
    output_names_1.OutputNamesMigration,
    property_names_1.PropertyNamesMigration,
    symbol_removal_1.SymbolRemovalMigration,
];
/**
 * Creates a Angular schematic rule that runs the upgrade for the
 * specified target version.
 */
function createMigrationSchematicRule(targetVersion, extraMigrations, upgradeData, onMigrationCompleteFn) {
    return async (tree, context) => {
        const logger = context.logger;
        const workspace = await (0, project_tsconfig_paths_1.getWorkspaceConfigGracefully)(tree);
        if (workspace === null) {
            logger.error('Could not find workspace configuration file.');
            return;
        }
        // Keep track of all project source files which have been checked/migrated. This is
        // necessary because multiple TypeScript projects can contain the same source file and
        // we don't want to check these again, as this would result in duplicated failure messages.
        const analyzedFiles = new Set();
        const fileSystem = new devkit_file_system_1.DevkitFileSystem(tree);
        const projectNames = workspace.projects.keys();
        const migrations = [...exports.cdkMigrations, ...extraMigrations];
        let hasFailures = false;
        for (const projectName of projectNames) {
            const project = workspace.projects.get(projectName);
            const buildTsconfigPath = (0, project_tsconfig_paths_1.getTargetTsconfigPath)(project, 'build');
            const testTsconfigPath = (0, project_tsconfig_paths_1.getTargetTsconfigPath)(project, 'test');
            if (!buildTsconfigPath && !testTsconfigPath) {
                logger.warn(`Skipping migration for project ${projectName}. Unable to determine 'tsconfig.json' file in workspace config.`);
                continue;
            }
            // In some applications, developers will have global stylesheets which are not
            // specified in any Angular component. Therefore we glob up all CSS and SCSS files
            // in the project and migrate them if needed.
            // TODO: rework this to collect global stylesheets from the workspace config.
            // TODO: https://github.com/angular/components/issues/24032.
            const additionalStylesheetPaths = (0, find_stylesheets_1.findStylesheetFiles)(tree, project.root);
            if (buildTsconfigPath !== null) {
                runMigrations(project, projectName, buildTsconfigPath, additionalStylesheetPaths, false);
            }
            if (testTsconfigPath !== null) {
                runMigrations(project, projectName, testTsconfigPath, additionalStylesheetPaths, true);
            }
        }
        let runPackageManager = false;
        // Run the global post migration static members for all
        // registered devkit migrations.
        migrations.forEach(m => {
            const actionResult = isDevkitMigration(m) && m.globalPostMigration !== undefined
                ? m.globalPostMigration(tree, targetVersion, context)
                : null;
            if (actionResult) {
                runPackageManager = runPackageManager || actionResult.runPackageManager;
            }
        });
        // If a migration requested the package manager to run, we run it as an
        // asynchronous post migration task. We cannot run it synchronously,
        // as file changes from the current migration task are not applied to
        // the file system yet.
        if (runPackageManager) {
            context.addTask(new tasks_1.NodePackageInstallTask({ quiet: false }));
        }
        if (onMigrationCompleteFn) {
            onMigrationCompleteFn(context, targetVersion, hasFailures);
        }
        /** Runs the migrations for the specified workspace project. */
        function runMigrations(project, projectName, tsconfigPath, additionalStylesheetPaths, isTestTarget) {
            const program = update_tool_1.UpdateProject.createProgramFromTsconfig(tsconfigPath, fileSystem);
            const updateContext = {
                isTestTarget,
                projectName,
                project,
                tree,
            };
            const updateProject = new update_tool_1.UpdateProject(updateContext, program, fileSystem, analyzedFiles, context.logger);
            const result = updateProject.migrate(migrations, targetVersion, upgradeData, additionalStylesheetPaths);
            // Commit all recorded edits in the update recorder. We apply the edits after all
            // migrations ran because otherwise offsets in the TypeScript program would be
            // shifted and individual migrations could no longer update the same source file.
            fileSystem.commitEdits();
            hasFailures = hasFailures || result.hasFailures;
        }
    };
}
exports.createMigrationSchematicRule = createMigrationSchematicRule;
/** Whether the given migration type refers to a devkit migration */
function isDevkitMigration(value) {
    return devkit_migration_1.DevkitMigration.isPrototypeOf(value);
}
exports.isDevkitMigration = isDevkitMigration;
//# sourceMappingURL=data:application/json;base64,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