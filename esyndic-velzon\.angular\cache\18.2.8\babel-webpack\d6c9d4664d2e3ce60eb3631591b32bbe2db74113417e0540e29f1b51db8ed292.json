{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport BaseAxisPointer from './BaseAxisPointer.js';\nimport * as viewHelper from './viewHelper.js';\nimport * as cartesianAxisHelper from '../../coord/cartesian/cartesianAxisHelper.js';\nvar CartesianAxisPointer = /** @class */function (_super) {\n  __extends(CartesianAxisPointer, _super);\n  function CartesianAxisPointer() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\n   * @override\n   */\n  CartesianAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    var axis = axisModel.axis;\n    var grid = axis.grid;\n    var axisPointerType = axisPointerModel.get('type');\n    var otherExtent = getCartesian(grid, axis).getOtherAxis(axis).getGlobalExtent();\n    var pixelValue = axis.toGlobalCoord(axis.dataToCoord(value, true));\n    if (axisPointerType && axisPointerType !== 'none') {\n      var elStyle = viewHelper.buildElStyle(axisPointerModel);\n      var pointerOption = pointerShapeBuilder[axisPointerType](axis, pixelValue, otherExtent);\n      pointerOption.style = elStyle;\n      elOption.graphicKey = pointerOption.type;\n      elOption.pointer = pointerOption;\n    }\n    var layoutInfo = cartesianAxisHelper.layout(grid.model, axisModel);\n    viewHelper.buildCartesianSingleLabelElOption(\n    // @ts-ignore\n    value, elOption, layoutInfo, axisModel, axisPointerModel, api);\n  };\n  /**\n   * @override\n   */\n  CartesianAxisPointer.prototype.getHandleTransform = function (value, axisModel, axisPointerModel) {\n    var layoutInfo = cartesianAxisHelper.layout(axisModel.axis.grid.model, axisModel, {\n      labelInside: false\n    });\n    // @ts-ignore\n    layoutInfo.labelMargin = axisPointerModel.get(['handle', 'margin']);\n    var pos = viewHelper.getTransformedPosition(axisModel.axis, value, layoutInfo);\n    return {\n      x: pos[0],\n      y: pos[1],\n      rotation: layoutInfo.rotation + (layoutInfo.labelDirection < 0 ? Math.PI : 0)\n    };\n  };\n  /**\n   * @override\n   */\n  CartesianAxisPointer.prototype.updateHandleTransform = function (transform, delta, axisModel, axisPointerModel) {\n    var axis = axisModel.axis;\n    var grid = axis.grid;\n    var axisExtent = axis.getGlobalExtent(true);\n    var otherExtent = getCartesian(grid, axis).getOtherAxis(axis).getGlobalExtent();\n    var dimIndex = axis.dim === 'x' ? 0 : 1;\n    var currPosition = [transform.x, transform.y];\n    currPosition[dimIndex] += delta[dimIndex];\n    currPosition[dimIndex] = Math.min(axisExtent[1], currPosition[dimIndex]);\n    currPosition[dimIndex] = Math.max(axisExtent[0], currPosition[dimIndex]);\n    var cursorOtherValue = (otherExtent[1] + otherExtent[0]) / 2;\n    var cursorPoint = [cursorOtherValue, cursorOtherValue];\n    cursorPoint[dimIndex] = currPosition[dimIndex];\n    // Make tooltip do not overlap axisPointer and in the middle of the grid.\n    var tooltipOptions = [{\n      verticalAlign: 'middle'\n    }, {\n      align: 'center'\n    }];\n    return {\n      x: currPosition[0],\n      y: currPosition[1],\n      rotation: transform.rotation,\n      cursorPoint: cursorPoint,\n      tooltipOption: tooltipOptions[dimIndex]\n    };\n  };\n  return CartesianAxisPointer;\n}(BaseAxisPointer);\nfunction getCartesian(grid, axis) {\n  var opt = {};\n  opt[axis.dim + 'AxisIndex'] = axis.index;\n  return grid.getCartesian(opt);\n}\nvar pointerShapeBuilder = {\n  line: function (axis, pixelValue, otherExtent) {\n    var targetShape = viewHelper.makeLineShape([pixelValue, otherExtent[0]], [pixelValue, otherExtent[1]], getAxisDimIndex(axis));\n    return {\n      type: 'Line',\n      subPixelOptimize: true,\n      shape: targetShape\n    };\n  },\n  shadow: function (axis, pixelValue, otherExtent) {\n    var bandWidth = Math.max(1, axis.getBandWidth());\n    var span = otherExtent[1] - otherExtent[0];\n    return {\n      type: 'Rect',\n      shape: viewHelper.makeRectShape([pixelValue - bandWidth / 2, otherExtent[0]], [bandWidth, span], getAxisDimIndex(axis))\n    };\n  }\n};\nfunction getAxisDimIndex(axis) {\n  return axis.dim === 'x' ? 0 : 1;\n}\nexport default CartesianAxisPointer;", "map": {"version": 3, "names": ["__extends", "BaseAxisPointer", "viewHelper", "cartesianAxisHelper", "<PERSON><PERSON>ian<PERSON><PERSON>s<PERSON>oint<PERSON>", "_super", "apply", "arguments", "prototype", "makeElOption", "elOption", "value", "axisModel", "axisPointerModel", "api", "axis", "grid", "axisPointerType", "get", "otherExtent", "getCartesian", "getOtherAxis", "getGlobalExtent", "pixelValue", "toGlobalCoord", "dataToCoord", "elStyle", "buildElStyle", "pointerOption", "pointerShapeBuilder", "style", "<PERSON><PERSON><PERSON>", "type", "pointer", "layoutInfo", "layout", "model", "buildCartesianSingleLabelElOption", "getHandleTransform", "labelInside", "labelMargin", "pos", "getTransformedPosition", "x", "y", "rotation", "labelDirection", "Math", "PI", "updateHandleTransform", "transform", "delta", "axisExtent", "dimIndex", "dim", "currPosition", "min", "max", "cursor<PERSON><PERSON><PERSON><PERSON><PERSON>", "cursorPoint", "tooltipOptions", "verticalAlign", "align", "tooltipOption", "opt", "index", "line", "targetShape", "makeLineShape", "getAxisDimIndex", "subPixelOptimize", "shape", "shadow", "bandWidth", "getBandWidth", "span", "makeRectShape"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/component/axisPointer/CartesianAxisPointer.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport BaseAxisPointer from './BaseAxisPointer.js';\nimport * as viewHelper from './viewHelper.js';\nimport * as cartesianAxisHelper from '../../coord/cartesian/cartesianAxisHelper.js';\nvar CartesianAxisPointer = /** @class */function (_super) {\n  __extends(CartesianAxisPointer, _super);\n  function CartesianAxisPointer() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\n   * @override\n   */\n  CartesianAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    var axis = axisModel.axis;\n    var grid = axis.grid;\n    var axisPointerType = axisPointerModel.get('type');\n    var otherExtent = getCartesian(grid, axis).getOtherAxis(axis).getGlobalExtent();\n    var pixelValue = axis.toGlobalCoord(axis.dataToCoord(value, true));\n    if (axisPointerType && axisPointerType !== 'none') {\n      var elStyle = viewHelper.buildElStyle(axisPointerModel);\n      var pointerOption = pointerShapeBuilder[axisPointerType](axis, pixelValue, otherExtent);\n      pointerOption.style = elStyle;\n      elOption.graphicKey = pointerOption.type;\n      elOption.pointer = pointerOption;\n    }\n    var layoutInfo = cartesianAxisHelper.layout(grid.model, axisModel);\n    viewHelper.buildCartesianSingleLabelElOption(\n    // @ts-ignore\n    value, elOption, layoutInfo, axisModel, axisPointerModel, api);\n  };\n  /**\n   * @override\n   */\n  CartesianAxisPointer.prototype.getHandleTransform = function (value, axisModel, axisPointerModel) {\n    var layoutInfo = cartesianAxisHelper.layout(axisModel.axis.grid.model, axisModel, {\n      labelInside: false\n    });\n    // @ts-ignore\n    layoutInfo.labelMargin = axisPointerModel.get(['handle', 'margin']);\n    var pos = viewHelper.getTransformedPosition(axisModel.axis, value, layoutInfo);\n    return {\n      x: pos[0],\n      y: pos[1],\n      rotation: layoutInfo.rotation + (layoutInfo.labelDirection < 0 ? Math.PI : 0)\n    };\n  };\n  /**\n   * @override\n   */\n  CartesianAxisPointer.prototype.updateHandleTransform = function (transform, delta, axisModel, axisPointerModel) {\n    var axis = axisModel.axis;\n    var grid = axis.grid;\n    var axisExtent = axis.getGlobalExtent(true);\n    var otherExtent = getCartesian(grid, axis).getOtherAxis(axis).getGlobalExtent();\n    var dimIndex = axis.dim === 'x' ? 0 : 1;\n    var currPosition = [transform.x, transform.y];\n    currPosition[dimIndex] += delta[dimIndex];\n    currPosition[dimIndex] = Math.min(axisExtent[1], currPosition[dimIndex]);\n    currPosition[dimIndex] = Math.max(axisExtent[0], currPosition[dimIndex]);\n    var cursorOtherValue = (otherExtent[1] + otherExtent[0]) / 2;\n    var cursorPoint = [cursorOtherValue, cursorOtherValue];\n    cursorPoint[dimIndex] = currPosition[dimIndex];\n    // Make tooltip do not overlap axisPointer and in the middle of the grid.\n    var tooltipOptions = [{\n      verticalAlign: 'middle'\n    }, {\n      align: 'center'\n    }];\n    return {\n      x: currPosition[0],\n      y: currPosition[1],\n      rotation: transform.rotation,\n      cursorPoint: cursorPoint,\n      tooltipOption: tooltipOptions[dimIndex]\n    };\n  };\n  return CartesianAxisPointer;\n}(BaseAxisPointer);\nfunction getCartesian(grid, axis) {\n  var opt = {};\n  opt[axis.dim + 'AxisIndex'] = axis.index;\n  return grid.getCartesian(opt);\n}\nvar pointerShapeBuilder = {\n  line: function (axis, pixelValue, otherExtent) {\n    var targetShape = viewHelper.makeLineShape([pixelValue, otherExtent[0]], [pixelValue, otherExtent[1]], getAxisDimIndex(axis));\n    return {\n      type: 'Line',\n      subPixelOptimize: true,\n      shape: targetShape\n    };\n  },\n  shadow: function (axis, pixelValue, otherExtent) {\n    var bandWidth = Math.max(1, axis.getBandWidth());\n    var span = otherExtent[1] - otherExtent[0];\n    return {\n      type: 'Rect',\n      shape: viewHelper.makeRectShape([pixelValue - bandWidth / 2, otherExtent[0]], [bandWidth, span], getAxisDimIndex(axis))\n    };\n  }\n};\nfunction getAxisDimIndex(axis) {\n  return axis.dim === 'x' ? 0 : 1;\n}\nexport default CartesianAxisPointer;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAO,KAAKC,UAAU,MAAM,iBAAiB;AAC7C,OAAO,KAAKC,mBAAmB,MAAM,8CAA8C;AACnF,IAAIC,oBAAoB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACxDL,SAAS,CAACI,oBAAoB,EAAEC,MAAM,CAAC;EACvC,SAASD,oBAAoBA,CAAA,EAAG;IAC9B,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACA;AACF;AACA;EACEH,oBAAoB,CAACI,SAAS,CAACC,YAAY,GAAG,UAAUC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,GAAG,EAAE;IACzG,IAAIC,IAAI,GAAGH,SAAS,CAACG,IAAI;IACzB,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IACpB,IAAIC,eAAe,GAAGJ,gBAAgB,CAACK,GAAG,CAAC,MAAM,CAAC;IAClD,IAAIC,WAAW,GAAGC,YAAY,CAACJ,IAAI,EAAED,IAAI,CAAC,CAACM,YAAY,CAACN,IAAI,CAAC,CAACO,eAAe,CAAC,CAAC;IAC/E,IAAIC,UAAU,GAAGR,IAAI,CAACS,aAAa,CAACT,IAAI,CAACU,WAAW,CAACd,KAAK,EAAE,IAAI,CAAC,CAAC;IAClE,IAAIM,eAAe,IAAIA,eAAe,KAAK,MAAM,EAAE;MACjD,IAAIS,OAAO,GAAGxB,UAAU,CAACyB,YAAY,CAACd,gBAAgB,CAAC;MACvD,IAAIe,aAAa,GAAGC,mBAAmB,CAACZ,eAAe,CAAC,CAACF,IAAI,EAAEQ,UAAU,EAAEJ,WAAW,CAAC;MACvFS,aAAa,CAACE,KAAK,GAAGJ,OAAO;MAC7BhB,QAAQ,CAACqB,UAAU,GAAGH,aAAa,CAACI,IAAI;MACxCtB,QAAQ,CAACuB,OAAO,GAAGL,aAAa;IAClC;IACA,IAAIM,UAAU,GAAG/B,mBAAmB,CAACgC,MAAM,CAACnB,IAAI,CAACoB,KAAK,EAAExB,SAAS,CAAC;IAClEV,UAAU,CAACmC,iCAAiC;IAC5C;IACA1B,KAAK,EAAED,QAAQ,EAAEwB,UAAU,EAAEtB,SAAS,EAAEC,gBAAgB,EAAEC,GAAG,CAAC;EAChE,CAAC;EACD;AACF;AACA;EACEV,oBAAoB,CAACI,SAAS,CAAC8B,kBAAkB,GAAG,UAAU3B,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,EAAE;IAChG,IAAIqB,UAAU,GAAG/B,mBAAmB,CAACgC,MAAM,CAACvB,SAAS,CAACG,IAAI,CAACC,IAAI,CAACoB,KAAK,EAAExB,SAAS,EAAE;MAChF2B,WAAW,EAAE;IACf,CAAC,CAAC;IACF;IACAL,UAAU,CAACM,WAAW,GAAG3B,gBAAgB,CAACK,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACnE,IAAIuB,GAAG,GAAGvC,UAAU,CAACwC,sBAAsB,CAAC9B,SAAS,CAACG,IAAI,EAAEJ,KAAK,EAAEuB,UAAU,CAAC;IAC9E,OAAO;MACLS,CAAC,EAAEF,GAAG,CAAC,CAAC,CAAC;MACTG,CAAC,EAAEH,GAAG,CAAC,CAAC,CAAC;MACTI,QAAQ,EAAEX,UAAU,CAACW,QAAQ,IAAIX,UAAU,CAACY,cAAc,GAAG,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;IAC9E,CAAC;EACH,CAAC;EACD;AACF;AACA;EACE5C,oBAAoB,CAACI,SAAS,CAACyC,qBAAqB,GAAG,UAAUC,SAAS,EAAEC,KAAK,EAAEvC,SAAS,EAAEC,gBAAgB,EAAE;IAC9G,IAAIE,IAAI,GAAGH,SAAS,CAACG,IAAI;IACzB,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IACpB,IAAIoC,UAAU,GAAGrC,IAAI,CAACO,eAAe,CAAC,IAAI,CAAC;IAC3C,IAAIH,WAAW,GAAGC,YAAY,CAACJ,IAAI,EAAED,IAAI,CAAC,CAACM,YAAY,CAACN,IAAI,CAAC,CAACO,eAAe,CAAC,CAAC;IAC/E,IAAI+B,QAAQ,GAAGtC,IAAI,CAACuC,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;IACvC,IAAIC,YAAY,GAAG,CAACL,SAAS,CAACP,CAAC,EAAEO,SAAS,CAACN,CAAC,CAAC;IAC7CW,YAAY,CAACF,QAAQ,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC;IACzCE,YAAY,CAACF,QAAQ,CAAC,GAAGN,IAAI,CAACS,GAAG,CAACJ,UAAU,CAAC,CAAC,CAAC,EAAEG,YAAY,CAACF,QAAQ,CAAC,CAAC;IACxEE,YAAY,CAACF,QAAQ,CAAC,GAAGN,IAAI,CAACU,GAAG,CAACL,UAAU,CAAC,CAAC,CAAC,EAAEG,YAAY,CAACF,QAAQ,CAAC,CAAC;IACxE,IAAIK,gBAAgB,GAAG,CAACvC,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5D,IAAIwC,WAAW,GAAG,CAACD,gBAAgB,EAAEA,gBAAgB,CAAC;IACtDC,WAAW,CAACN,QAAQ,CAAC,GAAGE,YAAY,CAACF,QAAQ,CAAC;IAC9C;IACA,IAAIO,cAAc,GAAG,CAAC;MACpBC,aAAa,EAAE;IACjB,CAAC,EAAE;MACDC,KAAK,EAAE;IACT,CAAC,CAAC;IACF,OAAO;MACLnB,CAAC,EAAEY,YAAY,CAAC,CAAC,CAAC;MAClBX,CAAC,EAAEW,YAAY,CAAC,CAAC,CAAC;MAClBV,QAAQ,EAAEK,SAAS,CAACL,QAAQ;MAC5Bc,WAAW,EAAEA,WAAW;MACxBI,aAAa,EAAEH,cAAc,CAACP,QAAQ;IACxC,CAAC;EACH,CAAC;EACD,OAAOjD,oBAAoB;AAC7B,CAAC,CAACH,eAAe,CAAC;AAClB,SAASmB,YAAYA,CAACJ,IAAI,EAAED,IAAI,EAAE;EAChC,IAAIiD,GAAG,GAAG,CAAC,CAAC;EACZA,GAAG,CAACjD,IAAI,CAACuC,GAAG,GAAG,WAAW,CAAC,GAAGvC,IAAI,CAACkD,KAAK;EACxC,OAAOjD,IAAI,CAACI,YAAY,CAAC4C,GAAG,CAAC;AAC/B;AACA,IAAInC,mBAAmB,GAAG;EACxBqC,IAAI,EAAE,SAAAA,CAAUnD,IAAI,EAAEQ,UAAU,EAAEJ,WAAW,EAAE;IAC7C,IAAIgD,WAAW,GAAGjE,UAAU,CAACkE,aAAa,CAAC,CAAC7C,UAAU,EAAEJ,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAACI,UAAU,EAAEJ,WAAW,CAAC,CAAC,CAAC,CAAC,EAAEkD,eAAe,CAACtD,IAAI,CAAC,CAAC;IAC7H,OAAO;MACLiB,IAAI,EAAE,MAAM;MACZsC,gBAAgB,EAAE,IAAI;MACtBC,KAAK,EAAEJ;IACT,CAAC;EACH,CAAC;EACDK,MAAM,EAAE,SAAAA,CAAUzD,IAAI,EAAEQ,UAAU,EAAEJ,WAAW,EAAE;IAC/C,IAAIsD,SAAS,GAAG1B,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE1C,IAAI,CAAC2D,YAAY,CAAC,CAAC,CAAC;IAChD,IAAIC,IAAI,GAAGxD,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC;IAC1C,OAAO;MACLa,IAAI,EAAE,MAAM;MACZuC,KAAK,EAAErE,UAAU,CAAC0E,aAAa,CAAC,CAACrD,UAAU,GAAGkD,SAAS,GAAG,CAAC,EAAEtD,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAACsD,SAAS,EAAEE,IAAI,CAAC,EAAEN,eAAe,CAACtD,IAAI,CAAC;IACxH,CAAC;EACH;AACF,CAAC;AACD,SAASsD,eAAeA,CAACtD,IAAI,EAAE;EAC7B,OAAOA,IAAI,CAACuC,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;AACjC;AACA,eAAelD,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}