{"version": 3, "file": "legacy-input.mjs", "sources": ["../../../../../../src/material/legacy-input/input.ts", "../../../../../../src/material/legacy-input/input-module.ts", "../../../../../../src/material/legacy-input/legacy-input_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, inject} from '@angular/core';\nimport {MatInput as BaseMatInput} from '@angular/material/input';\nimport {\n  MatLegacyFormFieldControl,\n  MatLegacyFormField,\n  MAT_LEGACY_FORM_FIELD,\n} from '@angular/material/legacy-form-field';\n\n/**\n * Directive that allows a native input to work inside a `MatFormField`.\n * @deprecated Use `MatInput` from `@angular/material/input` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n  exportAs: 'matInput',\n  host: {\n    /**\n     * @breaking-change 8.0.0 remove .mat-form-field-autofill-control in favor of AutofillMonitor.\n     */\n    'class': 'mat-input-element mat-form-field-autofill-control',\n    '[class.mat-input-server]': '_isServer',\n    // These classes are inherited from the base input class and need to be cleared.\n    '[class.mat-mdc-input-element]': 'false',\n    '[class.mat-mdc-form-field-textarea-control]': 'false',\n    '[class.mat-mdc-form-field-input-control]': 'false',\n    '[class.mdc-text-field__input]': 'false',\n    '[class.mat-mdc-native-select-inline]': 'false',\n    // At the time of writing, we have a lot of customer tests that look up the input based on its\n    // placeholder. Since we sometimes omit the placeholder attribute from the DOM to prevent screen\n    // readers from reading it twice, we have to keep it somewhere in the DOM for the lookup.\n    '[attr.data-placeholder]': 'placeholder',\n    '[class.mat-native-select-inline]': '_isInlineSelect()',\n  },\n  providers: [{provide: MatLegacyFormFieldControl, useExisting: MatLegacyInput}],\n})\nexport class MatLegacyInput extends BaseMatInput {\n  private _legacyFormField = inject<MatLegacyFormField>(MAT_LEGACY_FORM_FIELD, {optional: true});\n\n  protected override _getPlaceholder() {\n    // If we're hiding the native placeholder, it should also be cleared from the DOM, otherwise\n    // screen readers will read it out twice: once from the label and once from the attribute.\n    // TODO: can be removed once we get rid of the `legacy` style for the form field, because it's\n    // the only one that supports promoting the placeholder to a label.\n    const formField = this._legacyFormField;\n    return formField && formField.appearance === 'legacy' && !formField._hasLabel?.()\n      ? null\n      : this.placeholder;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {TextFieldModule} from '@angular/cdk/text-field';\nimport {NgModule} from '@angular/core';\nimport {ErrorStateMatcher, MatCommonModule} from '@angular/material/core';\nimport {MatLegacyFormFieldModule} from '@angular/material/legacy-form-field';\nimport {MatLegacyInput} from './input';\n\n/**\n * @deprecated Use `MatInputModule` from `@angular/material/input` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@NgModule({\n  declarations: [MatLegacyInput],\n  imports: [TextFieldModule, MatLegacyFormFieldModule, MatCommonModule],\n  exports: [\n    TextFieldModule,\n    // We re-export the `MatLegacyFormFieldModule` since `MatLegacyInput` will almost always\n    // be used together with `MatLegacyFormField`.\n    MatLegacyFormFieldModule,\n    MatLegacyInput,\n  ],\n  providers: [ErrorStateMatcher],\n})\nexport class MatLegacyInputModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["BaseMatInput"], "mappings": ";;;;;;;;AAgBA;;;;AAIG;AAyBG,MAAO,cAAe,SAAQA,QAAY,CAAA;AAxBhD,IAAA,WAAA,GAAA;;QAyBU,IAAgB,CAAA,gBAAA,GAAG,MAAM,CAAqB,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAYhG,KAAA;IAVoB,eAAe,GAAA;;;;;AAKhC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACxC,QAAA,OAAO,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,QAAQ,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI;AAC/E,cAAE,IAAI;AACN,cAAE,IAAI,CAAC,WAAW,CAAC;KACtB;8GAZU,cAAc,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,QAAA,EAAA,2HAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,wBAAA,EAAA,WAAA,EAAA,6BAAA,EAAA,OAAA,EAAA,2CAAA,EAAA,OAAA,EAAA,wCAAA,EAAA,OAAA,EAAA,6BAAA,EAAA,OAAA,EAAA,oCAAA,EAAA,OAAA,EAAA,uBAAA,EAAA,aAAA,EAAA,gCAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,mDAAA,EAAA,EAAA,SAAA,EAFd,CAAC,EAAC,OAAO,EAAE,yBAAyB,EAAE,WAAW,EAAE,cAAc,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAEnE,cAAc,EAAA,UAAA,EAAA,CAAA;kBAxB1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAA;AAC8C,yDAAA,CAAA;AACxD,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,IAAI,EAAE;AACJ;;AAEG;AACH,wBAAA,OAAO,EAAE,mDAAmD;AAC5D,wBAAA,0BAA0B,EAAE,WAAW;;AAEvC,wBAAA,+BAA+B,EAAE,OAAO;AACxC,wBAAA,6CAA6C,EAAE,OAAO;AACtD,wBAAA,0CAA0C,EAAE,OAAO;AACnD,wBAAA,+BAA+B,EAAE,OAAO;AACxC,wBAAA,sCAAsC,EAAE,OAAO;;;;AAI/C,wBAAA,yBAAyB,EAAE,aAAa;AACxC,wBAAA,kCAAkC,EAAE,mBAAmB;AACxD,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,yBAAyB,EAAE,WAAW,EAAgB,cAAA,EAAC,CAAC;AAC/E,iBAAA,CAAA;;;AC9BD;;;AAGG;MAaU,oBAAoB,CAAA;8GAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAApB,oBAAoB,EAAA,YAAA,EAAA,CAXhB,cAAc,CACnB,EAAA,OAAA,EAAA,CAAA,eAAe,EAAE,wBAAwB,EAAE,eAAe,CAAA,EAAA,OAAA,EAAA,CAElE,eAAe;;;YAGf,wBAAwB;YACxB,cAAc,CAAA,EAAA,CAAA,CAAA,EAAA;+GAIL,oBAAoB,EAAA,SAAA,EAFpB,CAAC,iBAAiB,CAAC,EAAA,OAAA,EAAA,CARpB,eAAe,EAAE,wBAAwB,EAAE,eAAe,EAElE,eAAe;;;YAGf,wBAAwB,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAKf,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAZhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,YAAY,EAAE,CAAC,cAAc,CAAC;AAC9B,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,wBAAwB,EAAE,eAAe,CAAC;AACrE,oBAAA,OAAO,EAAE;wBACP,eAAe;;;wBAGf,wBAAwB;wBACxB,cAAc;AACf,qBAAA;oBACD,SAAS,EAAE,CAAC,iBAAiB,CAAC;AAC/B,iBAAA,CAAA;;;AC7BD;;AAEG;;;;"}