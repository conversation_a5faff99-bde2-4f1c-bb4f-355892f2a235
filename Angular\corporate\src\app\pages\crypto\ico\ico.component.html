<!-- Start Breadcrumbs -->
<app-breadcrumbs title="ICO List" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row row-cols-xxl-5 row-cols-lg-3 row-cols-sm-2 row-cols-1">
    <div class="col">
        <div class="card">
            <div class="card-body d-flex">
                <div class="flex-grow-1">
                    <h4>4751</h4>
                    <h6 class="text-muted fs-13 mb-0">ICOs Published</h6>
                </div>
                <div class="flex-shrink-0 avatar-sm">
                    <div class="avatar-title bg-primary-subtle text-primary fs-22 rounded">
                        <i class="ri-upload-2-line"></i>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col">
        <div class="card">
            <div class="card-body d-flex">
                <div class="flex-grow-1">
                    <h4>3423</h4>
                    <h6 class="text-muted fs-13 mb-0">Active ICOs</h6>
                </div>
                <div class="flex-shrink-0 avatar-sm">
                    <div class="avatar-title bg-secondary-subtle text-secondary fs-22 rounded">
                        <i class="ri-remote-control-line"></i>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col">
        <div class="card">
            <div class="card-body d-flex">
                <div class="flex-grow-1">
                    <h4>354</h4>
                    <h6 class="text-muted fs-13 mb-0">ICOs Trading</h6>
                </div>
                <div class="flex-shrink-0 avatar-sm">
                    <div class="avatar-title bg-success-subtle text-success fs-22 rounded">
                        <i class="ri-flashlight-fill"></i>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col">
        <div class="card">
            <div class="card-body d-flex">
                <div class="flex-grow-1">
                    <h4>2762</h4>
                    <h6 class="text-muted fs-13 mb-0">Funded ICOs</h6>
                </div>
                <div class="flex-shrink-0 avatar-sm">
                    <div class="avatar-title bg-danger-subtle text-danger fs-22 rounded">
                        <i class="ri-hand-coin-line"></i>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col">
        <div class="card">
            <div class="card-body d-flex">
                <div class="flex-grow-1">
                    <h4>1585</h4>
                    <h6 class="text-muted fs-13 mb-0">Upcoming ICO</h6>
                </div>
                <div class="flex-shrink-0 avatar-sm">
                    <div class="avatar-title bg-warning-subtle text-warning fs-22 rounded">
                        <i class="ri-donut-chart-line"></i>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end row-->

<div class="card">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-xxl-4 col-lg-6">
                <div class="search-box">
                    <input type="text" class="form-control" placeholder="Search to ICOs..." [(ngModel)]="term">
                    <i class="ri-search-line search-icon"></i>
                </div>
            </div><!--end col-->
            <div class="col-xxl-3 col-lg-6">
                <input class="form-control flatpickr-input" type="text" mwlFlatpickr [altInput]="true"
                    [convertModelValue]="true" placeholder="Select date" id="isDate">
            </div>
            <div class="col-xxl-2 col-lg-4">
                <select class="form-control" data-choices data-choices-search-false name="choices-single-default2"
                    id="choices-single-default2">
                    <option value="">Select Status</option>
                    <option value="Active">Active</option>
                    <option value="Ended">Ended</option>
                    <option value="Upcoming">Upcoming</option>
                </select>
            </div><!--end col-->
            <div class="col-xxl-2 col-lg-4">
                <select class="form-control" data-choices data-choices-search-false name="choices-single-default"
                    id="choices-single-default">
                    <option value="">Select Rating</option>
                    <option value="1">1 star</option>
                    <option value="2">2 star</option>
                    <option value="3">3 star</option>
                    <option value="4">4 star</option>
                    <option value="5">5 star</option>
                </select>
            </div><!--end col-->
            <div class="col-xxl-1 col-lg-4">
                <button class="btn btn-primary w-100" (click)="SearchData();"><i
                        class="ri-equalizer-line align-bottom me-1"></i> Filters</button>
            </div>
        </div><!--end row-->
    </div>
</div>

<div class="row">
    <div class="col-xxl-3 col-md-6">
        <div class="card overflow-hidden shadow-none">
            <div class="card-body bg-success-subtle">
                <h5 class="fs-17 text-center mb-0">Active ICOs</h5>
            </div>
        </div>
        @for(data of chatMessageDatas;track $index){
        <!-- <div *ngFor="let data of chatMessageDatas | filterBy:['label','caption']:term"> -->
            <div>
            @if(data.status == 'Active'){
            <div class="card mb-2">
                <div class="card-body">
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0 avatar-sm">
                            <div class="avatar-title bg-light rounded">
                                <img src="{{data.img}}" alt="" class="avatar-xxs" />
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="fs-15 mb-1">{{data.label}}</h5>
                            <p class="text-muted mb-2">{{data.caption}}</p>
                        </div>
                        <div>
                            <a href="javascript:void(0);" class="badge bg-primary-subtle text-primary">Visit Website <i
                                    class="ri-arrow-right-up-line align-bottom"></i></a>
                        </div>
                    </div>
                    <h6 class="text-muted mb-0">{{data.amount}} <span
                            class="badge bg-success-subtle text-success">{{data.percentage}}</span></h6>
                </div>
                <div class="card-body border-top border-top-dashed">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <h6 class="mb-0">{{data.rating}} <i class="ri-star-fill align-bottom text-warning"></i></h6>
                        </div>
                        <h6 class="flex-shrink-0 text-{{data.dateClass}} mb-0"><i class="ri-time-line align-bottom"></i>
                            {{data.date}}</h6>
                    </div>
                </div>
            </div>
            }
        </div>
    }
    </div><!--end col-->

    <div class="col-xxl-3 col-md-6">
        <div class="card overflow-hidden shadow-none">
            <div class="card-body bg-danger-subtle">
                <h5 class="fs-17 text-center mb-0">Ended ICOs</h5>
            </div>
        </div>
        @for(data of chatMessageDatas;track $index){
        <div>
            @if(data.status == 'Ended'){
            <div class="card mb-2">
                <div class="card-body">
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0 avatar-sm">
                            <div class="avatar-title bg-light rounded">
                                <img src="{{data.img}}" alt="" class="avatar-xxs" />
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="fs-15 mb-1">{{data.label}}</h5>
                            <p class="text-muted mb-2">{{data.caption}}</p>
                        </div>
                        <div>
                            <a href="javascript:void(0);" class="badge bg-primary-subtle text-primary">Visit Website <i
                                    class="ri-arrow-right-up-line align-bottom"></i></a>
                        </div>
                    </div>
                    <h6 class="text-muted mb-0">{{data.amount}} <span
                            class="badge bg-success-subtle text-success">{{data.percentage}}</span></h6>
                </div>
                <div class="card-body border-top border-top-dashed">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <h6 class="mb-0">{{data.rating}} <i class="ri-star-fill align-bottom text-warning"></i></h6>
                        </div>
                        <h6 class="flex-shrink-0 text-{{data.dateClass}} mb-0"><i class="ri-time-line align-bottom"></i>
                            {{data.date}}</h6>
                    </div>
                </div>
            </div>
            }
        </div>
    }
    </div><!--end col-->

    <div class="col-xxl-3 col-md-6">
        <div class="card overflow-hidden shadow-none">
            <div class="card-body bg-primary-subtle">
                <h5 class="fs-17 text-center mb-0">Upcoming ICOs</h5>
            </div>
        </div>
        @for(data of chatMessageDatas;track $index){
        <div>
            @if(data.status == 'Upcoming'){
            <div class="card mb-2">
                <div class="card-body">
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0 avatar-sm">
                            <div class="avatar-title bg-light rounded">
                                <img src="{{data.img}}" alt="" class="avatar-xxs" />
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="fs-15 mb-1">{{data.label}}</h5>
                            <p class="text-muted mb-2">{{data.caption}}</p>
                        </div>
                        <div>
                            <a href="javascript:void(0);" class="badge bg-primary-subtle text-primary">Visit Website <i
                                    class="ri-arrow-right-up-line align-bottom"></i></a>
                        </div>
                    </div>
                    <h6 class="text-muted mb-0">{{data.amount}} <span
                            class="badge bg-success-subtle text-success">{{data.percentage}}</span></h6>
                </div>
                <div class="card-body border-top border-top-dashed">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <h6 class="mb-0">{{data.rating}} <i class="ri-star-fill align-bottom text-warning"></i></h6>
                        </div>
                        <h6 class="flex-shrink-0 text-{{data.dateClass}} mb-0"><i class="ri-time-line align-bottom"></i>
                            {{data.date}}</h6>
                    </div>
                </div>
            </div>
            }
        </div>
    }

    </div><!--end col-->

    <div class="col-xxl-3 col-md-6">
        <div class="card overflow-hidden shadow-none">
            <div class="card-body bg-info-subtle ">
                <h5 class="fs-17 text-center mb-0">Trading ICOs</h5>
            </div>
        </div>
        @for(data of chatMessageDatas;track $index){
        <div>
            @if(data.status == 'Trading'){
            <div class="card mb-2 ribbon-box ribbon-fill right">
                <div class="ribbon ribbon-info shadow-none"><i class="ri-flashlight-fill me-1"></i>
                    {{data.ribbonNumber}}</div>
                <div class="card-body">
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0 avatar-sm">
                            <div class="avatar-title bg-light rounded">
                                <img src="{{data.img}}" alt="" class="avatar-xxs" />
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="fs-15 mb-1">{{data.label}}</h5>
                            <p class="text-muted mb-2">{{data.caption}}</p>
                        </div>
                        <div class="me-4">
                            <a href="javascript:void(0);" class="badge bg-primary-subtle text-primary">Visit Website <i
                                    class="ri-arrow-right-up-line align-bottom"></i></a>
                        </div>
                    </div>
                    <h6 class="text-muted mb-0">{{data.amount}} <span
                            class="badge bg-success-subtle text-success">{{data.percentage}}</span></h6>
                </div>
                <div class="card-body border-top border-top-dashed">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <h6 class="mb-0">{{data.rating}} <i class="ri-star-fill align-bottom text-warning"></i></h6>
                        </div>
                        <h6 class="flex-shrink-0 text-{{data.dateClass}} mb-0"><i class="ri-time-line align-bottom"></i>
                            {{data.date}}</h6>
                    </div>
                </div>
            </div>
            }
        </div>
    }
    </div><!--end col-->
</div>