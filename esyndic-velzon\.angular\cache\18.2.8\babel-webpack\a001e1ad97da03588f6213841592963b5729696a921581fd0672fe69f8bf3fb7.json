{"ast": null, "code": "export const MENU = [{\n  id: 1,\n  label: 'MENUITEMS.MENU.TEXT',\n  isTitle: true\n}, {\n  id: 2,\n  label: 'MENUITEMS.DASHBOARDS.TEXT',\n  icon: 'las la-tachometer-alt',\n  link: '/dashboard'\n}, {\n  id: 3,\n  label: 'MENUITEMS.BUILDINGS.TEXT',\n  icon: 'las la-building',\n  isCollapsed: true,\n  subItems: [{\n    id: 31,\n    label: 'MENUITEMS.BUILDINGS.LIST.ALL',\n    link: '/buildings',\n    parentId: 3\n  }, {\n    id: 32,\n    label: 'MENUITEMS.BUILDINGS.LIST.ADD',\n    link: '/buildings/add',\n    parentId: 3\n  }]\n}, {\n  id: 4,\n  label: 'MENUITEMS.USERS.TEXT',\n  icon: 'las la-users',\n  isCollapsed: true,\n  subItems: [{\n    id: 41,\n    label: 'MENUITEMS.USERS.LIST.RESIDENTS',\n    link: '/users/residents',\n    parentId: 4\n  }, {\n    id: 42,\n    label: 'MENUITEMS.USERS.LIST.OWNERS',\n    link: '/users/owners',\n    parentId: 4\n  }]\n}, {\n  id: 5,\n  label: 'MENUITEMS.MEETINGS.TEXT',\n  icon: 'las la-calendar',\n  isCollapsed: true,\n  subItems: [{\n    id: 51,\n    label: 'MENUITEMS.MEETINGS.LIST.ALL',\n    link: '/meetings',\n    parentId: 5\n  }, {\n    id: 52,\n    label: 'MENUITEMS.MEETINGS.LIST.SCHEDULE',\n    link: '/meetings/schedule',\n    parentId: 5\n  }]\n}, {\n  id: 6,\n  label: 'MENUITEMS.INCIDENTS.TEXT',\n  icon: 'las la-exclamation-triangle',\n  isCollapsed: true,\n  subItems: [{\n    id: 61,\n    label: 'MENUITEMS.INCIDENTS.LIST.ALL',\n    link: '/incidents',\n    parentId: 6\n  }, {\n    id: 62,\n    label: 'MENUITEMS.INCIDENTS.LIST.REPORT',\n    link: '/incidents/report',\n    parentId: 6\n  }]\n}, {\n  id: 7,\n  label: 'MENUITEMS.PAYMENTS.TEXT',\n  icon: 'las la-credit-card',\n  isCollapsed: true,\n  subItems: [{\n    id: 71,\n    label: 'MENUITEMS.PAYMENTS.LIST.ALL',\n    link: '/payments',\n    parentId: 7\n  }, {\n    id: 72,\n    label: 'MENUITEMS.PAYMENTS.LIST.PENDING',\n    link: '/payments/pending',\n    parentId: 7\n  }]\n}, {\n  id: 8,\n  label: 'MENUITEMS.SETTINGS.TEXT',\n  icon: 'las la-cog',\n  link: '/settings'\n}];", "map": {"version": 3, "names": ["MENU", "id", "label", "isTitle", "icon", "link", "isCollapsed", "subItems", "parentId"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\layouts\\sidebar\\menu.ts"], "sourcesContent": ["import { MenuItem } from './menu.model';\n\nexport const MENU: MenuItem[] = [\n  {\n    id: 1,\n    label: 'MENUITEMS.MENU.TEXT',\n    isTitle: true\n  },\n  {\n    id: 2,\n    label: 'MENUITEMS.DASHBOARDS.TEXT',\n    icon: 'las la-tachometer-alt',\n    link: '/dashboard'\n  },\n  {\n    id: 3,\n    label: 'MENUITEMS.BUILDINGS.TEXT',\n    icon: 'las la-building',\n    isCollapsed: true,\n    subItems: [\n      {\n        id: 31,\n        label: 'MENUITEMS.BUILDINGS.LIST.ALL',\n        link: '/buildings',\n        parentId: 3\n      },\n      {\n        id: 32,\n        label: 'MENUITEMS.BUILDINGS.LIST.ADD',\n        link: '/buildings/add',\n        parentId: 3\n      }\n    ]\n  },\n  {\n    id: 4,\n    label: 'MENUITEMS.USERS.TEXT',\n    icon: 'las la-users',\n    isCollapsed: true,\n    subItems: [\n      {\n        id: 41,\n        label: 'MENUITEMS.USERS.LIST.RESIDENTS',\n        link: '/users/residents',\n        parentId: 4\n      },\n      {\n        id: 42,\n        label: 'MENUITEMS.USERS.LIST.OWNERS',\n        link: '/users/owners',\n        parentId: 4\n      }\n    ]\n  },\n  {\n    id: 5,\n    label: 'MENUITEMS.MEETINGS.TEXT',\n    icon: 'las la-calendar',\n    isCollapsed: true,\n    subItems: [\n      {\n        id: 51,\n        label: 'MENUITEMS.MEETINGS.LIST.ALL',\n        link: '/meetings',\n        parentId: 5\n      },\n      {\n        id: 52,\n        label: 'MENUITEMS.MEETINGS.LIST.SCHEDULE',\n        link: '/meetings/schedule',\n        parentId: 5\n      }\n    ]\n  },\n  {\n    id: 6,\n    label: 'MENUITEMS.INCIDENTS.TEXT',\n    icon: 'las la-exclamation-triangle',\n    isCollapsed: true,\n    subItems: [\n      {\n        id: 61,\n        label: 'MENUITEMS.INCIDENTS.LIST.ALL',\n        link: '/incidents',\n        parentId: 6\n      },\n      {\n        id: 62,\n        label: 'MENUITEMS.INCIDENTS.LIST.REPORT',\n        link: '/incidents/report',\n        parentId: 6\n      }\n    ]\n  },\n  {\n    id: 7,\n    label: 'MENUITEMS.PAYMENTS.TEXT',\n    icon: 'las la-credit-card',\n    isCollapsed: true,\n    subItems: [\n      {\n        id: 71,\n        label: 'MENUITEMS.PAYMENTS.LIST.ALL',\n        link: '/payments',\n        parentId: 7\n      },\n      {\n        id: 72,\n        label: 'MENUITEMS.PAYMENTS.LIST.PENDING',\n        link: '/payments/pending',\n        parentId: 7\n      }\n    ]\n  },\n  {\n    id: 8,\n    label: 'MENUITEMS.SETTINGS.TEXT',\n    icon: 'las la-cog',\n    link: '/settings'\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,IAAI,GAAe,CAC9B;EACEC,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,qBAAqB;EAC5BC,OAAO,EAAE;CACV,EACD;EACEF,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,2BAA2B;EAClCE,IAAI,EAAE,uBAAuB;EAC7BC,IAAI,EAAE;CACP,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,0BAA0B;EACjCE,IAAI,EAAE,iBAAiB;EACvBE,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,CACR;IACEN,EAAE,EAAE,EAAE;IACNC,KAAK,EAAE,8BAA8B;IACrCG,IAAI,EAAE,YAAY;IAClBG,QAAQ,EAAE;GACX,EACD;IACEP,EAAE,EAAE,EAAE;IACNC,KAAK,EAAE,8BAA8B;IACrCG,IAAI,EAAE,gBAAgB;IACtBG,QAAQ,EAAE;GACX;CAEJ,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,sBAAsB;EAC7BE,IAAI,EAAE,cAAc;EACpBE,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,CACR;IACEN,EAAE,EAAE,EAAE;IACNC,KAAK,EAAE,gCAAgC;IACvCG,IAAI,EAAE,kBAAkB;IACxBG,QAAQ,EAAE;GACX,EACD;IACEP,EAAE,EAAE,EAAE;IACNC,KAAK,EAAE,6BAA6B;IACpCG,IAAI,EAAE,eAAe;IACrBG,QAAQ,EAAE;GACX;CAEJ,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,yBAAyB;EAChCE,IAAI,EAAE,iBAAiB;EACvBE,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,CACR;IACEN,EAAE,EAAE,EAAE;IACNC,KAAK,EAAE,6BAA6B;IACpCG,IAAI,EAAE,WAAW;IACjBG,QAAQ,EAAE;GACX,EACD;IACEP,EAAE,EAAE,EAAE;IACNC,KAAK,EAAE,kCAAkC;IACzCG,IAAI,EAAE,oBAAoB;IAC1BG,QAAQ,EAAE;GACX;CAEJ,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,0BAA0B;EACjCE,IAAI,EAAE,6BAA6B;EACnCE,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,CACR;IACEN,EAAE,EAAE,EAAE;IACNC,KAAK,EAAE,8BAA8B;IACrCG,IAAI,EAAE,YAAY;IAClBG,QAAQ,EAAE;GACX,EACD;IACEP,EAAE,EAAE,EAAE;IACNC,KAAK,EAAE,iCAAiC;IACxCG,IAAI,EAAE,mBAAmB;IACzBG,QAAQ,EAAE;GACX;CAEJ,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,yBAAyB;EAChCE,IAAI,EAAE,oBAAoB;EAC1BE,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,CACR;IACEN,EAAE,EAAE,EAAE;IACNC,KAAK,EAAE,6BAA6B;IACpCG,IAAI,EAAE,WAAW;IACjBG,QAAQ,EAAE;GACX,EACD;IACEP,EAAE,EAAE,EAAE;IACNC,KAAK,EAAE,iCAAiC;IACxCG,IAAI,EAAE,mBAAmB;IACzBG,QAAQ,EAAE;GACX;CAEJ,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,yBAAyB;EAChCE,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE;CACP,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}