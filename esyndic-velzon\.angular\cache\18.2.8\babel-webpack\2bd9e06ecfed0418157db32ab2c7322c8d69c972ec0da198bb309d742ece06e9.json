{"ast": null, "code": "import { Actions, createEffect, ofType } from '@ngrx/effects';\nimport { map, catchError, exhaustMap, tap } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { login, loginSuccess, loginFailure, logout, logoutSuccess, Register, RegisterSuccess, RegisterFailure } from './authentication.actions';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngrx/effects\";\nexport class AuthenticationEffects {\n  constructor(actions$, AuthenticationService, router) {\n    this.actions$ = actions$;\n    this.AuthenticationService = AuthenticationService;\n    this.router = router;\n    this.Register$ = createEffect(() => this.actions$.pipe(ofType(Register), exhaustMap(({\n      email,\n      first_name,\n      password\n    }) => this.AuthenticationService.register(email, first_name, password).pipe(map(response => {\n      if (response.status === 'success') {\n        this.router.navigate(['/auth/login']);\n        return RegisterSuccess({\n          user: response.data\n        });\n      } else {\n        return RegisterFailure({\n          error: response.data\n        });\n      }\n    }), catchError(error => of(RegisterFailure({\n      error: error.message || 'Registration failed'\n    })))))));\n    this.login$ = createEffect(() => this.actions$.pipe(ofType(login), exhaustMap(({\n      email,\n      password\n    }) => {\n      if (environment.defaultauth === \"fakebackend\") {\n        return this.AuthenticationService.login(email, password).pipe(map(response => {\n          if (response.status === 'success') {\n            sessionStorage.setItem('toast', 'true');\n            sessionStorage.setItem('currentUser', JSON.stringify(response.data));\n            if (response.token) {\n              sessionStorage.setItem('token', response.token);\n            }\n            this.router.navigate(['/']);\n            // Return the user data from the response\n            return loginSuccess({\n              user: response.data\n            });\n          } else {\n            // Handle error response\n            return loginFailure({\n              error: response.data\n            });\n          }\n        }), catchError(error => of(loginFailure({\n          error: error.message || 'Login failed'\n        }))));\n      } else if (environment.defaultauth === \"firebase\") {\n        return of(); // Return an observable, even if it's empty\n      } else {\n        return of(); // Return an observable, even if it's empty\n      }\n    })));\n    this.logout$ = createEffect(() => this.actions$.pipe(ofType(logout), tap(() => {\n      // Perform any necessary cleanup or side effects before logging out\n    }), exhaustMap(() => of(logoutSuccess()))));\n  }\n  static {\n    this.ɵfac = function AuthenticationEffects_Factory(t) {\n      return new (t || AuthenticationEffects)(i0.ɵɵinject(Actions), i0.ɵɵinject(i1.AuthenticationService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthenticationEffects,\n      factory: AuthenticationEffects.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["Actions", "createEffect", "ofType", "map", "catchError", "exhaustMap", "tap", "of", "login", "loginSuccess", "loginFailure", "logout", "logoutSuccess", "Register", "RegisterSuccess", "RegisterFailure", "environment", "AuthenticationEffects", "constructor", "actions$", "AuthenticationService", "router", "Register$", "pipe", "email", "first_name", "password", "register", "response", "status", "navigate", "user", "data", "error", "message", "login$", "<PERSON>auth", "sessionStorage", "setItem", "JSON", "stringify", "token", "logout$", "i0", "ɵɵinject", "i1", "i2", "Router", "factory", "ɵfac"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\store\\Authentication\\authentication.effects.ts"], "sourcesContent": ["import { Injectable, Inject } from '@angular/core';\r\nimport { Actions, createEffect, ofType } from '@ngrx/effects';\r\nimport { map, switchMap, catchError, exhaustMap, tap } from 'rxjs/operators';\r\nimport { from, of } from 'rxjs';\r\nimport { AuthenticationService } from '../../core/services/auth.service';\r\nimport { login, loginSuccess, loginFailure, logout, logoutSuccess, Register, RegisterSuccess, RegisterFailure} from './authentication.actions';\r\nimport { Router } from '@angular/router';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable()\r\nexport class AuthenticationEffects {\r\n\r\n  Register$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(Register),\r\n      exhaustMap(({ email, first_name, password }) =>\r\n        this.AuthenticationService.register(email, first_name, password).pipe(\r\n          map((response) => {\r\n            if (response.status === 'success') {\r\n              this.router.navigate(['/auth/login']);\r\n              return RegisterSuccess({ user: response.data });\r\n            } else {\r\n              return RegisterFailure({ error: response.data });\r\n            }\r\n          }),\r\n          catchError((error) => of(RegisterFailure({ error: error.message || 'Registration failed' })))\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  login$ = createEffect(() =>\r\n  this.actions$.pipe(\r\n    ofType(login),\r\n    exhaustMap(({ email, password }) => {\r\n      if (environment.defaultauth === \"fakebackend\") {\r\n        return this.AuthenticationService.login(email, password).pipe(\r\n          map((response) => {\r\n            if (response.status === 'success') {\r\n              sessionStorage.setItem('toast', 'true');\r\n              sessionStorage.setItem('currentUser', JSON.stringify(response.data));\r\n              if (response.token) {\r\n                sessionStorage.setItem('token', response.token);\r\n              }\r\n              this.router.navigate(['/']);\r\n              // Return the user data from the response\r\n              return loginSuccess({ user: response.data });\r\n            } else {\r\n              // Handle error response\r\n              return loginFailure({ error: response.data });\r\n            }\r\n          }),\r\n          catchError((error) => of(loginFailure({ error: error.message || 'Login failed' })))\r\n        );\r\n      } else if (environment.defaultauth === \"firebase\") {\r\n        return of(); // Return an observable, even if it's empty\r\n      } else {\r\n        return of(); // Return an observable, even if it's empty\r\n      }\r\n    })\r\n  )\r\n);\r\n\r\n  logout$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(logout),\r\n      tap(() => {\r\n        // Perform any necessary cleanup or side effects before logging out\r\n      }),\r\n      exhaustMap(() => of(logoutSuccess()))\r\n    )\r\n  );\r\n\r\n  constructor(\r\n    @Inject(Actions) private actions$: Actions,\r\n    private AuthenticationService: AuthenticationService,\r\n    private router: Router) { }\r\n\r\n}"], "mappings": "AACA,SAASA,OAAO,EAAEC,YAAY,EAAEC,MAAM,QAAQ,eAAe;AAC7D,SAASC,GAAG,EAAaC,UAAU,EAAEC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAC5E,SAAeC,EAAE,QAAQ,MAAM;AAE/B,SAASC,KAAK,EAAEC,YAAY,EAAEC,YAAY,EAAEC,MAAM,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,eAAe,QAAO,0BAA0B;AAE9I,SAASC,WAAW,QAAQ,8BAA8B;;;;;AAG1D,OAAM,MAAOC,qBAAqB;EA+DhCC,YAC2BC,QAAiB,EAClCC,qBAA4C,EAC5CC,MAAc;IAFG,KAAAF,QAAQ,GAARA,QAAQ;IACzB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,MAAM,GAANA,MAAM;IAhEhB,KAAAC,SAAS,GAAGrB,YAAY,CAAC,MACvB,IAAI,CAACkB,QAAQ,CAACI,IAAI,CAChBrB,MAAM,CAACW,QAAQ,CAAC,EAChBR,UAAU,CAAC,CAAC;MAAEmB,KAAK;MAAEC,UAAU;MAAEC;IAAQ,CAAE,KACzC,IAAI,CAACN,qBAAqB,CAACO,QAAQ,CAACH,KAAK,EAAEC,UAAU,EAAEC,QAAQ,CAAC,CAACH,IAAI,CACnEpB,GAAG,CAAEyB,QAAQ,IAAI;MACf,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;QACjC,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;QACrC,OAAOhB,eAAe,CAAC;UAAEiB,IAAI,EAAEH,QAAQ,CAACI;QAAI,CAAE,CAAC;MACjD,CAAC,MAAM;QACL,OAAOjB,eAAe,CAAC;UAAEkB,KAAK,EAAEL,QAAQ,CAACI;QAAI,CAAE,CAAC;MAClD;IACF,CAAC,CAAC,EACF5B,UAAU,CAAE6B,KAAK,IAAK1B,EAAE,CAACQ,eAAe,CAAC;MAAEkB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;IAAqB,CAAE,CAAC,CAAC,CAAC,CAC9F,CACF,CACF,CACF;IAED,KAAAC,MAAM,GAAGlC,YAAY,CAAC,MACtB,IAAI,CAACkB,QAAQ,CAACI,IAAI,CAChBrB,MAAM,CAACM,KAAK,CAAC,EACbH,UAAU,CAAC,CAAC;MAAEmB,KAAK;MAAEE;IAAQ,CAAE,KAAI;MACjC,IAAIV,WAAW,CAACoB,WAAW,KAAK,aAAa,EAAE;QAC7C,OAAO,IAAI,CAAChB,qBAAqB,CAACZ,KAAK,CAACgB,KAAK,EAAEE,QAAQ,CAAC,CAACH,IAAI,CAC3DpB,GAAG,CAAEyB,QAAQ,IAAI;UACf,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;YACjCQ,cAAc,CAACC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;YACvCD,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACZ,QAAQ,CAACI,IAAI,CAAC,CAAC;YACpE,IAAIJ,QAAQ,CAACa,KAAK,EAAE;cAClBJ,cAAc,CAACC,OAAO,CAAC,OAAO,EAAEV,QAAQ,CAACa,KAAK,CAAC;YACjD;YACA,IAAI,CAACpB,MAAM,CAACS,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3B;YACA,OAAOrB,YAAY,CAAC;cAAEsB,IAAI,EAAEH,QAAQ,CAACI;YAAI,CAAE,CAAC;UAC9C,CAAC,MAAM;YACL;YACA,OAAOtB,YAAY,CAAC;cAAEuB,KAAK,EAAEL,QAAQ,CAACI;YAAI,CAAE,CAAC;UAC/C;QACF,CAAC,CAAC,EACF5B,UAAU,CAAE6B,KAAK,IAAK1B,EAAE,CAACG,YAAY,CAAC;UAAEuB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAAc,CAAE,CAAC,CAAC,CAAC,CACpF;MACH,CAAC,MAAM,IAAIlB,WAAW,CAACoB,WAAW,KAAK,UAAU,EAAE;QACjD,OAAO7B,EAAE,EAAE,CAAC,CAAC;MACf,CAAC,MAAM;QACL,OAAOA,EAAE,EAAE,CAAC,CAAC;MACf;IACF,CAAC,CAAC,CACH,CACF;IAEC,KAAAmC,OAAO,GAAGzC,YAAY,CAAC,MACrB,IAAI,CAACkB,QAAQ,CAACI,IAAI,CAChBrB,MAAM,CAACS,MAAM,CAAC,EACdL,GAAG,CAAC,MAAK;MACP;IAAA,CACD,CAAC,EACFD,UAAU,CAAC,MAAME,EAAE,CAACK,aAAa,EAAE,CAAC,CAAC,CACtC,CACF;EAK2B;;;uBAlEjBK,qBAAqB,EAAA0B,EAAA,CAAAC,QAAA,CAgEtB5C,OAAO,GAAA2C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAzB,qBAAA,GAAAuB,EAAA,CAAAC,QAAA,CAAAE,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAhEN9B,qBAAqB;MAAA+B,OAAA,EAArB/B,qBAAqB,CAAAgC;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}