{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport Cartesian from './Cartesian.js';\nimport { invert } from 'zrender/lib/core/matrix.js';\nimport { applyTransform } from 'zrender/lib/core/vector.js';\nexport var cartesian2DDimensions = ['x', 'y'];\nfunction canCalculateAffineTransform(scale) {\n  return scale.type === 'interval' || scale.type === 'time';\n}\nvar Cartesian2D = /** @class */function (_super) {\n  __extends(Cartesian2D, _super);\n  function Cartesian2D() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'cartesian2d';\n    _this.dimensions = cartesian2DDimensions;\n    return _this;\n  }\n  /**\n   * Calculate an affine transform matrix if two axes are time or value.\n   * It's mainly for accelartion on the large time series data.\n   */\n  Cartesian2D.prototype.calcAffineTransform = function () {\n    this._transform = this._invTransform = null;\n    var xAxisScale = this.getAxis('x').scale;\n    var yAxisScale = this.getAxis('y').scale;\n    if (!canCalculateAffineTransform(xAxisScale) || !canCalculateAffineTransform(yAxisScale)) {\n      return;\n    }\n    var xScaleExtent = xAxisScale.getExtent();\n    var yScaleExtent = yAxisScale.getExtent();\n    var start = this.dataToPoint([xScaleExtent[0], yScaleExtent[0]]);\n    var end = this.dataToPoint([xScaleExtent[1], yScaleExtent[1]]);\n    var xScaleSpan = xScaleExtent[1] - xScaleExtent[0];\n    var yScaleSpan = yScaleExtent[1] - yScaleExtent[0];\n    if (!xScaleSpan || !yScaleSpan) {\n      return;\n    }\n    // Accelerate data to point calculation on the special large time series data.\n    var scaleX = (end[0] - start[0]) / xScaleSpan;\n    var scaleY = (end[1] - start[1]) / yScaleSpan;\n    var translateX = start[0] - xScaleExtent[0] * scaleX;\n    var translateY = start[1] - yScaleExtent[0] * scaleY;\n    var m = this._transform = [scaleX, 0, 0, scaleY, translateX, translateY];\n    this._invTransform = invert([], m);\n  };\n  /**\n   * Base axis will be used on stacking.\n   */\n  Cartesian2D.prototype.getBaseAxis = function () {\n    return this.getAxesByScale('ordinal')[0] || this.getAxesByScale('time')[0] || this.getAxis('x');\n  };\n  Cartesian2D.prototype.containPoint = function (point) {\n    var axisX = this.getAxis('x');\n    var axisY = this.getAxis('y');\n    return axisX.contain(axisX.toLocalCoord(point[0])) && axisY.contain(axisY.toLocalCoord(point[1]));\n  };\n  Cartesian2D.prototype.containData = function (data) {\n    return this.getAxis('x').containData(data[0]) && this.getAxis('y').containData(data[1]);\n  };\n  Cartesian2D.prototype.containZone = function (data1, data2) {\n    var zoneDiag1 = this.dataToPoint(data1);\n    var zoneDiag2 = this.dataToPoint(data2);\n    var area = this.getArea();\n    var zone = new BoundingRect(zoneDiag1[0], zoneDiag1[1], zoneDiag2[0] - zoneDiag1[0], zoneDiag2[1] - zoneDiag1[1]);\n    return area.intersect(zone);\n  };\n  Cartesian2D.prototype.dataToPoint = function (data, clamp, out) {\n    out = out || [];\n    var xVal = data[0];\n    var yVal = data[1];\n    // Fast path\n    if (this._transform\n    // It's supported that if data is like `[Inifity, 123]`, where only Y pixel calculated.\n    && xVal != null && isFinite(xVal) && yVal != null && isFinite(yVal)) {\n      return applyTransform(out, data, this._transform);\n    }\n    var xAxis = this.getAxis('x');\n    var yAxis = this.getAxis('y');\n    out[0] = xAxis.toGlobalCoord(xAxis.dataToCoord(xVal, clamp));\n    out[1] = yAxis.toGlobalCoord(yAxis.dataToCoord(yVal, clamp));\n    return out;\n  };\n  Cartesian2D.prototype.clampData = function (data, out) {\n    var xScale = this.getAxis('x').scale;\n    var yScale = this.getAxis('y').scale;\n    var xAxisExtent = xScale.getExtent();\n    var yAxisExtent = yScale.getExtent();\n    var x = xScale.parse(data[0]);\n    var y = yScale.parse(data[1]);\n    out = out || [];\n    out[0] = Math.min(Math.max(Math.min(xAxisExtent[0], xAxisExtent[1]), x), Math.max(xAxisExtent[0], xAxisExtent[1]));\n    out[1] = Math.min(Math.max(Math.min(yAxisExtent[0], yAxisExtent[1]), y), Math.max(yAxisExtent[0], yAxisExtent[1]));\n    return out;\n  };\n  Cartesian2D.prototype.pointToData = function (point, clamp) {\n    var out = [];\n    if (this._invTransform) {\n      return applyTransform(out, point, this._invTransform);\n    }\n    var xAxis = this.getAxis('x');\n    var yAxis = this.getAxis('y');\n    out[0] = xAxis.coordToData(xAxis.toLocalCoord(point[0]), clamp);\n    out[1] = yAxis.coordToData(yAxis.toLocalCoord(point[1]), clamp);\n    return out;\n  };\n  Cartesian2D.prototype.getOtherAxis = function (axis) {\n    return this.getAxis(axis.dim === 'x' ? 'y' : 'x');\n  };\n  /**\n   * Get rect area of cartesian.\n   * Area will have a contain function to determine if a point is in the coordinate system.\n   */\n  Cartesian2D.prototype.getArea = function (tolerance) {\n    tolerance = tolerance || 0;\n    var xExtent = this.getAxis('x').getGlobalExtent();\n    var yExtent = this.getAxis('y').getGlobalExtent();\n    var x = Math.min(xExtent[0], xExtent[1]) - tolerance;\n    var y = Math.min(yExtent[0], yExtent[1]) - tolerance;\n    var width = Math.max(xExtent[0], xExtent[1]) - x + tolerance;\n    var height = Math.max(yExtent[0], yExtent[1]) - y + tolerance;\n    return new BoundingRect(x, y, width, height);\n  };\n  return Cartesian2D;\n}(Cartesian);\n;\nexport default Cartesian2D;", "map": {"version": 3, "names": ["__extends", "BoundingRect", "<PERSON><PERSON><PERSON>", "invert", "applyTransform", "cartesian2DDimensions", "canCalculateAffineTransform", "scale", "type", "Cartesian2D", "_super", "_this", "apply", "arguments", "dimensions", "prototype", "calcAffineTransform", "_transform", "_invTransform", "xAxisScale", "getAxis", "yAxisScale", "xScaleExtent", "getExtent", "yScaleExtent", "start", "dataToPoint", "end", "xScaleSpan", "yScaleSpan", "scaleX", "scaleY", "translateX", "translateY", "m", "getBaseAxis", "getAxesByScale", "containPoint", "point", "axisX", "axisY", "contain", "toLocalCoord", "containData", "data", "containZone", "data1", "data2", "zoneDiag1", "zoneDiag2", "area", "getArea", "zone", "intersect", "clamp", "out", "xVal", "yVal", "isFinite", "xAxis", "yAxis", "toGlobalCoord", "dataToCoord", "clampData", "xScale", "yScale", "xAxisExtent", "yAxisExtent", "x", "parse", "y", "Math", "min", "max", "pointToData", "coordToData", "getOtherAxis", "axis", "dim", "tolerance", "xExtent", "getGlobalExtent", "yExtent", "width", "height"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/coord/cartesian/Cartesian2D.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport Cartesian from './Cartesian.js';\nimport { invert } from 'zrender/lib/core/matrix.js';\nimport { applyTransform } from 'zrender/lib/core/vector.js';\nexport var cartesian2DDimensions = ['x', 'y'];\nfunction canCalculateAffineTransform(scale) {\n  return scale.type === 'interval' || scale.type === 'time';\n}\nvar Cartesian2D = /** @class */function (_super) {\n  __extends(Cartesian2D, _super);\n  function Cartesian2D() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'cartesian2d';\n    _this.dimensions = cartesian2DDimensions;\n    return _this;\n  }\n  /**\n   * Calculate an affine transform matrix if two axes are time or value.\n   * It's mainly for accelartion on the large time series data.\n   */\n  Cartesian2D.prototype.calcAffineTransform = function () {\n    this._transform = this._invTransform = null;\n    var xAxisScale = this.getAxis('x').scale;\n    var yAxisScale = this.getAxis('y').scale;\n    if (!canCalculateAffineTransform(xAxisScale) || !canCalculateAffineTransform(yAxisScale)) {\n      return;\n    }\n    var xScaleExtent = xAxisScale.getExtent();\n    var yScaleExtent = yAxisScale.getExtent();\n    var start = this.dataToPoint([xScaleExtent[0], yScaleExtent[0]]);\n    var end = this.dataToPoint([xScaleExtent[1], yScaleExtent[1]]);\n    var xScaleSpan = xScaleExtent[1] - xScaleExtent[0];\n    var yScaleSpan = yScaleExtent[1] - yScaleExtent[0];\n    if (!xScaleSpan || !yScaleSpan) {\n      return;\n    }\n    // Accelerate data to point calculation on the special large time series data.\n    var scaleX = (end[0] - start[0]) / xScaleSpan;\n    var scaleY = (end[1] - start[1]) / yScaleSpan;\n    var translateX = start[0] - xScaleExtent[0] * scaleX;\n    var translateY = start[1] - yScaleExtent[0] * scaleY;\n    var m = this._transform = [scaleX, 0, 0, scaleY, translateX, translateY];\n    this._invTransform = invert([], m);\n  };\n  /**\n   * Base axis will be used on stacking.\n   */\n  Cartesian2D.prototype.getBaseAxis = function () {\n    return this.getAxesByScale('ordinal')[0] || this.getAxesByScale('time')[0] || this.getAxis('x');\n  };\n  Cartesian2D.prototype.containPoint = function (point) {\n    var axisX = this.getAxis('x');\n    var axisY = this.getAxis('y');\n    return axisX.contain(axisX.toLocalCoord(point[0])) && axisY.contain(axisY.toLocalCoord(point[1]));\n  };\n  Cartesian2D.prototype.containData = function (data) {\n    return this.getAxis('x').containData(data[0]) && this.getAxis('y').containData(data[1]);\n  };\n  Cartesian2D.prototype.containZone = function (data1, data2) {\n    var zoneDiag1 = this.dataToPoint(data1);\n    var zoneDiag2 = this.dataToPoint(data2);\n    var area = this.getArea();\n    var zone = new BoundingRect(zoneDiag1[0], zoneDiag1[1], zoneDiag2[0] - zoneDiag1[0], zoneDiag2[1] - zoneDiag1[1]);\n    return area.intersect(zone);\n  };\n  Cartesian2D.prototype.dataToPoint = function (data, clamp, out) {\n    out = out || [];\n    var xVal = data[0];\n    var yVal = data[1];\n    // Fast path\n    if (this._transform\n    // It's supported that if data is like `[Inifity, 123]`, where only Y pixel calculated.\n    && xVal != null && isFinite(xVal) && yVal != null && isFinite(yVal)) {\n      return applyTransform(out, data, this._transform);\n    }\n    var xAxis = this.getAxis('x');\n    var yAxis = this.getAxis('y');\n    out[0] = xAxis.toGlobalCoord(xAxis.dataToCoord(xVal, clamp));\n    out[1] = yAxis.toGlobalCoord(yAxis.dataToCoord(yVal, clamp));\n    return out;\n  };\n  Cartesian2D.prototype.clampData = function (data, out) {\n    var xScale = this.getAxis('x').scale;\n    var yScale = this.getAxis('y').scale;\n    var xAxisExtent = xScale.getExtent();\n    var yAxisExtent = yScale.getExtent();\n    var x = xScale.parse(data[0]);\n    var y = yScale.parse(data[1]);\n    out = out || [];\n    out[0] = Math.min(Math.max(Math.min(xAxisExtent[0], xAxisExtent[1]), x), Math.max(xAxisExtent[0], xAxisExtent[1]));\n    out[1] = Math.min(Math.max(Math.min(yAxisExtent[0], yAxisExtent[1]), y), Math.max(yAxisExtent[0], yAxisExtent[1]));\n    return out;\n  };\n  Cartesian2D.prototype.pointToData = function (point, clamp) {\n    var out = [];\n    if (this._invTransform) {\n      return applyTransform(out, point, this._invTransform);\n    }\n    var xAxis = this.getAxis('x');\n    var yAxis = this.getAxis('y');\n    out[0] = xAxis.coordToData(xAxis.toLocalCoord(point[0]), clamp);\n    out[1] = yAxis.coordToData(yAxis.toLocalCoord(point[1]), clamp);\n    return out;\n  };\n  Cartesian2D.prototype.getOtherAxis = function (axis) {\n    return this.getAxis(axis.dim === 'x' ? 'y' : 'x');\n  };\n  /**\n   * Get rect area of cartesian.\n   * Area will have a contain function to determine if a point is in the coordinate system.\n   */\n  Cartesian2D.prototype.getArea = function (tolerance) {\n    tolerance = tolerance || 0;\n    var xExtent = this.getAxis('x').getGlobalExtent();\n    var yExtent = this.getAxis('y').getGlobalExtent();\n    var x = Math.min(xExtent[0], xExtent[1]) - tolerance;\n    var y = Math.min(yExtent[0], yExtent[1]) - tolerance;\n    var width = Math.max(xExtent[0], xExtent[1]) - x + tolerance;\n    var height = Math.max(yExtent[0], yExtent[1]) - y + tolerance;\n    return new BoundingRect(x, y, width, height);\n  };\n  return Cartesian2D;\n}(Cartesian);\n;\nexport default Cartesian2D;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,SAAS,MAAM,gBAAgB;AACtC,SAASC,MAAM,QAAQ,4BAA4B;AACnD,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,OAAO,IAAIC,qBAAqB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AAC7C,SAASC,2BAA2BA,CAACC,KAAK,EAAE;EAC1C,OAAOA,KAAK,CAACC,IAAI,KAAK,UAAU,IAAID,KAAK,CAACC,IAAI,KAAK,MAAM;AAC3D;AACA,IAAIC,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/CV,SAAS,CAACS,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACrB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACH,IAAI,GAAG,aAAa;IAC1BG,KAAK,CAACG,UAAU,GAAGT,qBAAqB;IACxC,OAAOM,KAAK;EACd;EACA;AACF;AACA;AACA;EACEF,WAAW,CAACM,SAAS,CAACC,mBAAmB,GAAG,YAAY;IACtD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,aAAa,GAAG,IAAI;IAC3C,IAAIC,UAAU,GAAG,IAAI,CAACC,OAAO,CAAC,GAAG,CAAC,CAACb,KAAK;IACxC,IAAIc,UAAU,GAAG,IAAI,CAACD,OAAO,CAAC,GAAG,CAAC,CAACb,KAAK;IACxC,IAAI,CAACD,2BAA2B,CAACa,UAAU,CAAC,IAAI,CAACb,2BAA2B,CAACe,UAAU,CAAC,EAAE;MACxF;IACF;IACA,IAAIC,YAAY,GAAGH,UAAU,CAACI,SAAS,CAAC,CAAC;IACzC,IAAIC,YAAY,GAAGH,UAAU,CAACE,SAAS,CAAC,CAAC;IACzC,IAAIE,KAAK,GAAG,IAAI,CAACC,WAAW,CAAC,CAACJ,YAAY,CAAC,CAAC,CAAC,EAAEE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,IAAIG,GAAG,GAAG,IAAI,CAACD,WAAW,CAAC,CAACJ,YAAY,CAAC,CAAC,CAAC,EAAEE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,IAAII,UAAU,GAAGN,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC;IAClD,IAAIO,UAAU,GAAGL,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC;IAClD,IAAI,CAACI,UAAU,IAAI,CAACC,UAAU,EAAE;MAC9B;IACF;IACA;IACA,IAAIC,MAAM,GAAG,CAACH,GAAG,CAAC,CAAC,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC,IAAIG,UAAU;IAC7C,IAAIG,MAAM,GAAG,CAACJ,GAAG,CAAC,CAAC,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC,IAAII,UAAU;IAC7C,IAAIG,UAAU,GAAGP,KAAK,CAAC,CAAC,CAAC,GAAGH,YAAY,CAAC,CAAC,CAAC,GAAGQ,MAAM;IACpD,IAAIG,UAAU,GAAGR,KAAK,CAAC,CAAC,CAAC,GAAGD,YAAY,CAAC,CAAC,CAAC,GAAGO,MAAM;IACpD,IAAIG,CAAC,GAAG,IAAI,CAACjB,UAAU,GAAG,CAACa,MAAM,EAAE,CAAC,EAAE,CAAC,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,CAAC;IACxE,IAAI,CAACf,aAAa,GAAGf,MAAM,CAAC,EAAE,EAAE+B,CAAC,CAAC;EACpC,CAAC;EACD;AACF;AACA;EACEzB,WAAW,CAACM,SAAS,CAACoB,WAAW,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAACA,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAChB,OAAO,CAAC,GAAG,CAAC;EACjG,CAAC;EACDX,WAAW,CAACM,SAAS,CAACsB,YAAY,GAAG,UAAUC,KAAK,EAAE;IACpD,IAAIC,KAAK,GAAG,IAAI,CAACnB,OAAO,CAAC,GAAG,CAAC;IAC7B,IAAIoB,KAAK,GAAG,IAAI,CAACpB,OAAO,CAAC,GAAG,CAAC;IAC7B,OAAOmB,KAAK,CAACE,OAAO,CAACF,KAAK,CAACG,YAAY,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIE,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,YAAY,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACnG,CAAC;EACD7B,WAAW,CAACM,SAAS,CAAC4B,WAAW,GAAG,UAAUC,IAAI,EAAE;IAClD,OAAO,IAAI,CAACxB,OAAO,CAAC,GAAG,CAAC,CAACuB,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAACxB,OAAO,CAAC,GAAG,CAAC,CAACuB,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;EACzF,CAAC;EACDnC,WAAW,CAACM,SAAS,CAAC8B,WAAW,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAC1D,IAAIC,SAAS,GAAG,IAAI,CAACtB,WAAW,CAACoB,KAAK,CAAC;IACvC,IAAIG,SAAS,GAAG,IAAI,CAACvB,WAAW,CAACqB,KAAK,CAAC;IACvC,IAAIG,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IACzB,IAAIC,IAAI,GAAG,IAAInD,YAAY,CAAC+C,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEC,SAAS,CAAC,CAAC,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,EAAEC,SAAS,CAAC,CAAC,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,CAAC;IACjH,OAAOE,IAAI,CAACG,SAAS,CAACD,IAAI,CAAC;EAC7B,CAAC;EACD3C,WAAW,CAACM,SAAS,CAACW,WAAW,GAAG,UAAUkB,IAAI,EAAEU,KAAK,EAAEC,GAAG,EAAE;IAC9DA,GAAG,GAAGA,GAAG,IAAI,EAAE;IACf,IAAIC,IAAI,GAAGZ,IAAI,CAAC,CAAC,CAAC;IAClB,IAAIa,IAAI,GAAGb,IAAI,CAAC,CAAC,CAAC;IAClB;IACA,IAAI,IAAI,CAAC3B;IACT;IAAA,GACGuC,IAAI,IAAI,IAAI,IAAIE,QAAQ,CAACF,IAAI,CAAC,IAAIC,IAAI,IAAI,IAAI,IAAIC,QAAQ,CAACD,IAAI,CAAC,EAAE;MACnE,OAAOrD,cAAc,CAACmD,GAAG,EAAEX,IAAI,EAAE,IAAI,CAAC3B,UAAU,CAAC;IACnD;IACA,IAAI0C,KAAK,GAAG,IAAI,CAACvC,OAAO,CAAC,GAAG,CAAC;IAC7B,IAAIwC,KAAK,GAAG,IAAI,CAACxC,OAAO,CAAC,GAAG,CAAC;IAC7BmC,GAAG,CAAC,CAAC,CAAC,GAAGI,KAAK,CAACE,aAAa,CAACF,KAAK,CAACG,WAAW,CAACN,IAAI,EAAEF,KAAK,CAAC,CAAC;IAC5DC,GAAG,CAAC,CAAC,CAAC,GAAGK,KAAK,CAACC,aAAa,CAACD,KAAK,CAACE,WAAW,CAACL,IAAI,EAAEH,KAAK,CAAC,CAAC;IAC5D,OAAOC,GAAG;EACZ,CAAC;EACD9C,WAAW,CAACM,SAAS,CAACgD,SAAS,GAAG,UAAUnB,IAAI,EAAEW,GAAG,EAAE;IACrD,IAAIS,MAAM,GAAG,IAAI,CAAC5C,OAAO,CAAC,GAAG,CAAC,CAACb,KAAK;IACpC,IAAI0D,MAAM,GAAG,IAAI,CAAC7C,OAAO,CAAC,GAAG,CAAC,CAACb,KAAK;IACpC,IAAI2D,WAAW,GAAGF,MAAM,CAACzC,SAAS,CAAC,CAAC;IACpC,IAAI4C,WAAW,GAAGF,MAAM,CAAC1C,SAAS,CAAC,CAAC;IACpC,IAAI6C,CAAC,GAAGJ,MAAM,CAACK,KAAK,CAACzB,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAI0B,CAAC,GAAGL,MAAM,CAACI,KAAK,CAACzB,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7BW,GAAG,GAAGA,GAAG,IAAI,EAAE;IACfA,GAAG,CAAC,CAAC,CAAC,GAAGgB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACF,IAAI,CAACC,GAAG,CAACN,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC,EAAEE,CAAC,CAAC,EAAEG,IAAI,CAACE,GAAG,CAACP,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAClHX,GAAG,CAAC,CAAC,CAAC,GAAGgB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACF,IAAI,CAACC,GAAG,CAACL,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC,EAAEG,CAAC,CAAC,EAAEC,IAAI,CAACE,GAAG,CAACN,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAClH,OAAOZ,GAAG;EACZ,CAAC;EACD9C,WAAW,CAACM,SAAS,CAAC2D,WAAW,GAAG,UAAUpC,KAAK,EAAEgB,KAAK,EAAE;IAC1D,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAI,IAAI,CAACrC,aAAa,EAAE;MACtB,OAAOd,cAAc,CAACmD,GAAG,EAAEjB,KAAK,EAAE,IAAI,CAACpB,aAAa,CAAC;IACvD;IACA,IAAIyC,KAAK,GAAG,IAAI,CAACvC,OAAO,CAAC,GAAG,CAAC;IAC7B,IAAIwC,KAAK,GAAG,IAAI,CAACxC,OAAO,CAAC,GAAG,CAAC;IAC7BmC,GAAG,CAAC,CAAC,CAAC,GAAGI,KAAK,CAACgB,WAAW,CAAChB,KAAK,CAACjB,YAAY,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEgB,KAAK,CAAC;IAC/DC,GAAG,CAAC,CAAC,CAAC,GAAGK,KAAK,CAACe,WAAW,CAACf,KAAK,CAAClB,YAAY,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEgB,KAAK,CAAC;IAC/D,OAAOC,GAAG;EACZ,CAAC;EACD9C,WAAW,CAACM,SAAS,CAAC6D,YAAY,GAAG,UAAUC,IAAI,EAAE;IACnD,OAAO,IAAI,CAACzD,OAAO,CAACyD,IAAI,CAACC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACnD,CAAC;EACD;AACF;AACA;AACA;EACErE,WAAW,CAACM,SAAS,CAACoC,OAAO,GAAG,UAAU4B,SAAS,EAAE;IACnDA,SAAS,GAAGA,SAAS,IAAI,CAAC;IAC1B,IAAIC,OAAO,GAAG,IAAI,CAAC5D,OAAO,CAAC,GAAG,CAAC,CAAC6D,eAAe,CAAC,CAAC;IACjD,IAAIC,OAAO,GAAG,IAAI,CAAC9D,OAAO,CAAC,GAAG,CAAC,CAAC6D,eAAe,CAAC,CAAC;IACjD,IAAIb,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACQ,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGD,SAAS;IACpD,IAAIT,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACU,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGH,SAAS;IACpD,IAAII,KAAK,GAAGZ,IAAI,CAACE,GAAG,CAACO,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGZ,CAAC,GAAGW,SAAS;IAC5D,IAAIK,MAAM,GAAGb,IAAI,CAACE,GAAG,CAACS,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGZ,CAAC,GAAGS,SAAS;IAC7D,OAAO,IAAI9E,YAAY,CAACmE,CAAC,EAAEE,CAAC,EAAEa,KAAK,EAAEC,MAAM,CAAC;EAC9C,CAAC;EACD,OAAO3E,WAAW;AACpB,CAAC,CAACP,SAAS,CAAC;AACZ;AACA,eAAeO,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}