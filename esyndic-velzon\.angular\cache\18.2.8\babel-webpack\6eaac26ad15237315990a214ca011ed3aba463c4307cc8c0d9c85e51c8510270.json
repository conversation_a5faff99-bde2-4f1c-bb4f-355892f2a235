{"ast": null, "code": "(function (a, b) {\n  if (\"function\" == typeof define && define.amd) define([], b);else if (\"undefined\" != typeof exports) b();else {\n    b(), a.FileSaver = {\n      exports: {}\n    }.exports;\n  }\n})(this, function () {\n  \"use strict\";\n\n  function b(a, b) {\n    return \"undefined\" == typeof b ? b = {\n      autoBom: !1\n    } : \"object\" != typeof b && (console.warn(\"Deprecated: Expected third argument to be a object\"), b = {\n      autoBom: !b\n    }), b.autoBom && /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(a.type) ? new Blob([\"\\uFEFF\", a], {\n      type: a.type\n    }) : a;\n  }\n  function c(a, b, c) {\n    var d = new XMLHttpRequest();\n    d.open(\"GET\", a), d.responseType = \"blob\", d.onload = function () {\n      g(d.response, b, c);\n    }, d.onerror = function () {\n      console.error(\"could not download file\");\n    }, d.send();\n  }\n  function d(a) {\n    var b = new XMLHttpRequest();\n    b.open(\"HEAD\", a, !1);\n    try {\n      b.send();\n    } catch (a) {}\n    return 200 <= b.status && 299 >= b.status;\n  }\n  function e(a) {\n    try {\n      a.dispatchEvent(new MouseEvent(\"click\"));\n    } catch (c) {\n      var b = document.createEvent(\"MouseEvents\");\n      b.initMouseEvent(\"click\", !0, !0, window, 0, 0, 0, 80, 20, !1, !1, !1, !1, 0, null), a.dispatchEvent(b);\n    }\n  }\n  var f = \"object\" == typeof window && window.window === window ? window : \"object\" == typeof self && self.self === self ? self : \"object\" == typeof global && global.global === global ? global : void 0,\n    a = f.navigator && /Macintosh/.test(navigator.userAgent) && /AppleWebKit/.test(navigator.userAgent) && !/Safari/.test(navigator.userAgent),\n    g = f.saveAs || (\"object\" != typeof window || window !== f ? function () {} : \"download\" in HTMLAnchorElement.prototype && !a ? function (b, g, h) {\n      var i = f.URL || f.webkitURL,\n        j = document.createElement(\"a\");\n      g = g || b.name || \"download\", j.download = g, j.rel = \"noopener\", \"string\" == typeof b ? (j.href = b, j.origin === location.origin ? e(j) : d(j.href) ? c(b, g, h) : e(j, j.target = \"_blank\")) : (j.href = i.createObjectURL(b), setTimeout(function () {\n        i.revokeObjectURL(j.href);\n      }, 4E4), setTimeout(function () {\n        e(j);\n      }, 0));\n    } : \"msSaveOrOpenBlob\" in navigator ? function (f, g, h) {\n      if (g = g || f.name || \"download\", \"string\" != typeof f) navigator.msSaveOrOpenBlob(b(f, h), g);else if (d(f)) c(f, g, h);else {\n        var i = document.createElement(\"a\");\n        i.href = f, i.target = \"_blank\", setTimeout(function () {\n          e(i);\n        });\n      }\n    } : function (b, d, e, g) {\n      if (g = g || open(\"\", \"_blank\"), g && (g.document.title = g.document.body.innerText = \"downloading...\"), \"string\" == typeof b) return c(b, d, e);\n      var h = \"application/octet-stream\" === b.type,\n        i = /constructor/i.test(f.HTMLElement) || f.safari,\n        j = /CriOS\\/[\\d]+/.test(navigator.userAgent);\n      if ((j || h && i || a) && \"undefined\" != typeof FileReader) {\n        var k = new FileReader();\n        k.onloadend = function () {\n          var a = k.result;\n          a = j ? a : a.replace(/^data:[^;]*;/, \"data:attachment/file;\"), g ? g.location.href = a : location = a, g = null;\n        }, k.readAsDataURL(b);\n      } else {\n        var l = f.URL || f.webkitURL,\n          m = l.createObjectURL(b);\n        g ? g.location = m : location.href = m, g = null, setTimeout(function () {\n          l.revokeObjectURL(m);\n        }, 4E4);\n      }\n    });\n  f.saveAs = g.saveAs = g, \"undefined\" != typeof module && (module.exports = g);\n});", "map": {"version": 3, "names": ["a", "b", "define", "amd", "exports", "FileSaver", "autoBom", "console", "warn", "test", "type", "Blob", "c", "d", "XMLHttpRequest", "open", "responseType", "onload", "g", "response", "onerror", "error", "send", "status", "e", "dispatchEvent", "MouseEvent", "document", "createEvent", "initMouseEvent", "window", "f", "self", "global", "navigator", "userAgent", "saveAs", "HTMLAnchorElement", "prototype", "h", "i", "URL", "webkitURL", "j", "createElement", "name", "download", "rel", "href", "origin", "location", "target", "createObjectURL", "setTimeout", "revokeObjectURL", "msSaveOrOpenBlob", "title", "body", "innerText", "HTMLElement", "safari", "FileReader", "k", "onloadend", "result", "replace", "readAsDataURL", "l", "m", "module"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/file-saver/dist/FileSaver.min.js"], "sourcesContent": ["(function(a,b){if(\"function\"==typeof define&&define.amd)define([],b);else if(\"undefined\"!=typeof exports)b();else{b(),a.FileSaver={exports:{}}.exports}})(this,function(){\"use strict\";function b(a,b){return\"undefined\"==typeof b?b={autoBom:!1}:\"object\"!=typeof b&&(console.warn(\"Deprecated: Expected third argument to be a object\"),b={autoBom:!b}),b.autoBom&&/^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(a.type)?new Blob([\"\\uFEFF\",a],{type:a.type}):a}function c(a,b,c){var d=new XMLHttpRequest;d.open(\"GET\",a),d.responseType=\"blob\",d.onload=function(){g(d.response,b,c)},d.onerror=function(){console.error(\"could not download file\")},d.send()}function d(a){var b=new XMLHttpRequest;b.open(\"HEAD\",a,!1);try{b.send()}catch(a){}return 200<=b.status&&299>=b.status}function e(a){try{a.dispatchEvent(new MouseEvent(\"click\"))}catch(c){var b=document.createEvent(\"MouseEvents\");b.initMouseEvent(\"click\",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),a.dispatchEvent(b)}}var f=\"object\"==typeof window&&window.window===window?window:\"object\"==typeof self&&self.self===self?self:\"object\"==typeof global&&global.global===global?global:void 0,a=f.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),g=f.saveAs||(\"object\"!=typeof window||window!==f?function(){}:\"download\"in HTMLAnchorElement.prototype&&!a?function(b,g,h){var i=f.URL||f.webkitURL,j=document.createElement(\"a\");g=g||b.name||\"download\",j.download=g,j.rel=\"noopener\",\"string\"==typeof b?(j.href=b,j.origin===location.origin?e(j):d(j.href)?c(b,g,h):e(j,j.target=\"_blank\")):(j.href=i.createObjectURL(b),setTimeout(function(){i.revokeObjectURL(j.href)},4E4),setTimeout(function(){e(j)},0))}:\"msSaveOrOpenBlob\"in navigator?function(f,g,h){if(g=g||f.name||\"download\",\"string\"!=typeof f)navigator.msSaveOrOpenBlob(b(f,h),g);else if(d(f))c(f,g,h);else{var i=document.createElement(\"a\");i.href=f,i.target=\"_blank\",setTimeout(function(){e(i)})}}:function(b,d,e,g){if(g=g||open(\"\",\"_blank\"),g&&(g.document.title=g.document.body.innerText=\"downloading...\"),\"string\"==typeof b)return c(b,d,e);var h=\"application/octet-stream\"===b.type,i=/constructor/i.test(f.HTMLElement)||f.safari,j=/CriOS\\/[\\d]+/.test(navigator.userAgent);if((j||h&&i||a)&&\"undefined\"!=typeof FileReader){var k=new FileReader;k.onloadend=function(){var a=k.result;a=j?a:a.replace(/^data:[^;]*;/,\"data:attachment/file;\"),g?g.location.href=a:location=a,g=null},k.readAsDataURL(b)}else{var l=f.URL||f.webkitURL,m=l.createObjectURL(b);g?g.location=m:location.href=m,g=null,setTimeout(function(){l.revokeObjectURL(m)},4E4)}});f.saveAs=g.saveAs=g,\"undefined\"!=typeof module&&(module.exports=g)});\n\n"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,IAAG,UAAU,IAAE,OAAOC,MAAM,IAAEA,MAAM,CAACC,GAAG,EAACD,MAAM,CAAC,EAAE,EAACD,CAAC,CAAC,CAAC,KAAK,IAAG,WAAW,IAAE,OAAOG,OAAO,EAACH,CAAC,CAAC,CAAC,CAAC,KAAI;IAACA,CAAC,CAAC,CAAC,EAACD,CAAC,CAACK,SAAS,GAAC;MAACD,OAAO,EAAC,CAAC;IAAC,CAAC,CAACA,OAAO;EAAA;AAAC,CAAC,EAAE,IAAI,EAAC,YAAU;EAAC,YAAY;;EAAC,SAASH,CAACA,CAACD,CAAC,EAACC,CAAC,EAAC;IAAC,OAAM,WAAW,IAAE,OAAOA,CAAC,GAACA,CAAC,GAAC;MAACK,OAAO,EAAC,CAAC;IAAC,CAAC,GAAC,QAAQ,IAAE,OAAOL,CAAC,KAAGM,OAAO,CAACC,IAAI,CAAC,oDAAoD,CAAC,EAACP,CAAC,GAAC;MAACK,OAAO,EAAC,CAACL;IAAC,CAAC,CAAC,EAACA,CAAC,CAACK,OAAO,IAAE,4EAA4E,CAACG,IAAI,CAACT,CAAC,CAACU,IAAI,CAAC,GAAC,IAAIC,IAAI,CAAC,CAAC,QAAQ,EAACX,CAAC,CAAC,EAAC;MAACU,IAAI,EAACV,CAAC,CAACU;IAAI,CAAC,CAAC,GAACV,CAAC;EAAA;EAAC,SAASY,CAACA,CAACZ,CAAC,EAACC,CAAC,EAACW,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIC,cAAc,CAAD,CAAC;IAACD,CAAC,CAACE,IAAI,CAAC,KAAK,EAACf,CAAC,CAAC,EAACa,CAAC,CAACG,YAAY,GAAC,MAAM,EAACH,CAAC,CAACI,MAAM,GAAC,YAAU;MAACC,CAAC,CAACL,CAAC,CAACM,QAAQ,EAAClB,CAAC,EAACW,CAAC,CAAC;IAAA,CAAC,EAACC,CAAC,CAACO,OAAO,GAAC,YAAU;MAACb,OAAO,CAACc,KAAK,CAAC,yBAAyB,CAAC;IAAA,CAAC,EAACR,CAAC,CAACS,IAAI,CAAC,CAAC;EAAA;EAAC,SAAST,CAACA,CAACb,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIa,cAAc,CAAD,CAAC;IAACb,CAAC,CAACc,IAAI,CAAC,MAAM,EAACf,CAAC,EAAC,CAAC,CAAC,CAAC;IAAC,IAAG;MAACC,CAAC,CAACqB,IAAI,CAAC,CAAC;IAAA,CAAC,QAAMtB,CAAC,EAAC,CAAC;IAAC,OAAO,GAAG,IAAEC,CAAC,CAACsB,MAAM,IAAE,GAAG,IAAEtB,CAAC,CAACsB,MAAM;EAAA;EAAC,SAASC,CAACA,CAACxB,CAAC,EAAC;IAAC,IAAG;MAACA,CAAC,CAACyB,aAAa,CAAC,IAAIC,UAAU,CAAC,OAAO,CAAC,CAAC;IAAA,CAAC,QAAMd,CAAC,EAAC;MAAC,IAAIX,CAAC,GAAC0B,QAAQ,CAACC,WAAW,CAAC,aAAa,CAAC;MAAC3B,CAAC,CAAC4B,cAAc,CAAC,OAAO,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACC,MAAM,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC,EAAC9B,CAAC,CAACyB,aAAa,CAACxB,CAAC,CAAC;IAAA;EAAC;EAAC,IAAI8B,CAAC,GAAC,QAAQ,IAAE,OAAOD,MAAM,IAAEA,MAAM,CAACA,MAAM,KAAGA,MAAM,GAACA,MAAM,GAAC,QAAQ,IAAE,OAAOE,IAAI,IAAEA,IAAI,CAACA,IAAI,KAAGA,IAAI,GAACA,IAAI,GAAC,QAAQ,IAAE,OAAOC,MAAM,IAAEA,MAAM,CAACA,MAAM,KAAGA,MAAM,GAACA,MAAM,GAAC,KAAK,CAAC;IAACjC,CAAC,GAAC+B,CAAC,CAACG,SAAS,IAAE,WAAW,CAACzB,IAAI,CAACyB,SAAS,CAACC,SAAS,CAAC,IAAE,aAAa,CAAC1B,IAAI,CAACyB,SAAS,CAACC,SAAS,CAAC,IAAE,CAAC,QAAQ,CAAC1B,IAAI,CAACyB,SAAS,CAACC,SAAS,CAAC;IAACjB,CAAC,GAACa,CAAC,CAACK,MAAM,KAAG,QAAQ,IAAE,OAAON,MAAM,IAAEA,MAAM,KAAGC,CAAC,GAAC,YAAU,CAAC,CAAC,GAAC,UAAU,IAAGM,iBAAiB,CAACC,SAAS,IAAE,CAACtC,CAAC,GAAC,UAASC,CAAC,EAACiB,CAAC,EAACqB,CAAC,EAAC;MAAC,IAAIC,CAAC,GAACT,CAAC,CAACU,GAAG,IAAEV,CAAC,CAACW,SAAS;QAACC,CAAC,GAAChB,QAAQ,CAACiB,aAAa,CAAC,GAAG,CAAC;MAAC1B,CAAC,GAACA,CAAC,IAAEjB,CAAC,CAAC4C,IAAI,IAAE,UAAU,EAACF,CAAC,CAACG,QAAQ,GAAC5B,CAAC,EAACyB,CAAC,CAACI,GAAG,GAAC,UAAU,EAAC,QAAQ,IAAE,OAAO9C,CAAC,IAAE0C,CAAC,CAACK,IAAI,GAAC/C,CAAC,EAAC0C,CAAC,CAACM,MAAM,KAAGC,QAAQ,CAACD,MAAM,GAACzB,CAAC,CAACmB,CAAC,CAAC,GAAC9B,CAAC,CAAC8B,CAAC,CAACK,IAAI,CAAC,GAACpC,CAAC,CAACX,CAAC,EAACiB,CAAC,EAACqB,CAAC,CAAC,GAACf,CAAC,CAACmB,CAAC,EAACA,CAAC,CAACQ,MAAM,GAAC,QAAQ,CAAC,KAAGR,CAAC,CAACK,IAAI,GAACR,CAAC,CAACY,eAAe,CAACnD,CAAC,CAAC,EAACoD,UAAU,CAAC,YAAU;QAACb,CAAC,CAACc,eAAe,CAACX,CAAC,CAACK,IAAI,CAAC;MAAA,CAAC,EAAC,GAAG,CAAC,EAACK,UAAU,CAAC,YAAU;QAAC7B,CAAC,CAACmB,CAAC,CAAC;MAAA,CAAC,EAAC,CAAC,CAAC,CAAC;IAAA,CAAC,GAAC,kBAAkB,IAAGT,SAAS,GAAC,UAASH,CAAC,EAACb,CAAC,EAACqB,CAAC,EAAC;MAAC,IAAGrB,CAAC,GAACA,CAAC,IAAEa,CAAC,CAACc,IAAI,IAAE,UAAU,EAAC,QAAQ,IAAE,OAAOd,CAAC,EAACG,SAAS,CAACqB,gBAAgB,CAACtD,CAAC,CAAC8B,CAAC,EAACQ,CAAC,CAAC,EAACrB,CAAC,CAAC,CAAC,KAAK,IAAGL,CAAC,CAACkB,CAAC,CAAC,EAACnB,CAAC,CAACmB,CAAC,EAACb,CAAC,EAACqB,CAAC,CAAC,CAAC,KAAI;QAAC,IAAIC,CAAC,GAACb,QAAQ,CAACiB,aAAa,CAAC,GAAG,CAAC;QAACJ,CAAC,CAACQ,IAAI,GAACjB,CAAC,EAACS,CAAC,CAACW,MAAM,GAAC,QAAQ,EAACE,UAAU,CAAC,YAAU;UAAC7B,CAAC,CAACgB,CAAC,CAAC;QAAA,CAAC,CAAC;MAAA;IAAC,CAAC,GAAC,UAASvC,CAAC,EAACY,CAAC,EAACW,CAAC,EAACN,CAAC,EAAC;MAAC,IAAGA,CAAC,GAACA,CAAC,IAAEH,IAAI,CAAC,EAAE,EAAC,QAAQ,CAAC,EAACG,CAAC,KAAGA,CAAC,CAACS,QAAQ,CAAC6B,KAAK,GAACtC,CAAC,CAACS,QAAQ,CAAC8B,IAAI,CAACC,SAAS,GAAC,gBAAgB,CAAC,EAAC,QAAQ,IAAE,OAAOzD,CAAC,EAAC,OAAOW,CAAC,CAACX,CAAC,EAACY,CAAC,EAACW,CAAC,CAAC;MAAC,IAAIe,CAAC,GAAC,0BAA0B,KAAGtC,CAAC,CAACS,IAAI;QAAC8B,CAAC,GAAC,cAAc,CAAC/B,IAAI,CAACsB,CAAC,CAAC4B,WAAW,CAAC,IAAE5B,CAAC,CAAC6B,MAAM;QAACjB,CAAC,GAAC,cAAc,CAAClC,IAAI,CAACyB,SAAS,CAACC,SAAS,CAAC;MAAC,IAAG,CAACQ,CAAC,IAAEJ,CAAC,IAAEC,CAAC,IAAExC,CAAC,KAAG,WAAW,IAAE,OAAO6D,UAAU,EAAC;QAAC,IAAIC,CAAC,GAAC,IAAID,UAAU,CAAD,CAAC;QAACC,CAAC,CAACC,SAAS,GAAC,YAAU;UAAC,IAAI/D,CAAC,GAAC8D,CAAC,CAACE,MAAM;UAAChE,CAAC,GAAC2C,CAAC,GAAC3C,CAAC,GAACA,CAAC,CAACiE,OAAO,CAAC,cAAc,EAAC,uBAAuB,CAAC,EAAC/C,CAAC,GAACA,CAAC,CAACgC,QAAQ,CAACF,IAAI,GAAChD,CAAC,GAACkD,QAAQ,GAAClD,CAAC,EAACkB,CAAC,GAAC,IAAI;QAAA,CAAC,EAAC4C,CAAC,CAACI,aAAa,CAACjE,CAAC,CAAC;MAAA,CAAC,MAAI;QAAC,IAAIkE,CAAC,GAACpC,CAAC,CAACU,GAAG,IAAEV,CAAC,CAACW,SAAS;UAAC0B,CAAC,GAACD,CAAC,CAACf,eAAe,CAACnD,CAAC,CAAC;QAACiB,CAAC,GAACA,CAAC,CAACgC,QAAQ,GAACkB,CAAC,GAAClB,QAAQ,CAACF,IAAI,GAACoB,CAAC,EAAClD,CAAC,GAAC,IAAI,EAACmC,UAAU,CAAC,YAAU;UAACc,CAAC,CAACb,eAAe,CAACc,CAAC,CAAC;QAAA,CAAC,EAAC,GAAG,CAAC;MAAA;IAAC,CAAC,CAAC;EAACrC,CAAC,CAACK,MAAM,GAAClB,CAAC,CAACkB,MAAM,GAAClB,CAAC,EAAC,WAAW,IAAE,OAAOmD,MAAM,KAAGA,MAAM,CAACjE,OAAO,GAACc,CAAC,CAAC;AAAA,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}