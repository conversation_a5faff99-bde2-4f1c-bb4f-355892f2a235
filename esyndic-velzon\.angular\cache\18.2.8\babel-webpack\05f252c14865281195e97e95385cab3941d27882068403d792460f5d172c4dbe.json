{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { map } from 'zrender/lib/core/util.js';\nimport createRenderPlanner from '../chart/helper/createRenderPlanner.js';\nimport { isDimensionStacked } from '../data/helper/dataStackHelper.js';\nimport { createFloat32Array } from '../util/vendor.js';\nexport default function pointsLayout(seriesType, forceStoreInTypedArray) {\n  return {\n    seriesType: seriesType,\n    plan: createRenderPlanner(),\n    reset: function (seriesModel) {\n      var data = seriesModel.getData();\n      var coordSys = seriesModel.coordinateSystem;\n      var pipelineContext = seriesModel.pipelineContext;\n      var useTypedArray = forceStoreInTypedArray || pipelineContext.large;\n      if (!coordSys) {\n        return;\n      }\n      var dims = map(coordSys.dimensions, function (dim) {\n        return data.mapDimension(dim);\n      }).slice(0, 2);\n      var dimLen = dims.length;\n      var stackResultDim = data.getCalculationInfo('stackResultDimension');\n      if (isDimensionStacked(data, dims[0])) {\n        dims[0] = stackResultDim;\n      }\n      if (isDimensionStacked(data, dims[1])) {\n        dims[1] = stackResultDim;\n      }\n      var store = data.getStore();\n      var dimIdx0 = data.getDimensionIndex(dims[0]);\n      var dimIdx1 = data.getDimensionIndex(dims[1]);\n      return dimLen && {\n        progress: function (params, data) {\n          var segCount = params.end - params.start;\n          var points = useTypedArray && createFloat32Array(segCount * dimLen);\n          var tmpIn = [];\n          var tmpOut = [];\n          for (var i = params.start, offset = 0; i < params.end; i++) {\n            var point = void 0;\n            if (dimLen === 1) {\n              var x = store.get(dimIdx0, i);\n              // NOTE: Make sure the second parameter is null to use default strategy.\n              point = coordSys.dataToPoint(x, null, tmpOut);\n            } else {\n              tmpIn[0] = store.get(dimIdx0, i);\n              tmpIn[1] = store.get(dimIdx1, i);\n              // Let coordinate system to handle the NaN data.\n              point = coordSys.dataToPoint(tmpIn, null, tmpOut);\n            }\n            if (useTypedArray) {\n              points[offset++] = point[0];\n              points[offset++] = point[1];\n            } else {\n              data.setItemLayout(i, point.slice());\n            }\n          }\n          useTypedArray && data.setLayout('points', points);\n        }\n      };\n    }\n  };\n}\n;", "map": {"version": 3, "names": ["map", "createRenderPlanner", "isDimensionStacked", "createFloat32Array", "pointsLayout", "seriesType", "forceStoreInTypedArray", "plan", "reset", "seriesModel", "data", "getData", "coordSys", "coordinateSystem", "pipelineContext", "useTypedArray", "large", "dims", "dimensions", "dim", "mapDimension", "slice", "dim<PERSON>en", "length", "stackResultDim", "getCalculationInfo", "store", "getStore", "dimIdx0", "getDimensionIndex", "dimIdx1", "progress", "params", "segCount", "end", "start", "points", "tmpIn", "tmpOut", "i", "offset", "point", "x", "get", "dataToPoint", "setItemLayout", "setLayout"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/layout/points.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { map } from 'zrender/lib/core/util.js';\nimport createRenderPlanner from '../chart/helper/createRenderPlanner.js';\nimport { isDimensionStacked } from '../data/helper/dataStackHelper.js';\nimport { createFloat32Array } from '../util/vendor.js';\nexport default function pointsLayout(seriesType, forceStoreInTypedArray) {\n  return {\n    seriesType: seriesType,\n    plan: createRenderPlanner(),\n    reset: function (seriesModel) {\n      var data = seriesModel.getData();\n      var coordSys = seriesModel.coordinateSystem;\n      var pipelineContext = seriesModel.pipelineContext;\n      var useTypedArray = forceStoreInTypedArray || pipelineContext.large;\n      if (!coordSys) {\n        return;\n      }\n      var dims = map(coordSys.dimensions, function (dim) {\n        return data.mapDimension(dim);\n      }).slice(0, 2);\n      var dimLen = dims.length;\n      var stackResultDim = data.getCalculationInfo('stackResultDimension');\n      if (isDimensionStacked(data, dims[0])) {\n        dims[0] = stackResultDim;\n      }\n      if (isDimensionStacked(data, dims[1])) {\n        dims[1] = stackResultDim;\n      }\n      var store = data.getStore();\n      var dimIdx0 = data.getDimensionIndex(dims[0]);\n      var dimIdx1 = data.getDimensionIndex(dims[1]);\n      return dimLen && {\n        progress: function (params, data) {\n          var segCount = params.end - params.start;\n          var points = useTypedArray && createFloat32Array(segCount * dimLen);\n          var tmpIn = [];\n          var tmpOut = [];\n          for (var i = params.start, offset = 0; i < params.end; i++) {\n            var point = void 0;\n            if (dimLen === 1) {\n              var x = store.get(dimIdx0, i);\n              // NOTE: Make sure the second parameter is null to use default strategy.\n              point = coordSys.dataToPoint(x, null, tmpOut);\n            } else {\n              tmpIn[0] = store.get(dimIdx0, i);\n              tmpIn[1] = store.get(dimIdx1, i);\n              // Let coordinate system to handle the NaN data.\n              point = coordSys.dataToPoint(tmpIn, null, tmpOut);\n            }\n            if (useTypedArray) {\n              points[offset++] = point[0];\n              points[offset++] = point[1];\n            } else {\n              data.setItemLayout(i, point.slice());\n            }\n          }\n          useTypedArray && data.setLayout('points', points);\n        }\n      };\n    }\n  };\n}\n;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,GAAG,QAAQ,0BAA0B;AAC9C,OAAOC,mBAAmB,MAAM,wCAAwC;AACxE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,eAAe,SAASC,YAAYA,CAACC,UAAU,EAAEC,sBAAsB,EAAE;EACvE,OAAO;IACLD,UAAU,EAAEA,UAAU;IACtBE,IAAI,EAAEN,mBAAmB,CAAC,CAAC;IAC3BO,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAE;MAC5B,IAAIC,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;MAChC,IAAIC,QAAQ,GAAGH,WAAW,CAACI,gBAAgB;MAC3C,IAAIC,eAAe,GAAGL,WAAW,CAACK,eAAe;MACjD,IAAIC,aAAa,GAAGT,sBAAsB,IAAIQ,eAAe,CAACE,KAAK;MACnE,IAAI,CAACJ,QAAQ,EAAE;QACb;MACF;MACA,IAAIK,IAAI,GAAGjB,GAAG,CAACY,QAAQ,CAACM,UAAU,EAAE,UAAUC,GAAG,EAAE;QACjD,OAAOT,IAAI,CAACU,YAAY,CAACD,GAAG,CAAC;MAC/B,CAAC,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MACd,IAAIC,MAAM,GAAGL,IAAI,CAACM,MAAM;MACxB,IAAIC,cAAc,GAAGd,IAAI,CAACe,kBAAkB,CAAC,sBAAsB,CAAC;MACpE,IAAIvB,kBAAkB,CAACQ,IAAI,EAAEO,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QACrCA,IAAI,CAAC,CAAC,CAAC,GAAGO,cAAc;MAC1B;MACA,IAAItB,kBAAkB,CAACQ,IAAI,EAAEO,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QACrCA,IAAI,CAAC,CAAC,CAAC,GAAGO,cAAc;MAC1B;MACA,IAAIE,KAAK,GAAGhB,IAAI,CAACiB,QAAQ,CAAC,CAAC;MAC3B,IAAIC,OAAO,GAAGlB,IAAI,CAACmB,iBAAiB,CAACZ,IAAI,CAAC,CAAC,CAAC,CAAC;MAC7C,IAAIa,OAAO,GAAGpB,IAAI,CAACmB,iBAAiB,CAACZ,IAAI,CAAC,CAAC,CAAC,CAAC;MAC7C,OAAOK,MAAM,IAAI;QACfS,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAEtB,IAAI,EAAE;UAChC,IAAIuB,QAAQ,GAAGD,MAAM,CAACE,GAAG,GAAGF,MAAM,CAACG,KAAK;UACxC,IAAIC,MAAM,GAAGrB,aAAa,IAAIZ,kBAAkB,CAAC8B,QAAQ,GAAGX,MAAM,CAAC;UACnE,IAAIe,KAAK,GAAG,EAAE;UACd,IAAIC,MAAM,GAAG,EAAE;UACf,KAAK,IAAIC,CAAC,GAAGP,MAAM,CAACG,KAAK,EAAEK,MAAM,GAAG,CAAC,EAAED,CAAC,GAAGP,MAAM,CAACE,GAAG,EAAEK,CAAC,EAAE,EAAE;YAC1D,IAAIE,KAAK,GAAG,KAAK,CAAC;YAClB,IAAInB,MAAM,KAAK,CAAC,EAAE;cAChB,IAAIoB,CAAC,GAAGhB,KAAK,CAACiB,GAAG,CAACf,OAAO,EAAEW,CAAC,CAAC;cAC7B;cACAE,KAAK,GAAG7B,QAAQ,CAACgC,WAAW,CAACF,CAAC,EAAE,IAAI,EAAEJ,MAAM,CAAC;YAC/C,CAAC,MAAM;cACLD,KAAK,CAAC,CAAC,CAAC,GAAGX,KAAK,CAACiB,GAAG,CAACf,OAAO,EAAEW,CAAC,CAAC;cAChCF,KAAK,CAAC,CAAC,CAAC,GAAGX,KAAK,CAACiB,GAAG,CAACb,OAAO,EAAES,CAAC,CAAC;cAChC;cACAE,KAAK,GAAG7B,QAAQ,CAACgC,WAAW,CAACP,KAAK,EAAE,IAAI,EAAEC,MAAM,CAAC;YACnD;YACA,IAAIvB,aAAa,EAAE;cACjBqB,MAAM,CAACI,MAAM,EAAE,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC;cAC3BL,MAAM,CAACI,MAAM,EAAE,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC;YAC7B,CAAC,MAAM;cACL/B,IAAI,CAACmC,aAAa,CAACN,CAAC,EAAEE,KAAK,CAACpB,KAAK,CAAC,CAAC,CAAC;YACtC;UACF;UACAN,aAAa,IAAIL,IAAI,CAACoC,SAAS,CAAC,QAAQ,EAAEV,MAAM,CAAC;QACnD;MACF,CAAC;IACH;EACF,CAAC;AACH;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}