package com.esyndic.repository;

import com.esyndic.entity.AssemblyVote;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface AssemblyVoteRepository extends JpaRepository<AssemblyVote, UUID> {

    List<AssemblyVote> findByAssemblyId(UUID assemblyId);

    List<AssemblyVote> findByUserId(UUID userId);

    List<AssemblyVote> findByApartmentId(UUID apartmentId);

    List<AssemblyVote> findByAssemblyIdAndAgendaItem(UUID assemblyId, String agendaItem);

    Optional<AssemblyVote> findByAssemblyIdAndUserIdAndAgendaItem(UUID assemblyId, UUID userId, String agendaItem);

    List<AssemblyVote> findByAssemblyIdAndVote(UUID assemblyId, AssemblyVote.VoteType vote);

    List<AssemblyVote> findByAssemblyIdAndAgendaItemAndVote(UUID assemblyId, String agendaItem, AssemblyVote.VoteType vote);

    @Query("SELECT COUNT(av) FROM AssemblyVote av WHERE av.assembly.id = :assemblyId AND av.agendaItem = :agendaItem AND av.vote = 'YES'")
    long countYesVotesByAssemblyIdAndAgendaItem(@Param("assemblyId") UUID assemblyId, @Param("agendaItem") String agendaItem);

    @Query("SELECT COUNT(av) FROM AssemblyVote av WHERE av.assembly.id = :assemblyId AND av.agendaItem = :agendaItem AND av.vote = 'NO'")
    long countNoVotesByAssemblyIdAndAgendaItem(@Param("assemblyId") UUID assemblyId, @Param("agendaItem") String agendaItem);

    @Query("SELECT COUNT(av) FROM AssemblyVote av WHERE av.assembly.id = :assemblyId AND av.agendaItem = :agendaItem AND av.vote = 'ABSTAIN'")
    long countAbstainVotesByAssemblyIdAndAgendaItem(@Param("assemblyId") UUID assemblyId, @Param("agendaItem") String agendaItem);

    @Query("SELECT COUNT(av) FROM AssemblyVote av WHERE av.assembly.id = :assemblyId AND av.agendaItem = :agendaItem")
    long countTotalVotesByAssemblyIdAndAgendaItem(@Param("assemblyId") UUID assemblyId, @Param("agendaItem") String agendaItem);

    @Query("SELECT DISTINCT av.agendaItem FROM AssemblyVote av WHERE av.assembly.id = :assemblyId")
    List<String> findDistinctAgendaItemsByAssemblyId(@Param("assemblyId") UUID assemblyId);

    @Query("SELECT av FROM AssemblyVote av WHERE av.assembly.building.id = :buildingId")
    List<AssemblyVote> findByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT av FROM AssemblyVote av WHERE av.user.id = :userId AND av.vote = :voteType")
    List<AssemblyVote> findByUserIdAndVoteType(@Param("userId") UUID userId, @Param("voteType") AssemblyVote.VoteType voteType);

    @Query("SELECT COUNT(av) FROM AssemblyVote av WHERE av.user.id = :userId")
    long countVotesByUserId(@Param("userId") UUID userId);

    @Query("SELECT COUNT(av) FROM AssemblyVote av WHERE av.user.id = :userId AND av.vote = :voteType")
    long countVotesByUserIdAndVoteType(@Param("userId") UUID userId, @Param("voteType") AssemblyVote.VoteType voteType);

    boolean existsByAssemblyIdAndUserIdAndAgendaItem(UUID assemblyId, UUID userId, String agendaItem);

    @Query("SELECT (COUNT(av) * 100.0 / (SELECT COUNT(av2) FROM AssemblyVote av2 WHERE av2.assembly.id = :assemblyId AND av2.agendaItem = :agendaItem)) " +
           "FROM AssemblyVote av WHERE av.assembly.id = :assemblyId AND av.agendaItem = :agendaItem AND av.vote = 'YES'")
    Double getYesVotePercentageByAssemblyIdAndAgendaItem(@Param("assemblyId") UUID assemblyId, @Param("agendaItem") String agendaItem);

    @Query("SELECT av FROM AssemblyVote av WHERE av.assembly.id = :assemblyId ORDER BY av.voteTime DESC")
    List<AssemblyVote> findByAssemblyIdOrderByVoteTimeDesc(@Param("assemblyId") UUID assemblyId);
}
