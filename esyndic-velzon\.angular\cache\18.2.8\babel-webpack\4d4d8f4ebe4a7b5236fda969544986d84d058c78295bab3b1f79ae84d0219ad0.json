{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesDataSimply from '../helper/createSeriesDataSimply.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport { createTooltipMarkup, retrieveVisualColorForTooltipMarker } from '../../component/tooltip/tooltipMarkup.js';\nvar RadarSeriesModel = /** @class */function (_super) {\n  __extends(RadarSeriesModel, _super);\n  function RadarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = RadarSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  // Overwrite\n  RadarSeriesModel.prototype.init = function (option) {\n    _super.prototype.init.apply(this, arguments);\n    // Enable legend selection for each data item\n    // Use a function instead of direct access because data reference may changed\n    this.legendVisualProvider = new LegendVisualProvider(zrUtil.bind(this.getData, this), zrUtil.bind(this.getRawData, this));\n  };\n  RadarSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesDataSimply(this, {\n      generateCoord: 'indicator_',\n      generateCoordCount: Infinity\n    });\n  };\n  RadarSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var coordSys = this.coordinateSystem;\n    var indicatorAxes = coordSys.getIndicatorAxes();\n    var name = this.getData().getName(dataIndex);\n    var nameToDisplay = name === '' ? this.name : name;\n    var markerColor = retrieveVisualColorForTooltipMarker(this, dataIndex);\n    return createTooltipMarkup('section', {\n      header: nameToDisplay,\n      sortBlocks: true,\n      blocks: zrUtil.map(indicatorAxes, function (axis) {\n        var val = data.get(data.mapDimension(axis.dim), dataIndex);\n        return createTooltipMarkup('nameValue', {\n          markerType: 'subItem',\n          markerColor: markerColor,\n          name: axis.name,\n          value: val,\n          sortParam: val\n        });\n      })\n    });\n  };\n  RadarSeriesModel.prototype.getTooltipPosition = function (dataIndex) {\n    if (dataIndex != null) {\n      var data_1 = this.getData();\n      var coordSys = this.coordinateSystem;\n      var values = data_1.getValues(zrUtil.map(coordSys.dimensions, function (dim) {\n        return data_1.mapDimension(dim);\n      }), dataIndex);\n      for (var i = 0, len = values.length; i < len; i++) {\n        if (!isNaN(values[i])) {\n          var indicatorAxes = coordSys.getIndicatorAxes();\n          return coordSys.coordToPoint(indicatorAxes[i].dataToCoord(values[i]), i);\n        }\n      }\n    }\n  };\n  RadarSeriesModel.type = 'series.radar';\n  RadarSeriesModel.dependencies = ['radar'];\n  RadarSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    colorBy: 'data',\n    coordinateSystem: 'radar',\n    legendHoverLink: true,\n    radarIndex: 0,\n    lineStyle: {\n      width: 2,\n      type: 'solid',\n      join: 'round'\n    },\n    label: {\n      position: 'top'\n    },\n    // areaStyle: {\n    // },\n    // itemStyle: {}\n    symbolSize: 8\n    // symbolRotate: null\n  };\n  return RadarSeriesModel;\n}(SeriesModel);\nexport default RadarSeriesModel;", "map": {"version": 3, "names": ["__extends", "SeriesModel", "createSeriesDataSimply", "zrUtil", "LegendVisualProvider", "createTooltipMarkup", "retrieveVisualColorForTooltipMarker", "RadarSeriesModel", "_super", "_this", "apply", "arguments", "type", "hasSymbolVisual", "prototype", "init", "option", "legend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bind", "getData", "getRawData", "getInitialData", "ecModel", "generateCoord", "generateCoordCount", "Infinity", "formatTooltip", "dataIndex", "multipleSeries", "dataType", "data", "coordSys", "coordinateSystem", "indicatorAxes", "getIndicatorAxes", "name", "getName", "nameToDisplay", "markerColor", "header", "sortBlocks", "blocks", "map", "axis", "val", "get", "mapDimension", "dim", "markerType", "value", "sortParam", "getTooltipPosition", "data_1", "values", "getV<PERSON>ues", "dimensions", "i", "len", "length", "isNaN", "coordToPoint", "dataToCoord", "dependencies", "defaultOption", "z", "colorBy", "legendHoverLink", "radarIndex", "lineStyle", "width", "join", "label", "position", "symbolSize"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/chart/radar/RadarSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesDataSimply from '../helper/createSeriesDataSimply.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport { createTooltipMarkup, retrieveVisualColorForTooltipMarker } from '../../component/tooltip/tooltipMarkup.js';\nvar RadarSeriesModel = /** @class */function (_super) {\n  __extends(RadarSeriesModel, _super);\n  function RadarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = RadarSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  // Overwrite\n  RadarSeriesModel.prototype.init = function (option) {\n    _super.prototype.init.apply(this, arguments);\n    // Enable legend selection for each data item\n    // Use a function instead of direct access because data reference may changed\n    this.legendVisualProvider = new LegendVisualProvider(zrUtil.bind(this.getData, this), zrUtil.bind(this.getRawData, this));\n  };\n  RadarSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesDataSimply(this, {\n      generateCoord: 'indicator_',\n      generateCoordCount: Infinity\n    });\n  };\n  RadarSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var coordSys = this.coordinateSystem;\n    var indicatorAxes = coordSys.getIndicatorAxes();\n    var name = this.getData().getName(dataIndex);\n    var nameToDisplay = name === '' ? this.name : name;\n    var markerColor = retrieveVisualColorForTooltipMarker(this, dataIndex);\n    return createTooltipMarkup('section', {\n      header: nameToDisplay,\n      sortBlocks: true,\n      blocks: zrUtil.map(indicatorAxes, function (axis) {\n        var val = data.get(data.mapDimension(axis.dim), dataIndex);\n        return createTooltipMarkup('nameValue', {\n          markerType: 'subItem',\n          markerColor: markerColor,\n          name: axis.name,\n          value: val,\n          sortParam: val\n        });\n      })\n    });\n  };\n  RadarSeriesModel.prototype.getTooltipPosition = function (dataIndex) {\n    if (dataIndex != null) {\n      var data_1 = this.getData();\n      var coordSys = this.coordinateSystem;\n      var values = data_1.getValues(zrUtil.map(coordSys.dimensions, function (dim) {\n        return data_1.mapDimension(dim);\n      }), dataIndex);\n      for (var i = 0, len = values.length; i < len; i++) {\n        if (!isNaN(values[i])) {\n          var indicatorAxes = coordSys.getIndicatorAxes();\n          return coordSys.coordToPoint(indicatorAxes[i].dataToCoord(values[i]), i);\n        }\n      }\n    }\n  };\n  RadarSeriesModel.type = 'series.radar';\n  RadarSeriesModel.dependencies = ['radar'];\n  RadarSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    colorBy: 'data',\n    coordinateSystem: 'radar',\n    legendHoverLink: true,\n    radarIndex: 0,\n    lineStyle: {\n      width: 2,\n      type: 'solid',\n      join: 'round'\n    },\n    label: {\n      position: 'top'\n    },\n    // areaStyle: {\n    // },\n    // itemStyle: {}\n    symbolSize: 8\n    // symbolRotate: null\n  };\n\n  return RadarSeriesModel;\n}(SeriesModel);\nexport default RadarSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,sBAAsB,MAAM,qCAAqC;AACxE,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,SAASC,mBAAmB,EAAEC,mCAAmC,QAAQ,0CAA0C;AACnH,IAAIC,gBAAgB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACpDR,SAAS,CAACO,gBAAgB,EAAEC,MAAM,CAAC;EACnC,SAASD,gBAAgBA,CAAA,EAAG;IAC1B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,gBAAgB,CAACK,IAAI;IAClCH,KAAK,CAACI,eAAe,GAAG,IAAI;IAC5B,OAAOJ,KAAK;EACd;EACA;EACAF,gBAAgB,CAACO,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAE;IAClDR,MAAM,CAACM,SAAS,CAACC,IAAI,CAACL,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC5C;IACA;IACA,IAAI,CAACM,oBAAoB,GAAG,IAAIb,oBAAoB,CAACD,MAAM,CAACe,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC,EAAEhB,MAAM,CAACe,IAAI,CAAC,IAAI,CAACE,UAAU,EAAE,IAAI,CAAC,CAAC;EAC3H,CAAC;EACDb,gBAAgB,CAACO,SAAS,CAACO,cAAc,GAAG,UAAUL,MAAM,EAAEM,OAAO,EAAE;IACrE,OAAOpB,sBAAsB,CAAC,IAAI,EAAE;MAClCqB,aAAa,EAAE,YAAY;MAC3BC,kBAAkB,EAAEC;IACtB,CAAC,CAAC;EACJ,CAAC;EACDlB,gBAAgB,CAACO,SAAS,CAACY,aAAa,GAAG,UAAUC,SAAS,EAAEC,cAAc,EAAEC,QAAQ,EAAE;IACxF,IAAIC,IAAI,GAAG,IAAI,CAACX,OAAO,CAAC,CAAC;IACzB,IAAIY,QAAQ,GAAG,IAAI,CAACC,gBAAgB;IACpC,IAAIC,aAAa,GAAGF,QAAQ,CAACG,gBAAgB,CAAC,CAAC;IAC/C,IAAIC,IAAI,GAAG,IAAI,CAAChB,OAAO,CAAC,CAAC,CAACiB,OAAO,CAACT,SAAS,CAAC;IAC5C,IAAIU,aAAa,GAAGF,IAAI,KAAK,EAAE,GAAG,IAAI,CAACA,IAAI,GAAGA,IAAI;IAClD,IAAIG,WAAW,GAAGhC,mCAAmC,CAAC,IAAI,EAAEqB,SAAS,CAAC;IACtE,OAAOtB,mBAAmB,CAAC,SAAS,EAAE;MACpCkC,MAAM,EAAEF,aAAa;MACrBG,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAEtC,MAAM,CAACuC,GAAG,CAACT,aAAa,EAAE,UAAUU,IAAI,EAAE;QAChD,IAAIC,GAAG,GAAGd,IAAI,CAACe,GAAG,CAACf,IAAI,CAACgB,YAAY,CAACH,IAAI,CAACI,GAAG,CAAC,EAAEpB,SAAS,CAAC;QAC1D,OAAOtB,mBAAmB,CAAC,WAAW,EAAE;UACtC2C,UAAU,EAAE,SAAS;UACrBV,WAAW,EAAEA,WAAW;UACxBH,IAAI,EAAEQ,IAAI,CAACR,IAAI;UACfc,KAAK,EAAEL,GAAG;UACVM,SAAS,EAAEN;QACb,CAAC,CAAC;MACJ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EACDrC,gBAAgB,CAACO,SAAS,CAACqC,kBAAkB,GAAG,UAAUxB,SAAS,EAAE;IACnE,IAAIA,SAAS,IAAI,IAAI,EAAE;MACrB,IAAIyB,MAAM,GAAG,IAAI,CAACjC,OAAO,CAAC,CAAC;MAC3B,IAAIY,QAAQ,GAAG,IAAI,CAACC,gBAAgB;MACpC,IAAIqB,MAAM,GAAGD,MAAM,CAACE,SAAS,CAACnD,MAAM,CAACuC,GAAG,CAACX,QAAQ,CAACwB,UAAU,EAAE,UAAUR,GAAG,EAAE;QAC3E,OAAOK,MAAM,CAACN,YAAY,CAACC,GAAG,CAAC;MACjC,CAAC,CAAC,EAAEpB,SAAS,CAAC;MACd,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGJ,MAAM,CAACK,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QACjD,IAAI,CAACG,KAAK,CAACN,MAAM,CAACG,CAAC,CAAC,CAAC,EAAE;UACrB,IAAIvB,aAAa,GAAGF,QAAQ,CAACG,gBAAgB,CAAC,CAAC;UAC/C,OAAOH,QAAQ,CAAC6B,YAAY,CAAC3B,aAAa,CAACuB,CAAC,CAAC,CAACK,WAAW,CAACR,MAAM,CAACG,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC;QAC1E;MACF;IACF;EACF,CAAC;EACDjD,gBAAgB,CAACK,IAAI,GAAG,cAAc;EACtCL,gBAAgB,CAACuD,YAAY,GAAG,CAAC,OAAO,CAAC;EACzCvD,gBAAgB,CAACwD,aAAa,GAAG;IAC/B;IACAC,CAAC,EAAE,CAAC;IACJC,OAAO,EAAE,MAAM;IACfjC,gBAAgB,EAAE,OAAO;IACzBkC,eAAe,EAAE,IAAI;IACrBC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE;MACTC,KAAK,EAAE,CAAC;MACRzD,IAAI,EAAE,OAAO;MACb0D,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLC,QAAQ,EAAE;IACZ,CAAC;IACD;IACA;IACA;IACAC,UAAU,EAAE;IACZ;EACF,CAAC;EAED,OAAOlE,gBAAgB;AACzB,CAAC,CAACN,WAAW,CAAC;AACd,eAAeM,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}