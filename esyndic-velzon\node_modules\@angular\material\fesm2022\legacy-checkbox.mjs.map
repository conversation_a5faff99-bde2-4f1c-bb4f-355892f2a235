{"version": 3, "file": "legacy-checkbox.mjs", "sources": ["../../../../../../src/material/legacy-checkbox/checkbox.ts", "../../../../../../src/material/legacy-checkbox/checkbox.html", "../../../../../../src/material/legacy-checkbox/checkbox-module.ts", "../../../../../../src/material/legacy-checkbox/legacy-checkbox_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {\n  AfterViewInit,\n  Attribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  forwardRef,\n  Inject,\n  NgZone,\n  OnDestroy,\n  Optional,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {NG_VALUE_ACCESSOR} from '@angular/forms';\nimport {ANIMATION_MODULE_TYPE} from '@angular/platform-browser/animations';\nimport {\n  _MatCheckboxBase,\n  MAT_CHECKBOX_DEFAULT_OPTIONS,\n  MatCheckboxDefaultOptions,\n} from '@angular/material/checkbox';\n\n/**\n * Change event object emitted by a checkbox.\n * @deprecated Use `MatCheckboxChange` from `@angular/material/checkbox` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport class MatLegacyCheckboxChange {\n  /** The source checkbox of the event. */\n  source: MatLegacyCheckbox;\n  /** The new `checked` value of the checkbox. */\n  checked: boolean;\n}\n/**\n * Provider Expression that allows mat-checkbox to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n * @deprecated Use `MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR` from `@angular/material/checkbox` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport const MAT_LEGACY_CHECKBOX_CONTROL_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatLegacyCheckbox),\n  multi: true,\n};\n\n/**\n * A material design checkbox component. Supports all of the functionality of an HTML5 checkbox,\n * and exposes a similar API. A checkbox can be either checked, unchecked, indeterminate, or\n * disabled. Note that all additional accessibility attributes are taken care of by the component,\n * so there is no need to provide them yourself. However, if you want to omit a label and still\n * have the checkbox be accessible, you may supply an [aria-label] input.\n * See: https://material.io/design/components/selection-controls.html\n * @deprecated Use `MatCheckbox` from `@angular/material/checkbox` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Component({\n  selector: 'mat-checkbox',\n  templateUrl: 'checkbox.html',\n  styleUrls: ['checkbox.css'],\n  exportAs: 'matCheckbox',\n  host: {\n    'class': 'mat-checkbox',\n    '[id]': 'id',\n    '[attr.tabindex]': 'null',\n    '[attr.aria-label]': 'null',\n    '[attr.aria-labelledby]': 'null',\n    '[class.mat-checkbox-indeterminate]': 'indeterminate',\n    '[class.mat-checkbox-checked]': 'checked',\n    '[class.mat-checkbox-disabled]': 'disabled',\n    '[class.mat-checkbox-label-before]': 'labelPosition == \"before\"',\n    '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n  },\n  providers: [MAT_LEGACY_CHECKBOX_CONTROL_VALUE_ACCESSOR],\n  inputs: ['disableRipple', 'color', 'tabIndex'],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatLegacyCheckbox\n  extends _MatCheckboxBase<MatLegacyCheckboxChange>\n  implements AfterViewInit, OnDestroy\n{\n  protected _animationClasses = {\n    uncheckedToChecked: 'mat-checkbox-anim-unchecked-checked',\n    uncheckedToIndeterminate: 'mat-checkbox-anim-unchecked-indeterminate',\n    checkedToUnchecked: 'mat-checkbox-anim-checked-unchecked',\n    checkedToIndeterminate: 'mat-checkbox-anim-checked-indeterminate',\n    indeterminateToChecked: 'mat-checkbox-anim-indeterminate-checked',\n    indeterminateToUnchecked: 'mat-checkbox-anim-indeterminate-unchecked',\n  };\n\n  constructor(\n    elementRef: ElementRef<HTMLElement>,\n    changeDetectorRef: ChangeDetectorRef,\n    private _focusMonitor: FocusMonitor,\n    ngZone: NgZone,\n    @Attribute('tabindex') tabIndex: string,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n    @Optional()\n    @Inject(MAT_CHECKBOX_DEFAULT_OPTIONS)\n    options?: MatCheckboxDefaultOptions,\n  ) {\n    super('mat-checkbox-', elementRef, changeDetectorRef, ngZone, tabIndex, animationMode, options);\n  }\n\n  protected _createChangeEvent(isChecked: boolean) {\n    const event = new MatLegacyCheckboxChange();\n    event.source = this;\n    event.checked = isChecked;\n    return event;\n  }\n\n  protected _getAnimationTargetElement() {\n    return this._elementRef.nativeElement;\n  }\n\n  override ngAfterViewInit() {\n    super.ngAfterViewInit();\n\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        this._onBlur();\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n  }\n\n  /**\n   * Event handler for checkbox input element.\n   * Toggles checked state if element is not disabled.\n   * Do not toggle on (change) event since IE doesn't fire change event when\n   *   indeterminate checkbox is clicked.\n   * @param event\n   */\n  _onInputClick(event: Event) {\n    // We have to stop propagation for click events on the visual hidden input element.\n    // By default, when a user clicks on a label element, a generated click event will be\n    // dispatched on the associated input element. Since we are using a label element as our\n    // root container, the click event on the `checkbox` will be executed twice.\n    // The real click event will bubble up, and the generated click event also tries to bubble up.\n    // This will lead to multiple click events.\n    // Preventing bubbling for the second event will solve that issue.\n    event.stopPropagation();\n    super._handleInputClick();\n  }\n\n  /** Focuses the checkbox. */\n  focus(origin?: FocusOrigin, options?: FocusOptions): void {\n    if (origin) {\n      this._focusMonitor.focusVia(this._inputElement, origin, options);\n    } else {\n      this._inputElement.nativeElement.focus(options);\n    }\n  }\n}\n", "<label [attr.for]=\"inputId\" class=\"mat-checkbox-layout\" #label>\n  <span class=\"mat-checkbox-inner-container\"\n       [class.mat-checkbox-inner-container-no-side-margin]=\"!checkboxLabel.textContent || !checkboxLabel.textContent.trim()\">\n    <input #input\n           class=\"mat-checkbox-input cdk-visually-hidden\" type=\"checkbox\"\n           [id]=\"inputId\"\n           [required]=\"required\"\n           [checked]=\"checked\"\n           [attr.value]=\"value\"\n           [disabled]=\"disabled\"\n           [attr.name]=\"name\"\n           [tabIndex]=\"tabIndex\"\n           [attr.aria-label]=\"ariaLabel || null\"\n           [attr.aria-labelledby]=\"ariaLabelledby\"\n           [attr.aria-describedby]=\"ariaDescribedby\"\n           (change)=\"_onInteractionEvent($event)\"\n           (click)=\"_onInputClick($event)\">\n    <span matRipple class=\"mat-checkbox-ripple mat-focus-indicator\"\n         [matRippleTrigger]=\"label\"\n         [matRippleDisabled]=\"_isRippleDisabled()\"\n         [matRippleRadius]=\"20\"\n         [matRippleCentered]=\"true\"\n         [matRippleAnimation]=\"{enterDuration: _animationMode === 'NoopAnimations' ? 0 : 150}\">\n      <span class=\"mat-ripple-element mat-checkbox-persistent-ripple\"></span>\n    </span>\n    <span class=\"mat-checkbox-frame\"></span>\n    <span class=\"mat-checkbox-background\">\n      <svg version=\"1.1\"\n           focusable=\"false\"\n           class=\"mat-checkbox-checkmark\"\n           viewBox=\"0 0 24 24\"\n           aria-hidden=\"true\">\n        <path class=\"mat-checkbox-checkmark-path\"\n              fill=\"none\"\n              stroke=\"white\"\n              d=\"M4.1,12.7 9,17.6 20.3,6.3\"/>\n      </svg>\n      <!-- Element for rendering the indeterminate state checkbox. -->\n      <span class=\"mat-checkbox-mixedmark\"></span>\n    </span>\n  </span>\n  <span class=\"mat-checkbox-label\" #checkboxLabel (cdkObserveContent)=\"_onLabelTextChange()\">\n    <!-- Add an invisible span so JAWS can read the label -->\n    <span style=\"display:none\">&nbsp;</span>\n    <ng-content></ng-content>\n  </span>\n</label>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ObserversModule} from '@angular/cdk/observers';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '@angular/material/core';\nimport {MatLegacyCheckbox} from './checkbox';\nimport {_MatCheckboxRequiredValidatorModule} from '@angular/material/checkbox';\n\n/**\n * @deprecated Use `MatCheckboxModule` from `@angular/material/checkbox` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@NgModule({\n  imports: [MatRippleModule, MatCommonModule, ObserversModule, _MatCheckboxRequiredValidatorModule],\n  exports: [MatLegacyCheckbox, MatCommonModule, _MatCheckboxRequiredValidatorModule],\n  declarations: [MatLegacyCheckbox],\n})\nexport class MatLegacyCheckboxModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;;;;AA+BA;;;;AAIG;MACU,uBAAuB,CAAA;AAKnC,CAAA;AACD;;;;;;AAMG;AACU,MAAA,0CAA0C,GAAQ;AAC7D,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,iBAAiB,CAAC;AAChD,IAAA,KAAK,EAAE,IAAI;EACX;AAEF;;;;;;;;;AASG;AAuBG,MAAO,iBACX,SAAQ,gBAAyC,CAAA;AAYjD,IAAA,WAAA,CACE,UAAmC,EACnC,iBAAoC,EAC5B,aAA2B,EACnC,MAAc,EACS,QAAgB,EACI,aAAsB,EAGjE,OAAmC,EAAA;AAEnC,QAAA,KAAK,CAAC,eAAe,EAAE,UAAU,EAAE,iBAAiB,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QARxF,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;AAZ3B,QAAA,IAAA,CAAA,iBAAiB,GAAG;AAC5B,YAAA,kBAAkB,EAAE,qCAAqC;AACzD,YAAA,wBAAwB,EAAE,2CAA2C;AACrE,YAAA,kBAAkB,EAAE,qCAAqC;AACzD,YAAA,sBAAsB,EAAE,yCAAyC;AACjE,YAAA,sBAAsB,EAAE,yCAAyC;AACjE,YAAA,wBAAwB,EAAE,2CAA2C;SACtE,CAAC;KAcD;AAES,IAAA,kBAAkB,CAAC,SAAkB,EAAA;AAC7C,QAAA,MAAM,KAAK,GAAG,IAAI,uBAAuB,EAAE,CAAC;AAC5C,QAAA,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;AACpB,QAAA,KAAK,CAAC,OAAO,GAAG,SAAS,CAAC;AAC1B,QAAA,OAAO,KAAK,CAAC;KACd;IAES,0BAA0B,GAAA;AAClC,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;KACvC;IAEQ,eAAe,GAAA;QACtB,KAAK,CAAC,eAAe,EAAE,CAAC;AAExB,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,IAAG;YACzE,IAAI,CAAC,WAAW,EAAE;gBAChB,IAAI,CAAC,OAAO,EAAE,CAAC;AAChB,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACrD;AAED;;;;;;AAMG;AACH,IAAA,aAAa,CAAC,KAAY,EAAA;;;;;;;;QAQxB,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,KAAK,CAAC,iBAAiB,EAAE,CAAC;KAC3B;;IAGD,KAAK,CAAC,MAAoB,EAAE,OAAsB,EAAA;AAChD,QAAA,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAClE,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACjD,SAAA;KACF;AA9EU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,EAkBf,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,UAAU,EACD,SAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,qBAAqB,6BAEjC,4BAA4B,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AArB3B,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,EALjB,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,eAAA,EAAA,MAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,kCAAA,EAAA,eAAA,EAAA,4BAAA,EAAA,SAAA,EAAA,6BAAA,EAAA,UAAA,EAAA,iCAAA,EAAA,6BAAA,EAAA,+BAAA,EAAA,qCAAA,EAAA,EAAA,cAAA,EAAA,cAAA,EAAA,EAAA,SAAA,EAAA,CAAC,0CAA0C,CAAC,4EClFzD,slEA+CA,EAAA,MAAA,EAAA,CAAA,g0NAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDwCa,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAtB7B,SAAS;+BACE,cAAc,EAAA,QAAA,EAGd,aAAa,EACjB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,cAAc;AACvB,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,iBAAiB,EAAE,MAAM;AACzB,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,wBAAwB,EAAE,MAAM;AAChC,wBAAA,oCAAoC,EAAE,eAAe;AACrD,wBAAA,8BAA8B,EAAE,SAAS;AACzC,wBAAA,+BAA+B,EAAE,UAAU;AAC3C,wBAAA,mCAAmC,EAAE,2BAA2B;AAChE,wBAAA,iCAAiC,EAAE,CAAqC,mCAAA,CAAA;AACzE,qBAAA,EAAA,SAAA,EACU,CAAC,0CAA0C,CAAC,UAC/C,CAAC,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC,iBAC/B,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,slEAAA,EAAA,MAAA,EAAA,CAAA,g0NAAA,CAAA,EAAA,CAAA;;0BAoB5C,SAAS;2BAAC,UAAU,CAAA;;0BACpB,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,QAAQ;;0BACR,MAAM;2BAAC,4BAA4B,CAAA;;;AE9FxC;;;AAGG;MAMU,uBAAuB,CAAA;8GAAvB,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAvB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,EAFnB,YAAA,EAAA,CAAA,iBAAiB,CAFtB,EAAA,OAAA,EAAA,CAAA,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,mCAAmC,CACtF,EAAA,OAAA,EAAA,CAAA,iBAAiB,EAAE,eAAe,EAAE,mCAAmC,CAAA,EAAA,CAAA,CAAA,EAAA;+GAGtE,uBAAuB,EAAA,OAAA,EAAA,CAJxB,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,mCAAmC,EACnE,eAAe,EAAE,mCAAmC,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAGtE,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBALnC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,mCAAmC,CAAC;AACjG,oBAAA,OAAO,EAAE,CAAC,iBAAiB,EAAE,eAAe,EAAE,mCAAmC,CAAC;oBAClF,YAAY,EAAE,CAAC,iBAAiB,CAAC;AAClC,iBAAA,CAAA;;;ACtBD;;AAEG;;;;"}