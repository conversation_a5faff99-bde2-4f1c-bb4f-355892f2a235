{"ast": null, "code": "import baseEach from './_baseEach.js';\n\n/**\n * The base implementation of `_.filter` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction baseFilter(collection, predicate) {\n  var result = [];\n  baseEach(collection, function (value, index, collection) {\n    if (predicate(value, index, collection)) {\n      result.push(value);\n    }\n  });\n  return result;\n}\nexport default baseFilter;", "map": {"version": 3, "names": ["baseEach", "baseFilter", "collection", "predicate", "result", "value", "index", "push"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_baseFilter.js"], "sourcesContent": ["import baseEach from './_baseEach.js';\n\n/**\n * The base implementation of `_.filter` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction baseFilter(collection, predicate) {\n  var result = [];\n  baseEach(collection, function(value, index, collection) {\n    if (predicate(value, index, collection)) {\n      result.push(value);\n    }\n  });\n  return result;\n}\n\nexport default baseFilter;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,UAAU,EAAEC,SAAS,EAAE;EACzC,IAAIC,MAAM,GAAG,EAAE;EACfJ,QAAQ,CAACE,UAAU,EAAE,UAASG,KAAK,EAAEC,KAAK,EAAEJ,UAAU,EAAE;IACtD,IAAIC,SAAS,CAACE,KAAK,EAAEC,KAAK,EAAEJ,UAAU,CAAC,EAAE;MACvCE,MAAM,CAACG,IAAI,CAACF,KAAK,CAAC;IACpB;EACF,CAAC,CAAC;EACF,OAAOD,MAAM;AACf;AAEA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}