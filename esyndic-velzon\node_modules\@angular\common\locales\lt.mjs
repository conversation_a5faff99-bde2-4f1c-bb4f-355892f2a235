/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, f = parseInt(val.toString().replace(/^[^.]*\.?/, ''), 10) || 0;
    if (n % 10 === 1 && !(n % 100 >= 11 && n % 100 <= 19))
        return 1;
    if (n % 10 === Math.floor(n % 10) && (n % 10 >= 2 && n % 10 <= 9) && !(n % 100 >= 11 && n % 100 <= 19))
        return 3;
    if (!(f === 0))
        return 4;
    return 5;
}
export default ["lt", [["pr. p.", "pop."], ["priešpiet", "popiet"], u], u, [["S", "P", "A", "T", "K", "P", "Š"], ["sk", "pr", "an", "tr", "kt", "pn", "št"], ["sekmadienis", "pirmadienis", "antradienis", "trečiadienis", "ketvirtadienis", "penktadienis", "šeštadienis"], ["Sk", "Pr", "An", "Tr", "Kt", "Pn", "Št"]], u, [["S", "V", "K", "B", "G", "B", "L", "R", "R", "S", "L", "G"], ["saus.", "vas.", "kov.", "bal.", "geg.", "birž.", "liep.", "rugp.", "rugs.", "spal.", "lapkr.", "gruod."], ["sausio", "vasario", "kovo", "balandžio", "gegužės", "birželio", "liepos", "rugpjūčio", "rugsėjo", "spalio", "lapkričio", "gruodžio"]], [["S", "V", "K", "B", "G", "B", "L", "R", "R", "S", "L", "G"], ["saus.", "vas.", "kov.", "bal.", "geg.", "birž.", "liep.", "rugp.", "rugs.", "spal.", "lapkr.", "gruod."], ["sausis", "vasaris", "kovas", "balandis", "gegužė", "birželis", "liepa", "rugpjūtis", "rugsėjis", "spalis", "lapkritis", "gruodis"]], [["pr. Kr.", "po Kr."], u, ["prieš Kristų", "po Kristaus"]], 1, [6, 0], ["y-MM-dd", u, "y 'm'. MMMM d 'd'.", "y 'm'. MMMM d 'd'., EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "−", "×10^", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "EUR", "€", "Euras", { "AUD": [u, "$"], "BDT": [], "BRL": [u, "R$"], "BYN": [u, "Br"], "CAD": [u, "$"], "CNY": [u, "¥"], "GBP": [u, "£"], "HKD": [u, "$"], "ILS": [], "INR": [], "JPY": [u, "¥"], "KHR": [], "KRW": [u, "₩"], "LAK": [], "MNT": [], "MXN": [u, "$"], "NZD": [u, "$"], "PHP": [u, "₱"], "PLN": [u, "zl"], "PYG": [u, "Gs"], "RUB": [u, "rb"], "TWD": [u, "$"], "USD": [u, "$"], "VND": [], "XAF": [], "XCD": [u, "$"], "XOF": [], "XPF": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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