{"ast": null, "code": "import { RouterModule } from '@angular/router';\n// Component pages\nimport { ListViewComponent } from './list-view/list-view.component';\nimport { GridViewComponent } from './grid-view/grid-view.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: \"listview\",\n  component: ListViewComponent\n}, {\n  path: \"gridview\",\n  component: GridViewComponent\n}];\nexport class CandidateListsRoutingModule {\n  static {\n    this.ɵfac = function CandidateListsRoutingModule_Factory(t) {\n      return new (t || CandidateListsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CandidateListsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CandidateListsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ListViewComponent", "GridViewComponent", "routes", "path", "component", "CandidateListsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\candidate-lists\\candidate-lists-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\n// Component pages\r\nimport { ListViewComponent } from './list-view/list-view.component';\r\nimport { GridViewComponent } from './grid-view/grid-view.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: \"listview\",\r\n    component: ListViewComponent\r\n  },\r\n  {\r\n    path: \"gridview\",\r\n    component: GridViewComponent\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class CandidateListsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AAEtD;AACA,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,iBAAiB,QAAQ,iCAAiC;;;AAEnE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5BN,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXM,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAAT,YAAA;IAAAU,OAAA,GAF5BV,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}