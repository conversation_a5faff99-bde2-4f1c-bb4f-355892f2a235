{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { allNotification, messages } from './data';\nimport { cartData } from './data';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/event.service\";\nimport * as i2 from \"../../core/services/language.service\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"ngx-cookie-service\";\nimport * as i5 from \"@ngx-translate/core\";\nimport * as i6 from \"../../core/services/auth.service\";\nimport * as i7 from \"../../core/services/authfake.service\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"../../core/services/token-storage.service\";\nimport * as i10 from \"../../core/services/keycloak-auth.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"simplebar-angular\";\nimport * as i13 from \"@angular/forms\";\nconst _c0 = [\"removenotification\"];\nconst _c1 = a0 => ({\n  \"active\": a0\n});\nfunction TopbarComponent_Conditional_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 60);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r1.valueset, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction TopbarComponent_Conditional_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 60);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r1.flagvalue, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction TopbarComponent_For_101_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 153);\n    i0.ɵɵlistener(\"click\", function TopbarComponent_For_101_Template_a_click_0_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setLanguage(item_r4.text, item_r4.lang, item_r4.flag));\n    });\n    i0.ɵɵelement(1, \"img\", 154);\n    i0.ɵɵelementStart(2, \"span\", 137);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c1, ctx_r1.cookieValue === item_r4.lang));\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"src\", item_r4.flag, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.text);\n  }\n}\nfunction TopbarComponent_For_175_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"div\", 47);\n    i0.ɵɵelement(2, \"img\", 155);\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"h6\", 156)(5, \"a\", 157);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 158);\n    i0.ɵɵtext(8, \" Quantity: \");\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 100)(12, \"h5\", 159);\n    i0.ɵɵtext(13, \"$ \");\n    i0.ɵɵelementStart(14, \"span\", 160);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 161)(17, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function TopbarComponent_For_175_Template_button_click_17_listener($event) {\n      const data_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteItem($event, data_r6.id));\n    });\n    i0.ɵɵelement(18, \"i\", 163);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const data_r6 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"item_\", data_r6.id, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"src\", data_r6.img, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(data_r6.product);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", data_r6.quantity, \" x $\", data_r6.price, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(data_r6.quantity * data_r6.price);\n  }\n}\nfunction TopbarComponent_ng_template_217_For_4_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 171);\n    i0.ɵɵelementStart(1, \"div\", 39)(2, \"a\", 172)(3, \"h6\", 173);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 174)(6, \"p\", 175);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p\", 176)(9, \"span\");\n    i0.ɵɵelement(10, \"i\", 177);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"src\", item_r8.img, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r8.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r8.desc);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r8.time, \"\");\n  }\n}\nfunction TopbarComponent_ng_template_217_For_4_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 178)(1, \"span\", 179);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"a\", 172)(5, \"h6\", 180);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 176)(8, \"span\");\n    i0.ɵɵelement(9, \"i\", 177);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"bx \", item_r8.icon, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", item_r8.desc, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r8.time);\n  }\n}\nfunction TopbarComponent_ng_template_217_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 166)(1, \"div\", 5);\n    i0.ɵɵtemplate(2, TopbarComponent_ng_template_217_For_4_Conditional_2_Template, 12, 4)(3, TopbarComponent_ng_template_217_For_4_Conditional_3_Template, 11, 5);\n    i0.ɵɵelementStart(4, \"div\", 169)(5, \"input\", 170);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TopbarComponent_ng_template_217_For_4_Template_input_ngModelChange_5_listener($event) {\n      const item_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r8.state, $event) || (item_r8.state = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function TopbarComponent_ng_template_217_For_4_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCheckboxChange($event, \"1\"));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(item_r8.img ? 2 : 3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r8.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r8.state);\n  }\n}\nfunction TopbarComponent_ng_template_217_ForEmpty_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 167)(1, \"div\", 181);\n    i0.ɵɵelement(2, \"img\", 182);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 183)(4, \"h6\", 184);\n    i0.ɵɵtext(5, \"Hey! You have no any notifications \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r1.allnotifications == null ? null : ctx_r1.allnotifications.length) != 0 ? \"d-none\" : \"\");\n  }\n}\nfunction TopbarComponent_ng_template_217_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 168)(1, \"button\", 185);\n    i0.ɵɵtext(2, \"View All Notifications \");\n    i0.ɵɵelement(3, \"i\", 186);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TopbarComponent_ng_template_217_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"ngx-simplebar\", 89)(2, \"div\", 165);\n    i0.ɵɵrepeaterCreate(3, TopbarComponent_ng_template_217_For_4_Template, 6, 3, \"div\", 166, i0.ɵɵrepeaterTrackByIndex, false, TopbarComponent_ng_template_217_ForEmpty_5_Template, 6, 1, \"div\", 167);\n    i0.ɵɵtemplate(6, TopbarComponent_ng_template_217_Conditional_6_Template, 4, 0, \"div\", 168);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵrepeater(ctx_r1.allnotifications);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional((ctx_r1.allnotifications == null ? null : ctx_r1.allnotifications.length) > 0 ? 6 : -1);\n  }\n}\nfunction TopbarComponent_ng_template_221_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 187)(1, \"div\", 5);\n    i0.ɵɵelement(2, \"img\", 171);\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"a\", 172)(5, \"h6\", 173);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 174)(8, \"p\", 175);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"p\", 176)(11, \"span\");\n    i0.ɵɵelement(12, \"i\", 177);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 169)(15, \"input\", 188);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TopbarComponent_ng_template_221_For_4_Template_input_ngModelChange_15_listener($event) {\n      const message_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      i0.ɵɵtwoWayBindingSet(message_r10.state, $event) || (message_r10.state = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function TopbarComponent_ng_template_221_For_4_Template_input_change_15_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCheckboxChange($event, \"2\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"label\", 189);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"src\", message_r10.avatar, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(message_r10.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r10.message);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", message_r10.time_ago, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"value\", message_r10.id);\n    i0.ɵɵpropertyInterpolate(\"id\", message_r10.checkboxId);\n    i0.ɵɵtwoWayProperty(\"ngModel\", message_r10.state);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"for\", message_r10.checkboxId);\n  }\n}\nfunction TopbarComponent_ng_template_221_ForEmpty_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 167)(1, \"div\", 181);\n    i0.ɵɵelement(2, \"img\", 182);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 183)(4, \"h6\", 184);\n    i0.ɵɵtext(5, \"Hey! You have no any notifications \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r1.messages == null ? null : ctx_r1.messages.length) != 0 ? \"d-none\" : \"\");\n  }\n}\nfunction TopbarComponent_ng_template_221_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 168)(1, \"button\", 185);\n    i0.ɵɵtext(2, \"View All Messages \");\n    i0.ɵɵelement(3, \"i\", 186);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TopbarComponent_ng_template_221_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"ngx-simplebar\", 89)(2, \"div\", 165);\n    i0.ɵɵrepeaterCreate(3, TopbarComponent_ng_template_221_For_4_Template, 17, 8, \"div\", 187, i0.ɵɵrepeaterTrackByIndex, false, TopbarComponent_ng_template_221_ForEmpty_5_Template, 6, 1, \"div\", 167);\n    i0.ɵɵtemplate(6, TopbarComponent_ng_template_221_Conditional_6_Template, 4, 0, \"div\", 168);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵrepeater(ctx_r1.messages);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional((ctx_r1.messages == null ? null : ctx_r1.messages.length) > 0 ? 6 : -1);\n  }\n}\nfunction TopbarComponent_ng_template_225_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 181);\n    i0.ɵɵelement(2, \"img\", 182);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 183)(4, \"h6\", 184);\n    i0.ɵɵtext(5, \"Hey! You have no any notifications \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction TopbarComponent_span_241_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 191)(1, \"span\", 192);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 193);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.userProfile.fullName || ctx_r1.userProfile.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.keycloakAuthService.getUserRoleLevel(), \" \");\n  }\n}\nfunction TopbarComponent_span_242_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 191)(1, \"span\", 192);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 193);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.userData.first_name, \" \", ctx_r1.userData.last_name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.userData.role == 0 ? \"Admin\" : \"User\");\n  }\n}\nfunction TopbarComponent_h6_244_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h6\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Welcome \", ctx_r1.userProfile.firstName || ctx_r1.userProfile.username, \"! \");\n  }\n}\nfunction TopbarComponent_h6_245_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h6\", 23);\n    i0.ɵɵtext(1, \"Welcome Anna!\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TopbarComponent_ng_template_283_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 194)(1, \"div\", 195)(2, \"button\", 196);\n    i0.ɵɵlistener(\"click\", function TopbarComponent_ng_template_283_Template_button_click_2_listener() {\n      const modal_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      return i0.ɵɵresetView(modal_r13.dismiss(\"Cross click\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 197)(4, \"div\", 198);\n    i0.ɵɵelement(5, \"lord-icon\", 199);\n    i0.ɵɵelementStart(6, \"div\", 200)(7, \"h4\");\n    i0.ɵɵtext(8, \"Are you sure ?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 201);\n    i0.ɵɵtext(10, \"Are you sure you want to remove this Notification ?\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 202)(12, \"button\", 203);\n    i0.ɵɵlistener(\"click\", function TopbarComponent_ng_template_283_Template_button_click_12_listener() {\n      const modal_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      return i0.ɵɵresetView(modal_r13.dismiss(\"Cross click\"));\n    });\n    i0.ɵɵtext(13, \"Close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 204);\n    i0.ɵɵlistener(\"click\", function TopbarComponent_ng_template_283_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.notificationDelete());\n    });\n    i0.ɵɵtext(15, \"Yes, Delete It!\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport class TopbarComponent {\n  constructor(document, eventService, languageService, modalService, _cookiesService, translate, authService, authFackservice, router, TokenStorageService, keycloakAuthService) {\n    this.document = document;\n    this.eventService = eventService;\n    this.languageService = languageService;\n    this.modalService = modalService;\n    this._cookiesService = _cookiesService;\n    this.translate = translate;\n    this.authService = authService;\n    this.authFackservice = authFackservice;\n    this.router = router;\n    this.TokenStorageService = TokenStorageService;\n    this.keycloakAuthService = keycloakAuthService;\n    this.mobileMenuButtonClicked = new EventEmitter();\n    this.total = 0;\n    this.cart_length = 0;\n    this.totalNotify = 0;\n    this.newNotify = 0;\n    this.readNotify = 0;\n    this.isDropdownOpen = false;\n    // Keycloak user profile\n    this.userProfile = null;\n    this.isAuthenticated = false;\n    /***\n     * Language Listing\n     */\n    this.listLang = [{\n      text: 'English',\n      flag: 'assets/images/flags/us.svg',\n      lang: 'en'\n    }, {\n      text: 'Española',\n      flag: 'assets/images/flags/spain.svg',\n      lang: 'es'\n    }, {\n      text: 'Deutsche',\n      flag: 'assets/images/flags/germany.svg',\n      lang: 'de'\n    }, {\n      text: 'Italiana',\n      flag: 'assets/images/flags/italy.svg',\n      lang: 'it'\n    }, {\n      text: 'русский',\n      flag: 'assets/images/flags/russia.svg',\n      lang: 'ru'\n    }, {\n      text: '中国人',\n      flag: 'assets/images/flags/china.svg',\n      lang: 'ch'\n    }, {\n      text: 'français',\n      flag: 'assets/images/flags/french.svg',\n      lang: 'fr'\n    }, {\n      text: 'Arabic',\n      flag: 'assets/images/flags/ar.svg',\n      lang: 'ar'\n    }];\n    // Remove Notification\n    this.checkedValGet = [];\n  }\n  ngOnInit() {\n    this.userData = this.TokenStorageService.getUser();\n    this.element = document.documentElement;\n    // Load Keycloak user profile\n    this.isAuthenticated = this.keycloakAuthService.isAuthenticated();\n    if (this.isAuthenticated) {\n      this.keycloakAuthService.userProfile$.subscribe(profile => {\n        this.userProfile = profile;\n      });\n    }\n    // Cookies wise Language set\n    this.cookieValue = this._cookiesService.get('lang');\n    const val = this.listLang.filter(x => x.lang === this.cookieValue);\n    this.countryName = val.map(element => element.text);\n    if (val.length === 0) {\n      if (this.flagvalue === undefined) {\n        this.valueset = 'assets/images/flags/us.svg';\n      }\n    } else {\n      this.flagvalue = val.map(element => element.flag);\n    }\n    // Fetch Data\n    this.allnotifications = allNotification;\n    this.messages = messages;\n    this.cartData = cartData;\n    this.cart_length = this.cartData.length;\n    this.cartData.forEach(item => {\n      var item_price = item.quantity * item.price;\n      this.total += item_price;\n    });\n  }\n  /**\n   * Toggle the menu bar when having mobile screen\n   */\n  toggleMobileMenu(event) {\n    document.querySelector('.hamburger-icon')?.classList.toggle('open');\n    event.preventDefault();\n    this.mobileMenuButtonClicked.emit();\n  }\n  /**\n   * Fullscreen method\n   */\n  fullscreen() {\n    document.body.classList.toggle('fullscreen-enable');\n    if (!document.fullscreenElement && !this.element.mozFullScreenElement && !this.element.webkitFullscreenElement) {\n      if (this.element.requestFullscreen) {\n        this.element.requestFullscreen();\n      } else if (this.element.mozRequestFullScreen) {\n        /* Firefox */\n        this.element.mozRequestFullScreen();\n      } else if (this.element.webkitRequestFullscreen) {\n        /* Chrome, Safari and Opera */\n        this.element.webkitRequestFullscreen();\n      } else if (this.element.msRequestFullscreen) {\n        /* IE/Edge */\n        this.element.msRequestFullscreen();\n      }\n    } else {\n      if (this.document.exitFullscreen) {\n        this.document.exitFullscreen();\n      } else if (this.document.mozCancelFullScreen) {\n        /* Firefox */\n        this.document.mozCancelFullScreen();\n      } else if (this.document.webkitExitFullscreen) {\n        /* Chrome, Safari and Opera */\n        this.document.webkitExitFullscreen();\n      } else if (this.document.msExitFullscreen) {\n        /* IE/Edge */\n        this.document.msExitFullscreen();\n      }\n    }\n  }\n  /**\n  * Open modal\n  * @param content modal content\n  */\n  openModal(content) {\n    // this.submitted = false;\n    this.modalService.open(content, {\n      centered: true\n    });\n  }\n  /**\n  * Topbar Light-Dark Mode Change\n  */\n  changeMode(mode) {\n    this.mode = mode;\n    this.eventService.broadcast('changeMode', mode);\n    switch (mode) {\n      case 'light':\n        document.documentElement.setAttribute('data-bs-theme', \"light\");\n        break;\n      case 'dark':\n        document.documentElement.setAttribute('data-bs-theme', \"dark\");\n        break;\n      default:\n        document.documentElement.setAttribute('data-bs-theme', \"light\");\n        break;\n    }\n  }\n  /***\n   * Language Value Set\n   */\n  setLanguage(text, lang, flag) {\n    this.countryName = text;\n    this.flagvalue = flag;\n    this.cookieValue = lang;\n    this.languageService.setLanguage(lang);\n  }\n  /**\n   * Logout the user\n   */\n  logout() {\n    if (this.isAuthenticated) {\n      this.keycloakAuthService.logout();\n    } else {\n      this.authService.logout();\n      this.router.navigate(['/auth/login']);\n    }\n  }\n  windowScroll() {\n    if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {\n      document.getElementById(\"back-to-top\").style.display = \"block\";\n      document.getElementById('page-topbar')?.classList.add('topbar-shadow');\n    } else {\n      document.getElementById(\"back-to-top\").style.display = \"none\";\n      document.getElementById('page-topbar')?.classList.remove('topbar-shadow');\n    }\n  }\n  // Delete Item\n  deleteItem(event, id) {\n    var price = event.target.closest('.dropdown-item').querySelector('.item_price').innerHTML;\n    var Total_price = this.total - price;\n    this.total = Total_price;\n    this.cart_length = this.cart_length - 1;\n    this.total > 1 ? document.getElementById(\"empty-cart\").style.display = \"none\" : document.getElementById(\"empty-cart\").style.display = \"block\";\n    document.getElementById('item_' + id)?.remove();\n  }\n  toggleDropdown(event) {\n    event.stopPropagation();\n    if (this.isDropdownOpen) {\n      this.isDropdownOpen = false;\n    } else {\n      this.isDropdownOpen = true;\n    }\n  }\n  // Search Topbar\n  Search() {\n    var searchOptions = document.getElementById(\"search-close-options\");\n    var dropdown = document.getElementById(\"search-dropdown\");\n    var input, filter, ul, li, a, i, txtValue;\n    input = document.getElementById(\"search-options\");\n    filter = input.value.toUpperCase();\n    var inputLength = filter.length;\n    if (inputLength > 0) {\n      dropdown.classList.add(\"show\");\n      searchOptions.classList.remove(\"d-none\");\n      var inputVal = input.value.toUpperCase();\n      var notifyItem = document.getElementsByClassName(\"notify-item\");\n      Array.from(notifyItem).forEach(function (element) {\n        var notifiTxt = '';\n        if (element.querySelector(\"h6\")) {\n          var spantext = element.getElementsByTagName(\"span\")[0].innerText.toLowerCase();\n          var name = element.querySelector(\"h6\").innerText.toLowerCase();\n          if (name.includes(inputVal)) {\n            notifiTxt = name;\n          } else {\n            notifiTxt = spantext;\n          }\n        } else if (element.getElementsByTagName(\"span\")) {\n          notifiTxt = element.getElementsByTagName(\"span\")[0].innerText.toLowerCase();\n        }\n        if (notifiTxt) element.style.display = notifiTxt.includes(inputVal) ? \"block\" : \"none\";\n      });\n    } else {\n      dropdown.classList.remove(\"show\");\n      searchOptions.classList.add(\"d-none\");\n    }\n  }\n  /**\n   * Search Close Btn\n   */\n  closeBtn() {\n    var searchOptions = document.getElementById(\"search-close-options\");\n    var dropdown = document.getElementById(\"search-dropdown\");\n    var searchInputReponsive = document.getElementById(\"search-options\");\n    dropdown.classList.remove(\"show\");\n    searchOptions.classList.add(\"d-none\");\n    searchInputReponsive.value = \"\";\n  }\n  onCheckboxChange(event, id) {\n    this.notifyId = id;\n    var result;\n    if (id == '1') {\n      var checkedVal = [];\n      for (var i = 0; i < this.allnotifications.length; i++) {\n        if (this.allnotifications[i].state == true) {\n          result = this.allnotifications[i].id;\n          checkedVal.push(result);\n        }\n      }\n      this.checkedValGet = checkedVal;\n    } else {\n      var checkedVal = [];\n      for (var i = 0; i < this.messages.length; i++) {\n        if (this.messages[i].state == true) {\n          result = this.messages[i].id;\n          checkedVal.push(result);\n        }\n      }\n      console.log(checkedVal);\n      this.checkedValGet = checkedVal;\n    }\n    checkedVal.length > 0 ? document.getElementById(\"notification-actions\").style.display = 'block' : document.getElementById(\"notification-actions\").style.display = 'none';\n  }\n  notificationDelete() {\n    if (this.notifyId == '1') {\n      for (var i = 0; i < this.checkedValGet.length; i++) {\n        for (var j = 0; j < this.allnotifications.length; j++) {\n          if (this.allnotifications[j].id == this.checkedValGet[i]) {\n            this.allnotifications.splice(j, 1);\n          }\n        }\n      }\n    } else {\n      for (var i = 0; i < this.checkedValGet.length; i++) {\n        for (var j = 0; j < this.messages.length; j++) {\n          if (this.messages[j].id == this.checkedValGet[i]) {\n            this.messages.splice(j, 1);\n          }\n        }\n      }\n    }\n    this.calculatenotification();\n    this.modalService.dismissAll();\n  }\n  calculatenotification() {\n    this.totalNotify = 0;\n    this.checkedValGet = [];\n    this.checkedValGet.length > 0 ? document.getElementById(\"notification-actions\").style.display = 'block' : document.getElementById(\"notification-actions\").style.display = 'none';\n    if (this.totalNotify == 0) {\n      document.querySelector('.empty-notification-elem')?.classList.remove('d-none');\n    }\n  }\n  static {\n    this.ɵfac = function TopbarComponent_Factory(t) {\n      return new (t || TopbarComponent)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i1.EventService), i0.ɵɵdirectiveInject(i2.LanguageService), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.CookieService), i0.ɵɵdirectiveInject(i5.TranslateService), i0.ɵɵdirectiveInject(i6.AuthenticationService), i0.ɵɵdirectiveInject(i7.AuthfakeauthenticationService), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i9.TokenStorageService), i0.ɵɵdirectiveInject(i10.KeycloakAuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TopbarComponent,\n      selectors: [[\"app-topbar\"]],\n      viewQuery: function TopbarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.removenotification = _t.first);\n        }\n      },\n      outputs: {\n        mobileMenuButtonClicked: \"mobileMenuButtonClicked\"\n      },\n      decls: 285,\n      vars: 17,\n      consts: [[\"nav\", \"ngbNav\"], [\"removenotification\", \"\"], [\"id\", \"page-topbar\", \"data-scroll-header\", \"\", 3, \"scroll\"], [1, \"layout-width\"], [1, \"navbar-header\"], [1, \"d-flex\"], [1, \"navbar-brand-box\", \"horizontal-logo\"], [\"href\", \"javascript:void(0);\", 1, \"logo\", \"logo-dark\"], [1, \"logo-sm\"], [\"src\", \"assets/images/logo-sm.png\", \"alt\", \"\", \"height\", \"22\"], [1, \"logo-lg\"], [\"src\", \"assets/images/logo-dark.png\", \"alt\", \"\", \"height\", \"17\"], [\"href\", \"javascript:void(0);\", 1, \"logo\", \"logo-light\"], [\"src\", \"assets/images/logo-light.png\", \"alt\", \"\", \"height\", \"17\"], [\"type\", \"button\", \"id\", \"topnav-hamburger-icon\", 1, \"btn\", \"btn-sm\", \"px-3\", \"fs-16\", \"header-item\", \"vertical-menu-btn\", \"topnav-hamburger\", 3, \"click\"], [1, \"hamburger-icon\"], [1, \"app-search\", \"d-none\", \"d-md-block\"], [1, \"position-relative\"], [\"type\", \"text\", \"placeholder\", \"Search...\", \"autocomplete\", \"off\", \"id\", \"search-options\", \"value\", \"\", 1, \"form-control\", 3, \"keyup\"], [1, \"mdi\", \"mdi-magnify\", \"search-widget-icon\"], [\"id\", \"search-close-options\", 1, \"mdi\", \"mdi-close-circle\", \"search-widget-icon\", \"search-widget-icon-close\", \"d-none\", 3, \"click\"], [\"id\", \"search-dropdown\", 1, \"dropdown-menu\", \"dropdown-menu-lg\"], [2, \"max-height\", \"320px\"], [1, \"dropdown-header\"], [1, \"text-overflow\", \"text-muted\", \"mb-0\", \"text-uppercase\"], [1, \"dropdown-item\", \"bg-transparent\", \"text-wrap\"], [\"href\", \"javascript:void(0);\", 1, \"btn\", \"btn-soft-secondary\", \"btn-sm\", \"rounded-pill\", \"me-1\"], [1, \"mdi\", \"mdi-magnify\", \"ms-1\"], [\"href\", \"javascript:void(0);\", 1, \"btn\", \"btn-soft-secondary\", \"btn-sm\", \"rounded-pill\"], [1, \"dropdown-header\", \"mt-2\"], [1, \"text-overflow\", \"text-muted\", \"mb-1\", \"text-uppercase\"], [\"href\", \"javascript:void(0);\", 1, \"dropdown-item\", \"notify-item\"], [1, \"ri-bubble-chart-line\", \"align-middle\", \"fs-18\", \"text-muted\", \"me-2\"], [1, \"ri-lifebuoy-line\", \"align-middle\", \"fs-18\", \"text-muted\", \"me-2\"], [1, \"ri-user-settings-line\", \"align-middle\", \"fs-18\", \"text-muted\", \"me-2\"], [1, \"text-overflow\", \"text-muted\", \"mb-2\", \"text-uppercase\"], [1, \"notification-list\"], [\"href\", \"javascript:void(0);\", 1, \"dropdown-item\", \"notify-item\", \"py-2\"], [\"src\", \"assets/images/users/avatar-2.jpg\", \"alt\", \"user-pic\", 1, \"me-3\", \"rounded-circle\", \"avatar-xs\"], [1, \"flex-grow-1\"], [1, \"m-0\"], [1, \"fs-11\", \"mb-0\", \"text-muted\"], [\"src\", \"assets/images/users/avatar-3.jpg\", \"alt\", \"user-pic\", 1, \"me-3\", \"rounded-circle\", \"avatar-xs\"], [\"src\", \"assets/images/users/avatar-5.jpg\", \"alt\", \"user-pic\", 1, \"me-3\", \"rounded-circle\", \"avatar-xs\"], [1, \"text-center\", \"pt-3\", \"pb-1\"], [\"href\", \"pages/search-results\", 1, \"btn\", \"btn-primary\", \"btn-sm\"], [1, \"ri-arrow-right-line\", \"ms-1\"], [1, \"d-flex\", \"align-items-center\"], [\"ngbDropdown\", \"\", 1, \"dropdown\", \"d-md-none\", \"topbar-head-dropdown\", \"header-item\"], [\"type\", \"button\", \"id\", \"page-header-search-dropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-haspopup\", \"true\", \"aria-expanded\", \"false\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-icon\", \"btn-topbar\", \"btn-ghost-secondary\", \"rounded-circle\", \"shadow-none\"], [1, \"bx\", \"bx-search\", \"fs-22\"], [\"aria-labelledby\", \"page-header-search-dropdown\", \"ngbDropdownMenu\", \"\", 1, \"dropdown-menu\", \"dropdown-menu-lg\", \"dropdown-menu-end\", \"p-0\"], [1, \"p-3\"], [1, \"form-group\", \"m-0\"], [1, \"input-group\"], [\"type\", \"text\", \"placeholder\", \"Search ...\", \"aria-label\", \"Recipient's username\", 1, \"form-control\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [1, \"mdi\", \"mdi-magnify\"], [\"ngbDropdown\", \"\", 1, \"dropdown\", \"ms-1\", \"topbar-head-dropdown\", \"header-item\"], [\"type\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-haspopup\", \"true\", \"aria-expanded\", \"false\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-icon\", \"btn-topbar\", \"btn-ghost-secondary\", \"rounded-circle\", \"shadow-none\"], [\"alt\", \"Header Language\", \"height\", \"20\", 1, \"rounded\", 3, \"src\"], [\"ngbDropdownMenu\", \"\", 1, \"dropdown-menu\", \"dropdown-menu-end\"], [\"href\", \"javascript:void(0);\", \"data-lang\", \"eng\", 1, \"dropdown-item\", \"notify-item\", \"language\", \"py-2\", 3, \"ngClass\"], [\"ngbDropdown\", \"\", 1, \"dropdown\", \"topbar-head-dropdown\", \"ms-1\", \"header-item\"], [1, \"bx\", \"bx-category-alt\", \"fs-22\"], [\"ngbDropdownMenu\", \"\", 1, \"dropdown-menu\", \"dropdown-menu-lg\", \"p-0\", \"dropdown-menu-end\"], [1, \"p-3\", \"border-top-0\", \"border-start-0\", \"border-end-0\", \"border-dashed\", \"border\"], [1, \"row\", \"align-items-center\"], [1, \"col\"], [1, \"m-0\", \"fw-semibold\", \"fs-15\"], [1, \"col-auto\"], [\"href\", \"javascript:void(0);\", 1, \"btn\", \"btn-sm\", \"btn-soft-info\", \"shadow-none\"], [1, \"ri-arrow-right-s-line\", \"align-middle\"], [1, \"p-2\"], [1, \"row\", \"g-0\"], [\"href\", \"javascript:void(0);\", 1, \"dropdown-icon-item\"], [\"src\", \"assets/images/brands/github.png\", \"alt\", \"Github\"], [\"src\", \"assets/images/brands/bitbucket.png\", \"alt\", \"bitbucket\"], [\"src\", \"assets/images/brands/dribbble.png\", \"alt\", \"dribbble\"], [\"src\", \"assets/images/brands/dropbox.png\", \"alt\", \"dropbox\"], [\"src\", \"assets/images/brands/mail_chimp.png\", \"alt\", \"mail_chimp\"], [\"src\", \"assets/images/brands/slack.png\", \"alt\", \"slack\"], [\"type\", \"button\", \"id\", \"page-header-cart-dropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-haspopup\", \"true\", \"aria-expanded\", \"false\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-icon\", \"btn-topbar\", \"btn-ghost-secondary\", \"rounded-circle\", \"shadow-none\"], [1, \"bx\", \"bx-shopping-bag\", \"fs-22\"], [1, \"position-absolute\", \"topbar-badge\", \"fs-10\", \"translate-middle\", \"badge\", \"rounded-pill\", \"bg-info\"], [1, \"visually-hidden\"], [\"aria-labelledby\", \"page-header-cart-dropdown\", \"ngbDropdownMenu\", \"\", 1, \"dropdown-menu\", \"dropdown-menu-xl\", \"dropdown-menu-end\", \"p-0\"], [1, \"m-0\", \"fs-16\", \"fw-semibold\"], [1, \"badge\", \"bg-info-subtle\", \"text-info\", \"fs-13\"], [2, \"max-height\", \"300px\"], [\"id\", \"empty-cart\", 1, \"text-center\", \"empty-cart\", 2, \"display\", \"none\"], [1, \"avatar-md\", \"mx-auto\", \"my-3\"], [1, \"avatar-title\", \"bg-info-subtle\", \"text-info\", \"fs-36\", \"rounded-circle\"], [1, \"bx\", \"bx-cart\"], [1, \"mb-3\"], [\"routerLink\", \"/ecommerce/products\", 1, \"btn\", \"btn-success\", \"w-md\", \"mb-3\"], [1, \"d-block\", \"dropdown-item\", \"text-wrap\", \"px-3\", \"py-2\", 3, \"id\"], [1, \"p-3\", \"border-bottom-0\", \"border-start-0\", \"border-end-0\", \"border-dashed\", \"border\", \"d-grid\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"pb-3\"], [1, \"m-0\", \"text-muted\"], [1, \"px-2\"], [1, \"total_price\"], [\"routerLink\", \"/ecommerce/checkout\", 1, \"btn\", \"btn-success\", \"text-center\"], [1, \"ms-1\", \"header-item\", \"d-none\", \"d-sm-flex\"], [\"type\", \"button\", \"data-toggle\", \"fullscreen\", 1, \"btn\", \"btn-icon\", \"btn-topbar\", \"btn-ghost-secondary\", \"rounded-circle\", \"shadow-none\", 3, \"click\"], [1, \"bx\", \"bx-fullscreen\", \"fs-22\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-topbar\", \"btn-ghost-secondary\", \"rounded-circle\", \"shadow-none\", \"light-dark-mode\"], [1, \"bx\", \"bx-moon\", \"fs-22\", 3, \"click\"], [1, \"bx\", \"bx-sun\", \"fs-22\", 3, \"click\"], [\"ngbDropdown\", \"\", 1, \"dropdown\", \"topbar-head-dropdown\", \"ms-1\", \"header-item\", 3, \"autoClose\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", \"id\", \"page-header-notifications-dropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-haspopup\", \"true\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-icon\", \"btn-topbar\", \"btn-ghost-secondary\", \"rounded-circle\", \"shadow-none\"], [1, \"bx\", \"bx-bell\", \"fs-22\"], [1, \"position-absolute\", \"topbar-badge\", \"fs-10\", \"translate-middle\", \"badge\", \"rounded-pill\", \"bg-danger\"], [\"ngbDropdownMenu\", \"\", \"aria-labelledby\", \"page-header-notifications-dropdown\", 1, \"dropdown-menu\", \"dropdown-menu-lg\", \"dropdown-menu-end\", \"p-0\"], [1, \"dropdown-head\", \"bg-primary\", \"bg-pattern\", \"rounded-top\"], [1, \"m-0\", \"fs-16\", \"fw-semibold\", \"text-white\"], [1, \"col-auto\", \"dropdown-tabs\"], [1, \"badge\", \"bg-light-subtle\", \"text-body\", \"fs-13\"], [1, \"px-2\", \"pt-2\"], [\"ngbNav\", \"\", \"id\", \"notificationItemsTab\", 1, \"nav\", \"nav-tabs\", \"dropdown-tabs\", \"nav-tabs-custom\", 3, \"activeId\"], [3, \"ngbNavItem\"], [\"ngbNavLink\", \"\"], [\"ngbNavContent\", \"\"], [\"id\", \"notificationItemsTabContent\", 1, \"tab-content\"], [1, \"tab-content\", \"text-muted\"], [3, \"ngbNavOutlet\"], [\"id\", \"notification-actions\", 1, \"notification-actions\"], [1, \"d-flex\", \"text-muted\", \"justify-content-center\"], [\"id\", \"select-content\", 1, \"text-body\", \"fw-semibold\", \"px-1\"], [\"type\", \"button\", \"data-bs-toggle\", \"modal\", \"data-bs-target\", \"#removeNotificationModal\", 1, \"btn\", \"btn-link\", \"link-danger\", \"p-0\", \"ms-3\", 3, \"click\"], [\"ngbDropdown\", \"\", 1, \"dropdown\", \"ms-sm-3\", \"header-item\", \"topbar-user\"], [\"type\", \"button\", \"id\", \"page-header-user-dropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-haspopup\", \"true\", \"aria-expanded\", \"false\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"shadow-none\"], [\"src\", \"assets/images/users/avatar-1.jpg\", \"alt\", \"Header Avatar\", 1, \"rounded-circle\", \"header-profile-user\"], [\"class\", \"text-start ms-xl-2\", 4, \"ngIf\"], [\"class\", \"dropdown-header\", 4, \"ngIf\"], [\"routerLink\", \"/pages/profile\", 1, \"dropdown-item\"], [1, \"mdi\", \"mdi-account-circle\", \"text-muted\", \"fs-16\", \"align-middle\", \"me-1\"], [1, \"align-middle\"], [\"routerLink\", \"/apps/chat\", 1, \"dropdown-item\"], [1, \"mdi\", \"mdi-message-text-outline\", \"text-muted\", \"fs-16\", \"align-middle\", \"me-1\"], [\"routerLink\", \"/tasks/kanban\", 1, \"dropdown-item\"], [1, \"mdi\", \"mdi-calendar-check-outline\", \"text-muted\", \"fs-16\", \"align-middle\", \"me-1\"], [\"routerLink\", \"/pages/faqs\", 1, \"dropdown-item\"], [1, \"mdi\", \"mdi-lifebuoy\", \"text-muted\", \"fs-16\", \"align-middle\", \"me-1\"], [1, \"dropdown-divider\"], [1, \"mdi\", \"mdi-wallet\", \"text-muted\", \"fs-16\", \"align-middle\", \"me-1\"], [1, \"badge\", \"bg-success-subtle\", \"text-success\", \"mt-1\", \"float-end\"], [1, \"mdi\", \"mdi-cog-outline\", \"text-muted\", \"fs-16\", \"align-middle\", \"me-1\"], [\"routerLink\", \"/auth/lockscreen/basic\", 1, \"dropdown-item\"], [1, \"mdi\", \"mdi-lock\", \"text-muted\", \"fs-16\", \"align-middle\", \"me-1\"], [\"href\", \"javascript: void(0);\", 1, \"dropdown-item\", 3, \"click\"], [1, \"mdi\", \"mdi-logout\", \"text-muted\", \"fs-16\", \"align-middle\", \"me-1\"], [\"data-key\", \"t-logout\", 1, \"align-middle\"], [\"href\", \"javascript:void(0);\", \"data-lang\", \"eng\", 1, \"dropdown-item\", \"notify-item\", \"language\", \"py-2\", 3, \"click\", \"ngClass\"], [\"alt\", \"user-image\", \"height\", \"18\", 1, \"me-2\", \"rounded\", 3, \"src\"], [\"alt\", \"user-pic\", 1, \"me-3\", \"rounded-circle\", \"avatar-sm\", \"p-2\", \"bg-light\", 3, \"src\"], [1, \"mt-0\", \"mb-1\", \"fs-14\"], [\"routerLink\", \"/ecommerce/product-detail/1\", 1, \"text-reset\"], [1, \"mb-0\", \"fs-12\", \"text-muted\"], [1, \"m-0\", \"fw-normal\"], [1, \"item_price\"], [1, \"ps-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"btn-ghost-secondary\", \"remove-item-btn\", \"shadow-none\", 3, \"click\"], [1, \"ri-close-fill\", \"fs-16\"], [\"id\", \"all-noti-tab\", \"role\", \"tabpanel\", 1, \"tab-pane\", \"fade\", \"show\", \"active\", \"py-2\", \"ps-2\"], [1, \"pe-2\"], [1, \"text-reset\", \"notification-item\", \"d-block\", \"dropdown-item\", \"position-relative\"], [\"id\", \"alerts-tab\", \"role\", \"tabpanel\", \"aria-labelledby\", \"alerts-tab\", 1, \"tab-pane\", \"p-4\", 3, \"ngClass\"], [1, \"my-3\", \"text-center\"], [1, \"px-2\", \"fs-15\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"ngModelChange\", \"change\", \"ngModel\", \"value\"], [\"alt\", \"user-pic\", 1, \"me-3\", \"rounded-circle\", \"avatar-xs\", 3, \"src\"], [\"href\", \"javascript:void(0);\", 1, \"stretched-link\"], [1, \"mt-0\", \"mb-1\", \"fs-13\", \"fw-semibold\"], [1, \"fs-13\", \"text-muted\"], [1, \"mb-1\"], [1, \"mb-0\", \"fs-11\", \"fw-medium\", \"text-uppercase\", \"text-muted\"], [1, \"mdi\", \"mdi-clock-outline\"], [1, \"avatar-xs\", \"me-3\"], [1, \"avatar-title\", \"bg-info-subtle\", \"text-info\", \"rounded-circle\", \"fs-16\"], [1, \"mt-0\", \"mb-2\", \"lh-base\"], [1, \"w-25\", \"w-sm-50\", \"pt-3\", \"mx-auto\"], [\"src\", \"assets/images/svg/bell.svg\", \"alt\", \"user-pic\", 1, \"img-fluid\"], [1, \"text-center\", \"pb-5\", \"mt-2\"], [1, \"fs-18\", \"fw-semibold\", \"lh-base\"], [\"type\", \"button\", 1, \"btn\", \"btn-soft-success\", \"waves-effect\", \"waves-light\"], [1, \"ri-arrow-right-line\", \"align-middle\"], [1, \"text-reset\", \"notification-item\", \"d-block\", \"dropdown-item\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"ngModelChange\", \"change\", \"value\", \"ngModel\", \"id\"], [1, \"form-check-label\", 3, \"for\"], [\"id\", \"alerts-tab\", \"role\", \"tabpanel\", \"aria-labelledby\", \"alerts-tab\", 1, \"tab-pane\", \"p-4\"], [1, \"text-start\", \"ms-xl-2\"], [1, \"d-none\", \"d-xl-inline-block\", \"ms-1\", \"fw-medium\", \"user-name-text\"], [1, \"d-none\", \"d-xl-block\", \"ms-1\", \"fs-12\", \"text-muted\", \"user-name-sub-text\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", \"id\", \"NotificationModalbtn-close\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\"], [1, \"mt-2\", \"text-center\"], [\"src\", \"https://cdn.lordicon.com/gsqxdxog.json\", \"trigger\", \"loop\", \"colors\", \"primary:#f7b84b,secondary:#f06548\", 2, \"width\", \"100px\", \"height\", \"100px\"], [1, \"mt-4\", \"pt-2\", \"fs-15\", \"mx-4\", \"mx-sm-5\"], [1, \"text-muted\", \"mx-4\", \"mb-0\"], [1, \"d-flex\", \"gap-2\", \"justify-content-center\", \"mt-4\", \"mb-2\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"w-sm\", \"btn-light\", 3, \"click\"], [\"type\", \"button\", \"id\", \"delete-notification\", 1, \"btn\", \"w-sm\", \"btn-danger\", 3, \"click\"]],\n      template: function TopbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"header\", 2);\n          i0.ɵɵlistener(\"scroll\", function TopbarComponent_Template_header_scroll_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.windowScroll());\n          }, false, i0.ɵɵresolveWindow);\n          i0.ɵɵelementStart(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"div\", 6)(5, \"a\", 7)(6, \"span\", 8);\n          i0.ɵɵelement(7, \"img\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"span\", 10);\n          i0.ɵɵelement(9, \"img\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"a\", 12)(11, \"span\", 8);\n          i0.ɵɵelement(12, \"img\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"span\", 10);\n          i0.ɵɵelement(14, \"img\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function TopbarComponent_Template_button_click_15_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleMobileMenu($event));\n          });\n          i0.ɵɵelementStart(16, \"span\", 15);\n          i0.ɵɵelement(17, \"span\")(18, \"span\")(19, \"span\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"form\", 16)(21, \"div\", 17)(22, \"input\", 18);\n          i0.ɵɵlistener(\"keyup\", function TopbarComponent_Template_input_keyup_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.Search());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(23, \"span\", 19);\n          i0.ɵɵelementStart(24, \"span\", 20);\n          i0.ɵɵlistener(\"click\", function TopbarComponent_Template_span_click_24_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.closeBtn());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 21)(26, \"ngx-simplebar\", 22)(27, \"div\", 23)(28, \"h6\", 24);\n          i0.ɵɵtext(29, \"Recent Searches\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 25)(31, \"a\", 26);\n          i0.ɵɵtext(32, \"how to setup \");\n          i0.ɵɵelement(33, \"i\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"a\", 28);\n          i0.ɵɵtext(35, \"buttons \");\n          i0.ɵɵelement(36, \"i\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 29)(38, \"h6\", 30);\n          i0.ɵɵtext(39, \"Pages\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"a\", 31);\n          i0.ɵɵelement(41, \"i\", 32);\n          i0.ɵɵelementStart(42, \"span\");\n          i0.ɵɵtext(43, \"Analytics Dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"a\", 31);\n          i0.ɵɵelement(45, \"i\", 33);\n          i0.ɵɵelementStart(46, \"span\");\n          i0.ɵɵtext(47, \"Help Center\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"a\", 31);\n          i0.ɵɵelement(49, \"i\", 34);\n          i0.ɵɵelementStart(50, \"span\");\n          i0.ɵɵtext(51, \"My account settings\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 29)(53, \"h6\", 35);\n          i0.ɵɵtext(54, \"Members\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 36)(56, \"a\", 37)(57, \"div\", 5);\n          i0.ɵɵelement(58, \"img\", 38);\n          i0.ɵɵelementStart(59, \"div\", 39)(60, \"h6\", 40);\n          i0.ɵɵtext(61, \"Angela Bernier\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"span\", 41);\n          i0.ɵɵtext(63, \"Manager\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(64, \"a\", 37)(65, \"div\", 5);\n          i0.ɵɵelement(66, \"img\", 42);\n          i0.ɵɵelementStart(67, \"div\", 39)(68, \"h6\", 40);\n          i0.ɵɵtext(69, \"David Grasso\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"span\", 41);\n          i0.ɵɵtext(71, \"Web Designer\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(72, \"a\", 37)(73, \"div\", 5);\n          i0.ɵɵelement(74, \"img\", 43);\n          i0.ɵɵelementStart(75, \"div\", 39)(76, \"h6\", 40);\n          i0.ɵɵtext(77, \"Mike Bunch\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"span\", 41);\n          i0.ɵɵtext(79, \"React Developer\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(80, \"div\", 44)(81, \"a\", 45);\n          i0.ɵɵtext(82, \"View All Results \");\n          i0.ɵɵelement(83, \"i\", 46);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(84, \"div\", 47)(85, \"div\", 48)(86, \"button\", 49);\n          i0.ɵɵelement(87, \"i\", 50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"div\", 51)(89, \"form\", 52)(90, \"div\", 53)(91, \"div\", 54);\n          i0.ɵɵelement(92, \"input\", 55);\n          i0.ɵɵelementStart(93, \"button\", 56);\n          i0.ɵɵelement(94, \"i\", 57);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(95, \"div\", 58)(96, \"button\", 59);\n          i0.ɵɵtemplate(97, TopbarComponent_Conditional_97_Template, 1, 1, \"img\", 60)(98, TopbarComponent_Conditional_98_Template, 1, 1, \"img\", 60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"div\", 61);\n          i0.ɵɵrepeaterCreate(100, TopbarComponent_For_101_Template, 4, 5, \"a\", 62, i0.ɵɵrepeaterTrackByIndex);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(102, \"div\", 63)(103, \"button\", 59);\n          i0.ɵɵelement(104, \"i\", 64);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"div\", 65)(106, \"div\", 66)(107, \"div\", 67)(108, \"div\", 68)(109, \"h6\", 69);\n          i0.ɵɵtext(110, \" Web Apps \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(111, \"div\", 70)(112, \"a\", 71);\n          i0.ɵɵtext(113, \" View All Apps \");\n          i0.ɵɵelement(114, \"i\", 72);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(115, \"div\", 73)(116, \"div\", 74)(117, \"div\", 68)(118, \"a\", 75);\n          i0.ɵɵelement(119, \"img\", 76);\n          i0.ɵɵelementStart(120, \"span\");\n          i0.ɵɵtext(121, \"GitHub\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(122, \"div\", 68)(123, \"a\", 75);\n          i0.ɵɵelement(124, \"img\", 77);\n          i0.ɵɵelementStart(125, \"span\");\n          i0.ɵɵtext(126, \"Bitbucket\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(127, \"div\", 68)(128, \"a\", 75);\n          i0.ɵɵelement(129, \"img\", 78);\n          i0.ɵɵelementStart(130, \"span\");\n          i0.ɵɵtext(131, \"Dribbble\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(132, \"div\", 74)(133, \"div\", 68)(134, \"a\", 75);\n          i0.ɵɵelement(135, \"img\", 79);\n          i0.ɵɵelementStart(136, \"span\");\n          i0.ɵɵtext(137, \"Dropbox\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(138, \"div\", 68)(139, \"a\", 75);\n          i0.ɵɵelement(140, \"img\", 80);\n          i0.ɵɵelementStart(141, \"span\");\n          i0.ɵɵtext(142, \"Mail Chimp\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(143, \"div\", 68)(144, \"a\", 75);\n          i0.ɵɵelement(145, \"img\", 81);\n          i0.ɵɵelementStart(146, \"span\");\n          i0.ɵɵtext(147, \"Slack\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(148, \"div\", 63)(149, \"button\", 82);\n          i0.ɵɵelement(150, \"i\", 83);\n          i0.ɵɵelementStart(151, \"span\", 84);\n          i0.ɵɵtext(152);\n          i0.ɵɵelementStart(153, \"span\", 85);\n          i0.ɵɵtext(154, \"unread messages\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(155, \"div\", 86)(156, \"div\", 66)(157, \"div\", 67)(158, \"div\", 68)(159, \"h6\", 87);\n          i0.ɵɵtext(160, \" My Cart\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(161, \"div\", 70)(162, \"span\", 88);\n          i0.ɵɵtext(163);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(164, \"ngx-simplebar\", 89)(165, \"div\", 73)(166, \"div\", 90)(167, \"div\", 91)(168, \"div\", 92);\n          i0.ɵɵelement(169, \"i\", 93);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(170, \"h5\", 94);\n          i0.ɵɵtext(171, \"Your Cart is Empty!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(172, \"a\", 95);\n          i0.ɵɵtext(173, \"Shop Now\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵrepeaterCreate(174, TopbarComponent_For_175_Template, 19, 7, \"div\", 96, i0.ɵɵrepeaterTrackByIndex);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(176, \"div\", 97)(177, \"div\", 98)(178, \"h5\", 99);\n          i0.ɵɵtext(179, \"Total:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(180, \"div\", 100)(181, \"h5\", 40);\n          i0.ɵɵtext(182, \"$\");\n          i0.ɵɵelementStart(183, \"span\", 101);\n          i0.ɵɵtext(184);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(185, \"a\", 102);\n          i0.ɵɵtext(186, \" Checkout \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(187, \"div\", 103)(188, \"button\", 104);\n          i0.ɵɵlistener(\"click\", function TopbarComponent_Template_button_click_188_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.fullscreen());\n          });\n          i0.ɵɵelement(189, \"i\", 105);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(190, \"div\", 103)(191, \"button\", 106)(192, \"i\", 107);\n          i0.ɵɵlistener(\"click\", function TopbarComponent_Template_i_click_192_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.changeMode(\"dark\"));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(193, \"i\", 108);\n          i0.ɵɵlistener(\"click\", function TopbarComponent_Template_i_click_193_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.changeMode(\"light\"));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(194, \"div\", 109)(195, \"button\", 110);\n          i0.ɵɵelement(196, \"i\", 111);\n          i0.ɵɵelementStart(197, \"span\", 112);\n          i0.ɵɵtext(198, \"3\");\n          i0.ɵɵelementStart(199, \"span\", 85);\n          i0.ɵɵtext(200, \"unread messages\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(201, \"div\", 113)(202, \"div\", 114)(203, \"div\", 52)(204, \"div\", 67)(205, \"div\", 68)(206, \"h6\", 115);\n          i0.ɵɵtext(207, \" Notifications \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(208, \"div\", 116)(209, \"span\", 117);\n          i0.ɵɵtext(210);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(211, \"div\", 118)(212, \"ul\", 119, 0)(214, \"li\", 120)(215, \"a\", 121);\n          i0.ɵɵtext(216);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(217, TopbarComponent_ng_template_217_Template, 7, 2, \"ng-template\", 122);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(218, \"li\", 120)(219, \"a\", 121);\n          i0.ɵɵtext(220, \" Messages \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(221, TopbarComponent_ng_template_221_Template, 7, 2, \"ng-template\", 122);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(222, \"li\", 120)(223, \"a\", 121);\n          i0.ɵɵtext(224, \" Alerts \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(225, TopbarComponent_ng_template_225_Template, 6, 0, \"ng-template\", 122);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(226, \"div\", 123)(227, \"div\", 124);\n          i0.ɵɵelement(228, \"div\", 125);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(229, \"div\", 126)(230, \"div\", 127);\n          i0.ɵɵtext(231, \" Select \");\n          i0.ɵɵelementStart(232, \"div\", 128);\n          i0.ɵɵtext(233);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(234, \" Result \");\n          i0.ɵɵelementStart(235, \"button\", 129);\n          i0.ɵɵlistener(\"click\", function TopbarComponent_Template_button_click_235_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const removenotification_r11 = i0.ɵɵreference(284);\n            return i0.ɵɵresetView(ctx.openModal(removenotification_r11));\n          });\n          i0.ɵɵtext(236, \"Remove\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(237, \"div\", 130)(238, \"button\", 131)(239, \"span\", 47);\n          i0.ɵɵelement(240, \"img\", 132);\n          i0.ɵɵtemplate(241, TopbarComponent_span_241_Template, 5, 2, \"span\", 133)(242, TopbarComponent_span_242_Template, 5, 3, \"span\", 133);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(243, \"div\", 61);\n          i0.ɵɵtemplate(244, TopbarComponent_h6_244_Template, 2, 1, \"h6\", 134)(245, TopbarComponent_h6_245_Template, 2, 0, \"h6\", 134);\n          i0.ɵɵelementStart(246, \"a\", 135);\n          i0.ɵɵelement(247, \"i\", 136);\n          i0.ɵɵelementStart(248, \"span\", 137);\n          i0.ɵɵtext(249, \"Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(250, \"a\", 138);\n          i0.ɵɵelement(251, \"i\", 139);\n          i0.ɵɵelementStart(252, \"span\", 137);\n          i0.ɵɵtext(253, \"Messages\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(254, \"a\", 140);\n          i0.ɵɵelement(255, \"i\", 141);\n          i0.ɵɵelementStart(256, \"span\", 137);\n          i0.ɵɵtext(257, \"Taskboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(258, \"a\", 142);\n          i0.ɵɵelement(259, \"i\", 143);\n          i0.ɵɵelementStart(260, \"span\", 137);\n          i0.ɵɵtext(261, \"Help\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(262, \"div\", 144);\n          i0.ɵɵelementStart(263, \"a\", 135);\n          i0.ɵɵelement(264, \"i\", 145);\n          i0.ɵɵelementStart(265, \"span\", 137);\n          i0.ɵɵtext(266, \"Balance : \");\n          i0.ɵɵelementStart(267, \"b\");\n          i0.ɵɵtext(268, \"$5971.67\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(269, \"a\", 135)(270, \"span\", 146);\n          i0.ɵɵtext(271, \"New\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(272, \"i\", 147);\n          i0.ɵɵelementStart(273, \"span\", 137);\n          i0.ɵɵtext(274, \"Settings\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(275, \"a\", 148);\n          i0.ɵɵelement(276, \"i\", 149);\n          i0.ɵɵelementStart(277, \"span\", 137);\n          i0.ɵɵtext(278, \"Lock screen\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(279, \"a\", 150);\n          i0.ɵɵlistener(\"click\", function TopbarComponent_Template_a_click_279_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.logout());\n          });\n          i0.ɵɵelement(280, \"i\", 151);\n          i0.ɵɵelementStart(281, \"span\", 152);\n          i0.ɵɵtext(282, \"Logout\");\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(283, TopbarComponent_ng_template_283_Template, 16, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const nav_r14 = i0.ɵɵreference(213);\n          i0.ɵɵadvance(97);\n          i0.ɵɵconditional(ctx.flagvalue === undefined ? 97 : 98);\n          i0.ɵɵadvance(3);\n          i0.ɵɵrepeater(ctx.listLang);\n          i0.ɵɵadvance(52);\n          i0.ɵɵtextInterpolate(ctx.cart_length);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\" \", ctx.cart_length, \" items\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵrepeater(ctx.cartData);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(ctx.total);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"autoClose\", false);\n          i0.ɵɵadvance(16);\n          i0.ɵɵtextInterpolate1(\" \", ctx.allnotifications == null ? null : ctx.allnotifications.length, \" New\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"activeId\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngbNavItem\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" All (\", ctx.allnotifications == null ? null : ctx.allnotifications.length, \") \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngbNavItem\", 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavItem\", 3);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngbNavOutlet\", nav_r14);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.checkedValGet.length);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated && ctx.userProfile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated && ctx.userProfile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n        }\n      },\n      dependencies: [i11.NgClass, i11.NgIf, i8.RouterLink, i3.NgbDropdown, i3.NgbDropdownToggle, i3.NgbDropdownMenu, i3.NgbNavContent, i3.NgbNav, i3.NgbNavItem, i3.NgbNavItemRole, i3.NgbNavLink, i3.NgbNavLinkBase, i3.NgbNavOutlet, i12.SimplebarAngularComponent, i13.ɵNgNoValidate, i13.CheckboxControlValueAccessor, i13.NgControlStatus, i13.NgControlStatusGroup, i13.NgModel, i13.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "DOCUMENT", "allNotification", "messages", "cartData", "i0", "ɵɵelement", "ɵɵpropertyInterpolate", "ctx_r1", "valueset", "ɵɵsanitizeUrl", "flagvalue", "ɵɵelementStart", "ɵɵlistener", "TopbarComponent_For_101_Template_a_click_0_listener", "item_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "setLanguage", "text", "lang", "flag", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c1", "cookieValue", "ɵɵadvance", "ɵɵtextInterpolate", "TopbarComponent_For_175_Template_button_click_17_listener", "$event", "data_r6", "_r5", "deleteItem", "id", "ɵɵpropertyInterpolate1", "img", "product", "ɵɵtextInterpolate2", "quantity", "price", "item_r8", "title", "desc", "ɵɵtextInterpolate1", "time", "ɵɵclassMapInterpolate1", "icon", "ɵɵtemplate", "TopbarComponent_ng_template_217_For_4_Conditional_2_Template", "TopbarComponent_ng_template_217_For_4_Conditional_3_Template", "ɵɵtwoWayListener", "TopbarComponent_ng_template_217_For_4_Template_input_ngModelChange_5_listener", "_r7", "ɵɵtwoWayBindingSet", "state", "TopbarComponent_ng_template_217_For_4_Template_input_change_5_listener", "onCheckboxChange", "ɵɵconditional", "ɵɵtwoWayProperty", "allnotifications", "length", "ɵɵrepeaterCreate", "TopbarComponent_ng_template_217_For_4_Template", "ɵɵrepeaterTrackByIndex", "TopbarComponent_ng_template_217_ForEmpty_5_Template", "TopbarComponent_ng_template_217_Conditional_6_Template", "ɵɵrepeater", "TopbarComponent_ng_template_221_For_4_Template_input_ngModelChange_15_listener", "message_r10", "_r9", "TopbarComponent_ng_template_221_For_4_Template_input_change_15_listener", "avatar", "name", "message", "time_ago", "checkboxId", "TopbarComponent_ng_template_221_For_4_Template", "TopbarComponent_ng_template_221_ForEmpty_5_Template", "TopbarComponent_ng_template_221_Conditional_6_Template", "userProfile", "fullName", "username", "keycloakAuthService", "getUserRoleLevel", "userData", "first_name", "last_name", "role", "firstName", "TopbarComponent_ng_template_283_Template_button_click_2_listener", "modal_r13", "_r12", "dismiss", "TopbarComponent_ng_template_283_Template_button_click_12_listener", "TopbarComponent_ng_template_283_Template_button_click_14_listener", "notificationDelete", "TopbarComponent", "constructor", "document", "eventService", "languageService", "modalService", "_cookiesService", "translate", "authService", "authFackservice", "router", "TokenStorageService", "mobileMenuButtonClicked", "total", "cart_length", "totalNotify", "newNotify", "readNotify", "isDropdownOpen", "isAuthenticated", "listLang", "checkedValGet", "ngOnInit", "getUser", "element", "documentElement", "userProfile$", "subscribe", "profile", "get", "val", "filter", "x", "countryName", "map", "undefined", "for<PERSON>ach", "item", "item_price", "toggleMobileMenu", "event", "querySelector", "classList", "toggle", "preventDefault", "emit", "fullscreen", "body", "fullscreenElement", "mozFullScreenElement", "webkitFullscreenElement", "requestFullscreen", "mozRequestFullScreen", "webkitRequestFullscreen", "msRequestFullscreen", "exitFullscreen", "mozCancelFullScreen", "webkitExitFullscreen", "msExitFullscreen", "openModal", "content", "open", "centered", "changeMode", "mode", "broadcast", "setAttribute", "logout", "navigate", "windowScroll", "scrollTop", "getElementById", "style", "display", "add", "remove", "target", "closest", "innerHTML", "Total_price", "toggleDropdown", "stopPropagation", "Search", "searchOptions", "dropdown", "input", "ul", "li", "a", "i", "txtValue", "value", "toUpperCase", "inputLength", "inputVal", "notifyItem", "getElementsByClassName", "Array", "from", "notifiTxt", "spantext", "getElementsByTagName", "innerText", "toLowerCase", "includes", "closeBtn", "searchInputReponsive", "notifyId", "result", "checkedVal", "push", "console", "log", "j", "splice", "calculatenotification", "dismissAll", "ɵɵdirectiveInject", "i1", "EventService", "i2", "LanguageService", "i3", "NgbModal", "i4", "CookieService", "i5", "TranslateService", "i6", "AuthenticationService", "i7", "AuthfakeauthenticationService", "i8", "Router", "i9", "i10", "KeycloakAuthService", "selectors", "viewQuery", "TopbarComponent_Query", "rf", "ctx", "TopbarComponent_Template_header_scroll_0_listener", "_r1", "ɵɵresolveWindow", "TopbarComponent_Template_button_click_15_listener", "TopbarComponent_Template_input_keyup_22_listener", "TopbarComponent_Template_span_click_24_listener", "TopbarComponent_Conditional_97_Template", "TopbarComponent_Conditional_98_Template", "TopbarComponent_For_101_Template", "TopbarComponent_For_175_Template", "TopbarComponent_Template_button_click_188_listener", "TopbarComponent_Template_i_click_192_listener", "TopbarComponent_Template_i_click_193_listener", "TopbarComponent_ng_template_217_Template", "TopbarComponent_ng_template_221_Template", "TopbarComponent_ng_template_225_Template", "TopbarComponent_Template_button_click_235_listener", "removenotification_r11", "ɵɵreference", "TopbarComponent_span_241_Template", "TopbarComponent_span_242_Template", "TopbarComponent_h6_244_Template", "TopbarComponent_h6_245_Template", "TopbarComponent_Template_a_click_279_listener", "TopbarComponent_ng_template_283_Template", "ɵɵtemplateRefExtractor", "nav_r14"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\layouts\\topbar\\topbar.component.ts", "C:\\e-syndic\\esyndic-velzon\\src\\app\\layouts\\topbar\\topbar.component.html"], "sourcesContent": ["import { Component, OnInit, EventEmitter, Output, Inject, ViewChild, TemplateRef } from '@angular/core';\r\nimport { DOCUMENT } from '@angular/common';\r\nimport { EventService } from '../../core/services/event.service';\r\n\r\n//Logout\r\nimport { environment } from '../../../environments/environment';\r\nimport { AuthenticationService } from '../../core/services/auth.service';\r\nimport { AuthfakeauthenticationService } from '../../core/services/authfake.service';\r\nimport { Router } from '@angular/router';\r\nimport { TokenStorageService } from '../../core/services/token-storage.service';\r\n\r\n// Keycloak\r\nimport { KeycloakAuthService, UserProfile } from '../../core/services/keycloak-auth.service';\r\n\r\n// Language\r\nimport { CookieService } from 'ngx-cookie-service';\r\nimport { LanguageService } from '../../core/services/language.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { allNotification, messages } from './data'\r\nimport { CartModel } from './topbar.model';\r\nimport { cartData } from './data';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\n\r\n@Component({\r\n  selector: 'app-topbar',\r\n  templateUrl: './topbar.component.html',\r\n  styleUrls: ['./topbar.component.scss']\r\n})\r\nexport class TopbarComponent implements OnInit {\r\n  messages: any\r\n  element: any;\r\n  mode: string | undefined;\r\n  @Output() mobileMenuButtonClicked = new EventEmitter();\r\n  allnotifications: any\r\n  flagvalue: any;\r\n  valueset: any;\r\n  countryName: any;\r\n  cookieValue: any;\r\n  userData: any;\r\n  cartData!: CartModel[];\r\n  total = 0;\r\n  cart_length: any = 0;\r\n  totalNotify: number = 0;\r\n  newNotify: number = 0;\r\n  readNotify: number = 0;\r\n  isDropdownOpen = false;\r\n  @ViewChild('removenotification') removenotification !: TemplateRef<any>;\r\n  notifyId: any;\r\n\r\n  // Keycloak user profile\r\n  userProfile: UserProfile | null = null;\r\n  isAuthenticated = false;\r\n\r\n  constructor(@Inject(DOCUMENT) private document: any, private eventService: EventService, public languageService: LanguageService, private modalService: NgbModal,\r\n    public _cookiesService: CookieService, public translate: TranslateService, private authService: AuthenticationService, private authFackservice: AuthfakeauthenticationService,\r\n    private router: Router, private TokenStorageService: TokenStorageService, public keycloakAuthService: KeycloakAuthService) { }\r\n\r\n  ngOnInit(): void {\r\n    this.userData = this.TokenStorageService.getUser();\r\n    this.element = document.documentElement;\r\n\r\n    // Load Keycloak user profile\r\n    this.isAuthenticated = this.keycloakAuthService.isAuthenticated();\r\n    if (this.isAuthenticated) {\r\n      this.keycloakAuthService.userProfile$.subscribe(profile => {\r\n        this.userProfile = profile;\r\n      });\r\n    }\r\n\r\n    // Cookies wise Language set\r\n    this.cookieValue = this._cookiesService.get('lang');\r\n    const val = this.listLang.filter(x => x.lang === this.cookieValue);\r\n    this.countryName = val.map(element => element.text);\r\n    if (val.length === 0) {\r\n      if (this.flagvalue === undefined) { this.valueset = 'assets/images/flags/us.svg'; }\r\n    } else {\r\n      this.flagvalue = val.map(element => element.flag);\r\n    }\r\n\r\n    // Fetch Data\r\n    this.allnotifications = allNotification;\r\n\r\n    this.messages = messages;\r\n    this.cartData = cartData;\r\n    this.cart_length = this.cartData.length;\r\n    this.cartData.forEach((item) => {\r\n      var item_price = item.quantity * item.price\r\n      this.total += item_price\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Toggle the menu bar when having mobile screen\r\n   */\r\n  toggleMobileMenu(event: any) {\r\n    document.querySelector('.hamburger-icon')?.classList.toggle('open')\r\n    event.preventDefault();\r\n    this.mobileMenuButtonClicked.emit();\r\n  }\r\n\r\n  /**\r\n   * Fullscreen method\r\n   */\r\n  fullscreen() {\r\n    document.body.classList.toggle('fullscreen-enable');\r\n    if (\r\n      !document.fullscreenElement && !this.element.mozFullScreenElement &&\r\n      !this.element.webkitFullscreenElement) {\r\n      if (this.element.requestFullscreen) {\r\n        this.element.requestFullscreen();\r\n      } else if (this.element.mozRequestFullScreen) {\r\n        /* Firefox */\r\n        this.element.mozRequestFullScreen();\r\n      } else if (this.element.webkitRequestFullscreen) {\r\n        /* Chrome, Safari and Opera */\r\n        this.element.webkitRequestFullscreen();\r\n      } else if (this.element.msRequestFullscreen) {\r\n        /* IE/Edge */\r\n        this.element.msRequestFullscreen();\r\n      }\r\n    } else {\r\n      if (this.document.exitFullscreen) {\r\n        this.document.exitFullscreen();\r\n      } else if (this.document.mozCancelFullScreen) {\r\n        /* Firefox */\r\n        this.document.mozCancelFullScreen();\r\n      } else if (this.document.webkitExitFullscreen) {\r\n        /* Chrome, Safari and Opera */\r\n        this.document.webkitExitFullscreen();\r\n      } else if (this.document.msExitFullscreen) {\r\n        /* IE/Edge */\r\n        this.document.msExitFullscreen();\r\n      }\r\n    }\r\n  }\r\n  /**\r\n* Open modal\r\n* @param content modal content\r\n*/\r\n  openModal(content: any) {\r\n    // this.submitted = false;\r\n    this.modalService.open(content, { centered: true });\r\n  }\r\n\r\n  /**\r\n  * Topbar Light-Dark Mode Change\r\n  */\r\n  changeMode(mode: string) {\r\n    this.mode = mode;\r\n    this.eventService.broadcast('changeMode', mode);\r\n\r\n    switch (mode) {\r\n      case 'light':\r\n        document.documentElement.setAttribute('data-bs-theme', \"light\");\r\n        break;\r\n      case 'dark':\r\n        document.documentElement.setAttribute('data-bs-theme', \"dark\");\r\n        break;\r\n      default:\r\n        document.documentElement.setAttribute('data-bs-theme', \"light\");\r\n        break;\r\n    }\r\n  }\r\n\r\n  /***\r\n   * Language Listing\r\n   */\r\n  listLang = [\r\n    { text: 'English', flag: 'assets/images/flags/us.svg', lang: 'en' },\r\n    { text: 'Española', flag: 'assets/images/flags/spain.svg', lang: 'es' },\r\n    { text: 'Deutsche', flag: 'assets/images/flags/germany.svg', lang: 'de' },\r\n    { text: 'Italiana', flag: 'assets/images/flags/italy.svg', lang: 'it' },\r\n    { text: 'русский', flag: 'assets/images/flags/russia.svg', lang: 'ru' },\r\n    { text: '中国人', flag: 'assets/images/flags/china.svg', lang: 'ch' },\r\n    { text: 'français', flag: 'assets/images/flags/french.svg', lang: 'fr' },\r\n    { text: 'Arabic', flag: 'assets/images/flags/ar.svg', lang: 'ar' },\r\n  ];\r\n\r\n  /***\r\n   * Language Value Set\r\n   */\r\n  setLanguage(text: string, lang: string, flag: string) {\r\n    this.countryName = text;\r\n    this.flagvalue = flag;\r\n    this.cookieValue = lang;\r\n    this.languageService.setLanguage(lang);\r\n  }\r\n\r\n  /**\r\n   * Logout the user\r\n   */\r\n  logout() {\r\n    if (this.isAuthenticated) {\r\n      this.keycloakAuthService.logout();\r\n    } else {\r\n      this.authService.logout();\r\n      this.router.navigate(['/auth/login']);\r\n    }\r\n  }\r\n\r\n  windowScroll() {\r\n    if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {\r\n      (document.getElementById(\"back-to-top\") as HTMLElement).style.display = \"block\";\r\n      document.getElementById('page-topbar')?.classList.add('topbar-shadow');\r\n    } else {\r\n      (document.getElementById(\"back-to-top\") as HTMLElement).style.display = \"none\";\r\n      document.getElementById('page-topbar')?.classList.remove('topbar-shadow');\r\n    }\r\n  }\r\n\r\n  // Delete Item\r\n  deleteItem(event: any, id: any) {\r\n    var price = event.target.closest('.dropdown-item').querySelector('.item_price').innerHTML;\r\n    var Total_price = this.total - price;\r\n    this.total = Total_price;\r\n    this.cart_length = this.cart_length - 1;\r\n    this.total > 1 ? (document.getElementById(\"empty-cart\") as HTMLElement).style.display = \"none\" : (document.getElementById(\"empty-cart\") as HTMLElement).style.display = \"block\";\r\n    document.getElementById('item_' + id)?.remove();\r\n  }\r\n\r\n  toggleDropdown(event: Event) {\r\n    event.stopPropagation();\r\n    if (this.isDropdownOpen) {\r\n      this.isDropdownOpen = false;\r\n    } else {\r\n      this.isDropdownOpen = true;\r\n    }\r\n  }\r\n  // Search Topbar\r\n  Search() {\r\n    var searchOptions = document.getElementById(\"search-close-options\") as HTMLAreaElement;\r\n    var dropdown = document.getElementById(\"search-dropdown\") as HTMLAreaElement;\r\n    var input: any, filter: any, ul: any, li: any, a: any | undefined, i: any, txtValue: any;\r\n    input = document.getElementById(\"search-options\") as HTMLAreaElement;\r\n    filter = input.value.toUpperCase();\r\n    var inputLength = filter.length;\r\n\r\n    if (inputLength > 0) {\r\n      dropdown.classList.add(\"show\");\r\n      searchOptions.classList.remove(\"d-none\");\r\n      var inputVal = input.value.toUpperCase();\r\n      var notifyItem = document.getElementsByClassName(\"notify-item\");\r\n\r\n      Array.from(notifyItem).forEach(function (element: any) {\r\n        var notifiTxt = ''\r\n        if (element.querySelector(\"h6\")) {\r\n          var spantext = element.getElementsByTagName(\"span\")[0].innerText.toLowerCase()\r\n          var name = element.querySelector(\"h6\").innerText.toLowerCase()\r\n          if (name.includes(inputVal)) {\r\n            notifiTxt = name\r\n          } else {\r\n            notifiTxt = spantext\r\n          }\r\n        } else if (element.getElementsByTagName(\"span\")) {\r\n          notifiTxt = element.getElementsByTagName(\"span\")[0].innerText.toLowerCase()\r\n        }\r\n        if (notifiTxt)\r\n          element.style.display = notifiTxt.includes(inputVal) ? \"block\" : \"none\";\r\n\r\n      });\r\n    } else {\r\n      dropdown.classList.remove(\"show\");\r\n      searchOptions.classList.add(\"d-none\");\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Search Close Btn\r\n   */\r\n  closeBtn() {\r\n    var searchOptions = document.getElementById(\"search-close-options\") as HTMLAreaElement;\r\n    var dropdown = document.getElementById(\"search-dropdown\") as HTMLAreaElement;\r\n    var searchInputReponsive = document.getElementById(\"search-options\") as HTMLInputElement;\r\n    dropdown.classList.remove(\"show\");\r\n    searchOptions.classList.add(\"d-none\");\r\n    searchInputReponsive.value = \"\";\r\n  }\r\n\r\n  // Remove Notification\r\n  checkedValGet: any[] = [];\r\n  onCheckboxChange(event: any, id: any) {\r\n    this.notifyId = id\r\n    var result;\r\n    if (id == '1') {\r\n      var checkedVal: any[] = [];\r\n      for (var i = 0; i < this.allnotifications.length; i++) {\r\n        if (this.allnotifications[i].state == true) {\r\n          result = this.allnotifications[i].id;\r\n          checkedVal.push(result);\r\n        }\r\n      }\r\n      this.checkedValGet = checkedVal;\r\n    } else {\r\n      var checkedVal: any[] = [];\r\n      for (var i = 0; i < this.messages.length; i++) {\r\n        if (this.messages[i].state == true) {\r\n          result = this.messages[i].id;\r\n          checkedVal.push(result);\r\n        }\r\n      }\r\n      console.log(checkedVal)\r\n      this.checkedValGet = checkedVal;\r\n    }\r\n    checkedVal.length > 0 ? (document.getElementById(\"notification-actions\") as HTMLElement).style.display = 'block' : (document.getElementById(\"notification-actions\") as HTMLElement).style.display = 'none';\r\n  }\r\n\r\n  notificationDelete() {\r\n    if (this.notifyId == '1') {\r\n      for (var i = 0; i < this.checkedValGet.length; i++) {\r\n        for (var j = 0; j < this.allnotifications.length; j++) {\r\n          if (this.allnotifications[j].id == this.checkedValGet[i]) {\r\n            this.allnotifications.splice(j, 1)\r\n          }\r\n        }\r\n      }\r\n    } else {\r\n      for (var i = 0; i < this.checkedValGet.length; i++) {\r\n        for (var j = 0; j < this.messages.length; j++) {\r\n          if (this.messages[j].id == this.checkedValGet[i]) {\r\n            this.messages.splice(j, 1)\r\n          }\r\n        }\r\n      }\r\n    }\r\n    this.calculatenotification()\r\n    this.modalService.dismissAll();\r\n  }\r\n\r\n  calculatenotification() {\r\n    this.totalNotify = 0;\r\n    this.checkedValGet = []\r\n\r\n    this.checkedValGet.length > 0 ? (document.getElementById(\"notification-actions\") as HTMLElement).style.display = 'block' : (document.getElementById(\"notification-actions\") as HTMLElement).style.display = 'none';\r\n    if (this.totalNotify == 0) {\r\n      document.querySelector('.empty-notification-elem')?.classList.remove('d-none')\r\n    }\r\n  }\r\n}\r\n", "<header id=\"page-topbar\" data-scroll-header (window:scroll)=\"windowScroll()\">\r\n    <div class=\"layout-width\">\r\n        <div class=\"navbar-header\">\r\n            <div class=\"d-flex\">\r\n                <!-- LOGO -->\r\n                <div class=\"navbar-brand-box horizontal-logo\">\r\n                    <a href=\"javascript:void(0);\" class=\"logo logo-dark\">\r\n                        <span class=\"logo-sm\">\r\n                            <img src=\"assets/images/logo-sm.png\" alt=\"\" height=\"22\">\r\n                        </span>\r\n                        <span class=\"logo-lg\">\r\n                            <img src=\"assets/images/logo-dark.png\" alt=\"\" height=\"17\">\r\n                        </span>\r\n                    </a>\r\n\r\n                    <a href=\"javascript:void(0);\" class=\"logo logo-light\">\r\n                        <span class=\"logo-sm\">\r\n                            <img src=\"assets/images/logo-sm.png\" alt=\"\" height=\"22\">\r\n                        </span>\r\n                        <span class=\"logo-lg\">\r\n                            <img src=\"assets/images/logo-light.png\" alt=\"\" height=\"17\">\r\n                        </span>\r\n                    </a>\r\n                </div>\r\n\r\n                <button type=\"button\" class=\"btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger\" id=\"topnav-hamburger-icon\" (click)=\"toggleMobileMenu($event)\">\r\n                    <span class=\"hamburger-icon\">\r\n                        <span></span>\r\n                        <span></span>\r\n                        <span></span>\r\n                    </span>\r\n                </button>\r\n\r\n                <!-- App Search-->\r\n                <form class=\"app-search d-none d-md-block\">\r\n                    <div class=\"position-relative\">\r\n                        <input type=\"text\" class=\"form-control\" placeholder=\"Search...\" autocomplete=\"off\" id=\"search-options\" value=\"\" (keyup)=\"Search()\">\r\n                        <span class=\"mdi mdi-magnify search-widget-icon\"></span>\r\n                        <span class=\"mdi mdi-close-circle search-widget-icon search-widget-icon-close d-none\" id=\"search-close-options\" (click)=\"closeBtn()\"></span>\r\n                    </div>\r\n                    <div class=\"dropdown-menu dropdown-menu-lg\" id=\"search-dropdown\">\r\n                        <ngx-simplebar style=\"max-height: 320px;\">\r\n                            <!-- item-->\r\n                            <div class=\"dropdown-header\">\r\n                                <h6 class=\"text-overflow text-muted mb-0 text-uppercase\">Recent Searches</h6>\r\n                            </div>\r\n\r\n                            <div class=\"dropdown-item bg-transparent text-wrap\">\r\n                                <a href=\"javascript:void(0);\" class=\"btn btn-soft-secondary btn-sm rounded-pill me-1\">how to\r\n                                    setup <i class=\"mdi mdi-magnify ms-1\"></i></a>\r\n                                <a href=\"javascript:void(0);\" class=\"btn btn-soft-secondary btn-sm rounded-pill\">buttons\r\n                                    <i class=\"mdi mdi-magnify ms-1\"></i></a>\r\n                            </div>\r\n                            <!-- item-->\r\n                            <div class=\"dropdown-header mt-2\">\r\n                                <h6 class=\"text-overflow text-muted mb-1 text-uppercase\">Pages</h6>\r\n                            </div>\r\n\r\n                            <!-- item-->\r\n                            <a href=\"javascript:void(0);\" class=\"dropdown-item notify-item\">\r\n                                <i class=\"ri-bubble-chart-line align-middle fs-18 text-muted me-2\"></i>\r\n                                <span>Analytics Dashboard</span>\r\n                            </a>\r\n\r\n                            <!-- item-->\r\n                            <a href=\"javascript:void(0);\" class=\"dropdown-item notify-item\">\r\n                                <i class=\"ri-lifebuoy-line align-middle fs-18 text-muted me-2\"></i>\r\n                                <span>Help Center</span>\r\n                            </a>\r\n\r\n                            <!-- item-->\r\n                            <a href=\"javascript:void(0);\" class=\"dropdown-item notify-item\">\r\n                                <i class=\"ri-user-settings-line align-middle fs-18 text-muted me-2\"></i>\r\n                                <span>My account settings</span>\r\n                            </a>\r\n\r\n                            <!-- item-->\r\n                            <div class=\"dropdown-header mt-2\">\r\n                                <h6 class=\"text-overflow text-muted mb-2 text-uppercase\">Members</h6>\r\n                            </div>\r\n\r\n                            <div class=\"notification-list\">\r\n                                <!-- item -->\r\n                                <a href=\"javascript:void(0);\" class=\"dropdown-item notify-item py-2\">\r\n                                    <div class=\"d-flex\">\r\n                                        <img src=\"assets/images/users/avatar-2.jpg\" class=\"me-3 rounded-circle avatar-xs\" alt=\"user-pic\">\r\n                                        <div class=\"flex-grow-1\">\r\n                                            <h6 class=\"m-0\">Angela Bernier</h6>\r\n                                            <span class=\"fs-11 mb-0 text-muted\">Manager</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </a>\r\n                                <!-- item -->\r\n                                <a href=\"javascript:void(0);\" class=\"dropdown-item notify-item py-2\">\r\n                                    <div class=\"d-flex\">\r\n                                        <img src=\"assets/images/users/avatar-3.jpg\" class=\"me-3 rounded-circle avatar-xs\" alt=\"user-pic\">\r\n                                        <div class=\"flex-grow-1\">\r\n                                            <h6 class=\"m-0\">David Grasso</h6>\r\n                                            <span class=\"fs-11 mb-0 text-muted\">Web Designer</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </a>\r\n                                <!-- item -->\r\n                                <a href=\"javascript:void(0);\" class=\"dropdown-item notify-item py-2\">\r\n                                    <div class=\"d-flex\">\r\n                                        <img src=\"assets/images/users/avatar-5.jpg\" class=\"me-3 rounded-circle avatar-xs\" alt=\"user-pic\">\r\n                                        <div class=\"flex-grow-1\">\r\n                                            <h6 class=\"m-0\">Mike Bunch</h6>\r\n                                            <span class=\"fs-11 mb-0 text-muted\">React Developer</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </a>\r\n                            </div>\r\n                        </ngx-simplebar>\r\n\r\n                        <div class=\"text-center pt-3 pb-1\">\r\n                            <a href=\"pages/search-results\" class=\"btn btn-primary btn-sm\">View All Results <i class=\"ri-arrow-right-line ms-1\"></i></a>\r\n                        </div>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n\r\n            <div class=\"d-flex align-items-center\">\r\n\r\n                <div class=\"dropdown d-md-none topbar-head-dropdown header-item\" ngbDropdown>\r\n                    <button type=\"button\" class=\"btn btn-icon btn-topbar btn-ghost-secondary rounded-circle shadow-none\" id=\"page-header-search-dropdown\" data-bs-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\" ngbDropdownToggle>\r\n                        <i class=\"bx bx-search fs-22\"></i>\r\n                    </button>\r\n                    <div class=\"dropdown-menu dropdown-menu-lg dropdown-menu-end p-0\" aria-labelledby=\"page-header-search-dropdown\" ngbDropdownMenu>\r\n                        <form class=\"p-3\">\r\n                            <div class=\"form-group m-0\">\r\n                                <div class=\"input-group\">\r\n                                    <input type=\"text\" class=\"form-control\" placeholder=\"Search ...\" aria-label=\"Recipient's username\">\r\n                                    <button class=\"btn btn-primary\" type=\"submit\"><i class=\"mdi mdi-magnify\"></i></button>\r\n                                </div>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"dropdown ms-1 topbar-head-dropdown header-item\" ngbDropdown>\r\n                    <button type=\"button\" class=\"btn btn-icon btn-topbar btn-ghost-secondary rounded-circle shadow-none\" data-bs-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\" ngbDropdownToggle>\r\n                       @if(flagvalue === undefined){\r\n                        <img src=\"{{valueset}}\" alt=\"Header Language\" height=\"20\" class=\"rounded\">\r\n                       }@else{\r\n                        <img src=\"{{flagvalue}}\" alt=\"Header Language\" height=\"20\" class=\"rounded\">\r\n                       }\r\n                    </button>\r\n                    <div class=\"dropdown-menu dropdown-menu-end\" ngbDropdownMenu>\r\n\r\n                        <!-- item-->\r\n                        @for(item of listLang;track $index){\r\n                        <a href=\"javascript:void(0);\" class=\"dropdown-item notify-item language py-2\" data-lang=\"eng\" (click)=\"setLanguage(item.text, item.lang, item.flag)\" [ngClass]=\"{'active': cookieValue === item.lang}\">\r\n                            <img src=\"{{item.flag}}\" alt=\"user-image\" class=\"me-2 rounded\" height=\"18\"> <span class=\"align-middle\">{{item.text}}</span>\r\n                        </a>\r\n                    }\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"dropdown topbar-head-dropdown ms-1 header-item\" ngbDropdown>\r\n                    <button type=\"button\" class=\"btn btn-icon btn-topbar btn-ghost-secondary rounded-circle shadow-none\" data-bs-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\" ngbDropdownToggle>\r\n                        <i class='bx bx-category-alt fs-22'></i>\r\n                    </button>\r\n                    <div class=\"dropdown-menu dropdown-menu-lg p-0 dropdown-menu-end\" ngbDropdownMenu>\r\n                        <div class=\"p-3 border-top-0 border-start-0 border-end-0 border-dashed border\">\r\n                            <div class=\"row align-items-center\">\r\n                                <div class=\"col\">\r\n                                    <h6 class=\"m-0 fw-semibold fs-15\"> Web Apps </h6>\r\n                                </div>\r\n                                <div class=\"col-auto\">\r\n                                    <a href=\"javascript:void(0);\" class=\"btn btn-sm btn-soft-info shadow-none\"> View All Apps\r\n                                        <i class=\"ri-arrow-right-s-line align-middle\"></i></a>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"p-2\">\r\n                            <div class=\"row g-0\">\r\n                                <div class=\"col\">\r\n                                    <a class=\"dropdown-icon-item\" href=\"javascript:void(0);\">\r\n                                        <img src=\"assets/images/brands/github.png\" alt=\"Github\">\r\n                                        <span>GitHub</span>\r\n                                    </a>\r\n                                </div>\r\n                                <div class=\"col\">\r\n                                    <a class=\"dropdown-icon-item\" href=\"javascript:void(0);\">\r\n                                        <img src=\"assets/images/brands/bitbucket.png\" alt=\"bitbucket\">\r\n                                        <span>Bitbucket</span>\r\n                                    </a>\r\n                                </div>\r\n                                <div class=\"col\">\r\n                                    <a class=\"dropdown-icon-item\" href=\"javascript:void(0);\">\r\n                                        <img src=\"assets/images/brands/dribbble.png\" alt=\"dribbble\">\r\n                                        <span>Dribbble</span>\r\n                                    </a>\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div class=\"row g-0\">\r\n                                <div class=\"col\">\r\n                                    <a class=\"dropdown-icon-item\" href=\"javascript:void(0);\">\r\n                                        <img src=\"assets/images/brands/dropbox.png\" alt=\"dropbox\">\r\n                                        <span>Dropbox</span>\r\n                                    </a>\r\n                                </div>\r\n                                <div class=\"col\">\r\n                                    <a class=\"dropdown-icon-item\" href=\"javascript:void(0);\">\r\n                                        <img src=\"assets/images/brands/mail_chimp.png\" alt=\"mail_chimp\">\r\n                                        <span>Mail Chimp</span>\r\n                                    </a>\r\n                                </div>\r\n                                <div class=\"col\">\r\n                                    <a class=\"dropdown-icon-item\" href=\"javascript:void(0);\">\r\n                                        <img src=\"assets/images/brands/slack.png\" alt=\"slack\">\r\n                                        <span>Slack</span>\r\n                                    </a>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"dropdown topbar-head-dropdown ms-1 header-item\" ngbDropdown>\r\n                    <button type=\"button\" class=\"btn btn-icon btn-topbar btn-ghost-secondary rounded-circle shadow-none\" id=\"page-header-cart-dropdown\" data-bs-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\" ngbDropdownToggle>\r\n                        <i class='bx bx-shopping-bag fs-22'></i>\r\n                        <span class=\"position-absolute topbar-badge fs-10 translate-middle badge rounded-pill bg-info\">{{cart_length}}<span class=\"visually-hidden\">unread messages</span></span>\r\n                    </button>\r\n                    <div class=\"dropdown-menu dropdown-menu-xl dropdown-menu-end p-0\" aria-labelledby=\"page-header-cart-dropdown\" ngbDropdownMenu>\r\n                        <div class=\"p-3 border-top-0 border-start-0 border-end-0 border-dashed border\">\r\n                            <div class=\"row align-items-center\">\r\n                                <div class=\"col\">\r\n                                    <h6 class=\"m-0 fs-16 fw-semibold\"> My Cart</h6>\r\n                                </div>\r\n                                <div class=\"col-auto\">\r\n                                    <span class=\"badge bg-info-subtle text-info fs-13\"> {{cart_length}} items</span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <ngx-simplebar style=\"max-height: 300px;\">\r\n                            <div class=\"p-2\">\r\n                                <div class=\"text-center empty-cart\" id=\"empty-cart\" style=\"display: none;\">\r\n                                    <div class=\"avatar-md mx-auto my-3\">\r\n                                        <div class=\"avatar-title bg-info-subtle text-info fs-36 rounded-circle\">\r\n                                            <i class='bx bx-cart'></i>\r\n                                        </div>\r\n                                    </div>\r\n                                    <h5 class=\"mb-3\">Your Cart is Empty!</h5>\r\n                                    <a routerLink=\"/ecommerce/products\" class=\"btn btn-success w-md mb-3\">Shop Now</a>\r\n                                </div>\r\n                                @for(data of cartData;track $index){\r\n                                <div class=\"d-block dropdown-item text-wrap px-3 py-2\" id=\"item_{{data.id}}\">\r\n                                    <div class=\"d-flex align-items-center\">\r\n                                        <img src=\"{{data.img}}\" class=\"me-3 rounded-circle avatar-sm p-2 bg-light\" alt=\"user-pic\">\r\n                                        <div class=\"flex-grow-1\">\r\n                                            <h6 class=\"mt-0 mb-1 fs-14\">\r\n                                                <a routerLink=\"/ecommerce/product-detail/1\" class=\"text-reset\">{{data.product}}</a>\r\n                                            </h6>\r\n                                            <p class=\"mb-0 fs-12 text-muted\">\r\n                                                Quantity: <span>{{data.quantity}} x ${{data.price}}</span>\r\n                                            </p>\r\n                                        </div>\r\n                                        <div class=\"px-2\">\r\n                                            <h5 class=\"m-0 fw-normal\">$ <span class=\"item_price\">{{data.quantity *\r\n                                                    data.price}}</span></h5>\r\n                                        </div>\r\n                                        <div class=\"ps-2\">\r\n                                            <button type=\"button\" class=\"btn btn-icon btn-sm btn-ghost-secondary remove-item-btn shadow-none\" (click)=\"deleteItem($event,data.id)\">\r\n                                                <i class=\"ri-close-fill fs-16\"></i>\r\n                                            </button>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            }\r\n                            </div>\r\n                        </ngx-simplebar>\r\n                        <div class=\"p-3 border-bottom-0 border-start-0 border-end-0 border-dashed border d-grid\">\r\n                            <div class=\"d-flex justify-content-between align-items-center pb-3\">\r\n                                <h5 class=\"m-0 text-muted\">Total:</h5>\r\n                                <div class=\"px-2\">\r\n                                    <h5 class=\"m-0\">$<span class=\"total_price\">{{total}}</span></h5>\r\n                                </div>\r\n                            </div>\r\n\r\n                            <a routerLink=\"/ecommerce/checkout\" class=\"btn btn-success text-center\">\r\n                                Checkout\r\n                            </a>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"ms-1 header-item d-none d-sm-flex\">\r\n                    <button type=\"button\" class=\"btn btn-icon btn-topbar btn-ghost-secondary rounded-circle shadow-none\" data-toggle=\"fullscreen\" (click)=\"fullscreen()\">\r\n                        <i class='bx bx-fullscreen fs-22'></i>\r\n                    </button>\r\n                </div>\r\n\r\n                <div class=\"ms-1 header-item d-none d-sm-flex\">\r\n                    <button type=\"button\" class=\"btn btn-icon btn-topbar btn-ghost-secondary rounded-circle shadow-none light-dark-mode\">\r\n                        <i class='bx bx-moon fs-22' (click)=\"changeMode('dark')\"></i>\r\n                        <i class='bx bx-sun fs-22' (click)=\"changeMode('light')\"></i>\r\n                    </button>\r\n                </div>\r\n\r\n                <div ngbDropdown [autoClose]=\"false\" class=\"dropdown topbar-head-dropdown ms-1 header-item\">\r\n                    <button type=\"button\" ngbDropdownToggle class=\"btn btn-icon btn-topbar btn-ghost-secondary rounded-circle shadow-none\" id=\"page-header-notifications-dropdown\" data-bs-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\">\r\n                        <i class='bx bx-bell fs-22'></i>\r\n                        <span class=\"position-absolute topbar-badge fs-10 translate-middle badge rounded-pill bg-danger\">3<span class=\"visually-hidden\">unread messages</span></span>\r\n                    </button>\r\n                    <div ngbDropdownMenu class=\"dropdown-menu dropdown-menu-lg dropdown-menu-end p-0\" aria-labelledby=\"page-header-notifications-dropdown\">\r\n\r\n                        <div class=\"dropdown-head bg-primary  bg-pattern rounded-top\">\r\n                            <div class=\"p-3\">\r\n                                <div class=\"row align-items-center\">\r\n                                    <div class=\"col\">\r\n                                        <h6 class=\"m-0 fs-16 fw-semibold text-white\"> Notifications </h6>\r\n                                    </div>\r\n                                    <div class=\"col-auto dropdown-tabs\">\r\n                                        <span class=\"badge bg-light-subtle text-body fs-13\"> {{allnotifications?.length}} New</span>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div class=\"px-2 pt-2\">\r\n                                <ul ngbNav #nav=\"ngbNav\" [activeId]=\"1\" class=\"nav nav-tabs dropdown-tabs nav-tabs-custom\" id=\"notificationItemsTab\">\r\n                                    <li [ngbNavItem]=\"1\">\r\n                                        <a ngbNavLink>\r\n                                            All ({{allnotifications?.length}})\r\n                                        </a>\r\n                                        <ng-template ngbNavContent>\r\n                                            <div class=\"tab-pane fade show active py-2 ps-2\" id=\"all-noti-tab\" role=\"tabpanel\">\r\n                                                <ngx-simplebar style=\"max-height: 300px;\">\r\n                                                    <div class=\"pe-2\">\r\n                                                        @for(item of allnotifications;track $index){\r\n                                                        <div class=\"text-reset notification-item d-block dropdown-item position-relative\">\r\n                                                            <div class=\" d-flex\">\r\n                                                                @if(item.img){\r\n                                                                    <img src=\"{{item.img}}\" class=\"me-3 rounded-circle avatar-xs\" alt=\"user-pic\">\r\n                                                               \r\n                                                                    <div class=\"flex-grow-1\">\r\n                                                                        <a href=\"javascript:void(0);\" class=\"stretched-link\">\r\n                                                                            <h6 class=\"mt-0 mb-1 fs-13 fw-semibold\">{{item.title}}</h6>\r\n                                                                        </a>\r\n                                                                        <div class=\"fs-13 text-muted\">\r\n                                                                            <p class=\"mb-1\">{{item.desc}}</p>\r\n                                                                        </div>\r\n                                                                        <p class=\"mb-0 fs-11 fw-medium text-uppercase text-muted\">\r\n                                                                            <span><i class=\"mdi mdi-clock-outline\"></i> {{item.time}}</span>\r\n                                                                        </p>\r\n                                                                    </div>\r\n                                                                }@else{\r\n                                                                    <div class=\"avatar-xs me-3\">\r\n                                                                        <span class=\"avatar-title bg-info-subtle text-info rounded-circle fs-16\">\r\n                                                                            <i class=\"bx {{item.icon}}\"></i>\r\n                                                                        </span>\r\n                                                                    </div>\r\n                                                                    <div class=\"flex-grow-1\">\r\n                                                                        <a href=\"javascript:void(0);\" class=\"stretched-link\">\r\n                                                                            <h6 class=\"mt-0 mb-2 lh-base\">{{item.desc}}\r\n                                                                            </h6>\r\n                                                                        </a>\r\n                                                                        <p class=\"mb-0 fs-11 fw-medium text-uppercase text-muted\">\r\n                                                                            <span><i class=\"mdi mdi-clock-outline\"></i>{{item.time}}</span>\r\n                                                                        </p>\r\n                                                                    </div>\r\n                                                                }\r\n                                                                <div class=\"px-2 fs-15\">\r\n                                                                    <input class=\"form-check-input\" type=\"checkbox\" [(ngModel)]=\"item.state\" (change)=\"onCheckboxChange($event,'1')\" value=\"{{item.id}}\">\r\n                                                                </div>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                } @empty{\r\n                                                    <div [ngClass]=\"allnotifications?.length != 0 ?'d-none':''\" class=\"tab-pane p-4\" id=\"alerts-tab\" role=\"tabpanel\" aria-labelledby=\"alerts-tab\">\r\n                                                        <div class=\"w-25 w-sm-50 pt-3 mx-auto\">\r\n                                                            <img src=\"assets/images/svg/bell.svg\" class=\"img-fluid\" alt=\"user-pic\">\r\n                                                        </div>\r\n                                                        <div class=\"text-center pb-5 mt-2\">\r\n                                                            <h6 class=\"fs-18 fw-semibold lh-base\">Hey! You have no any\r\n                                                                notifications </h6>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    }\r\n                                                    @if(allnotifications?.length > 0){\r\n                                                        <div class=\"my-3 text-center\">\r\n                                                            <button type=\"button\" class=\"btn btn-soft-success waves-effect waves-light\">View\r\n                                                                All Notifications <i class=\"ri-arrow-right-line align-middle\"></i></button>\r\n                                                        </div>\r\n                                                    }\r\n                                                </div>\r\n\r\n                                                </ngx-simplebar>\r\n                                            </div>\r\n                                        </ng-template>\r\n                                    </li>\r\n                                    <li [ngbNavItem]=\"2\">\r\n                                        <a ngbNavLink>\r\n                                            Messages\r\n                                        </a>\r\n                                        <ng-template ngbNavContent>\r\n                                            <div class=\"tab-pane fade show active py-2 ps-2\" id=\"all-noti-tab\" role=\"tabpanel\">\r\n                                                <ngx-simplebar style=\"max-height: 300px;\">\r\n                                                    <div class=\"pe-2\">\r\n                                                        @for(message of messages;track $index){\r\n                                                        <div class=\"text-reset notification-item d-block dropdown-item\">\r\n                                                            <div class=\"d-flex\">\r\n                                                                <img src=\"{{message.avatar}}\" class=\"me-3 rounded-circle avatar-xs\" alt=\"user-pic\">\r\n                                                                <div class=\"flex-grow-1\">\r\n                                                                    <a href=\"javascript:void(0);\" class=\"stretched-link\">\r\n                                                                        <h6 class=\"mt-0 mb-1 fs-13 fw-semibold\">{{message.name}}</h6>\r\n                                                                    </a>\r\n                                                                    <div class=\"fs-13 text-muted\">\r\n                                                                        <p class=\"mb-1\">{{message.message}}</p>\r\n                                                                    </div>\r\n                                                                    <p class=\"mb-0 fs-11 fw-medium text-uppercase text-muted\">\r\n                                                                        <span><i class=\"mdi mdi-clock-outline\"></i> {{message.time_ago}}</span>\r\n                                                                    </p>\r\n                                                                </div>\r\n                                                                <div class=\"px-2 fs-15\">\r\n                                                                    <input class=\"form-check-input\" type=\"checkbox\" value=\"{{message.id}}\" [(ngModel)]=\"message.state\" (change)=\"onCheckboxChange($event,'2')\" id=\"{{message.checkboxId}}\">\r\n                                                                    <label class=\"form-check-label\" for=\"{{message.checkboxId}}\"></label>\r\n                                                                </div>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    }@empty {\r\n                                                        <div [ngClass]=\"messages?.length != 0 ?'d-none':''\" class=\"tab-pane p-4\" id=\"alerts-tab\" role=\"tabpanel\" aria-labelledby=\"alerts-tab\">\r\n                                                            <div class=\"w-25 w-sm-50 pt-3 mx-auto\">\r\n                                                                <img src=\"assets/images/svg/bell.svg\" class=\"img-fluid\" alt=\"user-pic\">\r\n                                                            </div>\r\n                                                            <div class=\"text-center pb-5 mt-2\">\r\n                                                                <h6 class=\"fs-18 fw-semibold lh-base\">Hey! You have no any\r\n                                                                    notifications </h6>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        }\r\n                                                        @if(messages?.length > 0){\r\n                                                            <div  class=\"my-3 text-center\">\r\n                                                                <button type=\"button\" class=\"btn btn-soft-success waves-effect waves-light\">View\r\n                                                                    All Messages <i class=\"ri-arrow-right-line align-middle\"></i></button>\r\n                                                            </div>\r\n                                                            }\r\n                                                    </div>\r\n                                                </ngx-simplebar>\r\n                                            </div>\r\n                                        </ng-template>\r\n                                    </li>\r\n                                    <li [ngbNavItem]=\"3\">\r\n                                        <a ngbNavLink>\r\n                                            Alerts\r\n                                        </a>\r\n                                        <ng-template ngbNavContent>\r\n                                            <div class=\"tab-pane p-4\" id=\"alerts-tab\" role=\"tabpanel\" aria-labelledby=\"alerts-tab\">\r\n                                                <div class=\"w-25 w-sm-50 pt-3 mx-auto\">\r\n                                                    <img src=\"assets/images/svg/bell.svg\" class=\"img-fluid\" alt=\"user-pic\">\r\n                                                </div>\r\n                                                <div class=\"text-center pb-5 mt-2\">\r\n                                                    <h6 class=\"fs-18 fw-semibold lh-base\">Hey! You have no any\r\n                                                        notifications </h6>\r\n                                                </div>\r\n                                            </div>\r\n                                        </ng-template>\r\n                                    </li>\r\n\r\n                                </ul>\r\n                            </div>\r\n\r\n                        </div>\r\n\r\n                        <div class=\"tab-content\" id=\"notificationItemsTabContent\">\r\n                            <div class=\"tab-content text-muted\">\r\n                                <div [ngbNavOutlet]=\"nav\"></div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"notification-actions\" id=\"notification-actions\">\r\n                            <div class=\" d-flex text-muted justify-content-center\">\r\n                                Select <div id=\"select-content\" class=\"text-body fw-semibold px-1\">{{checkedValGet.length}}</div> Result <button type=\"button\" class=\"btn btn-link link-danger p-0 ms-3\" data-bs-toggle=\"modal\" (click)=\"openModal(removenotification)\" data-bs-target=\"#removeNotificationModal\">Remove</button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"dropdown ms-sm-3 header-item topbar-user\" ngbDropdown>\r\n                    <button type=\"button\" class=\"btn shadow-none\" id=\"page-header-user-dropdown\" data-bs-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\" ngbDropdownToggle>\r\n                        <span class=\"d-flex align-items-center\">\r\n                            <img class=\"rounded-circle header-profile-user\" src=\"assets/images/users/avatar-1.jpg\" alt=\"Header Avatar\">\r\n                            <span class=\"text-start ms-xl-2\" *ngIf=\"isAuthenticated && userProfile\">\r\n                                <span class=\"d-none d-xl-inline-block ms-1 fw-medium user-name-text\">\r\n                                    {{userProfile.fullName || userProfile.username}}\r\n                                </span>\r\n                                <span class=\"d-none d-xl-block ms-1 fs-12 text-muted user-name-sub-text\">\r\n                                    {{keycloakAuthService.getUserRoleLevel()}}\r\n                                </span>\r\n                            </span>\r\n                            <span class=\"text-start ms-xl-2\" *ngIf=\"!isAuthenticated\">\r\n                                <span class=\"d-none d-xl-inline-block ms-1 fw-medium user-name-text\">{{userData.first_name}}\r\n                                    {{userData.last_name}}</span>\r\n                                <span class=\"d-none d-xl-block ms-1 fs-12 text-muted user-name-sub-text\">{{userData.role\r\n                                    == 0 ? 'Admin':'User'}}</span>\r\n                            </span>\r\n                        </span>\r\n                    </button>\r\n                    <div class=\"dropdown-menu dropdown-menu-end\" ngbDropdownMenu>\r\n                        <!-- item-->\r\n                        <h6 class=\"dropdown-header\" *ngIf=\"isAuthenticated && userProfile\">\r\n                            Welcome {{userProfile.firstName || userProfile.username}}!\r\n                        </h6>\r\n                        <h6 class=\"dropdown-header\" *ngIf=\"!isAuthenticated\">Welcome Anna!</h6>\r\n                        <a class=\"dropdown-item\" routerLink=\"/pages/profile\"><i class=\"mdi mdi-account-circle text-muted fs-16 align-middle me-1\"></i> <span class=\"align-middle\">Profile</span></a>\r\n                        <a class=\"dropdown-item\" routerLink=\"/apps/chat\"><i class=\"mdi mdi-message-text-outline text-muted fs-16 align-middle me-1\"></i> <span class=\"align-middle\">Messages</span></a>\r\n                        <a class=\"dropdown-item\" routerLink=\"/tasks/kanban\"><i class=\"mdi mdi-calendar-check-outline text-muted fs-16 align-middle me-1\"></i> <span class=\"align-middle\">Taskboard</span></a>\r\n                        <a class=\"dropdown-item\" routerLink=\"/pages/faqs\"><i class=\"mdi mdi-lifebuoy text-muted fs-16 align-middle me-1\"></i> <span class=\"align-middle\">Help</span></a>\r\n                        <div class=\"dropdown-divider\"></div>\r\n                        <a class=\"dropdown-item\" routerLink=\"/pages/profile\"><i class=\"mdi mdi-wallet text-muted fs-16 align-middle me-1\"></i> <span class=\"align-middle\">Balance : <b>$5971.67</b></span></a>\r\n                        <a class=\"dropdown-item\" routerLink=\"/pages/profile\"><span class=\"badge bg-success-subtle text-success mt-1 float-end\">New</span><i class=\"mdi mdi-cog-outline text-muted fs-16 align-middle me-1\"></i> <span class=\"align-middle\">Settings</span></a>\r\n                        <a class=\"dropdown-item\" routerLink=\"/auth/lockscreen/basic\"><i class=\"mdi mdi-lock text-muted fs-16 align-middle me-1\"></i> <span class=\"align-middle\">Lock screen</span></a>\r\n                        <a class=\"dropdown-item\" href=\"javascript: void(0);\" (click)=\"logout()\"><i class=\"mdi mdi-logout text-muted fs-16 align-middle me-1\"></i> <span class=\"align-middle\" data-key=\"t-logout\">Logout</span></a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</header>\r\n\r\n<!-- removeNotificationModal -->\r\n<ng-template #removenotification let-modal>\r\n    <div class=\"modal-content\">\r\n        <div class=\"modal-header\">\r\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\" id=\"NotificationModalbtn-close\" (click)=\"modal.dismiss('Cross click')\"></button>\r\n        </div>\r\n        <div class=\"modal-body\">\r\n            <div class=\"mt-2 text-center\">\r\n                <lord-icon src=\"https://cdn.lordicon.com/gsqxdxog.json\" trigger=\"loop\" colors=\"primary:#f7b84b,secondary:#f06548\" style=\"width:100px;height:100px\"></lord-icon>\r\n                <div class=\"mt-4 pt-2 fs-15 mx-4 mx-sm-5\">\r\n                    <h4>Are you sure ?</h4>\r\n                    <p class=\"text-muted mx-4 mb-0\">Are you sure you want to remove this Notification ?</p>\r\n                </div>\r\n            </div>\r\n            <div class=\"d-flex gap-2 justify-content-center mt-4 mb-2\">\r\n                <button type=\"button\" class=\"btn w-sm btn-light\" data-bs-dismiss=\"modal\" (click)=\"modal.dismiss('Cross click')\">Close</button>\r\n                <button type=\"button\" class=\"btn w-sm btn-danger\" id=\"delete-notification\" (click)=\"notificationDelete()\">Yes, Delete It!</button>\r\n            </div>\r\n        </div>\r\n\r\n    </div><!-- /.modal-content -->\r\n\r\n</ng-template>"], "mappings": "AAAA,SAA4BA,YAAY,QAAgD,eAAe;AACvG,SAASC,QAAQ,QAAQ,iBAAiB;AAiB1C,SAASC,eAAe,EAAEC,QAAQ,QAAQ,QAAQ;AAElD,SAASC,QAAQ,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;IC2HTC,EAAA,CAAAC,SAAA,cAA0E;;;;IAArED,EAAA,CAAAE,qBAAA,QAAAC,MAAA,CAAAC,QAAA,EAAAJ,EAAA,CAAAK,aAAA,CAAkB;;;;;IAEvBL,EAAA,CAAAC,SAAA,cAA2E;;;;IAAtED,EAAA,CAAAE,qBAAA,QAAAC,MAAA,CAAAG,SAAA,EAAAN,EAAA,CAAAK,aAAA,CAAmB;;;;;;IAOxBL,EAAA,CAAAO,cAAA,aAAuM;IAAzGP,EAAA,CAAAQ,UAAA,mBAAAC,oDAAA;MAAA,MAAAC,OAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAH,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASZ,MAAA,CAAAa,WAAA,CAAAN,OAAA,CAAAO,IAAA,EAAAP,OAAA,CAAAQ,IAAA,EAAAR,OAAA,CAAAS,IAAA,CAA4C;IAAA,EAAC;IAChJnB,EAAA,CAAAC,SAAA,eAA2E;IAACD,EAAA,CAAAO,cAAA,gBAA2B;IAAAP,EAAA,CAAAoB,MAAA,GAAa;IACxHpB,EADwH,CAAAqB,YAAA,EAAO,EAC3H;;;;;IAFiJrB,EAAA,CAAAsB,UAAA,YAAAtB,EAAA,CAAAuB,eAAA,IAAAC,GAAA,EAAArB,MAAA,CAAAsB,WAAA,KAAAf,OAAA,CAAAQ,IAAA,EAAiD;IAC7LlB,EAAA,CAAA0B,SAAA,EAAmB;IAAnB1B,EAAA,CAAAE,qBAAA,QAAAQ,OAAA,CAAAS,IAAA,EAAAnB,EAAA,CAAAK,aAAA,CAAmB;IAA+EL,EAAA,CAAA0B,SAAA,GAAa;IAAb1B,EAAA,CAAA2B,iBAAA,CAAAjB,OAAA,CAAAO,IAAA,CAAa;;;;;;IAkG5GjB,EADJ,CAAAO,cAAA,cAA6E,cAClC;IACnCP,EAAA,CAAAC,SAAA,eAA0F;IAGlFD,EAFR,CAAAO,cAAA,cAAyB,cACO,aACuC;IAAAP,EAAA,CAAAoB,MAAA,GAAgB;IACnFpB,EADmF,CAAAqB,YAAA,EAAI,EAClF;IACLrB,EAAA,CAAAO,cAAA,aAAiC;IAC7BP,EAAA,CAAAoB,MAAA,kBAAU;IAAApB,EAAA,CAAAO,cAAA,WAAM;IAAAP,EAAA,CAAAoB,MAAA,IAAmC;IAE3DpB,EAF2D,CAAAqB,YAAA,EAAO,EAC1D,EACF;IAEFrB,EADJ,CAAAO,cAAA,gBAAkB,eACY;IAAAP,EAAA,CAAAoB,MAAA,UAAE;IAAApB,EAAA,CAAAO,cAAA,iBAAyB;IAAAP,EAAA,CAAAoB,MAAA,IACjC;IACxBpB,EADwB,CAAAqB,YAAA,EAAO,EAAK,EAC9B;IAEFrB,EADJ,CAAAO,cAAA,gBAAkB,mBACyH;IAArCP,EAAA,CAAAQ,UAAA,mBAAAoB,0DAAAC,MAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAW,aAAA,CAAAoB,GAAA,EAAAlB,SAAA;MAAA,MAAAV,MAAA,GAAAH,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASZ,MAAA,CAAA6B,UAAA,CAAAH,MAAA,EAAAC,OAAA,CAAAG,EAAA,CAA0B;IAAA,EAAC;IAClIjC,EAAA,CAAAC,SAAA,cAAmC;IAInDD,EAHY,CAAAqB,YAAA,EAAS,EACP,EACJ,EACJ;;;;IArBiDrB,EAAA,CAAAkC,sBAAA,gBAAAJ,OAAA,CAAAG,EAAA,KAAqB;IAE/DjC,EAAA,CAAA0B,SAAA,GAAkB;IAAlB1B,EAAA,CAAAE,qBAAA,QAAA4B,OAAA,CAAAK,GAAA,EAAAnC,EAAA,CAAAK,aAAA,CAAkB;IAGgDL,EAAA,CAAA0B,SAAA,GAAgB;IAAhB1B,EAAA,CAAA2B,iBAAA,CAAAG,OAAA,CAAAM,OAAA,CAAgB;IAG/DpC,EAAA,CAAA0B,SAAA,GAAmC;IAAnC1B,EAAA,CAAAqC,kBAAA,KAAAP,OAAA,CAAAQ,QAAA,UAAAR,OAAA,CAAAS,KAAA,KAAmC;IAIFvC,EAAA,CAAA0B,SAAA,GACjC;IADiC1B,EAAA,CAAA2B,iBAAA,CAAAG,OAAA,CAAAQ,QAAA,GAAAR,OAAA,CAAAS,KAAA,CACjC;;;;;IAyEIvC,EAAA,CAAAC,SAAA,eAA6E;IAIrED,EAFR,CAAAO,cAAA,cAAyB,aACgC,cACT;IAAAP,EAAA,CAAAoB,MAAA,GAAc;IAC1DpB,EAD0D,CAAAqB,YAAA,EAAK,EAC3D;IAEArB,EADJ,CAAAO,cAAA,eAA8B,aACV;IAAAP,EAAA,CAAAoB,MAAA,GAAa;IACjCpB,EADiC,CAAAqB,YAAA,EAAI,EAC/B;IAEFrB,EADJ,CAAAO,cAAA,aAA0D,WAChD;IAAAP,EAAA,CAAAC,SAAA,cAAqC;IAACD,EAAA,CAAAoB,MAAA,IAAa;IAEjEpB,EAFiE,CAAAqB,YAAA,EAAO,EAChE,EACF;;;;IAZDrB,EAAA,CAAAE,qBAAA,QAAAsC,OAAA,CAAAL,GAAA,EAAAnC,EAAA,CAAAK,aAAA,CAAkB;IAIyBL,EAAA,CAAA0B,SAAA,GAAc;IAAd1B,EAAA,CAAA2B,iBAAA,CAAAa,OAAA,CAAAC,KAAA,CAAc;IAGtCzC,EAAA,CAAA0B,SAAA,GAAa;IAAb1B,EAAA,CAAA2B,iBAAA,CAAAa,OAAA,CAAAE,IAAA,CAAa;IAGe1C,EAAA,CAAA0B,SAAA,GAAa;IAAb1B,EAAA,CAAA2C,kBAAA,MAAAH,OAAA,CAAAI,IAAA,KAAa;;;;;IAK7D5C,EADJ,CAAAO,cAAA,eAA4B,gBACiD;IACrEP,EAAA,CAAAC,SAAA,QAAgC;IAExCD,EADI,CAAAqB,YAAA,EAAO,EACL;IAGErB,EAFR,CAAAO,cAAA,cAAyB,aACgC,cACnB;IAAAP,EAAA,CAAAoB,MAAA,GAC9B;IACJpB,EADI,CAAAqB,YAAA,EAAK,EACL;IAEArB,EADJ,CAAAO,cAAA,aAA0D,WAChD;IAAAP,EAAA,CAAAC,SAAA,aAAqC;IAAAD,EAAA,CAAAoB,MAAA,IAAa;IAEhEpB,EAFgE,CAAAqB,YAAA,EAAO,EAC/D,EACF;;;;IAXKrB,EAAA,CAAA0B,SAAA,GAAwB;IAAxB1B,EAAA,CAAA6C,sBAAA,QAAAL,OAAA,CAAAM,IAAA,KAAwB;IAKG9C,EAAA,CAAA0B,SAAA,GAC9B;IAD8B1B,EAAA,CAAA2C,kBAAA,KAAAH,OAAA,CAAAE,IAAA,MAC9B;IAG2C1C,EAAA,CAAA0B,SAAA,GAAa;IAAb1B,EAAA,CAAA2B,iBAAA,CAAAa,OAAA,CAAAI,IAAA,CAAa;;;;;;IA3BxE5C,EADJ,CAAAO,cAAA,eAAkF,aACzD;IAehBP,EAdD,CAAA+C,UAAA,IAAAC,4DAAA,QAAc,IAAAC,4DAAA,QAcP;IAiBHjD,EADJ,CAAAO,cAAA,eAAwB,iBACiH;IAArFP,EAAA,CAAAkD,gBAAA,2BAAAC,8EAAAtB,MAAA;MAAA,MAAAW,OAAA,GAAAxC,EAAA,CAAAW,aAAA,CAAAyC,GAAA,EAAAvC,SAAA;MAAAb,EAAA,CAAAqD,kBAAA,CAAAb,OAAA,CAAAc,KAAA,EAAAzB,MAAA,MAAAW,OAAA,CAAAc,KAAA,GAAAzB,MAAA;MAAA,OAAA7B,EAAA,CAAAe,WAAA,CAAAc,MAAA;IAAA,EAAwB;IAAC7B,EAAA,CAAAQ,UAAA,oBAAA+C,uEAAA1B,MAAA;MAAA7B,EAAA,CAAAW,aAAA,CAAAyC,GAAA;MAAA,MAAAjD,MAAA,GAAAH,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAUZ,MAAA,CAAAqD,gBAAA,CAAA3B,MAAA,EAAwB,GAAG,CAAC;IAAA,EAAC;IAG5H7B,EAHY,CAAAqB,YAAA,EAAqI,EACnI,EACJ,EACJ;;;;IAlCErB,EAAA,CAAA0B,SAAA,GA6BC;IA7BD1B,EAAA,CAAAyD,aAAA,CAAAjB,OAAA,CAAAL,GAAA,SA6BC;IAEoHnC,EAAA,CAAA0B,SAAA,GAAmB;IAAnB1B,EAAA,CAAAE,qBAAA,UAAAsC,OAAA,CAAAP,EAAA,CAAmB;IAApFjC,EAAA,CAAA0D,gBAAA,YAAAlB,OAAA,CAAAc,KAAA,CAAwB;;;;;IAMpFtD,EADJ,CAAAO,cAAA,eAA8I,eACnG;IACnCP,EAAA,CAAAC,SAAA,eAAuE;IAC3ED,EAAA,CAAAqB,YAAA,EAAM;IAEFrB,EADJ,CAAAO,cAAA,eAAmC,cACO;IAAAP,EAAA,CAAAoB,MAAA,0CACpB;IAE1BpB,EAF0B,CAAAqB,YAAA,EAAK,EACrB,EACJ;;;;IARDrB,EAAA,CAAAsB,UAAA,aAAAnB,MAAA,CAAAwD,gBAAA,kBAAAxD,MAAA,CAAAwD,gBAAA,CAAAC,MAAA,uBAAsD;;;;;IAYnD5D,EADJ,CAAAO,cAAA,eAA8B,kBACkD;IAAAP,EAAA,CAAAoB,MAAA,8BACtD;IAAApB,EAAA,CAAAC,SAAA,aAAgD;IAC1ED,EAD0E,CAAAqB,YAAA,EAAS,EAC7E;;;;;IAtDVrB,EAFR,CAAAO,cAAA,eAAmF,wBACrC,eACpB;IACdP,EAAA,CAAA6D,gBAAA,IAAAC,8CAAA,oBAAA9D,EAAA,CAAA+D,sBAAA,SAAAC,mDAAA,mBAgDH;IACDhE,EAAA,CAAA+C,UAAA,IAAAkB,sDAAA,mBAAkC;IAS1CjE,EAHI,CAAAqB,YAAA,EAAM,EAEU,EACd;;;;IA1DMrB,EAAA,CAAA0B,SAAA,GAgDH;IAhDG1B,EAAA,CAAAkE,UAAA,CAAA/D,MAAA,CAAAwD,gBAAA,CAgDH;IACD3D,EAAA,CAAA0B,SAAA,GAKC;IALD1B,EAAA,CAAAyD,aAAA,EAAAtD,MAAA,CAAAwD,gBAAA,kBAAAxD,MAAA,CAAAwD,gBAAA,CAAAC,MAAA,eAKC;;;;;;IAiBO5D,EADJ,CAAAO,cAAA,eAAgE,aACxC;IAChBP,EAAA,CAAAC,SAAA,eAAmF;IAG3ED,EAFR,CAAAO,cAAA,cAAyB,aACgC,cACT;IAAAP,EAAA,CAAAoB,MAAA,GAAgB;IAC5DpB,EAD4D,CAAAqB,YAAA,EAAK,EAC7D;IAEArB,EADJ,CAAAO,cAAA,eAA8B,aACV;IAAAP,EAAA,CAAAoB,MAAA,GAAmB;IACvCpB,EADuC,CAAAqB,YAAA,EAAI,EACrC;IAEFrB,EADJ,CAAAO,cAAA,cAA0D,YAChD;IAAAP,EAAA,CAAAC,SAAA,cAAqC;IAACD,EAAA,CAAAoB,MAAA,IAAoB;IAExEpB,EAFwE,CAAAqB,YAAA,EAAO,EACvE,EACF;IAEFrB,EADJ,CAAAO,cAAA,gBAAwB,kBACmJ;IAAhGP,EAAA,CAAAkD,gBAAA,2BAAAiB,+EAAAtC,MAAA;MAAA,MAAAuC,WAAA,GAAApE,EAAA,CAAAW,aAAA,CAAA0D,GAAA,EAAAxD,SAAA;MAAAb,EAAA,CAAAqD,kBAAA,CAAAe,WAAA,CAAAd,KAAA,EAAAzB,MAAA,MAAAuC,WAAA,CAAAd,KAAA,GAAAzB,MAAA;MAAA,OAAA7B,EAAA,CAAAe,WAAA,CAAAc,MAAA;IAAA,EAA2B;IAAC7B,EAAA,CAAAQ,UAAA,oBAAA8D,wEAAAzC,MAAA;MAAA7B,EAAA,CAAAW,aAAA,CAAA0D,GAAA;MAAA,MAAAlE,MAAA,GAAAH,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAUZ,MAAA,CAAAqD,gBAAA,CAAA3B,MAAA,EAAwB,GAAG,CAAC;IAAA,EAAC;IAA1I7B,EAAA,CAAAqB,YAAA,EAAuK;IACvKrB,EAAA,CAAAC,SAAA,kBAAqE;IAGjFD,EAFQ,CAAAqB,YAAA,EAAM,EACJ,EACJ;;;;IAjBOrB,EAAA,CAAA0B,SAAA,GAAwB;IAAxB1B,EAAA,CAAAE,qBAAA,QAAAkE,WAAA,CAAAG,MAAA,EAAAvE,EAAA,CAAAK,aAAA,CAAwB;IAGmBL,EAAA,CAAA0B,SAAA,GAAgB;IAAhB1B,EAAA,CAAA2B,iBAAA,CAAAyC,WAAA,CAAAI,IAAA,CAAgB;IAGxCxE,EAAA,CAAA0B,SAAA,GAAmB;IAAnB1B,EAAA,CAAA2B,iBAAA,CAAAyC,WAAA,CAAAK,OAAA,CAAmB;IAGSzE,EAAA,CAAA0B,SAAA,GAAoB;IAApB1B,EAAA,CAAA2C,kBAAA,MAAAyB,WAAA,CAAAM,QAAA,KAAoB;IAIpB1E,EAAA,CAAA0B,SAAA,GAAsB;IAAtB1B,EAAA,CAAAE,qBAAA,UAAAkE,WAAA,CAAAnC,EAAA,CAAsB;IAAqEjC,EAAA,CAAAE,qBAAA,OAAAkE,WAAA,CAAAO,UAAA,CAA2B;IAA/F3E,EAAA,CAAA0D,gBAAA,YAAAU,WAAA,CAAAd,KAAA,CAA2B;IAClEtD,EAAA,CAAA0B,SAAA,EAA4B;IAA5B1B,EAAA,CAAAE,qBAAA,QAAAkE,WAAA,CAAAO,UAAA,CAA4B;;;;;IAMpE3E,EADJ,CAAAO,cAAA,eAAsI,eAC3F;IACnCP,EAAA,CAAAC,SAAA,eAAuE;IAC3ED,EAAA,CAAAqB,YAAA,EAAM;IAEFrB,EADJ,CAAAO,cAAA,eAAmC,cACO;IAAAP,EAAA,CAAAoB,MAAA,0CACpB;IAE1BpB,EAF0B,CAAAqB,YAAA,EAAK,EACrB,EACJ;;;;IARDrB,EAAA,CAAAsB,UAAA,aAAAnB,MAAA,CAAAL,QAAA,kBAAAK,MAAA,CAAAL,QAAA,CAAA8D,MAAA,uBAA8C;;;;;IAY3C5D,EADJ,CAAAO,cAAA,eAA+B,kBACiD;IAAAP,EAAA,CAAAoB,MAAA,yBAC3D;IAAApB,EAAA,CAAAC,SAAA,aAAgD;IACrED,EADqE,CAAAqB,YAAA,EAAS,EACxE;;;;;IArCdrB,EAFR,CAAAO,cAAA,eAAmF,wBACrC,eACpB;IACdP,EAAA,CAAA6D,gBAAA,IAAAe,8CAAA,qBAAA5E,EAAA,CAAA+D,sBAAA,SAAAc,mDAAA,mBA+BC;IACD7E,EAAA,CAAA+C,UAAA,IAAA+B,sDAAA,mBAA0B;IAQtC9E,EAFQ,CAAAqB,YAAA,EAAM,EACM,EACd;;;;IAxCMrB,EAAA,CAAA0B,SAAA,GA+BC;IA/BD1B,EAAA,CAAAkE,UAAA,CAAA/D,MAAA,CAAAL,QAAA,CA+BC;IACDE,EAAA,CAAA0B,SAAA,GAKK;IALL1B,EAAA,CAAAyD,aAAA,EAAAtD,MAAA,CAAAL,QAAA,kBAAAK,MAAA,CAAAL,QAAA,CAAA8D,MAAA,eAKK;;;;;IAYb5D,EADJ,CAAAO,cAAA,eAAuF,eAC5C;IACnCP,EAAA,CAAAC,SAAA,eAAuE;IAC3ED,EAAA,CAAAqB,YAAA,EAAM;IAEFrB,EADJ,CAAAO,cAAA,eAAmC,cACO;IAAAP,EAAA,CAAAoB,MAAA,0CACpB;IAE1BpB,EAF0B,CAAAqB,YAAA,EAAK,EACrB,EACJ;;;;;IA4BlBrB,EADJ,CAAAO,cAAA,gBAAwE,gBACC;IACjEP,EAAA,CAAAoB,MAAA,GACJ;IAAApB,EAAA,CAAAqB,YAAA,EAAO;IACPrB,EAAA,CAAAO,cAAA,gBAAyE;IACrEP,EAAA,CAAAoB,MAAA,GACJ;IACJpB,EADI,CAAAqB,YAAA,EAAO,EACJ;;;;IALCrB,EAAA,CAAA0B,SAAA,GACJ;IADI1B,EAAA,CAAA2C,kBAAA,MAAAxC,MAAA,CAAA4E,WAAA,CAAAC,QAAA,IAAA7E,MAAA,CAAA4E,WAAA,CAAAE,QAAA,MACJ;IAEIjF,EAAA,CAAA0B,SAAA,GACJ;IADI1B,EAAA,CAAA2C,kBAAA,MAAAxC,MAAA,CAAA+E,mBAAA,CAAAC,gBAAA,QACJ;;;;;IAGAnF,EADJ,CAAAO,cAAA,gBAA0D,gBACe;IAAAP,EAAA,CAAAoB,MAAA,GAC3C;IAAApB,EAAA,CAAAqB,YAAA,EAAO;IACjCrB,EAAA,CAAAO,cAAA,gBAAyE;IAAAP,EAAA,CAAAoB,MAAA,GAC9C;IAC/BpB,EAD+B,CAAAqB,YAAA,EAAO,EAC/B;;;;IAJkErB,EAAA,CAAA0B,SAAA,GAC3C;IAD2C1B,EAAA,CAAAqC,kBAAA,KAAAlC,MAAA,CAAAiF,QAAA,CAAAC,UAAA,OAAAlF,MAAA,CAAAiF,QAAA,CAAAE,SAAA,KAC3C;IAC+CtF,EAAA,CAAA0B,SAAA,GAC9C;IAD8C1B,EAAA,CAAA2B,iBAAA,CAAAxB,MAAA,CAAAiF,QAAA,CAAAG,IAAA,yBAC9C;;;;;IAMnCvF,EAAA,CAAAO,cAAA,aAAmE;IAC/DP,EAAA,CAAAoB,MAAA,GACJ;IAAApB,EAAA,CAAAqB,YAAA,EAAK;;;;IADDrB,EAAA,CAAA0B,SAAA,EACJ;IADI1B,EAAA,CAAA2C,kBAAA,cAAAxC,MAAA,CAAA4E,WAAA,CAAAS,SAAA,IAAArF,MAAA,CAAA4E,WAAA,CAAAE,QAAA,OACJ;;;;;IACAjF,EAAA,CAAAO,cAAA,aAAqD;IAAAP,EAAA,CAAAoB,MAAA,oBAAa;IAAApB,EAAA,CAAAqB,YAAA,EAAK;;;;;;IAqBnFrB,EAFR,CAAAO,cAAA,eAA2B,eACG,kBACoI;IAAvCP,EAAA,CAAAQ,UAAA,mBAAAiF,iEAAA;MAAA,MAAAC,SAAA,GAAA1F,EAAA,CAAAW,aAAA,CAAAgF,IAAA,EAAA9E,SAAA;MAAA,OAAAb,EAAA,CAAAe,WAAA,CAAS2E,SAAA,CAAAE,OAAA,CAAc,aAAa,CAAC;IAAA,EAAC;IAC7J5F,EAD8J,CAAAqB,YAAA,EAAS,EACjK;IAEFrB,EADJ,CAAAO,cAAA,eAAwB,eACU;IAC1BP,EAAA,CAAAC,SAAA,qBAA+J;IAE3JD,EADJ,CAAAO,cAAA,eAA0C,SAClC;IAAAP,EAAA,CAAAoB,MAAA,qBAAc;IAAApB,EAAA,CAAAqB,YAAA,EAAK;IACvBrB,EAAA,CAAAO,cAAA,aAAgC;IAAAP,EAAA,CAAAoB,MAAA,2DAAmD;IAE3FpB,EAF2F,CAAAqB,YAAA,EAAI,EACrF,EACJ;IAEFrB,EADJ,CAAAO,cAAA,gBAA2D,mBACyD;IAAvCP,EAAA,CAAAQ,UAAA,mBAAAqF,kEAAA;MAAA,MAAAH,SAAA,GAAA1F,EAAA,CAAAW,aAAA,CAAAgF,IAAA,EAAA9E,SAAA;MAAA,OAAAb,EAAA,CAAAe,WAAA,CAAS2E,SAAA,CAAAE,OAAA,CAAc,aAAa,CAAC;IAAA,EAAC;IAAC5F,EAAA,CAAAoB,MAAA,aAAK;IAAApB,EAAA,CAAAqB,YAAA,EAAS;IAC9HrB,EAAA,CAAAO,cAAA,mBAA0G;IAA/BP,EAAA,CAAAQ,UAAA,mBAAAsF,kEAAA;MAAA9F,EAAA,CAAAW,aAAA,CAAAgF,IAAA;MAAA,MAAAxF,MAAA,GAAAH,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASZ,MAAA,CAAA4F,kBAAA,EAAoB;IAAA,EAAC;IAAC/F,EAAA,CAAAoB,MAAA,uBAAe;IAIrIpB,EAJqI,CAAAqB,YAAA,EAAS,EAChI,EACJ,EAEJ;;;ADlgBV,OAAM,MAAO2E,eAAe;EAyB1BC,YAAsCC,QAAa,EAAUC,YAA0B,EAASC,eAAgC,EAAUC,YAAsB,EACvJC,eAA8B,EAASC,SAA2B,EAAUC,WAAkC,EAAUC,eAA8C,EACrKC,MAAc,EAAUC,mBAAwC,EAASzB,mBAAwC;IAFrF,KAAAgB,QAAQ,GAARA,QAAQ;IAAe,KAAAC,YAAY,GAAZA,YAAY;IAAuB,KAAAC,eAAe,GAAfA,eAAe;IAA2B,KAAAC,YAAY,GAAZA,YAAY;IAC7I,KAAAC,eAAe,GAAfA,eAAe;IAAwB,KAAAC,SAAS,GAATA,SAAS;IAA4B,KAAAC,WAAW,GAAXA,WAAW;IAAiC,KAAAC,eAAe,GAAfA,eAAe;IACtI,KAAAC,MAAM,GAANA,MAAM;IAAkB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAA8B,KAAAzB,mBAAmB,GAAnBA,mBAAmB;IAvB5F,KAAA0B,uBAAuB,GAAG,IAAIjH,YAAY,EAAE;IAQtD,KAAAkH,KAAK,GAAG,CAAC;IACT,KAAAC,WAAW,GAAQ,CAAC;IACpB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,SAAS,GAAW,CAAC;IACrB,KAAAC,UAAU,GAAW,CAAC;IACtB,KAAAC,cAAc,GAAG,KAAK;IAItB;IACA,KAAAnC,WAAW,GAAuB,IAAI;IACtC,KAAAoC,eAAe,GAAG,KAAK;IAiHvB;;;IAGA,KAAAC,QAAQ,GAAG,CACT;MAAEnG,IAAI,EAAE,SAAS;MAAEE,IAAI,EAAE,4BAA4B;MAAED,IAAI,EAAE;IAAI,CAAE,EACnE;MAAED,IAAI,EAAE,UAAU;MAAEE,IAAI,EAAE,+BAA+B;MAAED,IAAI,EAAE;IAAI,CAAE,EACvE;MAAED,IAAI,EAAE,UAAU;MAAEE,IAAI,EAAE,iCAAiC;MAAED,IAAI,EAAE;IAAI,CAAE,EACzE;MAAED,IAAI,EAAE,UAAU;MAAEE,IAAI,EAAE,+BAA+B;MAAED,IAAI,EAAE;IAAI,CAAE,EACvE;MAAED,IAAI,EAAE,SAAS;MAAEE,IAAI,EAAE,gCAAgC;MAAED,IAAI,EAAE;IAAI,CAAE,EACvE;MAAED,IAAI,EAAE,KAAK;MAAEE,IAAI,EAAE,+BAA+B;MAAED,IAAI,EAAE;IAAI,CAAE,EAClE;MAAED,IAAI,EAAE,UAAU;MAAEE,IAAI,EAAE,gCAAgC;MAAED,IAAI,EAAE;IAAI,CAAE,EACxE;MAAED,IAAI,EAAE,QAAQ;MAAEE,IAAI,EAAE,4BAA4B;MAAED,IAAI,EAAE;IAAI,CAAE,CACnE;IAsGD;IACA,KAAAmG,aAAa,GAAU,EAAE;EAhOsG;EAE/HC,QAAQA,CAAA;IACN,IAAI,CAAClC,QAAQ,GAAG,IAAI,CAACuB,mBAAmB,CAACY,OAAO,EAAE;IAClD,IAAI,CAACC,OAAO,GAAGtB,QAAQ,CAACuB,eAAe;IAEvC;IACA,IAAI,CAACN,eAAe,GAAG,IAAI,CAACjC,mBAAmB,CAACiC,eAAe,EAAE;IACjE,IAAI,IAAI,CAACA,eAAe,EAAE;MACxB,IAAI,CAACjC,mBAAmB,CAACwC,YAAY,CAACC,SAAS,CAACC,OAAO,IAAG;QACxD,IAAI,CAAC7C,WAAW,GAAG6C,OAAO;MAC5B,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAACnG,WAAW,GAAG,IAAI,CAAC6E,eAAe,CAACuB,GAAG,CAAC,MAAM,CAAC;IACnD,MAAMC,GAAG,GAAG,IAAI,CAACV,QAAQ,CAACW,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9G,IAAI,KAAK,IAAI,CAACO,WAAW,CAAC;IAClE,IAAI,CAACwG,WAAW,GAAGH,GAAG,CAACI,GAAG,CAACV,OAAO,IAAIA,OAAO,CAACvG,IAAI,CAAC;IACnD,IAAI6G,GAAG,CAAClE,MAAM,KAAK,CAAC,EAAE;MACpB,IAAI,IAAI,CAACtD,SAAS,KAAK6H,SAAS,EAAE;QAAE,IAAI,CAAC/H,QAAQ,GAAG,4BAA4B;MAAE;IACpF,CAAC,MAAM;MACL,IAAI,CAACE,SAAS,GAAGwH,GAAG,CAACI,GAAG,CAACV,OAAO,IAAIA,OAAO,CAACrG,IAAI,CAAC;IACnD;IAEA;IACA,IAAI,CAACwC,gBAAgB,GAAG9D,eAAe;IAEvC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC+G,WAAW,GAAG,IAAI,CAAC/G,QAAQ,CAAC6D,MAAM;IACvC,IAAI,CAAC7D,QAAQ,CAACqI,OAAO,CAAEC,IAAI,IAAI;MAC7B,IAAIC,UAAU,GAAGD,IAAI,CAAC/F,QAAQ,GAAG+F,IAAI,CAAC9F,KAAK;MAC3C,IAAI,CAACsE,KAAK,IAAIyB,UAAU;IAC1B,CAAC,CAAC;EACJ;EAEA;;;EAGAC,gBAAgBA,CAACC,KAAU;IACzBtC,QAAQ,CAACuC,aAAa,CAAC,iBAAiB,CAAC,EAAEC,SAAS,CAACC,MAAM,CAAC,MAAM,CAAC;IACnEH,KAAK,CAACI,cAAc,EAAE;IACtB,IAAI,CAAChC,uBAAuB,CAACiC,IAAI,EAAE;EACrC;EAEA;;;EAGAC,UAAUA,CAAA;IACR5C,QAAQ,CAAC6C,IAAI,CAACL,SAAS,CAACC,MAAM,CAAC,mBAAmB,CAAC;IACnD,IACE,CAACzC,QAAQ,CAAC8C,iBAAiB,IAAI,CAAC,IAAI,CAACxB,OAAO,CAACyB,oBAAoB,IACjE,CAAC,IAAI,CAACzB,OAAO,CAAC0B,uBAAuB,EAAE;MACvC,IAAI,IAAI,CAAC1B,OAAO,CAAC2B,iBAAiB,EAAE;QAClC,IAAI,CAAC3B,OAAO,CAAC2B,iBAAiB,EAAE;MAClC,CAAC,MAAM,IAAI,IAAI,CAAC3B,OAAO,CAAC4B,oBAAoB,EAAE;QAC5C;QACA,IAAI,CAAC5B,OAAO,CAAC4B,oBAAoB,EAAE;MACrC,CAAC,MAAM,IAAI,IAAI,CAAC5B,OAAO,CAAC6B,uBAAuB,EAAE;QAC/C;QACA,IAAI,CAAC7B,OAAO,CAAC6B,uBAAuB,EAAE;MACxC,CAAC,MAAM,IAAI,IAAI,CAAC7B,OAAO,CAAC8B,mBAAmB,EAAE;QAC3C;QACA,IAAI,CAAC9B,OAAO,CAAC8B,mBAAmB,EAAE;MACpC;IACF,CAAC,MAAM;MACL,IAAI,IAAI,CAACpD,QAAQ,CAACqD,cAAc,EAAE;QAChC,IAAI,CAACrD,QAAQ,CAACqD,cAAc,EAAE;MAChC,CAAC,MAAM,IAAI,IAAI,CAACrD,QAAQ,CAACsD,mBAAmB,EAAE;QAC5C;QACA,IAAI,CAACtD,QAAQ,CAACsD,mBAAmB,EAAE;MACrC,CAAC,MAAM,IAAI,IAAI,CAACtD,QAAQ,CAACuD,oBAAoB,EAAE;QAC7C;QACA,IAAI,CAACvD,QAAQ,CAACuD,oBAAoB,EAAE;MACtC,CAAC,MAAM,IAAI,IAAI,CAACvD,QAAQ,CAACwD,gBAAgB,EAAE;QACzC;QACA,IAAI,CAACxD,QAAQ,CAACwD,gBAAgB,EAAE;MAClC;IACF;EACF;EACA;;;;EAIAC,SAASA,CAACC,OAAY;IACpB;IACA,IAAI,CAACvD,YAAY,CAACwD,IAAI,CAACD,OAAO,EAAE;MAAEE,QAAQ,EAAE;IAAI,CAAE,CAAC;EACrD;EAEA;;;EAGAC,UAAUA,CAACC,IAAY;IACrB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC7D,YAAY,CAAC8D,SAAS,CAAC,YAAY,EAAED,IAAI,CAAC;IAE/C,QAAQA,IAAI;MACV,KAAK,OAAO;QACV9D,QAAQ,CAACuB,eAAe,CAACyC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;QAC/D;MACF,KAAK,MAAM;QACThE,QAAQ,CAACuB,eAAe,CAACyC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;QAC9D;MACF;QACEhE,QAAQ,CAACuB,eAAe,CAACyC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;QAC/D;IACJ;EACF;EAgBA;;;EAGAlJ,WAAWA,CAACC,IAAY,EAAEC,IAAY,EAAEC,IAAY;IAClD,IAAI,CAAC8G,WAAW,GAAGhH,IAAI;IACvB,IAAI,CAACX,SAAS,GAAGa,IAAI;IACrB,IAAI,CAACM,WAAW,GAAGP,IAAI;IACvB,IAAI,CAACkF,eAAe,CAACpF,WAAW,CAACE,IAAI,CAAC;EACxC;EAEA;;;EAGAiJ,MAAMA,CAAA;IACJ,IAAI,IAAI,CAAChD,eAAe,EAAE;MACxB,IAAI,CAACjC,mBAAmB,CAACiF,MAAM,EAAE;IACnC,CAAC,MAAM;MACL,IAAI,CAAC3D,WAAW,CAAC2D,MAAM,EAAE;MACzB,IAAI,CAACzD,MAAM,CAAC0D,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;IACvC;EACF;EAEAC,YAAYA,CAAA;IACV,IAAInE,QAAQ,CAAC6C,IAAI,CAACuB,SAAS,GAAG,GAAG,IAAIpE,QAAQ,CAACuB,eAAe,CAAC6C,SAAS,GAAG,GAAG,EAAE;MAC5EpE,QAAQ,CAACqE,cAAc,CAAC,aAAa,CAAiB,CAACC,KAAK,CAACC,OAAO,GAAG,OAAO;MAC/EvE,QAAQ,CAACqE,cAAc,CAAC,aAAa,CAAC,EAAE7B,SAAS,CAACgC,GAAG,CAAC,eAAe,CAAC;IACxE,CAAC,MAAM;MACJxE,QAAQ,CAACqE,cAAc,CAAC,aAAa,CAAiB,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;MAC9EvE,QAAQ,CAACqE,cAAc,CAAC,aAAa,CAAC,EAAE7B,SAAS,CAACiC,MAAM,CAAC,eAAe,CAAC;IAC3E;EACF;EAEA;EACA3I,UAAUA,CAACwG,KAAU,EAAEvG,EAAO;IAC5B,IAAIM,KAAK,GAAGiG,KAAK,CAACoC,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAACpC,aAAa,CAAC,aAAa,CAAC,CAACqC,SAAS;IACzF,IAAIC,WAAW,GAAG,IAAI,CAAClE,KAAK,GAAGtE,KAAK;IACpC,IAAI,CAACsE,KAAK,GAAGkE,WAAW;IACxB,IAAI,CAACjE,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,CAAC;IACvC,IAAI,CAACD,KAAK,GAAG,CAAC,GAAIX,QAAQ,CAACqE,cAAc,CAAC,YAAY,CAAiB,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM,GAAIvE,QAAQ,CAACqE,cAAc,CAAC,YAAY,CAAiB,CAACC,KAAK,CAACC,OAAO,GAAG,OAAO;IAC/KvE,QAAQ,CAACqE,cAAc,CAAC,OAAO,GAAGtI,EAAE,CAAC,EAAE0I,MAAM,EAAE;EACjD;EAEAK,cAAcA,CAACxC,KAAY;IACzBA,KAAK,CAACyC,eAAe,EAAE;IACvB,IAAI,IAAI,CAAC/D,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,GAAG,KAAK;IAC7B,CAAC,MAAM;MACL,IAAI,CAACA,cAAc,GAAG,IAAI;IAC5B;EACF;EACA;EACAgE,MAAMA,CAAA;IACJ,IAAIC,aAAa,GAAGjF,QAAQ,CAACqE,cAAc,CAAC,sBAAsB,CAAoB;IACtF,IAAIa,QAAQ,GAAGlF,QAAQ,CAACqE,cAAc,CAAC,iBAAiB,CAAoB;IAC5E,IAAIc,KAAU,EAAEtD,MAAW,EAAEuD,EAAO,EAAEC,EAAO,EAAEC,CAAkB,EAAEC,CAAM,EAAEC,QAAa;IACxFL,KAAK,GAAGnF,QAAQ,CAACqE,cAAc,CAAC,gBAAgB,CAAoB;IACpExC,MAAM,GAAGsD,KAAK,CAACM,KAAK,CAACC,WAAW,EAAE;IAClC,IAAIC,WAAW,GAAG9D,MAAM,CAACnE,MAAM;IAE/B,IAAIiI,WAAW,GAAG,CAAC,EAAE;MACnBT,QAAQ,CAAC1C,SAAS,CAACgC,GAAG,CAAC,MAAM,CAAC;MAC9BS,aAAa,CAACzC,SAAS,CAACiC,MAAM,CAAC,QAAQ,CAAC;MACxC,IAAImB,QAAQ,GAAGT,KAAK,CAACM,KAAK,CAACC,WAAW,EAAE;MACxC,IAAIG,UAAU,GAAG7F,QAAQ,CAAC8F,sBAAsB,CAAC,aAAa,CAAC;MAE/DC,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAAC3D,OAAO,CAAC,UAAUZ,OAAY;QACnD,IAAI2E,SAAS,GAAG,EAAE;QAClB,IAAI3E,OAAO,CAACiB,aAAa,CAAC,IAAI,CAAC,EAAE;UAC/B,IAAI2D,QAAQ,GAAG5E,OAAO,CAAC6E,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,SAAS,CAACC,WAAW,EAAE;UAC9E,IAAI/H,IAAI,GAAGgD,OAAO,CAACiB,aAAa,CAAC,IAAI,CAAC,CAAC6D,SAAS,CAACC,WAAW,EAAE;UAC9D,IAAI/H,IAAI,CAACgI,QAAQ,CAACV,QAAQ,CAAC,EAAE;YAC3BK,SAAS,GAAG3H,IAAI;UAClB,CAAC,MAAM;YACL2H,SAAS,GAAGC,QAAQ;UACtB;QACF,CAAC,MAAM,IAAI5E,OAAO,CAAC6E,oBAAoB,CAAC,MAAM,CAAC,EAAE;UAC/CF,SAAS,GAAG3E,OAAO,CAAC6E,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,SAAS,CAACC,WAAW,EAAE;QAC7E;QACA,IAAIJ,SAAS,EACX3E,OAAO,CAACgD,KAAK,CAACC,OAAO,GAAG0B,SAAS,CAACK,QAAQ,CAACV,QAAQ,CAAC,GAAG,OAAO,GAAG,MAAM;MAE3E,CAAC,CAAC;IACJ,CAAC,MAAM;MACLV,QAAQ,CAAC1C,SAAS,CAACiC,MAAM,CAAC,MAAM,CAAC;MACjCQ,aAAa,CAACzC,SAAS,CAACgC,GAAG,CAAC,QAAQ,CAAC;IACvC;EACF;EAEA;;;EAGA+B,QAAQA,CAAA;IACN,IAAItB,aAAa,GAAGjF,QAAQ,CAACqE,cAAc,CAAC,sBAAsB,CAAoB;IACtF,IAAIa,QAAQ,GAAGlF,QAAQ,CAACqE,cAAc,CAAC,iBAAiB,CAAoB;IAC5E,IAAImC,oBAAoB,GAAGxG,QAAQ,CAACqE,cAAc,CAAC,gBAAgB,CAAqB;IACxFa,QAAQ,CAAC1C,SAAS,CAACiC,MAAM,CAAC,MAAM,CAAC;IACjCQ,aAAa,CAACzC,SAAS,CAACgC,GAAG,CAAC,QAAQ,CAAC;IACrCgC,oBAAoB,CAACf,KAAK,GAAG,EAAE;EACjC;EAIAnI,gBAAgBA,CAACgF,KAAU,EAAEvG,EAAO;IAClC,IAAI,CAAC0K,QAAQ,GAAG1K,EAAE;IAClB,IAAI2K,MAAM;IACV,IAAI3K,EAAE,IAAI,GAAG,EAAE;MACb,IAAI4K,UAAU,GAAU,EAAE;MAC1B,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9H,gBAAgB,CAACC,MAAM,EAAE6H,CAAC,EAAE,EAAE;QACrD,IAAI,IAAI,CAAC9H,gBAAgB,CAAC8H,CAAC,CAAC,CAACnI,KAAK,IAAI,IAAI,EAAE;UAC1CsJ,MAAM,GAAG,IAAI,CAACjJ,gBAAgB,CAAC8H,CAAC,CAAC,CAACxJ,EAAE;UACpC4K,UAAU,CAACC,IAAI,CAACF,MAAM,CAAC;QACzB;MACF;MACA,IAAI,CAACvF,aAAa,GAAGwF,UAAU;IACjC,CAAC,MAAM;MACL,IAAIA,UAAU,GAAU,EAAE;MAC1B,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3L,QAAQ,CAAC8D,MAAM,EAAE6H,CAAC,EAAE,EAAE;QAC7C,IAAI,IAAI,CAAC3L,QAAQ,CAAC2L,CAAC,CAAC,CAACnI,KAAK,IAAI,IAAI,EAAE;UAClCsJ,MAAM,GAAG,IAAI,CAAC9M,QAAQ,CAAC2L,CAAC,CAAC,CAACxJ,EAAE;UAC5B4K,UAAU,CAACC,IAAI,CAACF,MAAM,CAAC;QACzB;MACF;MACAG,OAAO,CAACC,GAAG,CAACH,UAAU,CAAC;MACvB,IAAI,CAACxF,aAAa,GAAGwF,UAAU;IACjC;IACAA,UAAU,CAACjJ,MAAM,GAAG,CAAC,GAAIsC,QAAQ,CAACqE,cAAc,CAAC,sBAAsB,CAAiB,CAACC,KAAK,CAACC,OAAO,GAAG,OAAO,GAAIvE,QAAQ,CAACqE,cAAc,CAAC,sBAAsB,CAAiB,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;EAC5M;EAEA1E,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC4G,QAAQ,IAAI,GAAG,EAAE;MACxB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpE,aAAa,CAACzD,MAAM,EAAE6H,CAAC,EAAE,EAAE;QAClD,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtJ,gBAAgB,CAACC,MAAM,EAAEqJ,CAAC,EAAE,EAAE;UACrD,IAAI,IAAI,CAACtJ,gBAAgB,CAACsJ,CAAC,CAAC,CAAChL,EAAE,IAAI,IAAI,CAACoF,aAAa,CAACoE,CAAC,CAAC,EAAE;YACxD,IAAI,CAAC9H,gBAAgB,CAACuJ,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;UACpC;QACF;MACF;IACF,CAAC,MAAM;MACL,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpE,aAAa,CAACzD,MAAM,EAAE6H,CAAC,EAAE,EAAE;QAClD,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACnN,QAAQ,CAAC8D,MAAM,EAAEqJ,CAAC,EAAE,EAAE;UAC7C,IAAI,IAAI,CAACnN,QAAQ,CAACmN,CAAC,CAAC,CAAChL,EAAE,IAAI,IAAI,CAACoF,aAAa,CAACoE,CAAC,CAAC,EAAE;YAChD,IAAI,CAAC3L,QAAQ,CAACoN,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;UAC5B;QACF;MACF;IACF;IACA,IAAI,CAACE,qBAAqB,EAAE;IAC5B,IAAI,CAAC9G,YAAY,CAAC+G,UAAU,EAAE;EAChC;EAEAD,qBAAqBA,CAAA;IACnB,IAAI,CAACpG,WAAW,GAAG,CAAC;IACpB,IAAI,CAACM,aAAa,GAAG,EAAE;IAEvB,IAAI,CAACA,aAAa,CAACzD,MAAM,GAAG,CAAC,GAAIsC,QAAQ,CAACqE,cAAc,CAAC,sBAAsB,CAAiB,CAACC,KAAK,CAACC,OAAO,GAAG,OAAO,GAAIvE,QAAQ,CAACqE,cAAc,CAAC,sBAAsB,CAAiB,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;IAClN,IAAI,IAAI,CAAC1D,WAAW,IAAI,CAAC,EAAE;MACzBb,QAAQ,CAACuC,aAAa,CAAC,0BAA0B,CAAC,EAAEC,SAAS,CAACiC,MAAM,CAAC,QAAQ,CAAC;IAChF;EACF;;;uBApTW3E,eAAe,EAAAhG,EAAA,CAAAqN,iBAAA,CAyBNzN,QAAQ,GAAAI,EAAA,CAAAqN,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAvN,EAAA,CAAAqN,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAzN,EAAA,CAAAqN,iBAAA,CAAAK,EAAA,CAAAC,QAAA,GAAA3N,EAAA,CAAAqN,iBAAA,CAAAO,EAAA,CAAAC,aAAA,GAAA7N,EAAA,CAAAqN,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA/N,EAAA,CAAAqN,iBAAA,CAAAW,EAAA,CAAAC,qBAAA,GAAAjO,EAAA,CAAAqN,iBAAA,CAAAa,EAAA,CAAAC,6BAAA,GAAAnO,EAAA,CAAAqN,iBAAA,CAAAe,EAAA,CAAAC,MAAA,GAAArO,EAAA,CAAAqN,iBAAA,CAAAiB,EAAA,CAAA3H,mBAAA,GAAA3G,EAAA,CAAAqN,iBAAA,CAAAkB,GAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAzBjBxI,eAAe;MAAAyI,SAAA;MAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;UC5B5B5O,EAAA,CAAAO,cAAA,gBAA6E;UAAjCP,EAAA,CAAAQ,UAAA,oBAAAsO,kDAAA;YAAA9O,EAAA,CAAAW,aAAA,CAAAoO,GAAA;YAAA,OAAA/O,EAAA,CAAAe,WAAA,CAAiB8N,GAAA,CAAAxE,YAAA,EAAc;UAAA,UAAArK,EAAA,CAAAgP,eAAA,CAAC;UAOpDhP,EANpB,CAAAO,cAAA,aAA0B,aACK,aACH,aAE8B,WACW,cAC3B;UAClBP,EAAA,CAAAC,SAAA,aAAwD;UAC5DD,EAAA,CAAAqB,YAAA,EAAO;UACPrB,EAAA,CAAAO,cAAA,eAAsB;UAClBP,EAAA,CAAAC,SAAA,cAA0D;UAElED,EADI,CAAAqB,YAAA,EAAO,EACP;UAGArB,EADJ,CAAAO,cAAA,aAAsD,eAC5B;UAClBP,EAAA,CAAAC,SAAA,cAAwD;UAC5DD,EAAA,CAAAqB,YAAA,EAAO;UACPrB,EAAA,CAAAO,cAAA,gBAAsB;UAClBP,EAAA,CAAAC,SAAA,eAA2D;UAGvED,EAFQ,CAAAqB,YAAA,EAAO,EACP,EACF;UAENrB,EAAA,CAAAO,cAAA,kBAAiK;UAAnCP,EAAA,CAAAQ,UAAA,mBAAAyO,kDAAApN,MAAA;YAAA7B,EAAA,CAAAW,aAAA,CAAAoO,GAAA;YAAA,OAAA/O,EAAA,CAAAe,WAAA,CAAS8N,GAAA,CAAAtG,gBAAA,CAAA1G,MAAA,CAAwB;UAAA,EAAC;UAC5J7B,EAAA,CAAAO,cAAA,gBAA6B;UAGzBP,EAFA,CAAAC,SAAA,YAAa,YACA,YACA;UAErBD,EADI,CAAAqB,YAAA,EAAO,EACF;UAKDrB,EAFR,CAAAO,cAAA,gBAA2C,eACR,iBACwG;UAAnBP,EAAA,CAAAQ,UAAA,mBAAA0O,iDAAA;YAAAlP,EAAA,CAAAW,aAAA,CAAAoO,GAAA;YAAA,OAAA/O,EAAA,CAAAe,WAAA,CAAS8N,GAAA,CAAA3D,MAAA,EAAQ;UAAA,EAAC;UAAlIlL,EAAA,CAAAqB,YAAA,EAAmI;UACnIrB,EAAA,CAAAC,SAAA,gBAAwD;UACxDD,EAAA,CAAAO,cAAA,gBAAqI;UAArBP,EAAA,CAAAQ,UAAA,mBAAA2O,gDAAA;YAAAnP,EAAA,CAAAW,aAAA,CAAAoO,GAAA;YAAA,OAAA/O,EAAA,CAAAe,WAAA,CAAS8N,GAAA,CAAApC,QAAA,EAAU;UAAA,EAAC;UACxIzM,EADyI,CAAAqB,YAAA,EAAO,EAC1I;UAKMrB,EAJZ,CAAAO,cAAA,eAAiE,yBACnB,eAET,cACgC;UAAAP,EAAA,CAAAoB,MAAA,uBAAe;UAC5EpB,EAD4E,CAAAqB,YAAA,EAAK,EAC3E;UAGFrB,EADJ,CAAAO,cAAA,eAAoD,aACsC;UAAAP,EAAA,CAAAoB,MAAA,qBAC5E;UAAApB,EAAA,CAAAC,SAAA,aAAoC;UAAAD,EAAA,CAAAqB,YAAA,EAAI;UAClDrB,EAAA,CAAAO,cAAA,aAAiF;UAAAP,EAAA,CAAAoB,MAAA,gBAC7E;UAAApB,EAAA,CAAAC,SAAA,aAAoC;UAC5CD,EAD4C,CAAAqB,YAAA,EAAI,EAC1C;UAGFrB,EADJ,CAAAO,cAAA,eAAkC,cAC2B;UAAAP,EAAA,CAAAoB,MAAA,aAAK;UAClEpB,EADkE,CAAAqB,YAAA,EAAK,EACjE;UAGNrB,EAAA,CAAAO,cAAA,aAAgE;UAC5DP,EAAA,CAAAC,SAAA,aAAuE;UACvED,EAAA,CAAAO,cAAA,YAAM;UAAAP,EAAA,CAAAoB,MAAA,2BAAmB;UAC7BpB,EAD6B,CAAAqB,YAAA,EAAO,EAChC;UAGJrB,EAAA,CAAAO,cAAA,aAAgE;UAC5DP,EAAA,CAAAC,SAAA,aAAmE;UACnED,EAAA,CAAAO,cAAA,YAAM;UAAAP,EAAA,CAAAoB,MAAA,mBAAW;UACrBpB,EADqB,CAAAqB,YAAA,EAAO,EACxB;UAGJrB,EAAA,CAAAO,cAAA,aAAgE;UAC5DP,EAAA,CAAAC,SAAA,aAAwE;UACxED,EAAA,CAAAO,cAAA,YAAM;UAAAP,EAAA,CAAAoB,MAAA,2BAAmB;UAC7BpB,EAD6B,CAAAqB,YAAA,EAAO,EAChC;UAIArB,EADJ,CAAAO,cAAA,eAAkC,cAC2B;UAAAP,EAAA,CAAAoB,MAAA,eAAO;UACpEpB,EADoE,CAAAqB,YAAA,EAAK,EACnE;UAKErB,EAHR,CAAAO,cAAA,eAA+B,aAE0C,cAC7C;UAChBP,EAAA,CAAAC,SAAA,eAAiG;UAE7FD,EADJ,CAAAO,cAAA,eAAyB,cACL;UAAAP,EAAA,CAAAoB,MAAA,sBAAc;UAAApB,EAAA,CAAAqB,YAAA,EAAK;UACnCrB,EAAA,CAAAO,cAAA,gBAAoC;UAAAP,EAAA,CAAAoB,MAAA,eAAO;UAGvDpB,EAHuD,CAAAqB,YAAA,EAAO,EAChD,EACJ,EACN;UAGArB,EADJ,CAAAO,cAAA,aAAqE,cAC7C;UAChBP,EAAA,CAAAC,SAAA,eAAiG;UAE7FD,EADJ,CAAAO,cAAA,eAAyB,cACL;UAAAP,EAAA,CAAAoB,MAAA,oBAAY;UAAApB,EAAA,CAAAqB,YAAA,EAAK;UACjCrB,EAAA,CAAAO,cAAA,gBAAoC;UAAAP,EAAA,CAAAoB,MAAA,oBAAY;UAG5DpB,EAH4D,CAAAqB,YAAA,EAAO,EACrD,EACJ,EACN;UAGArB,EADJ,CAAAO,cAAA,aAAqE,cAC7C;UAChBP,EAAA,CAAAC,SAAA,eAAiG;UAE7FD,EADJ,CAAAO,cAAA,eAAyB,cACL;UAAAP,EAAA,CAAAoB,MAAA,kBAAU;UAAApB,EAAA,CAAAqB,YAAA,EAAK;UAC/BrB,EAAA,CAAAO,cAAA,gBAAoC;UAAAP,EAAA,CAAAoB,MAAA,uBAAe;UAKvEpB,EALuE,CAAAqB,YAAA,EAAO,EACxD,EACJ,EACN,EACF,EACM;UAGZrB,EADJ,CAAAO,cAAA,eAAmC,aAC+B;UAAAP,EAAA,CAAAoB,MAAA,yBAAiB;UAAApB,EAAA,CAAAC,SAAA,aAAwC;UAIvID,EAJuI,CAAAqB,YAAA,EAAI,EACzH,EACJ,EACH,EACL;UAKErB,EAHR,CAAAO,cAAA,eAAuC,eAE0C,kBACoJ;UACzNP,EAAA,CAAAC,SAAA,aAAkC;UACtCD,EAAA,CAAAqB,YAAA,EAAS;UAIGrB,EAHZ,CAAAO,cAAA,eAAgI,gBAC1G,eACc,eACC;UACrBP,EAAA,CAAAC,SAAA,iBAAmG;UACnGD,EAAA,CAAAO,cAAA,kBAA8C;UAAAP,EAAA,CAAAC,SAAA,aAA+B;UAKjGD,EALiG,CAAAqB,YAAA,EAAS,EACpF,EACJ,EACH,EACL,EACJ;UAGFrB,EADJ,CAAAO,cAAA,eAAwE,kBACwH;UAGxLP,EAFD,CAAA+C,UAAA,KAAAqM,uCAAA,kBAA6B,KAAAC,uCAAA,kBAEtB;UAGVrP,EAAA,CAAAqB,YAAA,EAAS;UACTrB,EAAA,CAAAO,cAAA,eAA6D;UAGzDP,EAAA,CAAA6D,gBAAA,MAAAyL,gCAAA,iBAAAtP,EAAA,CAAA+D,sBAAA,CAIH;UAEL/D,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGFrB,EADJ,CAAAO,cAAA,gBAAwE,mBACwH;UACxLP,EAAA,CAAAC,SAAA,cAAwC;UAC5CD,EAAA,CAAAqB,YAAA,EAAS;UAKOrB,EAJhB,CAAAO,cAAA,gBAAkF,gBACC,gBACvC,gBACf,eACqB;UAACP,EAAA,CAAAoB,MAAA,mBAAS;UAChDpB,EADgD,CAAAqB,YAAA,EAAK,EAC/C;UAEFrB,EADJ,CAAAO,cAAA,gBAAsB,cACyD;UAACP,EAAA,CAAAoB,MAAA,wBACxE;UAAApB,EAAA,CAAAC,SAAA,cAAkD;UAGlED,EAHkE,CAAAqB,YAAA,EAAI,EACxD,EACJ,EACJ;UAKMrB,EAHZ,CAAAO,cAAA,gBAAiB,gBACQ,gBACA,cAC4C;UACrDP,EAAA,CAAAC,SAAA,gBAAwD;UACxDD,EAAA,CAAAO,cAAA,aAAM;UAAAP,EAAA,CAAAoB,MAAA,eAAM;UAEpBpB,EAFoB,CAAAqB,YAAA,EAAO,EACnB,EACF;UAEFrB,EADJ,CAAAO,cAAA,gBAAiB,cAC4C;UACrDP,EAAA,CAAAC,SAAA,gBAA8D;UAC9DD,EAAA,CAAAO,cAAA,aAAM;UAAAP,EAAA,CAAAoB,MAAA,kBAAS;UAEvBpB,EAFuB,CAAAqB,YAAA,EAAO,EACtB,EACF;UAEFrB,EADJ,CAAAO,cAAA,gBAAiB,cAC4C;UACrDP,EAAA,CAAAC,SAAA,gBAA4D;UAC5DD,EAAA,CAAAO,cAAA,aAAM;UAAAP,EAAA,CAAAoB,MAAA,iBAAQ;UAG1BpB,EAH0B,CAAAqB,YAAA,EAAO,EACrB,EACF,EACJ;UAIErB,EAFR,CAAAO,cAAA,gBAAqB,gBACA,cAC4C;UACrDP,EAAA,CAAAC,SAAA,gBAA0D;UAC1DD,EAAA,CAAAO,cAAA,aAAM;UAAAP,EAAA,CAAAoB,MAAA,gBAAO;UAErBpB,EAFqB,CAAAqB,YAAA,EAAO,EACpB,EACF;UAEFrB,EADJ,CAAAO,cAAA,gBAAiB,cAC4C;UACrDP,EAAA,CAAAC,SAAA,gBAAgE;UAChED,EAAA,CAAAO,cAAA,aAAM;UAAAP,EAAA,CAAAoB,MAAA,mBAAU;UAExBpB,EAFwB,CAAAqB,YAAA,EAAO,EACvB,EACF;UAEFrB,EADJ,CAAAO,cAAA,gBAAiB,cAC4C;UACrDP,EAAA,CAAAC,SAAA,gBAAsD;UACtDD,EAAA,CAAAO,cAAA,aAAM;UAAAP,EAAA,CAAAoB,MAAA,cAAK;UAMnCpB,EANmC,CAAAqB,YAAA,EAAO,EAClB,EACF,EACJ,EACJ,EACJ,EACJ;UAGFrB,EADJ,CAAAO,cAAA,gBAAwE,mBACuJ;UACvNP,EAAA,CAAAC,SAAA,cAAwC;UACxCD,EAAA,CAAAO,cAAA,iBAA+F;UAAAP,EAAA,CAAAoB,MAAA,KAAe;UAAApB,EAAA,CAAAO,cAAA,iBAA8B;UAAAP,EAAA,CAAAoB,MAAA,wBAAe;UAC/JpB,EAD+J,CAAAqB,YAAA,EAAO,EAAO,EACpK;UAKOrB,EAJhB,CAAAO,cAAA,gBAA8H,gBAC3C,gBACvC,gBACf,eACqB;UAACP,EAAA,CAAAoB,MAAA,iBAAO;UAC9CpB,EAD8C,CAAAqB,YAAA,EAAK,EAC7C;UAEFrB,EADJ,CAAAO,cAAA,gBAAsB,iBACiC;UAACP,EAAA,CAAAoB,MAAA,KAAqB;UAGrFpB,EAHqF,CAAAqB,YAAA,EAAO,EAC9E,EACJ,EACJ;UAKUrB,EAJhB,CAAAO,cAAA,0BAA0C,gBACrB,gBAC8D,gBACnC,gBACwC;UACpEP,EAAA,CAAAC,SAAA,cAA0B;UAElCD,EADI,CAAAqB,YAAA,EAAM,EACJ;UACNrB,EAAA,CAAAO,cAAA,eAAiB;UAAAP,EAAA,CAAAoB,MAAA,4BAAmB;UAAApB,EAAA,CAAAqB,YAAA,EAAK;UACzCrB,EAAA,CAAAO,cAAA,cAAsE;UAAAP,EAAA,CAAAoB,MAAA,iBAAQ;UAClFpB,EADkF,CAAAqB,YAAA,EAAI,EAChF;UACNrB,EAAA,CAAA6D,gBAAA,MAAA0L,gCAAA,oBAAAvP,EAAA,CAAA+D,sBAAA,CAuBH;UAEL/D,EADI,CAAAqB,YAAA,EAAM,EACM;UAGRrB,EAFR,CAAAO,cAAA,gBAAyF,gBACjB,eACrC;UAAAP,EAAA,CAAAoB,MAAA,eAAM;UAAApB,EAAA,CAAAqB,YAAA,EAAK;UAElCrB,EADJ,CAAAO,cAAA,iBAAkB,eACE;UAAAP,EAAA,CAAAoB,MAAA,UAAC;UAAApB,EAAA,CAAAO,cAAA,kBAA0B;UAAAP,EAAA,CAAAoB,MAAA,KAAS;UAE5DpB,EAF4D,CAAAqB,YAAA,EAAO,EAAK,EAC9D,EACJ;UAENrB,EAAA,CAAAO,cAAA,eAAwE;UACpEP,EAAA,CAAAoB,MAAA,mBACJ;UAGZpB,EAHY,CAAAqB,YAAA,EAAI,EACF,EACJ,EACJ;UAGFrB,EADJ,CAAAO,cAAA,iBAA+C,oBAC0G;UAAvBP,EAAA,CAAAQ,UAAA,mBAAAgP,mDAAA;YAAAxP,EAAA,CAAAW,aAAA,CAAAoO,GAAA;YAAA,OAAA/O,EAAA,CAAAe,WAAA,CAAS8N,GAAA,CAAA/F,UAAA,EAAY;UAAA,EAAC;UAChJ9I,EAAA,CAAAC,SAAA,eAAsC;UAE9CD,EADI,CAAAqB,YAAA,EAAS,EACP;UAIErB,EAFR,CAAAO,cAAA,iBAA+C,oBAC0E,eACxD;UAA7BP,EAAA,CAAAQ,UAAA,mBAAAiP,8CAAA;YAAAzP,EAAA,CAAAW,aAAA,CAAAoO,GAAA;YAAA,OAAA/O,EAAA,CAAAe,WAAA,CAAS8N,GAAA,CAAA9E,UAAA,CAAW,MAAM,CAAC;UAAA,EAAC;UAAC/J,EAAA,CAAAqB,YAAA,EAAI;UAC7DrB,EAAA,CAAAO,cAAA,eAAyD;UAA9BP,EAAA,CAAAQ,UAAA,mBAAAkP,8CAAA;YAAA1P,EAAA,CAAAW,aAAA,CAAAoO,GAAA;YAAA,OAAA/O,EAAA,CAAAe,WAAA,CAAS8N,GAAA,CAAA9E,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UAEhE/J,EAFiE,CAAAqB,YAAA,EAAI,EACxD,EACP;UAGFrB,EADJ,CAAAO,cAAA,iBAA4F,oBAC4I;UAChOP,EAAA,CAAAC,SAAA,eAAgC;UAChCD,EAAA,CAAAO,cAAA,kBAAiG;UAAAP,EAAA,CAAAoB,MAAA,UAAC;UAAApB,EAAA,CAAAO,cAAA,iBAA8B;UAAAP,EAAA,CAAAoB,MAAA,wBAAe;UACnJpB,EADmJ,CAAAqB,YAAA,EAAO,EAAO,EACxJ;UAOWrB,EANpB,CAAAO,cAAA,iBAAuI,iBAErE,gBACzC,gBACuB,gBACf,gBACgC;UAACP,EAAA,CAAAoB,MAAA,wBAAc;UAChEpB,EADgE,CAAAqB,YAAA,EAAK,EAC/D;UAEFrB,EADJ,CAAAO,cAAA,iBAAoC,kBACoB;UAACP,EAAA,CAAAoB,MAAA,KAAgC;UAGjGpB,EAHiG,CAAAqB,YAAA,EAAO,EAC1F,EACJ,EACJ;UAKMrB,EAHZ,CAAAO,cAAA,iBAAuB,mBACkG,gBAC5F,eACH;UACVP,EAAA,CAAAoB,MAAA,KACJ;UAAApB,EAAA,CAAAqB,YAAA,EAAI;UACJrB,EAAA,CAAA+C,UAAA,MAAA4M,wCAAA,2BAA2B;UAgE/B3P,EAAA,CAAAqB,YAAA,EAAK;UAEDrB,EADJ,CAAAO,cAAA,gBAAqB,eACH;UACVP,EAAA,CAAAoB,MAAA,mBACJ;UAAApB,EAAA,CAAAqB,YAAA,EAAI;UACJrB,EAAA,CAAA+C,UAAA,MAAA6M,wCAAA,2BAA2B;UA8C/B5P,EAAA,CAAAqB,YAAA,EAAK;UAEDrB,EADJ,CAAAO,cAAA,gBAAqB,eACH;UACVP,EAAA,CAAAoB,MAAA,iBACJ;UAAApB,EAAA,CAAAqB,YAAA,EAAI;UACJrB,EAAA,CAAA+C,UAAA,MAAA8M,wCAAA,2BAA2B;UAgB3C7P,EALY,CAAAqB,YAAA,EAAK,EAEJ,EACH,EAEJ;UAGFrB,EADJ,CAAAO,cAAA,iBAA0D,iBAClB;UAChCP,EAAA,CAAAC,SAAA,iBAAgC;UAExCD,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGFrB,EADJ,CAAAO,cAAA,iBAA4D,iBACD;UACnDP,EAAA,CAAAoB,MAAA,iBAAO;UAAApB,EAAA,CAAAO,cAAA,iBAA4D;UAAAP,EAAA,CAAAoB,MAAA,KAAwB;UAAApB,EAAA,CAAAqB,YAAA,EAAM;UAACrB,EAAA,CAAAoB,MAAA,iBAAO;UAAApB,EAAA,CAAAO,cAAA,oBAAyK;UAAlFP,EAAA,CAAAQ,UAAA,mBAAAsP,mDAAA;YAAA9P,EAAA,CAAAW,aAAA,CAAAoO,GAAA;YAAA,MAAAgB,sBAAA,GAAA/P,EAAA,CAAAgQ,WAAA;YAAA,OAAAhQ,EAAA,CAAAe,WAAA,CAAS8N,GAAA,CAAAlF,SAAA,CAAAoG,sBAAA,CAA6B;UAAA,EAAC;UAA2C/P,EAAA,CAAAoB,MAAA,eAAM;UAIxSpB,EAJwS,CAAAqB,YAAA,EAAS,EAC/R,EACJ,EACJ,EACJ;UAIErB,EAFR,CAAAO,cAAA,iBAAkE,oBACsG,iBACxH;UACpCP,EAAA,CAAAC,SAAA,iBAA2G;UAS3GD,EARA,CAAA+C,UAAA,MAAAkN,iCAAA,oBAAwE,MAAAC,iCAAA,oBAQd;UAOlElQ,EADI,CAAAqB,YAAA,EAAO,EACF;UACTrB,EAAA,CAAAO,cAAA,gBAA6D;UAKzDP,EAHA,CAAA+C,UAAA,MAAAoN,+BAAA,kBAAmE,MAAAC,+BAAA,kBAGd;UACrDpQ,EAAA,CAAAO,cAAA,eAAqD;UAAAP,EAAA,CAAAC,SAAA,eAAyE;UAACD,EAAA,CAAAO,cAAA,kBAA2B;UAAAP,EAAA,CAAAoB,MAAA,gBAAO;UAAOpB,EAAP,CAAAqB,YAAA,EAAO,EAAI;UAC5KrB,EAAA,CAAAO,cAAA,eAAiD;UAAAP,EAAA,CAAAC,SAAA,eAA+E;UAACD,EAAA,CAAAO,cAAA,kBAA2B;UAAAP,EAAA,CAAAoB,MAAA,iBAAQ;UAAOpB,EAAP,CAAAqB,YAAA,EAAO,EAAI;UAC/KrB,EAAA,CAAAO,cAAA,eAAoD;UAAAP,EAAA,CAAAC,SAAA,eAAiF;UAACD,EAAA,CAAAO,cAAA,kBAA2B;UAAAP,EAAA,CAAAoB,MAAA,kBAAS;UAAOpB,EAAP,CAAAqB,YAAA,EAAO,EAAI;UACrLrB,EAAA,CAAAO,cAAA,eAAkD;UAAAP,EAAA,CAAAC,SAAA,eAAmE;UAACD,EAAA,CAAAO,cAAA,kBAA2B;UAAAP,EAAA,CAAAoB,MAAA,aAAI;UAAOpB,EAAP,CAAAqB,YAAA,EAAO,EAAI;UAChKrB,EAAA,CAAAC,SAAA,iBAAoC;UACpCD,EAAA,CAAAO,cAAA,eAAqD;UAAAP,EAAA,CAAAC,SAAA,eAAiE;UAACD,EAAA,CAAAO,cAAA,kBAA2B;UAAAP,EAAA,CAAAoB,MAAA,mBAAU;UAAApB,EAAA,CAAAO,cAAA,UAAG;UAAAP,EAAA,CAAAoB,MAAA,iBAAQ;UAAWpB,EAAX,CAAAqB,YAAA,EAAI,EAAO,EAAI;UACjIrB,EAArD,CAAAO,cAAA,eAAqD,kBAAkE;UAAAP,EAAA,CAAAoB,MAAA,YAAG;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAC,SAAA,eAAsE;UAACD,EAAA,CAAAO,cAAA,kBAA2B;UAAAP,EAAA,CAAAoB,MAAA,iBAAQ;UAAOpB,EAAP,CAAAqB,YAAA,EAAO,EAAI;UACtPrB,EAAA,CAAAO,cAAA,eAA6D;UAAAP,EAAA,CAAAC,SAAA,eAA+D;UAACD,EAAA,CAAAO,cAAA,kBAA2B;UAAAP,EAAA,CAAAoB,MAAA,oBAAW;UAAOpB,EAAP,CAAAqB,YAAA,EAAO,EAAI;UAC9KrB,EAAA,CAAAO,cAAA,eAAwE;UAAnBP,EAAA,CAAAQ,UAAA,mBAAA6P,8CAAA;YAAArQ,EAAA,CAAAW,aAAA,CAAAoO,GAAA;YAAA,OAAA/O,EAAA,CAAAe,WAAA,CAAS8N,GAAA,CAAA1E,MAAA,EAAQ;UAAA,EAAC;UAACnK,EAAA,CAAAC,SAAA,eAAiE;UAACD,EAAA,CAAAO,cAAA,kBAA+C;UAAAP,EAAA,CAAAoB,MAAA,eAAM;UAMvNpB,EANuN,CAAAqB,YAAA,EAAO,EAAI,EACxM,EACJ,EACJ,EACJ,EACJ,EACD;UAGTrB,EAAA,CAAA+C,UAAA,MAAAuN,wCAAA,iCAAAtQ,EAAA,CAAAuQ,sBAAA,CAA2C;;;;UA7XpBvQ,EAAA,CAAA0B,SAAA,IAIC;UAJD1B,EAAA,CAAAyD,aAAA,CAAAoL,GAAA,CAAAvO,SAAA,KAAA6H,SAAA,WAIC;UAKAnI,EAAA,CAAA0B,SAAA,GAIH;UAJG1B,EAAA,CAAAkE,UAAA,CAAA2K,GAAA,CAAAzH,QAAA,CAIH;UAsEkGpH,EAAA,CAAA0B,SAAA,IAAe;UAAf1B,EAAA,CAAA2B,iBAAA,CAAAkN,GAAA,CAAA/H,WAAA,CAAe;UAS9C9G,EAAA,CAAA0B,SAAA,IAAqB;UAArB1B,EAAA,CAAA2C,kBAAA,MAAAkM,GAAA,CAAA/H,WAAA,WAAqB;UAe7E9G,EAAA,CAAA0B,SAAA,IAuBH;UAvBG1B,EAAA,CAAAkE,UAAA,CAAA2K,GAAA,CAAA9O,QAAA,CAuBH;UAOkDC,EAAA,CAAA0B,SAAA,IAAS;UAAT1B,EAAA,CAAA2B,iBAAA,CAAAkN,GAAA,CAAAhI,KAAA,CAAS;UAwBvD7G,EAAA,CAAA0B,SAAA,IAAmB;UAAnB1B,EAAA,CAAAsB,UAAA,oBAAmB;UAcyCtB,EAAA,CAAA0B,SAAA,IAAgC;UAAhC1B,EAAA,CAAA2C,kBAAA,MAAAkM,GAAA,CAAAlL,gBAAA,kBAAAkL,GAAA,CAAAlL,gBAAA,CAAAC,MAAA,SAAgC;UAMpE5D,EAAA,CAAA0B,SAAA,GAAc;UAAd1B,EAAA,CAAAsB,UAAA,eAAc;UAC/BtB,EAAA,CAAA0B,SAAA,GAAgB;UAAhB1B,EAAA,CAAAsB,UAAA,iBAAgB;UAEZtB,EAAA,CAAA0B,SAAA,GACJ;UADI1B,EAAA,CAAA2C,kBAAA,WAAAkM,GAAA,CAAAlL,gBAAA,kBAAAkL,GAAA,CAAAlL,gBAAA,CAAAC,MAAA,OACJ;UAkEA5D,EAAA,CAAA0B,SAAA,GAAgB;UAAhB1B,EAAA,CAAAsB,UAAA,iBAAgB;UAmDhBtB,EAAA,CAAA0B,SAAA,GAAgB;UAAhB1B,EAAA,CAAAsB,UAAA,iBAAgB;UAwBnBtB,EAAA,CAAA0B,SAAA,GAAoB;UAApB1B,EAAA,CAAAsB,UAAA,iBAAAkP,OAAA,CAAoB;UAM0CxQ,EAAA,CAAA0B,SAAA,GAAwB;UAAxB1B,EAAA,CAAA2B,iBAAA,CAAAkN,GAAA,CAAAxH,aAAA,CAAAzD,MAAA,CAAwB;UAU7D5D,EAAA,CAAA0B,SAAA,GAAoC;UAApC1B,EAAA,CAAAsB,UAAA,SAAAuN,GAAA,CAAA1H,eAAA,IAAA0H,GAAA,CAAA9J,WAAA,CAAoC;UAQpC/E,EAAA,CAAA0B,SAAA,EAAsB;UAAtB1B,EAAA,CAAAsB,UAAA,UAAAuN,GAAA,CAAA1H,eAAA,CAAsB;UAU/BnH,EAAA,CAAA0B,SAAA,GAAoC;UAApC1B,EAAA,CAAAsB,UAAA,SAAAuN,GAAA,CAAA1H,eAAA,IAAA0H,GAAA,CAAA9J,WAAA,CAAoC;UAGpC/E,EAAA,CAAA0B,SAAA,EAAsB;UAAtB1B,EAAA,CAAAsB,UAAA,UAAAuN,GAAA,CAAA1H,eAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}