{"ast": null, "code": "import { APP_INITIALIZER } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LayoutsModule } from \"./layouts/layouts.module\";\nimport { PagesModule } from \"./pages/pages.module\";\nimport { CoreModule } from \"./core/core.module\";\n// Auth\nimport { HttpClient, HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { environment } from '../environments/environment';\nimport { initFirebaseBackend } from './authUtils';\nimport { FakeBackendInterceptor } from './core/helpers/fake-backend';\nimport { ErrorInterceptor } from './core/helpers/error.interceptor';\nimport { JwtInterceptor } from './core/helpers/jwt.interceptor';\n// Keycloak\nimport { KeycloakAngularModule, KeycloakService } from 'keycloak-angular';\nimport { KeycloakTokenInterceptor } from './core/interceptors/keycloak-token.interceptor';\n// Language\nimport { TranslateHttpLoader } from '@ngx-translate/http-loader';\nimport { TranslateModule, TranslateLoader } from '@ngx-translate/core';\n// Store\nimport { rootReducer } from './store';\nimport { StoreModule } from '@ngrx/store';\nimport { StoreDevtoolsModule } from '@ngrx/store-devtools';\nimport { EffectsModule } from '@ngrx/effects';\nimport { EcommerceEffects } from './store/Ecommerce/ecommerce_effect';\nimport { ProjectEffects } from './store/Project/project_effect';\nimport { TaskEffects } from './store/Task/task_effect';\nimport { CRMEffects } from './store/CRM/crm_effect';\nimport { CryptoEffects } from './store/Crypto/crypto_effect';\nimport { InvoiceEffects } from './store/Invoice/invoice_effect';\nimport { TicketEffects } from './store/Ticket/ticket_effect';\nimport { FileManagerEffects } from './store/File Manager/filemanager_effect';\nimport { TodoEffects } from './store/Todo/todo_effect';\nimport { ApplicationEffects } from './store/Jobs/jobs_effect';\nimport { ApikeyEffects } from './store/APIKey/apikey_effect';\nimport { AuthenticationEffects } from './store/Authentication/authentication.effects';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@ngrx/store\";\nimport * as i3 from \"@ngrx/store-devtools\";\nimport * as i4 from \"@ngrx/effects\";\nexport function createTranslateLoader(http) {\n  return new TranslateHttpLoader(http, 'assets/i18n/', '.json');\n}\n// Keycloak initialization function\nfunction initializeKeycloak(keycloak) {\n  return () => keycloak.init({\n    config: {\n      url: 'http://localhost:8180',\n      realm: 'esyndic',\n      clientId: 'esyndic-frontend'\n    },\n    initOptions: {\n      onLoad: 'check-sso',\n      silentCheckSsoRedirectUri: window.location.origin + '/assets/silent-check-sso.html',\n      checkLoginIframe: false,\n      pkceMethod: 'S256'\n    },\n    loadUserProfileAtStartUp: true\n  });\n}\nif (environment.defaultauth === 'firebase') {\n  initFirebaseBackend(environment.firebaseConfig);\n} else {\n  FakeBackendInterceptor;\n}\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [KeycloakService, {\n        provide: APP_INITIALIZER,\n        useFactory: initializeKeycloak,\n        multi: true,\n        deps: [KeycloakService]\n      }, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: KeycloakTokenInterceptor,\n        multi: true\n      }, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: JwtInterceptor,\n        multi: true\n      }, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: ErrorInterceptor,\n        multi: true\n      }, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: FakeBackendInterceptor,\n        multi: true\n      }, provideHttpClient(withInterceptorsFromDi())],\n      imports: [TranslateModule.forRoot({\n        defaultLanguage: 'en',\n        loader: {\n          provide: TranslateLoader,\n          useFactory: createTranslateLoader,\n          deps: [HttpClient]\n        }\n      }), BrowserAnimationsModule, BrowserModule, AppRoutingModule, LayoutsModule, PagesModule, CoreModule, KeycloakAngularModule, StoreModule.forRoot(rootReducer), StoreDevtoolsModule.instrument({\n        maxAge: 25,\n        // Retains last 25 states\n        logOnly: environment.production // Restrict extension to log-only mode\n      }), EffectsModule.forRoot([AuthenticationEffects, EcommerceEffects, ProjectEffects, TaskEffects, CRMEffects, CryptoEffects, InvoiceEffects, TicketEffects, FileManagerEffects, TodoEffects, ApplicationEffects, ApikeyEffects])]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [i1.TranslateModule, BrowserAnimationsModule, BrowserModule, AppRoutingModule, LayoutsModule, PagesModule, CoreModule, KeycloakAngularModule, i2.StoreRootModule, i3.StoreDevtoolsModule, i4.EffectsRootModule]\n  });\n})();", "map": {"version": 3, "names": ["APP_INITIALIZER", "BrowserModule", "AppRoutingModule", "AppComponent", "LayoutsModule", "PagesModule", "CoreModule", "HttpClient", "HTTP_INTERCEPTORS", "provideHttpClient", "withInterceptorsFromDi", "BrowserAnimationsModule", "environment", "initFirebaseBackend", "FakeBackendInterceptor", "ErrorInterceptor", "JwtInterceptor", "KeycloakAngularModule", "KeycloakService", "KeycloakTokenInterceptor", "TranslateHttpLoader", "TranslateModule", "Translate<PERSON><PERSON><PERSON>", "rootReducer", "StoreModule", "StoreDevtoolsModule", "EffectsModule", "EcommerceEffects", "ProjectEffects", "TaskEffects", "CRMEffects", "CryptoEffects", "InvoiceEffects", "TicketEffects", "FileManagerEffects", "TodoEffects", "ApplicationEffects", "ApikeyEffects", "AuthenticationEffects", "createTranslateLoader", "http", "initializeKeycloak", "keycloak", "init", "config", "url", "realm", "clientId", "initOptions", "onLoad", "silentCheckSsoRedirectUri", "window", "location", "origin", "checkLoginIframe", "pkceMethod", "loadUserProfileAtStartUp", "<PERSON>auth", "firebaseConfig", "AppModule", "bootstrap", "provide", "useFactory", "multi", "deps", "useClass", "imports", "forRoot", "defaultLanguage", "loader", "instrument", "maxAge", "logOnly", "production", "declarations", "i1", "i2", "StoreRootModule", "i3", "i4", "EffectsRootModule"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule, APP_INITIALIZER } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\n\r\nimport { LayoutsModule } from \"./layouts/layouts.module\";\r\nimport { PagesModule } from \"./pages/pages.module\";\r\nimport { CoreModule } from \"./core/core.module\";\r\n\r\n// Auth\r\nimport { HttpClient, HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { environment } from '../environments/environment';\r\nimport { initFirebaseBackend } from './authUtils';\r\nimport { FakeBackendInterceptor } from './core/helpers/fake-backend';\r\nimport { ErrorInterceptor } from './core/helpers/error.interceptor';\r\nimport { JwtInterceptor } from './core/helpers/jwt.interceptor';\r\n\r\n// Keycloak\r\nimport { KeycloakAngularModule, KeycloakService } from 'keycloak-angular';\r\nimport { KeycloakTokenInterceptor } from './core/interceptors/keycloak-token.interceptor';\r\n\r\n// Language\r\nimport { TranslateHttpLoader } from '@ngx-translate/http-loader';\r\nimport { TranslateModule, TranslateLoader } from '@ngx-translate/core';\r\n// Store\r\nimport { rootReducer } from './store';\r\nimport { StoreModule } from '@ngrx/store';\r\nimport { StoreDevtoolsModule } from '@ngrx/store-devtools';\r\nimport { EffectsModule } from '@ngrx/effects';\r\nimport { EcommerceEffects } from './store/Ecommerce/ecommerce_effect';\r\nimport { ProjectEffects } from './store/Project/project_effect';\r\nimport { TaskEffects } from './store/Task/task_effect';\r\nimport { CRMEffects } from './store/CRM/crm_effect';\r\nimport { CryptoEffects } from './store/Crypto/crypto_effect';\r\nimport { InvoiceEffects } from './store/Invoice/invoice_effect';\r\nimport { TicketEffects } from './store/Ticket/ticket_effect';\r\nimport { FileManagerEffects } from './store/File Manager/filemanager_effect';\r\nimport { TodoEffects } from './store/Todo/todo_effect';\r\nimport { ApplicationEffects } from './store/Jobs/jobs_effect';\r\nimport { ApikeyEffects } from './store/APIKey/apikey_effect';\r\nimport { AuthenticationEffects } from './store/Authentication/authentication.effects';\r\n\r\nexport function createTranslateLoader(http: HttpClient): any {\r\n  return new TranslateHttpLoader(http, 'assets/i18n/', '.json');\r\n}\r\n\r\n// Keycloak initialization function\r\nfunction initializeKeycloak(keycloak: KeycloakService) {\r\n  return () =>\r\n    keycloak.init({\r\n      config: {\r\n        url: 'http://localhost:8180',\r\n        realm: 'esyndic',\r\n        clientId: 'esyndic-frontend'\r\n      },\r\n      initOptions: {\r\n        onLoad: 'check-sso',\r\n        silentCheckSsoRedirectUri: window.location.origin + '/assets/silent-check-sso.html',\r\n        checkLoginIframe: false,\r\n        pkceMethod: 'S256'\r\n      },\r\n      loadUserProfileAtStartUp: true\r\n    });\r\n}\r\n\r\nif (environment.defaultauth === 'firebase') {\r\n  initFirebaseBackend(environment.firebaseConfig);\r\n} else {\r\n  FakeBackendInterceptor;\r\n}\r\n\r\n@NgModule({ declarations: [\r\n        AppComponent\r\n    ],\r\n    bootstrap: [AppComponent], imports: [TranslateModule.forRoot({\r\n            defaultLanguage: 'en',\r\n            loader: {\r\n                provide: TranslateLoader,\r\n                useFactory: (createTranslateLoader),\r\n                deps: [HttpClient]\r\n            }\r\n        }),\r\n        BrowserAnimationsModule,\r\n        BrowserModule,\r\n        AppRoutingModule,\r\n        LayoutsModule,\r\n        PagesModule,\r\n        CoreModule,\r\n        KeycloakAngularModule,\r\n        StoreModule.forRoot(rootReducer),\r\n        StoreDevtoolsModule.instrument({\r\n            maxAge: 25, // Retains last 25 states\r\n            logOnly: environment.production, // Restrict extension to log-only mode\r\n        }),\r\n        EffectsModule.forRoot([\r\n            AuthenticationEffects,\r\n            EcommerceEffects,\r\n            ProjectEffects,\r\n            TaskEffects,\r\n            CRMEffects,\r\n            CryptoEffects,\r\n            InvoiceEffects,\r\n            TicketEffects,\r\n            FileManagerEffects,\r\n            TodoEffects,\r\n            ApplicationEffects,\r\n            ApikeyEffects\r\n        ])], providers: [\r\n        KeycloakService,\r\n        {\r\n            provide: APP_INITIALIZER,\r\n            useFactory: initializeKeycloak,\r\n            multi: true,\r\n            deps: [KeycloakService]\r\n        },\r\n        { provide: HTTP_INTERCEPTORS, useClass: KeycloakTokenInterceptor, multi: true },\r\n        { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },\r\n        { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },\r\n        { provide: HTTP_INTERCEPTORS, useClass: FakeBackendInterceptor, multi: true },\r\n        provideHttpClient(withInterceptorsFromDi()),\r\n    ] })\r\nexport class AppModule { }\r\n"], "mappings": "AAAA,SAAmBA,eAAe,QAAQ,eAAe;AACzD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,UAAU,QAAQ,oBAAoB;AAE/C;AACA,SAASC,UAAU,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,sBAAsB,QAAQ,sBAAsB;AAC/G,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,mBAAmB,QAAQ,aAAa;AACjD,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,cAAc,QAAQ,gCAAgC;AAE/D;AACA,SAASC,qBAAqB,EAAEC,eAAe,QAAQ,kBAAkB;AACzE,SAASC,wBAAwB,QAAQ,gDAAgD;AAEzF;AACA,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,eAAe,EAAEC,eAAe,QAAQ,qBAAqB;AACtE;AACA,SAASC,WAAW,QAAQ,SAAS;AACrC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,aAAa,QAAQ,eAAe;AAC7C,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,qBAAqB,QAAQ,+CAA+C;;;;;;AAErF,OAAM,SAAUC,qBAAqBA,CAACC,IAAgB;EACpD,OAAO,IAAIpB,mBAAmB,CAACoB,IAAI,EAAE,cAAc,EAAE,OAAO,CAAC;AAC/D;AAEA;AACA,SAASC,kBAAkBA,CAACC,QAAyB;EACnD,OAAO,MACLA,QAAQ,CAACC,IAAI,CAAC;IACZC,MAAM,EAAE;MACNC,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE;KACX;IACDC,WAAW,EAAE;MACXC,MAAM,EAAE,WAAW;MACnBC,yBAAyB,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM,GAAG,+BAA+B;MACnFC,gBAAgB,EAAE,KAAK;MACvBC,UAAU,EAAE;KACb;IACDC,wBAAwB,EAAE;GAC3B,CAAC;AACN;AAEA,IAAI5C,WAAW,CAAC6C,WAAW,KAAK,UAAU,EAAE;EAC1C5C,mBAAmB,CAACD,WAAW,CAAC8C,cAAc,CAAC;AACjD,CAAC,MAAM;EACL5C,sBAAsB;AACxB;AAoDA,OAAM,MAAO6C,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GA/CNzD,YAAY;IAAA;EAAA;;;iBAiCJ,CAChBe,eAAe,EACf;QACI2C,OAAO,EAAE7D,eAAe;QACxB8D,UAAU,EAAErB,kBAAkB;QAC9BsB,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE,CAAC9C,eAAe;OACzB,EACD;QAAE2C,OAAO,EAAErD,iBAAiB;QAAEyD,QAAQ,EAAE9C,wBAAwB;QAAE4C,KAAK,EAAE;MAAI,CAAE,EAC/E;QAAEF,OAAO,EAAErD,iBAAiB;QAAEyD,QAAQ,EAAEjD,cAAc;QAAE+C,KAAK,EAAE;MAAI,CAAE,EACrE;QAAEF,OAAO,EAAErD,iBAAiB;QAAEyD,QAAQ,EAAElD,gBAAgB;QAAEgD,KAAK,EAAE;MAAI,CAAE,EACvE;QAAEF,OAAO,EAAErD,iBAAiB;QAAEyD,QAAQ,EAAEnD,sBAAsB;QAAEiD,KAAK,EAAE;MAAI,CAAE,EAC7EtD,iBAAiB,CAACC,sBAAsB,EAAE,CAAC,CAC9C;MAAAwD,OAAA,GA9CoC7C,eAAe,CAAC8C,OAAO,CAAC;QACrDC,eAAe,EAAE,IAAI;QACrBC,MAAM,EAAE;UACJR,OAAO,EAAEvC,eAAe;UACxBwC,UAAU,EAAGvB,qBAAsB;UACnCyB,IAAI,EAAE,CAACzD,UAAU;;OAExB,CAAC,EACFI,uBAAuB,EACvBV,aAAa,EACbC,gBAAgB,EAChBE,aAAa,EACbC,WAAW,EACXC,UAAU,EACVW,qBAAqB,EACrBO,WAAW,CAAC2C,OAAO,CAAC5C,WAAW,CAAC,EAChCE,mBAAmB,CAAC6C,UAAU,CAAC;QAC3BC,MAAM,EAAE,EAAE;QAAE;QACZC,OAAO,EAAE5D,WAAW,CAAC6D,UAAU,CAAE;OACpC,CAAC,EACF/C,aAAa,CAACyC,OAAO,CAAC,CAClB7B,qBAAqB,EACrBX,gBAAgB,EAChBC,cAAc,EACdC,WAAW,EACXC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,kBAAkB,EAClBC,WAAW,EACXC,kBAAkB,EAClBC,aAAa,CAChB,CAAC;IAAA;EAAA;;;2EAcGsB,SAAS;IAAAe,YAAA,GAjDdvE,YAAY;IAAA+D,OAAA,GAAAS,EAAA,CAAAtD,eAAA,EAUZV,uBAAuB,EACvBV,aAAa,EACbC,gBAAgB,EAChBE,aAAa,EACbC,WAAW,EACXC,UAAU,EACVW,qBAAqB,EAAA2D,EAAA,CAAAC,eAAA,EAAAC,EAAA,CAAArD,mBAAA,EAAAsD,EAAA,CAAAC,iBAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}