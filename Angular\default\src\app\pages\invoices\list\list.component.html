<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Invoice List" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
    <div class="col-xl-3 col-md-6">
        <!-- card -->
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <p class="text-uppercase fw-medium text-muted mb-0">Invoices Sent</p>
                    </div>
                    <div class="flex-shrink-0">
                        <h5 class="text-success fs-14 mb-0">
                            <i class="ri-arrow-right-up-line fs-13 align-middle"></i> +89.24 %
                        </h5>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">$<span [countUp]="559.25" class="counter-value" [options]="option"></span>k</h4>
                        <span class="badge bg-warning me-1">2,258</span> <span class="text-muted"> Invoices sent</span>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-light rounded fs-3">
                            <i-feather name="file-text" class="text-success icon-dual-success feather-icon-align">
                            </i-feather>
                        </span>
                    </div>
                </div>
            </div><!-- end card body -->
        </div><!-- end card -->
    </div><!-- end col -->

    <div class="col-xl-3 col-md-6">
        <!-- card -->
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <p class="text-uppercase fw-medium text-muted mb-0">Paid Invoices</p>
                    </div>
                    <div class="flex-shrink-0">
                        <h5 class="text-danger fs-14 mb-0">
                            <i class="ri-arrow-right-down-line fs-13 align-middle"></i> +8.09 %
                        </h5>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">$<span [countUp]="409.66" class="counter-value" [options]="option"></span>k</h4>
                        <span class="badge bg-warning me-1">1,958</span> <span class="text-muted"> Paid by
                            clients</span>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-light rounded fs-3">
                            <i-feather name="check-square" class="text-success icon-dual-success feather-icon-align">
                            </i-feather>
                        </span>
                    </div>
                </div>
            </div><!-- end card body -->
        </div><!-- end card -->
    </div><!-- end col -->

    <div class="col-xl-3 col-md-6">
        <!-- card -->
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <p class="text-uppercase fw-medium text-muted mb-0">Unpaid Invoices</p>
                    </div>
                    <div class="flex-shrink-0">
                        <h5 class="text-danger fs-14 mb-0">
                            <i class="ri-arrow-right-down-line fs-13 align-middle"></i> +9.01 %
                        </h5>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">$<span [countUp]="136.98" class="counter-value" [options]="option"></span>k</h4>
                        <span class="badge bg-warning me-1">338</span> <span class="text-muted"> Unpaid by
                            clients</span>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-light rounded fs-3">
                            <i-feather name="clock" class="text-success icon-dual-success feather-icon-align">
                            </i-feather>
                        </span>
                    </div>
                </div>
            </div><!-- end card body -->
        </div><!-- end card -->
    </div><!-- end col -->

    <div class="col-xl-3 col-md-6">
        <!-- card -->
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <p class="text-uppercase fw-medium text-muted mb-0">Cancelled Invoices</p>
                    </div>
                    <div class="flex-shrink-0">
                        <h5 class="text-success fs-14 mb-0">
                            <i class="ri-arrow-right-up-line fs-13 align-middle"></i> ***** %
                        </h5>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">$<span [countUp]="84.20" class="counter-value" [options]="option"></span>k</h4>
                        <span class="badge bg-warning me-1">502</span> <span class="text-muted"> Cancelled by
                            clients</span>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-light rounded fs-3">
                            <i-feather name="x-octagon" class="text-success icon-dual-success feather-icon-align">
                            </i-feather>
                        </span>
                    </div>
                </div>
            </div><!-- end card body -->
        </div><!-- end card -->
    </div><!-- end col -->
</div> <!-- end row-->

<div class="row">
    <div class="col-lg-12">
        <div class="card" id="invoiceList">
            <div class="card-header border-0">
                <div class="d-flex align-items-center">
                    <h5 class="card-title mb-0 flex-grow-1">Invoices</h5>
                    <div class="flex-shrink-0 d-flex gap-2 flex-wrap">
                        <button class="btn btn-primary" id="remove-actions" style="display: none" (click)="deleteMultiple(deleteModel)"><i class="ri-delete-bin-2-line"></i></button>
                        <a routerLink="/invoices/create" class="btn btn-danger"><i class="ri-add-line align-bottom me-1"></i> Create Invoice</a>
                    </div>
                </div>
            </div>
            <div class="card-body bg-light-subtle border border-dashed border-start-0 border-end-0">
                <div class="row g-3">
                    <div class="col-xxl-5 col-sm-12">
                        <div class="search-box">
                            <input type="text" name="searchTerm" class="form-control search bg-light border-light" placeholder="Search for customer, email, country, status or something..." [(ngModel)]="searchTerm" (ngModelChange)="performSearch()">
                            <i class="ri-search-line search-icon"></i>
                        </div>
                    </div>
                    <!--end col-->
                    <div class="col-xxl-3 col-sm-4">
                        <input class="form-control bg-light border-light" type="text" mwlFlatpickr [altInput]="true" [convertModelValue]="true" placeholder="Select date" id="isDate" [(ngModel)]="date" mode="range">
                    </div>
                    <!--end col-->
                    <div class="col-xxl-3 col-sm-4">
                        <div class="input-light">
                            <select class="form-control bg-light border-light" data-choices data-choices-search-false name="choices-single-default" id="idStatus" [(ngModel)]="status" (ngModelChange)="statusFilter()">
                                <option value="" selected>Select Status</option>
                                <option value="Unpaid">Unpaid</option>
                                <option value="Paid">Paid</option>
                                <option value="Cancel">Cancel</option>
                                <option value="Refund">Refund</option>
                            </select>
                        </div>
                    </div>
                    <!--end col-->

                    <div class="col-xxl-1 col-sm-4">
                        <button type="button" class="btn btn-primary w-100" (click)="SearchData();">
                            <i class="ri-equalizer-fill me-1 align-bottom"></i> Filters
                        </button>
                    </div>
                    <!--end col-->
                </div>
                <!--end row-->
            </div>
            <div class="card-body">
                <div>
                    <div class="table-responsive table-card">
                        <table class="table align-middle table-nowrap" id="invoiceTable">
                            <thead class="text-muted">
                                <tr>
                                    <th scope="col" style="width: 50px;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkAll" value="option" [(ngModel)]="masterSelected" (change)="checkUncheckAll($event)">
                                        </div>
                                    </th>
                                    <th class="sort text-uppercase">ID</th>
                                    <th class="sort text-uppercase" (click)="onSort('name')">
                                        Customer
                                    </th>
                                    <th class="sort text-uppercase" (click)="onSort('email')">
                                        Email</th>
                                    <th class="sort text-uppercase" (click)="onSort('country')">
                                        Country
                                    </th>
                                    <th class="sort text-uppercase" (click)="onSort('date')">Date
                                    </th>
                                    <th class="sort text-uppercase" (click)="onSort('amount')">
                                        Amount
                                    </th>
                                    <th class="sort text-uppercase" (click)="onSort('status')">Payment
                                        Status</th>
                                    <th class="text-uppercase">Action</th>
                                </tr>
                            </thead>
                            <tbody class="list form-check-all">
                                @for (data of invoices; track $index) {
                                <tr id="i_{{data._id}}">
                                    <th scope="row">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="checkAll" value="{{data._id}}" [(ngModel)]="data.state" (change)="onCheckboxChange($event)">
                                        </div>
                                    </th>
                                    <td>
                                        <ngb-highlight [result]="data.invoiceId" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td class="customer_name">
                                        @if(data.img){
                                        <div class="d-flex align-items-center">
                                            <img src="assets/images/users/{{data.img}}" class="avatar-xs rounded-circle me-2" alt="">
                                            {{data.name}}
                                        </div>
                                        }@else(){
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0 avatar-xs me-2">
                                                <div class="avatar-title bg-success-subtle text-success rounded-circle fs-13">
                                                    {{ data.name.charAt(0) }}
                                                </div>
                                            </div>
                                            {{data.name}}
                                        </div>
                                        }
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.email" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.country" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td class="date">{{data.date | date :'longDate'}} <small class="text-muted">{{data.time}}</small></td>
                                    <td>
                                        <ngb-highlight [result]="data.amount" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td class="status"><span class="badge text-uppercase" [ngClass]=" { 'bg-success-subtle text-success': data.status === 'Paid', 'bg-warning-subtle text-warning': data.status === 'Unpaid', 'bg-primary-subtle text-primary': data.status === 'Refund', 'bg-danger-subtle text-danger': data.status === 'Cancel'}">{{data.status}}</span>
                                    </td>
                                    <td>
                                        <div class="dropdown" ngbDropdown>
                                            <button class="btn btn-soft-secondary btn-sm dropdown arrow-none" type="button" data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle>
                                                <i class="ri-more-fill align-middle"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                                                <li><a class="dropdown-item" routerLink="/invoices/details"><i class="ri-eye-fill align-bottom me-2 text-muted"></i>
                                                        View</a></li>
                                                <li><a class="dropdown-item" routerLink="/invoices/create"><i class="ri-pencil-fill align-bottom me-2 text-muted"></i>
                                                        Edit</a></li>
                                                <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-download-2-line align-bottom me-2 text-muted"></i>
                                                        Download</a></li>
                                                <li class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item remove-item-btn" data-bs-toggle="modal" (click)="confirm(deleteModel,data._id)">
                                                        <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i>
                                                        Delete
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="row justify-content-md-end align-items-md-center mt-3">
                        <!-- Pagination -->
                        <div class="col col-sm-6">
                            <div class="text-sm-right float-sm-end listjs-pagination">
                                <ngb-pagination [collectionSize]="allinvoices?.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                                </ngb-pagination>
                            </div>
                        </div>
                        <!-- End Pagination -->
                    </div>
                </div>
            </div>
        </div>
        <div id="elmLoader">
            <div class="spinner-border text-primary avatar-sm" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    </div>
    <!--end col-->
</div>
<!--end row-->

<!-- removeItemModal -->
<ng-template #deleteModel let-modal>
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="btn-close" (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body">
            <div class="mt-2 text-center">
                <lord-icon src="https://cdn.lordicon.com/gsqxdxog.json" trigger="loop" colors="primary:#405189,secondary:#f06548" style="width:90px;height:90px"></lord-icon>
                <div class="mt-4 pt-2 fs-15 mx-4 mx-sm-5">
                    <h4>You are about to delete a contact ?</h4>
                    <p class="text-muted mx-4 mb-0">Deleting your contact will remove all of your information from our
                        database.</p>
                </div>
            </div>
            <div class="d-flex gap-2 justify-content-center mt-4 mb-2">
                <button class="btn btn-link link-success fw-medium text-decoration-none" data-bs-dismiss="modal" id="deleteRecord-close" (click)="modal.close('Close click')"><i class="ri-close-line me-1 align-middle"></i> Close</button>
                <button type="button" class="btn w-sm btn-danger " id="delete-product" (click)="deleteData(deleteId)" (click)="modal.close('Close click')">Yes, Delete It!</button>
            </div>
        </div>
    </div><!-- /.modal-content -->
</ng-template>