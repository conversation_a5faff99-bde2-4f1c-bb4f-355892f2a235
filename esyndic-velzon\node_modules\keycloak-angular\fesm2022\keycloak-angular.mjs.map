{"version": 3, "file": "keycloak-angular.mjs", "sources": ["../../../projects/keycloak-angular/src/lib/core/interfaces/keycloak-event.ts", "../../../projects/keycloak-angular/src/lib/core/services/keycloak-auth-guard.ts", "../../../projects/keycloak-angular/src/lib/core/services/keycloak.service.ts", "../../../projects/keycloak-angular/src/lib/core/interceptors/keycloak-bearer.interceptor.ts", "../../../projects/keycloak-angular/src/lib/core/core.module.ts", "../../../projects/keycloak-angular/src/lib/keycloak-angular.module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright <PERSON><PERSON><PERSON> and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n\n/**\n * Keycloak event types, as described at the keycloak-js documentation:\n * https://www.keycloak.org/docs/latest/securing_apps/index.html#callback-events\n */\nexport enum KeycloakEventType {\n  /**\n   * Called if there was an error during authentication.\n   */\n  OnAuthError,\n  /**\n   * Called if the user is logged out\n   * (will only be called if the session status iframe is enabled, or in Cordova mode).\n   */\n  OnAuthLogout,\n  /**\n   * Called if there was an error while trying to refresh the token.\n   */\n  OnAuthRefreshError,\n  /**\n   * Called when the token is refreshed.\n   */\n  OnAuthRefreshSuccess,\n  /**\n   * Called when a user is successfully authenticated.\n   */\n  OnAuthSuccess,\n  /**\n   * Called when the adapter is initialized.\n   */\n  OnReady,\n  /**\n   * Called when the access token is expired. If a refresh token is available the token\n   * can be refreshed with updateToken, or in cases where it is not (that is, with implicit flow)\n   * you can redirect to login screen to obtain a new access token.\n   */\n  OnTokenExpired,\n  /**\n   * Called when a AIA has been requested by the application.\n   */\n  OnActionUpdate\n}\n\n/**\n * Structure of an event triggered by Keycloak, contains it's type\n * and arguments (if any).\n */\nexport interface KeycloakEvent {\n  /**\n   * Event type as described at {@link KeycloakEventType}.\n   */\n  type: KeycloakEventType;\n  /**\n   * Arguments from the keycloak-js event function.\n   */\n  args?: unknown;\n}\n", "/**\n * @license\n * Copyright <PERSON><PERSON><PERSON> and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n\nimport {\n  CanActivate,\n  Router,\n  ActivatedRouteSnapshot,\n  RouterStateSnapshot,\n  UrlTree\n} from '@angular/router';\n\nimport { KeycloakService } from './keycloak.service';\n\n/**\n * A simple guard implementation out of the box. This class should be inherited and\n * implemented by the application. The only method that should be implemented is #isAccessAllowed.\n * The reason for this is that the authorization flow is usually not unique, so in this way you will\n * have more freedom to customize your authorization flow.\n */\nexport abstract class KeycloakAuthGuard implements CanActivate {\n  /**\n   * Indicates if the user is authenticated or not.\n   */\n  protected authenticated: boolean;\n  /**\n   * Roles of the logged user. It contains the clientId and realm user roles.\n   */\n  protected roles: string[];\n\n  constructor(\n    protected router: Router,\n    protected keycloakAngular: KeycloakService\n  ) {}\n\n  /**\n   * CanActivate checks if the user is logged in and get the full list of roles (REALM + CLIENT)\n   * of the logged user. This values are set to authenticated and roles params.\n   *\n   * @param route\n   * @param state\n   */\n  async canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Promise<boolean | UrlTree> {\n    try {\n      this.authenticated = await this.keycloakAngular.isLoggedIn();\n      this.roles = await this.keycloakAngular.getUserRoles(true);\n\n      return await this.isAccessAllowed(route, state);\n    } catch (error) {\n      throw new Error(\n        'An error happened during access validation. Details:' + error\n      );\n    }\n  }\n\n  /**\n   * Create your own customized authorization flow in this method. From here you already known\n   * if the user is authenticated (this.authenticated) and the user roles (this.roles).\n   *\n   * Return a UrlTree if the user should be redirected to another route.\n   *\n   * @param route\n   * @param state\n   */\n  abstract isAccessAllowed(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Promise<boolean | UrlTree>;\n}\n", "/**\n * @license\n * Copyright <PERSON><PERSON><PERSON> and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n\nimport { Injectable } from '@angular/core';\nimport { HttpHeaders, HttpRequest } from '@angular/common/http';\n\nimport { Subject, from } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport Keycloak from 'keycloak-js';\n\nimport {\n  ExcludedUrl,\n  ExcludedUrlRegex,\n  KeycloakOptions\n} from '../interfaces/keycloak-options';\nimport { KeycloakEvent, KeycloakEventType } from '../interfaces/keycloak-event';\n\n/**\n * Service to expose existent methods from the Keycloak JS adapter, adding new\n * functionalities to improve the use of keycloak in Angular v > 4.3 applications.\n *\n * This class should be injected in the application bootstrap, so the same instance will be used\n * along the web application.\n */\n@Injectable()\nexport class KeycloakService {\n  /**\n   * Keycloak-js instance.\n   */\n  private _instance: Keycloak.KeycloakInstance;\n  /**\n   * User profile as KeycloakProfile interface.\n   */\n  private _userProfile: Keycloak.KeycloakProfile;\n  /**\n   * Flag to indicate if the bearer will not be added to the authorization header.\n   */\n  private _enableBearerInterceptor: boolean;\n  /**\n   * When the implicit flow is choosen there must exist a silentRefresh, as there is\n   * no refresh token.\n   */\n  private _silentRefresh: boolean;\n  /**\n   * Indicates that the user profile should be loaded at the keycloak initialization,\n   * just after the login.\n   */\n  private _loadUserProfileAtStartUp: boolean;\n  /**\n   * The bearer prefix that will be appended to the Authorization Header.\n   */\n  private _bearerPrefix: string;\n  /**\n   * Value that will be used as the Authorization Http Header name.\n   */\n  private _authorizationHeaderName: string;\n  /**\n   * @deprecated\n   * The excluded urls patterns that must skip the KeycloakBearerInterceptor.\n   */\n  private _excludedUrls: ExcludedUrlRegex[];\n  /**\n   * Observer for the keycloak events\n   */\n  private _keycloakEvents$: Subject<KeycloakEvent> =\n    new Subject<KeycloakEvent>();\n  /**\n   * The amount of required time remaining before expiry of the token before the token will be refreshed.\n   */\n  private _updateMinValidity: number;\n  /**\n   * Returns true if the request should have the token added to the headers by the KeycloakBearerInterceptor.\n   */\n  shouldAddToken: (request: HttpRequest<unknown>) => boolean;\n  /**\n   * Returns true if the request being made should potentially update the token.\n   */\n  shouldUpdateToken: (request: HttpRequest<unknown>) => boolean;\n\n  /**\n   * Binds the keycloak-js events to the keycloakEvents Subject\n   * which is a good way to monitor for changes, if needed.\n   *\n   * The keycloakEvents returns the keycloak-js event type and any\n   * argument if the source function provides any.\n   */\n  private bindsKeycloakEvents(): void {\n    this._instance.onAuthError = (errorData) => {\n      this._keycloakEvents$.next({\n        args: errorData,\n        type: KeycloakEventType.OnAuthError\n      });\n    };\n\n    this._instance.onAuthLogout = () => {\n      this._keycloakEvents$.next({ type: KeycloakEventType.OnAuthLogout });\n    };\n\n    this._instance.onAuthRefreshSuccess = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnAuthRefreshSuccess\n      });\n    };\n\n    this._instance.onAuthRefreshError = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnAuthRefreshError\n      });\n    };\n\n    this._instance.onAuthSuccess = () => {\n      this._keycloakEvents$.next({ type: KeycloakEventType.OnAuthSuccess });\n    };\n\n    this._instance.onTokenExpired = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnTokenExpired\n      });\n    };\n\n    this._instance.onActionUpdate = (state) => {\n      this._keycloakEvents$.next({\n        args: state,\n        type: KeycloakEventType.OnActionUpdate\n      });\n    };\n\n    this._instance.onReady = (authenticated) => {\n      this._keycloakEvents$.next({\n        args: authenticated,\n        type: KeycloakEventType.OnReady\n      });\n    };\n  }\n\n  /**\n   * Loads all bearerExcludedUrl content in a uniform type: ExcludedUrl,\n   * so it becomes easier to handle.\n   *\n   * @param bearerExcludedUrls array of strings or ExcludedUrl that includes\n   * the url and HttpMethod.\n   */\n  private loadExcludedUrls(\n    bearerExcludedUrls: (string | ExcludedUrl)[]\n  ): ExcludedUrlRegex[] {\n    const excludedUrls: ExcludedUrlRegex[] = [];\n    for (const item of bearerExcludedUrls) {\n      let excludedUrl: ExcludedUrlRegex;\n      if (typeof item === 'string') {\n        excludedUrl = { urlPattern: new RegExp(item, 'i'), httpMethods: [] };\n      } else {\n        excludedUrl = {\n          urlPattern: new RegExp(item.url, 'i'),\n          httpMethods: item.httpMethods\n        };\n      }\n      excludedUrls.push(excludedUrl);\n    }\n    return excludedUrls;\n  }\n\n  /**\n   * Handles the class values initialization.\n   *\n   * @param options\n   */\n  private initServiceValues({\n    enableBearerInterceptor = true,\n    loadUserProfileAtStartUp = false,\n    bearerExcludedUrls = [],\n    authorizationHeaderName = 'Authorization',\n    bearerPrefix = 'Bearer',\n    initOptions,\n    updateMinValidity = 20,\n    shouldAddToken = () => true,\n    shouldUpdateToken = () => true\n  }: KeycloakOptions): void {\n    this._enableBearerInterceptor = enableBearerInterceptor;\n    this._loadUserProfileAtStartUp = loadUserProfileAtStartUp;\n    this._authorizationHeaderName = authorizationHeaderName;\n    this._bearerPrefix = bearerPrefix.trim().concat(' ');\n    this._excludedUrls = this.loadExcludedUrls(bearerExcludedUrls);\n    this._silentRefresh = initOptions ? initOptions.flow === 'implicit' : false;\n    this._updateMinValidity = updateMinValidity;\n    this.shouldAddToken = shouldAddToken;\n    this.shouldUpdateToken = shouldUpdateToken;\n  }\n\n  /**\n   * Keycloak initialization. It should be called to initialize the adapter.\n   * Options is an object with 2 main parameters: config and initOptions. The first one\n   * will be used to create the Keycloak instance. The second one are options to initialize the\n   * keycloak instance.\n   *\n   * @param options\n   * Config: may be a string representing the keycloak URI or an object with the\n   * following content:\n   * - url: Keycloak json URL\n   * - realm: realm name\n   * - clientId: client id\n   *\n   * initOptions:\n   * Options to initialize the Keycloak adapter, matches the options as provided by Keycloak itself.\n   *\n   * enableBearerInterceptor:\n   * Flag to indicate if the bearer will added to the authorization header.\n   *\n   * loadUserProfileInStartUp:\n   * Indicates that the user profile should be loaded at the keycloak initialization,\n   * just after the login.\n   *\n   * bearerExcludedUrls:\n   * String Array to exclude the urls that should not have the Authorization Header automatically\n   * added.\n   *\n   * authorizationHeaderName:\n   * This value will be used as the Authorization Http Header name.\n   *\n   * bearerPrefix:\n   * This value will be included in the Authorization Http Header param.\n   *\n   * tokenUpdateExcludedHeaders:\n   * Array of Http Header key/value maps that should not trigger the token to be updated.\n   *\n   * updateMinValidity:\n   * This value determines if the token will be refreshed based on its expiration time.\n   *\n   * @returns\n   * A Promise with a boolean indicating if the initialization was successful.\n   */\n  public async init(options: KeycloakOptions = {}) {\n    this.initServiceValues(options);\n    const { config, initOptions } = options;\n\n    this._instance = new Keycloak(config);\n    this.bindsKeycloakEvents();\n\n    const authenticated = await this._instance.init(initOptions);\n\n    if (authenticated && this._loadUserProfileAtStartUp) {\n      await this.loadUserProfile();\n    }\n\n    return authenticated;\n  }\n\n  /**\n   * Redirects to login form on (options is an optional object with redirectUri and/or\n   * prompt fields).\n   *\n   * @param options\n   * Object, where:\n   *  - redirectUri: Specifies the uri to redirect to after login.\n   *  - prompt:By default the login screen is displayed if the user is not logged-in to Keycloak.\n   * To only authenticate to the application if the user is already logged-in and not display the\n   * login page if the user is not logged-in, set this option to none. To always require\n   * re-authentication and ignore SSO, set this option to login .\n   *  - maxAge: Used just if user is already authenticated. Specifies maximum time since the\n   * authentication of user happened. If user is already authenticated for longer time than\n   * maxAge, the SSO is ignored and he will need to re-authenticate again.\n   *  - loginHint: Used to pre-fill the username/email field on the login form.\n   *  - action: If value is 'register' then user is redirected to registration page, otherwise to\n   * login page.\n   *  - locale: Specifies the desired locale for the UI.\n   * @returns\n   * A void Promise if the login is successful and after the user profile loading.\n   */\n  public async login(options: Keycloak.KeycloakLoginOptions = {}) {\n    await this._instance.login(options);\n\n    if (this._loadUserProfileAtStartUp) {\n      await this.loadUserProfile();\n    }\n  }\n\n  /**\n   * Redirects to logout.\n   *\n   * @param redirectUri\n   * Specifies the uri to redirect to after logout.\n   * @returns\n   * A void Promise if the logout was successful, cleaning also the userProfile.\n   */\n  public async logout(redirectUri?: string) {\n    const options = {\n      redirectUri\n    };\n\n    await this._instance.logout(options);\n    this._userProfile = undefined;\n  }\n\n  /**\n   * Redirects to registration form. Shortcut for login with option\n   * action = 'register'. Options are same as for the login method but 'action' is set to\n   * 'register'.\n   *\n   * @param options\n   * login options\n   * @returns\n   * A void Promise if the register flow was successful.\n   */\n  public async register(\n    options: Keycloak.KeycloakLoginOptions = { action: 'register' }\n  ) {\n    await this._instance.register(options);\n  }\n\n  /**\n   * Check if the user has access to the specified role. It will look for roles in\n   * realm and the given resource, but will not check if the user is logged in for better performance.\n   *\n   * @param role\n   * role name\n   * @param resource\n   * resource name. If not specified, `clientId` is used\n   * @returns\n   * A boolean meaning if the user has the specified Role.\n   */\n  isUserInRole(role: string, resource?: string): boolean {\n    let hasRole: boolean;\n    hasRole = this._instance.hasResourceRole(role, resource);\n    if (!hasRole) {\n      hasRole = this._instance.hasRealmRole(role);\n    }\n    return hasRole;\n  }\n\n  /**\n   * Return the roles of the logged user. The realmRoles parameter, with default value\n   * true, will return the resource roles and realm roles associated with the logged user. If set to false\n   * it will only return the resource roles. The resource parameter, if specified, will return only resource roles\n   * associated with the given resource.\n   *\n   * @param realmRoles\n   * Set to false to exclude realm roles (only client roles)\n   * @param resource\n   * resource name If not specified, returns roles from all resources\n   * @returns\n   * Array of Roles associated with the logged user.\n   */\n  getUserRoles(realmRoles: boolean = true, resource?: string): string[] {\n    let roles: string[] = [];\n\n    if (this._instance.resourceAccess) {\n      Object.keys(this._instance.resourceAccess).forEach((key) => {\n        if (resource && resource !== key) {\n          return;\n        }\n\n        const resourceAccess = this._instance.resourceAccess[key];\n        const clientRoles = resourceAccess['roles'] || [];\n        roles = roles.concat(clientRoles);\n      });\n    }\n\n    if (realmRoles && this._instance.realmAccess) {\n      const realmRoles = this._instance.realmAccess['roles'] || [];\n      roles.push(...realmRoles);\n    }\n\n    return roles;\n  }\n\n  /**\n   * Check if user is logged in.\n   *\n   * @returns\n   * A boolean that indicates if the user is logged in.\n   */\n  isLoggedIn(): boolean {\n    if (!this._instance) {\n      return false;\n    }\n\n    return this._instance.authenticated;\n  }\n\n  /**\n   * Returns true if the token has less than minValidity seconds left before\n   * it expires.\n   *\n   * @param minValidity\n   * Seconds left. (minValidity) is optional. Default value is 0.\n   * @returns\n   * Boolean indicating if the token is expired.\n   */\n  isTokenExpired(minValidity: number = 0): boolean {\n    return this._instance.isTokenExpired(minValidity);\n  }\n\n  /**\n   * If the token expires within _updateMinValidity seconds the token is refreshed. If the\n   * session status iframe is enabled, the session status is also checked.\n   * Returns a promise telling if the token was refreshed or not. If the session is not active\n   * anymore, the promise is rejected.\n   *\n   * @param minValidity\n   * Seconds left. (minValidity is optional, if not specified updateMinValidity - default 20 is used)\n   * @returns\n   * Promise with a boolean indicating if the token was succesfully updated.\n   */\n  public async updateToken(minValidity = this._updateMinValidity) {\n    // TODO: this is a workaround until the silent refresh (issue #43)\n    // is not implemented, avoiding the redirect loop.\n    if (this._silentRefresh) {\n      if (this.isTokenExpired()) {\n        throw new Error(\n          'Failed to refresh the token, or the session is expired'\n        );\n      }\n\n      return true;\n    }\n\n    if (!this._instance) {\n      throw new Error('Keycloak Angular library is not initialized.');\n    }\n\n    try {\n      return await this._instance.updateToken(minValidity);\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Loads the user profile.\n   * Returns promise to set functions to be invoked if the profile was loaded\n   * successfully, or if the profile could not be loaded.\n   *\n   * @param forceReload\n   * If true will force the loadUserProfile even if its already loaded.\n   * @returns\n   * A promise with the KeycloakProfile data loaded.\n   */\n  public async loadUserProfile(forceReload = false) {\n    if (this._userProfile && !forceReload) {\n      return this._userProfile;\n    }\n\n    if (!this._instance.authenticated) {\n      throw new Error(\n        'The user profile was not loaded as the user is not logged in.'\n      );\n    }\n\n    return (this._userProfile = await this._instance.loadUserProfile());\n  }\n\n  /**\n   * Returns the authenticated token.\n   */\n  public async getToken() {\n    return this._instance.token;\n  }\n\n  /**\n   * Returns the logged username.\n   *\n   * @returns\n   * The logged username.\n   */\n  public getUsername() {\n    if (!this._userProfile) {\n      throw new Error('User not logged in or user profile was not loaded.');\n    }\n\n    return this._userProfile.username;\n  }\n\n  /**\n   * Clear authentication state, including tokens. This can be useful if application\n   * has detected the session was expired, for example if updating token fails.\n   * Invoking this results in onAuthLogout callback listener being invoked.\n   */\n  clearToken(): void {\n    this._instance.clearToken();\n  }\n\n  /**\n   * Adds a valid token in header. The key & value format is:\n   * Authorization Bearer <token>.\n   * If the headers param is undefined it will create the Angular headers object.\n   *\n   * @param headers\n   * Updated header with Authorization and Keycloak token.\n   * @returns\n   * An observable with with the HTTP Authorization header and the current token.\n   */\n  public addTokenToHeader(headers: HttpHeaders = new HttpHeaders()) {\n    return from(this.getToken()).pipe(\n      map((token) =>\n        token\n          ? headers.set(\n              this._authorizationHeaderName,\n              this._bearerPrefix + token\n            )\n          : headers\n      )\n    );\n  }\n\n  /**\n   * Returns the original Keycloak instance, if you need any customization that\n   * this Angular service does not support yet. Use with caution.\n   *\n   * @returns\n   * The KeycloakInstance from keycloak-js.\n   */\n  getKeycloakInstance(): Keycloak.KeycloakInstance {\n    return this._instance;\n  }\n\n  /**\n   * @deprecated\n   * Returns the excluded URLs that should not be considered by\n   * the http interceptor which automatically adds the authorization header in the Http Request.\n   *\n   * @returns\n   * The excluded urls that must not be intercepted by the KeycloakBearerInterceptor.\n   */\n  get excludedUrls(): ExcludedUrlRegex[] {\n    return this._excludedUrls;\n  }\n\n  /**\n   * Flag to indicate if the bearer will be added to the authorization header.\n   *\n   * @returns\n   * Returns if the bearer interceptor was set to be disabled.\n   */\n  get enableBearerInterceptor(): boolean {\n    return this._enableBearerInterceptor;\n  }\n\n  /**\n   * Keycloak subject to monitor the events triggered by keycloak-js.\n   * The following events as available (as described at keycloak docs -\n   * https://www.keycloak.org/docs/latest/securing_apps/index.html#callback-events):\n   * - OnAuthError\n   * - OnAuthLogout\n   * - OnAuthRefreshError\n   * - OnAuthRefreshSuccess\n   * - OnAuthSuccess\n   * - OnReady\n   * - OnTokenExpire\n   * In each occurrence of any of these, this subject will return the event type,\n   * described at {@link KeycloakEventType} enum and the function args from the keycloak-js\n   * if provided any.\n   *\n   * @returns\n   * A subject with the {@link KeycloakEvent} which describes the event type and attaches the\n   * function args.\n   */\n  get keycloakEvents$(): Subject<KeycloakEvent> {\n    return this._keycloakEvents$;\n  }\n}\n", "/**\n * @license\n * Copyright <PERSON><PERSON><PERSON> and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n\nimport { Injectable } from '@angular/core';\nimport {\n  HttpInterceptor,\n  HttpRequest,\n  HttpHandler,\n  HttpEvent\n} from '@angular/common/http';\n\nimport { Observable, combineLatest, from, of } from 'rxjs';\nimport { mergeMap } from 'rxjs/operators';\n\nimport { KeycloakService } from '../services/keycloak.service';\nimport { ExcludedUrlRegex } from '../interfaces/keycloak-options';\n\n/**\n * This interceptor includes the bearer by default in all HttpClient requests.\n *\n * If you need to exclude some URLs from adding the bearer, please, take a look\n * at the {@link KeycloakOptions} bearerExcludedUrls property.\n */\n@Injectable()\nexport class KeycloakBearerInterceptor implements HttpInterceptor {\n  constructor(private keycloak: KeycloakService) {}\n\n  /**\n   * Calls to update the keycloak token if the request should update the token.\n   *\n   * @param req http request from @angular http module.\n   * @returns\n   * A promise boolean for the token update or noop result.\n   */\n  private async conditionallyUpdateToken(\n    req: HttpRequest<unknown>\n  ): Promise<boolean> {\n    if (this.keycloak.shouldUpdateToken(req)) {\n      return await this.keycloak.updateToken();\n    }\n\n    return true;\n  }\n\n  /**\n   * @deprecated\n   * Checks if the url is excluded from having the Bearer Authorization\n   * header added.\n   *\n   * @param req http request from @angular http module.\n   * @param excludedUrlRegex contains the url pattern and the http methods,\n   * excluded from adding the bearer at the Http Request.\n   */\n  private isUrlExcluded(\n    { method, url }: HttpRequest<unknown>,\n    { urlPattern, httpMethods }: ExcludedUrlRegex\n  ): boolean {\n    const httpTest =\n      httpMethods.length === 0 ||\n      httpMethods.join().indexOf(method.toUpperCase()) > -1;\n\n    const urlTest = urlPattern.test(url);\n\n    return httpTest && urlTest;\n  }\n\n  /**\n   * Intercept implementation that checks if the request url matches the excludedUrls.\n   * If not, adds the Authorization header to the request if the user is logged in.\n   *\n   * @param req\n   * @param next\n   */\n  public intercept(\n    req: HttpRequest<unknown>,\n    next: HttpHandler\n  ): Observable<HttpEvent<unknown>> {\n    const { enableBearerInterceptor, excludedUrls } = this.keycloak;\n    if (!enableBearerInterceptor) {\n      return next.handle(req);\n    }\n\n    const shallPass: boolean =\n      !this.keycloak.shouldAddToken(req) ||\n      excludedUrls.findIndex((item) => this.isUrlExcluded(req, item)) > -1;\n    if (shallPass) {\n      return next.handle(req);\n    }\n\n    return combineLatest([\n      from(this.conditionallyUpdateToken(req)),\n      of(this.keycloak.isLoggedIn())\n    ]).pipe(\n      mergeMap(([_, isLoggedIn]) =>\n        isLoggedIn\n          ? this.handleRequestWithTokenHeader(req, next)\n          : next.handle(req)\n      )\n    );\n  }\n\n  /**\n   * Adds the token of the current user to the Authorization header\n   *\n   * @param req\n   * @param next\n   */\n  private handleRequestWithTokenHeader(\n    req: HttpRequest<unknown>,\n    next: HttpHandler\n  ): Observable<HttpEvent<unknown>> {\n    return this.keycloak.addTokenToHeader(req.headers).pipe(\n      mergeMap((headersWithBearer) => {\n        const kcReq = req.clone({ headers: headersWithBearer });\n        return next.handle(kcReq);\n      })\n    );\n  }\n}\n", "/**\n * @license\n * Copyright <PERSON><PERSON><PERSON> and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\n\nimport { KeycloakService } from './services/keycloak.service';\nimport { KeycloakBearerInterceptor } from './interceptors/keycloak-bearer.interceptor';\n\n@NgModule({\n  imports: [CommonModule],\n  providers: [\n    KeycloakService,\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: KeycloakBearerInterceptor,\n      multi: true\n    }\n  ]\n})\nexport class CoreModule {}\n", "/**\n * @license\n * Copyright <PERSON><PERSON><PERSON> and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { CoreModule } from './core/core.module';\n\n@NgModule({\n  imports: [CoreModule]\n})\nexport class KeycloakAngularModule {}\n"], "names": ["i1.KeycloakService"], "mappings": ";;;;;;;;IAYY,kBAoCX;AApCD,CAAA,UAAY,iBAAiB,EAAA;AAI3B,IAAA,iBAAA,CAAA,iBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW,CAAA;AAKX,IAAA,iBAAA,CAAA,iBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAY,CAAA;AAIZ,IAAA,iBAAA,CAAA,iBAAA,CAAA,oBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,oBAAkB,CAAA;AAIlB,IAAA,iBAAA,CAAA,iBAAA,CAAA,sBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,sBAAoB,CAAA;AAIpB,IAAA,iBAAA,CAAA,iBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAa,CAAA;AAIb,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AAMP,IAAA,iBAAA,CAAA,iBAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAc,CAAA;AAId,IAAA,iBAAA,CAAA,iBAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAc,CAAA;AAChB,CAAC,EApCW,iBAAiB,KAAjB,iBAAiB,GAoC5B,EAAA,CAAA,CAAA;;MCxBqB,iBAAiB,CAAA;IAUrC,WACY,CAAA,MAAc,EACd,eAAgC,EAAA;QADhC,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QACd,IAAe,CAAA,eAAA,GAAf,eAAe,CAAiB;KACxC;AASJ,IAAA,MAAM,WAAW,CACf,KAA6B,EAC7B,KAA0B,EAAA;QAE1B,IAAI;YACF,IAAI,CAAC,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;AAC7D,YAAA,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAE3D,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACjD,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;AACd,YAAA,MAAM,IAAI,KAAK,CACb,sDAAsD,GAAG,KAAK,CAC/D,CAAC;AACH,SAAA;KACF;AAeF;;MC7CY,eAAe,CAAA;AAD5B,IAAA,WAAA,GAAA;AAwCU,QAAA,IAAA,CAAA,gBAAgB,GACtB,IAAI,OAAO,EAAiB,CAAC;AA6ehC,KAAA;IAxdS,mBAAmB,GAAA;QACzB,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,SAAS,KAAI;AACzC,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACzB,gBAAA,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,iBAAiB,CAAC,WAAW;AACpC,aAAA,CAAC,CAAC;AACL,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,MAAK;AACjC,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,iBAAiB,CAAC,YAAY,EAAE,CAAC,CAAC;AACvE,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,SAAS,CAAC,oBAAoB,GAAG,MAAK;AACzC,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBACzB,IAAI,EAAE,iBAAiB,CAAC,oBAAoB;AAC7C,aAAA,CAAC,CAAC;AACL,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,MAAK;AACvC,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBACzB,IAAI,EAAE,iBAAiB,CAAC,kBAAkB;AAC3C,aAAA,CAAC,CAAC;AACL,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,MAAK;AAClC,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,iBAAiB,CAAC,aAAa,EAAE,CAAC,CAAC;AACxE,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,MAAK;AACnC,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBACzB,IAAI,EAAE,iBAAiB,CAAC,cAAc;AACvC,aAAA,CAAC,CAAC;AACL,SAAC,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,CAAC,KAAK,KAAI;AACxC,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACzB,gBAAA,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,iBAAiB,CAAC,cAAc;AACvC,aAAA,CAAC,CAAC;AACL,SAAC,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,aAAa,KAAI;AACzC,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACzB,gBAAA,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,iBAAiB,CAAC,OAAO;AAChC,aAAA,CAAC,CAAC;AACL,SAAC,CAAC;KACH;AASO,IAAA,gBAAgB,CACtB,kBAA4C,EAAA;QAE5C,MAAM,YAAY,GAAuB,EAAE,CAAC;AAC5C,QAAA,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE;AACrC,YAAA,IAAI,WAA6B,CAAC;AAClC,YAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,gBAAA,WAAW,GAAG,EAAE,UAAU,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC;AACtE,aAAA;AAAM,iBAAA;AACL,gBAAA,WAAW,GAAG;oBACZ,UAAU,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;oBACrC,WAAW,EAAE,IAAI,CAAC,WAAW;iBAC9B,CAAC;AACH,aAAA;AACD,YAAA,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAChC,SAAA;AACD,QAAA,OAAO,YAAY,CAAC;KACrB;AAOO,IAAA,iBAAiB,CAAC,EACxB,uBAAuB,GAAG,IAAI,EAC9B,wBAAwB,GAAG,KAAK,EAChC,kBAAkB,GAAG,EAAE,EACvB,uBAAuB,GAAG,eAAe,EACzC,YAAY,GAAG,QAAQ,EACvB,WAAW,EACX,iBAAiB,GAAG,EAAE,EACtB,cAAc,GAAG,MAAM,IAAI,EAC3B,iBAAiB,GAAG,MAAM,IAAI,EACd,EAAA;AAChB,QAAA,IAAI,CAAC,wBAAwB,GAAG,uBAAuB,CAAC;AACxD,QAAA,IAAI,CAAC,yBAAyB,GAAG,wBAAwB,CAAC;AAC1D,QAAA,IAAI,CAAC,wBAAwB,GAAG,uBAAuB,CAAC;AACxD,QAAA,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACrD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,cAAc,GAAG,WAAW,GAAG,WAAW,CAAC,IAAI,KAAK,UAAU,GAAG,KAAK,CAAC;AAC5E,QAAA,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;AAC5C,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;AACrC,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC5C;AA4CM,IAAA,MAAM,IAAI,CAAC,OAAA,GAA2B,EAAE,EAAA;AAC7C,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAChC,QAAA,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAExC,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAE7D,QAAA,IAAI,aAAa,IAAI,IAAI,CAAC,yBAAyB,EAAE;AACnD,YAAA,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;AAC9B,SAAA;AAED,QAAA,OAAO,aAAa,CAAC;KACtB;AAuBM,IAAA,MAAM,KAAK,CAAC,OAAA,GAAyC,EAAE,EAAA;QAC5D,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAEpC,IAAI,IAAI,CAAC,yBAAyB,EAAE;AAClC,YAAA,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;AAC9B,SAAA;KACF;IAUM,MAAM,MAAM,CAAC,WAAoB,EAAA;AACtC,QAAA,MAAM,OAAO,GAAG;YACd,WAAW;SACZ,CAAC;QAEF,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;KAC/B;IAYM,MAAM,QAAQ,CACnB,OAAA,GAAyC,EAAE,MAAM,EAAE,UAAU,EAAE,EAAA;QAE/D,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;KACxC;IAaD,YAAY,CAAC,IAAY,EAAE,QAAiB,EAAA;AAC1C,QAAA,IAAI,OAAgB,CAAC;QACrB,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAC7C,SAAA;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AAeD,IAAA,YAAY,CAAC,UAAA,GAAsB,IAAI,EAAE,QAAiB,EAAA;QACxD,IAAI,KAAK,GAAa,EAAE,CAAC;AAEzB,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE;AACjC,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AACzD,gBAAA,IAAI,QAAQ,IAAI,QAAQ,KAAK,GAAG,EAAE;oBAChC,OAAO;AACR,iBAAA;gBAED,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;gBAC1D,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AAClD,gBAAA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AACpC,aAAC,CAAC,CAAC;AACJ,SAAA;AAED,QAAA,IAAI,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;AAC5C,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AAC7D,YAAA,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;AAC3B,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KACd;IAQD,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;KACrC;IAWD,cAAc,CAAC,cAAsB,CAAC,EAAA;QACpC,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;KACnD;AAaM,IAAA,MAAM,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAA;QAG5D,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;AACzB,gBAAA,MAAM,IAAI,KAAK,CACb,wDAAwD,CACzD,CAAC;AACH,aAAA;AAED,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;AACjE,SAAA;QAED,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACtD,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;AACd,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;KACF;AAYM,IAAA,MAAM,eAAe,CAAC,WAAW,GAAG,KAAK,EAAA;AAC9C,QAAA,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,WAAW,EAAE;YACrC,OAAO,IAAI,CAAC,YAAY,CAAC;AAC1B,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE;AACjC,YAAA,MAAM,IAAI,KAAK,CACb,+DAA+D,CAChE,CAAC;AACH,SAAA;AAED,QAAA,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,EAAE;KACrE;AAKM,IAAA,MAAM,QAAQ,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;KAC7B;IAQM,WAAW,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;AACvE,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;KACnC;IAOD,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;KAC7B;AAYM,IAAA,gBAAgB,CAAC,OAAA,GAAuB,IAAI,WAAW,EAAE,EAAA;AAC9D,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAC/B,GAAG,CAAC,CAAC,KAAK,KACR,KAAK;AACH,cAAE,OAAO,CAAC,GAAG,CACT,IAAI,CAAC,wBAAwB,EAC7B,IAAI,CAAC,aAAa,GAAG,KAAK,CAC3B;AACH,cAAE,OAAO,CACZ,CACF,CAAC;KACH;IASD,mBAAmB,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AAUD,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;AAQD,IAAA,IAAI,uBAAuB,GAAA;QACzB,OAAO,IAAI,CAAC,wBAAwB,CAAC;KACtC;AAqBD,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;8GAphBU,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;kHAAf,eAAe,EAAA,CAAA,CAAA,EAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;;;MCAE,yBAAyB,CAAA;AACpC,IAAA,WAAA,CAAoB,QAAyB,EAAA;QAAzB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;KAAI;IASzC,MAAM,wBAAwB,CACpC,GAAyB,EAAA;QAEzB,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE;AACxC,YAAA,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;AAC1C,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACb;IAWO,aAAa,CACnB,EAAE,MAAM,EAAE,GAAG,EAAwB,EACrC,EAAE,UAAU,EAAE,WAAW,EAAoB,EAAA;AAE7C,QAAA,MAAM,QAAQ,GACZ,WAAW,CAAC,MAAM,KAAK,CAAC;AACxB,YAAA,WAAW,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAExD,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAErC,OAAO,QAAQ,IAAI,OAAO,CAAC;KAC5B;IASM,SAAS,CACd,GAAyB,EACzB,IAAiB,EAAA;QAEjB,MAAM,EAAE,uBAAuB,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;QAChE,IAAI,CAAC,uBAAuB,EAAE;AAC5B,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACzB,SAAA;QAED,MAAM,SAAS,GACb,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC;YAClC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACvE,QAAA,IAAI,SAAS,EAAE;AACb,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACzB,SAAA;AAED,QAAA,OAAO,aAAa,CAAC;AACnB,YAAA,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;AACxC,YAAA,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;AAC/B,SAAA,CAAC,CAAC,IAAI,CACL,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KACvB,UAAU;cACN,IAAI,CAAC,4BAA4B,CAAC,GAAG,EAAE,IAAI,CAAC;cAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CACrB,CACF,CAAC;KACH;IAQO,4BAA4B,CAClC,GAAyB,EACzB,IAAiB,EAAA;AAEjB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CACrD,QAAQ,CAAC,CAAC,iBAAiB,KAAI;AAC7B,YAAA,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC,CAAC;AACxD,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAC3B,CAAC,CACH,CAAC;KACH;8GA7FU,yBAAyB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;kHAAzB,yBAAyB,EAAA,CAAA,CAAA,EAAA;;2FAAzB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBADrC,UAAU;;;MCFE,UAAU,CAAA;8GAAV,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAV,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,YAVX,YAAY,CAAA,EAAA,CAAA,CAAA,EAAA;AAUX,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,EATV,SAAA,EAAA;YACT,eAAe;AACf,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,QAAQ,EAAE,yBAAyB;AACnC,gBAAA,KAAK,EAAE,IAAI;AACZ,aAAA;AACF,SAAA,EAAA,OAAA,EAAA,CARS,YAAY,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAUX,UAAU,EAAA,UAAA,EAAA,CAAA;kBAXtB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,SAAS,EAAE;wBACT,eAAe;AACf,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,QAAQ,EAAE,yBAAyB;AACnC,4BAAA,KAAK,EAAE,IAAI;AACZ,yBAAA;AACF,qBAAA;AACF,iBAAA,CAAA;;;MCVY,qBAAqB,CAAA;8GAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAArB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,YAFtB,UAAU,CAAA,EAAA,CAAA,CAAA,EAAA;AAET,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,YAFtB,UAAU,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAET,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAHjC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,UAAU,CAAC;AACtB,iBAAA,CAAA;;;;;"}