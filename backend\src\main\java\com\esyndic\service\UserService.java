package com.esyndic.service;

import com.esyndic.entity.User;
import com.esyndic.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class UserService {

    private final UserRepository userRepository;

    @Autowired
    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    public List<User> getActiveUsers() {
        return userRepository.findByIsActiveTrue();
    }

    public Optional<User> getUserById(UUID id) {
        return userRepository.findById(id);
    }

    public Optional<User> getUserByKeycloakId(String keycloakId) {
        return userRepository.findByKeycloakId(keycloakId);
    }

    public Optional<User> getUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    public User createUser(User user) {
        validateUser(user);
        return userRepository.save(user);
    }

    public User updateUser(UUID id, User userDetails) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + id));

        user.setUsername(userDetails.getUsername());
        user.setEmail(userDetails.getEmail());
        user.setFirstName(userDetails.getFirstName());
        user.setLastName(userDetails.getLastName());
        user.setPhone(userDetails.getPhone());
        user.setIsActive(userDetails.getIsActive());

        validateUser(user);
        return userRepository.save(user);
    }

    public void deleteUser(UUID id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + id));
        user.setIsActive(false);
        userRepository.save(user);
    }

    public void activateUser(UUID id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + id));
        user.setIsActive(true);
        userRepository.save(user);
    }

    public List<User> searchUsersByName(String name) {
        return userRepository.findByNameContaining(name);
    }

    public List<User> searchUsersByEmail(String email) {
        return userRepository.findByEmailContaining(email);
    }

    public List<User> getUsersByBuildingId(UUID buildingId) {
        return userRepository.findUsersByBuildingId(buildingId);
    }

    public List<User> getOwnersByBuildingId(UUID buildingId) {
        return userRepository.findOwnersByBuildingId(buildingId);
    }

    public List<User> getResidentsByBuildingId(UUID buildingId) {
        return userRepository.findResidentsByBuildingId(buildingId);
    }

    public long getActiveUserCount() {
        return userRepository.countActiveUsers();
    }

    public User syncUserFromKeycloak(String keycloakId, String username, String email, 
                                   String firstName, String lastName) {
        Optional<User> existingUser = userRepository.findByKeycloakId(keycloakId);
        
        if (existingUser.isPresent()) {
            User user = existingUser.get();
            user.setUsername(username);
            user.setEmail(email);
            user.setFirstName(firstName);
            user.setLastName(lastName);
            return userRepository.save(user);
        } else {
            User newUser = new User(keycloakId, username, email, firstName, lastName);
            return userRepository.save(newUser);
        }
    }

    public boolean isUserOwner(String keycloakId, UUID userId) {
        Optional<User> currentUser = userRepository.findByKeycloakId(keycloakId);
        if (currentUser.isEmpty()) {
            return false;
        }

        Optional<User> targetUser = userRepository.findById(userId);
        if (targetUser.isEmpty()) {
            return false;
        }

        return currentUser.get().getId().equals(targetUser.get().getId());
    }

    private void validateUser(User user) {
        if (user.getUsername() != null && userRepository.existsByUsername(user.getUsername())) {
            User existingUser = userRepository.findByUsername(user.getUsername()).orElse(null);
            if (existingUser != null && !existingUser.getId().equals(user.getId())) {
                throw new RuntimeException("Username already exists: " + user.getUsername());
            }
        }

        if (user.getEmail() != null && userRepository.existsByEmail(user.getEmail())) {
            User existingUser = userRepository.findByEmail(user.getEmail()).orElse(null);
            if (existingUser != null && !existingUser.getId().equals(user.getId())) {
                throw new RuntimeException("Email already exists: " + user.getEmail());
            }
        }

        if (user.getKeycloakId() != null && userRepository.existsByKeycloakId(user.getKeycloakId())) {
            User existingUser = userRepository.findByKeycloakId(user.getKeycloakId()).orElse(null);
            if (existingUser != null && !existingUser.getId().equals(user.getId())) {
                throw new RuntimeException("Keycloak ID already exists: " + user.getKeycloakId());
            }
        }
    }
}
