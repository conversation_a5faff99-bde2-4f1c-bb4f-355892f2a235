import { Injectable, Inject } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { map, switchMap, catchError, exhaustMap, tap } from 'rxjs/operators';
import { from, of } from 'rxjs';
import { AuthenticationService } from '../../core/services/auth.service';
import { login, loginSuccess, loginFailure, logout, logoutSuccess, Register, RegisterSuccess, RegisterFailure} from './authentication.actions';
import { Router } from '@angular/router';
import { environment } from 'src/environments/environment';

@Injectable()
export class AuthenticationEffects {

  Register$ = createEffect(() =>
    this.actions$.pipe(
      ofType(Register),
      exhaustMap(({ email, first_name, password }) =>
        this.AuthenticationService.register(email, first_name, password).pipe(
          map((response) => {
            if (response.status === 'success') {
              this.router.navigate(['/auth/login']);
              return RegisterSuccess({ user: response.data });
            } else {
              return RegisterFailure({ error: response.data });
            }
          }),
          catchError((error) => of(RegisterFailure({ error: error.message || 'Registration failed' })))
        )
      )
    )
  );

  login$ = createEffect(() =>
  this.actions$.pipe(
    ofType(login),
    exhaustMap(({ email, password }) => {
      if (environment.defaultauth === "fakebackend") {
        return this.AuthenticationService.login(email, password).pipe(
          map((response) => {
            if (response.status === 'success') {
              sessionStorage.setItem('toast', 'true');
              sessionStorage.setItem('currentUser', JSON.stringify(response.data));
              // Type guard to check if response has token property
              if ('token' in response && response.token) {
                sessionStorage.setItem('token', response.token);
              }
              this.router.navigate(['/']);
              // Return the user data from the response
              return loginSuccess({ user: response.data });
            } else {
              // Handle error response
              return loginFailure({ error: response.data });
            }
          }),
          catchError((error) => of(loginFailure({ error: error.message || 'Login failed' })))
        );
      } else if (environment.defaultauth === "firebase") {
        return of(); // Return an observable, even if it's empty
      } else {
        return of(); // Return an observable, even if it's empty
      }
    })
  )
);

  logout$ = createEffect(() =>
    this.actions$.pipe(
      ofType(logout),
      tap(() => {
        // Perform any necessary cleanup or side effects before logging out
      }),
      exhaustMap(() => of(logoutSuccess()))
    )
  );

  constructor(
    @Inject(Actions) private actions$: Actions,
    private AuthenticationService: AuthenticationService,
    private router: Router) { }

}