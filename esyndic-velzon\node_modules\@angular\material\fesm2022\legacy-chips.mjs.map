{"version": 3, "file": "legacy-chips.mjs", "sources": ["../../../../../../src/material/legacy-chips/chip.ts", "../../../../../../src/material/legacy-chips/chip-default-options.ts", "../../../../../../src/material/legacy-chips/chip-list.ts", "../../../../../../src/material/legacy-chips/chip-input.ts", "../../../../../../src/material/legacy-chips/chips-module.ts", "../../../../../../src/material/legacy-chips/legacy-chips_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {FocusableOption} from '@angular/cdk/a11y';\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {BACKSPACE, DELETE, SPACE} from '@angular/cdk/keycodes';\nimport {Platform} from '@angular/cdk/platform';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  Attribute,\n  ChangeDetectorRef,\n  ContentChild,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnDestroy,\n  Optional,\n  Output,\n} from '@angular/core';\nimport {\n  CanColor,\n  CanDisable,\n  CanDisableRipple,\n  HasTabIndex,\n  MAT_RIPPLE_GLOBAL_OPTIONS,\n  mixinColor,\n  mixinDisableRipple,\n  mixinTabIndex,\n  RippleConfig,\n  R<PERSON>pleGlobalOptions,\n  <PERSON><PERSON><PERSON><PERSON>enderer,\n  RippleTarget,\n} from '@angular/material/core';\nimport {ANIMATION_MODULE_TYPE} from '@angular/platform-browser/animations';\nimport {Subject} from 'rxjs';\nimport {take} from 'rxjs/operators';\n\n/**\n * Represents an event fired on an individual `mat-chip`.\n * @deprecated Use `MatChipEvent` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport interface MatLegacyChipEvent {\n  /** The chip the event was fired on. */\n  chip: MatLegacyChip;\n}\n\n/**\n * Event object emitted by MatChip when selected or deselected.\n * @deprecated Use `MatChipSelectionChange` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport class MatLegacyChipSelectionChange {\n  constructor(\n    /** Reference to the chip that emitted the event. */\n    public source: MatLegacyChip,\n    /** Whether the chip that emitted the event is selected. */\n    public selected: boolean,\n    /** Whether the selection change was a result of a user interaction. */\n    public isUserInput = false,\n  ) {}\n}\n\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n * @deprecated Use `MAT_CHIP_REMOVE` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport const MAT_LEGACY_CHIP_REMOVE = new InjectionToken<MatLegacyChipRemove>('MatChipRemove');\n\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n * @deprecated Use `MAT_CHIP_AVATAR` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport const MAT_LEGACY_CHIP_AVATAR = new InjectionToken<MatLegacyChipAvatar>('MatChipAvatar');\n\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n * @deprecated Use `MAT_CHIP_TRAILING_ICON` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport const MAT_LEGACY_CHIP_TRAILING_ICON = new InjectionToken<MatLegacyChipTrailingIcon>(\n  'MatChipTrailingIcon',\n);\n\n// Boilerplate for applying mixins to MatChip.\n/** @docs-private */\nabstract class MatChipBase {\n  abstract disabled: boolean;\n  constructor(public _elementRef: ElementRef) {}\n}\n\nconst _MatChipMixinBase = mixinTabIndex(mixinColor(mixinDisableRipple(MatChipBase), 'primary'), -1);\n\n/**\n * Dummy directive to add CSS class to chip avatar.\n * @docs-private\n * @deprecated Use `MatChipAvatar` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: 'mat-chip-avatar, [matChipAvatar]',\n  host: {'class': 'mat-chip-avatar'},\n  providers: [{provide: MAT_LEGACY_CHIP_AVATAR, useExisting: MatLegacyChipAvatar}],\n})\nexport class MatLegacyChipAvatar {}\n\n/**\n * Dummy directive to add CSS class to chip trailing icon.\n * @docs-private\n * @deprecated Use `MatChipTrailingIcon` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: 'mat-chip-trailing-icon, [matChipTrailingIcon]',\n  host: {'class': 'mat-chip-trailing-icon'},\n  providers: [{provide: MAT_LEGACY_CHIP_TRAILING_ICON, useExisting: MatLegacyChipTrailingIcon}],\n})\nexport class MatLegacyChipTrailingIcon {}\n\n/**\n * Material Design styled chip directive. Used inside the MatChipList component.\n * @deprecated Use `MatChip` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: `mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]`,\n  inputs: ['color', 'disableRipple', 'tabIndex'],\n  exportAs: 'matChip',\n  host: {\n    'class': 'mat-chip mat-focus-indicator',\n    '[attr.tabindex]': 'disabled ? null : tabIndex',\n    '[attr.role]': 'role',\n    '[class.mat-chip-selected]': 'selected',\n    '[class.mat-chip-with-avatar]': 'avatar',\n    '[class.mat-chip-with-trailing-icon]': 'trailingIcon || removeIcon',\n    '[class.mat-chip-disabled]': 'disabled',\n    '[class._mat-animation-noopable]': '_animationsDisabled',\n    '[attr.disabled]': 'disabled || null',\n    '[attr.aria-disabled]': 'disabled.toString()',\n    '[attr.aria-selected]': 'ariaSelected',\n    '(click)': '_handleClick($event)',\n    '(keydown)': '_handleKeydown($event)',\n    '(focus)': 'focus()',\n    '(blur)': '_blur()',\n  },\n})\nexport class MatLegacyChip\n  extends _MatChipMixinBase\n  implements\n    FocusableOption,\n    OnDestroy,\n    CanColor,\n    CanDisableRipple,\n    RippleTarget,\n    HasTabIndex,\n    CanDisable\n{\n  /** Reference to the RippleRenderer for the chip. */\n  private _chipRipple: RippleRenderer;\n\n  /**\n   * Reference to the element that acts as the chip's ripple target. This element is\n   * dynamically added as a child node of the chip. The chip itself cannot be used as the\n   * ripple target because it must be the host of the focus indicator.\n   */\n  private _chipRippleTarget: HTMLElement;\n\n  /**\n   * Ripple configuration for ripples that are launched on pointer down. The ripple config\n   * is set to the global ripple options since we don't have any configurable options for\n   * the chip ripples.\n   * @docs-private\n   */\n  rippleConfig: RippleConfig & RippleGlobalOptions;\n\n  /**\n   * Whether ripples are disabled on interaction\n   * @docs-private\n   */\n  get rippleDisabled(): boolean {\n    return (\n      this.disabled ||\n      this.disableRipple ||\n      this._animationsDisabled ||\n      !!this.rippleConfig.disabled\n    );\n  }\n\n  /** Whether the chip has focus. */\n  _hasFocus: boolean = false;\n\n  /** Whether animations for the chip are enabled. */\n  _animationsDisabled: boolean;\n\n  /** Whether the chip list is selectable */\n  chipListSelectable: boolean = true;\n\n  /** Whether the chip list is in multi-selection mode. */\n  _chipListMultiple: boolean = false;\n\n  /** Whether the chip list as a whole is disabled. */\n  _chipListDisabled: boolean = false;\n\n  /** The chip avatar */\n  @ContentChild(MAT_LEGACY_CHIP_AVATAR) avatar: MatLegacyChipAvatar;\n\n  /** The chip's trailing icon. */\n  @ContentChild(MAT_LEGACY_CHIP_TRAILING_ICON) trailingIcon: MatLegacyChipTrailingIcon;\n\n  /** The chip's remove toggler. */\n  @ContentChild(MAT_LEGACY_CHIP_REMOVE) removeIcon: MatLegacyChipRemove;\n\n  /** ARIA role that should be applied to the chip. */\n  @Input() role: string = 'option';\n\n  /** Whether the chip is selected. */\n  @Input()\n  get selected(): boolean {\n    return this._selected;\n  }\n  set selected(value: BooleanInput) {\n    const coercedValue = coerceBooleanProperty(value);\n\n    if (coercedValue !== this._selected) {\n      this._selected = coercedValue;\n      this._dispatchSelectionChange();\n    }\n  }\n  protected _selected: boolean = false;\n\n  /** The value of the chip. Defaults to the content inside `<mat-chip>` tags. */\n  @Input()\n  get value(): any {\n    return this._value !== undefined ? this._value : this._elementRef.nativeElement.textContent;\n  }\n  set value(value: any) {\n    this._value = value;\n  }\n  protected _value: any;\n\n  /**\n   * Whether or not the chip is selectable. When a chip is not selectable,\n   * changes to its selected state are always ignored. By default a chip is\n   * selectable, and it becomes non-selectable if its parent chip list is\n   * not selectable.\n   */\n  @Input()\n  get selectable(): boolean {\n    return this._selectable && this.chipListSelectable;\n  }\n  set selectable(value: BooleanInput) {\n    this._selectable = coerceBooleanProperty(value);\n  }\n  protected _selectable: boolean = true;\n\n  /** Whether the chip is disabled. */\n  @Input()\n  get disabled(): boolean {\n    return this._chipListDisabled || this._disabled;\n  }\n  set disabled(value: BooleanInput) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n  protected _disabled: boolean = false;\n\n  /**\n   * Determines whether or not the chip displays the remove styling and emits (removed) events.\n   */\n  @Input()\n  get removable(): boolean {\n    return this._removable;\n  }\n  set removable(value: BooleanInput) {\n    this._removable = coerceBooleanProperty(value);\n  }\n  protected _removable: boolean = true;\n\n  /** Emits when the chip is focused. */\n  readonly _onFocus = new Subject<MatLegacyChipEvent>();\n\n  /** Emits when the chip is blurred. */\n  readonly _onBlur = new Subject<MatLegacyChipEvent>();\n\n  /** Emitted when the chip is selected or deselected. */\n  @Output() readonly selectionChange: EventEmitter<MatLegacyChipSelectionChange> =\n    new EventEmitter<MatLegacyChipSelectionChange>();\n\n  /** Emitted when the chip is destroyed. */\n  @Output() readonly destroyed: EventEmitter<MatLegacyChipEvent> =\n    new EventEmitter<MatLegacyChipEvent>();\n\n  /** Emitted when a chip is to be removed. */\n  @Output() readonly removed: EventEmitter<MatLegacyChipEvent> =\n    new EventEmitter<MatLegacyChipEvent>();\n\n  /** The ARIA selected applied to the chip. */\n  get ariaSelected(): string | null {\n    // Remove the `aria-selected` when the chip is deselected in single-selection mode, because\n    // it adds noise to NVDA users where \"not selected\" will be read out for each chip.\n    return this.selectable && (this._chipListMultiple || this.selected)\n      ? this.selected.toString()\n      : null;\n  }\n\n  constructor(\n    elementRef: ElementRef<HTMLElement>,\n    private _ngZone: NgZone,\n    platform: Platform,\n    @Optional()\n    @Inject(MAT_RIPPLE_GLOBAL_OPTIONS)\n    globalRippleOptions: RippleGlobalOptions | null,\n    private _changeDetectorRef: ChangeDetectorRef,\n    @Inject(DOCUMENT) _document: any,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n    @Attribute('tabindex') tabIndex?: string,\n  ) {\n    super(elementRef);\n\n    this._addHostClassName();\n\n    // Dynamically create the ripple target, append it within the chip, and use it as the\n    // chip's ripple target. Adding the class '.mat-chip-ripple' ensures that it will have\n    // the proper styles.\n    this._chipRippleTarget = _document.createElement('div');\n    this._chipRippleTarget.classList.add('mat-chip-ripple');\n    this._elementRef.nativeElement.appendChild(this._chipRippleTarget);\n    this._chipRipple = new RippleRenderer(this, _ngZone, this._chipRippleTarget, platform);\n    this._chipRipple.setupTriggerEvents(elementRef);\n\n    this.rippleConfig = globalRippleOptions || {};\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n    this.tabIndex = tabIndex != null ? parseInt(tabIndex) || -1 : -1;\n  }\n\n  _addHostClassName() {\n    const basicChipAttrName = 'mat-basic-chip';\n    const element = this._elementRef.nativeElement as HTMLElement;\n\n    if (\n      element.hasAttribute(basicChipAttrName) ||\n      element.tagName.toLowerCase() === basicChipAttrName\n    ) {\n      element.classList.add(basicChipAttrName);\n      return;\n    } else {\n      element.classList.add('mat-standard-chip');\n    }\n  }\n\n  ngOnDestroy() {\n    this.destroyed.emit({chip: this});\n    this._chipRipple._removeTriggerEvents();\n  }\n\n  /** Selects the chip. */\n  select(): void {\n    if (!this._selected) {\n      this._selected = true;\n      this._dispatchSelectionChange();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /** Deselects the chip. */\n  deselect(): void {\n    if (this._selected) {\n      this._selected = false;\n      this._dispatchSelectionChange();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /** Select this chip and emit selected event */\n  selectViaInteraction(): void {\n    if (!this._selected) {\n      this._selected = true;\n      this._dispatchSelectionChange(true);\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /** Toggles the current selected state of this chip. */\n  toggleSelected(isUserInput: boolean = false): boolean {\n    this._selected = !this.selected;\n    this._dispatchSelectionChange(isUserInput);\n    this._changeDetectorRef.markForCheck();\n    return this.selected;\n  }\n\n  /** Allows for programmatic focusing of the chip. */\n  focus(): void {\n    if (!this._hasFocus) {\n      this._elementRef.nativeElement.focus();\n      this._onFocus.next({chip: this});\n    }\n    this._hasFocus = true;\n  }\n\n  /**\n   * Allows for programmatic removal of the chip. Called by the MatChipList when the DELETE or\n   * BACKSPACE keys are pressed.\n   *\n   * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n   */\n  remove(): void {\n    if (this.removable) {\n      this.removed.emit({chip: this});\n    }\n  }\n\n  /** Handles click events on the chip. */\n  _handleClick(event: Event) {\n    if (this.disabled) {\n      event.preventDefault();\n    }\n  }\n\n  /** Handle custom key presses. */\n  _handleKeydown(event: KeyboardEvent): void {\n    if (this.disabled) {\n      return;\n    }\n\n    switch (event.keyCode) {\n      case DELETE:\n      case BACKSPACE:\n        // If we are removable, remove the focused chip\n        this.remove();\n        // Always prevent so page navigation does not occur\n        event.preventDefault();\n        break;\n      case SPACE:\n        // If we are selectable, toggle the focused chip\n        if (this.selectable) {\n          this.toggleSelected(true);\n        }\n\n        // Always prevent space from scrolling the page since the list has focus\n        event.preventDefault();\n        break;\n    }\n  }\n\n  _blur(): void {\n    // When animations are enabled, Angular may end up removing the chip from the DOM a little\n    // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n    // that moves focus not the next item. To work around the issue, we defer marking the chip\n    // as not focused until the next time the zone stabilizes.\n    this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      this._ngZone.run(() => {\n        this._hasFocus = false;\n        this._onBlur.next({chip: this});\n      });\n    });\n  }\n\n  private _dispatchSelectionChange(isUserInput = false) {\n    this.selectionChange.emit({\n      source: this,\n      isUserInput,\n      selected: this._selected,\n    });\n  }\n}\n\n/**\n * Applies proper (click) support and adds styling for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n *     `<mat-chip>\n *       <mat-icon matChipRemove>cancel</mat-icon>\n *     </mat-chip>`\n *\n * You *may* use a custom icon, but you may need to override the `mat-chip-remove` positioning\n * styles to properly center the icon within the chip.\n *\n * @deprecated Use `MatChipRemove` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: '[matChipRemove]',\n  host: {\n    'class': 'mat-chip-remove mat-chip-trailing-icon',\n    '(click)': '_handleClick($event)',\n  },\n  providers: [{provide: MAT_LEGACY_CHIP_REMOVE, useExisting: MatLegacyChipRemove}],\n})\nexport class MatLegacyChipRemove {\n  constructor(protected _parentChip: MatLegacyChip, elementRef: ElementRef<HTMLElement>) {\n    if (elementRef.nativeElement.nodeName === 'BUTTON') {\n      elementRef.nativeElement.setAttribute('type', 'button');\n    }\n  }\n\n  /** Calls the parent chip's public `remove()` method if applicable. */\n  _handleClick(event: Event): void {\n    const parentChip = this._parentChip;\n\n    if (parentChip.removable && !parentChip.disabled) {\n      parentChip.remove();\n    }\n\n    // We need to stop event propagation because otherwise the event will bubble up to the\n    // form field and cause the `onContainerClick` method to be invoked. This method would then\n    // reset the focused chip that has been focused after chip removal. Usually the parent\n    // the parent click listener of the `MatChip` would prevent propagation, but it can happen\n    // that the chip is being removed before the event bubbles up.\n    event.stopPropagation();\n    event.preventDefault();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n/**\n * Default options, for the chips module, that can be overridden.\n * @deprecated Use `MatChipsDefaultOptions` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport interface MatLegacyChipsDefaultOptions {\n  /** The list of key codes that will trigger a chipEnd event. */\n  separatorKeyCodes: readonly number[] | ReadonlySet<number>;\n}\n\n/**\n * Injection token to be used to override the default options for the chips module.\n * @deprecated Use `MAT_CHIPS_DEFAULT_OPTIONS` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport const MAT_LEGACY_CHIPS_DEFAULT_OPTIONS = new InjectionToken<MatLegacyChipsDefaultOptions>(\n  'mat-chips-default-options',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {FocusKeyManager} from '@angular/cdk/a11y';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {SelectionModel} from '@angular/cdk/collections';\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  DoCheck,\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  OnInit,\n  Optional,\n  Output,\n  QueryList,\n  Self,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {\n  ControlValueAccessor,\n  FormGroupDirective,\n  NgControl,\n  NgForm,\n  Validators,\n} from '@angular/forms';\nimport {CanUpdateErrorState, ErrorStateMatcher, mixinErrorState} from '@angular/material/core';\nimport {MatLegacyFormFieldControl} from '@angular/material/legacy-form-field';\nimport {merge, Observable, Subject, Subscription} from 'rxjs';\nimport {startWith, takeUntil} from 'rxjs/operators';\nimport {MatLegacyChip, MatLegacyChipEvent, MatLegacyChipSelectionChange} from './chip';\nimport {MatLegacyChipTextControl} from './chip-text-control';\n\n// Boilerplate for applying mixins to MatChipList.\n/** @docs-private */\nconst _MatChipListBase = mixinErrorState(\n  class {\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    readonly stateChanges = new Subject<void>();\n\n    constructor(\n      public _defaultErrorStateMatcher: ErrorStateMatcher,\n      public _parentForm: NgForm,\n      public _parentFormGroup: FormGroupDirective,\n      /**\n       * Form control bound to the component.\n       * Implemented as part of `MatFormFieldControl`.\n       * @docs-private\n       */\n      public ngControl: NgControl,\n    ) {}\n  },\n);\n\n// Increasing integer for generating unique ids for chip-list components.\nlet nextUniqueId = 0;\n\n/**\n * Change event object that is emitted when the chip list value has changed.\n * @deprecated Use `MatChipListChange` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport class MatLegacyChipListChange {\n  constructor(\n    /** Chip list that emitted the event. */\n    public source: MatLegacyChipList,\n    /** Value of the chip list when the event was emitted. */\n    public value: any,\n  ) {}\n}\n\n/**\n * A material design chips component (named ChipList for its similarity to the List component).\n * @deprecated Use `MatChipList` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Component({\n  selector: 'mat-chip-list',\n  template: `<div class=\"mat-chip-list-wrapper\"><ng-content></ng-content></div>`,\n  exportAs: 'matChipList',\n  host: {\n    '[attr.tabindex]': 'disabled ? null : _tabIndex',\n    '[attr.aria-required]': 'role ? required : null',\n    '[attr.aria-disabled]': 'disabled.toString()',\n    '[attr.aria-invalid]': 'errorState',\n    '[attr.aria-multiselectable]': 'multiple',\n    '[attr.role]': 'role',\n    '[class.mat-chip-list-disabled]': 'disabled',\n    '[class.mat-chip-list-invalid]': 'errorState',\n    '[class.mat-chip-list-required]': 'required',\n    '[attr.aria-orientation]': 'ariaOrientation',\n    'class': 'mat-chip-list',\n    '(focus)': 'focus()',\n    '(blur)': '_blur()',\n    '(keydown)': '_keydown($event)',\n    '[id]': '_uid',\n    'ngSkipHydration': '',\n  },\n  providers: [{provide: MatLegacyFormFieldControl, useExisting: MatLegacyChipList}],\n  styleUrls: ['chips.css'],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatLegacyChipList\n  extends _MatChipListBase\n  implements\n    MatLegacyFormFieldControl<any>,\n    ControlValueAccessor,\n    AfterContentInit,\n    DoCheck,\n    OnInit,\n    OnDestroy,\n    CanUpdateErrorState\n{\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  readonly controlType: string = 'mat-chip-list';\n\n  /**\n   * When a chip is destroyed, we store the index of the destroyed chip until the chips\n   * query list notifies about the update. This is necessary because we cannot determine an\n   * appropriate chip that should receive focus until the array of chips updated completely.\n   */\n  private _lastDestroyedChipIndex: number | null = null;\n\n  /** Subject that emits when the component has been destroyed. */\n  private readonly _destroyed = new Subject<void>();\n\n  /** Subscription to focus changes in the chips. */\n  private _chipFocusSubscription: Subscription | null;\n\n  /** Subscription to blur changes in the chips. */\n  private _chipBlurSubscription: Subscription | null;\n\n  /** Subscription to selection changes in chips. */\n  private _chipSelectionSubscription: Subscription | null;\n\n  /** Subscription to remove changes in chips. */\n  private _chipRemoveSubscription: Subscription | null;\n\n  /** The chip input to add more chips */\n  protected _chipInput: MatLegacyChipTextControl;\n\n  /** Uid of the chip list */\n  _uid: string = `mat-chip-list-${nextUniqueId++}`;\n\n  /** Tab index for the chip list. */\n  _tabIndex = 0;\n\n  /**\n   * User defined tab index.\n   * When it is not null, use user defined tab index. Otherwise use _tabIndex\n   */\n  _userTabIndex: number | null = null;\n\n  /** The FocusKeyManager which handles focus. */\n  _keyManager: FocusKeyManager<MatLegacyChip>;\n\n  /** Function when touched */\n  _onTouched = () => {};\n\n  /** Function when changed */\n  _onChange: (value: any) => void = () => {};\n\n  _selectionModel: SelectionModel<MatLegacyChip>;\n\n  /** The array of selected chips inside chip list. */\n  get selected(): MatLegacyChip[] | MatLegacyChip {\n    return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n  }\n\n  /** The ARIA role applied to the chip list. */\n  @Input()\n  get role(): string | null {\n    if (this._explicitRole) {\n      return this._explicitRole;\n    }\n\n    return this.empty ? null : 'listbox';\n  }\n  set role(role: string | null) {\n    this._explicitRole = role;\n  }\n  private _explicitRole?: string | null;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input('aria-describedby') userAriaDescribedBy: string;\n\n  /** An object used to control when error messages are shown. */\n  @Input() override errorStateMatcher: ErrorStateMatcher;\n\n  /** Whether the user should be allowed to select multiple chips. */\n  @Input()\n  get multiple(): boolean {\n    return this._multiple;\n  }\n  set multiple(value: BooleanInput) {\n    this._multiple = coerceBooleanProperty(value);\n    this._syncChipsState();\n  }\n  private _multiple: boolean = false;\n\n  /**\n   * A function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n  @Input()\n  get compareWith(): (o1: any, o2: any) => boolean {\n    return this._compareWith;\n  }\n  set compareWith(fn: (o1: any, o2: any) => boolean) {\n    this._compareWith = fn;\n    if (this._selectionModel) {\n      // A different comparator means the selection could change.\n      this._initializeSelection();\n    }\n  }\n  private _compareWith = (o1: any, o2: any) => o1 === o2;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get value(): any {\n    return this._value;\n  }\n  set value(value: any) {\n    this.writeValue(value);\n    this._value = value;\n  }\n  protected _value: any;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get id(): string {\n    return this._chipInput ? this._chipInput.id : this._uid;\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get required(): boolean {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value: BooleanInput) {\n    this._required = coerceBooleanProperty(value);\n    this.stateChanges.next();\n  }\n  protected _required: boolean | undefined;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get placeholder(): string {\n    return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n  }\n  set placeholder(value: string) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  protected _placeholder: string;\n\n  /** Whether any chips or the matChipInput inside of this chip-list has focus. */\n  get focused(): boolean {\n    return (this._chipInput && this._chipInput.focused) || this._hasFocusedChip();\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get empty(): boolean {\n    return (!this._chipInput || this._chipInput.empty) && (!this.chips || this.chips.length === 0);\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat(): boolean {\n    return !this.empty || this.focused;\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get disabled(): boolean {\n    return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n  }\n  set disabled(value: BooleanInput) {\n    this._disabled = coerceBooleanProperty(value);\n    this._syncChipsState();\n  }\n  protected _disabled: boolean = false;\n\n  /** Orientation of the chip list. */\n  @Input('aria-orientation') ariaOrientation: 'horizontal' | 'vertical' = 'horizontal';\n\n  /**\n   * Whether or not this chip list is selectable. When a chip list is not selectable,\n   * the selected states for all the chips inside the chip list are always ignored.\n   */\n  @Input()\n  get selectable(): boolean {\n    return this._selectable;\n  }\n  set selectable(value: BooleanInput) {\n    this._selectable = coerceBooleanProperty(value);\n    this._syncChipsState();\n  }\n  protected _selectable: boolean = true;\n\n  @Input()\n  set tabIndex(value: number) {\n    this._userTabIndex = value;\n    this._tabIndex = value;\n  }\n\n  /** Combined stream of all of the child chips' selection change events. */\n  get chipSelectionChanges(): Observable<MatLegacyChipSelectionChange> {\n    return merge(...this.chips.map(chip => chip.selectionChange));\n  }\n\n  /** Combined stream of all of the child chips' focus change events. */\n  get chipFocusChanges(): Observable<MatLegacyChipEvent> {\n    return merge(...this.chips.map(chip => chip._onFocus));\n  }\n\n  /** Combined stream of all of the child chips' blur change events. */\n  get chipBlurChanges(): Observable<MatLegacyChipEvent> {\n    return merge(...this.chips.map(chip => chip._onBlur));\n  }\n\n  /** Combined stream of all of the child chips' remove change events. */\n  get chipRemoveChanges(): Observable<MatLegacyChipEvent> {\n    return merge(...this.chips.map(chip => chip.destroyed));\n  }\n\n  /** Event emitted when the selected chip list value has been changed by the user. */\n  @Output() readonly change = new EventEmitter<MatLegacyChipListChange>();\n\n  /**\n   * Event that emits whenever the raw value of the chip-list changes. This is here primarily\n   * to facilitate the two-way binding for the `value` input.\n   * @docs-private\n   */\n  @Output() readonly valueChange = new EventEmitter<any>();\n\n  /** The chips contained within this chip list. */\n  @ContentChildren(MatLegacyChip, {\n    // We need to use `descendants: true`, because Ivy will no longer match\n    // indirect descendants if it's left as false.\n    descendants: true,\n  })\n  chips: QueryList<MatLegacyChip>;\n\n  constructor(\n    protected _elementRef: ElementRef<HTMLElement>,\n    private _changeDetectorRef: ChangeDetectorRef,\n    @Optional() private _dir: Directionality,\n    @Optional() _parentForm: NgForm,\n    @Optional() _parentFormGroup: FormGroupDirective,\n    _defaultErrorStateMatcher: ErrorStateMatcher,\n    @Optional() @Self() ngControl: NgControl,\n  ) {\n    super(_defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n    if (this.ngControl) {\n      this.ngControl.valueAccessor = this;\n    }\n  }\n\n  ngAfterContentInit() {\n    this._keyManager = new FocusKeyManager<MatLegacyChip>(this.chips)\n      .withWrap()\n      .withVerticalOrientation()\n      .withHomeAndEnd()\n      .withHorizontalOrientation(this._dir ? this._dir.value : 'ltr');\n\n    if (this._dir) {\n      this._dir.change\n        .pipe(takeUntil(this._destroyed))\n        .subscribe(dir => this._keyManager.withHorizontalOrientation(dir));\n    }\n\n    this._keyManager.tabOut.subscribe(() => this._allowFocusEscape());\n\n    // When the list changes, re-subscribe\n    this.chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      if (this.disabled || !this.selectable) {\n        // Since this happens after the content has been\n        // checked, we need to defer it to the next tick.\n        Promise.resolve().then(() => {\n          this._syncChipsState();\n        });\n      }\n\n      this._resetChips();\n\n      // Reset chips selected/deselected status\n      this._initializeSelection();\n\n      // Check to see if we need to update our tab index\n      this._updateTabIndex();\n\n      // Check to see if we have a destroyed chip and need to refocus\n      this._updateFocusForDestroyedChips();\n\n      this.stateChanges.next();\n    });\n  }\n\n  ngOnInit() {\n    this._selectionModel = new SelectionModel<MatLegacyChip>(this.multiple, undefined, false);\n    this.stateChanges.next();\n  }\n\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n\n      if (this.ngControl.disabled !== this._disabled) {\n        this.disabled = !!this.ngControl.disabled;\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.stateChanges.complete();\n    this._dropSubscriptions();\n  }\n\n  /** Associates an HTML input element with this chip list. */\n  registerInput(inputElement: MatLegacyChipTextControl): void {\n    this._chipInput = inputElement;\n\n    // We use this attribute to match the chip list to its input in test harnesses.\n    // Set the attribute directly here to avoid \"changed after checked\" errors.\n    this._elementRef.nativeElement.setAttribute('data-mat-chip-input', inputElement.id);\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids: string[]) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value: any): void {\n    if (this.chips) {\n      this._setSelectionByValue(value, false);\n    }\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn: (value: any) => void): void {\n    this._onChange = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn: () => void): void {\n    this._onTouched = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n    this.stateChanges.next();\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick(event: MouseEvent) {\n    if (!this._originatesFromChip(event)) {\n      this.focus();\n    }\n  }\n\n  /**\n   * Focuses the first non-disabled chip in this chip list, or the associated input when there\n   * are no eligible chips.\n   */\n  focus(options?: FocusOptions): void {\n    if (this.disabled) {\n      return;\n    }\n\n    // TODO: ARIA says this should focus the first `selected` chip if any are selected.\n    // Focus on first element if there's no chipInput inside chip-list\n    if (this._chipInput && this._chipInput.focused) {\n      // do nothing\n    } else if (this.chips.length > 0) {\n      this._keyManager.setFirstItemActive();\n      this.stateChanges.next();\n    } else {\n      this._focusInput(options);\n      this.stateChanges.next();\n    }\n  }\n\n  /** Attempt to focus an input if we have one. */\n  _focusInput(options?: FocusOptions) {\n    if (this._chipInput) {\n      this._chipInput.focus(options);\n    }\n  }\n\n  /**\n   * Pass events to the keyboard manager. Available here for tests.\n   */\n  _keydown(event: KeyboardEvent) {\n    const target = event.target as HTMLElement;\n\n    if (target && target.classList.contains('mat-chip')) {\n      this._keyManager.onKeydown(event);\n      this.stateChanges.next();\n    }\n  }\n\n  /**\n   * Check the tab index as you should not be allowed to focus an empty list.\n   */\n  protected _updateTabIndex(): void {\n    // If we have 0 chips, we should not allow keyboard focus\n    this._tabIndex = this._userTabIndex || (this.chips.length === 0 ? -1 : 0);\n  }\n\n  /**\n   * If the amount of chips changed, we need to update the\n   * key manager state and focus the next closest chip.\n   */\n  protected _updateFocusForDestroyedChips() {\n    // Move focus to the closest chip. If no other chips remain, focus the chip-list itself.\n    if (this._lastDestroyedChipIndex != null) {\n      if (this.chips.length) {\n        const newChipIndex = Math.min(this._lastDestroyedChipIndex, this.chips.length - 1);\n        this._keyManager.setActiveItem(newChipIndex);\n      } else {\n        this.focus();\n      }\n    }\n\n    this._lastDestroyedChipIndex = null;\n  }\n\n  /**\n   * Utility to ensure all indexes are valid.\n   *\n   * @param index The index to be checked.\n   * @returns True if the index is valid for our list of chips.\n   */\n  private _isValidIndex(index: number): boolean {\n    return index >= 0 && index < this.chips.length;\n  }\n\n  _setSelectionByValue(value: any, isUserInput: boolean = true) {\n    this._clearSelection();\n    this.chips.forEach(chip => chip.deselect());\n\n    if (Array.isArray(value)) {\n      value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n      this._sortValues();\n    } else {\n      const correspondingChip = this._selectValue(value, isUserInput);\n\n      // Shift focus to the active item. Note that we shouldn't do this in multiple\n      // mode, because we don't know what chip the user interacted with last.\n      if (correspondingChip) {\n        if (isUserInput) {\n          this._keyManager.setActiveItem(correspondingChip);\n        }\n      }\n    }\n  }\n\n  /**\n   * Finds and selects the chip based on its value.\n   * @returns Chip that has the corresponding value.\n   */\n  private _selectValue(value: any, isUserInput: boolean = true): MatLegacyChip | undefined {\n    const correspondingChip = this.chips.find(chip => {\n      return chip.value != null && this._compareWith(chip.value, value);\n    });\n\n    if (correspondingChip) {\n      isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n      this._selectionModel.select(correspondingChip);\n    }\n\n    return correspondingChip;\n  }\n\n  private _initializeSelection(): void {\n    // Defer setting the value in order to avoid the \"Expression\n    // has changed after it was checked\" errors from Angular.\n    Promise.resolve().then(() => {\n      if (this.ngControl || this._value) {\n        this._setSelectionByValue(this.ngControl ? this.ngControl.value : this._value, false);\n        this.stateChanges.next();\n      }\n    });\n  }\n\n  /**\n   * Deselects every chip in the list.\n   * @param skip Chip that should not be deselected.\n   */\n  private _clearSelection(skip?: MatLegacyChip): void {\n    this._selectionModel.clear();\n    this.chips.forEach(chip => {\n      if (chip !== skip) {\n        chip.deselect();\n      }\n    });\n    this.stateChanges.next();\n  }\n\n  /**\n   * Sorts the model values, ensuring that they keep the same\n   * order that they have in the panel.\n   */\n  private _sortValues(): void {\n    if (this._multiple) {\n      this._selectionModel.clear();\n\n      this.chips.forEach(chip => {\n        if (chip.selected) {\n          this._selectionModel.select(chip);\n        }\n      });\n      this.stateChanges.next();\n    }\n  }\n\n  /** Emits change event to set the model value. */\n  private _propagateChanges(fallbackValue?: any): void {\n    let valueToEmit: any = null;\n\n    if (Array.isArray(this.selected)) {\n      valueToEmit = this.selected.map(chip => chip.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : fallbackValue;\n    }\n    this._value = valueToEmit;\n    this.change.emit(new MatLegacyChipListChange(this, valueToEmit));\n    this.valueChange.emit(valueToEmit);\n    this._onChange(valueToEmit);\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** When blurred, mark the field as touched when focus moved outside the chip list. */\n  _blur() {\n    if (!this._hasFocusedChip()) {\n      this._keyManager.setActiveItem(-1);\n    }\n\n    if (!this.disabled) {\n      if (this._chipInput) {\n        // If there's a chip input, we should check whether the focus moved to chip input.\n        // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n        // to chip input, do nothing.\n        // Timeout is needed to wait for the focus() event trigger on chip input.\n        setTimeout(() => {\n          if (!this.focused) {\n            this._markAsTouched();\n          }\n        });\n      } else {\n        // If there's no chip input, then mark the field as touched.\n        this._markAsTouched();\n      }\n    }\n  }\n\n  /** Mark the field as touched */\n  _markAsTouched() {\n    this._onTouched();\n    this._changeDetectorRef.markForCheck();\n    this.stateChanges.next();\n  }\n\n  /**\n   * Removes the `tabindex` from the chip list and resets it back afterwards, allowing the\n   * user to tab out of it. This prevents the list from capturing focus and redirecting\n   * it back to the first chip, creating a focus trap, if it user tries to tab away.\n   */\n  _allowFocusEscape() {\n    if (this._tabIndex !== -1) {\n      this._tabIndex = -1;\n\n      setTimeout(() => {\n        this._tabIndex = this._userTabIndex || 0;\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n\n  private _resetChips() {\n    this._dropSubscriptions();\n    this._listenToChipsFocus();\n    this._listenToChipsSelection();\n    this._listenToChipsRemoved();\n  }\n\n  private _dropSubscriptions() {\n    if (this._chipFocusSubscription) {\n      this._chipFocusSubscription.unsubscribe();\n      this._chipFocusSubscription = null;\n    }\n\n    if (this._chipBlurSubscription) {\n      this._chipBlurSubscription.unsubscribe();\n      this._chipBlurSubscription = null;\n    }\n\n    if (this._chipSelectionSubscription) {\n      this._chipSelectionSubscription.unsubscribe();\n      this._chipSelectionSubscription = null;\n    }\n\n    if (this._chipRemoveSubscription) {\n      this._chipRemoveSubscription.unsubscribe();\n      this._chipRemoveSubscription = null;\n    }\n  }\n\n  /** Listens to user-generated selection events on each chip. */\n  private _listenToChipsSelection(): void {\n    this._chipSelectionSubscription = this.chipSelectionChanges.subscribe(event => {\n      event.source.selected\n        ? this._selectionModel.select(event.source)\n        : this._selectionModel.deselect(event.source);\n\n      // For single selection chip list, make sure the deselected value is unselected.\n      if (!this.multiple) {\n        this.chips.forEach(chip => {\n          if (!this._selectionModel.isSelected(chip) && chip.selected) {\n            chip.deselect();\n          }\n        });\n      }\n\n      if (event.isUserInput) {\n        this._propagateChanges();\n      }\n    });\n  }\n\n  /** Listens to user-generated selection events on each chip. */\n  private _listenToChipsFocus(): void {\n    this._chipFocusSubscription = this.chipFocusChanges.subscribe(event => {\n      let chipIndex: number = this.chips.toArray().indexOf(event.chip);\n\n      if (this._isValidIndex(chipIndex)) {\n        this._keyManager.updateActiveItem(chipIndex);\n      }\n      this.stateChanges.next();\n    });\n\n    this._chipBlurSubscription = this.chipBlurChanges.subscribe(() => {\n      this._blur();\n      this.stateChanges.next();\n    });\n  }\n\n  private _listenToChipsRemoved(): void {\n    this._chipRemoveSubscription = this.chipRemoveChanges.subscribe(event => {\n      const chip = event.chip;\n      const chipIndex = this.chips.toArray().indexOf(event.chip);\n\n      // In case the chip that will be removed is currently focused, we temporarily store\n      // the index in order to be able to determine an appropriate sibling chip that will\n      // receive focus.\n      if (this._isValidIndex(chipIndex) && chip._hasFocus) {\n        this._lastDestroyedChipIndex = chipIndex;\n      }\n    });\n  }\n\n  /** Checks whether an event comes from inside a chip element. */\n  private _originatesFromChip(event: Event): boolean {\n    let currentElement = event.target as HTMLElement | null;\n\n    while (currentElement && currentElement !== this._elementRef.nativeElement) {\n      if (currentElement.classList.contains('mat-chip')) {\n        return true;\n      }\n\n      currentElement = currentElement.parentElement;\n    }\n\n    return false;\n  }\n\n  /** Checks whether any of the chips is focused. */\n  private _hasFocusedChip() {\n    return this.chips && this.chips.some(chip => chip._hasFocus);\n  }\n\n  /** Syncs the list's state with the individual chips. */\n  private _syncChipsState() {\n    if (this.chips) {\n      this.chips.forEach(chip => {\n        chip._chipListDisabled = this._disabled;\n        chip._chipListMultiple = this.multiple;\n        chip.chipListSelectable = this._selectable;\n      });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {BACKSPACE, hasMod<PERSON><PERSON><PERSON>, TAB} from '@angular/cdk/keycodes';\nimport {\n  AfterContentInit,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Inject,\n  Input,\n  OnChanges,\n  OnDestroy,\n  Output,\n} from '@angular/core';\nimport {\n  MatLegacyChipsDefaultOptions,\n  MAT_LEGACY_CHIPS_DEFAULT_OPTIONS,\n} from './chip-default-options';\nimport {MatLegacyChipList} from './chip-list';\nimport {MatLegacyChipTextControl} from './chip-text-control';\n\n/**\n * Represents an input event on a `matChipInput`.\n * @deprecated Use `MatChipInputEvent` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport interface MatLegacyChipInputEvent {\n  /**\n   * The native `<input>` element that the event is being fired for.\n   * @deprecated Use `MatChipInputEvent#chipInput.inputElement` instead.\n   * @breaking-change 13.0.0 This property will be removed.\n   */\n  input: HTMLInputElement;\n\n  /** The value of the input. */\n  value: string;\n\n  /** Reference to the chip input that emitted the event. */\n  chipInput: MatLegacyChipInput;\n}\n\n// Increasing integer for generating unique ids.\nlet nextUniqueId = 0;\n\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of an `<mat-chip-list>`.\n * @deprecated Use `MatChipInput` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: 'input[matChipInputFor]',\n  exportAs: 'matChipInput, matChipInputFor',\n  host: {\n    'class': 'mat-chip-input mat-input-element',\n    '(keydown)': '_keydown($event)',\n    '(keyup)': '_keyup($event)',\n    '(blur)': '_blur()',\n    '(focus)': '_focus()',\n    '(input)': '_onInput()',\n    '[id]': 'id',\n    '[attr.disabled]': 'disabled || null',\n    '[attr.placeholder]': 'placeholder || null',\n    '[attr.aria-invalid]': '_chipList && _chipList.ngControl ? _chipList.ngControl.invalid : null',\n    '[attr.aria-required]': '_chipList && _chipList.required || null',\n  },\n})\nexport class MatLegacyChipInput\n  implements MatLegacyChipTextControl, OnChanges, OnDestroy, AfterContentInit\n{\n  /** Used to prevent focus moving to chips while user is holding backspace */\n  private _focusLastChipOnBackspace: boolean;\n\n  /** Whether the control is focused. */\n  focused: boolean = false;\n  _chipList: MatLegacyChipList;\n\n  /** Register input for chip list */\n  @Input('matChipInputFor')\n  set chipList(value: MatLegacyChipList) {\n    if (value) {\n      this._chipList = value;\n      this._chipList.registerInput(this);\n    }\n  }\n\n  /**\n   * Whether or not the chipEnd event will be emitted when the input is blurred.\n   */\n  @Input('matChipInputAddOnBlur')\n  get addOnBlur(): boolean {\n    return this._addOnBlur;\n  }\n  set addOnBlur(value: BooleanInput) {\n    this._addOnBlur = coerceBooleanProperty(value);\n  }\n  _addOnBlur: boolean = false;\n\n  /**\n   * The list of key codes that will trigger a chipEnd event.\n   *\n   * Defaults to `[ENTER]`.\n   */\n  @Input('matChipInputSeparatorKeyCodes')\n  separatorKeyCodes: readonly number[] | ReadonlySet<number>;\n\n  /** Emitted when a chip is to be added. */\n  @Output('matChipInputTokenEnd') readonly chipEnd = new EventEmitter<MatLegacyChipInputEvent>();\n\n  /** The input's placeholder text. */\n  @Input() placeholder: string = '';\n\n  /** Unique id for the input. */\n  @Input() id: string = `mat-chip-list-input-${nextUniqueId++}`;\n\n  /** Whether the input is disabled. */\n  @Input()\n  get disabled(): boolean {\n    return this._disabled || (this._chipList && this._chipList.disabled);\n  }\n  set disabled(value: BooleanInput) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n  private _disabled: boolean = false;\n\n  /** Whether the input is empty. */\n  get empty(): boolean {\n    return !this.inputElement.value;\n  }\n\n  /** The native input element to which this directive is attached. */\n  readonly inputElement!: HTMLInputElement;\n\n  constructor(\n    protected _elementRef: ElementRef<HTMLInputElement>,\n    @Inject(MAT_LEGACY_CHIPS_DEFAULT_OPTIONS) defaultOptions: MatLegacyChipsDefaultOptions,\n  ) {\n    this.inputElement = this._elementRef.nativeElement as HTMLInputElement;\n    this.separatorKeyCodes = defaultOptions.separatorKeyCodes;\n  }\n\n  ngOnChanges(): void {\n    this._chipList.stateChanges.next();\n  }\n\n  ngOnDestroy(): void {\n    this.chipEnd.complete();\n  }\n\n  ngAfterContentInit(): void {\n    this._focusLastChipOnBackspace = this.empty;\n  }\n\n  /** Utility method to make host definition/tests more clear. */\n  _keydown(event?: KeyboardEvent) {\n    if (event) {\n      // Allow the user's focus to escape when they're tabbing forward. Note that we don't\n      // want to do this when going backwards, because focus should go back to the first chip.\n      if (event.keyCode === TAB && !hasModifierKey(event, 'shiftKey')) {\n        this._chipList._allowFocusEscape();\n      }\n\n      // To prevent the user from accidentally deleting chips when pressing BACKSPACE continuously,\n      // We focus the last chip on backspace only after the user has released the backspace button,\n      // and the input is empty (see behaviour in _keyup)\n      if (event.keyCode === BACKSPACE && this._focusLastChipOnBackspace) {\n        this._chipList._keyManager.setLastItemActive();\n        event.preventDefault();\n        return;\n      } else {\n        this._focusLastChipOnBackspace = false;\n      }\n    }\n\n    this._emitChipEnd(event);\n  }\n\n  /**\n   * Pass events to the keyboard manager. Available here for tests.\n   */\n  _keyup(event: KeyboardEvent) {\n    // Allow user to move focus to chips next time he presses backspace\n    if (!this._focusLastChipOnBackspace && event.keyCode === BACKSPACE && this.empty) {\n      this._focusLastChipOnBackspace = true;\n      event.preventDefault();\n    }\n  }\n\n  /** Checks to see if the blur should emit the (chipEnd) event. */\n  _blur() {\n    if (this.addOnBlur) {\n      this._emitChipEnd();\n    }\n    this.focused = false;\n    // Blur the chip list if it is not focused\n    if (!this._chipList.focused) {\n      this._chipList._blur();\n    }\n    this._chipList.stateChanges.next();\n  }\n\n  _focus() {\n    this.focused = true;\n    this._focusLastChipOnBackspace = this.empty;\n    this._chipList.stateChanges.next();\n  }\n\n  /** Checks to see if the (chipEnd) event needs to be emitted. */\n  _emitChipEnd(event?: KeyboardEvent) {\n    if (!this.inputElement.value && !!event) {\n      this._chipList._keydown(event);\n    }\n\n    if (!event || this._isSeparatorKey(event)) {\n      this.chipEnd.emit({\n        input: this.inputElement,\n        value: this.inputElement.value,\n        chipInput: this,\n      });\n\n      event?.preventDefault();\n    }\n  }\n\n  _onInput() {\n    // Let chip list know whenever the value changes.\n    this._chipList.stateChanges.next();\n  }\n\n  /** Focuses the input. */\n  focus(options?: FocusOptions): void {\n    this.inputElement.focus(options);\n  }\n\n  /** Clears the input */\n  clear(): void {\n    this.inputElement.value = '';\n    this._focusLastChipOnBackspace = true;\n  }\n\n  /** Checks whether a keycode is one of the configured separators. */\n  private _isSeparatorKey(event: KeyboardEvent) {\n    return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ENTER} from '@angular/cdk/keycodes';\nimport {NgModule} from '@angular/core';\nimport {ErrorStateMatcher, MatCommonModule} from '@angular/material/core';\nimport {\n  MatLegacyChip,\n  MatLegacyChipAvatar,\n  MatLegacyChipRemove,\n  MatLegacyChipTrailingIcon,\n} from './chip';\nimport {\n  MAT_LEGACY_CHIPS_DEFAULT_OPTIONS,\n  MatLegacyChipsDefaultOptions,\n} from './chip-default-options';\nimport {MatLegacyChipInput} from './chip-input';\nimport {MatLegacyChipList} from './chip-list';\n\nconst CHIP_DECLARATIONS = [\n  MatLegacyChipList,\n  MatLegacyChip,\n  MatLegacyChipInput,\n  MatLegacyChipRemove,\n  MatLegacyChipAvatar,\n  MatLegacyChipTrailingIcon,\n];\n\n/**\n * @deprecated Use `MatChipsModule` from `@angular/material/chips` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@NgModule({\n  imports: [MatCommonModule],\n  exports: CHIP_DECLARATIONS,\n  declarations: CHIP_DECLARATIONS,\n  providers: [\n    ErrorStateMatcher,\n    {\n      provide: MAT_LEGACY_CHIPS_DEFAULT_OPTIONS,\n      useValue: {\n        separatorKeyCodes: [ENTER],\n      } as MatLegacyChipsDefaultOptions,\n    },\n  ],\n})\nexport class MatLegacyChipsModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["nextUniqueId", "i1"], "mappings": ";;;;;;;;;;;;;;;;;;AAwDA;;;;AAIG;MACU,4BAA4B,CAAA;AACvC,IAAA,WAAA;;IAES,MAAqB;;IAErB,QAAiB;;AAEjB,IAAA,WAAA,GAAc,KAAK,EAAA;QAJnB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAErB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAS;QAEjB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;KACxB;AACL,CAAA;AAED;;;;;;AAMG;MACU,sBAAsB,GAAG,IAAI,cAAc,CAAsB,eAAe,EAAE;AAE/F;;;;;;AAMG;MACU,sBAAsB,GAAG,IAAI,cAAc,CAAsB,eAAe,EAAE;AAE/F;;;;;;AAMG;MACU,6BAA6B,GAAG,IAAI,cAAc,CAC7D,qBAAqB,EACrB;AAEF;AACA;AACA,MAAe,WAAW,CAAA;AAExB,IAAA,WAAA,CAAmB,WAAuB,EAAA;QAAvB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;KAAI;AAC/C,CAAA;AAED,MAAM,iBAAiB,GAAG,aAAa,CAAC,UAAU,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAEpG;;;;;AAKG;MAMU,mBAAmB,CAAA;8GAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAnB,mBAAmB,EAAA,QAAA,EAAA,kCAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,SAAA,EAFnB,CAAC,EAAC,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAE,mBAAmB,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAErE,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAL/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kCAAkC;AAC5C,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,iBAAiB,EAAC;oBAClC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAqB,mBAAA,EAAC,CAAC;AACjF,iBAAA,CAAA;;AAGD;;;;;AAKG;MAMU,yBAAyB,CAAA;8GAAzB,yBAAyB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAzB,yBAAyB,EAAA,QAAA,EAAA,+CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,wBAAA,EAAA,EAAA,SAAA,EAFzB,CAAC,EAAC,OAAO,EAAE,6BAA6B,EAAE,WAAW,EAAE,yBAAyB,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAElF,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBALrC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+CAA+C;AACzD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,wBAAwB,EAAC;oBACzC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,6BAA6B,EAAE,WAAW,EAA2B,yBAAA,EAAC,CAAC;AAC9F,iBAAA,CAAA;;AAGD;;;;AAIG;AAuBG,MAAO,aACX,SAAQ,iBAAiB,CAAA;AA4BzB;;;AAGG;AACH,IAAA,IAAI,cAAc,GAAA;QAChB,QACE,IAAI,CAAC,QAAQ;AACb,YAAA,IAAI,CAAC,aAAa;AAClB,YAAA,IAAI,CAAC,mBAAmB;AACxB,YAAA,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAC5B;KACH;;AA8BD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,MAAM,YAAY,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;AAElD,QAAA,IAAI,YAAY,KAAK,IAAI,CAAC,SAAS,EAAE;AACnC,YAAA,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC;YAC9B,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACjC,SAAA;KACF;;AAID,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC;KAC7F;IACD,IAAI,KAAK,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KACrB;AAGD;;;;;AAKG;AACH,IAAA,IACI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,kBAAkB,CAAC;KACpD;IACD,IAAI,UAAU,CAAC,KAAmB,EAAA;AAChC,QAAA,IAAI,CAAC,WAAW,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KACjD;;AAID,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,SAAS,CAAC;KACjD;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC/C;AAGD;;AAEG;AACH,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IACD,IAAI,SAAS,CAAC,KAAmB,EAAA;AAC/B,QAAA,IAAI,CAAC,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAChD;;AAsBD,IAAA,IAAI,YAAY,GAAA;;;AAGd,QAAA,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,QAAQ,CAAC;AACjE,cAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;cACxB,IAAI,CAAC;KACV;AAED,IAAA,WAAA,CACE,UAAmC,EAC3B,OAAe,EACvB,QAAkB,EAGlB,mBAA+C,EACvC,kBAAqC,EAC3B,SAAc,EACW,aAAsB,EAC1C,QAAiB,EAAA;QAExC,KAAK,CAAC,UAAU,CAAC,CAAC;QAVV,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QAKf,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;;QA1H/C,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;;QAM3B,IAAkB,CAAA,kBAAA,GAAY,IAAI,CAAC;;QAGnC,IAAiB,CAAA,iBAAA,GAAY,KAAK,CAAC;;QAGnC,IAAiB,CAAA,iBAAA,GAAY,KAAK,CAAC;;QAY1B,IAAI,CAAA,IAAA,GAAW,QAAQ,CAAC;QAevB,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;QAyB3B,IAAW,CAAA,WAAA,GAAY,IAAI,CAAC;QAU5B,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;QAY3B,IAAU,CAAA,UAAA,GAAY,IAAI,CAAC;;AAG5B,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,OAAO,EAAsB,CAAC;;AAG7C,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,OAAO,EAAsB,CAAC;;AAGlC,QAAA,IAAA,CAAA,eAAe,GAChC,IAAI,YAAY,EAAgC,CAAC;;AAGhC,QAAA,IAAA,CAAA,SAAS,GAC1B,IAAI,YAAY,EAAsB,CAAC;;AAGtB,QAAA,IAAA,CAAA,OAAO,GACxB,IAAI,YAAY,EAAsB,CAAC;QAyBvC,IAAI,CAAC,iBAAiB,EAAE,CAAC;;;;QAKzB,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACxD,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACxD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;AACvF,QAAA,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAEhD,QAAA,IAAI,CAAC,YAAY,GAAG,mBAAmB,IAAI,EAAE,CAAC;AAC9C,QAAA,IAAI,CAAC,mBAAmB,GAAG,aAAa,KAAK,gBAAgB,CAAC;QAC9D,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAClE;IAED,iBAAiB,GAAA;QACf,MAAM,iBAAiB,GAAG,gBAAgB,CAAC;AAC3C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAA4B,CAAC;AAE9D,QAAA,IACE,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC;AACvC,YAAA,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,iBAAiB,EACnD;AACA,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YACzC,OAAO;AACR,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AAC5C,SAAA;KACF;IAED,WAAW,GAAA;QACT,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAC;KACzC;;IAGD,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAChC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACxC,SAAA;KACF;;IAGD,QAAQ,GAAA;QACN,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAChC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACxC,SAAA;KACF;;IAGD,oBAAoB,GAAA;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,YAAA,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACxC,SAAA;KACF;;IAGD,cAAc,CAAC,cAAuB,KAAK,EAAA;AACzC,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;AAChC,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YACvC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;AAClC,SAAA;AACD,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACvB;AAED;;;;;AAKG;IACH,MAAM,GAAA;QACJ,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;AACjC,SAAA;KACF;;AAGD,IAAA,YAAY,CAAC,KAAY,EAAA;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,KAAK,CAAC,cAAc,EAAE,CAAC;AACxB,SAAA;KACF;;AAGD,IAAA,cAAc,CAAC,KAAoB,EAAA;QACjC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO;AACR,SAAA;QAED,QAAQ,KAAK,CAAC,OAAO;AACnB,YAAA,KAAK,MAAM,CAAC;AACZ,YAAA,KAAK,SAAS;;gBAEZ,IAAI,CAAC,MAAM,EAAE,CAAC;;gBAEd,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACR,YAAA,KAAK,KAAK;;gBAER,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAC3B,iBAAA;;gBAGD,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,SAAA;KACF;IAED,KAAK,GAAA;;;;;AAKH,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AACjD,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;AAClC,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;IAEO,wBAAwB,CAAC,WAAW,GAAG,KAAK,EAAA;AAClD,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AACxB,YAAA,MAAM,EAAE,IAAI;YACZ,WAAW;YACX,QAAQ,EAAE,IAAI,CAAC,SAAS;AACzB,SAAA,CAAC,CAAC;KACJ;AA5TU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,0FAmKd,yBAAyB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAGzB,QAAQ,EACI,EAAA,EAAA,KAAA,EAAA,qBAAqB,6BAC9B,UAAU,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAxKZ,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EA0DV,QAAA,EAAA,wDAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,eAAA,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,sBAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,SAAA,EAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,4BAAA,EAAA,WAAA,EAAA,MAAA,EAAA,yBAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,QAAA,EAAA,mCAAA,EAAA,4BAAA,EAAA,yBAAA,EAAA,UAAA,EAAA,+BAAA,EAAA,qBAAA,EAAA,eAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,oBAAA,EAAA,cAAA,EAAA,EAAA,cAAA,EAAA,8BAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,sBAAsB,EAGtB,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,6BAA6B,6EAG7B,sBAAsB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAhEzB,aAAa,EAAA,UAAA,EAAA,CAAA;kBAtBzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAwD,sDAAA,CAAA;AAClE,oBAAA,MAAM,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,UAAU,CAAC;AAC9C,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,8BAA8B;AACvC,wBAAA,iBAAiB,EAAE,4BAA4B;AAC/C,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,2BAA2B,EAAE,UAAU;AACvC,wBAAA,8BAA8B,EAAE,QAAQ;AACxC,wBAAA,qCAAqC,EAAE,4BAA4B;AACnE,wBAAA,2BAA2B,EAAE,UAAU;AACvC,wBAAA,iCAAiC,EAAE,qBAAqB;AACxD,wBAAA,iBAAiB,EAAE,kBAAkB;AACrC,wBAAA,sBAAsB,EAAE,qBAAqB;AAC7C,wBAAA,sBAAsB,EAAE,cAAc;AACtC,wBAAA,SAAS,EAAE,sBAAsB;AACjC,wBAAA,WAAW,EAAE,wBAAwB;AACrC,wBAAA,SAAS,EAAE,SAAS;AACpB,wBAAA,QAAQ,EAAE,SAAS;AACpB,qBAAA;AACF,iBAAA,CAAA;;0BAmKI,QAAQ;;0BACR,MAAM;2BAAC,yBAAyB,CAAA;;0BAGhC,MAAM;2BAAC,QAAQ,CAAA;;0BACf,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,SAAS;2BAAC,UAAU,CAAA;4CA9Ge,MAAM,EAAA,CAAA;sBAA3C,YAAY;uBAAC,sBAAsB,CAAA;gBAGS,YAAY,EAAA,CAAA;sBAAxD,YAAY;uBAAC,6BAA6B,CAAA;gBAGL,UAAU,EAAA,CAAA;sBAA/C,YAAY;uBAAC,sBAAsB,CAAA;gBAG3B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAIF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAgBF,KAAK,EAAA,CAAA;sBADR,KAAK;gBAgBF,UAAU,EAAA,CAAA;sBADb,KAAK;gBAWF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAaF,SAAS,EAAA,CAAA;sBADZ,KAAK;gBAgBa,eAAe,EAAA,CAAA;sBAAjC,MAAM;gBAIY,SAAS,EAAA,CAAA;sBAA3B,MAAM;gBAIY,OAAO,EAAA,CAAA;sBAAzB,MAAM;;AA6KT;;;;;;;;;;;;;;;AAeG;MASU,mBAAmB,CAAA;IAC9B,WAAsB,CAAA,WAA0B,EAAE,UAAmC,EAAA;QAA/D,IAAW,CAAA,WAAA,GAAX,WAAW,CAAe;AAC9C,QAAA,IAAI,UAAU,CAAC,aAAa,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAClD,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACzD,SAAA;KACF;;AAGD,IAAA,YAAY,CAAC,KAAY,EAAA;AACvB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAEpC,IAAI,UAAU,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;YAChD,UAAU,CAAC,MAAM,EAAE,CAAC;AACrB,SAAA;;;;;;QAOD,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,KAAK,CAAC,cAAc,EAAE,CAAC;KACxB;8GAtBU,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAnB,mBAAmB,EAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,sBAAA,EAAA,EAAA,cAAA,EAAA,wCAAA,EAAA,EAAA,SAAA,EAFnB,CAAC,EAAC,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAE,mBAAmB,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAErE,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAR/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,wCAAwC;AACjD,wBAAA,SAAS,EAAE,sBAAsB;AAClC,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAqB,mBAAA,EAAC,CAAC;AACjF,iBAAA,CAAA;;;ACreD;;;;AAIG;MACU,gCAAgC,GAAG,IAAI,cAAc,CAChE,2BAA2B;;ACkB7B;AACA;AACA,MAAM,gBAAgB,GAAG,eAAe,CACtC,MAAA;AAQE,IAAA,WAAA,CACS,yBAA4C,EAC5C,WAAmB,EACnB,gBAAoC;AAC3C;;;;AAIG;IACI,SAAoB,EAAA;QARpB,IAAyB,CAAA,yBAAA,GAAzB,yBAAyB,CAAmB;QAC5C,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;QACnB,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAoB;QAMpC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;AAhB7B;;;;AAIG;AACM,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,OAAO,EAAQ,CAAC;KAYxC;AACL,CAAA,CACF,CAAC;AAEF;AACA,IAAIA,cAAY,GAAG,CAAC,CAAC;AAErB;;;;AAIG;MACU,uBAAuB,CAAA;AAClC,IAAA,WAAA;;IAES,MAAyB;;IAEzB,KAAU,EAAA;QAFV,IAAM,CAAA,MAAA,GAAN,MAAM,CAAmB;QAEzB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAK;KACf;AACL,CAAA;AAED;;;;AAIG;AA4BG,MAAO,iBACX,SAAQ,gBAAgB,CAAA;;AAiExB,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;KACjG;;AAGD,IAAA,IACI,IAAI,GAAA;QACN,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO,IAAI,CAAC,aAAa,CAAC;AAC3B,SAAA;QAED,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,SAAS,CAAC;KACtC;IACD,IAAI,IAAI,CAAC,IAAmB,EAAA;AAC1B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;KAC3B;;AAaD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,eAAe,EAAE,CAAC;KACxB;AAGD;;;;AAIG;AACH,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;IACD,IAAI,WAAW,CAAC,EAAiC,EAAA;AAC/C,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,eAAe,EAAE;;YAExB,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC7B,SAAA;KACF;AAGD;;;AAGG;AACH,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IACD,IAAI,KAAK,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KACrB;AAGD;;;AAGG;AACH,IAAA,IAAI,EAAE,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC;KACzD;AAED;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC;KAC9F;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;AAGD;;;AAGG;AACH,IAAA,IACI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;KAC1E;IACD,IAAI,WAAW,CAAC,KAAa,EAAA;AAC3B,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;;AAID,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;KAC/E;AAED;;;AAGG;AACH,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;KAChG;AAED;;;AAGG;AACH,IAAA,IAAI,gBAAgB,GAAA;QAClB,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC;KACpC;AAED;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;KACpE;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,eAAe,EAAE,CAAC;KACxB;AAMD;;;AAGG;AACH,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IACD,IAAI,UAAU,CAAC,KAAmB,EAAA;AAChC,QAAA,IAAI,CAAC,WAAW,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,EAAE,CAAC;KACxB;IAGD,IACI,QAAQ,CAAC,KAAa,EAAA;AACxB,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KACxB;;AAGD,IAAA,IAAI,oBAAoB,GAAA;AACtB,QAAA,OAAO,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;KAC/D;;AAGD,IAAA,IAAI,gBAAgB,GAAA;AAClB,QAAA,OAAO,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;KACxD;;AAGD,IAAA,IAAI,eAAe,GAAA;AACjB,QAAA,OAAO,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;KACvD;;AAGD,IAAA,IAAI,iBAAiB,GAAA;AACnB,QAAA,OAAO,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;KACzD;AAoBD,IAAA,WAAA,CACY,WAAoC,EACtC,kBAAqC,EACzB,IAAoB,EAC5B,WAAmB,EACnB,gBAAoC,EAChD,yBAA4C,EACxB,SAAoB,EAAA;QAExC,KAAK,CAAC,yBAAyB,EAAE,WAAW,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QARjE,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACtC,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACzB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;AApQ1C;;;AAGG;QACM,IAAW,CAAA,WAAA,GAAW,eAAe,CAAC;AAE/C;;;;AAIG;QACK,IAAuB,CAAA,uBAAA,GAAkB,IAAI,CAAC;;AAGrC,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;;AAkBlD,QAAA,IAAA,CAAA,IAAI,GAAW,CAAA,cAAA,EAAiBA,cAAY,EAAE,EAAE,CAAC;;QAGjD,IAAS,CAAA,SAAA,GAAG,CAAC,CAAC;AAEd;;;AAGG;QACH,IAAa,CAAA,aAAA,GAAkB,IAAI,CAAC;;AAMpC,QAAA,IAAA,CAAA,UAAU,GAAG,MAAK,GAAG,CAAC;;AAGtB,QAAA,IAAA,CAAA,SAAS,GAAyB,MAAK,GAAG,CAAC;QAyCnC,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;QAkB3B,IAAY,CAAA,YAAA,GAAG,CAAC,EAAO,EAAE,EAAO,KAAK,EAAE,KAAK,EAAE,CAAC;QAqF7C,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;;QAGV,IAAe,CAAA,eAAA,GAA8B,YAAY,CAAC;QAc3E,IAAW,CAAA,WAAA,GAAY,IAAI,CAAC;;AA6BnB,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,YAAY,EAA2B,CAAC;AAExE;;;;AAIG;AACgB,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,YAAY,EAAO,CAAC;QAoBvD,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC;AACrC,SAAA;KACF;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAgB,IAAI,CAAC,KAAK,CAAC;AAC9D,aAAA,QAAQ,EAAE;AACV,aAAA,uBAAuB,EAAE;AACzB,aAAA,cAAc,EAAE;AAChB,aAAA,yBAAyB,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAElE,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,MAAM;AACb,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAChC,iBAAA,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,CAAC;AACtE,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;;QAGlE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YAClF,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;;;AAGrC,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;oBAC1B,IAAI,CAAC,eAAe,EAAE,CAAC;AACzB,iBAAC,CAAC,CAAC;AACJ,aAAA;YAED,IAAI,CAAC,WAAW,EAAE,CAAC;;YAGnB,IAAI,CAAC,oBAAoB,EAAE,CAAC;;YAG5B,IAAI,CAAC,eAAe,EAAE,CAAC;;YAGvB,IAAI,CAAC,6BAA6B,EAAE,CAAC;AAErC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACJ;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAgB,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AAC1F,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;IAED,SAAS,GAAA;QACP,IAAI,IAAI,CAAC,SAAS,EAAE;;;;YAIlB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAExB,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,EAAE;gBAC9C,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC3C,aAAA;AACF,SAAA;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC7B,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC3B;;AAGD,IAAA,aAAa,CAAC,YAAsC,EAAA;AAClD,QAAA,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC;;;AAI/B,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,qBAAqB,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC;KACrF;AAED;;;AAGG;AACH,IAAA,iBAAiB,CAAC,GAAa,EAAA;QAC7B,IAAI,GAAG,CAAC,MAAM,EAAE;AACd,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAChF,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;AACpE,SAAA;KACF;;AAGD,IAAA,UAAU,CAAC,KAAU,EAAA;QACnB,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACzC,SAAA;KACF;;AAGD,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;KACrB;;AAGD,IAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB;;AAGD,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;AAC3B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,KAAiB,EAAA;AAChC,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;YACpC,IAAI,CAAC,KAAK,EAAE,CAAC;AACd,SAAA;KACF;AAED;;;AAGG;AACH,IAAA,KAAK,CAAC,OAAsB,EAAA;QAC1B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO;AACR,SAAA;;;QAID,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;;AAE/C,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAChC,YAAA,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC;AACtC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AAC1B,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC1B,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AAC1B,SAAA;KACF;;AAGD,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAChC,SAAA;KACF;AAED;;AAEG;AACH,IAAA,QAAQ,CAAC,KAAoB,EAAA;AAC3B,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAqB,CAAC;QAE3C,IAAI,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AACnD,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAClC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AAC1B,SAAA;KACF;AAED;;AAEG;IACO,eAAe,GAAA;;QAEvB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC3E;AAED;;;AAGG;IACO,6BAA6B,GAAA;;AAErC,QAAA,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,EAAE;AACxC,YAAA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACrB,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACnF,gBAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;AAC9C,aAAA;AAAM,iBAAA;gBACL,IAAI,CAAC,KAAK,EAAE,CAAC;AACd,aAAA;AACF,SAAA;AAED,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;KACrC;AAED;;;;;AAKG;AACK,IAAA,aAAa,CAAC,KAAa,EAAA;QACjC,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;KAChD;AAED,IAAA,oBAAoB,CAAC,KAAU,EAAE,WAAA,GAAuB,IAAI,EAAA;QAC1D,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AAE5C,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,YAAA,KAAK,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC;YAC5E,IAAI,CAAC,WAAW,EAAE,CAAC;AACpB,SAAA;AAAM,aAAA;YACL,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;;;AAIhE,YAAA,IAAI,iBAAiB,EAAE;AACrB,gBAAA,IAAI,WAAW,EAAE;AACf,oBAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;AACnD,iBAAA;AACF,aAAA;AACF,SAAA;KACF;AAED;;;AAGG;AACK,IAAA,YAAY,CAAC,KAAU,EAAE,WAAA,GAAuB,IAAI,EAAA;QAC1D,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAG;AAC/C,YAAA,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACpE,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,iBAAiB,EAAE;AACrB,YAAA,WAAW,GAAG,iBAAiB,CAAC,oBAAoB,EAAE,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC;AACpF,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAChD,SAAA;AAED,QAAA,OAAO,iBAAiB,CAAC;KAC1B;IAEO,oBAAoB,GAAA;;;AAG1B,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,YAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE;gBACjC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACtF,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AAC1B,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;AAED;;;AAGG;AACK,IAAA,eAAe,CAAC,IAAoB,EAAA;AAC1C,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;YACxB,IAAI,IAAI,KAAK,IAAI,EAAE;gBACjB,IAAI,CAAC,QAAQ,EAAE,CAAC;AACjB,aAAA;AACH,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;AAED;;;AAGG;IACK,WAAW,GAAA;QACjB,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;AAE7B,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;gBACxB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,oBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnC,iBAAA;AACH,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AAC1B,SAAA;KACF;;AAGO,IAAA,iBAAiB,CAAC,aAAmB,EAAA;QAC3C,IAAI,WAAW,GAAQ,IAAI,CAAC;QAE5B,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAChC,YAAA,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AACrD,SAAA;AAAM,aAAA;AACL,YAAA,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,aAAa,CAAC;AACnE,SAAA;AACD,QAAA,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,uBAAuB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;AACjE,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;YAC3B,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,IAAI,CAAC,UAAU,EAAE;;;;;gBAKnB,UAAU,CAAC,MAAK;AACd,oBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;wBACjB,IAAI,CAAC,cAAc,EAAE,CAAC;AACvB,qBAAA;AACH,iBAAC,CAAC,CAAC;AACJ,aAAA;AAAM,iBAAA;;gBAEL,IAAI,CAAC,cAAc,EAAE,CAAC;AACvB,aAAA;AACF,SAAA;KACF;;IAGD,cAAc,GAAA;QACZ,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACvC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;AAED;;;;AAIG;IACH,iBAAiB,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,EAAE;AACzB,YAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;YAEpB,UAAU,CAAC,MAAK;gBACd,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC;AACzC,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,aAAC,CAAC,CAAC;AACJ,SAAA;KACF;IAEO,WAAW,GAAA;QACjB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,qBAAqB,EAAE,CAAC;KAC9B;IAEO,kBAAkB,GAAA;QACxB,IAAI,IAAI,CAAC,sBAAsB,EAAE;AAC/B,YAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;AAC1C,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACpC,SAAA;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC9B,YAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;AACzC,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACnC,SAAA;QAED,IAAI,IAAI,CAAC,0BAA0B,EAAE;AACnC,YAAA,IAAI,CAAC,0BAA0B,CAAC,WAAW,EAAE,CAAC;AAC9C,YAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;AACxC,SAAA;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,YAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;AAC3C,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACrC,SAAA;KACF;;IAGO,uBAAuB,GAAA;QAC7B,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,KAAK,IAAG;YAC5E,KAAK,CAAC,MAAM,CAAC,QAAQ;kBACjB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;kBACzC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;;AAGhD,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;AACxB,oBAAA,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;wBAC3D,IAAI,CAAC,QAAQ,EAAE,CAAC;AACjB,qBAAA;AACH,iBAAC,CAAC,CAAC;AACJ,aAAA;YAED,IAAI,KAAK,CAAC,WAAW,EAAE;gBACrB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC1B,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;;IAGO,mBAAmB,GAAA;QACzB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,IAAG;AACpE,YAAA,IAAI,SAAS,GAAW,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAEjE,YAAA,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE;AACjC,gBAAA,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;AAC9C,aAAA;AACD,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAK;YAC/D,IAAI,CAAC,KAAK,EAAE,CAAC;AACb,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACJ;IAEO,qBAAqB,GAAA;QAC3B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,IAAG;AACtE,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;AACxB,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;;;;YAK3D,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;AACnD,gBAAA,IAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC;AAC1C,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;;AAGO,IAAA,mBAAmB,CAAC,KAAY,EAAA;AACtC,QAAA,IAAI,cAAc,GAAG,KAAK,CAAC,MAA4B,CAAC;QAExD,OAAO,cAAc,IAAI,cAAc,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;YAC1E,IAAI,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AACjD,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AAED,YAAA,cAAc,GAAG,cAAc,CAAC,aAAa,CAAC;AAC/C,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KACd;;IAGO,eAAe,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;KAC9D;;IAGO,eAAe,GAAA;QACrB,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;AACxB,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;AACxC,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;AACvC,gBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC;AAC7C,aAAC,CAAC,CAAC;AACJ,SAAA;KACF;8GA7tBU,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,cAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,kBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAjB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,EALjB,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,mBAAA,EAAA,CAAA,kBAAA,EAAA,qBAAA,CAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,UAAA,EAAA,eAAA,EAAA,CAAA,kBAAA,EAAA,iBAAA,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,SAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,6BAAA,EAAA,oBAAA,EAAA,wBAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,mBAAA,EAAA,YAAA,EAAA,2BAAA,EAAA,UAAA,EAAA,WAAA,EAAA,MAAA,EAAA,8BAAA,EAAA,UAAA,EAAA,6BAAA,EAAA,YAAA,EAAA,8BAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,eAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,yBAAyB,EAAE,WAAW,EAAE,iBAAiB,EAAC,CAAC,EA0QhE,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,aAAa,kGA9RpB,CAAoE,kEAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,+yGAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAyBnE,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBA3B7B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,EACf,QAAA,EAAA,CAAA,kEAAA,CAAoE,EACpE,QAAA,EAAA,aAAa,EACjB,IAAA,EAAA;AACJ,wBAAA,iBAAiB,EAAE,6BAA6B;AAChD,wBAAA,sBAAsB,EAAE,wBAAwB;AAChD,wBAAA,sBAAsB,EAAE,qBAAqB;AAC7C,wBAAA,qBAAqB,EAAE,YAAY;AACnC,wBAAA,6BAA6B,EAAE,UAAU;AACzC,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,gCAAgC,EAAE,UAAU;AAC5C,wBAAA,+BAA+B,EAAE,YAAY;AAC7C,wBAAA,gCAAgC,EAAE,UAAU;AAC5C,wBAAA,yBAAyB,EAAE,iBAAiB;AAC5C,wBAAA,OAAO,EAAE,eAAe;AACxB,wBAAA,SAAS,EAAE,SAAS;AACpB,wBAAA,QAAQ,EAAE,SAAS;AACnB,wBAAA,WAAW,EAAE,kBAAkB;AAC/B,wBAAA,MAAM,EAAE,MAAM;AACd,wBAAA,iBAAiB,EAAE,EAAE;AACtB,qBAAA,EAAA,SAAA,EACU,CAAC,EAAC,OAAO,EAAE,yBAAyB,EAAE,WAAW,EAAA,iBAAmB,EAAC,CAAC,iBAElE,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,+yGAAA,CAAA,EAAA,CAAA;;0BAiR5C,QAAQ;;0BACR,QAAQ;;0BACR,QAAQ;;0BAER,QAAQ;;0BAAI,IAAI;4CA3Mf,IAAI,EAAA,CAAA;sBADP,KAAK;gBAiBqB,mBAAmB,EAAA,CAAA;sBAA7C,KAAK;uBAAC,kBAAkB,CAAA;gBAGP,iBAAiB,EAAA,CAAA;sBAAlC,KAAK;gBAIF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAgBF,WAAW,EAAA,CAAA;sBADd,KAAK;gBAkBF,KAAK,EAAA,CAAA;sBADR,KAAK;gBAuBF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAeF,WAAW,EAAA,CAAA;sBADd,KAAK;gBAoCF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAWqB,eAAe,EAAA,CAAA;sBAAzC,KAAK;uBAAC,kBAAkB,CAAA;gBAOrB,UAAU,EAAA,CAAA;sBADb,KAAK;gBAWF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBA2Ba,MAAM,EAAA,CAAA;sBAAxB,MAAM;gBAOY,WAAW,EAAA,CAAA;sBAA7B,MAAM;gBAQP,KAAK,EAAA,CAAA;sBALJ,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,aAAa,EAAE;;;AAG9B,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA,CAAA;;;AC/UH;AACA,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB;;;;;AAKG;MAkBU,kBAAkB,CAAA;;IAW7B,IACI,QAAQ,CAAC,KAAwB,EAAA;AACnC,QAAA,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,YAAA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACpC,SAAA;KACF;AAED;;AAEG;AACH,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IACD,IAAI,SAAS,CAAC,KAAmB,EAAA;AAC/B,QAAA,IAAI,CAAC,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAChD;;AAqBD,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;KACtE;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC/C;;AAID,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;KACjC;IAKD,WACY,CAAA,WAAyC,EACT,cAA4C,EAAA;QAD5E,IAAW,CAAA,WAAA,GAAX,WAAW,CAA8B;;QA5DrD,IAAO,CAAA,OAAA,GAAY,KAAK,CAAC;QAsBzB,IAAU,CAAA,UAAA,GAAY,KAAK,CAAC;;AAWa,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,YAAY,EAA2B,CAAC;;QAGtF,IAAW,CAAA,WAAA,GAAW,EAAE,CAAC;;AAGzB,QAAA,IAAA,CAAA,EAAE,GAAW,CAAA,oBAAA,EAAuB,YAAY,EAAE,EAAE,CAAC;QAUtD,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;QAcjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAiC,CAAC;AACvE,QAAA,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,iBAAiB,CAAC;KAC3D;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KACpC;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;KACzB;IAED,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,KAAK,CAAC;KAC7C;;AAGD,IAAA,QAAQ,CAAC,KAAqB,EAAA;AAC5B,QAAA,IAAI,KAAK,EAAE;;;AAGT,YAAA,IAAI,KAAK,CAAC,OAAO,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE;AAC/D,gBAAA,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;AACpC,aAAA;;;;YAKD,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,yBAAyB,EAAE;AACjE,gBAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC;gBAC/C,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,OAAO;AACR,aAAA;AAAM,iBAAA;AACL,gBAAA,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;AACxC,aAAA;AACF,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;KAC1B;AAED;;AAEG;AACH,IAAA,MAAM,CAAC,KAAoB,EAAA;;AAEzB,QAAA,IAAI,CAAC,IAAI,CAAC,yBAAyB,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE;AAChF,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;YACtC,KAAK,CAAC,cAAc,EAAE,CAAC;AACxB,SAAA;KACF;;IAGD,KAAK,GAAA;QACH,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,YAAY,EAAE,CAAC;AACrB,SAAA;AACD,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;;AAErB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;AACxB,SAAA;AACD,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KACpC;IAED,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,KAAK,CAAC;AAC5C,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KACpC;;AAGD,IAAA,YAAY,CAAC,KAAqB,EAAA;QAChC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,EAAE;AACvC,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;QAED,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;AACzC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChB,KAAK,EAAE,IAAI,CAAC,YAAY;AACxB,gBAAA,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK;AAC9B,gBAAA,SAAS,EAAE,IAAI;AAChB,aAAA,CAAC,CAAC;YAEH,KAAK,EAAE,cAAc,EAAE,CAAC;AACzB,SAAA;KACF;IAED,QAAQ,GAAA;;AAEN,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KACpC;;AAGD,IAAA,KAAK,CAAC,OAAsB,EAAA;AAC1B,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAClC;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;KACvC;;AAGO,IAAA,eAAe,CAAC,KAAoB,EAAA;QAC1C,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KACrF;AAhLU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,4CAoEnB,gCAAgC,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGApE/B,kBAAkB,EAAA,QAAA,EAAA,wBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,EAAA,UAAA,CAAA,EAAA,SAAA,EAAA,CAAA,uBAAA,EAAA,WAAA,CAAA,EAAA,iBAAA,EAAA,CAAA,+BAAA,EAAA,mBAAA,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,SAAA,EAAA,OAAA,EAAA,UAAA,EAAA,OAAA,EAAA,YAAA,EAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,eAAA,EAAA,kBAAA,EAAA,kBAAA,EAAA,qBAAA,EAAA,mBAAA,EAAA,uEAAA,EAAA,oBAAA,EAAA,yCAAA,EAAA,EAAA,cAAA,EAAA,kCAAA,EAAA,EAAA,QAAA,EAAA,CAAA,cAAA,EAAA,iBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAjB9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wBAAwB;AAClC,oBAAA,QAAQ,EAAE,+BAA+B;AACzC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,kCAAkC;AAC3C,wBAAA,WAAW,EAAE,kBAAkB;AAC/B,wBAAA,SAAS,EAAE,gBAAgB;AAC3B,wBAAA,QAAQ,EAAE,SAAS;AACnB,wBAAA,SAAS,EAAE,UAAU;AACrB,wBAAA,SAAS,EAAE,YAAY;AACvB,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,iBAAiB,EAAE,kBAAkB;AACrC,wBAAA,oBAAoB,EAAE,qBAAqB;AAC3C,wBAAA,qBAAqB,EAAE,uEAAuE;AAC9F,wBAAA,sBAAsB,EAAE,yCAAyC;AAClE,qBAAA;AACF,iBAAA,CAAA;;0BAqEI,MAAM;2BAAC,gCAAgC,CAAA;4CAxDtC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,iBAAiB,CAAA;gBAYpB,SAAS,EAAA,CAAA;sBADZ,KAAK;uBAAC,uBAAuB,CAAA;gBAe9B,iBAAiB,EAAA,CAAA;sBADhB,KAAK;uBAAC,+BAA+B,CAAA;gBAIG,OAAO,EAAA,CAAA;sBAA/C,MAAM;uBAAC,sBAAsB,CAAA;gBAGrB,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAGG,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAIF,QAAQ,EAAA,CAAA;sBADX,KAAK;;;ACnGR,MAAM,iBAAiB,GAAG;IACxB,iBAAiB;IACjB,aAAa;IACb,kBAAkB;IAClB,mBAAmB;IACnB,mBAAmB;IACnB,yBAAyB;CAC1B,CAAC;AAEF;;;AAGG;MAeU,oBAAoB,CAAA;8GAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAApB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,iBA1B/B,iBAAiB;YACjB,aAAa;YACb,kBAAkB;YAClB,mBAAmB;YACnB,mBAAmB;YACnB,yBAAyB,CAAA,EAAA,OAAA,EAAA,CAQf,eAAe,CAAA,EAAA,OAAA,EAAA,CAbzB,iBAAiB;YACjB,aAAa;YACb,kBAAkB;YAClB,mBAAmB;YACnB,mBAAmB;YACnB,yBAAyB,CAAA,EAAA,CAAA,CAAA,EAAA;AAqBd,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,EAVpB,SAAA,EAAA;YACT,iBAAiB;AACjB,YAAA;AACE,gBAAA,OAAO,EAAE,gCAAgC;AACzC,gBAAA,QAAQ,EAAE;oBACR,iBAAiB,EAAE,CAAC,KAAK,CAAC;AACK,iBAAA;AAClC,aAAA;AACF,SAAA,EAAA,OAAA,EAAA,CAXS,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAad,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAdhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,CAAC;AAC1B,oBAAA,OAAO,EAAE,iBAAiB;AAC1B,oBAAA,YAAY,EAAE,iBAAiB;AAC/B,oBAAA,SAAS,EAAE;wBACT,iBAAiB;AACjB,wBAAA;AACE,4BAAA,OAAO,EAAE,gCAAgC;AACzC,4BAAA,QAAQ,EAAE;gCACR,iBAAiB,EAAE,CAAC,KAAK,CAAC;AACK,6BAAA;AAClC,yBAAA;AACF,qBAAA;AACF,iBAAA,CAAA;;;AClDD;;AAEG;;;;"}