{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Displayble from './Displayable.js';\nimport BoundingRect from '../core/BoundingRect.js';\nvar m = [];\nvar IncrementalDisplayable = function (_super) {\n  __extends(IncrementalDisplayable, _super);\n  function IncrementalDisplayable() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.notClear = true;\n    _this.incremental = true;\n    _this._displayables = [];\n    _this._temporaryDisplayables = [];\n    _this._cursor = 0;\n    return _this;\n  }\n  IncrementalDisplayable.prototype.traverse = function (cb, context) {\n    cb.call(context, this);\n  };\n  IncrementalDisplayable.prototype.useStyle = function () {\n    this.style = {};\n  };\n  IncrementalDisplayable.prototype.getCursor = function () {\n    return this._cursor;\n  };\n  IncrementalDisplayable.prototype.innerAfterBrush = function () {\n    this._cursor = this._displayables.length;\n  };\n  IncrementalDisplayable.prototype.clearDisplaybles = function () {\n    this._displayables = [];\n    this._temporaryDisplayables = [];\n    this._cursor = 0;\n    this.markRedraw();\n    this.notClear = false;\n  };\n  IncrementalDisplayable.prototype.clearTemporalDisplayables = function () {\n    this._temporaryDisplayables = [];\n  };\n  IncrementalDisplayable.prototype.addDisplayable = function (displayable, notPersistent) {\n    if (notPersistent) {\n      this._temporaryDisplayables.push(displayable);\n    } else {\n      this._displayables.push(displayable);\n    }\n    this.markRedraw();\n  };\n  IncrementalDisplayable.prototype.addDisplayables = function (displayables, notPersistent) {\n    notPersistent = notPersistent || false;\n    for (var i = 0; i < displayables.length; i++) {\n      this.addDisplayable(displayables[i], notPersistent);\n    }\n  };\n  IncrementalDisplayable.prototype.getDisplayables = function () {\n    return this._displayables;\n  };\n  IncrementalDisplayable.prototype.getTemporalDisplayables = function () {\n    return this._temporaryDisplayables;\n  };\n  IncrementalDisplayable.prototype.eachPendingDisplayable = function (cb) {\n    for (var i = this._cursor; i < this._displayables.length; i++) {\n      cb && cb(this._displayables[i]);\n    }\n    for (var i = 0; i < this._temporaryDisplayables.length; i++) {\n      cb && cb(this._temporaryDisplayables[i]);\n    }\n  };\n  IncrementalDisplayable.prototype.update = function () {\n    this.updateTransform();\n    for (var i = this._cursor; i < this._displayables.length; i++) {\n      var displayable = this._displayables[i];\n      displayable.parent = this;\n      displayable.update();\n      displayable.parent = null;\n    }\n    for (var i = 0; i < this._temporaryDisplayables.length; i++) {\n      var displayable = this._temporaryDisplayables[i];\n      displayable.parent = this;\n      displayable.update();\n      displayable.parent = null;\n    }\n  };\n  IncrementalDisplayable.prototype.getBoundingRect = function () {\n    if (!this._rect) {\n      var rect = new BoundingRect(Infinity, Infinity, -Infinity, -Infinity);\n      for (var i = 0; i < this._displayables.length; i++) {\n        var displayable = this._displayables[i];\n        var childRect = displayable.getBoundingRect().clone();\n        if (displayable.needLocalTransform()) {\n          childRect.applyTransform(displayable.getLocalTransform(m));\n        }\n        rect.union(childRect);\n      }\n      this._rect = rect;\n    }\n    return this._rect;\n  };\n  IncrementalDisplayable.prototype.contain = function (x, y) {\n    var localPos = this.transformCoordToLocal(x, y);\n    var rect = this.getBoundingRect();\n    if (rect.contain(localPos[0], localPos[1])) {\n      for (var i = 0; i < this._displayables.length; i++) {\n        var displayable = this._displayables[i];\n        if (displayable.contain(x, y)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  };\n  return IncrementalDisplayable;\n}(Displayble);\nexport default IncrementalDisplayable;", "map": {"version": 3, "names": ["__extends", "Displayble", "BoundingRect", "m", "IncrementalDisplayable", "_super", "_this", "apply", "arguments", "notClear", "incremental", "_displayables", "_temporaryDisplayables", "_cursor", "prototype", "traverse", "cb", "context", "call", "useStyle", "style", "getCursor", "innerAfterBrush", "length", "clearDisplaybles", "mark<PERSON><PERSON><PERSON>", "clearTemporalDisplayables", "addDisplayable", "displayable", "notPersistent", "push", "addDisplayables", "displayables", "i", "getDisplayables", "getTemporalDisplayables", "eachPendingDisplayable", "update", "updateTransform", "parent", "getBoundingRect", "_rect", "rect", "Infinity", "childRect", "clone", "needLocalTransform", "applyTransform", "getLocalTransform", "union", "contain", "x", "y", "localPos", "transformCoordToLocal"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/zrender/lib/graphic/IncrementalDisplayable.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Displayble from './Displayable.js';\nimport BoundingRect from '../core/BoundingRect.js';\nvar m = [];\nvar IncrementalDisplayable = (function (_super) {\n    __extends(IncrementalDisplayable, _super);\n    function IncrementalDisplayable() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.notClear = true;\n        _this.incremental = true;\n        _this._displayables = [];\n        _this._temporaryDisplayables = [];\n        _this._cursor = 0;\n        return _this;\n    }\n    IncrementalDisplayable.prototype.traverse = function (cb, context) {\n        cb.call(context, this);\n    };\n    IncrementalDisplayable.prototype.useStyle = function () {\n        this.style = {};\n    };\n    IncrementalDisplayable.prototype.getCursor = function () {\n        return this._cursor;\n    };\n    IncrementalDisplayable.prototype.innerAfterBrush = function () {\n        this._cursor = this._displayables.length;\n    };\n    IncrementalDisplayable.prototype.clearDisplaybles = function () {\n        this._displayables = [];\n        this._temporaryDisplayables = [];\n        this._cursor = 0;\n        this.markRedraw();\n        this.notClear = false;\n    };\n    IncrementalDisplayable.prototype.clearTemporalDisplayables = function () {\n        this._temporaryDisplayables = [];\n    };\n    IncrementalDisplayable.prototype.addDisplayable = function (displayable, notPersistent) {\n        if (notPersistent) {\n            this._temporaryDisplayables.push(displayable);\n        }\n        else {\n            this._displayables.push(displayable);\n        }\n        this.markRedraw();\n    };\n    IncrementalDisplayable.prototype.addDisplayables = function (displayables, notPersistent) {\n        notPersistent = notPersistent || false;\n        for (var i = 0; i < displayables.length; i++) {\n            this.addDisplayable(displayables[i], notPersistent);\n        }\n    };\n    IncrementalDisplayable.prototype.getDisplayables = function () {\n        return this._displayables;\n    };\n    IncrementalDisplayable.prototype.getTemporalDisplayables = function () {\n        return this._temporaryDisplayables;\n    };\n    IncrementalDisplayable.prototype.eachPendingDisplayable = function (cb) {\n        for (var i = this._cursor; i < this._displayables.length; i++) {\n            cb && cb(this._displayables[i]);\n        }\n        for (var i = 0; i < this._temporaryDisplayables.length; i++) {\n            cb && cb(this._temporaryDisplayables[i]);\n        }\n    };\n    IncrementalDisplayable.prototype.update = function () {\n        this.updateTransform();\n        for (var i = this._cursor; i < this._displayables.length; i++) {\n            var displayable = this._displayables[i];\n            displayable.parent = this;\n            displayable.update();\n            displayable.parent = null;\n        }\n        for (var i = 0; i < this._temporaryDisplayables.length; i++) {\n            var displayable = this._temporaryDisplayables[i];\n            displayable.parent = this;\n            displayable.update();\n            displayable.parent = null;\n        }\n    };\n    IncrementalDisplayable.prototype.getBoundingRect = function () {\n        if (!this._rect) {\n            var rect = new BoundingRect(Infinity, Infinity, -Infinity, -Infinity);\n            for (var i = 0; i < this._displayables.length; i++) {\n                var displayable = this._displayables[i];\n                var childRect = displayable.getBoundingRect().clone();\n                if (displayable.needLocalTransform()) {\n                    childRect.applyTransform(displayable.getLocalTransform(m));\n                }\n                rect.union(childRect);\n            }\n            this._rect = rect;\n        }\n        return this._rect;\n    };\n    IncrementalDisplayable.prototype.contain = function (x, y) {\n        var localPos = this.transformCoordToLocal(x, y);\n        var rect = this.getBoundingRect();\n        if (rect.contain(localPos[0], localPos[1])) {\n            for (var i = 0; i < this._displayables.length; i++) {\n                var displayable = this._displayables[i];\n                if (displayable.contain(x, y)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    };\n    return IncrementalDisplayable;\n}(Displayble));\nexport default IncrementalDisplayable;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,yBAAyB;AAClD,IAAIC,CAAC,GAAG,EAAE;AACV,IAAIC,sBAAsB,GAAI,UAAUC,MAAM,EAAE;EAC5CL,SAAS,CAACI,sBAAsB,EAAEC,MAAM,CAAC;EACzC,SAASD,sBAAsBA,CAAA,EAAG;IAC9B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,QAAQ,GAAG,IAAI;IACrBH,KAAK,CAACI,WAAW,GAAG,IAAI;IACxBJ,KAAK,CAACK,aAAa,GAAG,EAAE;IACxBL,KAAK,CAACM,sBAAsB,GAAG,EAAE;IACjCN,KAAK,CAACO,OAAO,GAAG,CAAC;IACjB,OAAOP,KAAK;EAChB;EACAF,sBAAsB,CAACU,SAAS,CAACC,QAAQ,GAAG,UAAUC,EAAE,EAAEC,OAAO,EAAE;IAC/DD,EAAE,CAACE,IAAI,CAACD,OAAO,EAAE,IAAI,CAAC;EAC1B,CAAC;EACDb,sBAAsB,CAACU,SAAS,CAACK,QAAQ,GAAG,YAAY;IACpD,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB,CAAC;EACDhB,sBAAsB,CAACU,SAAS,CAACO,SAAS,GAAG,YAAY;IACrD,OAAO,IAAI,CAACR,OAAO;EACvB,CAAC;EACDT,sBAAsB,CAACU,SAAS,CAACQ,eAAe,GAAG,YAAY;IAC3D,IAAI,CAACT,OAAO,GAAG,IAAI,CAACF,aAAa,CAACY,MAAM;EAC5C,CAAC;EACDnB,sBAAsB,CAACU,SAAS,CAACU,gBAAgB,GAAG,YAAY;IAC5D,IAAI,CAACb,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACY,UAAU,CAAC,CAAC;IACjB,IAAI,CAAChB,QAAQ,GAAG,KAAK;EACzB,CAAC;EACDL,sBAAsB,CAACU,SAAS,CAACY,yBAAyB,GAAG,YAAY;IACrE,IAAI,CAACd,sBAAsB,GAAG,EAAE;EACpC,CAAC;EACDR,sBAAsB,CAACU,SAAS,CAACa,cAAc,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAE;IACpF,IAAIA,aAAa,EAAE;MACf,IAAI,CAACjB,sBAAsB,CAACkB,IAAI,CAACF,WAAW,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAACjB,aAAa,CAACmB,IAAI,CAACF,WAAW,CAAC;IACxC;IACA,IAAI,CAACH,UAAU,CAAC,CAAC;EACrB,CAAC;EACDrB,sBAAsB,CAACU,SAAS,CAACiB,eAAe,GAAG,UAAUC,YAAY,EAAEH,aAAa,EAAE;IACtFA,aAAa,GAAGA,aAAa,IAAI,KAAK;IACtC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,CAACT,MAAM,EAAEU,CAAC,EAAE,EAAE;MAC1C,IAAI,CAACN,cAAc,CAACK,YAAY,CAACC,CAAC,CAAC,EAAEJ,aAAa,CAAC;IACvD;EACJ,CAAC;EACDzB,sBAAsB,CAACU,SAAS,CAACoB,eAAe,GAAG,YAAY;IAC3D,OAAO,IAAI,CAACvB,aAAa;EAC7B,CAAC;EACDP,sBAAsB,CAACU,SAAS,CAACqB,uBAAuB,GAAG,YAAY;IACnE,OAAO,IAAI,CAACvB,sBAAsB;EACtC,CAAC;EACDR,sBAAsB,CAACU,SAAS,CAACsB,sBAAsB,GAAG,UAAUpB,EAAE,EAAE;IACpE,KAAK,IAAIiB,CAAC,GAAG,IAAI,CAACpB,OAAO,EAAEoB,CAAC,GAAG,IAAI,CAACtB,aAAa,CAACY,MAAM,EAAEU,CAAC,EAAE,EAAE;MAC3DjB,EAAE,IAAIA,EAAE,CAAC,IAAI,CAACL,aAAa,CAACsB,CAAC,CAAC,CAAC;IACnC;IACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrB,sBAAsB,CAACW,MAAM,EAAEU,CAAC,EAAE,EAAE;MACzDjB,EAAE,IAAIA,EAAE,CAAC,IAAI,CAACJ,sBAAsB,CAACqB,CAAC,CAAC,CAAC;IAC5C;EACJ,CAAC;EACD7B,sBAAsB,CAACU,SAAS,CAACuB,MAAM,GAAG,YAAY;IAClD,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,KAAK,IAAIL,CAAC,GAAG,IAAI,CAACpB,OAAO,EAAEoB,CAAC,GAAG,IAAI,CAACtB,aAAa,CAACY,MAAM,EAAEU,CAAC,EAAE,EAAE;MAC3D,IAAIL,WAAW,GAAG,IAAI,CAACjB,aAAa,CAACsB,CAAC,CAAC;MACvCL,WAAW,CAACW,MAAM,GAAG,IAAI;MACzBX,WAAW,CAACS,MAAM,CAAC,CAAC;MACpBT,WAAW,CAACW,MAAM,GAAG,IAAI;IAC7B;IACA,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrB,sBAAsB,CAACW,MAAM,EAAEU,CAAC,EAAE,EAAE;MACzD,IAAIL,WAAW,GAAG,IAAI,CAAChB,sBAAsB,CAACqB,CAAC,CAAC;MAChDL,WAAW,CAACW,MAAM,GAAG,IAAI;MACzBX,WAAW,CAACS,MAAM,CAAC,CAAC;MACpBT,WAAW,CAACW,MAAM,GAAG,IAAI;IAC7B;EACJ,CAAC;EACDnC,sBAAsB,CAACU,SAAS,CAAC0B,eAAe,GAAG,YAAY;IAC3D,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE;MACb,IAAIC,IAAI,GAAG,IAAIxC,YAAY,CAACyC,QAAQ,EAAEA,QAAQ,EAAE,CAACA,QAAQ,EAAE,CAACA,QAAQ,CAAC;MACrE,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtB,aAAa,CAACY,MAAM,EAAEU,CAAC,EAAE,EAAE;QAChD,IAAIL,WAAW,GAAG,IAAI,CAACjB,aAAa,CAACsB,CAAC,CAAC;QACvC,IAAIW,SAAS,GAAGhB,WAAW,CAACY,eAAe,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC;QACrD,IAAIjB,WAAW,CAACkB,kBAAkB,CAAC,CAAC,EAAE;UAClCF,SAAS,CAACG,cAAc,CAACnB,WAAW,CAACoB,iBAAiB,CAAC7C,CAAC,CAAC,CAAC;QAC9D;QACAuC,IAAI,CAACO,KAAK,CAACL,SAAS,CAAC;MACzB;MACA,IAAI,CAACH,KAAK,GAAGC,IAAI;IACrB;IACA,OAAO,IAAI,CAACD,KAAK;EACrB,CAAC;EACDrC,sBAAsB,CAACU,SAAS,CAACoC,OAAO,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACvD,IAAIC,QAAQ,GAAG,IAAI,CAACC,qBAAqB,CAACH,CAAC,EAAEC,CAAC,CAAC;IAC/C,IAAIV,IAAI,GAAG,IAAI,CAACF,eAAe,CAAC,CAAC;IACjC,IAAIE,IAAI,CAACQ,OAAO,CAACG,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;MACxC,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtB,aAAa,CAACY,MAAM,EAAEU,CAAC,EAAE,EAAE;QAChD,IAAIL,WAAW,GAAG,IAAI,CAACjB,aAAa,CAACsB,CAAC,CAAC;QACvC,IAAIL,WAAW,CAACsB,OAAO,CAACC,CAAC,EAAEC,CAAC,CAAC,EAAE;UAC3B,OAAO,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EACD,OAAOhD,sBAAsB;AACjC,CAAC,CAACH,UAAU,CAAE;AACd,eAAeG,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}