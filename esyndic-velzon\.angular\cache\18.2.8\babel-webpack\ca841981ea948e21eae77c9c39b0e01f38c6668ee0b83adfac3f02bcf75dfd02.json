{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport Model from '../model/Model.js';\nimport env from 'zrender/lib/core/env.js';\n// default import ZH and EN lang\nimport langEN from '../i18n/langEN.js';\nimport langZH from '../i18n/langZH.js';\nimport { isString, clone, merge } from 'zrender/lib/core/util.js';\nvar LOCALE_ZH = 'ZH';\nvar LOCALE_EN = 'EN';\nvar DEFAULT_LOCALE = LOCALE_EN;\nvar localeStorage = {};\nvar localeModels = {};\nexport var SYSTEM_LANG = !env.domSupported ? DEFAULT_LOCALE : function () {\n  var langStr = ( /* eslint-disable-next-line */\n  document.documentElement.lang || navigator.language || navigator.browserLanguage || DEFAULT_LOCALE).toUpperCase();\n  return langStr.indexOf(LOCALE_ZH) > -1 ? LOCALE_ZH : DEFAULT_LOCALE;\n}();\nexport function registerLocale(locale, localeObj) {\n  locale = locale.toUpperCase();\n  localeModels[locale] = new Model(localeObj);\n  localeStorage[locale] = localeObj;\n}\n// export function getLocale(locale: string) {\n//     return localeStorage[locale];\n// }\nexport function createLocaleObject(locale) {\n  if (isString(locale)) {\n    var localeObj = localeStorage[locale.toUpperCase()] || {};\n    if (locale === LOCALE_ZH || locale === LOCALE_EN) {\n      return clone(localeObj);\n    } else {\n      return merge(clone(localeObj), clone(localeStorage[DEFAULT_LOCALE]), false);\n    }\n  } else {\n    return merge(clone(locale), clone(localeStorage[DEFAULT_LOCALE]), false);\n  }\n}\nexport function getLocaleModel(lang) {\n  return localeModels[lang];\n}\nexport function getDefaultLocaleModel() {\n  return localeModels[DEFAULT_LOCALE];\n}\n// Default locale\nregisterLocale(LOCALE_EN, langEN);\nregisterLocale(LOCALE_ZH, langZH);", "map": {"version": 3, "names": ["Model", "env", "langEN", "langZH", "isString", "clone", "merge", "LOCALE_ZH", "LOCALE_EN", "DEFAULT_LOCALE", "localeStorage", "localeModels", "SYSTEM_LANG", "domSupported", "langStr", "document", "documentElement", "lang", "navigator", "language", "browserLanguage", "toUpperCase", "indexOf", "registerLocale", "locale", "localeObj", "createLocaleObject", "getLocaleModel", "getDefaultLocaleModel"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/core/locale.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport Model from '../model/Model.js';\nimport env from 'zrender/lib/core/env.js';\n// default import ZH and EN lang\nimport langEN from '../i18n/langEN.js';\nimport langZH from '../i18n/langZH.js';\nimport { isString, clone, merge } from 'zrender/lib/core/util.js';\nvar LOCALE_ZH = 'ZH';\nvar LOCALE_EN = 'EN';\nvar DEFAULT_LOCALE = LOCALE_EN;\nvar localeStorage = {};\nvar localeModels = {};\nexport var SYSTEM_LANG = !env.domSupported ? DEFAULT_LOCALE : function () {\n  var langStr = ( /* eslint-disable-next-line */\n  document.documentElement.lang || navigator.language || navigator.browserLanguage || DEFAULT_LOCALE).toUpperCase();\n  return langStr.indexOf(LOCALE_ZH) > -1 ? LOCALE_ZH : DEFAULT_LOCALE;\n}();\nexport function registerLocale(locale, localeObj) {\n  locale = locale.toUpperCase();\n  localeModels[locale] = new Model(localeObj);\n  localeStorage[locale] = localeObj;\n}\n// export function getLocale(locale: string) {\n//     return localeStorage[locale];\n// }\nexport function createLocaleObject(locale) {\n  if (isString(locale)) {\n    var localeObj = localeStorage[locale.toUpperCase()] || {};\n    if (locale === LOCALE_ZH || locale === LOCALE_EN) {\n      return clone(localeObj);\n    } else {\n      return merge(clone(localeObj), clone(localeStorage[DEFAULT_LOCALE]), false);\n    }\n  } else {\n    return merge(clone(locale), clone(localeStorage[DEFAULT_LOCALE]), false);\n  }\n}\nexport function getLocaleModel(lang) {\n  return localeModels[lang];\n}\nexport function getDefaultLocaleModel() {\n  return localeModels[DEFAULT_LOCALE];\n}\n// Default locale\nregisterLocale(LOCALE_EN, langEN);\nregisterLocale(LOCALE_ZH, langZH);"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,OAAOC,GAAG,MAAM,yBAAyB;AACzC;AACA,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,QAAQ,0BAA0B;AACjE,IAAIC,SAAS,GAAG,IAAI;AACpB,IAAIC,SAAS,GAAG,IAAI;AACpB,IAAIC,cAAc,GAAGD,SAAS;AAC9B,IAAIE,aAAa,GAAG,CAAC,CAAC;AACtB,IAAIC,YAAY,GAAG,CAAC,CAAC;AACrB,OAAO,IAAIC,WAAW,GAAG,CAACX,GAAG,CAACY,YAAY,GAAGJ,cAAc,GAAG,YAAY;EACxE,IAAIK,OAAO,GAAG,EAAE;EAChBC,QAAQ,CAACC,eAAe,CAACC,IAAI,IAAIC,SAAS,CAACC,QAAQ,IAAID,SAAS,CAACE,eAAe,IAAIX,cAAc,EAAEY,WAAW,CAAC,CAAC;EACjH,OAAOP,OAAO,CAACQ,OAAO,CAACf,SAAS,CAAC,GAAG,CAAC,CAAC,GAAGA,SAAS,GAAGE,cAAc;AACrE,CAAC,CAAC,CAAC;AACH,OAAO,SAASc,cAAcA,CAACC,MAAM,EAAEC,SAAS,EAAE;EAChDD,MAAM,GAAGA,MAAM,CAACH,WAAW,CAAC,CAAC;EAC7BV,YAAY,CAACa,MAAM,CAAC,GAAG,IAAIxB,KAAK,CAACyB,SAAS,CAAC;EAC3Cf,aAAa,CAACc,MAAM,CAAC,GAAGC,SAAS;AACnC;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACF,MAAM,EAAE;EACzC,IAAIpB,QAAQ,CAACoB,MAAM,CAAC,EAAE;IACpB,IAAIC,SAAS,GAAGf,aAAa,CAACc,MAAM,CAACH,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACzD,IAAIG,MAAM,KAAKjB,SAAS,IAAIiB,MAAM,KAAKhB,SAAS,EAAE;MAChD,OAAOH,KAAK,CAACoB,SAAS,CAAC;IACzB,CAAC,MAAM;MACL,OAAOnB,KAAK,CAACD,KAAK,CAACoB,SAAS,CAAC,EAAEpB,KAAK,CAACK,aAAa,CAACD,cAAc,CAAC,CAAC,EAAE,KAAK,CAAC;IAC7E;EACF,CAAC,MAAM;IACL,OAAOH,KAAK,CAACD,KAAK,CAACmB,MAAM,CAAC,EAAEnB,KAAK,CAACK,aAAa,CAACD,cAAc,CAAC,CAAC,EAAE,KAAK,CAAC;EAC1E;AACF;AACA,OAAO,SAASkB,cAAcA,CAACV,IAAI,EAAE;EACnC,OAAON,YAAY,CAACM,IAAI,CAAC;AAC3B;AACA,OAAO,SAASW,qBAAqBA,CAAA,EAAG;EACtC,OAAOjB,YAAY,CAACF,cAAc,CAAC;AACrC;AACA;AACAc,cAAc,CAACf,SAAS,EAAEN,MAAM,CAAC;AACjCqB,cAAc,CAAChB,SAAS,EAAEJ,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}