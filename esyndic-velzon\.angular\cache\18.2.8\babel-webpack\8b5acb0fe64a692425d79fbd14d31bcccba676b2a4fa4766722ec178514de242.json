{"ast": null, "code": "import toNumber from './toNumber.js';\n\n/**\n * Creates a function that performs a relational operation on two values.\n *\n * @private\n * @param {Function} operator The function to perform the operation.\n * @returns {Function} Returns the new relational operation function.\n */\nfunction createRelationalOperation(operator) {\n  return function (value, other) {\n    if (!(typeof value == 'string' && typeof other == 'string')) {\n      value = toNumber(value);\n      other = toNumber(other);\n    }\n    return operator(value, other);\n  };\n}\nexport default createRelationalOperation;", "map": {"version": 3, "names": ["toNumber", "createRelationalOperation", "operator", "value", "other"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_createRelationalOperation.js"], "sourcesContent": ["import toNumber from './toNumber.js';\n\n/**\n * Creates a function that performs a relational operation on two values.\n *\n * @private\n * @param {Function} operator The function to perform the operation.\n * @returns {Function} Returns the new relational operation function.\n */\nfunction createRelationalOperation(operator) {\n  return function(value, other) {\n    if (!(typeof value == 'string' && typeof other == 'string')) {\n      value = toNumber(value);\n      other = toNumber(other);\n    }\n    return operator(value, other);\n  };\n}\n\nexport default createRelationalOperation;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,yBAAyBA,CAACC,QAAQ,EAAE;EAC3C,OAAO,UAASC,KAAK,EAAEC,KAAK,EAAE;IAC5B,IAAI,EAAE,OAAOD,KAAK,IAAI,QAAQ,IAAI,OAAOC,KAAK,IAAI,QAAQ,CAAC,EAAE;MAC3DD,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAAC;MACvBC,KAAK,GAAGJ,QAAQ,CAACI,KAAK,CAAC;IACzB;IACA,OAAOF,QAAQ,CAACC,KAAK,EAAEC,KAAK,CAAC;EAC/B,CAAC;AACH;AAEA,eAAeH,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}