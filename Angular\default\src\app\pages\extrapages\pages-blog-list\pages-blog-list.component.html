<!-- Start Breadcrumbs -->
<app-breadcrumbs title="List View" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->
<div class="row">
    <div class="col-xxl-3">
        <div class="card">
            <div class="card-body p-4">
                <div class="search-box">
                    <p class="text-muted">Search</p>
                    <div class="position-relative">
                        <input type="text" class="form-control rounded bg-light border-light" placeholder="Search...">
                        <i class="mdi mdi-magnify search-icon"></i>
                    </div>
                </div>

                <div class="mt-4 pt-4 border-top border-dashed border-bottom-0 border-start-0 border-end-0">
                    <p class="text-muted">Categories</p>

                    <ul class="list-unstyled fw-medium">
                        <li><a href="javascript: void(0);" class="text-muted py-2 d-block"><i
                                    class="mdi mdi-chevron-right me-1"></i> Art & Design</a></li>
                        <li><a href="javascript: void(0);" class="text-muted py-2 d-block"><i
                                    class="mdi mdi-chevron-right me-1"></i> Inspiration & Innovation <span
                                    class="badge badge-soft-success rounded-pill float-end ms-1 font-size-12">04</span></a>
                        </li>
                        <li><a href="javascript: void(0);" class="text-muted py-2 d-block"><i
                                    class="mdi mdi-chevron-right me-1"></i> Business</a></li>
                        <li><a href="javascript: void(0);" class="text-muted py-2 d-block"><i
                                    class="mdi mdi-chevron-right me-1"></i> Project</a></li>
                        <li><a href="javascript: void(0);" class="text-muted py-2 d-block"><i
                                    class="mdi mdi-chevron-right me-1"></i> Lifestyle</a></li>
                        <li><a href="javascript: void(0);" class="text-muted py-2 d-block"><i
                                    class="mdi mdi-chevron-right me-1"></i> Design Resources & Tools</a></li>
                        <li><a href="javascript: void(0);" class="text-muted py-2 d-block"><i
                                    class="mdi mdi-chevron-right me-1"></i> Travel<span
                                    class="badge badge-soft-success rounded-pill ms-1 float-end font-size-12">12</span></a>
                        </li>
                    </ul>
                </div>

                <div class="mt-4 pt-4 border-top border-dashed border-bottom-0 border-start-0 border-end-0">
                    <p class="text-muted">Archive</p>

                    <ul class="list-unstyled fw-medium">
                        <li><a href="javascript: void(0);" class="text-muted py-2 d-block"><i
                                    class="mdi mdi-chevron-right me-1"></i> 2024 <span
                                    class="badge badge-soft-success rounded-pill float-end ms-1 font-size-12">03</span></a>
                        </li>
                        <li><a href="javascript: void(0);" class="text-muted py-2 d-block"><i
                                    class="mdi mdi-chevron-right me-1"></i> 2023 <span
                                    class="badge badge-soft-success rounded-pill float-end ms-1 font-size-12">06</span></a>
                        </li>
                        <li><a href="javascript: void(0);" class="text-muted py-2 d-block"><i
                                    class="mdi mdi-chevron-right me-1"></i> 2022 <span
                                    class="badge badge-soft-success rounded-pill float-end ms-1 font-size-12">05</span></a>
                        </li>
                        <li><a href="javascript: void(0);" class="text-muted py-2 d-block"><i
                                    class="mdi mdi-chevron-right me-1"></i> 2021 <span
                                    class="badge badge-soft-success rounded-pill float-end ms-1 font-size-12">05</span></a>
                        </li>
                        <li><a href="javascript: void(0);" class="text-muted py-2 d-block"><i
                                    class="mdi mdi-chevron-right me-1"></i> 2020 <span
                                    class="badge badge-soft-success rounded-pill float-end ms-1 font-size-12">05</span></a>
                        </li>
                    </ul>
                </div>

                <div class="mt-4 pt-4 border-top border-dashed border-bottom-0 border-start-0 border-end-0">
                    <p class="text-muted mb-2">Popular Posts</p>

                    <div class="list-group list-group-flush">

                        <a href="javascript: void(0);" class="list-group-item text-muted py-3 px-2">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-3">
                                    <img src="assets/images/small/img-7.jpg" alt=""
                                        class="avatar-md h-auto d-block rounded">
                                </div>
                                <div class="flex-grow-1 overflow-hidden">
                                    <h5 class="fs-15 text-truncate">Beautiful Day with Friends</h5>
                                    <p class="mb-0 text-truncate">10 Apr, 2024</p>
                                </div>
                            </div>
                        </a>

                        <a href="javascript: void(0);" class="list-group-item text-muted py-3 px-2">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-3">
                                    <img src="assets/images/small/img-4.jpg" alt=""
                                        class="avatar-md h-auto d-block rounded">
                                </div>
                                <div class="flex-grow-1 overflow-hidden">
                                    <h5 class="fs-15 text-truncate">Drawing a sketch</h5>
                                    <p class="mb-0 text-truncate">24 Mar, 2024</p>
                                </div>
                            </div>
                        </a>

                        <a href="javascript: void(0);" class="list-group-item text-muted py-3 px-2">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-3">
                                    <img src="assets/images/small/img-6.jpg" alt=""
                                        class="avatar-md h-auto d-block rounded">
                                </div>
                                <div class="flex-grow-1 overflow-hidden">
                                    <h5 class="fs-15 text-truncate">Project discussion with team</h5>
                                    <p class="mb-0 text-truncate">11 Mar, 2024</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>

                <div class="mt-4 pt-4 border-top border-dashed border-bottom-0 border-start-0 border-end-0">
                    <p class="text-muted">Tags</p>

                    <div class="d-flex flex-wrap gap-2 widget-tag">
                        <div><a href="javascript: void(0);" class="badge bg-light text-muted font-size-12">Design</a>
                        </div>
                        <div><a href="javascript: void(0);"
                                class="badge bg-light text-muted font-size-12">Development</a></div>
                        <div><a href="javascript: void(0);" class="badge bg-light text-muted font-size-12">Business</a>
                        </div>
                        <div><a href="javascript: void(0);" class="badge bg-light text-muted font-size-12">Project</a>
                        </div>
                        <div><a href="javascript: void(0);" class="badge bg-light text-muted font-size-12">Travel</a>
                        </div>
                        <div><a href="javascript: void(0);" class="badge bg-light text-muted font-size-12">Lifestyle</a>
                        </div>
                        <div><a href="javascript: void(0);"
                                class="badge bg-light text-muted font-size-12">Photography</a></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xxl-9">
        <div class="row g-4 mb-3">
            <div class="col-sm-auto">
                <div>
                    <a href="apps-projects-create.html" class="btn btn-success"><i
                            class="ri-add-line align-bottom me-1"></i> Add New</a>
                </div>
            </div>
            <div class="col-sm">
                <div class="d-flex justify-content-sm-end gap-2">
                    <div class="search-box ms-2">
                        <input type="text" class="form-control" placeholder="Search...">
                        <i class="ri-search-line search-icon"></i>
                    </div>

                    <select class="form-control w-md" data-choices data-choices-search-false>
                        <option value="All">All</option>
                        <option value="Today">Today</option>
                        <option value="Yesterday" selected>Yesterday</option>
                        <option value="Last 7 Days">Last 7 Days</option>
                        <option value="Last 30 Days">Last 30 Days</option>
                        <option value="This Month">This Month</option>
                        <option value="Last Year">Last Year</option>
                    </select>
                </div>
            </div>
        </div><!--end row-->
        <div class="row gx-4">
            <div class="col-xxl-12" *ngFor="let blog of listview">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-xxl-3 col-lg-5">
                                <img [src]="blog.image" alt="" class="img-fluid rounded w-100 object-fit-cover">
                            </div><!--end col-->
                            <div class="col-xxl-9 col-lg-7">
                                <p class="mb-2 text-primary text-uppercase">{{ blog.author }}</p>
                                <a>
                                    <h5 class="fs-15 fw-semibold">{{ blog.title }}</h5>
                                </a>
                                <div class="d-flex align-items-center gap-2 mb-3 flex-wrap">
                                    <span class="text-muted"><i class="ri-calendar-event-line me-1"></i> {{ blog.date |
                                        date }}</span> |
                                    <span class="text-muted"><i class="ri-eye-line me-1"></i> {{ blog.views }}</span> |
                                    <a [routerLink]="['pages/pages-blog-overview']"><i class="ri-user-3-line me-1"></i>
                                        {{ blog.author }}</a>
                                </div>
                                <p class="text-muted mb-2">{{ blog.description }}</p>
                                <a class="text-decoration-underline">Read more <i class="ri-arrow-right-line"></i></a>
                                <div class="d-flex align-items-center gap-2 mt-3 flex-wrap">
                                    <span *ngFor="let tag of blog.tags" class="badge text-success bg-success-subtle">#{{
                                        tag }}</span>
                                </div>
                            </div><!--end col-->
                        </div><!--end row-->
                    </div>
                </div>
            </div>
        </div><!--end row-->
        <ngb-pagination class="d-flex justify-content-end pt-2" [collectionSize]="alllistview?.length"
            [(page)]="service.page" (pageChange)="changePage()" [pageSize]="service.pageSize"
            aria-label="Custom pagination">
            <ng-template ngbPaginationPrevious let-page let-pages="pages">
                <i class="ci-arrow-left me-2"></i>
                Prev
            </ng-template>
            <ng-template ngbPaginationNext>
                Next
                <i class="ci-arrow-right ms-2"></i>
            </ng-template>
        </ngb-pagination>
    </div><!--end col-->
</div><!--end row-->