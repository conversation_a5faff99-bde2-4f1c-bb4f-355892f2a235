{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// TODO Parse shadow style\n// TODO Only shallow path support\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function makeStyleMapper(properties, ignoreParent) {\n  // Normalize\n  for (var i = 0; i < properties.length; i++) {\n    if (!properties[i][1]) {\n      properties[i][1] = properties[i][0];\n    }\n  }\n  ignoreParent = ignoreParent || false;\n  return function (model, excludes, includes) {\n    var style = {};\n    for (var i = 0; i < properties.length; i++) {\n      var propName = properties[i][1];\n      if (excludes && zrUtil.indexOf(excludes, propName) >= 0 || includes && zrUtil.indexOf(includes, propName) < 0) {\n        continue;\n      }\n      var val = model.getShallow(propName, ignoreParent);\n      if (val != null) {\n        style[properties[i][0]] = val;\n      }\n    }\n    // TODO Text or image?\n    return style;\n  };\n}", "map": {"version": 3, "names": ["zrUtil", "makeStyleMapper", "properties", "ignoreParent", "i", "length", "model", "excludes", "includes", "style", "propName", "indexOf", "val", "getShallow"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/model/mixin/makeStyleMapper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// TODO Parse shadow style\n// TODO Only shallow path support\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function makeStyleMapper(properties, ignoreParent) {\n  // Normalize\n  for (var i = 0; i < properties.length; i++) {\n    if (!properties[i][1]) {\n      properties[i][1] = properties[i][0];\n    }\n  }\n  ignoreParent = ignoreParent || false;\n  return function (model, excludes, includes) {\n    var style = {};\n    for (var i = 0; i < properties.length; i++) {\n      var propName = properties[i][1];\n      if (excludes && zrUtil.indexOf(excludes, propName) >= 0 || includes && zrUtil.indexOf(includes, propName) < 0) {\n        continue;\n      }\n      var val = model.getShallow(propName, ignoreParent);\n      if (val != null) {\n        style[properties[i][0]] = val;\n      }\n    }\n    // TODO Text or image?\n    return style;\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,eAAe,SAASC,eAAeA,CAACC,UAAU,EAAEC,YAAY,EAAE;EAChE;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,IAAI,CAACF,UAAU,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrBF,UAAU,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,UAAU,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC;EACF;EACAD,YAAY,GAAGA,YAAY,IAAI,KAAK;EACpC,OAAO,UAAUG,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IAC1C,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1C,IAAIM,QAAQ,GAAGR,UAAU,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,IAAIG,QAAQ,IAAIP,MAAM,CAACW,OAAO,CAACJ,QAAQ,EAAEG,QAAQ,CAAC,IAAI,CAAC,IAAIF,QAAQ,IAAIR,MAAM,CAACW,OAAO,CAACH,QAAQ,EAAEE,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC7G;MACF;MACA,IAAIE,GAAG,GAAGN,KAAK,CAACO,UAAU,CAACH,QAAQ,EAAEP,YAAY,CAAC;MAClD,IAAIS,GAAG,IAAI,IAAI,EAAE;QACfH,KAAK,CAACP,UAAU,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGQ,GAAG;MAC/B;IACF;IACA;IACA,OAAOH,KAAK;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}