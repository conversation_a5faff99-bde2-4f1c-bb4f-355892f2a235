<div class="profile-foreground position-relative mx-n4 mt-n4">
    <div class="profile-wid-bg">
        <img src="assets/images/profile-bg.jpg" alt="" class="profile-wid-img" />
    </div>
</div>
<div class="pt-4 mb-4 mb-lg-3 pb-lg-4 profile-wrapper">
    <div class="row g-4">
        <div class="col-auto">
            <div class="avatar-lg">
                <img src="assets/images/users/avatar-1.jpg" alt="user-img" class="img-thumbnail rounded-circle" />
            </div>
        </div><!--end col-->
        <div class="col">
            <div class="p-2">
                <h3 class="text-white mb-1">{{userData.first_name}} {{userData.last_name}}</h3>
                <p class="text-white text-opacity-75">{{userData.role
                    == 0 ? 'Admin':'User'}}</p>
                <div class="hstack text-white-50 gap-1">
                    <div class="me-2"><i class="ri-map-pin-user-line mx-1 text-white text-opacity-75 fs-16 align-middle"></i>{{userData.city}},
                        {{userData.country}}</div>
                    <div><i class="ri-building-line mx-1 text-white text-opacity-75 fs-16 align-middle"></i>{{userData.company_name}}
                    </div>
                </div>
            </div>
        </div><!--end col-->
        <div class="col-12 col-lg-auto order-last order-lg-0">
            <div class="row text text-white-50 text-center">
                <div class="col-lg-6 col-4">
                    <div class="p-2">
                        <h4 class="text-white mb-1">24.3K</h4>
                        <p class="fs-14 mb-0">Followers</p>
                    </div>
                </div>
                <div class="col-lg-6 col-4">
                    <div class="p-2">
                        <h4 class="text-white mb-1">1.3K</h4>
                        <p class="fs-14 mb-0">Following</p>
                    </div>
                </div>
            </div>
        </div><!--end col-->

    </div><!--end row-->
</div>

<div class="row">
    <div class="col-lg-12">
        <div>
            <div class="d-flex profile-wrapper">
                <!-- Nav tabs -->
                <ul ngbNav #customNav="ngbNav" [activeId]="1" class="nav nav-pills animation-nav profile-nav gap-2 gap-lg-3 flex-grow-1" role="tablist">
                    <li [ngbNavItem]="1" class="nav-item">
                        <a ngbNavLink class="nav-link" data-bs-toggle="tab" role="tab">
                            <i class="ri-airplay-fill d-inline-block d-md-none"></i> <span class="d-none d-md-inline-block">Overview</span>
                        </a>
                        <ng-template ngbNavContent>
                            <div class="row">
                                <div class="col-xxl-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title mb-5">Complete Your Profile</h5>
                                            <div class="progress animated-progess custom-progress progress-label">
                                                <div class="progress-bar bg-danger" role="progressbar" style="width: 30%" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100">
                                                    <div class="label">30%</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title mb-3">Info</h5>
                                            <div class="table-responsive">
                                                <table class="table table-borderless mb-0">
                                                    <tbody>
                                                        <tr>
                                                            <th class="ps-0" scope="row">Full Name :</th>
                                                            <td class="text-muted">{{userData.first_name}}
                                                                {{userData.last_name}}</td>
                                                        </tr>
                                                        <tr>
                                                            <th class="ps-0" scope="row">Mobile :</th>
                                                            <td class="text-muted">{{userData.phone}}</td>
                                                        </tr>
                                                        <tr>
                                                            <th class="ps-0" scope="row">E-mail :</th>
                                                            <td class="text-muted">{{userData.email}}</td>
                                                        </tr>
                                                        <tr>
                                                            <th class="ps-0" scope="row">Location :</th>
                                                            <td class="text-muted">{{userData.city}},
                                                                {{userData.country}}</td>
                                                        </tr>
                                                        <tr>
                                                            <th class="ps-0" scope="row">Joining Date</th>
                                                            <td class="text-muted">{{userData.joining_date | date
                                                                :'longDate'}}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div><!-- end card body -->
                                    </div><!-- end card -->

                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title mb-4">Portfolio</h5>
                                            <div class="d-flex flex-wrap gap-2">
                                                @for ( data of userData.portfolio; track $index) {
                                                <div>
                                                    <a href="javascript:void(0);" class="avatar-xs d-block">
                                                        <span class="avatar-title rounded-circle fs-16 bg-{{data.bg_color}} text-body">
                                                            <i class="ri-{{data.logo}}-fill"></i>
                                                        </span>
                                                    </a>
                                                </div>
                                                }
                                            </div>

                                        </div><!-- end card body -->
                                    </div><!-- end card -->

                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title mb-4">Skills</h5>
                                            <div class="d-flex flex-wrap gap-2 fs-15">
                                                @for ( skill of userData.skills; track $index) {
                                                <a href="javascript:void(0);" class="badge bg-primary-subtle text-primary">{{skill}}</a> }
                                            </div>
                                        </div><!-- end card body -->
                                    </div><!-- end card -->

                                    <div class="card">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-4">
                                                <div class="flex-grow-1">
                                                    <h5 class="card-title mb-0">Suggestions</h5>
                                                </div>
                                                <div class="flex-shrink-0">
                                                    <div class="dropdown" ngbDropdown>
                                                        <a href="javascript:void(0);" class="arrow-none" role="button" id="dropdownMenuLink2" data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle>
                                                            <i class="ri-more-2-fill fs-14"></i>
                                                        </a>

                                                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink2" ngbDropdownMenu>
                                                            <li><a class="dropdown-item" href="javascript:void(0);">View</a></li>
                                                            <li><a class="dropdown-item" href="javascript:void(0);">Edit</a></li>
                                                            <li><a class="dropdown-item" href="javascript:void(0);">Delete</a></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="d-flex align-items-center py-3">
                                                    <div class="avatar-xs flex-shrink-0 me-3">
                                                        <img src="assets/images/users/avatar-3.jpg" alt="" class="img-fluid rounded-circle" />
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <div>
                                                            <h5 class="fs-14 mb-1">Esther James</h5>
                                                            <p class="fs-13 text-muted mb-0">Frontend Developer</p>
                                                        </div>
                                                    </div>
                                                    <div class="flex-shrink-0 ms-2">
                                                        <button type="button" class="btn btn-sm btn-outline-success"><i class="ri-user-add-line align-middle"></i></button>
                                                    </div>
                                                </div>
                                                <div class="d-flex align-items-center py-3">
                                                    <div class="avatar-xs flex-shrink-0 me-3">
                                                        <img src="assets/images/users/avatar-4.jpg" alt="" class="img-fluid rounded-circle" />
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <div>
                                                            <h5 class="fs-14 mb-1">Jacqueline Steve</h5>
                                                            <p class="fs-13 text-muted mb-0">UI/UX Designer</p>
                                                        </div>
                                                    </div>
                                                    <div class="flex-shrink-0 ms-2">
                                                        <button type="button" class="btn btn-sm btn-outline-success"><i class="ri-user-add-line align-middle"></i></button>
                                                    </div>
                                                </div>
                                                <div class="d-flex align-items-center py-3">
                                                    <div class="avatar-xs flex-shrink-0 me-3">
                                                        <img src="assets/images/users/avatar-5.jpg" alt="" class="img-fluid rounded-circle" />
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <div>
                                                            <h5 class="fs-14 mb-1">George Whalen</h5>
                                                            <p class="fs-13 text-muted mb-0">Backend Developer</p>
                                                        </div>
                                                    </div>
                                                    <div class="flex-shrink-0 ms-2">
                                                        <button type="button" class="btn btn-sm btn-outline-success"><i class="ri-user-add-line align-middle"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div><!-- end card body -->
                                    </div><!--end card-->

                                    <div class="card">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-4">
                                                <div class="flex-grow-1">
                                                    <h5 class="card-title mb-0">Popular Posts</h5>
                                                </div>
                                                <div class="flex-shrink-0">
                                                    <div class="dropdown" ngbDropdown>
                                                        <a href="javascript:void(0);" class="arrow-none" role="button" id="dropdownMenuLink1" data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle>
                                                            <i class="ri-more-2-fill fs-14"></i>
                                                        </a>

                                                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink1" ngbDropdownMenu>
                                                            <li><a class="dropdown-item" href="javascript:void(0);">View</a></li>
                                                            <li><a class="dropdown-item" href="javascript:void(0);">Edit</a></li>
                                                            <li><a class="dropdown-item" href="javascript:void(0);">Delete</a></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex mb-4">
                                                <div class="flex-shrink-0">
                                                    <img src="assets/images/small/img-4.jpg" alt="" height="50" class="rounded" />
                                                </div>
                                                <div class="flex-grow-1 ms-3 overflow-hidden">
                                                    <a href="javascript:void(0);">
                                                        <h6 class="text-truncate fs-14">Design your apps in your own way
                                                        </h6>
                                                    </a>
                                                    <p class="text-muted mb-0">15 Dec 2021</p>
                                                </div>
                                            </div>
                                            <div class="d-flex mb-4">
                                                <div class="flex-shrink-0">
                                                    <img src="assets/images/small/img-5.jpg" alt="" height="50" class="rounded" />
                                                </div>
                                                <div class="flex-grow-1 ms-3 overflow-hidden">
                                                    <a href="javascript:void(0);">
                                                        <h6 class="text-truncate fs-14">Smartest Applications for
                                                            Business</h6>
                                                    </a>
                                                    <p class="text-muted mb-0">28 Nov 2021</p>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="flex-shrink-0">
                                                    <img src="assets/images/small/img-6.jpg" alt="" height="50" class="rounded" />
                                                </div>
                                                <div class="flex-grow-1 ms-3 overflow-hidden">
                                                    <a href="javascript:void(0);">
                                                        <h6 class="text-truncate fs-14">How to get creative in your work
                                                        </h6>
                                                    </a>
                                                    <p class="text-muted mb-0">21 Nov 2021</p>
                                                </div>
                                            </div>
                                        </div><!--end card-body-->
                                    </div><!--end card-->
                                </div><!--end col-->
                                <div class="col-xxl-9">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title mb-3">About</h5>
                                            <p>{{userData.Description}}</p>
                                            <div class="row">
                                                <div class="col-6 col-md-4">
                                                    <div class="d-flex mt-4">
                                                        <div class="flex-shrink-0 avatar-xs align-self-center me-3">
                                                            <div class="avatar-title bg-light rounded-circle fs-16 text-primary">
                                                                <i class="ri-user-2-fill"></i>
                                                            </div>
                                                        </div>
                                                        <div class="flex-grow-1 overflow-hidden">
                                                            <p class="mb-1">Designation :</p>
                                                            <h6 class="text-truncate mb-0">{{userData.designation}}</h6>
                                                        </div>
                                                    </div>
                                                </div><!--end col-->
                                                <div class="col-6 col-md-4">
                                                    <div class="d-flex mt-4">
                                                        <div class="flex-shrink-0 avatar-xs align-self-center me-3">
                                                            <div class="avatar-title bg-light rounded-circle fs-16 text-primary">
                                                                <i class="ri-global-line"></i>
                                                            </div>
                                                        </div>
                                                        <div class="flex-grow-1 overflow-hidden">
                                                            <p class="mb-1">Website :</p>
                                                            <a href="javascript:void(0);" class="fw-semibold">{{userData.website}}</a>
                                                        </div>
                                                    </div>
                                                </div><!--end col-->
                                            </div><!--end row-->
                                        </div><!--end card-body-->
                                    </div><!-- end card -->

                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="card">
                                                <div class="card-header align-items-center d-flex">
                                                    <h4 class="card-title mb-0  me-2">Recent Activity</h4>
                                                    <div class="flex-shrink-0 ms-auto">
                                                        <ul ngbNav #customNav="ngbNav" [activeId]="1" class="nav justify-content-end nav-tabs-custom rounded card-header-tabs border-bottom-0" role="tablist">
                                                            <li [ngbNavItem]="1" class="nav-item">
                                                                <a ngbNavLink class="nav-link" data-bs-toggle="tab" role="tab">
                                                                    Today
                                                                </a>
                                                                <ng-template ngbNavContent>
                                                                    <div class="profile-timeline order-status">
                                                                        <div ngbAccordion activeIds="static-1">
                                                                            <div ngbAccordionItem [collapsed]="false" id="static-1">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="true" aria-controls="collapseOne">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0">
                                                                                                <img src="assets/images/users/avatar-2.jpg" alt="" class="avatar-xs rounded-circle" />
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    Jacqueline Steve
                                                                                                </h6>
                                                                                                <small class="text-muted">We
                                                                                                    has changed 2
                                                                                                    attributes on
                                                                                                    05:16PM</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody class="ps-5">
                                                                                        <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
                                                                                            In an awareness campaign, it
                                                                                            is vital for people to begin
                                                                                            put 2 and 2 together and
                                                                                            begin to recognize your
                                                                                            cause. Too much or too
                                                                                            little spacing, as in the
                                                                                            example below, can make
                                                                                            things unpleasant for the
                                                                                            reader. The goal is to make
                                                                                            your text as comfortable to
                                                                                            read as possible. A
                                                                                            wonderful serenity has taken
                                                                                            possession of my entire
                                                                                            soul, like these sweet
                                                                                            mornings of spring which I
                                                                                            enjoy with my whole heart.
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div ngbAccordionItem [collapsed]="false" id="static-2">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="false" aria-controls="collapseTwo">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0 avatar-xs">
                                                                                                <div class="avatar-title bg-light text-success rounded-circle">
                                                                                                    M
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    Megan Elmore</h6>
                                                                                                <small class="text-muted">Adding
                                                                                                    a new event with
                                                                                                    attachments -
                                                                                                    04:45PM</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody class="accordion-body ps-5">
                                                                                        <div class="row g-2">
                                                                                            <div class="col-auto">
                                                                                                <div class="d-flex border border-dashed p-2 rounded position-relative">
                                                                                                    <div class="flex-shrink-0">
                                                                                                        <i class="ri-image-2-line fs-17 text-danger"></i>
                                                                                                    </div>
                                                                                                    <div class="flex-grow-1 ms-2">
                                                                                                        <h6><a href="javascript:void(0);" class="stretched-link">Business
                                                                                                                Template
                                                                                                                - UI/UX
                                                                                                                design</a>
                                                                                                        </h6>
                                                                                                        <small>685
                                                                                                            KB</small>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-auto">
                                                                                                <div class="d-flex border border-dashed p-2 rounded position-relative">
                                                                                                    <div class="flex-shrink-0">
                                                                                                        <i class="ri-file-zip-line fs-17 text-info"></i>
                                                                                                    </div>
                                                                                                    <div class="flex-grow-1 ms-2">
                                                                                                        <h6><a href="javascript:void(0);" class="stretched-link">Bank
                                                                                                                Management
                                                                                                                System -
                                                                                                                PSD</a>
                                                                                                        </h6>
                                                                                                        <small>8.78
                                                                                                            MB</small>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div ngbAccordionItem [collapsed]="true" [disabled]="true" id="static-3">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="false" aria-controls="collapseThree">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0">
                                                                                                <img src="assets/images/users/avatar-5.jpg" alt="" class="avatar-xs rounded-circle" />
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    New ticket received
                                                                                                </h6>
                                                                                                <small class="text-muted mb-2">User
                                                                                                    <span class="text-secondary">Erica245</span>
                                                                                                    submitted a ticket -
                                                                                                    02:33PM</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody></div>
                                                                                </div>
                                                                            </div>
                                                                            <div ngbAccordionItem [collapsed]="false" id="static-4">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="false">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0 avatar-xs">
                                                                                                <div class="avatar-title bg-light text-muted rounded-circle">
                                                                                                    <i class="ri-user-3-fill"></i>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    Nancy Martino</h6>
                                                                                                <small class="text-muted">Commented
                                                                                                    on 12:57PM</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody class="accordion-body ps-5">
                                                                                        " A wonderful serenity has taken
                                                                                        possession of my entire soul,
                                                                                        like these sweet mornings of
                                                                                        spring which I enjoy with my
                                                                                        whole heart. Each design is a
                                                                                        new, unique piece of art birthed
                                                                                        into this world, and while you
                                                                                        have the opportunity to be
                                                                                        creative and make your own style
                                                                                        choices. "
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div ngbAccordionItem [collapsed]="false" id="static-5">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="false">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0">
                                                                                                <img src="assets/images/users/avatar-7.jpg" alt="" class="avatar-xs rounded-circle" />
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    Lewis Arnold</h6>
                                                                                                <small class="text-muted">Create
                                                                                                    new project buildng
                                                                                                    product -
                                                                                                    10:05AM</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody class="accordion-body">
                                                                                        <p class="text-muted mb-2">Every
                                                                                            team project can have a
                                                                                            velzon. Use the velzon to
                                                                                            share information with your
                                                                                            team to understand and
                                                                                            contribute to your project.
                                                                                        </p>
                                                                                        <div class="avatar-group">
                                                                                            <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Christi" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="" data-bs-original-title="Christi">
                                                                                                <img src="assets/images/users/avatar-4.jpg" alt="" class="rounded-circle avatar-xs">
                                                                                            </a>
                                                                                            <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Frank Hook" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="" data-bs-original-title="Frank Hook">
                                                                                                <img src="assets/images/users/avatar-3.jpg" alt="" class="rounded-circle avatar-xs">
                                                                                            </a>
                                                                                            <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Ruby" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="" data-bs-original-title=" Ruby">
                                                                                                <div class="avatar-xs">
                                                                                                    <div class="avatar-title rounded-circle bg-light text-primary">
                                                                                                        R
                                                                                                    </div>
                                                                                                </div>
                                                                                            </a>
                                                                                            <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="more" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="" data-bs-original-title="more">
                                                                                                <div class="avatar-xs">
                                                                                                    <div class="avatar-title rounded-circle">
                                                                                                        2+
                                                                                                    </div>
                                                                                                </div>
                                                                                            </a>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </ng-template>
                                                            </li>
                                                            <li [ngbNavItem]="2" class="nav-item">
                                                                <a ngbNavLink class="nav-link" data-bs-toggle="tab" role="tab">
                                                                    Weekly
                                                                </a>
                                                                <ng-template ngbNavContent>
                                                                    <div class="profile-timeline order-status">
                                                                        <div ngbAccordion activeIds="static-1">
                                                                            <div ngbAccordionItem [collapsed]="false" id="static-1">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="true" aria-controls="collapseOne">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0">
                                                                                                <img src="assets/images/users/avatar-3.jpg" alt="" class="avatar-xs rounded-circle" />
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    Joseph Parker</h6>
                                                                                                <small class="text-muted">New
                                                                                                    people joined with
                                                                                                    our company -
                                                                                                    Yesterday</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody class="ps-5">
                                                                                        <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
                                                                                            It makes a statement, it’s
                                                                                            impressive graphic design.
                                                                                            Increase or decrease the
                                                                                            letter spacing depending on
                                                                                            the situation and try, try
                                                                                            again until it looks right,
                                                                                            and each letter has the
                                                                                            perfect spot of its own.
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div ngbAccordionItem [disabled]="true" id="static-2">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="false" aria-controls="collapseTwo">
                                                                                        <div class="d-flex">
                                                                                            <div class="avatar-xs">
                                                                                                <div class="avatar-title rounded-circle bg-light text-danger">
                                                                                                    <i class="ri-shopping-bag-line"></i>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    Your order is placed
                                                                                                    <span class="badge bg-success-subtle text-success align-middle">Completed</span>
                                                                                                </h6>
                                                                                                <small class="text-muted">These
                                                                                                    customers can rest
                                                                                                    assured their order
                                                                                                    has been placed - 1
                                                                                                    week Ago</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody></div>
                                                                                </div>
                                                                            </div>
                                                                            <div ngbAccordionItem [collapsed]="false" id="static-3">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="false" aria-controls="collapseThree">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0 avatar-xs">
                                                                                                <div class="avatar-title bg-light text-success rounded-circle">
                                                                                                    <i class="ri-home-3-line"></i>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    Velzon admin
                                                                                                    dashboard templates
                                                                                                    layout upload</h6>
                                                                                                <small class="text-muted">We
                                                                                                    talked about a
                                                                                                    project on linkedin
                                                                                                    - 1 week Ago</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody class="accordion-body ps-5 fst-italic">
                                                                                        Powerful, clean & modern
                                                                                        responsive bootstrap 5 admin
                                                                                        template. The maximum file size
                                                                                        for uploads in this demo :
                                                                                        <div class="row mt-2">
                                                                                            <div class="col-xxl-6">
                                                                                                <div class="row border border-dashed gx-2 p-2">
                                                                                                    <div class="col-3">
                                                                                                        <img src="assets/images/small/img-3.jpg" alt="" class="img-fluid rounded" />
                                                                                                    </div>
                                                                                                    <div class="col-3">
                                                                                                        <img src="assets/images/small/img-5.jpg" alt="" class="img-fluid rounded" />
                                                                                                    </div>
                                                                                                    <div class="col-3">
                                                                                                        <img src="assets/images/small/img-7.jpg" alt="" class="img-fluid rounded" />
                                                                                                    </div>
                                                                                                    <div class="col-3">
                                                                                                        <img src="assets/images/small/img-9.jpg" alt="" class="img-fluid rounded" />
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div ngbAccordionItem [collapsed]="false" id="static-4">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="false">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0">
                                                                                                <img src="assets/images/users/avatar-6.jpg" alt="" class="avatar-xs rounded-circle" />
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    New ticket created
                                                                                                    <span class="badge bg-info-subtle text-info align-middle">Inprogress</span>
                                                                                                </h6>
                                                                                                <small class="text-muted mb-2">User
                                                                                                    <span class="text-secondary">Jack365</span>
                                                                                                    submitted a ticket -
                                                                                                    2 week Ago</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody class="accordion-body ps-5">
                                                                                        " A wonderful serenity has taken
                                                                                        possession of my entire soul,
                                                                                        like these sweet mornings of
                                                                                        spring which I enjoy with my
                                                                                        whole heart. Each design is a
                                                                                        new, unique piece of art birthed
                                                                                        into this world, and while you
                                                                                        have the opportunity to be
                                                                                        creative and make your own style
                                                                                        choices. "
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div ngbAccordionItem [collapsed]="false" id="static-5">
                                                                                <div ngbAccordionHeader>
                                                                                    <a class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" href="javascript:void(0);" aria-expanded="false">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0">
                                                                                                <img src="assets/images/users/avatar-5.jpg" alt="" class="avatar-xs rounded-circle" />
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    Jennifer Carter</h6>
                                                                                                <small class="text-muted">Commented
                                                                                                    - 4 week Ago</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </a>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div class="accordion-body ps-5">
                                                                                        <p class="text-muted fst-italic mb-2">
                                                                                            " This is an awesome admin
                                                                                            dashboard template. It is
                                                                                            extremely well structured
                                                                                            and uses state of the art
                                                                                            components (e.g. one of the
                                                                                            only templates using
                                                                                            boostrap 5.1.3 so far). I
                                                                                            integrated it into a Rails 6
                                                                                            project. Needs manual
                                                                                            integration work of course
                                                                                            but the template structure
                                                                                            made it easy. "</p>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                </ng-template>
                                                            </li>
                                                            <li [ngbNavItem]="3" class="nav-item">
                                                                <a ngbNavLink class="nav-link" data-bs-toggle="tab" role="tab">
                                                                    Monthly
                                                                </a>
                                                                <ng-template ngbNavContent>
                                                                    <div class="profile-timeline order-status">
                                                                        <div ngbAccordion activeIds="static-1">
                                                                            <div ngbAccordionItem [collapsed]="false" id="static-1">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="true" aria-controls="collapseOne">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0 avatar-xs">
                                                                                                <div class="avatar-title bg-light text-success rounded-circle">
                                                                                                    M
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    Megan Elmore</h6>
                                                                                                <small class="text-muted">Adding
                                                                                                    a new event with
                                                                                                    attachments - 1
                                                                                                    month Ago.</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody class="accordion-body ps-5">
                                                                                        <div class="row g-2">
                                                                                            <div class="col-auto">
                                                                                                <div class="d-flex border border-dashed p-2 rounded position-relative">
                                                                                                    <div class="flex-shrink-0">
                                                                                                        <i class="ri-image-2-line fs-17 text-danger"></i>
                                                                                                    </div>
                                                                                                    <div class="flex-grow-1 ms-2">
                                                                                                        <h6><a href="javascript:void(0);" class="stretched-link">Business
                                                                                                                Template
                                                                                                                - UI/UX
                                                                                                                design</a>
                                                                                                        </h6>
                                                                                                        <small>685
                                                                                                            KB</small>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-auto">
                                                                                                <div class="d-flex border border-dashed p-2 rounded position-relative">
                                                                                                    <div class="flex-shrink-0">
                                                                                                        <i class="ri-file-zip-line fs-17 text-info"></i>
                                                                                                    </div>
                                                                                                    <div class="flex-grow-1 ms-2">
                                                                                                        <h6><a href="javascript:void(0);" class="stretched-link">Bank
                                                                                                                Management
                                                                                                                System -
                                                                                                                PSD</a>
                                                                                                        </h6>
                                                                                                        <small>8.78
                                                                                                            MB</small>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-auto">
                                                                                                <div class="d-flex border border-dashed p-2 rounded position-relative">
                                                                                                    <div class="flex-shrink-0">
                                                                                                        <i class="ri-file-zip-line fs-17 text-info"></i>
                                                                                                    </div>
                                                                                                    <div class="flex-grow-1 ms-2">
                                                                                                        <h6><a href="javascript:void(0);" class="stretched-link">Bank
                                                                                                                Management
                                                                                                                System -
                                                                                                                PSD</a>
                                                                                                        </h6>
                                                                                                        <small>8.78
                                                                                                            MB</small>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div ngbAccordionItem [collapsed]="false" id="static-2">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="false" aria-controls="collapseTwo">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0">
                                                                                                <img src="assets/images/users/avatar-2.jpg" alt="" class="avatar-xs rounded-circle" />
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    Jacqueline Steve
                                                                                                </h6>
                                                                                                <small class="text-muted">We
                                                                                                    has changed 2
                                                                                                    attributes on 3
                                                                                                    month Ago</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody class="accordion-body ps-5">
                                                                                        In an awareness campaign, it is
                                                                                        vital for people to begin put 2
                                                                                        and 2 together and begin to
                                                                                        recognize your cause. Too much
                                                                                        or too little spacing, as in the
                                                                                        example below, can make things
                                                                                        unpleasant for the reader. The
                                                                                        goal is to make your text as
                                                                                        comfortable to read as possible.
                                                                                        A wonderful serenity has taken
                                                                                        possession of my entire soul,
                                                                                        like these sweet mornings of
                                                                                        spring which I enjoy with my
                                                                                        whole heart.
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div ngbAccordionItem [collapsed]="true" id="static-3">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="false" aria-controls="collapseThree">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0">
                                                                                                <img src="assets/images/users/avatar-5.jpg" alt="" class="avatar-xs rounded-circle" />
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    New ticket received
                                                                                                </h6>
                                                                                                <small class="text-muted mb-2">User
                                                                                                    <span class="text-secondary">Erica245</span>
                                                                                                    submitted a ticket -
                                                                                                    5 month Ago</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody class="accordion-body ps-5 fst-italic">
                                                                                        Powerful, clean & modern
                                                                                        responsive bootstrap 5 admin
                                                                                        template. The maximum file size
                                                                                        for uploads in this demo :
                                                                                        <div class="row mt-2">
                                                                                            <div class="col-xxl-6">
                                                                                                <div class="row border border-dashed gx-2 p-2">
                                                                                                    <div class="col-3">
                                                                                                        <img src="assets/images/small/img-3.jpg" alt="" class="img-fluid rounded" />
                                                                                                    </div>
                                                                                                    <div class="col-3">
                                                                                                        <img src="assets/images/small/img-5.jpg" alt="" class="img-fluid rounded" />
                                                                                                    </div>
                                                                                                    <div class="col-3">
                                                                                                        <img src="assets/images/small/img-7.jpg" alt="" class="img-fluid rounded" />
                                                                                                    </div>
                                                                                                    <div class="col-3">
                                                                                                        <img src="assets/images/small/img-9.jpg" alt="" class="img-fluid rounded" />
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div ngbAccordionItem [collapsed]="false" id="static-4">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="false">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0 avatar-xs">
                                                                                                <div class="avatar-title bg-light text-muted rounded-circle">
                                                                                                    <i class="ri-user-3-fill"></i>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    Nancy Martino</h6>
                                                                                                <small class="text-muted">Commented
                                                                                                    on 24 Nov,
                                                                                                    2021.</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody class="accordion-body ps-5 fst-italic">
                                                                                        " A wonderful serenity has taken
                                                                                        possession of my entire soul,
                                                                                        like these sweet mornings of
                                                                                        spring which I enjoy with my
                                                                                        whole heart. Each design is a
                                                                                        new, unique piece of art birthed
                                                                                        into this world, and while you
                                                                                        have the opportunity to be
                                                                                        creative and make your own style
                                                                                        choices. "
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div ngbAccordionItem [collapsed]="false" id="static-5">
                                                                                <div ngbAccordionHeader>
                                                                                    <button ngbAccordionButton class="accordion-button p-2 shadow-none" data-bs-toggle="collapse" aria-expanded="false">
                                                                                        <div class="d-flex">
                                                                                            <div class="flex-shrink-0">
                                                                                                <img src="assets/images/users/avatar-7.jpg" alt="" class="avatar-xs rounded-circle" />
                                                                                            </div>
                                                                                            <div class="flex-grow-1 ms-3">
                                                                                                <h6 class="fs-14 mb-1">
                                                                                                    Lewis Arnold</h6>
                                                                                                <small class="text-muted">Create
                                                                                                    new project buildng
                                                                                                    product - 8 month
                                                                                                    Ago</small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </button>
                                                                                </div>
                                                                                <div ngbAccordionCollapse>
                                                                                    <div ngbAccordionBody class="accordion-body ps-5">
                                                                                        <p class="text-muted mb-2">Every
                                                                                            team project can have a
                                                                                            velzon. Use the velzon to
                                                                                            share information with your
                                                                                            team to understand and
                                                                                            contribute to your project.
                                                                                        </p>
                                                                                        <div class="avatar-group">
                                                                                            <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Christi" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="" data-bs-original-title="Christi">
                                                                                                <img src="assets/images/users/avatar-4.jpg" alt="" class="rounded-circle avatar-xs">
                                                                                            </a>
                                                                                            <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Frank Hook" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="" data-bs-original-title="Frank Hook">
                                                                                                <img src="assets/images/users/avatar-3.jpg" alt="" class="rounded-circle avatar-xs">
                                                                                            </a>
                                                                                            <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Ruby" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="" data-bs-original-title=" Ruby">
                                                                                                <div class="avatar-xs">
                                                                                                    <div class="avatar-title rounded-circle bg-light text-primary">
                                                                                                        R
                                                                                                    </div>
                                                                                                </div>
                                                                                            </a>
                                                                                            <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="more" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="" data-bs-original-title="more">
                                                                                                <div class="avatar-xs">
                                                                                                    <div class="avatar-title rounded-circle">
                                                                                                        2+
                                                                                                    </div>
                                                                                                </div>
                                                                                            </a>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </ng-template>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="tab-content text-muted">
                                                        <div [ngbNavOutlet]="customNav"></div>
                                                    </div>
                                                </div><!-- end card body -->
                                            </div><!-- end card -->
                                        </div><!-- end col -->
                                    </div><!-- end row -->

                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title">Projects</h5>
                                            <!-- Swiper -->
                                            <div class="swiper project-swiper mt-4" dir="ltr">
                                                <ngx-slick-carousel [config]="config" class="carousel">
                                                    <div class="swiper-slide" ngxSlickItem>
                                                        <div class="card profile-project-card shadow-none profile-project-success mb-0">
                                                            <div class="card-body p-4">
                                                                <div class="d-flex">
                                                                    <div class="flex-grow-1 text-muted overflow-hidden">
                                                                        <h5 class="fs-14 text-truncate mb-1"><a href="javascript:void(0);" class="text-body">ABC Project
                                                                                Customization</a></h5>
                                                                        <p class="text-muted text-truncate mb-0">
                                                                            Last Update : <span class="fw-semibold text-body">4 hr
                                                                                Ago</span></p>
                                                                    </div>
                                                                    <div class="flex-shrink-0 ms-2">
                                                                        <div class="badge bg-warning-subtle text-warning fs-10">
                                                                            Inprogress</div>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex mt-4">
                                                                    <div class="flex-grow-1">
                                                                        <div class="d-flex align-items-center gap-2">
                                                                            <div>
                                                                                <h5 class="fs-12 text-muted mb-0">
                                                                                    Members :</h5>
                                                                            </div>
                                                                            <div class="avatar-group">
                                                                                <div class="avatar-group-item">
                                                                                    <div class="avatar-xs">
                                                                                        <img src="assets/images/users/avatar-4.jpg" alt="" class="rounded-circle img-fluid" />
                                                                                    </div>
                                                                                </div>
                                                                                <div class="avatar-group-item">
                                                                                    <div class="avatar-xs">
                                                                                        <img src="assets/images/users/avatar-5.jpg" alt="" class="rounded-circle img-fluid" />
                                                                                    </div>
                                                                                </div>
                                                                                <div class="avatar-group-item">
                                                                                    <div class="avatar-xs">
                                                                                        <div class="avatar-title rounded-circle bg-light text-primary">
                                                                                            A
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="avatar-group-item">
                                                                                    <div class="avatar-xs">
                                                                                        <img src="assets/images/users/avatar-2.jpg" alt="" class="rounded-circle img-fluid" />
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <!-- end card body -->
                                                        </div>
                                                        <!-- end card -->
                                                    </div>
                                                    <div class="swiper-slide" ngxSlickItem>
                                                        <div class="card profile-project-card shadow-none profile-project-danger mb-0">
                                                            <div class="card-body p-4">
                                                                <div class="d-flex">
                                                                    <div class="flex-grow-1 text-muted overflow-hidden">
                                                                        <h5 class="fs-14 text-truncate mb-1"><a href="javascript:void(0);" class="text-body">Client - John</a>
                                                                        </h5>
                                                                        <p class="text-muted text-truncate mb-0">
                                                                            Last Update : <span class="fw-semibold text-body">1 hr
                                                                                Ago</span></p>
                                                                    </div>
                                                                    <div class="flex-shrink-0 ms-2">
                                                                        <div class="badge bg-success-subtle text-success fs-10">
                                                                            Completed</div>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex mt-4">
                                                                    <div class="flex-grow-1">
                                                                        <div class="d-flex align-items-center gap-2">
                                                                            <div>
                                                                                <h5 class="fs-12 text-muted mb-0">
                                                                                    Members :</h5>
                                                                            </div>
                                                                            <div class="avatar-group">
                                                                                <div class="avatar-group-item">
                                                                                    <div class="avatar-xs">
                                                                                        <img src="assets/images/users/avatar-2.jpg" alt="" class="rounded-circle img-fluid" />
                                                                                    </div>
                                                                                </div>
                                                                                <div class="avatar-group-item">
                                                                                    <div class="avatar-xs">
                                                                                        <div class="avatar-title rounded-circle bg-light text-primary">
                                                                                            C
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div><!-- end card body -->
                                                        </div><!-- end card -->
                                                    </div><!-- end slide item -->
                                                    <div class="swiper-slide" ngxSlickItem>
                                                        <div class="card profile-project-card shadow-none profile-project-info mb-0">
                                                            <div class="card-body p-4">
                                                                <div class="d-flex">
                                                                    <div class="flex-grow-1 text-muted overflow-hidden">
                                                                        <h5 class="fs-14 text-truncate mb-1"><a href="javascript:void(0);" class="text-body">Brand logo
                                                                                Design</a></h5>
                                                                        <p class="text-muted text-truncate mb-0">
                                                                            Last Update : <span class="fw-semibold text-body">2 hr
                                                                                Ago</span></p>
                                                                    </div>
                                                                    <div class="flex-shrink-0 ms-2">
                                                                        <div class="badge bg-warning-subtle text-warning fs-10">
                                                                            Inprogress</div>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex mt-4">
                                                                    <div class="flex-grow-1">
                                                                        <div class="d-flex align-items-center gap-2">
                                                                            <div>
                                                                                <h5 class="fs-12 text-muted mb-0">
                                                                                    Members :</h5>
                                                                            </div>
                                                                            <div class="avatar-group">
                                                                                <div class="avatar-group-item">
                                                                                    <div class="avatar-xs">
                                                                                        <img src="assets/images/users/avatar-5.jpg" alt="" class="rounded-circle img-fluid" />
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div><!-- end card body -->
                                                        </div><!-- end card -->
                                                    </div><!-- end slide item -->
                                                    <div class="swiper-slide" ngxSlickItem>
                                                        <div class="card profile-project-card shadow-none profile-project-danger mb-0">
                                                            <div class="card-body p-4">
                                                                <div class="d-flex">
                                                                    <div class="flex-grow-1 text-muted overflow-hidden">
                                                                        <h5 class="fs-14 text-truncate mb-1"><a href="javascript:void(0);" class="text-body">Project update</a>
                                                                        </h5>
                                                                        <p class="text-muted text-truncate mb-0">
                                                                            Last Update : <span class="fw-semibold text-body">4 hr
                                                                                Ago</span></p>
                                                                    </div>
                                                                    <div class="flex-shrink-0 ms-2">
                                                                        <div class="badge bg-success-subtle text-success fs-10">
                                                                            Completed</div>
                                                                    </div>
                                                                </div>

                                                                <div class="d-flex mt-4">
                                                                    <div class="flex-grow-1">
                                                                        <div class="d-flex align-items-center gap-2">
                                                                            <div>
                                                                                <h5 class="fs-12 text-muted mb-0">
                                                                                    Members :</h5>
                                                                            </div>
                                                                            <div class="avatar-group">
                                                                                <div class="avatar-group-item">
                                                                                    <div class="avatar-xs">
                                                                                        <img src="assets/images/users/avatar-4.jpg" alt="" class="rounded-circle img-fluid" />
                                                                                    </div>
                                                                                </div>
                                                                                <div class="avatar-group-item">
                                                                                    <div class="avatar-xs">
                                                                                        <img src="assets/images/users/avatar-5.jpg" alt="" class="rounded-circle img-fluid" />
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <!-- end card body -->
                                                        </div>
                                                        <!-- end card -->
                                                    </div>
                                                    <!-- end slide item -->
                                                    <div class="swiper-slide" ngxSlickItem>
                                                        <div class="card profile-project-card shadow-none profile-project-warning mb-0">
                                                            <div class="card-body p-4">
                                                                <div class="d-flex">
                                                                    <div class="flex-grow-1 text-muted overflow-hidden">
                                                                        <h5 class="fs-14 text-truncate mb-1"><a href="javascript:void(0);" class="text-body">Chat App</a></h5>
                                                                        <p class="text-muted text-truncate mb-0">
                                                                            Last Update : <span class="fw-semibold text-body">1 hr
                                                                                Ago</span></p>
                                                                    </div>
                                                                    <div class="flex-shrink-0 ms-2">
                                                                        <div class="badge bg-warning-subtle text-warning fs-10">
                                                                            Inprogress</div>
                                                                    </div>
                                                                </div>

                                                                <div class="d-flex mt-4">
                                                                    <div class="flex-grow-1">
                                                                        <div class="d-flex align-items-center gap-2">
                                                                            <div>
                                                                                <h5 class="fs-12 text-muted mb-0">
                                                                                    Members :</h5>
                                                                            </div>
                                                                            <div class="avatar-group">
                                                                                <div class="avatar-group-item">
                                                                                    <div class="avatar-xs">
                                                                                        <img src="assets/images/users/avatar-4.jpg" alt="" class="rounded-circle img-fluid" />
                                                                                    </div>
                                                                                </div>
                                                                                <div class="avatar-group-item">
                                                                                    <div class="avatar-xs">
                                                                                        <img src="assets/images/users/avatar-5.jpg" alt="" class="rounded-circle img-fluid" />
                                                                                    </div>
                                                                                </div>
                                                                                <div class="avatar-group-item">
                                                                                    <div class="avatar-xs">
                                                                                        <div class="avatar-title rounded-circle bg-light text-primary">
                                                                                            A
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <!-- end card body -->
                                                        </div>
                                                        <!-- end card -->
                                                    </div>
                                                    <!-- end slide item -->
                                                    <!-- Add Arrows -->
                                                </ngx-slick-carousel>

                                            </div>

                                        </div>
                                        <!-- end card body -->
                                    </div><!-- end card -->

                                </div><!--end col-->
                            </div><!--end row-->
                        </ng-template>
                    </li>
                    <li [ngbNavItem]="2" class="nav-item">
                        <a ngbNavLink class="nav-link" data-bs-toggle="tab" role="tab">
                            <i class="ri-list-unordered d-inline-block d-md-none"></i> <span class="d-none d-md-inline-block">Activities</span>
                        </a>
                        <ng-template ngbNavContent>
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title mb-3">Activities</h5>
                                    <div class="acitivity-timeline">
                                        <div class="acitivity-item d-flex">
                                            <div class="flex-shrink-0">
                                                <img src="assets/images/users/avatar-1.jpg" alt="" class="avatar-xs rounded-circle acitivity-avatar" />
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">Oliver Phillips <span class="badge bg-primary-subtle text-primary align-middle">New</span>
                                                </h6>
                                                <p class="text-muted mb-2">We talked about a project on linkedin.</p>
                                                <small class="mb-0 text-muted">Today</small>
                                            </div>
                                        </div>
                                        <div class="acitivity-item py-3 d-flex">
                                            <div class="flex-shrink-0 avatar-xs acitivity-avatar">
                                                <div class="avatar-title bg-success-subtle text-success rounded-circle">
                                                    N
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">Nancy Martino <span class="badge bg-secondary-subtle text-secondary align-middle">In
                                                        Progress</span></h6>
                                                <p class="text-muted mb-2"><i class="ri-file-text-line align-middle ms-2"></i> Create new
                                                    project Buildng product</p>
                                                <div class="avatar-group mb-2">
                                                    <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Christi" placement="top" data-bs-toggle="tooltip" data-bs-placement="top" title="" data-bs-original-title="Christi">
                                                        <img src="assets/images/users/avatar-4.jpg" alt="" class="rounded-circle avatar-xs" />
                                                    </a>
                                                    <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Frank Hook" placement="top" data-bs-toggle="tooltip" data-bs-placement="top" title="" data-bs-original-title="Frank Hook">
                                                        <img src="assets/images/users/avatar-3.jpg" alt="" class="rounded-circle avatar-xs" />
                                                    </a>
                                                    <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Ruby" placement="top" data-bs-toggle="tooltip" data-bs-placement="top" title="" data-bs-original-title=" Ruby">
                                                        <div class="avatar-xs">
                                                            <div class="avatar-title rounded-circle bg-light text-primary">
                                                                R
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="more" placement="top" data-bs-toggle="tooltip" data-bs-placement="top" title="" data-bs-original-title="more">
                                                        <div class="avatar-xs">
                                                            <div class="avatar-title rounded-circle">
                                                                2+
                                                            </div>
                                                        </div>
                                                    </a>
                                                </div>
                                                <small class="mb-0 text-muted">Yesterday</small>
                                            </div>
                                        </div>
                                        <div class="acitivity-item py-3 d-flex">
                                            <div class="flex-shrink-0">
                                                <img src="assets/images/users/avatar-2.jpg" alt="" class="avatar-xs rounded-circle acitivity-avatar" />
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">Natasha Carey <span class="badge bg-success-subtle text-success align-middle">Completed</span>
                                                </h6>
                                                <p class="text-muted mb-2">Adding a new event with attachments</p>
                                                <div class="row">
                                                    <div class="col-xxl-4">
                                                        <div class="row border border-dashed gx-2 p-2 mb-2">
                                                            <div class="col-4">
                                                                <img src="assets/images/small/img-2.jpg" alt="" class="img-fluid rounded" />
                                                            </div><!--end col-->
                                                            <div class="col-4">
                                                                <img src="assets/images/small/img-3.jpg" alt="" class="img-fluid rounded" />
                                                            </div><!--end col-->
                                                            <div class="col-4">
                                                                <img src="assets/images/small/img-4.jpg" alt="" class="img-fluid rounded" />
                                                            </div><!--end col-->
                                                        </div><!--end row-->
                                                    </div>
                                                </div>
                                                <small class="mb-0 text-muted">25 Nov</small>
                                            </div>
                                        </div>
                                        <div class="acitivity-item py-3 d-flex">
                                            <div class="flex-shrink-0">
                                                <img src="assets/images/users/avatar-6.jpg" alt="" class="avatar-xs rounded-circle acitivity-avatar" />
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">Bethany Johnson</h6>
                                                <p class="text-muted mb-2">added a new member to velzon dashboard</p>
                                                <small class="mb-0 text-muted">19 Nov</small>
                                            </div>
                                        </div>
                                        <div class="acitivity-item py-3 d-flex">
                                            <div class="flex-shrink-0">
                                                <div class="avatar-xs acitivity-avatar">
                                                    <div class="avatar-title rounded-circle bg-danger-subtle text-danger">
                                                        <i class="ri-shopping-bag-line"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">Your order is placed <span class="badge bg-danger-subtle text-danger align-middle ms-1">Out
                                                        of Delivery</span></h6>
                                                <p class="text-muted mb-2">These customers can rest assured their order
                                                    has been placed.</p>
                                                <small class="mb-0 text-muted">16 Nov</small>
                                            </div>
                                        </div>
                                        <div class="acitivity-item py-3 d-flex">
                                            <div class="flex-shrink-0">
                                                <img src="assets/images/users/avatar-7.jpg" alt="" class="avatar-xs rounded-circle acitivity-avatar" />
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">Lewis Pratt</h6>
                                                <p class="text-muted mb-2">They all have something to say beyond the
                                                    words on the page. They can come across as casual or neutral, exotic
                                                    or graphic. </p>
                                                <small class="mb-0 text-muted">22 Oct</small>
                                            </div>
                                        </div>
                                        <div class="acitivity-item py-3 d-flex">
                                            <div class="flex-shrink-0">
                                                <div class="avatar-xs acitivity-avatar">
                                                    <div class="avatar-title rounded-circle bg-info-subtle text-info">
                                                        <i class="ri-line-chart-line"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">Monthly sales report</h6>
                                                <p class="text-muted mb-2"><span class="text-danger">2 days left</span>
                                                    notification to submit the monthly sales report. <a href="javascript:void(0);" class="link-warning text-decoration-underline">Reports
                                                        Builder</a></p>
                                                <small class="mb-0 text-muted">15 Oct</small>
                                            </div>
                                        </div>
                                        <div class="acitivity-item d-flex">
                                            <div class="flex-shrink-0">
                                                <img src="assets/images/users/avatar-8.jpg" alt="" class="avatar-xs rounded-circle acitivity-avatar" />
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">New ticket received <span class="badge bg-success-subtle text-success align-middle">Completed</span>
                                                </h6>
                                                <p class="text-muted mb-2">User <span class="text-secondary">Erica245</span> submitted a ticket.</p>
                                                <small class="mb-0 text-muted">26 Aug</small>
                                            </div>
                                        </div>
                                    </div>
                                </div><!--end card-body-->
                            </div><!--end card-->
                        </ng-template>
                    </li>
                    <li [ngbNavItem]="3" class="nav-item">
                        <a ngbNavLink class="nav-link" data-bs-toggle="tab" role="tab">
                            <i class="ri-price-tag-line d-inline-block d-md-none"></i> <span class="d-none d-md-inline-block">Projects</span>
                        </a>
                        <ng-template ngbNavContent>
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        @for ( data of projectList; track $index) {
                                        <div class="col-xxl-3 col-sm-6">
                                            <div class="card profile-project-card shadow-none profile-project-{{data.cardBorderColor}}">
                                                <div class="card-body p-4">
                                                    <div class="d-flex">
                                                        <div class="flex-grow-1 text-muted overflow-hidden">
                                                            <h5 class="fs-14 text-truncate"><a href="javascript:void(0);" class="text-body">{{data.title}}</a></h5>
                                                            <p class="text-muted text-truncate mb-0">Last Update : <span class="fw-semibold text-body">{{data.updatedTime}}</span>
                                                            </p>
                                                        </div>
                                                        <div class="flex-shrink-0 ms-2">
                                                            <div class="badge bg-{{data.badgeClass}}-subtle fs-10">
                                                                {{data.badgeText}}</div>
                                                        </div>
                                                    </div>

                                                    <div class="d-flex mt-4">
                                                        <div class="flex-grow-1">
                                                            <div class="d-flex align-items-center gap-2">
                                                                <div>
                                                                    <h5 class="fs-12 text-muted mb-0">Members :</h5>
                                                                </div>
                                                                <div class="avatar-group">
                                                                    @for(user of data.member; track $index){
                                                                    <div class="avatar-group-item">

                                                                        @if(user.img){
                                                                        <div class="avatar-xs" ngbTooltip="{{user.name}}" placement="top">
                                                                            <img :src=" {{user.img}}" alt="" class="rounded-circle img-fluid">
                                                                        </div>
                                                                        }@else ()
                                                                        {<a href="javascript: void(0);" class="d-block" ngbTooltip="{{user.name}}" placement="top">
                                                                            <div class="avatar-xs">
                                                                                <div class="avatar-title rounded-circle {{user.variant}}">
                                                                                    {{user.text}}
                                                                                </div>
                                                                            </div>
                                                                        </a>}

                                                                    </div>
                                                                    }
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- end card body -->
                                            </div>
                                            <!-- end card -->
                                        </div><!--end col-->
                                        }
                                        <div class="col-lg-12">
                                            <div class="mt-4">
                                                <ul class="pagination pagination-separated justify-content-center mb-0">
                                                    <ngb-pagination [collectionSize]="allprojectList.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                                                    </ngb-pagination>
                                                </ul>
                                            </div>
                                        </div>
                                    </div><!--end row-->
                                </div><!--end card-body-->
                            </div><!--end card-->
                        </ng-template>
                    </li>
                    <li [ngbNavItem]="4" class="nav-item">
                        <a ngbNavLink class="nav-link" data-bs-toggle="tab" role="tab">
                            <i class="ri-folder-4-line d-inline-block d-md-none"></i> <span class="d-none d-md-inline-block">Documents</span>
                        </a>
                        <ng-template ngbNavContent>
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-4">
                                        <h5 class="card-title flex-grow-1 mb-0">Documents</h5>
                                        <div class="flex-shrink-0">
                                            <input class="form-control d-none" type="file" id="formFile">
                                            <label for="formFile" class="btn btn-danger"><i class="ri-upload-2-fill me-1 align-bottom"></i> Upload File</label>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="table-responsive">
                                                <table class="table table-borderless align-middle mb-0">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th scope="col">File Name</th>
                                                            <th scope="col">Type</th>
                                                            <th scope="col">Size</th>
                                                            <th scope="col">Upload Date</th>
                                                            <th scope="col">Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @for (data of document; track $index) {
                                                        <tr id="lj_{{data.id}}">
                                                            <td>
                                                                <div class="d-flex align-items-center">
                                                                    <div class="avatar-sm">
                                                                        <div class="avatar-title bg-{{data.iconBackgroundClass}}-subtle text-{{data.iconBackgroundClass}} rounded fs-20">
                                                                            <i class="{{data.icon}}"></i>
                                                                        </div>
                                                                    </div>
                                                                    <div class="ms-3 flex-grow-1">
                                                                        <h6 class="fs-15 mb-0"><a href="javascript:void(0)">{{data.fileName}}</a>
                                                                        </h6>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>{{data.fileType}}</td>
                                                            <td>{{data.fileSize}}</td>
                                                            <td>{{data.updatedDate}}</td>
                                                            <td>
                                                                <div class="dropdown" ngbDropdown>
                                                                    <button href="javascript:void(0);" class="btn btn-light btn-md arrow-none" id="dropdownMenuLink15" data-bs-toggle="dropdown" aria-expanded="true" ngbDropdownToggle>
                                                                        <i class="ri-equalizer-fill"></i>
                                                                    </button>
                                                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink15" ngbDropdownMenu>
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-eye-fill me-2 align-middle text-muted float-start"></i>View</a>
                                                                        </li>
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-download-2-fill me-2 align-middle text-muted float-start"></i>Download</a>
                                                                        </li>
                                                                        <li class="dropdown-divider"></li>
                                                                        <li><a class="dropdown-item" href="javascript:void(0);" (click)="confirm(deleteModel,data.id)"><i class="ri-delete-bin-5-line me-2 align-middle text-muted float-start"></i>Delete</a>
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="text-center mt-3">
                                                <a href="javascript:void(0);" class="text-success "><i class="mdi mdi-loading mdi-spin fs-20 align-middle me-2"></i>
                                                    Load more </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </li>
                </ul>
                <div class="flex-shrink-0">
                    <a routerLink="/pages/profile-setting" class="btn btn-success"><i class="ri-edit-box-line align-bottom"></i> Edit Profile</a>
                </div>
            </div>
            <!-- Tab panes -->
            <div class="tab-content pt-4 text-muted">
                <div [ngbNavOutlet]="customNav"></div>
            </div><!--end tab-content-->
        </div>
    </div><!--end col-->
</div><!--end row-->

<!-- removeItemModal -->
<ng-template #deleteModel let-modal>
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="btn-close" (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body">
            <div class="mt-2 text-center">
                <lord-icon src="https://cdn.lordicon.com/gsqxdxog.json" trigger="loop" colors="primary:#405189,secondary:#f06548" style="width:90px;height:90px"></lord-icon>
                <div class="mt-4 pt-2 fs-15 mx-4 mx-sm-5">
                    <h4>You are about to delete a order ?</h4>
                    <p class="text-muted mx-4 mb-0">Deleting your order will remove all of your information from our
                        database.</p>
                </div>
            </div>
            <div class="d-flex gap-2 justify-content-center mt-4 mb-2">
                <button class="btn btn-link link-success fw-medium text-decoration-none" data-bs-dismiss="modal" (click)="modal.close('Close click')"><i class="ri-close-line me-1 align-middle"></i> Close</button>
                <button type="button" class="btn w-sm btn-danger " id="delete-product" (click)="deleteData(deleteId)" (click)="modal.close('Close click')">Yes, Delete It!</button>
            </div>
        </div>
    </div><!-- /.modal-content -->
</ng-template>