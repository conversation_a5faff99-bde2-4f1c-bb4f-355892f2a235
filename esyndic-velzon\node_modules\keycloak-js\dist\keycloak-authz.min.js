!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define("keycloak-authorization",e):(t="undefined"!=typeof globalThis?globalThis:t||self).KeycloakAuthorization=e()}(this,(function(){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};var e={exports:{}};!function(e,n){e.exports=function(){function e(t){var e=typeof t;return null!==t&&("object"===e||"function"===e)}function n(t){return"function"==typeof t}function r(t){X=t}function o(t){Y=t}function i(){return function(){return process.nextTick(f)}}function s(){return void 0!==J?function(){J(f)}:c()}function u(){var t=0,e=new U(f),n=document.createTextNode("");return e.observe(n,{characterData:!0}),function(){n.data=t=++t%2}}function a(){var t=new MessageChannel;return t.port1.onmessage=f,function(){return t.port2.postMessage(0)}}function c(){var t=setTimeout;return function(){return t(f,1)}}function f(){for(var t=0;t<F;t+=2)(0,W[t])(W[t+1]),W[t]=void 0,W[t+1]=void 0;F=0}function l(){try{var t=Function("return this")().require("vertx");return J=t.runOnLoop||t.runOnContext,s()}catch(t){return c()}}function h(t,e){var n=this,r=new this.constructor(d);void 0===r[V]&&R(r);var o=n._state;if(o){var i=arguments[o-1];Y((function(){return P(o,r,i,n._result)}))}else S(n,r,t,e);return r}function p(t){var e=this;if(t&&"object"==typeof t&&t.constructor===e)return t;var n=new e(d);return b(n,t),n}function d(){}function v(){return new TypeError("You cannot resolve a promise with itself")}function m(){return new TypeError("A promises callback cannot return that same promise.")}function _(t,e,n,r){try{t.call(e,n,r)}catch(t){return t}}function y(t,e,n){Y((function(t){var r=!1,o=_(n,e,(function(n){r||(r=!0,e!==n?b(t,n):k(t,n))}),(function(e){r||(r=!0,T(t,e))}),"Settle: "+(t._label||" unknown promise"));!r&&o&&(r=!0,T(t,o))}),t)}function g(t,e){e._state===$?k(t,e._result):e._state===tt?T(t,e._result):S(e,void 0,(function(e){return b(t,e)}),(function(e){return T(t,e)}))}function w(t,e,r){e.constructor===t.constructor&&r===h&&e.constructor.resolve===p?g(t,e):void 0===r?k(t,e):n(r)?y(t,e,r):k(t,e)}function b(t,n){if(t===n)T(t,v());else if(e(n)){var r=void 0;try{r=n.then}catch(e){return void T(t,e)}w(t,n,r)}else k(t,n)}function A(t){t._onerror&&t._onerror(t._result),x(t)}function k(t,e){t._state===Z&&(t._result=e,t._state=$,0!==t._subscribers.length&&Y(x,t))}function T(t,e){t._state===Z&&(t._state=tt,t._result=e,Y(A,t))}function S(t,e,n,r){var o=t._subscribers,i=o.length;t._onerror=null,o[i]=e,o[i+$]=n,o[i+tt]=r,0===i&&t._state&&Y(x,t)}function x(t){var e=t._subscribers,n=t._state;if(0!==e.length){for(var r=void 0,o=void 0,i=t._result,s=0;s<e.length;s+=3)r=e[s],o=e[s+n],r?P(n,r,o,i):o(i);t._subscribers.length=0}}function P(t,e,r,o){var i=n(r),s=void 0,u=void 0,a=!0;if(i){try{s=r(o)}catch(t){a=!1,u=t}if(e===s)return void T(e,m())}else s=o;e._state!==Z||(i&&a?b(e,s):!1===a?T(e,u):t===$?k(e,s):t===tt&&T(e,s))}function q(t,e){try{e((function(e){b(t,e)}),(function(e){T(t,e)}))}catch(e){T(t,e)}}function O(){return et++}function R(t){t[V]=et++,t._state=void 0,t._result=void 0,t._subscribers=[]}function j(){return new Error("Array Methods must be provided an Array")}function z(t){return new nt(this,t).promise}function C(t){var e=this;return new e(I(t)?function(n,r){for(var o=t.length,i=0;i<o;i++)e.resolve(t[i]).then(n,r)}:function(t,e){return e(new TypeError("You must pass an array to race."))})}function E(t){var e=new this(d);return T(e,t),e}function M(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function L(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}function N(){var e=void 0;if(void 0!==t)e=t;else if("undefined"!=typeof self)e=self;else try{e=Function("return this")()}catch(t){throw new Error("polyfill failed because global object is unavailable in this environment")}var n=e.Promise;if(n){var r=null;try{r=Object.prototype.toString.call(n.resolve())}catch(t){}if("[object Promise]"===r&&!n.cast)return}e.Promise=rt}var H=void 0;H=Array.isArray?Array.isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)};var I=H,F=0,J=void 0,X=void 0,Y=function(t,e){W[F]=t,W[F+1]=e,2===(F+=2)&&(X?X(f):Q())},B="undefined"!=typeof window?window:void 0,K=B||{},U=K.MutationObserver||K.WebKitMutationObserver,D="undefined"==typeof self&&"undefined"!=typeof process&&"[object process]"==={}.toString.call(process),G="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel,W=new Array(1e3),Q=void 0;Q=D?i():U?u():G?a():void 0===B?l():c();var V=Math.random().toString(36).substring(2),Z=void 0,$=1,tt=2,et=0,nt=function(){function t(t,e){this._instanceConstructor=t,this.promise=new t(d),this.promise[V]||R(this.promise),I(e)?(this.length=e.length,this._remaining=e.length,this._result=new Array(this.length),0===this.length?k(this.promise,this._result):(this.length=this.length||0,this._enumerate(e),0===this._remaining&&k(this.promise,this._result))):T(this.promise,j())}return t.prototype._enumerate=function(t){for(var e=0;this._state===Z&&e<t.length;e++)this._eachEntry(t[e],e)},t.prototype._eachEntry=function(t,e){var n=this._instanceConstructor,r=n.resolve;if(r===p){var o=void 0,i=void 0,s=!1;try{o=t.then}catch(t){s=!0,i=t}if(o===h&&t._state!==Z)this._settledAt(t._state,e,t._result);else if("function"!=typeof o)this._remaining--,this._result[e]=t;else if(n===rt){var u=new n(d);s?T(u,i):w(u,t,o),this._willSettleAt(u,e)}else this._willSettleAt(new n((function(e){return e(t)})),e)}else this._willSettleAt(r(t),e)},t.prototype._settledAt=function(t,e,n){var r=this.promise;r._state===Z&&(this._remaining--,t===tt?T(r,n):this._result[e]=n),0===this._remaining&&k(r,this._result)},t.prototype._willSettleAt=function(t,e){var n=this;S(t,void 0,(function(t){return n._settledAt($,e,t)}),(function(t){return n._settledAt(tt,e,t)}))},t}(),rt=function(){function t(e){this[V]=O(),this._result=this._state=void 0,this._subscribers=[],d!==e&&("function"!=typeof e&&M(),this instanceof t?q(this,e):L())}return t.prototype.catch=function(t){return this.then(null,t)},t.prototype.finally=function(t){var e=this,r=e.constructor;return n(t)?e.then((function(e){return r.resolve(t()).then((function(){return e}))}),(function(e){return r.resolve(t()).then((function(){throw e}))})):e.then(t,t)},t}();return rt.prototype.then=h,rt.all=z,rt.race=C,rt.resolve=p,rt.reject=E,rt._setScheduler=r,rt._setAsap=o,rt._asap=Y,rt.polyfill=N,rt.Promise=rt,rt}()}(e);var n=e.exports;return function(t,e){var r=this;this.rpt=null;var o=function(){},i=function(){};return void 0!==n.Promise&&-1!==n.Promise.toString().indexOf("[native code]")&&(this.ready=new n.Promise((function(t,e){o=t,i=e}))),this.init=function(){var e=new XMLHttpRequest;e.open("GET",t.authServerUrl+"/realms/"+t.realm+"/.well-known/uma2-configuration"),e.onreadystatechange=function(){4==e.readyState&&(200==e.status?(r.config=JSON.parse(e.responseText),o()):(console.error("Could not obtain configuration from server."),i()))},e.send(null)},this.authorize=function(e){return this.then=function(n,o,i){if(e&&e.ticket){var s=new XMLHttpRequest;s.open("POST",r.config.token_endpoint,!0),s.setRequestHeader("Content-type","application/x-www-form-urlencoded"),s.setRequestHeader("Authorization","Bearer "+t.token),s.onreadystatechange=function(){if(4==s.readyState){var t=s.status;if(t>=200&&t<300){var e=JSON.parse(s.responseText).access_token;r.rpt=e,n(e)}else 403==t?o?o():console.error("Authorization request was denied by the server."):i?i():console.error("Could not obtain authorization data from server.")}};var u="grant_type=urn:ietf:params:oauth:grant-type:uma-ticket&client_id="+t.clientId+"&ticket="+e.ticket;null!=e.submitRequest&&(u+="&submit_request="+e.submitRequest);var a=e.metadata;a&&(a.responseIncludeResourceName&&(u+="&response_include_resource_name="+a.responseIncludeResourceName),a.responsePermissionsLimit&&(u+="&response_permissions_limit="+a.responsePermissionsLimit)),r.rpt&&(null==e.incrementalAuthorization||e.incrementalAuthorization)&&(u+="&rpt="+r.rpt),s.send(u)}},this},this.entitlement=function(e,n){return this.then=function(o,i,s){var u=new XMLHttpRequest;u.open("POST",r.config.token_endpoint,!0),u.setRequestHeader("Content-type","application/x-www-form-urlencoded"),u.setRequestHeader("Authorization","Bearer "+t.token),u.onreadystatechange=function(){if(4==u.readyState){var t=u.status;if(t>=200&&t<300){var e=JSON.parse(u.responseText).access_token;r.rpt=e,o(e)}else 403==t?i?i():console.error("Authorization request was denied by the server."):s?s():console.error("Could not obtain authorization data from server.")}},n||(n={});var a="grant_type=urn:ietf:params:oauth:grant-type:uma-ticket&client_id="+t.clientId;n.claimToken&&(a+="&claim_token="+n.claimToken,n.claimTokenFormat&&(a+="&claim_token_format="+n.claimTokenFormat)),a+="&audience="+e;var c=n.permissions;c||(c=[]);for(var f=0;f<c.length;f++){var l=c[f],h=l.id;if(l.scopes&&l.scopes.length>0){h+="#";for(var p=0;p<l.scopes.length;p++){var d=l.scopes[p];h.indexOf("#")!=h.length-1&&(h+=","),h+=d}}a+="&permission="+h}var v=n.metadata;v&&(v.responseIncludeResourceName&&(a+="&response_include_resource_name="+v.responseIncludeResourceName),v.responsePermissionsLimit&&(a+="&response_permissions_limit="+v.responsePermissionsLimit)),r.rpt&&(a+="&rpt="+r.rpt),u.send(a)},this},this.init(this),this}}));
//# sourceMappingURL=keycloak-authz.min.js.map
