<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Range Slider" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header align-items-center d-flex">
                <h4 class="card-title mb-0 flex-grow-1">Bootstrap Range</h4>
                <div class="flex-shrink-0">
                    <div class="form-check form-switch form-switch-right form-switch-md">
                        <label for="default-rangeslider" class="form-label text-muted">Show Code</label>
                        <input class="form-check-input code-switcher" type="checkbox" id="default-rangeslider" (click)="ShowCode($event)">
                    </div>
                </div>
            </div><!-- end card header -->

            <div class="card-body">
                <p></p>
                <div class="live-preview">
                    <div>
                        <div>
                            <h5 class="fs-14">Default Range</h5>
                            <p class="text-muted">Use <code>type="range"</code> attribute and <code>form-range</code> class to create a deafult range.</p>
                            <!-- Default Range -->
                            <input type="range" class="form-range" id="customRange1">
                        </div>

                        <div class="mt-4">
                            <h5 class="fs-14">Disabled</h5>
                            <p class="text-muted">Use <code>disabled</code> attribute on an input to give it a grayed out appearance and remove pointer events.</p>

                            <!-- Disabled Range -->
                            <input type="range" class="form-range" id="disabledRange" disabled>
                        </div>

                        <div class="mt-4">
                            <h5 class="fs-14">Min and Max</h5>
                            <p class="text-muted">Use <code>min</code> and <code>max</code> attribute with specified range input respectively.</p>

                            <!-- Min and Max -->
                            <input type="range" class="form-range" min="0" max="5" id="customRange2">
                        </div>

                        <div class="mt-4">
                            <h5 class="fs-14">Steps</h5>
                            <p class="text-muted">By default, range inputs “snap” to integer values. To change this, you can specify a step value. In the example below, we double the number of steps by using <code>step="0.5"</code> attribute.</p>

                            <!-- Steps -->
                            <input type="range" class="form-range" min="0" max="5" step="0.5" id="customRange3">
                        </div>
                    </div>
                </div>


                <div class="d-none code-view">
                    <pre class="language-markup">
<code>&lt;!-- Default Range --&gt;
&lt;input type=&quot;range&quot; class=&quot;form-range&quot; id=&quot;customRange1&quot;&gt;

&lt;!-- Disabled Range --&gt;
&lt;input type=&quot;range&quot; class=&quot;form-range&quot; id=&quot;disabledRange&quot; disabled&gt;

&lt;!-- Min and Max --&gt;
&lt;input type=&quot;range&quot; class=&quot;form-range&quot; min=&quot;0&quot; max=&quot;5&quot; id=&quot;customRange2&quot;&gt;

&lt;!-- Steps --&gt;
&lt;input type=&quot;range&quot; class=&quot;form-range&quot; min=&quot;0&quot; max=&quot;5&quot; step=&quot;0.5&quot; id=&quot;customRange3&quot;&gt;</code></pre>
                </div>

            </div>
            <!-- end card body -->
        </div>
        <!-- end card -->
    </div> <!-- end col -->
</div>
<!-- end row -->


<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Nouislider</h4>
            </div><!-- end card header -->

            <div class="card-body">

                <p class="text-muted mb-4">noUiSlider is a lightweight JavaScript range slider</p>

                <div class="live-preview">
                    <div>
                        <div class="mb-3">
                            <div class="row align-items-center">
                                <div class="col-lg-3">
                                    <h5 class="fs-14">Basic Example</h5>
                                </div><!-- end col -->
                                <div class="col-lg-9">
                                    <ngx-slider [(value)]="defaultVal" [options]="option"></ngx-slider>
                                </div><!-- end col -->
                            </div><!-- end row -->
                        </div>

                        <div class="border border-dashed"></div>

                        <div class="my-4">
                            <div class="row align-items-center">
                                <div class="col-lg-3">
                                    <h5 class="fs-14">Min-Max</h5>
                                </div><!-- end col -->
                                <div class="col-lg-9">
                                    <ngx-slider [(value)]="value" [(highValue)]="highValue" [options]="options"></ngx-slider>
                                </div><!-- end col -->
                            </div><!-- end row -->
                        </div>

                        <div class="border border-dashed"></div>

                        <div class="my-4">
                            <div class="row">
                                <div class="col-lg-3">
                                    <h5 class="fs-14">Slider with custom step value</h5>
                                </div><!-- end col -->
                                <div class="col-lg-9">
                                    <ngx-slider [(value)]="custom" [options]="customOption"></ngx-slider>
                                </div><!-- end col -->
                            </div><!-- end row -->
                        </div>

                        <div class="border border-dashed"></div>

                        <div class="my-4">
                            <div class="row">
                                <div class="col-lg-3">
                                    <h5 class="fs-14">Slider with floating point values</h5>
                                </div><!-- end col -->
                                <div class="col-lg-9">
                                    <ngx-slider [(value)]="floatValue" [options]="floatingOptions"></ngx-slider>
                                    <div id="html5"></div>

                                </div><!-- end col -->
                            </div><!-- end row -->
                        </div>

                        <div class="border border-dashed"></div>

                        <div class="my-4">
                            <div class="row">
                                <div class="col-lg-3">
                                    <h5 class="fs-14">Slider with custom display function</h5>
                                </div><!-- end col -->
                                <div class="col-lg-9">
                                    <div id="nonlinear"></div>
                                    <ngx-slider [(value)]="value1" [(highValue)]="maxVal" [options]="option1"></ngx-slider>
                                </div><!-- end col -->
                            </div><!-- end row -->
                        </div>

                        <div class="border border-dashed"></div>

                        <div class="my-4">
                            <div class="row">
                                <div class="col-lg-3">
                                    <h5 class="fs-14">Slider with ticks</h5>
                                </div><!-- end col -->
                                <div class="col-lg-9">
                                    <ngx-slider [(value)]="tickvalue" [(highValue)]="tickhighValue" [options]="tickoptions"></ngx-slider>
                                </div><!-- end col -->
                            </div><!-- end row -->
                        </div>

                        <div class="border border-dashed"></div>

                        <div class="my-4">
                            <div class="row align-items-center">
                                <div class="col-lg-3">
                                    <h5 class="fs-14">Slider with draggable range</h5>
                                </div><!-- end col -->
                                <div class="col-lg-9">
                                    <ngx-slider [(value)]="draggableminValue" [(highValue)]="draggablemaxValue" [options]="draggableoptions"></ngx-slider>
                                </div><!-- end col -->
                            </div><!-- end row -->
                        </div>

                        <div class="border border-dashed"></div>

                        <div class="my-4">
                            <div class="row">
                                <div class="col-lg-3">
                                    <h5 class="fs-14">Slider with logarithmic scale</h5>
                                </div><!-- end col -->
                                <div class="col-lg-9">
                                    <ngx-slider [(value)]="logscale" [options]="logscaleoptions"></ngx-slider>
                                </div><!-- end col -->
                            </div><!-- end row-->
                        </div>

                        <div class="border border-dashed"></div>

                        <div class="mt-4 mb-5">
                            <div class="row">
                                <div class="col-lg-3">
                                    <h5 class="fs-14">Slider with visible selection bar</h5>
                                </div><!-- end col -->
                                <div class="col-lg-9">
                                    <ngx-slider [(value)]="visibleSelection" [options]="visibleBarOptions"></ngx-slider>
                                </div><!-- end col -->
                            </div><!-- end row-->
                        </div>

                        <div class="border border-dashed"></div>

                        <div class="my-4">
                            <div class="row">
                                <div class="col-lg-3">
                                    <h5 class="fs-14">Slider with ticks and value</h5>
                                </div><!-- end col -->
                                <div class="col-lg-9">
                                    <ngx-slider [(value)]="tickValue" [options]="tickValueoptions"></ngx-slider>
                                </div><!-- end col -->
                            </div><!-- end row -->
                        </div>

                        <div class="border border-dashed"></div>
                    </div>
                </div>

            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->