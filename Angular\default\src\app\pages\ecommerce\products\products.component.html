<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Products" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
  <div class="col-xl-3 col-lg-4">
    <div class="card">
      <div class="card-header">
        <div class="d-flex mb-3">
          <div class="flex-grow-1">
            <h5 class="fs-16">Filters</h5>
          </div>
          <div class="flex-shrink-0">
            <a href="javascript:void(0);" (click)="clearall($event)" class="text-decoration-underline">Clear All</a>
          </div>
        </div>

        <div class="filter-choices-input">
          <ng-select [items]="Default" [multiple]="true" bindLabel="name" [(ngModel)]="multiDefaultOption">
            <ng-template ng-optgroup-tmp let-item="item">
              {{item.country || 'Unnamed group'}}
            </ng-template>
          </ng-select>
        </div>
      </div>

      <div class="accordion accordion-flush">

        <div class="card-body border-bottom">
          <div>
            <p class="text-muted text-uppercase fs-12 fw-medium mb-2">Products</p>
            <ul class="list-unstyled mb-0 filter-list">
              <li class="filter-list-option">
                <a href="javascript:void(0);" class="d-flex py-1 align-items-center" (click)="changeProducts($event,'Kitchen Storage & Containers','Grocery')">
                  <div class="flex-grow-1">
                    <h5 class="fs-13 mb-0 listname">Grocery</h5>
                  </div>
                  @if (grocery > 0) {
                  <div class="flex-shrink-0 ms-2">
                    <span class="badge bg-light text-muted">{{grocery}}</span>
                  </div>
                  }
                </a>
              </li>
              <li class="filter-list-option">
                <a href="javascript:void(0);" class="d-flex py-1 align-items-center" (click)="changeProducts($event,'Clothes','Fashion')">
                  <div class="flex-grow-1">
                    <h5 class="fs-13 mb-0 listname">Fashion</h5>
                  </div>
                  @if (fashion > 0) {
                  <div class="flex-shrink-0 ms-2">
                    <span class="badge bg-light text-muted">{{fashion}}</span>
                  </div>}
                </a>
              </li>
              <li class="filter-list-option">
                <a href="javascript:void(0);" class="d-flex py-1 align-items-center" (click)="changeProducts($event,'Watches','Watches')">
                  <div class="flex-grow-1">
                    <h5 class="fs-13 mb-0 listname">Watches</h5>
                  </div>
                  @if (watches > 0) {
                  <div class="flex-shrink-0 ms-2">
                    <span class="badge bg-light text-muted">{{watches}}</span>
                  </div>
                  }
                </a>
              </li>
              <li class="filter-list-option">
                <a href="javascript:void(0);" class="d-flex py-1 align-items-center" (click)="changeProducts($event,'Electronics','Electronics')">
                  <div class="flex-grow-1">
                    <h5 class="fs-13 mb-0 listname">Electronics</h5>
                  </div>
                  @if (electronics > 0) {
                  <div class="flex-shrink-0 ms-2">
                    <span class="badge bg-light text-muted">{{electronics}}</span>
                  </div>
                  }
                </a>
              </li>
              <li class="filter-list-option">
                <a href="javascript:void(0);" class="d-flex py-1 align-items-center" (click)="changeProducts($event,'Furniture','Furniture')">
                  <div class="flex-grow-1">
                    <h5 class="fs-13 mb-0 listname">Furniture</h5>
                  </div>
                  @if (furniture > 0) {
                  <div class="flex-shrink-0 ms-2">
                    <span class="badge bg-light text-muted">{{furniture}}</span>
                  </div>
                  }
                </a>
              </li>
              <li>
                <a href="javascript:void(0);" class="d-flex py-1 align-items-center" (click)="changeProducts($event,'Bike Accessories','Automotive Accessories')">
                  <div class="flex-grow-1">
                    <h5 class="fs-13 mb-0 listname">Automotive Accessories</h5>
                  </div>
                  @if (accessories > 0) {
                  <div class="flex-shrink-0 ms-2">
                    <span class="badge bg-light text-muted">{{accessories}}</span>
                  </div>
                  }
                </a>
              </li>
              <li>
                <a href="javascript:void(0);" class="d-flex py-1 align-items-center" (click)="changeProducts($event,'Tableware & Dinnerware','Appliances')">
                  <div class="flex-grow-1">
                    <h5 class="fs-13 mb-0 listname">Appliances</h5>
                  </div>
                  @if (appliance > 0) {
                  <div class="flex-shrink-0 ms-2">
                    <span class="badge bg-light text-muted">{{appliance}}</span>
                  </div>
                  }
                </a>
              </li>

              <li>
                <a href="javascript:void(0);" class="d-flex py-1 align-items-center" (click)="changeProducts($event,'Bags, Wallets and Luggage','Kids')">
                  <div class="flex-grow-1">
                    <h5 class="fs-13 mb-0 listname">Kids</h5>
                  </div>
                  @if (kids > 0) {
                  <div class="flex-shrink-0 ms-2">
                    <span class="badge bg-light text-muted">{{kids}}</span>
                  </div>
                  }
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div class="card-body border-bottom">
          <p class="text-muted text-uppercase fs-12 fw-medium mb-4">Price</p>
          <ngx-slider [(value)]="minValue" [(highValue)]="maxValue" [options]="options" id="product-price-range" (valueChange)="valueChange($event,true)" (highValueChange)="valueChange($event,false)"></ngx-slider>
          <div class="formCost d-flex gap-2 align-items-center mt-3">
            <input class="form-control form-control-sm" type="text" id="minCost" value="{{minValue}}" />
            <span class="fw-semibold text-muted">to</span>
            <input class="form-control form-control-sm" type="text" id="maxCost" value="{{maxValue}}" />
          </div>
        </div>

        <div ngbAccordion activeIds="static-1" [closeOthers]="true">
          <div ngbAccordionItem [collapsed]="false">
            <div ngbAccordionHeader class="accordion-header" id="gen-ques-headingOne">
              <button ngbAccordionButton class="border-0 bg-transparent" type="button" data-bs-toggle="collapse" data-bs-target="#gen-ques-collapseOne" aria-expanded="false" aria-controls="gen-ques-collapseOne">
                <span class="text-muted text-uppercase fs-12 fw-medium">Brands</span>
                @if (totalbrand > 0) { <span class="badge bg-success rounded-pill align-middle ms-1">{{totalbrand}}</span>}
              </button>
            </div>
            <div ngbAccordionCollapse>
              <div ngbAccordionBody>
                <div class="accordion-body text-body p-0">
                  <div class="search-box search-box-sm">
                    <input type="text" class="form-control bg-light border-0" placeholder="Search Brands...">
                    <i class="ri-search-line search-icon"></i>
                  </div>
                  <div class="d-flex flex-column gap-2 mt-3">
                    <div class="form-check">
                      <input class="form-check-input" name="checkAll" type="checkbox" value="Boat" id="productBrandRadio5" (change)="changeBrand($event)">
                      <label class="form-check-label" for="productBrandRadio5">Boat</label>
                    </div>
                    <div class="form-check">
                      <input class="form-check-input" name="checkAll" type="checkbox" value="OnePlus" id="productBrandRadio4" (change)="changeBrand($event)">
                      <label class="form-check-label" for="productBrandRadio4">OnePlus</label>
                    </div>
                    <div class="form-check">
                      <input class="form-check-input" name="checkAll" type="checkbox" value="Realme" id="productBrandRadio3" (change)="changeBrand($event)">
                      <label class="form-check-label" for="productBrandRadio3">Realme</label>
                    </div>
                    <div class="form-check">
                      <input class="form-check-input" name="checkAll" type="checkbox" value="Sony" id="productBrandRadio2" (change)="changeBrand($event)">
                      <label class="form-check-label" for="productBrandRadio2">Sony</label>
                    </div>
                    <div class="form-check">
                      <input class="form-check-input" name="checkAll" type="checkbox" value="JBL" id="productBrandRadio1" (change)="changeBrand($event)">
                      <label class="form-check-label" for="productBrandRadio1">JBL</label>
                    </div>
                    <div>
                      <button type="button" class="btn btn-link text-decoration-none text-uppercase fw-medium p-0">1
                        More</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- end accordion-item -->

        <div ngbAccordion>
          <div ngbAccordionItem id="static-2">
            <div ngbAccordionHeader class="accordion-header" id="flush-headingDiscount">
              <button ngbAccordionButton class="border-0 bg-transparent" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseDiscount" aria-expanded="true" aria-controls="flush-collapseDiscount">
                <span class="text-muted text-uppercase fs-12 fw-medium">Discount</span>
                @if (totaldiscount > 0) {
                <span class="badge bg-success rounded-pill align-middle ms-1">{{totaldiscount}}</span>
                }
              </button>
            </div>
            <div ngbAccordionCollapse>
              <div ngbAccordionBody>
                <div class="d-flex flex-column gap-2">
                  <div class="form-check">
                    <input class="form-check-input" name="checkAll" type="checkbox" value="50%" id="productdiscountRadio6" (change)="changeDiscount($event)">
                    <label class="form-check-label" for="productdiscountRadio6">
                      50% or more
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" name="checkAll" type="checkbox" value="40%" id="productdiscountRadio5" (change)="changeDiscount($event)">
                    <label class="form-check-label" for="productdiscountRadio5">
                      40% or more
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" name="checkAll" type="checkbox" value="30%" id="productdiscountRadio4" (change)="changeDiscount($event)">
                    <label class="form-check-label" for="productdiscountRadio4">
                      30% or more
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" name="checkAll" type="checkbox" value="20%" id="productdiscountRadio3" (change)="changeDiscount($event)">
                    <label class="form-check-label" for="productdiscountRadio3">
                      20% or more
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" name="checkAll" type="checkbox" value="10%" id="productdiscountRadio2" (change)="changeDiscount($event)">
                    <label class="form-check-label" for="productdiscountRadio2">
                      10% or more
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" name="checkAll" type="checkbox" value="less10%" id="productdiscountRadio1" (change)="changeDiscount($event)">
                    <label class="form-check-label" for="productdiscountRadio1">
                      Less than 10%
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- end accordion-item -->

        <div ngbAccordion>
          <div ngbAccordionItem ngbAccordionid="static-3">
            <div ngbAccordionHeader class="accordion-header" id="flush-headingRating">
              <button ngbAccordionButton class="border-0 bg-transparent" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseRating" aria-expanded="false" aria-controls="flush-collapseRating">
                <span class="text-muted text-uppercase fs-12 fw-medium">Rating</span>
                @if (totalrate > 0) {
                <span class="badge bg-success rounded-pill align-middle ms-1">{{totalrate}}</span>
                }
              </button>
            </div>
            <div ngbAccordionCollapse>
              <div ngbAccordionBody>
                <div class="d-flex flex-column gap-2">
                  <div class="form-check">
                    <input class="form-check-input" name="checkAll" type="checkbox" value="4" id="productratingRadio4" (change)="changeRating($event,4)">
                    <label class="form-check-label" for="productratingRadio4">
                      <span class="text-muted">
                        <i class="mdi mdi-star text-warning"></i>
                        <i class="mdi mdi-star text-warning"></i>
                        <i class="mdi mdi-star text-warning"></i>
                        <i class="mdi mdi-star text-warning"></i>
                        <i class="mdi mdi-star"></i>
                      </span> 4 & Above
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" name="checkAll" type="checkbox" value="3" id="productratingRadio3" (change)="changeRating($event,3)">
                    <label class="form-check-label" for="productratingRadio3">
                      <span class="text-muted">
                        <i class="mdi mdi-star text-warning"></i>
                        <i class="mdi mdi-star text-warning"></i>
                        <i class="mdi mdi-star text-warning"></i>
                        <i class="mdi mdi-star"></i>
                        <i class="mdi mdi-star"></i>
                      </span> 3 & Above
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" name="checkAll" type="checkbox" value="2" id="productratingRadio2" (change)="changeRating($event,2)">
                    <label class="form-check-label" for="productratingRadio2">
                      <span class="text-muted">
                        <i class="mdi mdi-star text-warning"></i>
                        <i class="mdi mdi-star text-warning"></i>
                        <i class="mdi mdi-star"></i>
                        <i class="mdi mdi-star"></i>
                        <i class="mdi mdi-star"></i>
                      </span> 2 & Above
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" name="checkAll" type="checkbox" value="1" id="productratingRadio1" (change)="changeRating($event,1)">
                    <label class="form-check-label" for="productratingRadio1">
                      <span class="text-muted">
                        <i class="mdi mdi-star text-warning"></i>
                        <i class="mdi mdi-star"></i>
                        <i class="mdi mdi-star"></i>
                        <i class="mdi mdi-star"></i>
                        <i class="mdi mdi-star"></i>
                      </span> 1
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- end accordion-item -->
      </div>
    </div>
    <!-- end card -->
  </div>
  <!-- end col -->


  <div class="col-xl-9 col-lg-8">
    <div>
      <div class="card">
        <div class="card-header border-0">
          <div class="row g-4">
            <div class="col-sm-auto">
              <div>
                <a routerLink="/ecommerce/add-product" class="btn btn-success"><i class="ri-add-line align-bottom me-1"></i> Add Product</a>
              </div>
            </div>
            <div class="col-sm">
              <div class="d-flex justify-content-sm-end">
                <div class="search-box ms-2">
                  <input type="text" name="searchTerm" class="form-control" placeholder="Search Products..." [(ngModel)]="searchTerm" (ngModelChange)="performSearch()">
                  <i class="ri-search-line search-icon"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="card-body">
          <div class="row align-items-center">
            <div class="col">
              <!-- Nav tabs -->
              <ul ngbNav #nav="ngbNav" [activeId]="1" (navChange)="onNavChange($event)" class="nav nav-tabs-custom card-header-tabs border-bottom-0">
                <li [ngbNavItem]="1">
                  <a ngbNavLink>
                    All <span class="badge bg-danger-subtle text-danger align-middle rounded-pill ms-1">{{total}}</span>
                  </a>
                  <ng-template ngbNavContent>
                    <div class="mt-3">
                      <div class="table-card gridjs-border-none">
                        <div class="table-responsive">
                          <table class="table align-middle custom-datatable" id="datatableexample">
                            <thead>
                              <tr class="bg-light text-muted">
                                <th scope="col">#</th>
                                <th scope="col" class="sort" (click)="onSort('image')">
                                  Product</th>
                                <th scope="col" class="sort" (click)="onSort('stock')">
                                  Stock</th>
                                <th scope="col" class="sort" (click)="onSort('price')">
                                  Price</th>
                                <th scope="col" class="sort" (click)="onSort('orders')">
                                  Orders</th>
                                <th scope="col" class="sort" (click)="onSort('rating')">
                                  Rating</th>
                                <th scope="col" class="sort" (click)="onSort('publishedDate')">
                                  Published</th>
                                <th scope="col">Action</th>
                              </tr>
                            </thead>
                            <tbody>
                              @for(data of products; track $index){
                              <tr id=" p_{{data._id}}">
                                <td>
                                  <input class="form-check-input" type="checkbox" name="checkAll" value="{{data._id}}" (change)="onCheckboxChange($event)">
                                </td>
                                <td>
                                  <span>
                                    <div class="d-flex align-items-center">
                                      <div class="flex-shrink-0 me-3">
                                        <div class="avatar-sm bg-light rounded p-1">
                                          <img src="{{url}}/images/products/{{data.image}}" alt="" class="img-fluid d-block">
                                        </div>
                                      </div>
                                      <div class="flex-grow-1">
                                        <h5 class="fs-14 mb-1">
                                          <a routerLink="/ecommerce/product-detail/" class="text-body">{{data.name}}</a>
                                        </h5>
                                        <p class="text-muted mb-0">Category :
                                          <span class="fw-medium">{{data.category}}</span>
                                        </p>
                                      </div>
                                    </div>
                                  </span>
                                </td>
                                <td>
                                  <ngb-highlight [result]="data.stock" [term]="searchTerm"></ngb-highlight>
                                </td>
                                <td>$ <ngb-highlight [result]="data.price" [term]="searchTerm"></ngb-highlight>
                                </td>
                                <td>
                                  <ngb-highlight [result]="data.orders" [term]="searchTerm"></ngb-highlight>
                                </td>
                                <td><span class="badge bg-light text-body fs-12 fw-medium"><i class="mdi mdi-star text-warning me-1"></i>{{data.rating}}</span></td>
                                <td><span>{{data.publishedDate | date :'longDate'}}<small class="text-muted ms-1">{{data.time}}</small></span></td>
                                <td>
                                  <span>
                                    <div class="dropdown" ngbDropdown>
                                      <button class="btn btn-soft-secondary btn-sm dropdown arrow-none" type="button" data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle>
                                        <i class="ri-more-fill"></i>
                                      </button>
                                      <ul class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                                        <li>
                                          <a class="dropdown-item" (click)="godetail($index)"><i class="ri-eye-fill align-bottom me-2 text-muted"></i> View </a>
                                        </li>
                                        <li>
                                          <a class="dropdown-item" routerLink="/ecommerce/add-product"><i class="ri-pencil-fill align-bottom me-2 text-muted"></i> Edit</a>
                                        </li>
                                        <li class="dropdown-divider"></li>
                                        <li>
                                          <a class="dropdown-item" href="javascript:void(0);" data-bs-toggle="modal" data-bs-target="#removeItemModal" (click)="confirm(content,data._id)"><i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> Delete</a>
                                        </li>
                                      </ul>
                                    </div>
                                  </span>
                                </td>
                              </tr>
                              }
                            </tbody>
                          </table>
                        </div>
                        <div class="px-3 ">
                          <div class="row justify-content-md-between align-items-md-center g-0 pagination">
                            <div class="col-sm-12 col-md-5">
                              <div class="dataTables_info mb-2" id="tickets-table_info" role="status" aria-live="polite">
                                Showing
                                {{service.startIndex}} to
                                {{service.endIndex}} of {{allproduct?.length}}
                                entries
                              </div>
                            </div>
                            <!-- Pagination -->
                            <div class="col-sm-12 col-md-5">
                              <div class="text-md-right float-md-end pagination-rounded gridjs-pagination mb-3">
                                <ngb-pagination [collectionSize]="allproduct?.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                                </ngb-pagination>
                              </div>
                            </div>
                            <!-- End Pagination -->
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="tab-pane alltable d-none" id="productnav-draft" role="tabpanel">
                      <div class="py-4 text-center">
                        <div>
                          <lord-icon src="https://cdn.lordicon.com/msoeawqm.json" trigger="loop" colors="primary:#405189,secondary:#0ab39c" style="width:72px;height:72px">
                          </lord-icon>
                        </div>
                        <div class="mt-4">
                          <h5>Sorry! No Result Found</h5>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </li>
                <li [ngbNavItem]="2">
                  <a ngbNavLink>
                    Published <span class="badge bg-danger-subtle text-danger align-middle rounded-pill ms-1">{{publishedproduct?.length}}</span>
                  </a>
                  <ng-template ngbNavContent>
                    <div class="mt-3">
                      <div class="table-card gridjs-border-none">
                        <div class="table-responsive">
                          <table class="table align-middle">
                            <thead>
                              <tr class="bg-light text-muted">
                                <th scope="col">#</th>
                                <th scope="col" class="sort" (click)="onSort('name')">
                                  Product</th>
                                <th scope="col" class="sort" (click)="onSort('stock')">
                                  Stock</th>
                                <th scope="col" class="sort" (click)="onSort('price')">
                                  Price</th>
                                <th scope="col" class="sort" (click)="onSort('orders')">
                                  Orders</th>
                                <th scope="col" class="sort" (click)="onSort('rating')">
                                  Rating</th>
                                <th scope="col" class="sort" (click)="onSort('publishedDate')">
                                  Published</th>
                                <th scope="col">Action</th>
                              </tr>
                            </thead>
                            <tbody>
                              @for(data of publishedproduct; track $index){
                              <tr id="p_{{data._id}}">
                                <td>
                                  <input class="form-check-input" type="checkbox" name="checkAll" value="{{data._id}}" (change)="onCheckboxChange($event)">
                                </td>
                                <td>
                                  <span>
                                    <div class="d-flex align-items-center">
                                      <div class="flex-shrink-0 me-3">
                                        <div class="avatar-sm bg-light rounded p-1">
                                          <img src="{{url}}/images/products/{{data.image}}" alt="" class="img-fluid d-block">
                                        </div>
                                      </div>
                                      <div class="flex-grow-1">
                                        <h5 class="fs-14 mb-1">
                                          <a routerLink="/ecommerce/product-detail/" class="text-body">{{data.name}}</a>
                                        </h5>
                                        <p class="text-muted mb-0">Category :
                                          <span class="fw-medium">{{data.category}}</span>
                                        </p>
                                      </div>
                                    </div>
                                  </span>
                                </td>
                                <td>
                                  <ngb-highlight [result]="data.stock" [term]="searchTerm"></ngb-highlight>
                                </td>
                                <td>$ <ngb-highlight [result]="data.price" [term]="searchTerm">
                                  </ngb-highlight>
                                </td>
                                <td>
                                  <ngb-highlight [result]="data.orders" [term]="searchTerm"></ngb-highlight>
                                </td>
                                <td><span class="badge bg-light text-body fs-12 fw-medium"><i class="mdi mdi-star text-warning me-1"></i>{{data.rating}}</span></td>
                                <td><span>{{data.publishedDate | date :'longDate'}}<small class="text-muted ms-1">{{data.time}}</small></span></td>
                                <td>
                                  <span>
                                    <div class="dropdown" ngbDropdown>
                                      <button class="btn btn-soft-secondary btn-sm dropdown arrow-none" type="button" data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle>
                                        <i class="ri-more-fill"></i>
                                      </button>
                                      <ul class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                                        <li>
                                          <a class="dropdown-item" routerLink="/ecommerce/product-detail/"><i class="ri-eye-fill align-bottom me-2 text-muted"></i> View </a>
                                        </li>
                                        <li>
                                          <a class="dropdown-item" routerLink="/ecommerce/add-product"><i class="ri-pencil-fill align-bottom me-2 text-muted"></i> Edit</a>
                                        </li>
                                        <li class="dropdown-divider"></li>
                                        <li>
                                          <a class="dropdown-item" href="javascript:void(0);" data-bs-toggle="modal" data-bs-target="#removeItemModal" (click)="confirm(content,data._id)"><i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> Delete</a>
                                        </li>
                                      </ul>
                                    </div>
                                  </span>
                                </td>
                              </tr>
                              }
                            </tbody>
                          </table>
                        </div>
                        <div class="px-3">
                          <div class="row justify-content-md-between align-items-md-center g-0 pagination">
                            <div class="col-sm-12 col-md-6">
                              <div class="dataTables_info mb-2" id="tickets-table_info" role="status" aria-live="polite">
                                Showing
                                {{service.startIndex}} to
                                {{service.endIndex}} of {{publishedproduct?.length}}
                                entries
                              </div>
                            </div>
                            <!-- Pagination -->
                            <div class="col-sm-12 col-md-6">
                              <div class="text-md-right float-md-end pagination-rounded gridjs-pagination">
                                <ngb-pagination [collectionSize]="publishedproduct?.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                                </ngb-pagination>
                              </div>
                            </div>
                            <!-- End Pagination -->
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="tab-pane alltable d-none" id="productnav-draft" role="tabpanel">
                      <div class="py-4 text-center">
                        <div>
                          <lord-icon src="https://cdn.lordicon.com/msoeawqm.json" trigger="loop" colors="primary:#405189,secondary:#0ab39c" style="width:72px;height:72px">
                          </lord-icon>
                        </div>
                        <div class="mt-4">
                          <h5>Sorry! No Result Found</h5>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </li>
                <li [ngbNavItem]="3">
                  <a ngbNavLink>
                    Draft
                  </a>
                  <ng-template ngbNavContent>
                    <div class="tab-pane" id="productnav-draft" role="tabpanel">
                      <div class="py-4 text-center">
                        <div>
                          <lord-icon src="https://cdn.lordicon.com/msoeawqm.json" trigger="loop" colors="primary:#405189,secondary:#0ab39c" style="width:72px;height:72px">
                          </lord-icon>
                        </div>
                        <div class="mt-4">
                          <h5>Sorry! No Result Found</h5>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </li>
              </ul>
            </div>
            <div class="col-auto">
              <div id="selection-element">
                <div class="my-n1 d-flex align-items-center text-muted">
                  Select <div id="select-content" class="text-body fw-semibold px-1">1</div> Result <button type="button" class="btn btn-link link-danger p-0 ms-3" data-bs-toggle="modal" data-bs-target="#removeItemModal" (click)="confirm(content,'')">Remove</button>
                </div>
              </div>

            </div>

            <!-- Tab panes -->
            <div class="tab-content text-muted mt-3">
              <div [ngbNavOutlet]="nav"></div>
            </div>
            <div id="elmLoader">
              <div class="spinner-border text-primary avatar-sm" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
          </div>

        </div>
      </div>
      <!-- end card -->
    </div>
  </div>
  <!-- end col -->
</div>
<!-- end row -->

<!-- removeItemModal -->
<ng-template #content let-modal>
  <div class="modal-content">
    <div class="modal-header">
      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="btn-close" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body">
      <div class="mt-2 text-center">
        <lord-icon src="https://cdn.lordicon.com/gsqxdxog.json" trigger="loop" colors="primary:#f7b84b,secondary:#f06548" style="width:100px;height:100px"></lord-icon>
        <div class="mt-4 pt-2 fs-15 mx-4 mx-sm-5">
          <h4>Are you Sure ?</h4>
          <p class="text-muted mx-4 mb-0">Are you Sure You want to Remove this Product ?</p>
        </div>
      </div>
      <div class="d-flex gap-2 justify-content-center mt-4 mb-2">
        <button type="button" class="btn w-sm btn-light" data-bs-dismiss="modal" (click)="modal.close('Close click')">Close</button>
        <button type="button" class="btn w-sm btn-danger " id="delete-product" (click)="deleteData(deleteId)" (click)="modal.close('Close click')">Yes, Delete It!</button>
      </div>
    </div>
  </div><!-- /.modal-content -->
</ng-template>