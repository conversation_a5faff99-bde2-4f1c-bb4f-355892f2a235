<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Companies" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex align-items-center flex-wrap gap-2">
                    <div class="flex-grow-1">
                        <button class="btn btn-info add-btn" data-bs-toggle="modal" data-bs-target="#showModal" (click)="openModal(content)"><i class="ri-add-fill me-1 align-bottom"></i> Add
                            Company</button>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="hstack text-nowrap gap-2" ngbDropdown>
                            <button class="btn btn-soft-danger" id="remove-actions" style="display: none" (click)="deleteMultiple(deleteModel)"><i class="ri-delete-bin-2-line"></i></button>
                            <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#addmembers"><i class="ri-filter-2-line me-1 align-bottom"></i> Filters</button>
                            <button class="btn btn-soft-success" (click)="csvFileExport()">Export</button>
                            <button type="button" id="dropdownMenuLink1" data-bs-toggle="dropdown" aria-expanded="false" class="btn btn-soft-info arrow-none" ngbDropdownToggle><i class="ri-more-2-fill"></i></button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink1" ngbDropdownMenu>
                                <li><a class="dropdown-item" href="javascript:void(0);">All</a></li>
                                <li><a class="dropdown-item" href="javascript:void(0);">Last Week</a></li>
                                <li><a class="dropdown-item" href="javascript:void(0);">Last Month</a></li>
                                <li><a class="dropdown-item" href="javascript:void(0);">Last Year</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end col-->
    <div class="col-xxl-9">
        <div class="card" id="companyList">
            <div class="card-header">
                <div class="row g-2">
                    <div class="col-md-3">
                        <div class="search-box">
                            <input type="text" name="searchTerm" class="form-control" placeholder="Search for company..." [(ngModel)]="searchTerm" (ngModelChange)="performSearch()">
                            <i class="ri-search-line search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-auto ms-auto">
                        <div class="d-flex align-items-center gap-2">
                            <span class="text-muted text-nowrap">Sort by: </span>
                            <select class="form-control mb-0" data-choices data-choices-search-false id="choices-single-default" (click)="SortFilter();">
                                <option value="Downer">Owner</option>
                                <option value="DcompanyName">Company</option>
                                <option value="Dlocation">Location</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div>
                    <div class="table-responsive table-card mb-2">
                        <table class="table">
                            <thead>
                                <tr class="bg-light">
                                    <th scope="col" style="width: 50px;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkAll" value="option" [(ngModel)]="masterSelected" (change)="checkUncheckAll($event)">
                                        </div>
                                    </th>
                                    <th class="sort" (click)="onSort('name')">Company Name
                                    </th>
                                    <th class="sort" (click)="onSort('owner')">Owner</th>
                                    <th class="sort" (click)="onSort('industry_type')">
                                        Industry Type</th>
                                    <th class="sort" (click)="onSort('star_value')">Rating
                                    </th>
                                    <th class="sort" (click)="onSort('location')">Location
                                    </th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (data of company | sortBy:sortBy:sortField; track $index) {
                                <tr id="c_{{data._id}}">
                                    <th scope="row">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="checkAll" value="{{data._id}}" [(ngModel)]="data.state" (change)="onCheckboxChange($event)">
                                        </div>
                                    </th>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0">
                                                <img src="assets/images/{{data.image_src}}" alt="" class="avatar-xxs rounded-circle image_src">
                                            </div>
                                            <div class="flex-grow-1 ms-2 name">{{data.name}}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.owner" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.industry_type" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td><span class="star_value">{{data.star_value}} </span> <i class="ri-star-fill text-warning align-bottom"></i></td>
                                    <td>
                                        <ngb-highlight [result]="data.location" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ul class="list-inline hstack gap-2 mb-0">
                                            <li class="list-inline-item edit" ngbTooltip="Call" placement="top">
                                                <a href="javascript:void(0);" class="text-muted d-inline-block">
                                                    <i class="ri-phone-line fs-16"></i>
                                                </a>
                                            </li>
                                            <li class="list-inline-item edit" ngbTooltip="Message" placement="top">
                                                <a href="javascript:void(0);" class="text-muted d-inline-block">
                                                    <i class="ri-question-answer-line fs-16"></i>
                                                </a>
                                            </li>
                                            <li class="list-inline-item" ngbTooltip="View" placement="top">
                                                <a href="javascript:void(0);" (click)="viewDataGet($index)"><i class="ri-eye-fill align-bottom text-muted"></i></a>
                                            </li>
                                            <li class="list-inline-item" ngbTooltip="Edit" placement="top">
                                                <a class="edit-item-btn" data-bs-toggle="modal" (click)="editDataGet($index,content)"><i class="ri-pencil-fill align-bottom text-muted"></i></a>
                                            </li>
                                            <li class="list-inline-item me-0" ngbTooltip="Delete" placement="top">
                                                <a class="remove-item-btn" data-bs-toggle="modal" (click)="confirm(deleteModel,data._id)">
                                                    <i class="ri-delete-bin-fill align-bottom text-muted"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-end mt-3">
                        <!-- Pagination -->
                        <ngb-pagination [collectionSize]="allcompany?.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                        </ngb-pagination>
                        <!-- End Pagination -->
                    </div>
                    <div id="elmLoader">
                        <div class="spinner-border text-primary avatar-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>

                <!-- Companies Model -->
                <ng-template #content role="document" let-modal>
                    <div class="modal-header bg-info-subtle p-3">
                        <h5 class="modal-title" class="tablelist-form" autocomplete="off" id="exampleModalLabel">Add
                            Company</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="close-modal" (click)="modal.dismiss('Cross click')"></button>
                    </div>
                    <form (ngSubmit)="saveUser()" [formGroup]="companiesForm">
                        <div class="modal-body">
                            <input type="hidden" id="id-field" />
                            <div class="row g-3">
                                <div class="col-lg-12">
                                    <input type="hidden" name="id" value="" formControlName="_id" />
                                    <div class="text-center">
                                        <div class="position-relative d-inline-block">
                                            <div class="position-absolute bottom-0 end-0">
                                                <label for="company-logo-input" class="mb-0" data-bs-toggle="tooltip" data-bs-placement="right" title="Select Image">
                                                    <div class="avatar-xs cursor-pointer">
                                                        <div class="avatar-title bg-light border rounded-circle text-muted">
                                                            <i class="ri-image-fill"></i>
                                                        </div>
                                                    </div>
                                                </label>
                                                <input class="form-control d-none" id="company-logo-input" type="file" accept="image/png, image/gif, image/jpeg" (change)="fileChange($event)">
                                            </div>
                                            <div class="avatar-lg p-1">
                                                <div class="avatar-title bg-light rounded-circle">
                                                    <img src="assets/images/users/multi-user.jpg" id="companylogo-img" class="avatar-md rounded-circle object-fit-cover" />
                                                </div>
                                            </div>
                                        </div>
                                        <h5 class="fs-13 mt-3">Company Logo</h5>
                                    </div>
                                    <div>
                                        <label for="name-field" class="form-label">Name</label>
                                        <input type="text" id="customername-field" class="form-control" placeholder="Enter company name" required formControlName="name" [ngClass]="{ 'is-invalid': submitted && form['name'].errors }" />
                                        @if(submitted && form['name'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['name'].errors['required']){
                                            <div>Name is required</div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div>
                                        <label for="owner-field" class="form-label">Owner Name</label>
                                        <input type="text" id="owner-field" class="form-control" placeholder="Enter owner name" required formControlName="owner" [ngClass]="{ 'is-invalid': submitted && form['owner'].errors }" />
                                        @if(submitted && form['owner'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['owner'].errors['required']){
                                            <div>Owner Name is required</div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div>
                                        <label for="industry_type-field" class="form-label">Industry Type</label>
                                        <select class="form-control" id="industry_type-field" formControlName="industry_type" [ngClass]="{ 'is-invalid': submitted && form['industry_type'].errors }">
                                            <option value="">Select industry type</option>
                                            <option value="Computer Industry">Computer Industry</option>
                                            <option value="Chemical Industries">Chemical Industries</option>
                                            <option value="Health Services">Health Services</option>
                                            <option value="Telecommunications Services">Telecommunications Services
                                            </option>
                                            <option value="Textiles: Clothing, Footwear">Textiles: Clothing, Footwear
                                            </option>
                                        </select>
                                        @if(submitted && form['industry_type'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['industry_type'].errors['required']){
                                            <div>Industry Type is
                                                required</div>
                                            }
                                        </div>}
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div>
                                        <label for="star_value-field" class="form-label">Rating</label>
                                        <input type="text" id="star_value-field" class="form-control" placeholder="Enter rating" required formControlName="star_value" [ngClass]="{ 'is-invalid': submitted && form['star_value'].errors }" />
                                        @if(submitted && form['star_value'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['star_value'].errors['required']){
                                            <div>Rating Type is required
                                            </div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div>
                                        <label for="location-field" class="form-label">location</label>
                                        <input type="text" id="location-field" class="form-control" placeholder="Enter loation" required formControlName="location" [ngClass]="{ 'is-invalid': submitted && form['location'].errors }" />
                                        @if(submitted && form['location'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['location'].errors['required']){
                                            <div>Location Type is required
                                            </div>}
                                        </div>}
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div>
                                        <label for="employee-field" class="form-label">Employee</label>
                                        <input type="text" id="employee-field" class="form-control" placeholder="Enter employee" required formControlName="employee" [ngClass]="{ 'is-invalid': submitted && form['employee'].errors }" />
                                        @if(submitted && form['employee'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['employee'].errors['required']){
                                            <div>Employee Type is required
                                            </div>}
                                        </div>}
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div>
                                        <label for="website-field" class="form-label">Website</label>
                                        <input type="text" id="website-field" class="form-control" placeholder="Enter website" required formControlName="website" [ngClass]="{ 'is-invalid': submitted && form['website'].errors }" />
                                        @if(submitted && form['website'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['website'].errors['required']){
                                            <div>Website Type is required
                                            </div>}
                                        </div>}
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div>
                                        <label for="contact_email-field" class="form-label">Contact Email</label>
                                        <input type="text" id="contact_email-field" class="form-control" placeholder="Enter contact email" required formControlName="contact_email" [ngClass]="{ 'is-invalid': submitted && form['contact_email'].errors }" />
                                        @if(submitted && form['contact_email'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['contact_email'].errors['required']){
                                            <div>Contact Email Type is
                                                required</div>
                                            }
                                        </div>}
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div>
                                        <label for="since-field" class="form-label">Since</label>
                                        <input type="text" id="since-field" class="form-control" placeholder="Enter since" required formControlName="since" [ngClass]="{ 'is-invalid': submitted && form['since'].errors }" />
                                        @if(submitted && form['since'].errors){
                                        <div class="invalid-feedback" align="left">
                                            @if(form['since'].errors['required']){
                                            <div>Since Type is required</div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="hstack gap-2 justify-content-end">
                                <button type="button" class="btn btn-light" data-bs-dismiss="modal" (click)="modal.close('Close click')">Close</button>
                                <button type="submit" class="btn btn-success" id="add-btn">Add Company</button>
                            </div>
                        </div>
                    </form>
                </ng-template>

            </div>
        </div>
        <!--end card-->
    </div>
    <!--end col-->
    <div class="col-xxl-3">
        <div class="card" id="company-view-detail">
            <div class="card-body text-center company-details">
                <div class="position-relative d-inline-block">
                    <div class="avatar-md">
                        <div class="avatar-title bg-light rounded-circle">
                            <img src="assets/images/brands/mail_chimp.png" alt="" class="avatar-sm rounded-circle object-fit-cover">
                        </div>
                    </div>
                </div>
                <h5 class="mt-3 mb-1">Syntyce Solution</h5>
                <p class="text-muted">Michael Morris</p>

                <ul class="list-inline mb-0">
                    <li class="list-inline-item avatar-xs">
                        <a href="javascript:void(0);" class="avatar-title bg-success-subtle text-success fs-15 rounded">
                            <i class="ri-global-line"></i>
                        </a>
                    </li>
                    <li class="list-inline-item avatar-xs">
                        <a href="javascript:void(0);" class="avatar-title bg-danger-subtle text-danger fs-15 rounded">
                            <i class="ri-mail-line"></i>
                        </a>
                    </li>
                    <li class="list-inline-item avatar-xs">
                        <a href="javascript:void(0);" class="avatar-title bg-warning-subtle text-warning fs-15 rounded">
                            <i class="ri-question-answer-line"></i>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <h6 class="text-muted text-uppercase fw-semibold mb-3">Information</h6>
                <p class="text-muted mb-4">A company incurs fixed and variable costs such as the purchase of raw
                    materials, salaries and overhead, as explained by AccountingTools, Inc. Business owners have the
                    discretion to determine the actions.</p>
                <div class="table-responsive table-card">
                    <table class="table table-borderless mb-0">
                        <tbody>
                            <tr>
                                <td class="fw-medium" scope="row">Industry Type</td>
                                <td class="industry_type">Chemical Industries</td>
                            </tr>
                            <tr>
                                <td class="fw-medium" scope="row">Location</td>
                                <td class="location">Damascus, Syria</td>
                            </tr>
                            <tr>
                                <td class="fw-medium" scope="row">Employee</td>
                                <td class="employee">10-50</td>
                            </tr>
                            <tr>
                                <td class="fw-medium" scope="row">Rating</td>
                                <td><span class="rating">4.0 </span> <i class="ri-star-fill text-warning align-bottom"></i></td>
                            </tr>
                            <tr>
                                <td class="fw-medium" scope="row">Website</td>
                                <td>
                                    <a href="javascript:void(0);" class="link-primary text-decoration-underline website">www.syntycesolution.com</a>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-medium" scope="row">Contact Email</td>
                                <td class="email">info&#64;syntycesolution.com</td>
                            </tr>
                            <tr>
                                <td class="fw-medium" scope="row">Since</td>
                                <td class="since">1995</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <!--end card-->
    </div>
    <!--end col-->
</div>
<!--end row-->

<!-- removeItemModal -->
<ng-template #deleteModel let-modal>
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="deleteRecord-close" (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body">
            <div class="mt-2 text-center">
                <lord-icon src="https://cdn.lordicon.com/gsqxdxog.json" trigger="loop" colors="primary:#405189,secondary:#f06548" style="width:90px;height:90px"></lord-icon>
                <div class="mt-4 pt-2 fs-15 mx-4 mx-sm-5">
                    <h4>You are about to delete a contact ?</h4>
                    <p class="text-muted mx-4 mb-0">Deleting your contact will remove all of your information from our
                        database.</p>
                </div>
            </div>
            <div class="d-flex gap-2 justify-content-center mt-4 mb-2">
                <button class="btn btn-link link-success fw-medium text-decoration-none" data-bs-dismiss="modal" (click)="modal.close('Close click')"><i class="ri-close-line me-1 align-middle"></i> Close</button>
                <button type="button" class="btn w-sm btn-danger " id="delete-product" (click)="deleteData(deleteId)" (click)="modal.close('Close click')">Yes, Delete It!</button>
            </div>
        </div>
    </div><!-- /.modal-content -->
</ng-template>