/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["mni-Beng", [["নুমাং", "PM"], u, ["এ এম", "পি এম"]], [["নুমাং", "PM"], u, u], [["নোং", "নিং", "লৈবা", "য়ুম", "শগো", "ইরা", "থাং"], ["নোংমাইজিং", "নিংথৌকাবা", "লৈবাকপোকপা", "য়ুমশকৈশা", "শগোলশেন", "ইরাই", "থাংজ"], u, u], [["নো", "নিং", "লৈ", "য়ুম", "শগ", "ইরা", "থাং"], ["নোংমাইজিং", "নিংথৌকাবা", "লৈবাকপোকপা", "য়ুমশকৈশা", "শগোলশেন", "ইরাই", "থাংজ"], u, u], [["জা", "ফে", "মার", "এপ", "মে", "জুন", "জুল", "আ", "সে", "ওক", "নব", "ডি"], ["জানুৱারি", "ফেব্রুৱারি", "মার্চ", "এপ্রিল", "মে", "জুন", "জুলাই", "আগস্ট", "সেপ্টেম্বর", "ওক্টোবর", "নভেম্বর", "ডিসেম্বর"], u], [["জা", "ফে", "মার", "এপ", "মে", "জুন", "জুল", "আ", "সে", "ও", "নব", "ডি"], ["জানু", "ফেব্রু", "মার", "এপ্রি", "মে", "জুন", "জুলা", "আগ", "সেপ্ট", "ওক্টো", "নভে", "ডিসে"], ["জানুৱারি", "ফেব্রুৱারি", "মার্চ", "এপ্রিল", "মে", "জুন", "জুলাই", "ওগষ্ট", "সেপ্টেম্বর", "ওক্টোবর", "নবেম্বর", "ডিসেম্বর"]], [["খৃ: মমাং", "খৃ: মতুং"], u, u], 0, [0, 0], ["d/M/yy", "MMM d, y", "MMMM d, y", "MMMM d, y, EEEE"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} গী {0} দা", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "INR", "₹", "ইন্দিয়ান রুপী", { "JPY": ["JP¥", "¥"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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