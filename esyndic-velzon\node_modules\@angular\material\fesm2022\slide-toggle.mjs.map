{"version": 3, "file": "slide-toggle.mjs", "sources": ["../../../../../../src/material/slide-toggle/slide-toggle-config.ts", "../../../../../../src/material/slide-toggle/slide-toggle.ts", "../../../../../../src/material/slide-toggle/slide-toggle.html", "../../../../../../src/material/slide-toggle/slide-toggle-required-validator.ts", "../../../../../../src/material/slide-toggle/module.ts", "../../../../../../src/material/slide-toggle/slide-toggle_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {InjectionToken} from '@angular/core';\nimport {ThemePalette} from '@angular/material/core';\n\n/** Default `mat-slide-toggle` options that can be overridden. */\nexport interface MatSlideToggleDefaultOptions {\n  /** Whether toggle action triggers value changes in slide toggle. */\n  disableToggleValue?: boolean;\n\n  /** Default color for slide toggles. */\n  color?: ThemePalette;\n\n  /** Whether to hide the icon inside the slide toggle. */\n  hideIcon?: boolean;\n}\n\n/** Injection token to be used to override the default options for `mat-slide-toggle`. */\nexport const MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS = new InjectionToken<MatSlideToggleDefaultOptions>(\n  'mat-slide-toggle-default-options',\n  {\n    providedIn: 'root',\n    factory: () => ({disableToggleValue: false, hideIcon: false}),\n  },\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  AfterContentInit,\n  Attribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  forwardRef,\n  Inject,\n  Input,\n  OnDestroy,\n  Optional,\n  Output,\n  ViewChild,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';\nimport {ANIMATION_MODULE_TYPE} from '@angular/platform-browser/animations';\nimport {FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {\n  MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS,\n  MatSlideToggleDefaultOptions,\n} from './slide-toggle-config';\nimport {\n  CanColor,\n  CanDisable,\n  CanDisableRipple,\n  HasTabIndex,\n  mixinColor,\n  mixinDisabled,\n  mixinDisableRipple,\n  mixinTabIndex,\n} from '@angular/material/core';\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\n\n/** @docs-private */\nexport const MAT_SLIDE_TOGGLE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSlideToggle),\n  multi: true,\n};\n\n/** Change event object emitted by a slide toggle. */\nexport class MatSlideToggleChange {\n  constructor(\n    /** The source slide toggle of the event. */\n    public source: MatSlideToggle,\n    /** The new `checked` value of the slide toggle. */\n    public checked: boolean,\n  ) {}\n}\n\n// Increasing integer for generating unique ids for slide-toggle components.\nlet nextUniqueId = 0;\n\n// Boilerplate for applying mixins to MatSlideToggle.\n/** @docs-private */\nconst _MatSlideToggleMixinBase = mixinTabIndex(\n  mixinColor(\n    mixinDisableRipple(\n      mixinDisabled(\n        class {\n          constructor(public _elementRef: ElementRef) {}\n        },\n      ),\n    ),\n  ),\n);\n\n@Directive()\nexport abstract class _MatSlideToggleBase<T>\n  extends _MatSlideToggleMixinBase\n  implements\n    OnDestroy,\n    AfterContentInit,\n    ControlValueAccessor,\n    CanDisable,\n    CanColor,\n    HasTabIndex,\n    CanDisableRipple\n{\n  protected _onChange = (_: any) => {};\n  private _onTouched = () => {};\n\n  protected _uniqueId: string;\n  private _required: boolean = false;\n  private _checked: boolean = false;\n\n  protected abstract _createChangeEvent(isChecked: boolean): T;\n\n  abstract focus(options?: FocusOptions, origin?: FocusOrigin): void;\n\n  /** Whether noop animations are enabled. */\n  _noopAnimations: boolean;\n\n  /** Whether the slide toggle is currently focused. */\n  _focused: boolean;\n\n  /** Name value will be applied to the input element if present. */\n  @Input() name: string | null = null;\n\n  /** A unique id for the slide-toggle input. If none is supplied, it will be auto-generated. */\n  @Input() id: string;\n\n  /** Whether the label should appear after or before the slide-toggle. Defaults to 'after'. */\n  @Input() labelPosition: 'before' | 'after' = 'after';\n\n  /** Used to set the aria-label attribute on the underlying input element. */\n  @Input('aria-label') ariaLabel: string | null = null;\n\n  /** Used to set the aria-labelledby attribute on the underlying input element. */\n  @Input('aria-labelledby') ariaLabelledby: string | null = null;\n\n  /** Used to set the aria-describedby attribute on the underlying input element. */\n  @Input('aria-describedby') ariaDescribedby: string;\n\n  /** Whether the slide-toggle is required. */\n  @Input()\n  get required(): boolean {\n    return this._required;\n  }\n\n  set required(value: BooleanInput) {\n    this._required = coerceBooleanProperty(value);\n  }\n\n  /** Whether the slide-toggle element is checked or not. */\n  @Input()\n  get checked(): boolean {\n    return this._checked;\n  }\n\n  set checked(value: BooleanInput) {\n    this._checked = coerceBooleanProperty(value);\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Whether to hide the icon inside of the slide toggle. */\n  @Input()\n  get hideIcon(): boolean {\n    return this._hideIcon;\n  }\n  set hideIcon(value: BooleanInput) {\n    this._hideIcon = coerceBooleanProperty(value);\n  }\n  private _hideIcon = false;\n\n  /** An event will be dispatched each time the slide-toggle changes its value. */\n  @Output() readonly change: EventEmitter<T> = new EventEmitter<T>();\n\n  /**\n   * An event will be dispatched each time the slide-toggle input is toggled.\n   * This event is always emitted when the user toggles the slide toggle, but this does not mean\n   * the slide toggle's value has changed.\n   */\n  @Output() readonly toggleChange: EventEmitter<void> = new EventEmitter<void>();\n\n  /** Returns the unique id for the visual hidden input. */\n  get inputId(): string {\n    return `${this.id || this._uniqueId}-input`;\n  }\n\n  constructor(\n    elementRef: ElementRef,\n    protected _focusMonitor: FocusMonitor,\n    protected _changeDetectorRef: ChangeDetectorRef,\n    tabIndex: string,\n    public defaults: MatSlideToggleDefaultOptions,\n    animationMode: string | undefined,\n    idPrefix: string,\n  ) {\n    super(elementRef);\n    this.tabIndex = parseInt(tabIndex) || 0;\n    this.color = this.defaultColor = defaults.color || 'accent';\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    this.id = this._uniqueId = `${idPrefix}${++nextUniqueId}`;\n    this._hideIcon = defaults.hideIcon ?? false;\n  }\n\n  ngAfterContentInit() {\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n      if (focusOrigin === 'keyboard' || focusOrigin === 'program') {\n        this._focused = true;\n        this._changeDetectorRef.markForCheck();\n      } else if (!focusOrigin) {\n        // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n        // Angular does not expect events to be raised during change detection, so any state\n        // change (such as a form control's ng-touched) will cause a changed-after-checked error.\n        // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n        // telling the form control it has been touched until the next tick.\n        Promise.resolve().then(() => {\n          this._focused = false;\n          this._onTouched();\n          this._changeDetectorRef.markForCheck();\n        });\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n  }\n\n  /** Implemented as part of ControlValueAccessor. */\n  writeValue(value: any): void {\n    this.checked = !!value;\n  }\n\n  /** Implemented as part of ControlValueAccessor. */\n  registerOnChange(fn: any): void {\n    this._onChange = fn;\n  }\n\n  /** Implemented as part of ControlValueAccessor. */\n  registerOnTouched(fn: any): void {\n    this._onTouched = fn;\n  }\n\n  /** Implemented as a part of ControlValueAccessor. */\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Toggles the checked state of the slide-toggle. */\n  toggle(): void {\n    this.checked = !this.checked;\n    this._onChange(this.checked);\n  }\n\n  /**\n   * Emits a change event on the `change` output. Also notifies the FormControl about the change.\n   */\n  protected _emitChangeEvent() {\n    this._onChange(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n  }\n}\n\n@Component({\n  selector: 'mat-slide-toggle',\n  templateUrl: 'slide-toggle.html',\n  styleUrls: ['slide-toggle.css'],\n  inputs: ['disabled', 'disableRipple', 'color', 'tabIndex'],\n  host: {\n    'class': 'mat-mdc-slide-toggle',\n    '[id]': 'id',\n    // Needs to be removed since it causes some a11y issues (see #21266).\n    '[attr.tabindex]': 'null',\n    '[attr.aria-label]': 'null',\n    '[attr.name]': 'null',\n    '[attr.aria-labelledby]': 'null',\n    '[class.mat-mdc-slide-toggle-focused]': '_focused',\n    '[class.mat-mdc-slide-toggle-checked]': 'checked',\n    '[class._mat-animation-noopable]': '_noopAnimations',\n  },\n  exportAs: 'matSlideToggle',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [MAT_SLIDE_TOGGLE_VALUE_ACCESSOR],\n})\nexport class MatSlideToggle extends _MatSlideToggleBase<MatSlideToggleChange> {\n  /** Unique ID for the label element. */\n  _labelId: string;\n\n  /** Returns the unique id for the visual hidden button. */\n  get buttonId(): string {\n    return `${this.id || this._uniqueId}-button`;\n  }\n\n  /** Reference to the MDC switch element. */\n  @ViewChild('switch') _switchElement: ElementRef<HTMLElement>;\n\n  constructor(\n    elementRef: ElementRef,\n    focusMonitor: FocusMonitor,\n    changeDetectorRef: ChangeDetectorRef,\n    @Attribute('tabindex') tabIndex: string,\n    @Inject(MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS)\n    defaults: MatSlideToggleDefaultOptions,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n  ) {\n    super(\n      elementRef,\n      focusMonitor,\n      changeDetectorRef,\n      tabIndex,\n      defaults,\n      animationMode,\n      'mat-mdc-slide-toggle-',\n    );\n    this._labelId = this._uniqueId + '-label';\n  }\n\n  /** Method being called whenever the underlying button is clicked. */\n  _handleClick() {\n    this.toggleChange.emit();\n\n    if (!this.defaults.disableToggleValue) {\n      this.checked = !this.checked;\n      this._onChange(this.checked);\n      this.change.emit(new MatSlideToggleChange(this, this.checked));\n    }\n  }\n\n  /** Focuses the slide-toggle. */\n  focus(): void {\n    this._switchElement.nativeElement.focus();\n  }\n\n  protected _createChangeEvent(isChecked: boolean) {\n    return new MatSlideToggleChange(this, isChecked);\n  }\n\n  _getAriaLabelledBy() {\n    if (this.ariaLabelledby) {\n      return this.ariaLabelledby;\n    }\n\n    // Even though we have a `label` element with a `for` pointing to the button, we need the\n    // `aria-labelledby`, because the button gets flagged as not having a label by tools like axe.\n    return this.ariaLabel ? null : this._labelId;\n  }\n}\n", "<div class=\"mdc-form-field\"\n     [class.mdc-form-field--align-end]=\"labelPosition == 'before'\">\n  <button\n    class=\"mdc-switch\"\n    role=\"switch\"\n    type=\"button\"\n    [class.mdc-switch--selected]=\"checked\"\n    [class.mdc-switch--unselected]=\"!checked\"\n    [class.mdc-switch--checked]=\"checked\"\n    [class.mdc-switch--disabled]=\"disabled\"\n    [tabIndex]=\"tabIndex\"\n    [disabled]=\"disabled\"\n    [attr.id]=\"buttonId\"\n    [attr.name]=\"name\"\n    [attr.aria-label]=\"ariaLabel\"\n    [attr.aria-labelledby]=\"_getAriaLabelledBy()\"\n    [attr.aria-describedby]=\"ariaDescribedby\"\n    [attr.aria-required]=\"required || null\"\n    [attr.aria-checked]=\"checked\"\n    (click)=\"_handleClick()\"\n    #switch>\n    <div class=\"mdc-switch__track\"></div>\n    <div class=\"mdc-switch__handle-track\">\n      <div class=\"mdc-switch__handle\">\n        <div class=\"mdc-switch__shadow\">\n          <div class=\"mdc-elevation-overlay\"></div>\n        </div>\n        <div class=\"mdc-switch__ripple\">\n          <div class=\"mat-mdc-slide-toggle-ripple mat-mdc-focus-indicator\" mat-ripple\n            [matRippleTrigger]=\"switch\"\n            [matRippleDisabled]=\"disableRipple || disabled\"\n            [matRippleCentered]=\"true\"></div>\n        </div>\n        <div class=\"mdc-switch__icons\" *ngIf=\"!hideIcon\">\n          <svg\n            class=\"mdc-switch__icon mdc-switch__icon--on\"\n            viewBox=\"0 0 24 24\"\n            aria-hidden=\"true\">\n            <path d=\"M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z\" />\n          </svg>\n          <svg\n            class=\"mdc-switch__icon mdc-switch__icon--off\"\n            viewBox=\"0 0 24 24\"\n            aria-hidden=\"true\">\n            <path d=\"M20 13H4v-2h16v2z\" />\n          </svg>\n        </div>\n      </div>\n    </div>\n  </button>\n\n  <!--\n    Clicking on the label will trigger another click event from the button.\n    Stop propagation here so other listeners further up in the DOM don't execute twice.\n  -->\n  <label class=\"mdc-label\" [for]=\"buttonId\" [attr.id]=\"_labelId\" (click)=\"$event.stopPropagation()\">\n    <ng-content></ng-content>\n  </label>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, forwardRef, Provider} from '@angular/core';\nimport {CheckboxRequiredValidator, NG_VALIDATORS} from '@angular/forms';\n\nexport const MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR: Provider = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatSlideToggleRequiredValidator),\n  multi: true,\n};\n\n/**\n * Validator for Material slide-toggle components with the required attribute in a\n * template-driven form. The default validator for required form controls asserts\n * that the control value is not undefined but that is not appropriate for a slide-toggle\n * where the value is always defined.\n *\n * Required slide-toggle form controls are valid when checked.\n */\n@Directive({\n  selector: `mat-slide-toggle[required][formControlName],\n             mat-slide-toggle[required][formControl], mat-slide-toggle[required][ngModel]`,\n  providers: [MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR],\n})\nexport class MatSlideToggleRequiredValidator extends CheckboxRequiredValidator {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '@angular/material/core';\nimport {MatSlideToggle} from './slide-toggle';\nimport {MatSlideToggleRequiredValidator} from './slide-toggle-required-validator';\n\n/** This module is used by both original and MDC-based slide-toggle implementations. */\n@NgModule({\n  exports: [MatSlideToggleRequiredValidator],\n  declarations: [MatSlideToggleRequiredValidator],\n})\nexport class _MatSlideToggleRequiredValidatorModule {}\n\n@NgModule({\n  imports: [_MatSlideToggleRequiredValidatorModule, MatCommonModule, MatRippleModule, CommonModule],\n  exports: [_MatSlideToggleRequiredValidatorModule, MatSlideToggle, MatCommonModule],\n  declarations: [MatSlideToggle],\n})\nexport class MatSlideToggleModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;;;AAsBA;MACa,gCAAgC,GAAG,IAAI,cAAc,CAChE,kCAAkC,EAClC;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,OAAO,EAAC,kBAAkB,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC;AAC9D,CAAA;;ACiBH;AACa,MAAA,+BAA+B,GAAG;AAC7C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,cAAc,CAAC;AAC7C,IAAA,KAAK,EAAE,IAAI;EACX;AAEF;MACa,oBAAoB,CAAA;AAC/B,IAAA,WAAA;;IAES,MAAsB;;IAEtB,OAAgB,EAAA;QAFhB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAgB;QAEtB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;KACrB;AACL,CAAA;AAED;AACA,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB;AACA;AACA,MAAM,wBAAwB,GAAG,aAAa,CAC5C,UAAU,CACR,kBAAkB,CAChB,aAAa,CACX,MAAA;AACE,IAAA,WAAA,CAAmB,WAAuB,EAAA;QAAvB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;KAAI;CAC/C,CACF,CACF,CACF,CACF,CAAC;AAGI,MAAgB,mBACpB,SAAQ,wBAAwB,CAAA;;AA8ChC,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IAED,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC/C;;AAGD,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IAED,IAAI,OAAO,CAAC,KAAmB,EAAA;AAC7B,QAAA,IAAI,CAAC,QAAQ,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;AAGD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC/C;;AAcD,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,CAAA,EAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,SAAS,CAAA,MAAA,CAAQ,CAAC;KAC7C;AAED,IAAA,WAAA,CACE,UAAsB,EACZ,aAA2B,EAC3B,kBAAqC,EAC/C,QAAgB,EACT,QAAsC,EAC7C,aAAiC,EACjC,QAAgB,EAAA;QAEhB,KAAK,CAAC,UAAU,CAAC,CAAC;QAPR,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;QAC3B,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QAExC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAA8B;AAtFrC,QAAA,IAAA,CAAA,SAAS,GAAG,CAAC,CAAM,KAAI,GAAG,CAAC;AAC7B,QAAA,IAAA,CAAA,UAAU,GAAG,MAAK,GAAG,CAAC;QAGtB,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;QAC3B,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;;QAazB,IAAI,CAAA,IAAA,GAAkB,IAAI,CAAC;;QAM3B,IAAa,CAAA,aAAA,GAAuB,OAAO,CAAC;;QAGhC,IAAS,CAAA,SAAA,GAAkB,IAAI,CAAC;;QAG3B,IAAc,CAAA,cAAA,GAAkB,IAAI,CAAC;QAkCvD,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;;AAGP,QAAA,IAAA,CAAA,MAAM,GAAoB,IAAI,YAAY,EAAK,CAAC;AAEnE;;;;AAIG;AACgB,QAAA,IAAA,CAAA,YAAY,GAAuB,IAAI,YAAY,EAAQ,CAAC;QAiB7E,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC;AAC5D,QAAA,IAAI,CAAC,eAAe,GAAG,aAAa,KAAK,gBAAgB,CAAC;AAC1D,QAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,GAAG,CAAA,EAAG,QAAQ,CAAA,EAAG,EAAE,YAAY,EAAE,CAAC;QAC1D,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC;KAC7C;IAED,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,IAAG;AACzE,YAAA,IAAI,WAAW,KAAK,UAAU,IAAI,WAAW,KAAK,SAAS,EAAE;AAC3D,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACxC,aAAA;iBAAM,IAAI,CAAC,WAAW,EAAE;;;;;;AAMvB,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,oBAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACtB,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,oBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,iBAAC,CAAC,CAAC;AACJ,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACrD;;AAGD,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC;KACxB;;AAGD,IAAA,gBAAgB,CAAC,EAAO,EAAA;AACtB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;KACrB;;AAGD,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB;;AAGD,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;AAC3B,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;IAGD,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;AAC7B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC9B;AAED;;AAEG;IACO,gBAAgB,GAAA;AACxB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;KACzD;8GAtKmB,mBAAmB,EAAA,IAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAnB,mBAAmB,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAA,EAAA,eAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,eAAA,EAAA,CAAA,kBAAA,EAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBADxC,SAAS;gPA8BC,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAGG,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAGG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAGe,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY,CAAA;gBAGO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB,CAAA;gBAGG,eAAe,EAAA,CAAA;sBAAzC,KAAK;uBAAC,kBAAkB,CAAA;gBAIrB,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAWF,OAAO,EAAA,CAAA;sBADV,KAAK;gBAYF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAUa,MAAM,EAAA,CAAA;sBAAxB,MAAM;gBAOY,YAAY,EAAA,CAAA;sBAA9B,MAAM;;AA0GH,MAAO,cAAe,SAAQ,mBAAyC,CAAA;;AAK3E,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,CAAA,EAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,SAAS,CAAA,OAAA,CAAS,CAAC;KAC9C;IAKD,WACE,CAAA,UAAsB,EACtB,YAA0B,EAC1B,iBAAoC,EACb,QAAgB,EAEvC,QAAsC,EACK,aAAsB,EAAA;AAEjE,QAAA,KAAK,CACH,UAAU,EACV,YAAY,EACZ,iBAAiB,EACjB,QAAQ,EACR,QAAQ,EACR,aAAa,EACb,uBAAuB,CACxB,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;KAC3C;;IAGD,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AAEzB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE;AACrC,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;AAC7B,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AAChE,SAAA;KACF;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KAC3C;AAES,IAAA,kBAAkB,CAAC,SAAkB,EAAA;AAC7C,QAAA,OAAO,IAAI,oBAAoB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KAClD;IAED,kBAAkB,GAAA;QAChB,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,OAAO,IAAI,CAAC,cAAc,CAAC;AAC5B,SAAA;;;AAID,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;KAC9C;AA7DU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAgBZ,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,UAAU,EACb,SAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,gCAAgC,aAEpB,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAnBhC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,EAFd,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,aAAA,EAAA,eAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,eAAA,EAAA,MAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,WAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,oCAAA,EAAA,UAAA,EAAA,oCAAA,EAAA,SAAA,EAAA,+BAAA,EAAA,iBAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,SAAA,EAAA,CAAC,+BAA+B,CAAC,yLC7Q9C,8rEA2DA,EAAA,MAAA,EAAA,CAAA,0ieAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDoNa,cAAc,EAAA,UAAA,EAAA,CAAA;kBAtB1B,SAAS;+BACE,kBAAkB,EAAA,MAAA,EAGpB,CAAC,UAAU,EAAE,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC,EACpD,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,MAAM,EAAE,IAAI;;AAEZ,wBAAA,iBAAiB,EAAE,MAAM;AACzB,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,wBAAwB,EAAE,MAAM;AAChC,wBAAA,sCAAsC,EAAE,UAAU;AAClD,wBAAA,sCAAsC,EAAE,SAAS;AACjD,wBAAA,iCAAiC,EAAE,iBAAiB;AACrD,qBAAA,EAAA,QAAA,EACS,gBAAgB,EAAA,aAAA,EACX,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,SAAA,EACpC,CAAC,+BAA+B,CAAC,EAAA,QAAA,EAAA,8rEAAA,EAAA,MAAA,EAAA,CAAA,0ieAAA,CAAA,EAAA,CAAA;;0BAkBzC,SAAS;2BAAC,UAAU,CAAA;;0BACpB,MAAM;2BAAC,gCAAgC,CAAA;;0BAEvC,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;4CATtB,cAAc,EAAA,CAAA;sBAAlC,SAAS;uBAAC,QAAQ,CAAA;;;AE9QR,MAAA,mCAAmC,GAAa;AAC3D,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,+BAA+B,CAAC;AAC9D,IAAA,KAAK,EAAE,IAAI;EACX;AAEF;;;;;;;AAOG;AAMG,MAAO,+BAAgC,SAAQ,yBAAyB,CAAA;8GAAjE,+BAA+B,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAA/B,+BAA+B,EAAA,QAAA,EAAA,yIAAA,EAAA,SAAA,EAF/B,CAAC,mCAAmC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAErC,+BAA+B,EAAA,UAAA,EAAA,CAAA;kBAL3C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAA;AAC8E,yFAAA,CAAA;oBACxF,SAAS,EAAE,CAAC,mCAAmC,CAAC;AACjD,iBAAA,CAAA;;;ACfD;MAKa,sCAAsC,CAAA;8GAAtC,sCAAsC,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAtC,sCAAsC,EAAA,YAAA,EAAA,CAFlC,+BAA+B,CAAA,EAAA,OAAA,EAAA,CADpC,+BAA+B,CAAA,EAAA,CAAA,CAAA,EAAA;+GAG9B,sCAAsC,EAAA,CAAA,CAAA,EAAA;;2FAAtC,sCAAsC,EAAA,UAAA,EAAA,CAAA;kBAJlD,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,+BAA+B,CAAC;oBAC1C,YAAY,EAAE,CAAC,+BAA+B,CAAC;AAChD,iBAAA,CAAA;;MAQY,oBAAoB,CAAA;8GAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAApB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,EAFhB,YAAA,EAAA,CAAA,cAAc,CALlB,EAAA,OAAA,EAAA,CAAA,sCAAsC,EAGC,eAAe,EAAE,eAAe,EAAE,YAAY,CAHrF,EAAA,OAAA,EAAA,CAAA,sCAAsC,EAIC,cAAc,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;+GAGtE,oBAAoB,EAAA,OAAA,EAAA,CAJrB,sCAAsC,EAAE,eAAe,EAAE,eAAe,EAAE,YAAY,EAHrF,sCAAsC,EAIiB,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAGtE,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBALhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,sCAAsC,EAAE,eAAe,EAAE,eAAe,EAAE,YAAY,CAAC;AACjG,oBAAA,OAAO,EAAE,CAAC,sCAAsC,EAAE,cAAc,EAAE,eAAe,CAAC;oBAClF,YAAY,EAAE,CAAC,cAAc,CAAC;AAC/B,iBAAA,CAAA;;;ACzBD;;AAEG;;;;"}