{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, isObject, map, isString } from 'zrender/lib/core/util.js';\nvar uidBase = 0;\nvar OrdinalMeta = /** @class */function () {\n  function OrdinalMeta(opt) {\n    this.categories = opt.categories || [];\n    this._needCollect = opt.needCollect;\n    this._deduplication = opt.deduplication;\n    this.uid = ++uidBase;\n  }\n  OrdinalMeta.createByAxisModel = function (axisModel) {\n    var option = axisModel.option;\n    var data = option.data;\n    var categories = data && map(data, getName);\n    return new OrdinalMeta({\n      categories: categories,\n      needCollect: !categories,\n      // deduplication is default in axis.\n      deduplication: option.dedplication !== false\n    });\n  };\n  ;\n  OrdinalMeta.prototype.getOrdinal = function (category) {\n    // @ts-ignore\n    return this._getOrCreateMap().get(category);\n  };\n  /**\n   * @return The ordinal. If not found, return NaN.\n   */\n  OrdinalMeta.prototype.parseAndCollect = function (category) {\n    var index;\n    var needCollect = this._needCollect;\n    // The value of category dim can be the index of the given category set.\n    // This feature is only supported when !needCollect, because we should\n    // consider a common case: a value is 2017, which is a number but is\n    // expected to be tread as a category. This case usually happen in dataset,\n    // where it happent to be no need of the index feature.\n    if (!isString(category) && !needCollect) {\n      return category;\n    }\n    // Optimize for the scenario:\n    // category is ['2012-01-01', '2012-01-02', ...], where the input\n    // data has been ensured not duplicate and is large data.\n    // Notice, if a dataset dimension provide categroies, usually echarts\n    // should remove duplication except user tell echarts dont do that\n    // (set axis.deduplication = false), because echarts do not know whether\n    // the values in the category dimension has duplication (consider the\n    // parallel-aqi example)\n    if (needCollect && !this._deduplication) {\n      index = this.categories.length;\n      this.categories[index] = category;\n      return index;\n    }\n    var map = this._getOrCreateMap();\n    // @ts-ignore\n    index = map.get(category);\n    if (index == null) {\n      if (needCollect) {\n        index = this.categories.length;\n        this.categories[index] = category;\n        // @ts-ignore\n        map.set(category, index);\n      } else {\n        index = NaN;\n      }\n    }\n    return index;\n  };\n  // Consider big data, do not create map until needed.\n  OrdinalMeta.prototype._getOrCreateMap = function () {\n    return this._map || (this._map = createHashMap(this.categories));\n  };\n  return OrdinalMeta;\n}();\nfunction getName(obj) {\n  if (isObject(obj) && obj.value != null) {\n    return obj.value;\n  } else {\n    return obj + '';\n  }\n}\nexport default OrdinalMeta;", "map": {"version": 3, "names": ["createHashMap", "isObject", "map", "isString", "uidBase", "OrdinalMeta", "opt", "categories", "_needCollect", "needCollect", "_deduplication", "deduplication", "uid", "createByAxisModel", "axisModel", "option", "data", "getName", "dedplication", "prototype", "getOrdinal", "category", "_getOrCreateMap", "get", "parseAndCollect", "index", "length", "set", "NaN", "_map", "obj", "value"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/data/OrdinalMeta.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, isObject, map, isString } from 'zrender/lib/core/util.js';\nvar uidBase = 0;\nvar OrdinalMeta = /** @class */function () {\n  function OrdinalMeta(opt) {\n    this.categories = opt.categories || [];\n    this._needCollect = opt.needCollect;\n    this._deduplication = opt.deduplication;\n    this.uid = ++uidBase;\n  }\n  OrdinalMeta.createByAxisModel = function (axisModel) {\n    var option = axisModel.option;\n    var data = option.data;\n    var categories = data && map(data, getName);\n    return new OrdinalMeta({\n      categories: categories,\n      needCollect: !categories,\n      // deduplication is default in axis.\n      deduplication: option.dedplication !== false\n    });\n  };\n  ;\n  OrdinalMeta.prototype.getOrdinal = function (category) {\n    // @ts-ignore\n    return this._getOrCreateMap().get(category);\n  };\n  /**\n   * @return The ordinal. If not found, return NaN.\n   */\n  OrdinalMeta.prototype.parseAndCollect = function (category) {\n    var index;\n    var needCollect = this._needCollect;\n    // The value of category dim can be the index of the given category set.\n    // This feature is only supported when !needCollect, because we should\n    // consider a common case: a value is 2017, which is a number but is\n    // expected to be tread as a category. This case usually happen in dataset,\n    // where it happent to be no need of the index feature.\n    if (!isString(category) && !needCollect) {\n      return category;\n    }\n    // Optimize for the scenario:\n    // category is ['2012-01-01', '2012-01-02', ...], where the input\n    // data has been ensured not duplicate and is large data.\n    // Notice, if a dataset dimension provide categroies, usually echarts\n    // should remove duplication except user tell echarts dont do that\n    // (set axis.deduplication = false), because echarts do not know whether\n    // the values in the category dimension has duplication (consider the\n    // parallel-aqi example)\n    if (needCollect && !this._deduplication) {\n      index = this.categories.length;\n      this.categories[index] = category;\n      return index;\n    }\n    var map = this._getOrCreateMap();\n    // @ts-ignore\n    index = map.get(category);\n    if (index == null) {\n      if (needCollect) {\n        index = this.categories.length;\n        this.categories[index] = category;\n        // @ts-ignore\n        map.set(category, index);\n      } else {\n        index = NaN;\n      }\n    }\n    return index;\n  };\n  // Consider big data, do not create map until needed.\n  OrdinalMeta.prototype._getOrCreateMap = function () {\n    return this._map || (this._map = createHashMap(this.categories));\n  };\n  return OrdinalMeta;\n}();\nfunction getName(obj) {\n  if (isObject(obj) && obj.value != null) {\n    return obj.value;\n  } else {\n    return obj + '';\n  }\n}\nexport default OrdinalMeta;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,0BAA0B;AACjF,IAAIC,OAAO,GAAG,CAAC;AACf,IAAIC,WAAW,GAAG,aAAa,YAAY;EACzC,SAASA,WAAWA,CAACC,GAAG,EAAE;IACxB,IAAI,CAACC,UAAU,GAAGD,GAAG,CAACC,UAAU,IAAI,EAAE;IACtC,IAAI,CAACC,YAAY,GAAGF,GAAG,CAACG,WAAW;IACnC,IAAI,CAACC,cAAc,GAAGJ,GAAG,CAACK,aAAa;IACvC,IAAI,CAACC,GAAG,GAAG,EAAER,OAAO;EACtB;EACAC,WAAW,CAACQ,iBAAiB,GAAG,UAAUC,SAAS,EAAE;IACnD,IAAIC,MAAM,GAAGD,SAAS,CAACC,MAAM;IAC7B,IAAIC,IAAI,GAAGD,MAAM,CAACC,IAAI;IACtB,IAAIT,UAAU,GAAGS,IAAI,IAAId,GAAG,CAACc,IAAI,EAAEC,OAAO,CAAC;IAC3C,OAAO,IAAIZ,WAAW,CAAC;MACrBE,UAAU,EAAEA,UAAU;MACtBE,WAAW,EAAE,CAACF,UAAU;MACxB;MACAI,aAAa,EAAEI,MAAM,CAACG,YAAY,KAAK;IACzC,CAAC,CAAC;EACJ,CAAC;EACD;EACAb,WAAW,CAACc,SAAS,CAACC,UAAU,GAAG,UAAUC,QAAQ,EAAE;IACrD;IACA,OAAO,IAAI,CAACC,eAAe,CAAC,CAAC,CAACC,GAAG,CAACF,QAAQ,CAAC;EAC7C,CAAC;EACD;AACF;AACA;EACEhB,WAAW,CAACc,SAAS,CAACK,eAAe,GAAG,UAAUH,QAAQ,EAAE;IAC1D,IAAII,KAAK;IACT,IAAIhB,WAAW,GAAG,IAAI,CAACD,YAAY;IACnC;IACA;IACA;IACA;IACA;IACA,IAAI,CAACL,QAAQ,CAACkB,QAAQ,CAAC,IAAI,CAACZ,WAAW,EAAE;MACvC,OAAOY,QAAQ;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIZ,WAAW,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;MACvCe,KAAK,GAAG,IAAI,CAAClB,UAAU,CAACmB,MAAM;MAC9B,IAAI,CAACnB,UAAU,CAACkB,KAAK,CAAC,GAAGJ,QAAQ;MACjC,OAAOI,KAAK;IACd;IACA,IAAIvB,GAAG,GAAG,IAAI,CAACoB,eAAe,CAAC,CAAC;IAChC;IACAG,KAAK,GAAGvB,GAAG,CAACqB,GAAG,CAACF,QAAQ,CAAC;IACzB,IAAII,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIhB,WAAW,EAAE;QACfgB,KAAK,GAAG,IAAI,CAAClB,UAAU,CAACmB,MAAM;QAC9B,IAAI,CAACnB,UAAU,CAACkB,KAAK,CAAC,GAAGJ,QAAQ;QACjC;QACAnB,GAAG,CAACyB,GAAG,CAACN,QAAQ,EAAEI,KAAK,CAAC;MAC1B,CAAC,MAAM;QACLA,KAAK,GAAGG,GAAG;MACb;IACF;IACA,OAAOH,KAAK;EACd,CAAC;EACD;EACApB,WAAW,CAACc,SAAS,CAACG,eAAe,GAAG,YAAY;IAClD,OAAO,IAAI,CAACO,IAAI,KAAK,IAAI,CAACA,IAAI,GAAG7B,aAAa,CAAC,IAAI,CAACO,UAAU,CAAC,CAAC;EAClE,CAAC;EACD,OAAOF,WAAW;AACpB,CAAC,CAAC,CAAC;AACH,SAASY,OAAOA,CAACa,GAAG,EAAE;EACpB,IAAI7B,QAAQ,CAAC6B,GAAG,CAAC,IAAIA,GAAG,CAACC,KAAK,IAAI,IAAI,EAAE;IACtC,OAAOD,GAAG,CAACC,KAAK;EAClB,CAAC,MAAM;IACL,OAAOD,GAAG,GAAG,EAAE;EACjB;AACF;AACA,eAAezB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}