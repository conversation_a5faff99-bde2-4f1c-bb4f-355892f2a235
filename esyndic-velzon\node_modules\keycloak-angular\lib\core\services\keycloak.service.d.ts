import { HttpHeaders, HttpRequest } from '@angular/common/http';
import { Subject } from 'rxjs';
import { ExcludedUrlRegex, KeycloakOptions } from '../interfaces/keycloak-options';
import { KeycloakEvent } from '../interfaces/keycloak-event';
import * as i0 from "@angular/core";
export declare class KeycloakService {
    private _instance;
    private _userProfile;
    private _enableBearerInterceptor;
    private _silentRefresh;
    private _loadUserProfileAtStartUp;
    private _bearerPrefix;
    private _authorizationHeaderName;
    private _excludedUrls;
    private _keycloakEvents$;
    private _updateMinValidity;
    shouldAddToken: (request: HttpRequest<unknown>) => boolean;
    shouldUpdateToken: (request: HttpRequest<unknown>) => boolean;
    private bindsKeycloakEvents;
    private loadExcludedUrls;
    private initServiceValues;
    init(options?: KeycloakOptions): Promise<boolean>;
    login(options?: Keycloak.KeycloakLoginOptions): Promise<void>;
    logout(redirectUri?: string): Promise<void>;
    register(options?: Keycloak.KeycloakLoginOptions): Promise<void>;
    isUserInRole(role: string, resource?: string): boolean;
    getUserRoles(realmRoles?: boolean, resource?: string): string[];
    isLoggedIn(): boolean;
    isTokenExpired(minValidity?: number): boolean;
    updateToken(minValidity?: number): Promise<boolean>;
    loadUserProfile(forceReload?: boolean): Promise<import("keycloak-js").KeycloakProfile>;
    getToken(): Promise<string>;
    getUsername(): string;
    clearToken(): void;
    addTokenToHeader(headers?: HttpHeaders): import("rxjs").Observable<HttpHeaders>;
    getKeycloakInstance(): Keycloak.KeycloakInstance;
    get excludedUrls(): ExcludedUrlRegex[];
    get enableBearerInterceptor(): boolean;
    get keycloakEvents$(): Subject<KeycloakEvent>;
    static ɵfac: i0.ɵɵFactoryDeclaration<KeycloakService, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<KeycloakService>;
}
