{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport BaseBarSeriesModel from './BaseBarSeries.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar BarSeriesModel = /** @class */function (_super) {\n  __extends(BarSeriesModel, _super);\n  function BarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BarSeriesModel.type;\n    return _this;\n  }\n  BarSeriesModel.prototype.getInitialData = function () {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true,\n      createInvertedIndices: !!this.get('realtimeSort', true) || null\n    });\n  };\n  /**\n   * @override\n   */\n  BarSeriesModel.prototype.getProgressive = function () {\n    // Do not support progressive in normal mode.\n    return this.get('large') ? this.get('progressive') : false;\n  };\n  /**\n   * @override\n   */\n  BarSeriesModel.prototype.getProgressiveThreshold = function () {\n    // Do not support progressive in normal mode.\n    var progressiveThreshold = this.get('progressiveThreshold');\n    var largeThreshold = this.get('largeThreshold');\n    if (largeThreshold > progressiveThreshold) {\n      progressiveThreshold = largeThreshold;\n    }\n    return progressiveThreshold;\n  };\n  BarSeriesModel.prototype.brushSelector = function (dataIndex, data, selectors) {\n    return selectors.rect(data.getItemLayout(dataIndex));\n  };\n  BarSeriesModel.type = 'series.bar';\n  BarSeriesModel.dependencies = ['grid', 'polar'];\n  BarSeriesModel.defaultOption = inheritDefaultOption(BaseBarSeriesModel.defaultOption, {\n    // If clipped\n    // Only available on cartesian2d\n    clip: true,\n    roundCap: false,\n    showBackground: false,\n    backgroundStyle: {\n      color: 'rgba(180, 180, 180, 0.2)',\n      borderColor: null,\n      borderWidth: 0,\n      borderType: 'solid',\n      borderRadius: 0,\n      shadowBlur: 0,\n      shadowColor: null,\n      shadowOffsetX: 0,\n      shadowOffsetY: 0,\n      opacity: 1\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    },\n    realtimeSort: false\n  });\n  return BarSeriesModel;\n}(BaseBarSeriesModel);\nexport default BarSeriesModel;", "map": {"version": 3, "names": ["__extends", "BaseBarSeriesModel", "createSeriesData", "inheritDefaultOption", "BarSeriesModel", "_super", "_this", "apply", "arguments", "type", "prototype", "getInitialData", "useEncodeDefaulter", "createInvertedIndices", "get", "getProgressive", "getProgressiveThreshold", "progressiveThreshold", "largeThreshold", "brushSelector", "dataIndex", "data", "selectors", "rect", "getItemLayout", "dependencies", "defaultOption", "clip", "roundCap", "showBackground", "backgroundStyle", "color", "borderColor", "borderWidth", "borderType", "borderRadius", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "shadowOffsetX", "shadowOffsetY", "opacity", "select", "itemStyle", "realtimeSort"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/chart/bar/BarSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport BaseBarSeriesModel from './BaseBarSeries.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar BarSeriesModel = /** @class */function (_super) {\n  __extends(BarSeriesModel, _super);\n  function BarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BarSeriesModel.type;\n    return _this;\n  }\n  BarSeriesModel.prototype.getInitialData = function () {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true,\n      createInvertedIndices: !!this.get('realtimeSort', true) || null\n    });\n  };\n  /**\n   * @override\n   */\n  BarSeriesModel.prototype.getProgressive = function () {\n    // Do not support progressive in normal mode.\n    return this.get('large') ? this.get('progressive') : false;\n  };\n  /**\n   * @override\n   */\n  BarSeriesModel.prototype.getProgressiveThreshold = function () {\n    // Do not support progressive in normal mode.\n    var progressiveThreshold = this.get('progressiveThreshold');\n    var largeThreshold = this.get('largeThreshold');\n    if (largeThreshold > progressiveThreshold) {\n      progressiveThreshold = largeThreshold;\n    }\n    return progressiveThreshold;\n  };\n  BarSeriesModel.prototype.brushSelector = function (dataIndex, data, selectors) {\n    return selectors.rect(data.getItemLayout(dataIndex));\n  };\n  BarSeriesModel.type = 'series.bar';\n  BarSeriesModel.dependencies = ['grid', 'polar'];\n  BarSeriesModel.defaultOption = inheritDefaultOption(BaseBarSeriesModel.defaultOption, {\n    // If clipped\n    // Only available on cartesian2d\n    clip: true,\n    roundCap: false,\n    showBackground: false,\n    backgroundStyle: {\n      color: 'rgba(180, 180, 180, 0.2)',\n      borderColor: null,\n      borderWidth: 0,\n      borderType: 'solid',\n      borderRadius: 0,\n      shadowBlur: 0,\n      shadowColor: null,\n      shadowOffsetX: 0,\n      shadowOffsetY: 0,\n      opacity: 1\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    },\n    realtimeSort: false\n  });\n  return BarSeriesModel;\n}(BaseBarSeriesModel);\nexport default BarSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,kBAAkB,MAAM,oBAAoB;AACnD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,IAAIC,cAAc,GAAG,aAAa,UAAUC,MAAM,EAAE;EAClDL,SAAS,CAACI,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAAA,EAAG;IACxB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,cAAc,CAACK,IAAI;IAChC,OAAOH,KAAK;EACd;EACAF,cAAc,CAACM,SAAS,CAACC,cAAc,GAAG,YAAY;IACpD,OAAOT,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE;MAClCU,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI;IAC7D,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;EACEV,cAAc,CAACM,SAAS,CAACK,cAAc,GAAG,YAAY;IACpD;IACA,OAAO,IAAI,CAACD,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,CAACA,GAAG,CAAC,aAAa,CAAC,GAAG,KAAK;EAC5D,CAAC;EACD;AACF;AACA;EACEV,cAAc,CAACM,SAAS,CAACM,uBAAuB,GAAG,YAAY;IAC7D;IACA,IAAIC,oBAAoB,GAAG,IAAI,CAACH,GAAG,CAAC,sBAAsB,CAAC;IAC3D,IAAII,cAAc,GAAG,IAAI,CAACJ,GAAG,CAAC,gBAAgB,CAAC;IAC/C,IAAII,cAAc,GAAGD,oBAAoB,EAAE;MACzCA,oBAAoB,GAAGC,cAAc;IACvC;IACA,OAAOD,oBAAoB;EAC7B,CAAC;EACDb,cAAc,CAACM,SAAS,CAACS,aAAa,GAAG,UAAUC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAE;IAC7E,OAAOA,SAAS,CAACC,IAAI,CAACF,IAAI,CAACG,aAAa,CAACJ,SAAS,CAAC,CAAC;EACtD,CAAC;EACDhB,cAAc,CAACK,IAAI,GAAG,YAAY;EAClCL,cAAc,CAACqB,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;EAC/CrB,cAAc,CAACsB,aAAa,GAAGvB,oBAAoB,CAACF,kBAAkB,CAACyB,aAAa,EAAE;IACpF;IACA;IACAC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE,KAAK;IACrBC,eAAe,EAAE;MACfC,KAAK,EAAE,0BAA0B;MACjCC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,IAAI;MACjBC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChBC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE;MACNC,SAAS,EAAE;QACTV,WAAW,EAAE;MACf;IACF,CAAC;IACDW,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,OAAOvC,cAAc;AACvB,CAAC,CAACH,kBAAkB,CAAC;AACrB,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}