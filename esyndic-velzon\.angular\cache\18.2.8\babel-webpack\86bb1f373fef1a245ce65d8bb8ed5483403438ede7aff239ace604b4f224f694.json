{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n// Routing\nimport { CandidateListsRoutingModule } from './candidate-lists-routing.module';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { ListViewComponent } from './list-view/list-view.component';\nimport { GridViewComponent } from './grid-view/grid-view.component';\nimport * as i0 from \"@angular/core\";\nexport class CandidateListsModule {\n  static {\n    this.ɵfac = function CandidateListsModule_Factory(t) {\n      return new (t || CandidateListsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CandidateListsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, CandidateListsRoutingModule, SharedModule, NgbPaginationModule, FormsModule, ReactiveFormsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CandidateListsModule, {\n    declarations: [GridViewComponent, ListViewComponent],\n    imports: [CommonModule, CandidateListsRoutingModule, SharedModule, NgbPaginationModule, FormsModule, ReactiveFormsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "NgbPaginationModule", "FormsModule", "ReactiveFormsModule", "CandidateListsRoutingModule", "SharedModule", "ListViewComponent", "GridViewComponent", "CandidateListsModule", "declarations", "imports"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\candidate-lists\\candidate-lists.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\n// Routing\r\nimport { CandidateListsRoutingModule } from './candidate-lists-routing.module';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\n\r\nimport { ListViewComponent } from './list-view/list-view.component';\r\nimport { GridViewComponent } from './grid-view/grid-view.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    GridViewComponent,\r\n    ListViewComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    CandidateListsRoutingModule,\r\n    SharedModule,\r\n    NgbPaginationModule,\r\n    FormsModule,\r\n    ReactiveFormsModule\r\n  ]\r\n})\r\nexport class CandidateListsModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE;AACA,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,YAAY,QAAQ,8BAA8B;AAE3D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,iBAAiB,QAAQ,iCAAiC;;AAgBnE,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAR7BR,YAAY,EACZI,2BAA2B,EAC3BC,YAAY,EACZJ,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB;IAAA;EAAA;;;2EAGVK,oBAAoB;IAAAC,YAAA,GAZ7BF,iBAAiB,EACjBD,iBAAiB;IAAAI,OAAA,GAGjBV,YAAY,EACZI,2BAA2B,EAC3BC,YAAY,EACZJ,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}