{"ast": null, "code": "import baseEach from './_baseEach.js';\nimport isArrayLike from './isArrayLike.js';\n\n/**\n * The base implementation of `_.map` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction baseMap(collection, iteratee) {\n  var index = -1,\n    result = isArrayLike(collection) ? Array(collection.length) : [];\n  baseEach(collection, function (value, key, collection) {\n    result[++index] = iteratee(value, key, collection);\n  });\n  return result;\n}\nexport default baseMap;", "map": {"version": 3, "names": ["baseEach", "isArrayLike", "baseMap", "collection", "iteratee", "index", "result", "Array", "length", "value", "key"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_baseMap.js"], "sourcesContent": ["import baseEach from './_baseEach.js';\nimport isArrayLike from './isArrayLike.js';\n\n/**\n * The base implementation of `_.map` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction baseMap(collection, iteratee) {\n  var index = -1,\n      result = isArrayLike(collection) ? Array(collection.length) : [];\n\n  baseEach(collection, function(value, key, collection) {\n    result[++index] = iteratee(value, key, collection);\n  });\n  return result;\n}\n\nexport default baseMap;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,WAAW,MAAM,kBAAkB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EACrC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGL,WAAW,CAACE,UAAU,CAAC,GAAGI,KAAK,CAACJ,UAAU,CAACK,MAAM,CAAC,GAAG,EAAE;EAEpER,QAAQ,CAACG,UAAU,EAAE,UAASM,KAAK,EAAEC,GAAG,EAAEP,UAAU,EAAE;IACpDG,MAAM,CAAC,EAAED,KAAK,CAAC,GAAGD,QAAQ,CAACK,KAAK,EAAEC,GAAG,EAAEP,UAAU,CAAC;EACpD,CAAC,CAAC;EACF,OAAOG,MAAM;AACf;AAEA,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}