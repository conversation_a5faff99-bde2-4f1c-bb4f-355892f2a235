package com.esyndic.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "assembly_votes", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"assembly_id", "user_id", "agenda_item"})
})
public class AssemblyVote {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "agenda_item", nullable = false)
    @NotBlank(message = "Agenda item is required")
    private String agendaItem;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @NotNull(message = "Vote is required")
    private VoteType vote;

    @Column(name = "vote_time")
    @CreationTimestamp
    private LocalDateTime voteTime;

    @Column(columnDefinition = "TEXT")
    private String comments;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assembly_id", nullable = false)
    @NotNull(message = "Assembly is required")
    private Assembly assembly;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @NotNull(message = "User is required")
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "apartment_id")
    private Apartment apartment;

    // Enums
    public enum VoteType {
        YES, NO, ABSTAIN
    }

    // Constructors
    public AssemblyVote() {}

    public AssemblyVote(Assembly assembly, User user, String agendaItem, VoteType vote) {
        this.assembly = assembly;
        this.user = user;
        this.agendaItem = agendaItem;
        this.vote = vote;
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getAgendaItem() {
        return agendaItem;
    }

    public void setAgendaItem(String agendaItem) {
        this.agendaItem = agendaItem;
    }

    public VoteType getVote() {
        return vote;
    }

    public void setVote(VoteType vote) {
        this.vote = vote;
    }

    public LocalDateTime getVoteTime() {
        return voteTime;
    }

    public void setVoteTime(LocalDateTime voteTime) {
        this.voteTime = voteTime;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Assembly getAssembly() {
        return assembly;
    }

    public void setAssembly(Assembly assembly) {
        this.assembly = assembly;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Apartment getApartment() {
        return apartment;
    }

    public void setApartment(Apartment apartment) {
        this.apartment = apartment;
    }

    // Helper methods
    public boolean isPositive() {
        return vote == VoteType.YES;
    }

    public boolean isNegative() {
        return vote == VoteType.NO;
    }

    public boolean isAbstention() {
        return vote == VoteType.ABSTAIN;
    }

    @Override
    public String toString() {
        return "AssemblyVote{" +
                "id=" + id +
                ", agendaItem='" + agendaItem + '\'' +
                ", vote=" + vote +
                ", voteTime=" + voteTime +
                ", user=" + (user != null ? user.getUsername() : null) +
                ", apartment=" + (apartment != null ? apartment.getApartmentNumber() : null) +
                '}';
    }
}
