server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: esyndic-backend
  
  datasource:
    url: ****************************************
    username: postgres
    password: 1023
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8180/realms/esyndic
          jwk-set-uri: http://localhost:8180/realms/esyndic/protocol/openid-connect/certs
      client:
        registration:
          keycloak:
            client-id: esyndic-backend
            client-secret: 8SPDpxmeiPCZUiIki8dEAIBBUeAt78dh
            authorization-grant-type: authorization_code
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
            scope: openid,profile,email
        provider:
          keycloak:
            issuer-uri: http://localhost:8180/realms/esyndic
            authorization-uri: http://localhost:8180/realms/esyndic/protocol/openid-connect/auth
            token-uri: http://localhost:8180/realms/esyndic/protocol/openid-connect/token
            user-info-uri: http://localhost:8180/realms/esyndic/protocol/openid-connect/userinfo
            jwk-set-uri: http://localhost:8180/realms/esyndic/protocol/openid-connect/certs
            user-name-attribute: preferred_username

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# Keycloak Configuration
keycloak:
  realm: esyndic
  auth-server-url: http://localhost:8180
  resource: esyndic-backend
  public-client: false
  bearer-only: true
  credentials:
    secret: 8SPDpxmeiPCZUiIki8dEAIBBUeAt78dh

# Paymee Configuration
paymee:
  api:
    base-url: https://www.paymee.tn/api/v1
    token: your-paymee-token

# File Upload Configuration
file:
  upload:
    dir: ./uploads
    max-size: 10MB

# CORS Configuration
cors:
  allowed-origins: http://localhost:4200
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true

# Logging Configuration
logging:
  level:
    com.esyndic: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
