/*!
FullCalendar Core v6.1.14
Docs & License: https://fullcalendar.io
(c) 2024 <PERSON>
*/
(function (index_js) {
    'use strict';

    var locale = {
        code: 'pl',
        week: {
            dow: 1,
            doy: 4, // The week that contains <PERSON> 4th is the first week of the year.
        },
        buttonText: {
            prev: '<PERSON><PERSON><PERSON><PERSON>',
            next: 'Nast<PERSON><PERSON><PERSON>',
            today: '<PERSON><PERSON><PERSON>',
            year: 'Rok',
            month: '<PERSON><PERSON>ą<PERSON>',
            week: 'Tydzie<PERSON>',
            day: 'Dzień',
            list: 'Plan dnia',
        },
        weekText: 'Tydz',
        allDayText: 'Cały dzień',
        moreLinkText: 'więcej',
        noEventsText: 'Brak wydarzeń do wyświetlenia',
    };

    index_js.globalLocales.push(locale);

})(FullCalendar);
