{"ast": null, "code": "// so it doesn't throw if no window or matchMedia\nvar w = typeof window !== 'undefined' ? window : {\n  screen: {},\n  navigator: {}\n};\nvar matchMedia = (w.matchMedia || function () {\n  return {\n    matches: false\n  };\n}).bind(w);\n// passive events test\n// adapted from https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\nvar passiveOptionAccessed = false;\nvar options = {\n  get passive() {\n    return passiveOptionAccessed = true;\n  }\n};\n// have to set and remove a no-op listener instead of null\n// (which was used previously), because Edge v15 throws an error\n// when providing a null callback.\n// https://github.com/rafgraph/detect-passive-events/pull/3\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nvar noop = function () {};\nw.addEventListener && w.addEventListener('p', noop, options);\nw.removeEventListener && w.removeEventListener('p', noop, false);\nvar supportsPassiveEvents = passiveOptionAccessed;\nvar supportsPointerEvents = 'PointerEvent' in w;\nvar onTouchStartInWindow = 'ontouchstart' in w;\nvar touchEventInWindow = 'TouchEvent' in w;\n// onTouchStartInWindow is the old-old-legacy way to determine a touch device\n// and many websites interpreted it to mean that the device is a touch only phone,\n// so today browsers on a desktop/laptop computer with a touch screen (primary input mouse)\n// have onTouchStartInWindow as false (to prevent from being confused with a\n// touchOnly phone) even though they support the TouchEvents API, so need to check\n// both onTouchStartInWindow and touchEventInWindow for TouchEvent support,\n// however, some browsers (chromium) support the TouchEvents API even when running on\n// a mouse only device (touchEventInWindow true, but onTouchStartInWindow false)\n// so the touchEventInWindow check needs to include an coarse pointer media query\nvar supportsTouchEvents = onTouchStartInWindow || touchEventInWindow && matchMedia('(any-pointer: coarse)').matches;\nvar hasTouch = (w.navigator.maxTouchPoints || 0) > 0 || supportsTouchEvents;\n// userAgent is used as a backup to correct for known device/browser bugs\n// and when the browser doesn't support interaction media queries (pointer and hover)\n// see https://caniuse.com/css-media-interaction\nvar userAgent = w.navigator.userAgent || '';\n// iPads now support a mouse that can hover, however the media query interaction\n// feature results always say iPads only have a coarse pointer that can't hover\n// even when a mouse is connected (anyFine and anyHover are always false),\n// this unfortunately indicates a touch only device but iPads should\n// be classified as a hybrid device, so determine if it is an iPad\n// to indicate it should be treated as a hybrid device with anyHover true\nvar isIPad = matchMedia('(pointer: coarse)').matches &&\n// both iPad and iPhone can \"request desktop site\", which sets the userAgent to Macintosh\n// so need to check both userAgents to determine if it is an iOS device\n// and screen size to separate iPad from iPhone\n/iPad|Macintosh/.test(userAgent) && Math.min(w.screen.width || 0, w.screen.height || 0) >= 768;\nvar hasCoarsePrimaryPointer = (matchMedia('(pointer: coarse)').matches ||\n// if the pointer is not coarse and not fine then the browser doesn't support\n// interaction media queries (see https://caniuse.com/css-media-interaction)\n// so if it has onTouchStartInWindow assume it has a coarse primary pointer\n!matchMedia('(pointer: fine)').matches && onTouchStartInWindow) &&\n// bug in firefox (as of v81) on hybrid windows devices where the interaction media queries\n// always indicate a touch only device (only has a coarse pointer that can't hover)\n// so assume that the primary pointer is not coarse for firefox windows\n!/Windows.*Firefox/.test(userAgent);\nvar hasAnyHoverOrAnyFinePointer = matchMedia('(any-pointer: fine)').matches || matchMedia('(any-hover: hover)').matches ||\n// iPads might have an input device that can hover, so assume it has anyHover\nisIPad ||\n// if no onTouchStartInWindow then the browser is indicating that it is not a touch only device\n// see above note for supportsTouchEvents\n!onTouchStartInWindow;\n// a hybrid device is one that both hasTouch and\n// any input can hover or has a fine pointer, or the primary pointer is not coarse\n// if it's not a hybrid, then if it hasTouch it's touchOnly, otherwise it's mouseOnly\nvar deviceType = hasTouch && (hasAnyHoverOrAnyFinePointer || !hasCoarsePrimaryPointer) ? 'hybrid' : hasTouch ? 'touchOnly' : 'mouseOnly';\nvar primaryInput = deviceType === 'mouseOnly' ? 'mouse' : deviceType === 'touchOnly' ? 'touch' :\n// if the device is a hybrid, then if the primary pointer is coarse\n// assume the primaryInput is touch, otherwise assume it's mouse\nhasCoarsePrimaryPointer ? 'touch' : 'mouse';\nexport { deviceType, primaryInput, supportsPassiveEvents, supportsPointerEvents, supportsTouchEvents };", "map": {"version": 3, "names": ["w", "window", "screen", "navigator", "matchMedia", "matches", "bind", "passiveOptionAccessed", "options", "passive", "noop", "addEventListener", "removeEventListener", "supportsPassiveEvents", "supportsPointerEvents", "onTouchStartInWindow", "touchEventInWindow", "supportsTouchEvents", "has<PERSON><PERSON><PERSON>", "maxTouchPoints", "userAgent", "isIPad", "test", "Math", "min", "width", "height", "hasCoarsePrimaryPointer", "hasAnyHoverOrAnyFinePointer", "deviceType", "primaryInput"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/detect-it/dist/detect-it.esm.js"], "sourcesContent": ["// so it doesn't throw if no window or matchMedia\r\nvar w = typeof window !== 'undefined' ? window : { screen: {}, navigator: {} };\r\nvar matchMedia = (w.matchMedia || (function () { return ({ matches: false }); })).bind(w);\r\n// passive events test\r\n// adapted from https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\r\nvar passiveOptionAccessed = false;\r\nvar options = {\r\n    get passive() {\r\n        return (passiveOptionAccessed = true);\r\n    },\r\n};\r\n// have to set and remove a no-op listener instead of null\r\n// (which was used previously), because Edge v15 throws an error\r\n// when providing a null callback.\r\n// https://github.com/rafgraph/detect-passive-events/pull/3\r\n// eslint-disable-next-line @typescript-eslint/no-empty-function\r\nvar noop = function () { };\r\nw.addEventListener && w.addEventListener('p', noop, options);\r\nw.removeEventListener && w.removeEventListener('p', noop, false);\r\nvar supportsPassiveEvents = passiveOptionAccessed;\r\nvar supportsPointerEvents = 'PointerEvent' in w;\r\nvar onTouchStartInWindow = 'ontouchstart' in w;\r\nvar touchEventInWindow = 'TouchEvent' in w;\r\n// onTouchStartInWindow is the old-old-legacy way to determine a touch device\r\n// and many websites interpreted it to mean that the device is a touch only phone,\r\n// so today browsers on a desktop/laptop computer with a touch screen (primary input mouse)\r\n// have onTouchStartInWindow as false (to prevent from being confused with a\r\n// touchOnly phone) even though they support the TouchEvents API, so need to check\r\n// both onTouchStartInWindow and touchEventInWindow for TouchEvent support,\r\n// however, some browsers (chromium) support the TouchEvents API even when running on\r\n// a mouse only device (touchEventInWindow true, but onTouchStartInWindow false)\r\n// so the touchEventInWindow check needs to include an coarse pointer media query\r\nvar supportsTouchEvents = onTouchStartInWindow ||\r\n    (touchEventInWindow && matchMedia('(any-pointer: coarse)').matches);\r\nvar hasTouch = (w.navigator.maxTouchPoints || 0) > 0 || supportsTouchEvents;\r\n// userAgent is used as a backup to correct for known device/browser bugs\r\n// and when the browser doesn't support interaction media queries (pointer and hover)\r\n// see https://caniuse.com/css-media-interaction\r\nvar userAgent = w.navigator.userAgent || '';\r\n// iPads now support a mouse that can hover, however the media query interaction\r\n// feature results always say iPads only have a coarse pointer that can't hover\r\n// even when a mouse is connected (anyFine and anyHover are always false),\r\n// this unfortunately indicates a touch only device but iPads should\r\n// be classified as a hybrid device, so determine if it is an iPad\r\n// to indicate it should be treated as a hybrid device with anyHover true\r\nvar isIPad = matchMedia('(pointer: coarse)').matches &&\r\n    // both iPad and iPhone can \"request desktop site\", which sets the userAgent to Macintosh\r\n    // so need to check both userAgents to determine if it is an iOS device\r\n    // and screen size to separate iPad from iPhone\r\n    /iPad|Macintosh/.test(userAgent) &&\r\n    Math.min(w.screen.width || 0, w.screen.height || 0) >= 768;\r\nvar hasCoarsePrimaryPointer = (matchMedia('(pointer: coarse)').matches ||\r\n    // if the pointer is not coarse and not fine then the browser doesn't support\r\n    // interaction media queries (see https://caniuse.com/css-media-interaction)\r\n    // so if it has onTouchStartInWindow assume it has a coarse primary pointer\r\n    (!matchMedia('(pointer: fine)').matches && onTouchStartInWindow)) &&\r\n    // bug in firefox (as of v81) on hybrid windows devices where the interaction media queries\r\n    // always indicate a touch only device (only has a coarse pointer that can't hover)\r\n    // so assume that the primary pointer is not coarse for firefox windows\r\n    !/Windows.*Firefox/.test(userAgent);\r\nvar hasAnyHoverOrAnyFinePointer = matchMedia('(any-pointer: fine)').matches ||\r\n    matchMedia('(any-hover: hover)').matches ||\r\n    // iPads might have an input device that can hover, so assume it has anyHover\r\n    isIPad ||\r\n    // if no onTouchStartInWindow then the browser is indicating that it is not a touch only device\r\n    // see above note for supportsTouchEvents\r\n    !onTouchStartInWindow;\r\n// a hybrid device is one that both hasTouch and\r\n// any input can hover or has a fine pointer, or the primary pointer is not coarse\r\n// if it's not a hybrid, then if it hasTouch it's touchOnly, otherwise it's mouseOnly\r\nvar deviceType = hasTouch && (hasAnyHoverOrAnyFinePointer || !hasCoarsePrimaryPointer)\r\n    ? 'hybrid'\r\n    : hasTouch\r\n        ? 'touchOnly'\r\n        : 'mouseOnly';\r\nvar primaryInput = deviceType === 'mouseOnly'\r\n    ? 'mouse'\r\n    : deviceType === 'touchOnly'\r\n        ? 'touch'\r\n        : // if the device is a hybrid, then if the primary pointer is coarse\r\n            // assume the primaryInput is touch, otherwise assume it's mouse\r\n            hasCoarsePrimaryPointer\r\n                ? 'touch'\r\n                : 'mouse';\n\nexport { deviceType, primaryInput, supportsPassiveEvents, supportsPointerEvents, supportsTouchEvents };\n"], "mappings": "AAAA;AACA,IAAIA,CAAC,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG;EAAEC,MAAM,EAAE,CAAC,CAAC;EAAEC,SAAS,EAAE,CAAC;AAAE,CAAC;AAC9E,IAAIC,UAAU,GAAG,CAACJ,CAAC,CAACI,UAAU,IAAK,YAAY;EAAE,OAAQ;IAAEC,OAAO,EAAE;EAAM,CAAC;AAAG,CAAE,EAAEC,IAAI,CAACN,CAAC,CAAC;AACzF;AACA;AACA,IAAIO,qBAAqB,GAAG,KAAK;AACjC,IAAIC,OAAO,GAAG;EACV,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAQF,qBAAqB,GAAG,IAAI;EACxC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,IAAIG,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;AAC1BV,CAAC,CAACW,gBAAgB,IAAIX,CAAC,CAACW,gBAAgB,CAAC,GAAG,EAAED,IAAI,EAAEF,OAAO,CAAC;AAC5DR,CAAC,CAACY,mBAAmB,IAAIZ,CAAC,CAACY,mBAAmB,CAAC,GAAG,EAAEF,IAAI,EAAE,KAAK,CAAC;AAChE,IAAIG,qBAAqB,GAAGN,qBAAqB;AACjD,IAAIO,qBAAqB,GAAG,cAAc,IAAId,CAAC;AAC/C,IAAIe,oBAAoB,GAAG,cAAc,IAAIf,CAAC;AAC9C,IAAIgB,kBAAkB,GAAG,YAAY,IAAIhB,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIiB,mBAAmB,GAAGF,oBAAoB,IACzCC,kBAAkB,IAAIZ,UAAU,CAAC,uBAAuB,CAAC,CAACC,OAAQ;AACvE,IAAIa,QAAQ,GAAG,CAAClB,CAAC,CAACG,SAAS,CAACgB,cAAc,IAAI,CAAC,IAAI,CAAC,IAAIF,mBAAmB;AAC3E;AACA;AACA;AACA,IAAIG,SAAS,GAAGpB,CAAC,CAACG,SAAS,CAACiB,SAAS,IAAI,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAGjB,UAAU,CAAC,mBAAmB,CAAC,CAACC,OAAO;AAChD;AACA;AACA;AACA,gBAAgB,CAACiB,IAAI,CAACF,SAAS,CAAC,IAChCG,IAAI,CAACC,GAAG,CAACxB,CAAC,CAACE,MAAM,CAACuB,KAAK,IAAI,CAAC,EAAEzB,CAAC,CAACE,MAAM,CAACwB,MAAM,IAAI,CAAC,CAAC,IAAI,GAAG;AAC9D,IAAIC,uBAAuB,GAAG,CAACvB,UAAU,CAAC,mBAAmB,CAAC,CAACC,OAAO;AAClE;AACA;AACA;AACC,CAACD,UAAU,CAAC,iBAAiB,CAAC,CAACC,OAAO,IAAIU,oBAAqB;AAChE;AACA;AACA;AACA,CAAC,kBAAkB,CAACO,IAAI,CAACF,SAAS,CAAC;AACvC,IAAIQ,2BAA2B,GAAGxB,UAAU,CAAC,qBAAqB,CAAC,CAACC,OAAO,IACvED,UAAU,CAAC,oBAAoB,CAAC,CAACC,OAAO;AACxC;AACAgB,MAAM;AACN;AACA;AACA,CAACN,oBAAoB;AACzB;AACA;AACA;AACA,IAAIc,UAAU,GAAGX,QAAQ,KAAKU,2BAA2B,IAAI,CAACD,uBAAuB,CAAC,GAChF,QAAQ,GACRT,QAAQ,GACJ,WAAW,GACX,WAAW;AACrB,IAAIY,YAAY,GAAGD,UAAU,KAAK,WAAW,GACvC,OAAO,GACPA,UAAU,KAAK,WAAW,GACtB,OAAO;AACP;AACE;AACAF,uBAAuB,GACjB,OAAO,GACP,OAAO;AAEzB,SAASE,UAAU,EAAEC,YAAY,EAAEjB,qBAAqB,EAAEC,qBAAqB,EAAEG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}