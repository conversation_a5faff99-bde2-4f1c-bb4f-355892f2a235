<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Contacts" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex align-items-center flex-wrap gap-2">
                    <div class="flex-grow-1">
                        <button class="btn btn-primary add-btn" data-bs-toggle="modal" data-bs-target="#showModal"
                            (click)="openModal(content)"><i class="ri-add-fill me-1 align-bottom"></i> Add
                            Contacts</button>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="hstack text-nowrap gap-2" ngbDropdown>
                            <button class="btn btn-soft-danger" id="remove-actions" style="display: none"
                                (click)="deleteMultiple(deleteModel)"><i class="ri-delete-bin-2-line"></i></button>
                            <button class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#addmembers"><i
                                    class="ri-filter-2-line me-1 align-bottom"></i> Filters</button>
                            <button class="btn btn-soft-success" (click)="csvFileExport()">Export</button>
                            <button type="button" id="dropdownMenuLink1" data-bs-toggle="dropdown" aria-expanded="false"
                                class="btn btn-soft-info arrow-none" ngbDropdownToggle><i
                                    class="ri-more-2-fill"></i></button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink1" ngbDropdownMenu>
                                <li><a class="dropdown-item" href="javascript:void(0);">All</a></li>
                                <li><a class="dropdown-item" href="javascript:void(0);">Last Week</a></li>
                                <li><a class="dropdown-item" href="javascript:void(0);">Last Month</a></li>
                                <li><a class="dropdown-item" href="javascript:void(0);">Last Year</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end col-->
    <div class="col-xxl-9">
        <div class="card" id="contactList">
            <div class="card-header">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="search-box">
                            <input type="text" name="searchTerm" class="form-control"
                                placeholder="Search for contact..." [(ngModel)]="searchTerm" (ngModelChange)="performSearch()">
                            <i class="ri-search-line search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-auto ms-auto">
                        <div class="d-flex align-items-center gap-2">
                            <span class="text-muted text-nowrap">Sort by: </span>
                            <select class="form-control mb-0" data-choices data-choices-search-false
                                id="choices-single-default" (click)="SortFilter();">
                                <option value="Dname">Name</option>
                                <option value="Dcompany">Company</option>
                                <option value="Dscore">Lead</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div>
                    <div class="table-responsive table-card mb-2 table-nowrap">
                        <table class="table">
                            <thead>
                                <tr class="bg-light">
                                    <th scope="col" style="width: 50px;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkAll" value="option"
                                                [(ngModel)]="masterSelected" (change)="checkUncheckAll($event)">
                                        </div>
                                    </th>
                                    <th class="sort" (click)="onSort('name')">Name</th>
                                    <th class="sort" (click)="onSort('company')">Company
                                    </th>
                                    <th class="sort" (click)="onSort('email')">Email ID
                                    </th>
                                    <th class="sort" (click)="onSort('phone')">Phone No
                                    </th>
                                    <th class="sort" (click)="onSort('score')">Lead Score
                                    </th>
                                    <th class="sort" (click)="onSort('tags')">Tags</th>
                                    <th class="sort" (click)="onSort('date')">Last
                                        Contacted</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (data of contacts | sortBy:sortBy:sortField; track $index) {
                                    <tr id="c_{{data._id}}">
                                    <th scope="row">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="checkAll"
                                                value="{{data._id}}" (change)="onCheckboxChange($event)"
                                                [(ngModel)]="data.state">
                                        </div>
                                    </th>
                                    <td class="name">
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0"><img src="assets/images/users/{{data.image_src}}"
                                                    alt="" class="avatar-xs rounded-circle"></div>
                                            <div class="flex-grow-1 ms-2 name">{{data.name}}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.company" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.email" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.phone" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.lead_score" [term]="searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td class="tags">
                                        <div class="d-flex gap-1">
                                            @for(tag of data.tags;track $index){
                                            <span class="badge bg-primary-subtle text-primary"
                                                >{{tag}}</span>
                                            }
                                        </div>
                                    </td>
                                    <td class="date">{{data.last_contacted | date :'longDate'}}</td>
                                    <td>
                                        <ul class="list-inline hstack gap-2 mb-0">
                                            <li class="list-inline-item edit" ngbTooltip="Call" placement="top">
                                                <a href="javascript:void(0);" class="text-muted d-inline-block">
                                                    <i class="ri-phone-line fs-16"></i>
                                                </a>
                                            </li>
                                            <li class="list-inline-item edit" ngbTooltip="Message" placement="top">
                                                <a href="javascript:void(0);" class="text-muted d-inline-block">
                                                    <i class="ri-question-answer-line fs-16"></i>
                                                </a>
                                            </li>
                                            <li class="list-inline-item">
                                                <div class="dropdown" ngbDropdown>
                                                    <button class="btn btn-soft-secondary btn-sm dropdown arrow-none"
                                                        type="button" data-bs-toggle="dropdown" aria-expanded="false"
                                                        ngbDropdownToggle>
                                                        <i class="ri-more-fill align-middle"></i>
                                                    </button>
                                                    <ul class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                                                        <li><a class="dropdown-item" href="javascript:void(0);"
                                                                (click)="viewDataGet($index)"><i
                                                                    class="ri-eye-fill align-bottom me-2 text-muted float-start"></i>
                                                                View</a></li>
                                                        <li><a class="dropdown-item edit-item-btn"
                                                                data-bs-toggle="modal"
                                                                (click)="editDataGet($index,content)"><i
                                                                    class="ri-pencil-fill align-bottom me-2 text-muted float-start"></i>
                                                                Edit</a></li>
                                                        <li>
                                                            <a class="dropdown-item remove-item-btn"
                                                                data-bs-toggle="modal"
                                                                (click)="confirm(deleteModel,data._id)">
                                                                <i
                                                                    class="ri-delete-bin-fill align-bottom me-2 text-muted float-start"></i>
                                                                Delete
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </li>
                                        </ul>
                                    </td>
                                </tr>
                            }
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-end mt-3">
                        <!-- Pagination -->
                        <ngb-pagination [collectionSize]="allcontacts?.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                        </ngb-pagination>
                        <!-- End Pagination -->
                        <div id="elmLoader">
                            <div class="spinner-border text-primary avatar-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contacts Create Model -->
                <ng-template #content role="document" let-modal>
                    <div class="modal-header bg-info-subtle  p-3">
                        <h5 class="modal-title" id="exampleModalLabel">Add Contact</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
                    </div>
                    <form (ngSubmit)="saveUser()" [formGroup]="contactsForm" class="tablelist-form" autocomplete="off">
                        <div class="modal-body">
                            <input type="hidden" id="id-field" />
                            <div class="row g-3">
                                <div class="col-lg-12">
                                    <div class="text-center">
                                        <div class="position-relative d-inline-block">
                                            <input type="hidden" name="id" value="" formControlName="_id" />
                                            <div class="position-absolute  bottom-0 end-0">
                                                <label for="customer-image-input" class="mb-0" data-bs-toggle="tooltip"
                                                    data-bs-placement="right" title="Select Image">
                                                    <div class="avatar-xs cursor-pointer">
                                                        <div
                                                            class="avatar-title bg-light border rounded-circle text-muted">
                                                            <i class="ri-image-fill"></i>
                                                        </div>
                                                    </div>
                                                </label>
                                                <input class="form-control d-none" id="customer-image-input" type="file"
                                                    accept="image/png, image/gif, image/jpeg"
                                                    [ngClass]="{ 'is-invalid': submitted && form['image_src'].errors }"
                                                    (change)="fileChange($event)">
                                                @if(submitted && form['image_src'].errors){
                                                <div class="invalid-feedback" align="left">
                                                    @if(form['image_src'].errors['required']){
                                                    <div>Profile is
                                                        required</div>
                                                    }
                                                </div>
                                                }
                                            </div>
                                            <div class="avatar-lg p-1">
                                                <div class="avatar-title bg-light rounded-circle">
                                                    <img src="assets/images/users/user-dummy-img.jpg" id="customer-img"
                                                        class="avatar-md rounded-circle object-fit-cover" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <label for="name-field" class="form-label">Name</label>
                                        <input type="text" id="customername-field" class="form-control"
                                            placeholder="Enter name" required formControlName="name"
                                            [ngClass]="{ 'is-invalid': submitted && form['name'].errors }" />
                                        @if(submitted && form['name'].errors){
                                        <div class="invalid-feedback"
                                            align="left">
                                            @if(form['name'].errors['required']){
                                            <div>Name is required</div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div>
                                        <label for="company_name-field" class="form-label">Company Name</label>
                                        <input type="text" id="company_name-field" class="form-control"
                                            placeholder="Enter company name" required formControlName="company"
                                            [ngClass]="{ 'is-invalid': submitted && form['company'].errors }" />
                                        @if(submitted && form['company'].errors){
                                        <div class="invalid-feedback"
                                            align="left">
                                            @if(form['company'].errors['required']){
                                            <div>Company Name is required </div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div>
                                        <label for="designation-field" class="form-label">Designation</label>
                                        <input type="text" id="designation-field" class="form-control"
                                            placeholder="Enter Designation" required formControlName="designation"
                                            [ngClass]="{ 'is-invalid': submitted && form['designation'].errors }" />
                                        @if(submitted && form['designation'].errors){
                                        <div class="invalid-feedback"
                                            align="left">
                                            @if(form['designation'].errors['required']){
                                            <div>Designation is requireds </div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div>
                                        <label for="email_id-field" class="form-label">Email ID</label>
                                        <input type="text" id="email_id-field" class="form-control"
                                            placeholder="Enter email" required formControlName="email"
                                            [ngClass]="{ 'is-invalid': submitted && form['email'].errors }" />
                                        @if(submitted && form['email'].errors){
                                        <div class="invalid-feedback"
                                            align="left">
                                            @if(form['email'].errors['required']){
                                            <div>Email ID is required</div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div>
                                        <label for="phone-field" class="form-label">Phone</label>
                                        <input type="text" id="phone-field" class="form-control"
                                            placeholder="Enter phone no" required formControlName="phone"
                                            [ngClass]="{ 'is-invalid': submitted && form['phone'].errors }" />
                                        @if(submitted && form['phone'].errors){
                                        <div class="invalid-feedback"
                                            align="left">
                                            @if(form['phone'].errors['required']){
                                            <div>Phone is required</div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div>
                                        <label for="lead_score-field" class="form-label">Lead Score</label>
                                        <input type="text" id="lead_score-field" class="form-control"
                                            placeholder="Enter value" required formControlName="lead_score"
                                            [ngClass]="{ 'is-invalid': submitted && form['lead_score'].errors }" />
                                        @if(submitted && form['lead_score'].errors){
                                        <div class="invalid-feedback"
                                            align="left">
                                            @if(form['lead_score'].errors['required']){
                                            <div>Lead Score is required
                                            </div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div>
                                        <label for="taginput-choices"
                                            class="form-label font-size-13 text-muted">Tags</label>
                                        <ng-select [items]="selectValue" [multiple]="true" formControlName="tags"
                                            [ngClass]="{ 'is-invalid': submitted && form['tags'].errors }"></ng-select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="date-field" class="form-label">Contacted</label>
                                    <input class="form-control flatpickr-input" type="text" mwlFlatpickr
                                        [altInput]="true" [convertModelValue]="true" formControlName="last_contacted"
                                        [ngClass]="{ 'is-invalid': submitted && form['last_contacted'].errors }">
                                    @if(submitted && form['last_contacted'].errors){
                                    <div class="invalid-feedback"
                                        align="left">
                                        @if(form['last_contacted'].errors['required']){
                                        <div>Contacted Date is
                                            required</div>
                                        }
                                    </div>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="hstack gap-2 justify-content-end">
                                <button type="button" class="btn btn-light" data-bs-dismiss="modal"
                                    (click)="modal.close('Close click')">Close</button>
                                <button type="submit" class="btn btn-success" id="add-btn">Add Contact</button>
                            </div>
                        </div>
                    </form>
                </ng-template>

            </div>
        </div>
        <!--end card-->
    </div>
    <!--end col-->
    <div class="col-xxl-3">
        <div class="card">
            <div class="card-body text-center contact-details">
                <div class="position-relative d-inline-block">
                    <img src="assets/images/users/avatar-10.jpg" alt="" class="avatar-lg rounded-circle img-thumbnail">
                    <span class="contact-active position-absolute rounded-circle bg-success"></span><span
                        class="visually-hidden"></span>
                </div>
                <h5 class="mt-4 mb-1">Tonya Noble</h5>
                <p class="text-muted">Nesta Technologies</p>

                <ul class="list-inline mb-0">
                    <li class="list-inline-item avatar-xs">
                        <a href="javascript:void(0);" class="avatar-title bg-success-subtle text-success fs-15 rounded">
                            <i class="ri-phone-line"></i>
                        </a>
                    </li>
                    <li class="list-inline-item avatar-xs">
                        <a href="javascript:void(0);" class="avatar-title bg-danger-subtle text-danger fs-15 rounded">
                            <i class="ri-mail-line"></i>
                        </a>
                    </li>
                    <li class="list-inline-item avatar-xs">
                        <a href="javascript:void(0);" class="avatar-title bg-warning-subtle text-warning fs-15 rounded">
                            <i class="ri-question-answer-line"></i>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <h6 class="text-muted text-uppercase fw-semibold mb-3">Personal Information</h6>
                <p class="text-muted mb-4">Hello, I'm Tonya Noble, The most effective objective is one that is tailored
                    to the job you are applying for. It states what kind of career you are seeking, and what skills and
                    experiences.</p>
                <div class="table-responsive table-card">
                    <table class="table table-borderless mb-0">
                        <tbody>
                            <tr>
                                <td class="fw-medium" scope="row">Designation</td>
                                <td class="designation">Lead Designer / Developer</td>
                            </tr>
                            <tr>
                                <td class="fw-medium" scope="row">Email ID</td>
                                <td class="email">tonyanoble&#64;velzon.com</td>
                            </tr>
                            <tr>
                                <td class="fw-medium" scope="row">Phone No</td>
                                <td class="phone">************</td>
                            </tr>
                            <tr>
                                <td class="fw-medium" scope="row">Lead Score</td>
                                <td class="l_score">154</td>
                            </tr>
                            <tr>
                                <td class="fw-medium" scope="row">Tags</td>
                                <td class="tags-list">
                                    <div class="d-flex gap-1">
                                        <span class="badge bg-primary-subtle text-primary">Lead</span>
                                        <span class="badge bg-primary-subtle text-primary">Partner</span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-medium" scope="row">Last Contacted</td>
                                <td class="contacted_date">15 Dec, 2021 <small class="text-muted">08:58AM</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <!--end card-->
    </div>
    <!--end col-->
</div>
<!--end row-->

<!-- removeItemModal -->
<ng-template #deleteModel let-modal>
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="deleteRecord-close"
                (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body">
            <div class="mt-2 text-center">
                <lord-icon src="https://cdn.lordicon.com/gsqxdxog.json" trigger="loop"
                    colors="primary:#405189,secondary:#f06548" style="width:90px;height:90px"></lord-icon>
                <div class="mt-4 pt-2 fs-15 mx-4 mx-sm-5">
                    <h4>You are about to delete a contact ?</h4>
                    <p class="text-muted mx-4 mb-0">Deleting your contact will remove all of your information from our
                        database.</p>
                </div>
            </div>
            <div class="d-flex gap-2 justify-content-center mt-4 mb-2">
                <button class="btn btn-link link-success fw-medium text-decoration-none" data-bs-dismiss="modal"
                    id="deleteRecord-close" (click)="modal.close('Close click')"><i
                        class="ri-close-line me-1 align-middle"></i> Close</button>
                <button type="button" class="btn w-sm btn-danger " id="delete-product" (click)="deleteData(deleteId)"
                    (click)="modal.close('Close click')">Yes, Delete It!</button>
            </div>
        </div>
    </div><!-- /.modal-content -->
</ng-template>