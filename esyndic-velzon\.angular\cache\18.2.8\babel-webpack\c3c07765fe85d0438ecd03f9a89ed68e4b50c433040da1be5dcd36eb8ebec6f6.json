{"ast": null, "code": "import env from './core/env.js';\nvar dpr = 1;\nif (env.hasGlobalWindow) {\n  dpr = Math.max(window.devicePixelRatio || window.screen && window.screen.deviceXDPI / window.screen.logicalXDPI || 1, 1);\n}\nexport var debugMode = 0;\nexport var devicePixelRatio = dpr;\nexport var DARK_MODE_THRESHOLD = 0.4;\nexport var DARK_LABEL_COLOR = '#333';\nexport var LIGHT_LABEL_COLOR = '#ccc';\nexport var LIGHTER_LABEL_COLOR = '#eee';", "map": {"version": 3, "names": ["env", "dpr", "hasGlobalWindow", "Math", "max", "window", "devicePixelRatio", "screen", "deviceXDPI", "logicalXDPI", "debugMode", "DARK_MODE_THRESHOLD", "DARK_LABEL_COLOR", "LIGHT_LABEL_COLOR", "LIGHTER_LABEL_COLOR"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/zrender/lib/config.js"], "sourcesContent": ["import env from './core/env.js';\nvar dpr = 1;\nif (env.hasGlobalWindow) {\n    dpr = Math.max(window.devicePixelRatio\n        || (window.screen && window.screen.deviceXDPI / window.screen.logicalXDPI)\n        || 1, 1);\n}\nexport var debugMode = 0;\nexport var devicePixelRatio = dpr;\nexport var DARK_MODE_THRESHOLD = 0.4;\nexport var DARK_LABEL_COLOR = '#333';\nexport var LIGHT_LABEL_COLOR = '#ccc';\nexport var LIGHTER_LABEL_COLOR = '#eee';\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,eAAe;AAC/B,IAAIC,GAAG,GAAG,CAAC;AACX,IAAID,GAAG,CAACE,eAAe,EAAE;EACrBD,GAAG,GAAGE,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,gBAAgB,IAC9BD,MAAM,CAACE,MAAM,IAAIF,MAAM,CAACE,MAAM,CAACC,UAAU,GAAGH,MAAM,CAACE,MAAM,CAACE,WAAY,IACvE,CAAC,EAAE,CAAC,CAAC;AAChB;AACA,OAAO,IAAIC,SAAS,GAAG,CAAC;AACxB,OAAO,IAAIJ,gBAAgB,GAAGL,GAAG;AACjC,OAAO,IAAIU,mBAAmB,GAAG,GAAG;AACpC,OAAO,IAAIC,gBAAgB,GAAG,MAAM;AACpC,OAAO,IAAIC,iBAAiB,GAAG,MAAM;AACrC,OAAO,IAAIC,mBAAmB,GAAG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}