{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport InsideZoomModel from './InsideZoomModel.js';\nimport InsideZoomView from './InsideZoomView.js';\nimport { installDataZoomRoamProcessor } from './roams.js';\nimport installCommon from './installCommon.js';\nexport function install(registers) {\n  installCommon(registers);\n  registers.registerComponentModel(InsideZoomModel);\n  registers.registerComponentView(InsideZoomView);\n  installDataZoomRoamProcessor(registers);\n}", "map": {"version": 3, "names": ["InsideZoomModel", "InsideZoomView", "installDataZoomRoamProcessor", "installCommon", "install", "registers", "registerComponentModel", "registerComponentView"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/component/dataZoom/installDataZoomInside.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport InsideZoomModel from './InsideZoomModel.js';\nimport InsideZoomView from './InsideZoomView.js';\nimport { installDataZoomRoamProcessor } from './roams.js';\nimport installCommon from './installCommon.js';\nexport function install(registers) {\n  installCommon(registers);\n  registers.registerComponentModel(InsideZoomModel);\n  registers.registerComponentView(InsideZoomView);\n  installDataZoomRoamProcessor(registers);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,eAAe,MAAM,sBAAsB;AAClD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,SAASC,4BAA4B,QAAQ,YAAY;AACzD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCF,aAAa,CAACE,SAAS,CAAC;EACxBA,SAAS,CAACC,sBAAsB,CAACN,eAAe,CAAC;EACjDK,SAAS,CAACE,qBAAqB,CAACN,cAAc,CAAC;EAC/CC,4BAA4B,CAACG,SAAS,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}