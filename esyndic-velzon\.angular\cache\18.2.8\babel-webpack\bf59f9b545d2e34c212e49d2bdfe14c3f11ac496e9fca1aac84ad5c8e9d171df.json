{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport MarkerModel from './MarkerModel.js';\nvar MarkAreaModel = /** @class */function (_super) {\n  __extends(MarkAreaModel, _super);\n  function MarkAreaModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkAreaModel.type;\n    return _this;\n  }\n  MarkAreaModel.prototype.createMarkerModelFromSeries = function (markerOpt, masterMarkerModel, ecModel) {\n    return new MarkAreaModel(markerOpt, masterMarkerModel, ecModel);\n  };\n  MarkAreaModel.type = 'markArea';\n  MarkAreaModel.defaultOption = {\n    // zlevel: 0,\n    // PENDING\n    z: 1,\n    tooltip: {\n      trigger: 'item'\n    },\n    // markArea should fixed on the coordinate system\n    animation: false,\n    label: {\n      show: true,\n      position: 'top'\n    },\n    itemStyle: {\n      // color and borderColor default to use color from series\n      // color: 'auto'\n      // borderColor: 'auto'\n      borderWidth: 0\n    },\n    emphasis: {\n      label: {\n        show: true,\n        position: 'top'\n      }\n    }\n  };\n  return MarkAreaModel;\n}(MarkerModel);\nexport default MarkAreaModel;", "map": {"version": 3, "names": ["__extends", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkAreaModel", "_super", "_this", "apply", "arguments", "type", "prototype", "createMarkerModelFromSeries", "markerOpt", "masterMarkerModel", "ecModel", "defaultOption", "z", "tooltip", "trigger", "animation", "label", "show", "position", "itemStyle", "borderWidth", "emphasis"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/component/marker/MarkAreaModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport MarkerModel from './MarkerModel.js';\nvar MarkAreaModel = /** @class */function (_super) {\n  __extends(MarkAreaModel, _super);\n  function MarkAreaModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkAreaModel.type;\n    return _this;\n  }\n  MarkAreaModel.prototype.createMarkerModelFromSeries = function (markerOpt, masterMarkerModel, ecModel) {\n    return new MarkAreaModel(markerOpt, masterMarkerModel, ecModel);\n  };\n  MarkAreaModel.type = 'markArea';\n  MarkAreaModel.defaultOption = {\n    // zlevel: 0,\n    // PENDING\n    z: 1,\n    tooltip: {\n      trigger: 'item'\n    },\n    // markArea should fixed on the coordinate system\n    animation: false,\n    label: {\n      show: true,\n      position: 'top'\n    },\n    itemStyle: {\n      // color and borderColor default to use color from series\n      // color: 'auto'\n      // borderColor: 'auto'\n      borderWidth: 0\n    },\n    emphasis: {\n      label: {\n        show: true,\n        position: 'top'\n      }\n    }\n  };\n  return MarkAreaModel;\n}(MarkerModel);\nexport default MarkAreaModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,IAAIC,aAAa,GAAG,aAAa,UAAUC,MAAM,EAAE;EACjDH,SAAS,CAACE,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,aAAa,CAACK,IAAI;IAC/B,OAAOH,KAAK;EACd;EACAF,aAAa,CAACM,SAAS,CAACC,2BAA2B,GAAG,UAAUC,SAAS,EAAEC,iBAAiB,EAAEC,OAAO,EAAE;IACrG,OAAO,IAAIV,aAAa,CAACQ,SAAS,EAAEC,iBAAiB,EAAEC,OAAO,CAAC;EACjE,CAAC;EACDV,aAAa,CAACK,IAAI,GAAG,UAAU;EAC/BL,aAAa,CAACW,aAAa,GAAG;IAC5B;IACA;IACAC,CAAC,EAAE,CAAC;IACJC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACD;IACAC,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE;MACLC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACT;MACA;MACA;MACAC,WAAW,EAAE;IACf,CAAC;IACDC,QAAQ,EAAE;MACRL,KAAK,EAAE;QACLC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EACD,OAAOlB,aAAa;AACtB,CAAC,CAACD,WAAW,CAAC;AACd,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}