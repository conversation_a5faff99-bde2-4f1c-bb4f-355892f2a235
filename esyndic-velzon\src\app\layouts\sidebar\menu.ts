import { MenuItem } from './menu.model';

export const MENU: MenuItem[] = [
  {
    id: 1,
    label: 'MENUITEMS.MENU.TEXT',
    isTitle: true
  },
  {
    id: 2,
    label: 'MENUITEMS.DASHBOARDS.TEXT',
    icon: 'las la-tachometer-alt',
    link: '/dashboard'
  },
  {
    id: 3,
    label: 'MENUITEMS.BUILDINGS.TEXT',
    icon: 'las la-building',
    isCollapsed: true,
    subItems: [
      {
        id: 31,
        label: 'MENUITEMS.BUILDINGS.LIST.ALL',
        link: '/buildings',
        parentId: 3
      },
      {
        id: 32,
        label: 'MENUITEMS.BUILDINGS.LIST.ADD',
        link: '/buildings/add',
        parentId: 3
      }
    ]
  },
  {
    id: 4,
    label: 'MENUITEMS.USERS.TEXT',
    icon: 'las la-users',
    isCollapsed: true,
    subItems: [
      {
        id: 41,
        label: 'MENUITEMS.USERS.LIST.RESIDENTS',
        link: '/users/residents',
        parentId: 4
      },
      {
        id: 42,
        label: 'MENUITEMS.USERS.LIST.OWNERS',
        link: '/users/owners',
        parentId: 4
      }
    ]
  },
  {
    id: 5,
    label: 'MENUITEMS.MEETINGS.TEXT',
    icon: 'las la-calendar',
    isCollapsed: true,
    subItems: [
      {
        id: 51,
        label: 'MENUITEMS.MEETINGS.LIST.ALL',
        link: '/meetings',
        parentId: 5
      },
      {
        id: 52,
        label: 'MENUITEMS.MEETINGS.LIST.SCHEDULE',
        link: '/meetings/schedule',
        parentId: 5
      }
    ]
  },
  {
    id: 6,
    label: 'MENUITEMS.INCIDENTS.TEXT',
    icon: 'las la-exclamation-triangle',
    isCollapsed: true,
    subItems: [
      {
        id: 61,
        label: 'MENUITEMS.INCIDENTS.LIST.ALL',
        link: '/incidents',
        parentId: 6
      },
      {
        id: 62,
        label: 'MENUITEMS.INCIDENTS.LIST.REPORT',
        link: '/incidents/report',
        parentId: 6
      }
    ]
  },
  {
    id: 7,
    label: 'MENUITEMS.PAYMENTS.TEXT',
    icon: 'las la-credit-card',
    isCollapsed: true,
    subItems: [
      {
        id: 71,
        label: 'MENUITEMS.PAYMENTS.LIST.ALL',
        link: '/payments',
        parentId: 7
      },
      {
        id: 72,
        label: 'MENUITEMS.PAYMENTS.LIST.PENDING',
        link: '/payments/pending',
        parentId: 7
      }
    ]
  },
  {
    id: 8,
    label: 'MENUITEMS.SETTINGS.TEXT',
    icon: 'las la-cog',
    link: '/settings'
  }
];
