export class KeycloakAuthGuard {
    constructor(router, keycloakAngular) {
        this.router = router;
        this.keycloakAngular = keycloakAngular;
    }
    async canActivate(route, state) {
        try {
            this.authenticated = await this.keycloakAngular.isLoggedIn();
            this.roles = await this.keycloakAngular.getUserRoles(true);
            return await this.isAccessAllowed(route, state);
        }
        catch (error) {
            throw new Error('An error happened during access validation. Details:' + error);
        }
    }
}
//# sourceMappingURL=data:application/json;base64,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