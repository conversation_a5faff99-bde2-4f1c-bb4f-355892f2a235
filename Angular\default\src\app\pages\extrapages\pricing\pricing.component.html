<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Pages" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row justify-content-center mt-4">
    <div class="col-lg-5">
        <div class="text-center mb-4">
            <h4 class="fw-semibold fs-22">Plans & Pricing</h4>
            <p class="text-muted mb-4 fs-15">Simple pricing. No hidden fees. Advanced features for you business.</p>

            <div class="d-inline-flex">
                <ul ngbNav #customNav="ngbNav" [activeId]="1" class="nav nav-pills arrow-navtabs plan-nav rounded mb-3 p-1" id="pills-tab" role="tablist" role="tablist">
                    <li [ngbNavItem]="1" class="nav-item">
                        <a ngbNavLink class="nav-link" data-bs-toggle="tab" role="tab">
                            Monthly
                        </a>
                        <ng-template ngbNavContent>
                            <div class="row">
                                @for (data of MonthlyPlan; track $index) {
                                <div class="col-xxl-3 col-lg-6">
                                    <div class=" card pricing-box" [ngClass]=" { 'ribbon-box right': data.ribbon === true}">
                                        <div class="card-body bg-light m-2 p-4">
                                            <div class="ribbon-two ribbon-two-danger" [ngClass]=" { 'd-none': data.ribbon !== true}"><span>Popular</span>
                                            </div>
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="flex-grow-1">
                                                    <h5 class="mb-0 fw-semibold">{{data.type}}</h5>
                                                </div>
                                                <div class="ms-auto">
                                                    <h2 class="month mb-0">${{data.rate}} <small class="fs-13 text-muted">/Month</small></h2>
                                                </div>
                                            </div>

                                            <p class="text-muted">{{data.description}}</p>
                                            <ul class="list-unstyled vstack gap-3">
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-success me-1">
                                                            <i class="ri-checkbox-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <b>{{data.projects}}</b> Projects
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-success me-1">
                                                            <i class="ri-checkbox-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <b>{{data.Customers}}</b> Customers
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-success me-1">
                                                            <i class="ri-checkbox-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            Scalable Bandwidth
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-success me-1">
                                                            <i class="ri-checkbox-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <b>{{data.FTP}}</b> FTP Login
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-{{data.supportClass}} me-1">
                                                            <i class="ri-{{data.supportClassSymbol}}-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <b>24/7</b> Support
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-{{data.storageClass}} me-1">
                                                            <i class="ri-{{data.storageClassSymbol}}-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <b>Unlimited</b> Storage
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-{{data.domainClass}} me-1">
                                                            <i class="ri-{{data.domainClassSymbol}}-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            Domain
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                            <div class="mt-3 pt-2">
                                                <a href="javascript:void(0);" class="btn btn-{{data.planButtonClassname}} w-100" [ngClass]=" { 'disabled': data.planButtonClassname === 'danger'}">Your
                                                    Current Plan</a>
                                            </div>
                                        </div>
                                    </div>
                                </div><!--end col-->
                                }
                            </div>
                        </ng-template>
                    </li>
                    <li [ngbNavItem]="2" class="nav-item">
                        <a ngbNavLink class="nav-link" data-bs-toggle="tab" role="tab">
                            Annually <span class="badge bg-success">25% Off</span>
                        </a>
                        <ng-template ngbNavContent>
                            <div class="row">
                                @for (data of YearlyPlan; track $index) {
                                <div class="col-xxl-3 col-lg-6">
                                    <div class="card pricing-box" [ngClass]=" { 'ribbon-box right': data.ribbon === true}">
                                        <div class="card-body bg-light m-2 p-4">
                                            <div class="ribbon-two ribbon-two-danger" [ngClass]=" { 'd-none': data.ribbon !== true}"><span>Popular</span>
                                            </div>
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="flex-grow-1">
                                                    <h5 class="mb-0 fw-semibold">{{data.type}}</h5>
                                                </div>
                                                <div class="ms-auto">
                                                    <h2 class="annual mb-0"><small class="fs-16"><del>${{data.price}}</del></small>${{data.rate}}
                                                        <small class="fs-13 text-muted">/Year</small>
                                                    </h2>
                                                </div>
                                            </div>

                                            <p class="text-muted">{{data.description}}</p>
                                            <ul class="list-unstyled vstack gap-3">
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-success me-1">
                                                            <i class="ri-checkbox-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <b>{{data.projects}}</b> Projects
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-success me-1">
                                                            <i class="ri-checkbox-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <b>{{data.Customers}}</b> Customers
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-success me-1">
                                                            <i class="ri-checkbox-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            Scalable Bandwidth
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-success me-1">
                                                            <i class="ri-checkbox-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <b>{{data.FTP}}</b> FTP Login
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-{{data.supportClass}} me-1">
                                                            <i class="ri-{{data.supportClassSymbol}}-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <b>24/7</b> Support
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-{{data.storageClass}} me-1">
                                                            <i class="ri-{{data.storageClassSymbol}}-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <b>Unlimited</b> Storage
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0 text-{{data.domainClass}} me-1">
                                                            <i class="ri-{{data.domainClassSymbol}}-circle-fill fs-15 align-middle"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            Domain
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                            <div class="mt-3 pt-2">
                                                <a href="javascript:void(0);" class="btn btn-{{data.planButtonClassname}} w-100" [ngClass]=" { 'disabled': data.planButtonClassname === 'danger'}">Your
                                                    Current Plan</a>
                                            </div>
                                        </div>
                                    </div>
                                </div><!--end col-->
                                }
                            </div>
                        </ng-template>
                    </li>
                </ul>
            </div>
        </div>
    </div><!--end col-->
</div><!--end row-->

<div class="row">
    <div [ngbNavOutlet]="customNav"></div>
</div><!--end row-->

<div class="row justify-content-center mt-5">
    <div class="col-lg-5">
        <div class="text-center mb-4 pb-2">
            <h4 class="fw-semibold fs-22">Choose the plan that's right for you</h4>
            <p class="text-muted mb-4 fs-15">Simple pricing. No hidden fees. Advanced features for you business.</p>
        </div>
    </div><!--end col-->
</div><!--end row-->

<div class="row justify-content-center">
    <div class="col-xl-9">
        <div class="row">
            @for (data of pricingPlan; track $index) {
            <div class="col-lg-4">
                <div class=" card pricing-box" [ngClass]=" { 'ribbon-box right': data.ribbon === true}">
                    <div class="card-body p-4 m-2">
                        <div class="ribbon-two ribbon-two-danger" [ngClass]=" { 'd-none': data.ribbon !== true}">
                            <span>Popular</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="mb-1 fw-semibold">{{data.type}}</h5>
                                <p class="text-muted mb-0">{{data.purpose}}</p>
                            </div>
                            <div class="avatar-sm">
                                <div class="avatar-title bg-light rounded-circle text-primary">
                                    <i class="{{data.planIcon}} fs-20"></i>
                                </div>
                            </div>
                        </div>
                        <div class="pt-4">
                            <h2><sup><small>$</small></sup>{{data.rate}} <span class="fs-13 text-muted">/Month</span>
                            </h2>
                        </div>
                        <hr class="my-4 text-muted">
                        <div>
                            <ul class="list-unstyled text-muted vstack gap-3">
                                <li>
                                    <div class="d-flex">
                                        <div class="flex-shrink-0 text-success me-1">
                                            <i class="ri-checkbox-circle-fill fs-15 align-middle"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            Upto <b>{{data.projects}}</b> Projects
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="d-flex">
                                        <div class="flex-shrink-0 text-success me-1">
                                            <i class="ri-checkbox-circle-fill fs-15 align-middle"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            Upto <b>{{data.Customers}}</b> Customers
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="d-flex">
                                        <div class="flex-shrink-0 text-success me-1">
                                            <i class="ri-checkbox-circle-fill fs-15 align-middle"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            Scalable Bandwidth
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="d-flex">
                                        <div class="flex-shrink-0 text-success me-1">
                                            <i class="ri-checkbox-circle-fill fs-15 align-middle"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <b>{{data.FTP}}</b> FTP Login
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="d-flex">
                                        <div class="flex-shrink-0 text-{{data.supportClass}} me-1">
                                            <i class="ri-{{data.supportClassSymbol}}-circle-fill fs-15 align-middle"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <b>24/7</b> Support
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="d-flex">
                                        <div class="flex-shrink-0 text-{{data.storageClass}} me-1">
                                            <i class="ri-{{data.storageClassSymbol}}-circle-fill fs-15 align-middle"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <b>Unlimited</b> Storage
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="d-flex">
                                        <div class="flex-shrink-0 text-{{data.domainClass}} me-1">
                                            <i class="ri-{{data.domainClassSymbol}}-circle-fill fs-15 align-middle"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            Domain
                                        </div>
                                    </div>
                                </li>
                            </ul>
                            <div class="mt-4">
                                <a href="javascript:void(0);" class="btn btn-{{data.planButtonClassname}} w-100 waves-effect waves-light">Sign up
                                    free</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end col-->
            }
        </div><!--end row-->

    </div><!--end col-->
</div><!--end row-->

<div class="row justify-content-center mt-5">
    <div class="col-lg-4">
        <div class="text-center mb-4 pb-2">
            <h4 class="fw-semibold fs-22">Simple Pricing Plan</h4>
            <p class="text-muted mb-4 fs-15">Simple pricing. No hidden fees. Advanced features for you business.</p>

        </div>
    </div><!--end col-->
</div><!--end row-->

<div class="row">
    @for ( data of SimplePlan; track $index) {
    <div class="col-lg-6">
        <div class="card pricing-box text-center" [ngClass]=" { 'ribbon-box ribbon-fill': data.ribbon === true}">
            <div class="ribbon ribbon-primary" [ngClass]=" { 'd-none': data.ribbon !== true}">New</div>
            <div class="row g-0">
                <div class="col-lg-6">
                    <div class="card-body h-100">
                        <div>
                            <h5 class="mb-1">{{data.type}}</h5>
                            <p class="text-muted">{{data.purpose}}</p>
                        </div>

                        <div class="py-4">
                            <h2><sup><small>$</small></sup>{{data.rate}} <span class="fs-13 text-muted"> /Per
                                    month</span></h2>
                        </div>

                        <div class="text-center plan-btn mt-2">
                            <a href="javascript:void(0);" class="btn btn-success w-sm waves-effect waves-light">Sign
                                up</a>
                        </div>
                    </div>
                </div><!--end col-->
                <div class="col-lg-6">
                    <div class="card-body border-start mt-4 mt-lg-0">
                        <div class="card-header bg-light">
                            <h5 class="fs-15 mb-0">Plan Features:</h5>
                        </div>
                        <div class="card-body pb-0">
                            <ul class="list-unstyled vstack gap-3 mb-0">
                                <li>Users: <span class="text-success fw-semibold">{{data.users}}</span></li>
                                <li>Storage: <span class="text-success fw-semibold">{{data.storage}} GB</span></li>
                                <li>Domain: <span class="text-success fw-semibold">{{data.domain}}</span></li>
                                <li>Support: <span class="text-success fw-semibold">{{data.support}}</span></li>
                            </ul>
                        </div>
                    </div>
                </div><!--end col-->
            </div><!--end row-->
        </div>
    </div><!--end row-->
    }
</div><!--end row-->