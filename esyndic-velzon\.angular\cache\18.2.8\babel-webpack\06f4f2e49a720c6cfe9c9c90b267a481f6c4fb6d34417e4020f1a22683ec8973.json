{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Model from './Model.js';\nimport * as componentUtil from '../util/component.js';\nimport { enableClassManagement, parseClassType, isExtendedClass, mountExtend } from '../util/clazz.js';\nimport { makeInner, queryReferringComponents } from '../util/model.js';\nimport * as layout from '../util/layout.js';\nvar inner = makeInner();\nvar ComponentModel = /** @class */function (_super) {\n  __extends(ComponentModel, _super);\n  function ComponentModel(option, parentModel, ecModel) {\n    var _this = _super.call(this, option, parentModel, ecModel) || this;\n    _this.uid = componentUtil.getUID('ec_cpt_model');\n    return _this;\n  }\n  ComponentModel.prototype.init = function (option, parentModel, ecModel) {\n    this.mergeDefaultAndTheme(option, ecModel);\n  };\n  ComponentModel.prototype.mergeDefaultAndTheme = function (option, ecModel) {\n    var layoutMode = layout.fetchLayoutMode(this);\n    var inputPositionParams = layoutMode ? layout.getLayoutParams(option) : {};\n    var themeModel = ecModel.getTheme();\n    zrUtil.merge(option, themeModel.get(this.mainType));\n    zrUtil.merge(option, this.getDefaultOption());\n    if (layoutMode) {\n      layout.mergeLayoutParam(option, inputPositionParams, layoutMode);\n    }\n  };\n  ComponentModel.prototype.mergeOption = function (option, ecModel) {\n    zrUtil.merge(this.option, option, true);\n    var layoutMode = layout.fetchLayoutMode(this);\n    if (layoutMode) {\n      layout.mergeLayoutParam(this.option, option, layoutMode);\n    }\n  };\n  /**\n   * Called immediately after `init` or `mergeOption` of this instance called.\n   */\n  ComponentModel.prototype.optionUpdated = function (newCptOption, isInit) {};\n  /**\n   * [How to declare defaultOption]:\n   *\n   * (A) If using class declaration in typescript (since echarts 5):\n   * ```ts\n   * import {ComponentOption} from '../model/option.js';\n   * export interface XxxOption extends ComponentOption {\n   *     aaa: number\n   * }\n   * export class XxxModel extends Component {\n   *     static type = 'xxx';\n   *     static defaultOption: XxxOption = {\n   *         aaa: 123\n   *     }\n   * }\n   * Component.registerClass(XxxModel);\n   * ```\n   * ```ts\n   * import {inheritDefaultOption} from '../util/component.js';\n   * import {XxxModel, XxxOption} from './XxxModel.js';\n   * export interface XxxSubOption extends XxxOption {\n   *     bbb: number\n   * }\n   * class XxxSubModel extends XxxModel {\n   *     static defaultOption: XxxSubOption = inheritDefaultOption(XxxModel.defaultOption, {\n   *         bbb: 456\n   *     })\n   *     fn() {\n   *         let opt = this.getDefaultOption();\n   *         // opt is {aaa: 123, bbb: 456}\n   *     }\n   * }\n   * ```\n   *\n   * (B) If using class extend (previous approach in echarts 3 & 4):\n   * ```js\n   * let XxxComponent = Component.extend({\n   *     defaultOption: {\n   *         xx: 123\n   *     }\n   * })\n   * ```\n   * ```js\n   * let XxxSubComponent = XxxComponent.extend({\n   *     defaultOption: {\n   *         yy: 456\n   *     },\n   *     fn: function () {\n   *         let opt = this.getDefaultOption();\n   *         // opt is {xx: 123, yy: 456}\n   *     }\n   * })\n   * ```\n   */\n  ComponentModel.prototype.getDefaultOption = function () {\n    var ctor = this.constructor;\n    // If using class declaration, it is different to travel super class\n    // in legacy env and auto merge defaultOption. So if using class\n    // declaration, defaultOption should be merged manually.\n    if (!isExtendedClass(ctor)) {\n      // When using ts class, defaultOption must be declared as static.\n      return ctor.defaultOption;\n    }\n    // FIXME: remove this approach?\n    var fields = inner(this);\n    if (!fields.defaultOption) {\n      var optList = [];\n      var clz = ctor;\n      while (clz) {\n        var opt = clz.prototype.defaultOption;\n        opt && optList.push(opt);\n        clz = clz.superClass;\n      }\n      var defaultOption = {};\n      for (var i = optList.length - 1; i >= 0; i--) {\n        defaultOption = zrUtil.merge(defaultOption, optList[i], true);\n      }\n      fields.defaultOption = defaultOption;\n    }\n    return fields.defaultOption;\n  };\n  /**\n   * Notice: always force to input param `useDefault` in case that forget to consider it.\n   * The same behavior as `modelUtil.parseFinder`.\n   *\n   * @param useDefault In many cases like series refer axis and axis refer grid,\n   *        If axis index / axis id not specified, use the first target as default.\n   *        In other cases like dataZoom refer axis, if not specified, measn no refer.\n   */\n  ComponentModel.prototype.getReferringComponents = function (mainType, opt) {\n    var indexKey = mainType + 'Index';\n    var idKey = mainType + 'Id';\n    return queryReferringComponents(this.ecModel, mainType, {\n      index: this.get(indexKey, true),\n      id: this.get(idKey, true)\n    }, opt);\n  };\n  ComponentModel.prototype.getBoxLayoutParams = function () {\n    // Consider itself having box layout configs.\n    var boxLayoutModel = this;\n    return {\n      left: boxLayoutModel.get('left'),\n      top: boxLayoutModel.get('top'),\n      right: boxLayoutModel.get('right'),\n      bottom: boxLayoutModel.get('bottom'),\n      width: boxLayoutModel.get('width'),\n      height: boxLayoutModel.get('height')\n    };\n  };\n  /**\n   * Get key for zlevel.\n   * If developers don't configure zlevel. We will assign zlevel to series based on the key.\n   * For example, lines with trail effect and progressive series will in an individual zlevel.\n   */\n  ComponentModel.prototype.getZLevelKey = function () {\n    return '';\n  };\n  ComponentModel.prototype.setZLevel = function (zlevel) {\n    this.option.zlevel = zlevel;\n  };\n  ComponentModel.protoInitialize = function () {\n    var proto = ComponentModel.prototype;\n    proto.type = 'component';\n    proto.id = '';\n    proto.name = '';\n    proto.mainType = '';\n    proto.subType = '';\n    proto.componentIndex = 0;\n  }();\n  return ComponentModel;\n}(Model);\nmountExtend(ComponentModel, Model);\nenableClassManagement(ComponentModel);\ncomponentUtil.enableSubTypeDefaulter(ComponentModel);\ncomponentUtil.enableTopologicalTravel(ComponentModel, getDependencies);\nfunction getDependencies(componentType) {\n  var deps = [];\n  zrUtil.each(ComponentModel.getClassesByMainType(componentType), function (clz) {\n    deps = deps.concat(clz.dependencies || clz.prototype.dependencies || []);\n  });\n  // Ensure main type.\n  deps = zrUtil.map(deps, function (type) {\n    return parseClassType(type).main;\n  });\n  // Hack dataset for convenience.\n  if (componentType !== 'dataset' && zrUtil.indexOf(deps, 'dataset') <= 0) {\n    deps.unshift('dataset');\n  }\n  return deps;\n}\nexport default ComponentModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "Model", "componentUtil", "enableClassManagement", "parseClassType", "isExtendedClass", "mountExtend", "makeInner", "queryReferringComponents", "layout", "inner", "ComponentModel", "_super", "option", "parentModel", "ecModel", "_this", "call", "uid", "getUID", "prototype", "init", "mergeDefaultAndTheme", "layoutMode", "fetchLayoutMode", "inputPositionParams", "getLayoutParams", "themeModel", "getTheme", "merge", "get", "mainType", "getDefaultOption", "mergeLayoutParam", "mergeOption", "optionUpdated", "newCptOption", "isInit", "ctor", "constructor", "defaultOption", "fields", "optList", "clz", "opt", "push", "superClass", "i", "length", "getReferringComponents", "indexKey", "id<PERSON><PERSON>", "index", "id", "getBoxLayoutParams", "boxLayoutModel", "left", "top", "right", "bottom", "width", "height", "getZLevelKey", "setZLevel", "zlevel", "protoInitialize", "proto", "type", "name", "subType", "componentIndex", "enableSubTypeDefaulter", "enableTopologicalTravel", "getDependencies", "componentType", "deps", "each", "getClassesByMainType", "concat", "dependencies", "map", "main", "indexOf", "unshift"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/model/Component.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Model from './Model.js';\nimport * as componentUtil from '../util/component.js';\nimport { enableClassManagement, parseClassType, isExtendedClass, mountExtend } from '../util/clazz.js';\nimport { makeInner, queryReferringComponents } from '../util/model.js';\nimport * as layout from '../util/layout.js';\nvar inner = makeInner();\nvar ComponentModel = /** @class */function (_super) {\n  __extends(ComponentModel, _super);\n  function ComponentModel(option, parentModel, ecModel) {\n    var _this = _super.call(this, option, parentModel, ecModel) || this;\n    _this.uid = componentUtil.getUID('ec_cpt_model');\n    return _this;\n  }\n  ComponentModel.prototype.init = function (option, parentModel, ecModel) {\n    this.mergeDefaultAndTheme(option, ecModel);\n  };\n  ComponentModel.prototype.mergeDefaultAndTheme = function (option, ecModel) {\n    var layoutMode = layout.fetchLayoutMode(this);\n    var inputPositionParams = layoutMode ? layout.getLayoutParams(option) : {};\n    var themeModel = ecModel.getTheme();\n    zrUtil.merge(option, themeModel.get(this.mainType));\n    zrUtil.merge(option, this.getDefaultOption());\n    if (layoutMode) {\n      layout.mergeLayoutParam(option, inputPositionParams, layoutMode);\n    }\n  };\n  ComponentModel.prototype.mergeOption = function (option, ecModel) {\n    zrUtil.merge(this.option, option, true);\n    var layoutMode = layout.fetchLayoutMode(this);\n    if (layoutMode) {\n      layout.mergeLayoutParam(this.option, option, layoutMode);\n    }\n  };\n  /**\n   * Called immediately after `init` or `mergeOption` of this instance called.\n   */\n  ComponentModel.prototype.optionUpdated = function (newCptOption, isInit) {};\n  /**\n   * [How to declare defaultOption]:\n   *\n   * (A) If using class declaration in typescript (since echarts 5):\n   * ```ts\n   * import {ComponentOption} from '../model/option.js';\n   * export interface XxxOption extends ComponentOption {\n   *     aaa: number\n   * }\n   * export class XxxModel extends Component {\n   *     static type = 'xxx';\n   *     static defaultOption: XxxOption = {\n   *         aaa: 123\n   *     }\n   * }\n   * Component.registerClass(XxxModel);\n   * ```\n   * ```ts\n   * import {inheritDefaultOption} from '../util/component.js';\n   * import {XxxModel, XxxOption} from './XxxModel.js';\n   * export interface XxxSubOption extends XxxOption {\n   *     bbb: number\n   * }\n   * class XxxSubModel extends XxxModel {\n   *     static defaultOption: XxxSubOption = inheritDefaultOption(XxxModel.defaultOption, {\n   *         bbb: 456\n   *     })\n   *     fn() {\n   *         let opt = this.getDefaultOption();\n   *         // opt is {aaa: 123, bbb: 456}\n   *     }\n   * }\n   * ```\n   *\n   * (B) If using class extend (previous approach in echarts 3 & 4):\n   * ```js\n   * let XxxComponent = Component.extend({\n   *     defaultOption: {\n   *         xx: 123\n   *     }\n   * })\n   * ```\n   * ```js\n   * let XxxSubComponent = XxxComponent.extend({\n   *     defaultOption: {\n   *         yy: 456\n   *     },\n   *     fn: function () {\n   *         let opt = this.getDefaultOption();\n   *         // opt is {xx: 123, yy: 456}\n   *     }\n   * })\n   * ```\n   */\n  ComponentModel.prototype.getDefaultOption = function () {\n    var ctor = this.constructor;\n    // If using class declaration, it is different to travel super class\n    // in legacy env and auto merge defaultOption. So if using class\n    // declaration, defaultOption should be merged manually.\n    if (!isExtendedClass(ctor)) {\n      // When using ts class, defaultOption must be declared as static.\n      return ctor.defaultOption;\n    }\n    // FIXME: remove this approach?\n    var fields = inner(this);\n    if (!fields.defaultOption) {\n      var optList = [];\n      var clz = ctor;\n      while (clz) {\n        var opt = clz.prototype.defaultOption;\n        opt && optList.push(opt);\n        clz = clz.superClass;\n      }\n      var defaultOption = {};\n      for (var i = optList.length - 1; i >= 0; i--) {\n        defaultOption = zrUtil.merge(defaultOption, optList[i], true);\n      }\n      fields.defaultOption = defaultOption;\n    }\n    return fields.defaultOption;\n  };\n  /**\n   * Notice: always force to input param `useDefault` in case that forget to consider it.\n   * The same behavior as `modelUtil.parseFinder`.\n   *\n   * @param useDefault In many cases like series refer axis and axis refer grid,\n   *        If axis index / axis id not specified, use the first target as default.\n   *        In other cases like dataZoom refer axis, if not specified, measn no refer.\n   */\n  ComponentModel.prototype.getReferringComponents = function (mainType, opt) {\n    var indexKey = mainType + 'Index';\n    var idKey = mainType + 'Id';\n    return queryReferringComponents(this.ecModel, mainType, {\n      index: this.get(indexKey, true),\n      id: this.get(idKey, true)\n    }, opt);\n  };\n  ComponentModel.prototype.getBoxLayoutParams = function () {\n    // Consider itself having box layout configs.\n    var boxLayoutModel = this;\n    return {\n      left: boxLayoutModel.get('left'),\n      top: boxLayoutModel.get('top'),\n      right: boxLayoutModel.get('right'),\n      bottom: boxLayoutModel.get('bottom'),\n      width: boxLayoutModel.get('width'),\n      height: boxLayoutModel.get('height')\n    };\n  };\n  /**\n   * Get key for zlevel.\n   * If developers don't configure zlevel. We will assign zlevel to series based on the key.\n   * For example, lines with trail effect and progressive series will in an individual zlevel.\n   */\n  ComponentModel.prototype.getZLevelKey = function () {\n    return '';\n  };\n  ComponentModel.prototype.setZLevel = function (zlevel) {\n    this.option.zlevel = zlevel;\n  };\n  ComponentModel.protoInitialize = function () {\n    var proto = ComponentModel.prototype;\n    proto.type = 'component';\n    proto.id = '';\n    proto.name = '';\n    proto.mainType = '';\n    proto.subType = '';\n    proto.componentIndex = 0;\n  }();\n  return ComponentModel;\n}(Model);\nmountExtend(ComponentModel, Model);\nenableClassManagement(ComponentModel);\ncomponentUtil.enableSubTypeDefaulter(ComponentModel);\ncomponentUtil.enableTopologicalTravel(ComponentModel, getDependencies);\nfunction getDependencies(componentType) {\n  var deps = [];\n  zrUtil.each(ComponentModel.getClassesByMainType(componentType), function (clz) {\n    deps = deps.concat(clz.dependencies || clz.prototype.dependencies || []);\n  });\n  // Ensure main type.\n  deps = zrUtil.map(deps, function (type) {\n    return parseClassType(type).main;\n  });\n  // Hack dataset for convenience.\n  if (componentType !== 'dataset' && zrUtil.indexOf(deps, 'dataset') <= 0) {\n    deps.unshift('dataset');\n  }\n  return deps;\n}\nexport default ComponentModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAO,KAAKC,aAAa,MAAM,sBAAsB;AACrD,SAASC,qBAAqB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AACtG,SAASC,SAAS,EAAEC,wBAAwB,QAAQ,kBAAkB;AACtE,OAAO,KAAKC,MAAM,MAAM,mBAAmB;AAC3C,IAAIC,KAAK,GAAGH,SAAS,CAAC,CAAC;AACvB,IAAII,cAAc,GAAG,aAAa,UAAUC,MAAM,EAAE;EAClDb,SAAS,CAACY,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAACE,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IACpD,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,EAAEJ,MAAM,EAAEC,WAAW,EAAEC,OAAO,CAAC,IAAI,IAAI;IACnEC,KAAK,CAACE,GAAG,GAAGhB,aAAa,CAACiB,MAAM,CAAC,cAAc,CAAC;IAChD,OAAOH,KAAK;EACd;EACAL,cAAc,CAACS,SAAS,CAACC,IAAI,GAAG,UAAUR,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IACtE,IAAI,CAACO,oBAAoB,CAACT,MAAM,EAAEE,OAAO,CAAC;EAC5C,CAAC;EACDJ,cAAc,CAACS,SAAS,CAACE,oBAAoB,GAAG,UAAUT,MAAM,EAAEE,OAAO,EAAE;IACzE,IAAIQ,UAAU,GAAGd,MAAM,CAACe,eAAe,CAAC,IAAI,CAAC;IAC7C,IAAIC,mBAAmB,GAAGF,UAAU,GAAGd,MAAM,CAACiB,eAAe,CAACb,MAAM,CAAC,GAAG,CAAC,CAAC;IAC1E,IAAIc,UAAU,GAAGZ,OAAO,CAACa,QAAQ,CAAC,CAAC;IACnC5B,MAAM,CAAC6B,KAAK,CAAChB,MAAM,EAAEc,UAAU,CAACG,GAAG,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC;IACnD/B,MAAM,CAAC6B,KAAK,CAAChB,MAAM,EAAE,IAAI,CAACmB,gBAAgB,CAAC,CAAC,CAAC;IAC7C,IAAIT,UAAU,EAAE;MACdd,MAAM,CAACwB,gBAAgB,CAACpB,MAAM,EAAEY,mBAAmB,EAAEF,UAAU,CAAC;IAClE;EACF,CAAC;EACDZ,cAAc,CAACS,SAAS,CAACc,WAAW,GAAG,UAAUrB,MAAM,EAAEE,OAAO,EAAE;IAChEf,MAAM,CAAC6B,KAAK,CAAC,IAAI,CAAChB,MAAM,EAAEA,MAAM,EAAE,IAAI,CAAC;IACvC,IAAIU,UAAU,GAAGd,MAAM,CAACe,eAAe,CAAC,IAAI,CAAC;IAC7C,IAAID,UAAU,EAAE;MACdd,MAAM,CAACwB,gBAAgB,CAAC,IAAI,CAACpB,MAAM,EAAEA,MAAM,EAAEU,UAAU,CAAC;IAC1D;EACF,CAAC;EACD;AACF;AACA;EACEZ,cAAc,CAACS,SAAS,CAACe,aAAa,GAAG,UAAUC,YAAY,EAAEC,MAAM,EAAE,CAAC,CAAC;EAC3E;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE1B,cAAc,CAACS,SAAS,CAACY,gBAAgB,GAAG,YAAY;IACtD,IAAIM,IAAI,GAAG,IAAI,CAACC,WAAW;IAC3B;IACA;IACA;IACA,IAAI,CAAClC,eAAe,CAACiC,IAAI,CAAC,EAAE;MAC1B;MACA,OAAOA,IAAI,CAACE,aAAa;IAC3B;IACA;IACA,IAAIC,MAAM,GAAG/B,KAAK,CAAC,IAAI,CAAC;IACxB,IAAI,CAAC+B,MAAM,CAACD,aAAa,EAAE;MACzB,IAAIE,OAAO,GAAG,EAAE;MAChB,IAAIC,GAAG,GAAGL,IAAI;MACd,OAAOK,GAAG,EAAE;QACV,IAAIC,GAAG,GAAGD,GAAG,CAACvB,SAAS,CAACoB,aAAa;QACrCI,GAAG,IAAIF,OAAO,CAACG,IAAI,CAACD,GAAG,CAAC;QACxBD,GAAG,GAAGA,GAAG,CAACG,UAAU;MACtB;MACA,IAAIN,aAAa,GAAG,CAAC,CAAC;MACtB,KAAK,IAAIO,CAAC,GAAGL,OAAO,CAACM,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5CP,aAAa,GAAGxC,MAAM,CAAC6B,KAAK,CAACW,aAAa,EAAEE,OAAO,CAACK,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/D;MACAN,MAAM,CAACD,aAAa,GAAGA,aAAa;IACtC;IACA,OAAOC,MAAM,CAACD,aAAa;EAC7B,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE7B,cAAc,CAACS,SAAS,CAAC6B,sBAAsB,GAAG,UAAUlB,QAAQ,EAAEa,GAAG,EAAE;IACzE,IAAIM,QAAQ,GAAGnB,QAAQ,GAAG,OAAO;IACjC,IAAIoB,KAAK,GAAGpB,QAAQ,GAAG,IAAI;IAC3B,OAAOvB,wBAAwB,CAAC,IAAI,CAACO,OAAO,EAAEgB,QAAQ,EAAE;MACtDqB,KAAK,EAAE,IAAI,CAACtB,GAAG,CAACoB,QAAQ,EAAE,IAAI,CAAC;MAC/BG,EAAE,EAAE,IAAI,CAACvB,GAAG,CAACqB,KAAK,EAAE,IAAI;IAC1B,CAAC,EAAEP,GAAG,CAAC;EACT,CAAC;EACDjC,cAAc,CAACS,SAAS,CAACkC,kBAAkB,GAAG,YAAY;IACxD;IACA,IAAIC,cAAc,GAAG,IAAI;IACzB,OAAO;MACLC,IAAI,EAAED,cAAc,CAACzB,GAAG,CAAC,MAAM,CAAC;MAChC2B,GAAG,EAAEF,cAAc,CAACzB,GAAG,CAAC,KAAK,CAAC;MAC9B4B,KAAK,EAAEH,cAAc,CAACzB,GAAG,CAAC,OAAO,CAAC;MAClC6B,MAAM,EAAEJ,cAAc,CAACzB,GAAG,CAAC,QAAQ,CAAC;MACpC8B,KAAK,EAAEL,cAAc,CAACzB,GAAG,CAAC,OAAO,CAAC;MAClC+B,MAAM,EAAEN,cAAc,CAACzB,GAAG,CAAC,QAAQ;IACrC,CAAC;EACH,CAAC;EACD;AACF;AACA;AACA;AACA;EACEnB,cAAc,CAACS,SAAS,CAAC0C,YAAY,GAAG,YAAY;IAClD,OAAO,EAAE;EACX,CAAC;EACDnD,cAAc,CAACS,SAAS,CAAC2C,SAAS,GAAG,UAAUC,MAAM,EAAE;IACrD,IAAI,CAACnD,MAAM,CAACmD,MAAM,GAAGA,MAAM;EAC7B,CAAC;EACDrD,cAAc,CAACsD,eAAe,GAAG,YAAY;IAC3C,IAAIC,KAAK,GAAGvD,cAAc,CAACS,SAAS;IACpC8C,KAAK,CAACC,IAAI,GAAG,WAAW;IACxBD,KAAK,CAACb,EAAE,GAAG,EAAE;IACba,KAAK,CAACE,IAAI,GAAG,EAAE;IACfF,KAAK,CAACnC,QAAQ,GAAG,EAAE;IACnBmC,KAAK,CAACG,OAAO,GAAG,EAAE;IAClBH,KAAK,CAACI,cAAc,GAAG,CAAC;EAC1B,CAAC,CAAC,CAAC;EACH,OAAO3D,cAAc;AACvB,CAAC,CAACV,KAAK,CAAC;AACRK,WAAW,CAACK,cAAc,EAAEV,KAAK,CAAC;AAClCE,qBAAqB,CAACQ,cAAc,CAAC;AACrCT,aAAa,CAACqE,sBAAsB,CAAC5D,cAAc,CAAC;AACpDT,aAAa,CAACsE,uBAAuB,CAAC7D,cAAc,EAAE8D,eAAe,CAAC;AACtE,SAASA,eAAeA,CAACC,aAAa,EAAE;EACtC,IAAIC,IAAI,GAAG,EAAE;EACb3E,MAAM,CAAC4E,IAAI,CAACjE,cAAc,CAACkE,oBAAoB,CAACH,aAAa,CAAC,EAAE,UAAU/B,GAAG,EAAE;IAC7EgC,IAAI,GAAGA,IAAI,CAACG,MAAM,CAACnC,GAAG,CAACoC,YAAY,IAAIpC,GAAG,CAACvB,SAAS,CAAC2D,YAAY,IAAI,EAAE,CAAC;EAC1E,CAAC,CAAC;EACF;EACAJ,IAAI,GAAG3E,MAAM,CAACgF,GAAG,CAACL,IAAI,EAAE,UAAUR,IAAI,EAAE;IACtC,OAAO/D,cAAc,CAAC+D,IAAI,CAAC,CAACc,IAAI;EAClC,CAAC,CAAC;EACF;EACA,IAAIP,aAAa,KAAK,SAAS,IAAI1E,MAAM,CAACkF,OAAO,CAACP,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE;IACvEA,IAAI,CAACQ,OAAO,CAAC,SAAS,CAAC;EACzB;EACA,OAAOR,IAAI;AACb;AACA,eAAehE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}