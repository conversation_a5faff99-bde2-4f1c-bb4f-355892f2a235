{"ast": null, "code": "import _asyncToGenerator from \"C:/e-syndic/esyndic-velzon/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, NgModule, signal, InjectionToken, TemplateRef, ViewContainerRef, effect, Input, Directive, NgZone, computed, PLATFORM_ID, provideAppInitializer, EnvironmentInjector, runInInjectionContext, makeEnvironmentProviders } from '@angular/core';\nimport { HttpHeaders, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { Subject, from, combineLatest, of, fromEvent, mergeMap as mergeMap$1 } from 'rxjs';\nimport { map, mergeMap, debounceTime, takeUntil } from 'rxjs/operators';\nimport Keycloak from 'keycloak-js';\nimport { CommonModule, isPlatformBrowser } from '@angular/common';\n\n/**\n * @license\n * Copyright <PERSON><PERSON><PERSON> and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Keycloak event types, as described at the keycloak-js documentation:\n * https://www.keycloak.org/docs/latest/securing_apps/index.html#callback-events\n *\n * @deprecated Keycloak Event based on the KeycloakService is deprecated and\n * will be removed in future versions.\n * Use the new `KEYCLOAK_EVENT_SIGNAL` injection token to listen for the keycloak\n * events.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/blob/main/docs/migration-guides/v19.md\n */\nvar KeycloakEventTypeLegacy;\n(function (KeycloakEventTypeLegacy) {\n  /**\n   * Called if there was an error during authentication.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthError\"] = 0] = \"OnAuthError\";\n  /**\n   * Called if the user is logged out\n   * (will only be called if the session status iframe is enabled, or in Cordova mode).\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthLogout\"] = 1] = \"OnAuthLogout\";\n  /**\n   * Called if there was an error while trying to refresh the token.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthRefreshError\"] = 2] = \"OnAuthRefreshError\";\n  /**\n   * Called when the token is refreshed.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthRefreshSuccess\"] = 3] = \"OnAuthRefreshSuccess\";\n  /**\n   * Called when a user is successfully authenticated.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthSuccess\"] = 4] = \"OnAuthSuccess\";\n  /**\n   * Called when the adapter is initialized.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnReady\"] = 5] = \"OnReady\";\n  /**\n   * Called when the access token is expired. If a refresh token is available the token\n   * can be refreshed with updateToken, or in cases where it is not (that is, with implicit flow)\n   * you can redirect to login screen to obtain a new access token.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnTokenExpired\"] = 6] = \"OnTokenExpired\";\n  /**\n   * Called when a AIA has been requested by the application.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnActionUpdate\"] = 7] = \"OnActionUpdate\";\n})(KeycloakEventTypeLegacy || (KeycloakEventTypeLegacy = {}));\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * A simple guard implementation out of the box. This class should be inherited and\n * implemented by the application. The only method that should be implemented is #isAccessAllowed.\n * The reason for this is that the authorization flow is usually not unique, so in this way you will\n * have more freedom to customize your authorization flow.\n *\n * @deprecated Class based guards are deprecated in Keycloak Angular and will be removed in future versions.\n * Use the new `createAuthGuard` function to create a Guard for your application.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/blob/main/docs/migration-guides/v19.md\n */\nclass KeycloakAuthGuard {\n  constructor(router, keycloakAngular) {\n    this.router = router;\n    this.keycloakAngular = keycloakAngular;\n  }\n  /**\n   * CanActivate checks if the user is logged in and get the full list of roles (REALM + CLIENT)\n   * of the logged user. This values are set to authenticated and roles params.\n   *\n   * @param route\n   * @param state\n   */\n  canActivate(route, state) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.authenticated = yield _this.keycloakAngular.isLoggedIn();\n        _this.roles = yield _this.keycloakAngular.getUserRoles(true);\n        return yield _this.isAccessAllowed(route, state);\n      } catch (error) {\n        throw new Error('An error happened during access validation. Details:' + error);\n      }\n    })();\n  }\n}\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Service to expose existent methods from the Keycloak JS adapter, adding new\n * functionalities to improve the use of keycloak in Angular v > 4.3 applications.\n *\n * This class should be injected in the application bootstrap, so the same instance will be used\n * along the web application.\n *\n * @deprecated This service is deprecated and will be removed in future versions.\n * Use the new `provideKeycloak` function to load Keycloak in an Angular application.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/blob/main/docs/migration-guides/v19.md\n */\nclass KeycloakService {\n  constructor() {\n    /**\n     * Observer for the keycloak events\n     */\n    this._keycloakEvents$ = new Subject();\n  }\n  /**\n   * Binds the keycloak-js events to the keycloakEvents Subject\n   * which is a good way to monitor for changes, if needed.\n   *\n   * The keycloakEvents returns the keycloak-js event type and any\n   * argument if the source function provides any.\n   */\n  bindsKeycloakEvents() {\n    this._instance.onAuthError = errorData => {\n      this._keycloakEvents$.next({\n        args: errorData,\n        type: KeycloakEventTypeLegacy.OnAuthError\n      });\n    };\n    this._instance.onAuthLogout = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventTypeLegacy.OnAuthLogout\n      });\n    };\n    this._instance.onAuthRefreshSuccess = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventTypeLegacy.OnAuthRefreshSuccess\n      });\n    };\n    this._instance.onAuthRefreshError = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventTypeLegacy.OnAuthRefreshError\n      });\n    };\n    this._instance.onAuthSuccess = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventTypeLegacy.OnAuthSuccess\n      });\n    };\n    this._instance.onTokenExpired = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventTypeLegacy.OnTokenExpired\n      });\n    };\n    this._instance.onActionUpdate = state => {\n      this._keycloakEvents$.next({\n        args: state,\n        type: KeycloakEventTypeLegacy.OnActionUpdate\n      });\n    };\n    this._instance.onReady = authenticated => {\n      this._keycloakEvents$.next({\n        args: authenticated,\n        type: KeycloakEventTypeLegacy.OnReady\n      });\n    };\n  }\n  /**\n   * Loads all bearerExcludedUrl content in a uniform type: ExcludedUrl,\n   * so it becomes easier to handle.\n   *\n   * @param bearerExcludedUrls array of strings or ExcludedUrl that includes\n   * the url and HttpMethod.\n   */\n  loadExcludedUrls(bearerExcludedUrls) {\n    const excludedUrls = [];\n    for (const item of bearerExcludedUrls) {\n      let excludedUrl;\n      if (typeof item === 'string') {\n        excludedUrl = {\n          urlPattern: new RegExp(item, 'i'),\n          httpMethods: []\n        };\n      } else {\n        excludedUrl = {\n          urlPattern: new RegExp(item.url, 'i'),\n          httpMethods: item.httpMethods\n        };\n      }\n      excludedUrls.push(excludedUrl);\n    }\n    return excludedUrls;\n  }\n  /**\n   * Handles the class values initialization.\n   *\n   * @param options\n   */\n  initServiceValues({\n    enableBearerInterceptor = true,\n    loadUserProfileAtStartUp = false,\n    bearerExcludedUrls = [],\n    authorizationHeaderName = 'Authorization',\n    bearerPrefix = 'Bearer',\n    initOptions,\n    updateMinValidity = 20,\n    shouldAddToken = () => true,\n    shouldUpdateToken = () => true\n  }) {\n    this._enableBearerInterceptor = enableBearerInterceptor;\n    this._loadUserProfileAtStartUp = loadUserProfileAtStartUp;\n    this._authorizationHeaderName = authorizationHeaderName;\n    this._bearerPrefix = bearerPrefix.trim().concat(' ');\n    this._excludedUrls = this.loadExcludedUrls(bearerExcludedUrls);\n    this._silentRefresh = initOptions ? initOptions.flow === 'implicit' : false;\n    this._updateMinValidity = updateMinValidity;\n    this.shouldAddToken = shouldAddToken;\n    this.shouldUpdateToken = shouldUpdateToken;\n  }\n  /**\n   * Keycloak initialization. It should be called to initialize the adapter.\n   * Options is an object with 2 main parameters: config and initOptions. The first one\n   * will be used to create the Keycloak instance. The second one are options to initialize the\n   * keycloak instance.\n   *\n   * @param options\n   * Config: may be a string representing the keycloak URI or an object with the\n   * following content:\n   * - url: Keycloak json URL\n   * - realm: realm name\n   * - clientId: client id\n   *\n   * initOptions:\n   * Options to initialize the Keycloak adapter, matches the options as provided by Keycloak itself.\n   *\n   * enableBearerInterceptor:\n   * Flag to indicate if the bearer will added to the authorization header.\n   *\n   * loadUserProfileInStartUp:\n   * Indicates that the user profile should be loaded at the keycloak initialization,\n   * just after the login.\n   *\n   * bearerExcludedUrls:\n   * String Array to exclude the urls that should not have the Authorization Header automatically\n   * added.\n   *\n   * authorizationHeaderName:\n   * This value will be used as the Authorization Http Header name.\n   *\n   * bearerPrefix:\n   * This value will be included in the Authorization Http Header param.\n   *\n   * tokenUpdateExcludedHeaders:\n   * Array of Http Header key/value maps that should not trigger the token to be updated.\n   *\n   * updateMinValidity:\n   * This value determines if the token will be refreshed based on its expiration time.\n   *\n   * @returns\n   * A Promise with a boolean indicating if the initialization was successful.\n   */\n  init(options = {}) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.initServiceValues(options);\n      const {\n        config,\n        initOptions\n      } = options;\n      _this2._instance = new Keycloak(config);\n      _this2.bindsKeycloakEvents();\n      const authenticated = yield _this2._instance.init(initOptions);\n      if (authenticated && _this2._loadUserProfileAtStartUp) {\n        yield _this2.loadUserProfile();\n      }\n      return authenticated;\n    })();\n  }\n  /**\n   * Redirects to login form on (options is an optional object with redirectUri and/or\n   * prompt fields).\n   *\n   * @param options\n   * Object, where:\n   *  - redirectUri: Specifies the uri to redirect to after login.\n   *  - prompt:By default the login screen is displayed if the user is not logged-in to Keycloak.\n   * To only authenticate to the application if the user is already logged-in and not display the\n   * login page if the user is not logged-in, set this option to none. To always require\n   * re-authentication and ignore SSO, set this option to login .\n   *  - maxAge: Used just if user is already authenticated. Specifies maximum time since the\n   * authentication of user happened. If user is already authenticated for longer time than\n   * maxAge, the SSO is ignored and he will need to re-authenticate again.\n   *  - loginHint: Used to pre-fill the username/email field on the login form.\n   *  - action: If value is 'register' then user is redirected to registration page, otherwise to\n   * login page.\n   *  - locale: Specifies the desired locale for the UI.\n   * @returns\n   * A void Promise if the login is successful and after the user profile loading.\n   */\n  login(options = {}) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      yield _this3._instance.login(options);\n      if (_this3._loadUserProfileAtStartUp) {\n        yield _this3.loadUserProfile();\n      }\n    })();\n  }\n  /**\n   * Redirects to logout.\n   *\n   * @param redirectUri\n   * Specifies the uri to redirect to after logout.\n   * @returns\n   * A void Promise if the logout was successful, cleaning also the userProfile.\n   */\n  logout(redirectUri) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const options = {\n        redirectUri\n      };\n      yield _this4._instance.logout(options);\n      _this4._userProfile = undefined;\n    })();\n  }\n  /**\n   * Redirects to registration form. Shortcut for login with option\n   * action = 'register'. Options are same as for the login method but 'action' is set to\n   * 'register'.\n   *\n   * @param options\n   * login options\n   * @returns\n   * A void Promise if the register flow was successful.\n   */\n  register(options = {\n    action: 'register'\n  }) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      yield _this5._instance.register(options);\n    })();\n  }\n  /**\n   * Check if the user has access to the specified role. It will look for roles in\n   * realm and the given resource, but will not check if the user is logged in for better performance.\n   *\n   * @param role\n   * role name\n   * @param resource\n   * resource name. If not specified, `clientId` is used\n   * @returns\n   * A boolean meaning if the user has the specified Role.\n   */\n  isUserInRole(role, resource) {\n    let hasRole;\n    hasRole = this._instance.hasResourceRole(role, resource);\n    if (!hasRole) {\n      hasRole = this._instance.hasRealmRole(role);\n    }\n    return hasRole;\n  }\n  /**\n   * Return the roles of the logged user. The realmRoles parameter, with default value\n   * true, will return the resource roles and realm roles associated with the logged user. If set to false\n   * it will only return the resource roles. The resource parameter, if specified, will return only resource roles\n   * associated with the given resource.\n   *\n   * @param realmRoles\n   * Set to false to exclude realm roles (only client roles)\n   * @param resource\n   * resource name If not specified, returns roles from all resources\n   * @returns\n   * Array of Roles associated with the logged user.\n   */\n  getUserRoles(realmRoles = true, resource) {\n    let roles = [];\n    if (this._instance.resourceAccess) {\n      Object.keys(this._instance.resourceAccess).forEach(key => {\n        if (resource && resource !== key) {\n          return;\n        }\n        const resourceAccess = this._instance.resourceAccess[key];\n        const clientRoles = resourceAccess['roles'] || [];\n        roles = roles.concat(clientRoles);\n      });\n    }\n    if (realmRoles && this._instance.realmAccess) {\n      const realmRoles = this._instance.realmAccess['roles'] || [];\n      roles.push(...realmRoles);\n    }\n    return roles;\n  }\n  /**\n   * Check if user is logged in.\n   *\n   * @returns\n   * A boolean that indicates if the user is logged in.\n   */\n  isLoggedIn() {\n    if (!this._instance) {\n      return false;\n    }\n    return this._instance.authenticated;\n  }\n  /**\n   * Returns true if the token has less than minValidity seconds left before\n   * it expires.\n   *\n   * @param minValidity\n   * Seconds left. (minValidity) is optional. Default value is 0.\n   * @returns\n   * Boolean indicating if the token is expired.\n   */\n  isTokenExpired(minValidity = 0) {\n    return this._instance.isTokenExpired(minValidity);\n  }\n  /**\n   * If the token expires within _updateMinValidity seconds the token is refreshed. If the\n   * session status iframe is enabled, the session status is also checked.\n   * Returns a promise telling if the token was refreshed or not. If the session is not active\n   * anymore, the promise is rejected.\n   *\n   * @param minValidity\n   * Seconds left. (minValidity is optional, if not specified updateMinValidity - default 20 is used)\n   * @returns\n   * Promise with a boolean indicating if the token was succesfully updated.\n   */\n  updateToken(minValidity = this._updateMinValidity) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      // TODO: this is a workaround until the silent refresh (issue #43)\n      // is not implemented, avoiding the redirect loop.\n      if (_this6._silentRefresh) {\n        if (_this6.isTokenExpired()) {\n          throw new Error('Failed to refresh the token, or the session is expired');\n        }\n        return true;\n      }\n      if (!_this6._instance) {\n        throw new Error('Keycloak Angular library is not initialized.');\n      }\n      try {\n        return yield _this6._instance.updateToken(minValidity);\n      } catch (error) {\n        return false;\n      }\n    })();\n  }\n  /**\n   * Loads the user profile.\n   * Returns promise to set functions to be invoked if the profile was loaded\n   * successfully, or if the profile could not be loaded.\n   *\n   * @param forceReload\n   * If true will force the loadUserProfile even if its already loaded.\n   * @returns\n   * A promise with the KeycloakProfile data loaded.\n   */\n  loadUserProfile(forceReload = false) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (_this7._userProfile && !forceReload) {\n        return _this7._userProfile;\n      }\n      if (!_this7._instance.authenticated) {\n        throw new Error('The user profile was not loaded as the user is not logged in.');\n      }\n      return _this7._userProfile = yield _this7._instance.loadUserProfile();\n    })();\n  }\n  /**\n   * Returns the authenticated token.\n   */\n  getToken() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      return _this8._instance.token;\n    })();\n  }\n  /**\n   * Returns the logged username.\n   *\n   * @returns\n   * The logged username.\n   */\n  getUsername() {\n    if (!this._userProfile) {\n      throw new Error('User not logged in or user profile was not loaded.');\n    }\n    return this._userProfile.username;\n  }\n  /**\n   * Clear authentication state, including tokens. This can be useful if application\n   * has detected the session was expired, for example if updating token fails.\n   * Invoking this results in onAuthLogout callback listener being invoked.\n   */\n  clearToken() {\n    this._instance.clearToken();\n  }\n  /**\n   * Adds a valid token in header. The key & value format is:\n   * Authorization Bearer <token>.\n   * If the headers param is undefined it will create the Angular headers object.\n   *\n   * @param headers\n   * Updated header with Authorization and Keycloak token.\n   * @returns\n   * An observable with with the HTTP Authorization header and the current token.\n   */\n  addTokenToHeader(headers = new HttpHeaders()) {\n    return from(this.getToken()).pipe(map(token => token ? headers.set(this._authorizationHeaderName, this._bearerPrefix + token) : headers));\n  }\n  /**\n   * Returns the original Keycloak instance, if you need any customization that\n   * this Angular service does not support yet. Use with caution.\n   *\n   * @returns\n   * The KeycloakInstance from keycloak-js.\n   */\n  getKeycloakInstance() {\n    return this._instance;\n  }\n  /**\n   * @deprecated\n   * Returns the excluded URLs that should not be considered by\n   * the http interceptor which automatically adds the authorization header in the Http Request.\n   *\n   * @returns\n   * The excluded urls that must not be intercepted by the KeycloakBearerInterceptor.\n   */\n  get excludedUrls() {\n    return this._excludedUrls;\n  }\n  /**\n   * Flag to indicate if the bearer will be added to the authorization header.\n   *\n   * @returns\n   * Returns if the bearer interceptor was set to be disabled.\n   */\n  get enableBearerInterceptor() {\n    return this._enableBearerInterceptor;\n  }\n  /**\n   * Keycloak subject to monitor the events triggered by keycloak-js.\n   * The following events as available (as described at keycloak docs -\n   * https://www.keycloak.org/docs/latest/securing_apps/index.html#callback-events):\n   * - OnAuthError\n   * - OnAuthLogout\n   * - OnAuthRefreshError\n   * - OnAuthRefreshSuccess\n   * - OnAuthSuccess\n   * - OnReady\n   * - OnTokenExpire\n   * In each occurrence of any of these, this subject will return the event type,\n   * described at {@link KeycloakEventTypeLegacy} enum and the function args from the keycloak-js\n   * if provided any.\n   *\n   * @returns\n   * A subject with the {@link KeycloakEventLegacy} which describes the event type and attaches the\n   * function args.\n   */\n  get keycloakEvents$() {\n    return this._keycloakEvents$;\n  }\n  static {\n    this.ɵfac = function KeycloakService_Factory(t) {\n      return new (t || KeycloakService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: KeycloakService,\n      factory: KeycloakService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeycloakService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * This interceptor includes the bearer by default in all HttpClient requests.\n *\n * If you need to exclude some URLs from adding the bearer, please, take a look\n * at the {@link KeycloakOptions} bearerExcludedUrls property.\n *\n * @deprecated KeycloakBearerInterceptor is deprecated and will be removed in future versions.\n * Use the new functional interceptor such as `includeBearerTokenInterceptor`.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/blob/main/docs/migration-guides/v19.md\n */\nclass KeycloakBearerInterceptor {\n  constructor() {\n    this.keycloak = inject(KeycloakService);\n  }\n  /**\n   * Calls to update the keycloak token if the request should update the token.\n   *\n   * @param req http request from @angular http module.\n   * @returns\n   * A promise boolean for the token update or noop result.\n   */\n  conditionallyUpdateToken(req) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      if (_this9.keycloak.shouldUpdateToken(req)) {\n        return yield _this9.keycloak.updateToken();\n      }\n      return true;\n    })();\n  }\n  /**\n   * @deprecated\n   * Checks if the url is excluded from having the Bearer Authorization\n   * header added.\n   *\n   * @param req http request from @angular http module.\n   * @param excludedUrlRegex contains the url pattern and the http methods,\n   * excluded from adding the bearer at the Http Request.\n   */\n  isUrlExcluded({\n    method,\n    url\n  }, {\n    urlPattern,\n    httpMethods\n  }) {\n    const httpTest = httpMethods.length === 0 || httpMethods.join().indexOf(method.toUpperCase()) > -1;\n    const urlTest = urlPattern.test(url);\n    return httpTest && urlTest;\n  }\n  /**\n   * Intercept implementation that checks if the request url matches the excludedUrls.\n   * If not, adds the Authorization header to the request if the user is logged in.\n   *\n   * @param req\n   * @param next\n   */\n  intercept(req, next) {\n    const {\n      enableBearerInterceptor,\n      excludedUrls\n    } = this.keycloak;\n    if (!enableBearerInterceptor) {\n      return next.handle(req);\n    }\n    const shallPass = !this.keycloak.shouldAddToken(req) || excludedUrls.findIndex(item => this.isUrlExcluded(req, item)) > -1;\n    if (shallPass) {\n      return next.handle(req);\n    }\n    return combineLatest([from(this.conditionallyUpdateToken(req)), of(this.keycloak.isLoggedIn())]).pipe(mergeMap(([_, isLoggedIn]) => isLoggedIn ? this.handleRequestWithTokenHeader(req, next) : next.handle(req)));\n  }\n  /**\n   * Adds the token of the current user to the Authorization header\n   *\n   * @param req\n   * @param next\n   */\n  handleRequestWithTokenHeader(req, next) {\n    return this.keycloak.addTokenToHeader(req.headers).pipe(mergeMap(headersWithBearer => {\n      const kcReq = req.clone({\n        headers: headersWithBearer\n      });\n      return next.handle(kcReq);\n    }));\n  }\n  static {\n    this.ɵfac = function KeycloakBearerInterceptor_Factory(t) {\n      return new (t || KeycloakBearerInterceptor)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: KeycloakBearerInterceptor,\n      factory: KeycloakBearerInterceptor.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeycloakBearerInterceptor, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * @deprecated NgModules are deprecated in Keycloak Angular and will be removed in future versions.\n * Use the new `provideKeycloak` function to load Keycloak in an Angular application.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/blob/main/docs/migration-guides/v19.md\n */\nclass CoreModule {\n  static {\n    this.ɵfac = function CoreModule_Factory(t) {\n      return new (t || CoreModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CoreModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [KeycloakService, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: KeycloakBearerInterceptor,\n        multi: true\n      }],\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CoreModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      providers: [KeycloakService, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: KeycloakBearerInterceptor,\n        multi: true\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * @deprecated NgModules are deprecated in Keycloak Angular and will be removed in future versions.\n * Use the new `provideKeycloak` function to load Keycloak in an Angular application.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/blob/main/docs/migration-guides/v19.md\n */\nclass KeycloakAngularModule {\n  static {\n    this.ɵfac = function KeycloakAngularModule_Factory(t) {\n      return new (t || KeycloakAngularModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: KeycloakAngularModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CoreModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeycloakAngularModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CoreModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n// This legacy implementation will be removed in Keycloak Angular v20\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Keycloak event types, as described at the keycloak-js documentation:\n * https://www.keycloak.org/docs/latest/securing_apps/index.html#callback-events\n */\nvar KeycloakEventType;\n(function (KeycloakEventType) {\n  /**\n   * Keycloak Angular is not initialized yet. This is the initial state applied to the Keycloak Event Signal.\n   * Note: This event is only emitted in Keycloak Angular, it is not part of the keycloak-js.\n   */\n  KeycloakEventType[\"KeycloakAngularNotInitialized\"] = \"KeycloakAngularNotInitialized\";\n  /**\n   * Keycloak Angular is in the process of initializing the providers and Keycloak Instance.\n   * Note: This event is only emitted in Keycloak Angular, it is not part of the keycloak-js.\n   */\n  KeycloakEventType[\"KeycloakAngularInit\"] = \"KeycloakAngularInit\";\n  /**\n   * Triggered if there is an error during authentication.\n   */\n  KeycloakEventType[\"AuthError\"] = \"AuthError\";\n  /**\n   * Triggered when the user logs out. This event will only be triggered\n   * if the session status iframe is enabled or in Cordova mode.\n   */\n  KeycloakEventType[\"AuthLogout\"] = \"AuthLogout\";\n  /**\n   * Triggered if an error occurs while attempting to refresh the token.\n   */\n  KeycloakEventType[\"AuthRefreshError\"] = \"AuthRefreshError\";\n  /**\n   * Triggered when the token is successfully refreshed.\n   */\n  KeycloakEventType[\"AuthRefreshSuccess\"] = \"AuthRefreshSuccess\";\n  /**\n   * Triggered when a user is successfully authenticated.\n   */\n  KeycloakEventType[\"AuthSuccess\"] = \"AuthSuccess\";\n  /**\n   * Triggered when the Keycloak adapter has completed initialization.\n   */\n  KeycloakEventType[\"Ready\"] = \"Ready\";\n  /**\n   * Triggered when the access token expires. Depending on the flow, you may\n   * need to use `updateToken` to refresh the token or redirect the user\n   * to the login screen.\n   */\n  KeycloakEventType[\"TokenExpired\"] = \"TokenExpired\";\n  /**\n   * Triggered when an authentication action is requested by the application.\n   */\n  KeycloakEventType[\"ActionUpdate\"] = \"ActionUpdate\";\n})(KeycloakEventType || (KeycloakEventType = {}));\n/**\n * Helper function to typecast unknown arguments into a specific Keycloak event type.\n *\n * @template T - The expected argument type.\n * @param args - The arguments to be cast.\n * @returns The arguments typed as `T`.\n */\nconst typeEventArgs = args => args;\n/**\n * Creates a signal to manage Keycloak events, initializing the signal with\n * appropriate default values or values from a given Keycloak instance.\n *\n * @param keycloak - An instance of the Keycloak client.\n * @returns A `Signal` that tracks the current Keycloak event state.\n */\nconst createKeycloakSignal = keycloak => {\n  const keycloakSignal = signal({\n    type: KeycloakEventType.KeycloakAngularInit\n  });\n  if (!keycloak) {\n    keycloakSignal.set({\n      type: KeycloakEventType.KeycloakAngularNotInitialized\n    });\n    return keycloakSignal;\n  }\n  keycloak.onReady = authenticated => {\n    keycloakSignal.set({\n      type: KeycloakEventType.Ready,\n      args: authenticated\n    });\n  };\n  keycloak.onAuthError = errorData => {\n    keycloakSignal.set({\n      type: KeycloakEventType.AuthError,\n      args: errorData\n    });\n  };\n  keycloak.onAuthLogout = () => {\n    keycloakSignal.set({\n      type: KeycloakEventType.AuthLogout\n    });\n  };\n  keycloak.onActionUpdate = (status, action) => {\n    keycloakSignal.set({\n      type: KeycloakEventType.ActionUpdate,\n      args: {\n        status,\n        action\n      }\n    });\n  };\n  keycloak.onAuthRefreshError = () => {\n    keycloakSignal.set({\n      type: KeycloakEventType.AuthRefreshError\n    });\n  };\n  keycloak.onAuthRefreshSuccess = () => {\n    keycloakSignal.set({\n      type: KeycloakEventType.AuthRefreshSuccess\n    });\n  };\n  keycloak.onAuthSuccess = () => {\n    keycloakSignal.set({\n      type: KeycloakEventType.AuthSuccess\n    });\n  };\n  keycloak.onTokenExpired = () => {\n    keycloakSignal.set({\n      type: KeycloakEventType.TokenExpired\n    });\n  };\n  return keycloakSignal;\n};\n/**\n * Injection token for the Keycloak events signal, used for dependency injection.\n */\nconst KEYCLOAK_EVENT_SIGNAL = new InjectionToken('Keycloak Events Signal');\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Structural directive to conditionally display elements based on Keycloak user roles.\n *\n * This directive checks if the authenticated user has at least one of the specified roles.\n * Roles can be validated against a specific **resource (client ID)** or the **realm**.\n *\n * ### Features:\n * - Supports role checking in both **resources (client-level roles)** and the **realm**.\n * - Accepts an array of roles to match.\n * - Optional configuration to check realm-level roles.\n *\n * ### Inputs:\n * - `kaHasRoles` (Required): Array of roles to validate.\n * - `resource` (Optional): The client ID or resource name to validate resource-level roles.\n * - `checkRealm` (Optional): A boolean flag to enable realm role validation (default is `false`).\n *\n * ### Requirements:\n * - A Keycloak instance must be injected via Angular's dependency injection.\n * - The user must be authenticated in Keycloak.\n *\n * @example\n * #### Example 1: Check for Global Realm Roles\n * Show the content only if the user has the `admin` or `editor` role in the realm.\n * ```html\n * <div *kaHasRoles=\"['admin', 'editor']; checkRealm:true\">\n *   <p>This content is visible only to users with 'admin' or 'editor' realm roles.</p>\n * </div>\n * ```\n *\n * @example\n * #### Example 2: Check for Resource Roles\n * Show the content only if the user has the `read` or `write` role for a specific resource (`my-client`).\n * ```html\n * <div *kaHasRoles=\"['read', 'write']; resource:'my-client'\">\n *   <p>This content is visible only to users with 'read' or 'write' roles for 'my-client'.</p>\n * </div>\n * ```\n *\n * @example\n * #### Example 3: Check for Both Resource and Realm Roles\n * Show the content if the user has the roles in either the realm or a resource.\n * ```html\n * <div *kaHasRoles=\"['admin', 'write']; resource:'my-client' checkRealm:true\">\n *   <p>This content is visible to users with 'admin' in the realm or 'write' in 'my-client'.</p>\n * </div>\n * ```\n *\n * @example\n * #### Example 4: Fallback Content When Roles Do Not Match\n * Use an `<ng-template>` to display fallback content if the user lacks the required roles.\n * ```html\n * <div *kaHasRoles=\"['admin']; resource:'my-client'\">\n *   <p>Welcome, Admin!</p>\n * </div>\n * <ng-template #noAccess>\n *   <p>Access Denied</p>\n * </ng-template>\n * ```\n */\nclass HasRolesDirective {\n  constructor() {\n    this.templateRef = inject(TemplateRef);\n    this.viewContainer = inject(ViewContainerRef);\n    this.keycloak = inject(Keycloak);\n    /**\n     * List of roles to validate against the resource or realm.\n     */\n    this.roles = [];\n    /**\n     * Flag to enable realm-level role validation.\n     */\n    this.checkRealm = false;\n    this.viewContainer.clear();\n    const keycloakSignal = inject(KEYCLOAK_EVENT_SIGNAL);\n    effect(() => {\n      const keycloakEvent = keycloakSignal();\n      if (keycloakEvent.type !== KeycloakEventType.Ready) {\n        return;\n      }\n      const authenticated = typeEventArgs(keycloakEvent.args);\n      if (authenticated) {\n        this.render();\n      }\n    });\n  }\n  render() {\n    const hasAccess = this.checkUserRoles();\n    if (hasAccess) {\n      this.viewContainer.createEmbeddedView(this.templateRef);\n    } else {\n      this.viewContainer.clear();\n    }\n  }\n  /**\n   * Checks if the user has at least one of the specified roles in the resource or realm.\n   * @returns True if the user has access, false otherwise.\n   */\n  checkUserRoles() {\n    const hasResourceRole = this.roles.some(role => this.keycloak.hasResourceRole(role, this.resource));\n    const hasRealmRole = this.checkRealm ? this.roles.some(role => this.keycloak.hasRealmRole(role)) : false;\n    return hasResourceRole || hasRealmRole;\n  }\n  static {\n    this.ɵfac = function HasRolesDirective_Factory(t) {\n      return new (t || HasRolesDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: HasRolesDirective,\n      selectors: [[\"\", \"kaHasRoles\", \"\"]],\n      inputs: {\n        roles: [0, \"kaHasRoles\", \"roles\"],\n        resource: [0, \"kaHasRolesResource\", \"resource\"],\n        checkRealm: [0, \"kaHasRolesCheckRealm\", \"checkRealm\"]\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HasRolesDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[kaHasRoles]'\n    }]\n  }], () => [], {\n    roles: [{\n      type: Input,\n      args: ['kaHasRoles']\n    }],\n    resource: [{\n      type: Input,\n      args: ['kaHasRolesResource']\n    }],\n    checkRealm: [{\n      type: Input,\n      args: ['kaHasRolesCheckRealm']\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Service to monitor user activity in an Angular application.\n * Tracks user interactions (e.g., mouse movement, touch, key presses, clicks, and scrolls)\n * and updates the last activity timestamp. Consumers can check for user inactivity\n * based on a configurable timeout.\n *\n * The service is supposed to be used in the client context and for safety, it checks during the startup\n * if it is a browser context.\n */\nclass UserActivityService {\n  constructor() {\n    this.ngZone = inject(NgZone);\n    /**\n     * Signal to store the timestamp of the last user activity.\n     * The timestamp is represented as the number of milliseconds since epoch.\n     */\n    this.lastActivity = signal(Date.now());\n    /**\n     * Subject to signal the destruction of the service.\n     * Used to clean up RxJS subscriptions.\n     */\n    this.destroy$ = new Subject();\n    /**\n     * Computed signal to expose the last user activity as a read-only signal.\n     */\n    this.lastActivitySignal = computed(() => this.lastActivity());\n  }\n  /**\n   * Starts monitoring user activity events (`mousemove`, `touchstart`, `keydown`, `click`, `scroll`)\n   * and updates the last activity timestamp using RxJS with debounce.\n   * The events are processed outside Angular zone for performance optimization.\n   */\n  startMonitoring() {\n    const isBrowser = isPlatformBrowser(inject(PLATFORM_ID));\n    if (!isBrowser) {\n      return;\n    }\n    this.ngZone.runOutsideAngular(() => {\n      const events = ['mousemove', 'touchstart', 'keydown', 'click', 'scroll'];\n      events.forEach(event => {\n        fromEvent(window, event).pipe(debounceTime(300), takeUntil(this.destroy$)).subscribe(() => this.updateLastActivity());\n      });\n    });\n  }\n  /**\n   * Updates the last activity timestamp to the current time.\n   * This method runs inside Angular's zone to ensure reactivity with Angular signals.\n   */\n  updateLastActivity() {\n    this.ngZone.run(() => {\n      this.lastActivity.set(Date.now());\n    });\n  }\n  /**\n   * Retrieves the timestamp of the last recorded user activity.\n   * @returns {number} The last activity timestamp in milliseconds since epoch.\n   */\n  get lastActivityTime() {\n    return this.lastActivity();\n  }\n  /**\n   * Determines whether the user interacted with the application, meaning it is activily using the application, based on\n   * the specified duration.\n   * @param timeout - The inactivity timeout in milliseconds.\n   * @returns {boolean} `true` if the user is inactive, otherwise `false`.\n   */\n  isActive(timeout) {\n    return Date.now() - this.lastActivityTime < timeout;\n  }\n  /**\n   * Cleans up RxJS subscriptions and resources when the service is destroyed.\n   * This method is automatically called by Angular when the service is removed.\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function UserActivityService_Factory(t) {\n      return new (t || UserActivityService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: UserActivityService,\n      factory: UserActivityService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UserActivityService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Service to automatically manage the Keycloak token refresh process\n * based on user activity and token expiration events. This service\n * integrates with Keycloak for session management and interacts with\n * user activity monitoring to determine the appropriate action when\n * the token expires.\n *\n * The service listens to `KeycloakSignal` for token-related events\n * (e.g., `TokenExpired`) and provides configurable options for\n * session timeout and inactivity handling.\n */\nclass AutoRefreshTokenService {\n  constructor() {\n    this.keycloak = inject(Keycloak);\n    this.userActivity = inject(UserActivityService);\n    this.options = this.defaultOptions;\n    this.initialized = false;\n    const keycloakSignal = inject(KEYCLOAK_EVENT_SIGNAL);\n    effect(() => {\n      const keycloakEvent = keycloakSignal();\n      if (keycloakEvent.type === KeycloakEventType.TokenExpired) {\n        this.processTokenExpiredEvent();\n      }\n    });\n  }\n  get defaultOptions() {\n    return {\n      sessionTimeout: 300000,\n      onInactivityTimeout: 'logout'\n    };\n  }\n  executeOnInactivityTimeout() {\n    switch (this.options.onInactivityTimeout) {\n      case 'login':\n        this.keycloak.login().catch(error => console.error('Failed to execute the login call', error));\n        break;\n      case 'logout':\n        this.keycloak.logout().catch(error => console.error('Failed to execute the logout call', error));\n        break;\n      default:\n        break;\n    }\n  }\n  processTokenExpiredEvent() {\n    if (!this.initialized || !this.keycloak.authenticated) {\n      return;\n    }\n    if (this.userActivity.isActive(this.options.sessionTimeout)) {\n      this.keycloak.updateToken().catch(() => this.executeOnInactivityTimeout());\n    } else {\n      this.executeOnInactivityTimeout();\n    }\n  }\n  start(options) {\n    this.options = {\n      ...this.defaultOptions,\n      ...options\n    };\n    this.initialized = true;\n    this.userActivity.startMonitoring();\n  }\n  static {\n    this.ɵfac = function AutoRefreshTokenService_Factory(t) {\n      return new (t || AutoRefreshTokenService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AutoRefreshTokenService,\n      factory: AutoRefreshTokenService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoRefreshTokenService, [{\n    type: Injectable\n  }], () => [], null);\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Enables automatic token refresh and session inactivity handling for a\n * Keycloak-enabled Angular application.\n *\n * This function initializes a service that tracks user interactions, such as\n * mouse movements, touches, key presses, clicks, and scrolls. If user activity\n * is detected, it periodically calls `Keycloak.updateToken` to ensure the bearer\n * token remains valid and does not expire.\n *\n * If the session remains inactive beyond the defined `sessionTimeout`, the\n * specified action (`logout`, `login`, or `none`) will be executed. By default,\n * the service will call `keycloak.logout` upon inactivity timeout.\n *\n * Event tracking uses RxJS observables with a debounce of 300 milliseconds to\n * monitor user interactions. When the Keycloak `OnTokenExpired` event occurs,\n * the service checks the user's last activity timestamp. If the user has been\n * active within the session timeout period, it refreshes the token using `updateToken`.\n *\n *\n * @param options - Configuration options for the auto-refresh token feature.\n *   - `sessionTimeout` (optional): The duration in milliseconds after which\n *     the session is considered inactive. Defaults to `300000` (5 minutes).\n *   - `onInactivityTimeout` (optional): The action to take when session inactivity\n *     exceeds the specified timeout. Defaults to `'logout'`.\n *       - `'login'`: Execute `keycloak.login` function.\n *       - `'logout'`: Logs the user out by calling `keycloak.logout`.\n *       - `'none'`: No action is taken.\n *\n * @returns A `KeycloakFeature` instance that configures and enables the\n * auto-refresh token functionality.\n */\nfunction withAutoRefreshToken(options) {\n  return {\n    configure: () => {\n      const autoRefreshTokenService = inject(AutoRefreshTokenService);\n      autoRefreshTokenService.start(options);\n    }\n  };\n}\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\nconst mapResourceRoles = (resourceAccess = {}) => {\n  return Object.entries(resourceAccess).reduce((roles, [key, value]) => {\n    roles[key] = value.roles;\n    return roles;\n  }, {});\n};\n/**\n * Creates a custom authorization guard for Angular routes, enabling fine-grained access control.\n *\n * This guard invokes the provided `isAccessAllowed` function to determine if access is permitted\n * based on the current route, router state, and user's authentication and roles data.\n *\n * @template T - The type of the guard function (`CanActivateFn` or `CanActivateChildFn`).\n * @param isAccessAllowed - A callback function that evaluates access conditions. The function receives:\n *   - `route`: The current `ActivatedRouteSnapshot` for the route being accessed.\n *   - `state`: The current `RouterStateSnapshot` representing the router's state.\n *   - `authData`: An `AuthGuardData` object containing the user's authentication status, roles, and Keycloak instance.\n * @returns A guard function of type `T` that can be used as a route `canActivate` or `canActivateChild` guard.\n *\n * @example\n * ```ts\n * import { createAuthGuard } from './auth-guard';\n * import { Routes } from '@angular/router';\n *\n * const isUserAllowed = async (route, state, authData) => {\n *   const { authenticated, grantedRoles } = authData;\n *   return authenticated && grantedRoles.realmRoles.includes('admin');\n * };\n *\n * const routes: Routes = [\n *   {\n *     path: 'admin',\n *     canActivate: [createAuthGuard(isUserAllowed)],\n *     component: AdminComponent,\n *   },\n * ];\n * ```\n */\nconst createAuthGuard = isAccessAllowed => {\n  return (next, state) => {\n    const keycloak = inject(Keycloak);\n    const authenticated = keycloak?.authenticated ?? false;\n    const grantedRoles = {\n      resourceRoles: mapResourceRoles(keycloak?.resourceAccess),\n      realmRoles: keycloak?.realmAccess?.roles ?? []\n    };\n    const authData = {\n      authenticated,\n      keycloak,\n      grantedRoles\n    };\n    return isAccessAllowed(next, state, authData);\n  };\n};\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Default value for the authorization header prefix, used to construct the Authorization token.\n */\nconst BEARER_PREFIX = 'Bearer';\n/**\n * Default name of the authorization header.\n */\nconst AUTHORIZATION_HEADER_NAME = 'Authorization';\n/**\n * Generic factory function to create an interceptor condition with default values.\n *\n * This utility allows you to define custom interceptor conditions while ensuring that\n * default values are applied to any missing fields. By using generics, you can enforce\n * strong typing when creating the fields for the interceptor condition, enhancing type safety.\n *\n * @template T - A type that extends `AuthBearerCondition`.\n * @param value - An object of type `T` (extending `AuthBearerCondition`) to be enhanced with default values.\n * @returns A new object of type `T` with default values assigned to any undefined properties.\n */\nconst createInterceptorCondition = value => ({\n  ...value,\n  bearerPrefix: value.bearerPrefix ?? BEARER_PREFIX,\n  authorizationHeaderName: value.authorizationHeaderName ?? AUTHORIZATION_HEADER_NAME,\n  shouldUpdateToken: value.shouldUpdateToken ?? (() => true)\n});\n/**\n * Conditionally updates the Keycloak token based on the provided request and conditions.\n *\n * @param req - The `HttpRequest` object being processed.\n * @param keycloak - The Keycloak instance managing authentication.\n * @param condition - An `AuthBearerCondition` object with the `shouldUpdateToken` function.\n * @returns A `Promise<boolean>` indicating whether the token was successfully updated.\n */\nconst conditionallyUpdateToken = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (req, keycloak, {\n    shouldUpdateToken = _ => true\n  }) {\n    if (shouldUpdateToken(req)) {\n      return yield keycloak.updateToken().catch(() => false);\n    }\n    return true;\n  });\n  return function conditionallyUpdateToken(_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * Adds the Authorization header to an HTTP request and forwards it to the next handler.\n *\n * @param req - The original `HttpRequest` object.\n * @param next - The `HttpHandlerFn` function for forwarding the HTTP request.\n * @param keycloak - The Keycloak instance providing the authentication token.\n * @param condition - An `AuthBearerCondition` object specifying header configuration.\n * @returns An `Observable<HttpEvent<unknown>>` representing the HTTP response.\n */\nconst addAuthorizationHeader = (req, next, keycloak, condition) => {\n  const {\n    bearerPrefix = BEARER_PREFIX,\n    authorizationHeaderName = AUTHORIZATION_HEADER_NAME\n  } = condition;\n  const clonedRequest = req.clone({\n    setHeaders: {\n      [authorizationHeaderName]: `${bearerPrefix} ${keycloak.token}`\n    }\n  });\n  return next(clonedRequest);\n};\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Injection token for configuring the `customBearerTokenInterceptor`.\n *\n * This injection token holds an array of `CustomBearerTokenCondition` objects, which define\n * the conditions under which a Bearer token should be included in the `Authorization` header\n * of outgoing HTTP requests. Each condition provides a `shouldAddToken` function that dynamically\n * determines whether the token should be added based on the request, handler, and Keycloak state.\n */\nconst CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG = new InjectionToken('Include the bearer token as implemented by the provided function');\n/**\n * Custom HTTP Interceptor for dynamically adding a Bearer token to requests based on conditions.\n *\n * This interceptor uses a flexible approach where the decision to include a Bearer token in the\n * `Authorization` HTTP header is determined by a user-provided function (`shouldAddToken`).\n * This enables a dynamic and granular control over when tokens are added to HTTP requests.\n *\n * ### Key Features:\n * 1. **Dynamic Token Inclusion**: Uses a condition function (`shouldAddToken`) to decide dynamically\n *    whether to add the token based on the request, Keycloak state, and other factors.\n * 2. **Token Management**: Optionally refreshes the Keycloak token before adding it to the request.\n * 3. **Controlled Authorization**: Adds the Bearer token only when the condition function allows\n *    and the user is authenticated in Keycloak.\n *\n * ### Configuration:\n * The interceptor relies on `CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG`, an injection token that contains\n * an array of `CustomBearerTokenCondition` objects. Each condition specifies a `shouldAddToken` function\n * that determines whether to add the Bearer token for a given request.\n *\n * ### Workflow:\n * 1. Reads the conditions from the `CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG` injection token.\n * 2. Iterates through the conditions and evaluates the `shouldAddToken` function for the request.\n * 3. If a condition matches:\n *    - Optionally refreshes the Keycloak token if needed.\n *    - Adds the Bearer token to the request's `Authorization` header if the user is authenticated.\n * 4. If no conditions match, the request proceeds unchanged.\n *\n * ### Parameters:\n * @param req - The `HttpRequest` object representing the outgoing HTTP request.\n * @param next - The `HttpHandlerFn` for passing the request to the next handler in the chain.\n *\n * @returns An `Observable<HttpEvent<unknown>>` representing the HTTP response.\n *\n * ### Usage Example:\n * ```typescript\n * // Define a custom condition to include the token\n * const customCondition: CustomBearerTokenCondition = {\n *   shouldAddToken: async (req, next, keycloak) => {\n *     // Add token only for requests to the /api endpoint\n *     return req.url.startsWith('/api') && keycloak.authenticated;\n *   },\n * };\n *\n * // Configure the interceptor with the custom condition\n * export const appConfig: ApplicationConfig = {\n *   providers: [\n *     provideHttpClient(withInterceptors([customBearerTokenInterceptor])),\n *     {\n *       provide: CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG,\n *       useValue: [customCondition],\n *     },\n *   ],\n * };\n * ```\n */\nconst customBearerTokenInterceptor = (req, next) => {\n  const conditions = inject(CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG) ?? [];\n  const keycloak = inject(Keycloak);\n  return from(Promise.all(conditions.map( /*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator(function* (condition) {\n      return yield condition.shouldAddToken(req, next, keycloak);\n    });\n    return function (_x4) {\n      return _ref2.apply(this, arguments);\n    };\n  }()))).pipe(mergeMap$1(evaluatedConditions => {\n    const matchingConditionIndex = evaluatedConditions.findIndex(Boolean);\n    const matchingCondition = conditions[matchingConditionIndex];\n    if (!matchingCondition) {\n      return next(req);\n    }\n    return from(conditionallyUpdateToken(req, keycloak, matchingCondition)).pipe(mergeMap$1(() => keycloak.authenticated ? addAuthorizationHeader(req, next, keycloak, matchingCondition) : next(req)));\n  }));\n};\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Injection token for configuring the `includeBearerTokenInterceptor`, allowing the specification\n * of conditions under which the Bearer token should be included in HTTP request headers.\n *\n * This configuration supports multiple conditions, enabling customization for different URLs.\n * It also provides options to tailor the Bearer prefix and the Authorization header name as needed.\n */\nconst INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG = new InjectionToken('Include the bearer token when explicitly defined int the URL pattern condition');\nconst findMatchingCondition = ({\n  method,\n  url\n}, {\n  urlPattern,\n  httpMethods = []\n}) => {\n  const httpMethodTest = httpMethods.length === 0 || httpMethods.join().indexOf(method.toUpperCase()) > -1;\n  const urlTest = urlPattern.test(url);\n  return httpMethodTest && urlTest;\n};\n/**\n * HTTP Interceptor to include a Bearer token in the Authorization header for specific HTTP requests.\n *\n * This interceptor ensures that a Bearer token is added to outgoing HTTP requests based on explicitly\n * defined conditions. By default, the interceptor does not include the Bearer token unless the request\n * matches the provided configuration (`IncludeBearerTokenCondition`). This approach enhances security\n * by preventing sensitive tokens from being unintentionally sent to unauthorized services.\n *\n * ### Features:\n * 1. **Explicit URL Matching**: The interceptor uses regular expressions to match URLs where the Bearer token should be included.\n * 2. **HTTP Method Filtering**: Optional filtering by HTTP methods (e.g., `GET`, `POST`, `PUT`) to refine the conditions for adding the token.\n * 3. **Token Management**: Ensures the Keycloak token is valid by optionally refreshing it before attaching it to the request.\n * 4. **Controlled Authorization**: Sends the token only for requests where the user is authenticated, and the conditions match.\n *\n * ### Workflow:\n * - Reads conditions from `INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG`, which specifies when the Bearer token should be included.\n * - If a request matches the conditions:\n *   1. The Keycloak token is refreshed if needed.\n *   2. The Bearer token is added to the Authorization header.\n *   3. The modified request is passed to the next handler.\n * - If no conditions match, the request proceeds unchanged.\n *\n * ### Security:\n * By explicitly defining URL patterns and optional HTTP methods, this interceptor prevents the leakage of tokens\n * to unintended endpoints, such as third-party APIs or external services. This is especially critical for applications\n * that interact with both internal and external services.\n *\n * @param req - The `HttpRequest` object representing the outgoing HTTP request.\n * @param next - The `HttpHandlerFn` for passing the request to the next handler in the chain.\n * @returns An `Observable<HttpEvent<unknown>>` representing the asynchronous HTTP response.\n *\n * ### Configuration:\n * The interceptor relies on `INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG`, an injection token that holds\n * an array of `IncludeBearerTokenCondition` objects. Each object defines the conditions for including\n * the Bearer token in the request.\n *\n * #### Example Configuration:\n * ```typescript\n * provideHttpClient(\n *   withInterceptors([includeBearerTokenInterceptor]),\n *   {\n *     provide: INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG,\n *     useValue: [\n *       {\n *         urlPattern: /^https:\\/\\/api\\.internal\\.myapp\\.com\\/.*\\/,\n *         httpMethods: ['GET', 'POST'], // Add the token only for GET and POST methods\n *       },\n *     ],\n *   }\n * );\n * ```\n *\n * ### Example Usage:\n * ```typescript\n * export const appConfig: ApplicationConfig = {\n *   providers: [\n *     provideHttpClient(withInterceptors([includeBearerTokenInterceptor])),\n *     provideZoneChangeDetection({ eventCoalescing: true }),\n *     provideRouter(routes),\n *   ],\n * };\n * ```\n *\n * ### Example Matching Condition:\n * ```typescript\n * {\n *   urlPattern: /^(https:\\/\\/internal\\.mycompany\\.com)(\\/.*)?$/i,\n *   httpMethods: ['GET', 'PUT'], // Optional: Match only specific HTTP methods\n * }\n * ```\n */\nconst includeBearerTokenInterceptor = (req, next) => {\n  const conditions = inject(INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG) ?? [];\n  const matchingCondition = conditions.find(condition => findMatchingCondition(req, condition));\n  if (!matchingCondition) {\n    return next(req);\n  }\n  const keycloak = inject(Keycloak);\n  return from(conditionallyUpdateToken(req, keycloak, matchingCondition)).pipe(mergeMap$1(() => keycloak.authenticated ? addAuthorizationHeader(req, next, keycloak, matchingCondition) : next(req)));\n};\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Provides Keycloak initialization logic for the app initializer phase.\n * Ensures Keycloak is initialized and features are configured.\n *\n * @param keycloak - The Keycloak instance.\n * @param options - ProvideKeycloakOptions for configuration.\n * @returns EnvironmentProviders or an empty array if `initOptions` is not provided.\n */\nconst provideKeycloakInAppInitializer = (keycloak, options) => {\n  const {\n    initOptions,\n    features = []\n  } = options;\n  if (!initOptions) {\n    return [];\n  }\n  return provideAppInitializer( /*#__PURE__*/_asyncToGenerator(function* () {\n    const injector = inject(EnvironmentInjector);\n    runInInjectionContext(injector, () => features.forEach(feature => feature.configure()));\n    yield keycloak.init(initOptions).catch(error => console.error('Keycloak initialization failed', error));\n  }));\n};\n/**\n * Configures and provides Keycloak as a dependency in an Angular application.\n *\n * This function initializes a Keycloak instance with the provided configuration and\n * optional initialization options. It integrates Keycloak into Angular dependency\n * injection system, allowing easy consumption throughout the application. Additionally,\n * it supports custom providers and Keycloak Angular features.\n *\n * If `initOptions` is not provided, the Keycloak instance will not be automatically initialized.\n * In such cases, the application must call `keycloak.init()` explicitly.\n *\n * @param options - Configuration object for Keycloak:\n *   - `config`: The Keycloak configuration, including the server URL, realm, and client ID.\n *   - `initOptions` (Optional): Initialization options for the Keycloak instance.\n *   - `providers` (Optional): Additional Angular providers to include.\n *   - `features` (Optional): Keycloak Angular features to configure during initialization.\n *\n * @returns An `EnvironmentProviders` object integrating Keycloak setup and additional providers.\n *\n * @example\n * ```ts\n * import { provideKeycloak } from './keycloak.providers';\n * import { bootstrapApplication } from '@angular/platform-browser';\n * import { AppComponent } from './app/app.component';\n *\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *     provideKeycloak({\n *       config: {\n *         url: 'https://auth-server.example.com',\n *         realm: 'my-realm',\n *         clientId: 'my-client',\n *       },\n *       initOptions: {\n *         onLoad: 'login-required',\n *       },\n *     }),\n *   ],\n * });\n * ```\n */\nfunction provideKeycloak(options) {\n  const keycloak = new Keycloak(options.config);\n  const providers = options.providers ?? [];\n  const keycloakSignal = createKeycloakSignal(keycloak);\n  return makeEnvironmentProviders([{\n    provide: KEYCLOAK_EVENT_SIGNAL,\n    useValue: keycloakSignal\n  }, {\n    provide: Keycloak,\n    useValue: keycloak\n  }, ...providers, provideKeycloakInAppInitializer(keycloak, options)]);\n}\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoRefreshTokenService, CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG, CoreModule, HasRolesDirective, INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG, KEYCLOAK_EVENT_SIGNAL, KeycloakAngularModule, KeycloakAuthGuard, KeycloakBearerInterceptor, KeycloakEventType, KeycloakEventTypeLegacy, KeycloakService, UserActivityService, addAuthorizationHeader, conditionallyUpdateToken, createAuthGuard, createInterceptorCondition, createKeycloakSignal, customBearerTokenInterceptor, includeBearerTokenInterceptor, provideKeycloak, typeEventArgs, withAutoRefreshToken };", "map": {"version": 3, "names": ["i0", "Injectable", "inject", "NgModule", "signal", "InjectionToken", "TemplateRef", "ViewContainerRef", "effect", "Input", "Directive", "NgZone", "computed", "PLATFORM_ID", "provideAppInitializer", "EnvironmentInjector", "runInInjectionContext", "makeEnvironmentProviders", "HttpHeaders", "HTTP_INTERCEPTORS", "Subject", "from", "combineLatest", "of", "fromEvent", "mergeMap", "mergeMap$1", "map", "debounceTime", "takeUntil", "Keycloak", "CommonModule", "isPlatformBrowser", "KeycloakEventTypeLegacy", "KeycloakAuthGuard", "constructor", "router", "keycloakAngular", "canActivate", "route", "state", "_this", "_asyncToGenerator", "authenticated", "isLoggedIn", "roles", "getUserRoles", "isAccessAllowed", "error", "Error", "KeycloakService", "_keycloakEvents$", "bindsKeycloakEvents", "_instance", "onAuthError", "errorData", "next", "args", "type", "OnAuthError", "onAuthLogout", "OnAuthLogout", "onAuthRefreshSuccess", "OnAuthRefreshSuccess", "onAuthRefreshError", "OnAuthRefreshError", "onAuthSuccess", "OnAuthSuccess", "onTokenExpired", "OnTokenExpired", "onActionUpdate", "OnActionUpdate", "onReady", "OnReady", "loadExcludedUrls", "bearerExcludedUrls", "excludedUrls", "item", "excludedUrl", "urlPattern", "RegExp", "httpMethods", "url", "push", "initServiceValues", "enableBearerInterceptor", "loadUserProfileAtStartUp", "authorizationHeaderName", "bearerPrefix", "initOptions", "updateMinValidity", "shouldAddToken", "shouldUpdateToken", "_enableBearerInterceptor", "_loadUserProfileAtStartUp", "_authorizationHeaderName", "_bearerPrefix", "trim", "concat", "_excludedUrls", "_silentRefresh", "flow", "_updateMinValidity", "init", "options", "_this2", "config", "loadUserProfile", "login", "_this3", "logout", "redirectUri", "_this4", "_userProfile", "undefined", "register", "action", "_this5", "isUserInRole", "role", "resource", "hasRole", "hasResourceRole", "hasRealmRole", "realmRoles", "resourceAccess", "Object", "keys", "for<PERSON>ach", "key", "clientRoles", "realmAccess", "isTokenExpired", "minValidity", "updateToken", "_this6", "forceReload", "_this7", "getToken", "_this8", "token", "getUsername", "username", "clearToken", "addTokenToHeader", "headers", "pipe", "set", "getKeycloakInstance", "keycloakEvents$", "ɵfac", "KeycloakService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "factory", "ngDevMode", "ɵsetClassMetadata", "KeycloakBearerInterceptor", "keycloak", "conditionallyUpdateToken", "req", "_this9", "isUrlExcluded", "method", "httpTest", "length", "join", "indexOf", "toUpperCase", "urlTest", "test", "intercept", "handle", "shallPass", "findIndex", "_", "handleRequestWithTokenHeader", "headers<PERSON><PERSON><PERSON><PERSON><PERSON>", "kcReq", "clone", "KeycloakBearerInterceptor_Factory", "CoreModule", "CoreModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "provide", "useClass", "multi", "imports", "KeycloakAngularModule", "KeycloakAngularModule_Factory", "KeycloakEventType", "typeEventArgs", "createKeycloakSignal", "keycloakSignal", "KeycloakAngularInit", "KeycloakAngularNotInitialized", "Ready", "<PERSON>th<PERSON><PERSON><PERSON>", "AuthLogout", "status", "ActionUpdate", "AuthRefreshError", "AuthRefreshSuccess", "AuthSuccess", "TokenExpired", "KEYCLOAK_EVENT_SIGNAL", "HasRolesDirective", "templateRef", "viewContainer", "checkRealm", "clear", "keycloakEvent", "render", "hasAccess", "checkUserRoles", "createEmbeddedView", "some", "HasRolesDirective_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "standalone", "selector", "UserActivityService", "ngZone", "lastActivity", "Date", "now", "destroy$", "lastActivitySignal", "startMonitoring", "<PERSON><PERSON><PERSON><PERSON>", "runOutsideAngular", "events", "event", "window", "subscribe", "updateLastActivity", "run", "lastActivityTime", "isActive", "timeout", "ngOnDestroy", "complete", "UserActivityService_Factory", "AutoRefreshTokenService", "userActivity", "defaultOptions", "initialized", "processTokenExpiredEvent", "sessionTimeout", "onInactivityTimeout", "executeOnInactivityTimeout", "catch", "console", "start", "AutoRefreshTokenService_Factory", "withAutoRefreshToken", "configure", "autoRefreshTokenService", "mapResourceRoles", "entries", "reduce", "value", "createAuthGuard", "grantedRoles", "resourceRoles", "authData", "BEARER_PREFIX", "AUTHORIZATION_HEADER_NAME", "createInterceptorCondition", "_ref", "_x", "_x2", "_x3", "apply", "arguments", "addAuthorizationHeader", "condition", "clonedRequest", "setHeaders", "CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG", "customBearerTokenInterceptor", "conditions", "Promise", "all", "_ref2", "_x4", "evaluatedConditions", "matchingConditionIndex", "Boolean", "matchingCondition", "INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG", "findMatchingCondition", "httpMethodTest", "includeBearerTokenInterceptor", "find", "provideKeycloakInAppInitializer", "features", "injector", "feature", "provideKeycloak", "useValue"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/keycloak-angular/fesm2022/keycloak-angular.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, inject, NgModule, signal, InjectionToken, TemplateRef, ViewContainerRef, effect, Input, Directive, NgZone, computed, PLATFORM_ID, provideAppInitializer, EnvironmentInjector, runInInjectionContext, makeEnvironmentProviders } from '@angular/core';\nimport { HttpHeaders, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { Subject, from, combineLatest, of, fromEvent, mergeMap as mergeMap$1 } from 'rxjs';\nimport { map, mergeMap, debounceTime, takeUntil } from 'rxjs/operators';\nimport Keycloak from 'keycloak-js';\nimport { CommonModule, isPlatformBrowser } from '@angular/common';\n\n/**\n * @license\n * Copyright Ma<PERSON><PERSON> and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Keycloak event types, as described at the keycloak-js documentation:\n * https://www.keycloak.org/docs/latest/securing_apps/index.html#callback-events\n *\n * @deprecated Keycloak Event based on the KeycloakService is deprecated and\n * will be removed in future versions.\n * Use the new `KEYCLOAK_EVENT_SIGNAL` injection token to listen for the keycloak\n * events.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/blob/main/docs/migration-guides/v19.md\n */\nvar KeycloakEventTypeLegacy;\n(function (KeycloakEventTypeLegacy) {\n    /**\n     * Called if there was an error during authentication.\n     */\n    KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthError\"] = 0] = \"OnAuthError\";\n    /**\n     * Called if the user is logged out\n     * (will only be called if the session status iframe is enabled, or in Cordova mode).\n     */\n    KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthLogout\"] = 1] = \"OnAuthLogout\";\n    /**\n     * Called if there was an error while trying to refresh the token.\n     */\n    KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthRefreshError\"] = 2] = \"OnAuthRefreshError\";\n    /**\n     * Called when the token is refreshed.\n     */\n    KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthRefreshSuccess\"] = 3] = \"OnAuthRefreshSuccess\";\n    /**\n     * Called when a user is successfully authenticated.\n     */\n    KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthSuccess\"] = 4] = \"OnAuthSuccess\";\n    /**\n     * Called when the adapter is initialized.\n     */\n    KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnReady\"] = 5] = \"OnReady\";\n    /**\n     * Called when the access token is expired. If a refresh token is available the token\n     * can be refreshed with updateToken, or in cases where it is not (that is, with implicit flow)\n     * you can redirect to login screen to obtain a new access token.\n     */\n    KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnTokenExpired\"] = 6] = \"OnTokenExpired\";\n    /**\n     * Called when a AIA has been requested by the application.\n     */\n    KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnActionUpdate\"] = 7] = \"OnActionUpdate\";\n})(KeycloakEventTypeLegacy || (KeycloakEventTypeLegacy = {}));\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * A simple guard implementation out of the box. This class should be inherited and\n * implemented by the application. The only method that should be implemented is #isAccessAllowed.\n * The reason for this is that the authorization flow is usually not unique, so in this way you will\n * have more freedom to customize your authorization flow.\n *\n * @deprecated Class based guards are deprecated in Keycloak Angular and will be removed in future versions.\n * Use the new `createAuthGuard` function to create a Guard for your application.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/blob/main/docs/migration-guides/v19.md\n */\nclass KeycloakAuthGuard {\n    constructor(router, keycloakAngular) {\n        this.router = router;\n        this.keycloakAngular = keycloakAngular;\n    }\n    /**\n     * CanActivate checks if the user is logged in and get the full list of roles (REALM + CLIENT)\n     * of the logged user. This values are set to authenticated and roles params.\n     *\n     * @param route\n     * @param state\n     */\n    async canActivate(route, state) {\n        try {\n            this.authenticated = await this.keycloakAngular.isLoggedIn();\n            this.roles = await this.keycloakAngular.getUserRoles(true);\n            return await this.isAccessAllowed(route, state);\n        }\n        catch (error) {\n            throw new Error('An error happened during access validation. Details:' + error);\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Service to expose existent methods from the Keycloak JS adapter, adding new\n * functionalities to improve the use of keycloak in Angular v > 4.3 applications.\n *\n * This class should be injected in the application bootstrap, so the same instance will be used\n * along the web application.\n *\n * @deprecated This service is deprecated and will be removed in future versions.\n * Use the new `provideKeycloak` function to load Keycloak in an Angular application.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/blob/main/docs/migration-guides/v19.md\n */\nclass KeycloakService {\n    constructor() {\n        /**\n         * Observer for the keycloak events\n         */\n        this._keycloakEvents$ = new Subject();\n    }\n    /**\n     * Binds the keycloak-js events to the keycloakEvents Subject\n     * which is a good way to monitor for changes, if needed.\n     *\n     * The keycloakEvents returns the keycloak-js event type and any\n     * argument if the source function provides any.\n     */\n    bindsKeycloakEvents() {\n        this._instance.onAuthError = (errorData) => {\n            this._keycloakEvents$.next({\n                args: errorData,\n                type: KeycloakEventTypeLegacy.OnAuthError\n            });\n        };\n        this._instance.onAuthLogout = () => {\n            this._keycloakEvents$.next({ type: KeycloakEventTypeLegacy.OnAuthLogout });\n        };\n        this._instance.onAuthRefreshSuccess = () => {\n            this._keycloakEvents$.next({\n                type: KeycloakEventTypeLegacy.OnAuthRefreshSuccess\n            });\n        };\n        this._instance.onAuthRefreshError = () => {\n            this._keycloakEvents$.next({\n                type: KeycloakEventTypeLegacy.OnAuthRefreshError\n            });\n        };\n        this._instance.onAuthSuccess = () => {\n            this._keycloakEvents$.next({ type: KeycloakEventTypeLegacy.OnAuthSuccess });\n        };\n        this._instance.onTokenExpired = () => {\n            this._keycloakEvents$.next({\n                type: KeycloakEventTypeLegacy.OnTokenExpired\n            });\n        };\n        this._instance.onActionUpdate = (state) => {\n            this._keycloakEvents$.next({\n                args: state,\n                type: KeycloakEventTypeLegacy.OnActionUpdate\n            });\n        };\n        this._instance.onReady = (authenticated) => {\n            this._keycloakEvents$.next({\n                args: authenticated,\n                type: KeycloakEventTypeLegacy.OnReady\n            });\n        };\n    }\n    /**\n     * Loads all bearerExcludedUrl content in a uniform type: ExcludedUrl,\n     * so it becomes easier to handle.\n     *\n     * @param bearerExcludedUrls array of strings or ExcludedUrl that includes\n     * the url and HttpMethod.\n     */\n    loadExcludedUrls(bearerExcludedUrls) {\n        const excludedUrls = [];\n        for (const item of bearerExcludedUrls) {\n            let excludedUrl;\n            if (typeof item === 'string') {\n                excludedUrl = { urlPattern: new RegExp(item, 'i'), httpMethods: [] };\n            }\n            else {\n                excludedUrl = {\n                    urlPattern: new RegExp(item.url, 'i'),\n                    httpMethods: item.httpMethods\n                };\n            }\n            excludedUrls.push(excludedUrl);\n        }\n        return excludedUrls;\n    }\n    /**\n     * Handles the class values initialization.\n     *\n     * @param options\n     */\n    initServiceValues({ enableBearerInterceptor = true, loadUserProfileAtStartUp = false, bearerExcludedUrls = [], authorizationHeaderName = 'Authorization', bearerPrefix = 'Bearer', initOptions, updateMinValidity = 20, shouldAddToken = () => true, shouldUpdateToken = () => true }) {\n        this._enableBearerInterceptor = enableBearerInterceptor;\n        this._loadUserProfileAtStartUp = loadUserProfileAtStartUp;\n        this._authorizationHeaderName = authorizationHeaderName;\n        this._bearerPrefix = bearerPrefix.trim().concat(' ');\n        this._excludedUrls = this.loadExcludedUrls(bearerExcludedUrls);\n        this._silentRefresh = initOptions ? initOptions.flow === 'implicit' : false;\n        this._updateMinValidity = updateMinValidity;\n        this.shouldAddToken = shouldAddToken;\n        this.shouldUpdateToken = shouldUpdateToken;\n    }\n    /**\n     * Keycloak initialization. It should be called to initialize the adapter.\n     * Options is an object with 2 main parameters: config and initOptions. The first one\n     * will be used to create the Keycloak instance. The second one are options to initialize the\n     * keycloak instance.\n     *\n     * @param options\n     * Config: may be a string representing the keycloak URI or an object with the\n     * following content:\n     * - url: Keycloak json URL\n     * - realm: realm name\n     * - clientId: client id\n     *\n     * initOptions:\n     * Options to initialize the Keycloak adapter, matches the options as provided by Keycloak itself.\n     *\n     * enableBearerInterceptor:\n     * Flag to indicate if the bearer will added to the authorization header.\n     *\n     * loadUserProfileInStartUp:\n     * Indicates that the user profile should be loaded at the keycloak initialization,\n     * just after the login.\n     *\n     * bearerExcludedUrls:\n     * String Array to exclude the urls that should not have the Authorization Header automatically\n     * added.\n     *\n     * authorizationHeaderName:\n     * This value will be used as the Authorization Http Header name.\n     *\n     * bearerPrefix:\n     * This value will be included in the Authorization Http Header param.\n     *\n     * tokenUpdateExcludedHeaders:\n     * Array of Http Header key/value maps that should not trigger the token to be updated.\n     *\n     * updateMinValidity:\n     * This value determines if the token will be refreshed based on its expiration time.\n     *\n     * @returns\n     * A Promise with a boolean indicating if the initialization was successful.\n     */\n    async init(options = {}) {\n        this.initServiceValues(options);\n        const { config, initOptions } = options;\n        this._instance = new Keycloak(config);\n        this.bindsKeycloakEvents();\n        const authenticated = await this._instance.init(initOptions);\n        if (authenticated && this._loadUserProfileAtStartUp) {\n            await this.loadUserProfile();\n        }\n        return authenticated;\n    }\n    /**\n     * Redirects to login form on (options is an optional object with redirectUri and/or\n     * prompt fields).\n     *\n     * @param options\n     * Object, where:\n     *  - redirectUri: Specifies the uri to redirect to after login.\n     *  - prompt:By default the login screen is displayed if the user is not logged-in to Keycloak.\n     * To only authenticate to the application if the user is already logged-in and not display the\n     * login page if the user is not logged-in, set this option to none. To always require\n     * re-authentication and ignore SSO, set this option to login .\n     *  - maxAge: Used just if user is already authenticated. Specifies maximum time since the\n     * authentication of user happened. If user is already authenticated for longer time than\n     * maxAge, the SSO is ignored and he will need to re-authenticate again.\n     *  - loginHint: Used to pre-fill the username/email field on the login form.\n     *  - action: If value is 'register' then user is redirected to registration page, otherwise to\n     * login page.\n     *  - locale: Specifies the desired locale for the UI.\n     * @returns\n     * A void Promise if the login is successful and after the user profile loading.\n     */\n    async login(options = {}) {\n        await this._instance.login(options);\n        if (this._loadUserProfileAtStartUp) {\n            await this.loadUserProfile();\n        }\n    }\n    /**\n     * Redirects to logout.\n     *\n     * @param redirectUri\n     * Specifies the uri to redirect to after logout.\n     * @returns\n     * A void Promise if the logout was successful, cleaning also the userProfile.\n     */\n    async logout(redirectUri) {\n        const options = {\n            redirectUri\n        };\n        await this._instance.logout(options);\n        this._userProfile = undefined;\n    }\n    /**\n     * Redirects to registration form. Shortcut for login with option\n     * action = 'register'. Options are same as for the login method but 'action' is set to\n     * 'register'.\n     *\n     * @param options\n     * login options\n     * @returns\n     * A void Promise if the register flow was successful.\n     */\n    async register(options = { action: 'register' }) {\n        await this._instance.register(options);\n    }\n    /**\n     * Check if the user has access to the specified role. It will look for roles in\n     * realm and the given resource, but will not check if the user is logged in for better performance.\n     *\n     * @param role\n     * role name\n     * @param resource\n     * resource name. If not specified, `clientId` is used\n     * @returns\n     * A boolean meaning if the user has the specified Role.\n     */\n    isUserInRole(role, resource) {\n        let hasRole;\n        hasRole = this._instance.hasResourceRole(role, resource);\n        if (!hasRole) {\n            hasRole = this._instance.hasRealmRole(role);\n        }\n        return hasRole;\n    }\n    /**\n     * Return the roles of the logged user. The realmRoles parameter, with default value\n     * true, will return the resource roles and realm roles associated with the logged user. If set to false\n     * it will only return the resource roles. The resource parameter, if specified, will return only resource roles\n     * associated with the given resource.\n     *\n     * @param realmRoles\n     * Set to false to exclude realm roles (only client roles)\n     * @param resource\n     * resource name If not specified, returns roles from all resources\n     * @returns\n     * Array of Roles associated with the logged user.\n     */\n    getUserRoles(realmRoles = true, resource) {\n        let roles = [];\n        if (this._instance.resourceAccess) {\n            Object.keys(this._instance.resourceAccess).forEach((key) => {\n                if (resource && resource !== key) {\n                    return;\n                }\n                const resourceAccess = this._instance.resourceAccess[key];\n                const clientRoles = resourceAccess['roles'] || [];\n                roles = roles.concat(clientRoles);\n            });\n        }\n        if (realmRoles && this._instance.realmAccess) {\n            const realmRoles = this._instance.realmAccess['roles'] || [];\n            roles.push(...realmRoles);\n        }\n        return roles;\n    }\n    /**\n     * Check if user is logged in.\n     *\n     * @returns\n     * A boolean that indicates if the user is logged in.\n     */\n    isLoggedIn() {\n        if (!this._instance) {\n            return false;\n        }\n        return this._instance.authenticated;\n    }\n    /**\n     * Returns true if the token has less than minValidity seconds left before\n     * it expires.\n     *\n     * @param minValidity\n     * Seconds left. (minValidity) is optional. Default value is 0.\n     * @returns\n     * Boolean indicating if the token is expired.\n     */\n    isTokenExpired(minValidity = 0) {\n        return this._instance.isTokenExpired(minValidity);\n    }\n    /**\n     * If the token expires within _updateMinValidity seconds the token is refreshed. If the\n     * session status iframe is enabled, the session status is also checked.\n     * Returns a promise telling if the token was refreshed or not. If the session is not active\n     * anymore, the promise is rejected.\n     *\n     * @param minValidity\n     * Seconds left. (minValidity is optional, if not specified updateMinValidity - default 20 is used)\n     * @returns\n     * Promise with a boolean indicating if the token was succesfully updated.\n     */\n    async updateToken(minValidity = this._updateMinValidity) {\n        // TODO: this is a workaround until the silent refresh (issue #43)\n        // is not implemented, avoiding the redirect loop.\n        if (this._silentRefresh) {\n            if (this.isTokenExpired()) {\n                throw new Error('Failed to refresh the token, or the session is expired');\n            }\n            return true;\n        }\n        if (!this._instance) {\n            throw new Error('Keycloak Angular library is not initialized.');\n        }\n        try {\n            return await this._instance.updateToken(minValidity);\n        }\n        catch (error) {\n            return false;\n        }\n    }\n    /**\n     * Loads the user profile.\n     * Returns promise to set functions to be invoked if the profile was loaded\n     * successfully, or if the profile could not be loaded.\n     *\n     * @param forceReload\n     * If true will force the loadUserProfile even if its already loaded.\n     * @returns\n     * A promise with the KeycloakProfile data loaded.\n     */\n    async loadUserProfile(forceReload = false) {\n        if (this._userProfile && !forceReload) {\n            return this._userProfile;\n        }\n        if (!this._instance.authenticated) {\n            throw new Error('The user profile was not loaded as the user is not logged in.');\n        }\n        return (this._userProfile = await this._instance.loadUserProfile());\n    }\n    /**\n     * Returns the authenticated token.\n     */\n    async getToken() {\n        return this._instance.token;\n    }\n    /**\n     * Returns the logged username.\n     *\n     * @returns\n     * The logged username.\n     */\n    getUsername() {\n        if (!this._userProfile) {\n            throw new Error('User not logged in or user profile was not loaded.');\n        }\n        return this._userProfile.username;\n    }\n    /**\n     * Clear authentication state, including tokens. This can be useful if application\n     * has detected the session was expired, for example if updating token fails.\n     * Invoking this results in onAuthLogout callback listener being invoked.\n     */\n    clearToken() {\n        this._instance.clearToken();\n    }\n    /**\n     * Adds a valid token in header. The key & value format is:\n     * Authorization Bearer <token>.\n     * If the headers param is undefined it will create the Angular headers object.\n     *\n     * @param headers\n     * Updated header with Authorization and Keycloak token.\n     * @returns\n     * An observable with with the HTTP Authorization header and the current token.\n     */\n    addTokenToHeader(headers = new HttpHeaders()) {\n        return from(this.getToken()).pipe(map((token) => (token ? headers.set(this._authorizationHeaderName, this._bearerPrefix + token) : headers)));\n    }\n    /**\n     * Returns the original Keycloak instance, if you need any customization that\n     * this Angular service does not support yet. Use with caution.\n     *\n     * @returns\n     * The KeycloakInstance from keycloak-js.\n     */\n    getKeycloakInstance() {\n        return this._instance;\n    }\n    /**\n     * @deprecated\n     * Returns the excluded URLs that should not be considered by\n     * the http interceptor which automatically adds the authorization header in the Http Request.\n     *\n     * @returns\n     * The excluded urls that must not be intercepted by the KeycloakBearerInterceptor.\n     */\n    get excludedUrls() {\n        return this._excludedUrls;\n    }\n    /**\n     * Flag to indicate if the bearer will be added to the authorization header.\n     *\n     * @returns\n     * Returns if the bearer interceptor was set to be disabled.\n     */\n    get enableBearerInterceptor() {\n        return this._enableBearerInterceptor;\n    }\n    /**\n     * Keycloak subject to monitor the events triggered by keycloak-js.\n     * The following events as available (as described at keycloak docs -\n     * https://www.keycloak.org/docs/latest/securing_apps/index.html#callback-events):\n     * - OnAuthError\n     * - OnAuthLogout\n     * - OnAuthRefreshError\n     * - OnAuthRefreshSuccess\n     * - OnAuthSuccess\n     * - OnReady\n     * - OnTokenExpire\n     * In each occurrence of any of these, this subject will return the event type,\n     * described at {@link KeycloakEventTypeLegacy} enum and the function args from the keycloak-js\n     * if provided any.\n     *\n     * @returns\n     * A subject with the {@link KeycloakEventLegacy} which describes the event type and attaches the\n     * function args.\n     */\n    get keycloakEvents$() {\n        return this._keycloakEvents$;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: KeycloakService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: KeycloakService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: KeycloakService, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * This interceptor includes the bearer by default in all HttpClient requests.\n *\n * If you need to exclude some URLs from adding the bearer, please, take a look\n * at the {@link KeycloakOptions} bearerExcludedUrls property.\n *\n * @deprecated KeycloakBearerInterceptor is deprecated and will be removed in future versions.\n * Use the new functional interceptor such as `includeBearerTokenInterceptor`.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/blob/main/docs/migration-guides/v19.md\n */\nclass KeycloakBearerInterceptor {\n    constructor() {\n        this.keycloak = inject(KeycloakService);\n    }\n    /**\n     * Calls to update the keycloak token if the request should update the token.\n     *\n     * @param req http request from @angular http module.\n     * @returns\n     * A promise boolean for the token update or noop result.\n     */\n    async conditionallyUpdateToken(req) {\n        if (this.keycloak.shouldUpdateToken(req)) {\n            return await this.keycloak.updateToken();\n        }\n        return true;\n    }\n    /**\n     * @deprecated\n     * Checks if the url is excluded from having the Bearer Authorization\n     * header added.\n     *\n     * @param req http request from @angular http module.\n     * @param excludedUrlRegex contains the url pattern and the http methods,\n     * excluded from adding the bearer at the Http Request.\n     */\n    isUrlExcluded({ method, url }, { urlPattern, httpMethods }) {\n        const httpTest = httpMethods.length === 0 || httpMethods.join().indexOf(method.toUpperCase()) > -1;\n        const urlTest = urlPattern.test(url);\n        return httpTest && urlTest;\n    }\n    /**\n     * Intercept implementation that checks if the request url matches the excludedUrls.\n     * If not, adds the Authorization header to the request if the user is logged in.\n     *\n     * @param req\n     * @param next\n     */\n    intercept(req, next) {\n        const { enableBearerInterceptor, excludedUrls } = this.keycloak;\n        if (!enableBearerInterceptor) {\n            return next.handle(req);\n        }\n        const shallPass = !this.keycloak.shouldAddToken(req) || excludedUrls.findIndex((item) => this.isUrlExcluded(req, item)) > -1;\n        if (shallPass) {\n            return next.handle(req);\n        }\n        return combineLatest([from(this.conditionallyUpdateToken(req)), of(this.keycloak.isLoggedIn())]).pipe(mergeMap(([_, isLoggedIn]) => (isLoggedIn ? this.handleRequestWithTokenHeader(req, next) : next.handle(req))));\n    }\n    /**\n     * Adds the token of the current user to the Authorization header\n     *\n     * @param req\n     * @param next\n     */\n    handleRequestWithTokenHeader(req, next) {\n        return this.keycloak.addTokenToHeader(req.headers).pipe(mergeMap((headersWithBearer) => {\n            const kcReq = req.clone({ headers: headersWithBearer });\n            return next.handle(kcReq);\n        }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: KeycloakBearerInterceptor, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: KeycloakBearerInterceptor }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: KeycloakBearerInterceptor, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * @deprecated NgModules are deprecated in Keycloak Angular and will be removed in future versions.\n * Use the new `provideKeycloak` function to load Keycloak in an Angular application.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/blob/main/docs/migration-guides/v19.md\n */\nclass CoreModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: CoreModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.3\", ngImport: i0, type: CoreModule, imports: [CommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: CoreModule, providers: [\n            KeycloakService,\n            {\n                provide: HTTP_INTERCEPTORS,\n                useClass: KeycloakBearerInterceptor,\n                multi: true\n            }\n        ], imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: CoreModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    providers: [\n                        KeycloakService,\n                        {\n                            provide: HTTP_INTERCEPTORS,\n                            useClass: KeycloakBearerInterceptor,\n                            multi: true\n                        }\n                    ]\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * @deprecated NgModules are deprecated in Keycloak Angular and will be removed in future versions.\n * Use the new `provideKeycloak` function to load Keycloak in an Angular application.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/blob/main/docs/migration-guides/v19.md\n */\nclass KeycloakAngularModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: KeycloakAngularModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.3\", ngImport: i0, type: KeycloakAngularModule, imports: [CoreModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: KeycloakAngularModule, imports: [CoreModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: KeycloakAngularModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CoreModule]\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n// This legacy implementation will be removed in Keycloak Angular v20\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Keycloak event types, as described at the keycloak-js documentation:\n * https://www.keycloak.org/docs/latest/securing_apps/index.html#callback-events\n */\nvar KeycloakEventType;\n(function (KeycloakEventType) {\n    /**\n     * Keycloak Angular is not initialized yet. This is the initial state applied to the Keycloak Event Signal.\n     * Note: This event is only emitted in Keycloak Angular, it is not part of the keycloak-js.\n     */\n    KeycloakEventType[\"KeycloakAngularNotInitialized\"] = \"KeycloakAngularNotInitialized\";\n    /**\n     * Keycloak Angular is in the process of initializing the providers and Keycloak Instance.\n     * Note: This event is only emitted in Keycloak Angular, it is not part of the keycloak-js.\n     */\n    KeycloakEventType[\"KeycloakAngularInit\"] = \"KeycloakAngularInit\";\n    /**\n     * Triggered if there is an error during authentication.\n     */\n    KeycloakEventType[\"AuthError\"] = \"AuthError\";\n    /**\n     * Triggered when the user logs out. This event will only be triggered\n     * if the session status iframe is enabled or in Cordova mode.\n     */\n    KeycloakEventType[\"AuthLogout\"] = \"AuthLogout\";\n    /**\n     * Triggered if an error occurs while attempting to refresh the token.\n     */\n    KeycloakEventType[\"AuthRefreshError\"] = \"AuthRefreshError\";\n    /**\n     * Triggered when the token is successfully refreshed.\n     */\n    KeycloakEventType[\"AuthRefreshSuccess\"] = \"AuthRefreshSuccess\";\n    /**\n     * Triggered when a user is successfully authenticated.\n     */\n    KeycloakEventType[\"AuthSuccess\"] = \"AuthSuccess\";\n    /**\n     * Triggered when the Keycloak adapter has completed initialization.\n     */\n    KeycloakEventType[\"Ready\"] = \"Ready\";\n    /**\n     * Triggered when the access token expires. Depending on the flow, you may\n     * need to use `updateToken` to refresh the token or redirect the user\n     * to the login screen.\n     */\n    KeycloakEventType[\"TokenExpired\"] = \"TokenExpired\";\n    /**\n     * Triggered when an authentication action is requested by the application.\n     */\n    KeycloakEventType[\"ActionUpdate\"] = \"ActionUpdate\";\n})(KeycloakEventType || (KeycloakEventType = {}));\n/**\n * Helper function to typecast unknown arguments into a specific Keycloak event type.\n *\n * @template T - The expected argument type.\n * @param args - The arguments to be cast.\n * @returns The arguments typed as `T`.\n */\nconst typeEventArgs = (args) => args;\n/**\n * Creates a signal to manage Keycloak events, initializing the signal with\n * appropriate default values or values from a given Keycloak instance.\n *\n * @param keycloak - An instance of the Keycloak client.\n * @returns A `Signal` that tracks the current Keycloak event state.\n */\nconst createKeycloakSignal = (keycloak) => {\n    const keycloakSignal = signal({\n        type: KeycloakEventType.KeycloakAngularInit\n    });\n    if (!keycloak) {\n        keycloakSignal.set({\n            type: KeycloakEventType.KeycloakAngularNotInitialized\n        });\n        return keycloakSignal;\n    }\n    keycloak.onReady = (authenticated) => {\n        keycloakSignal.set({\n            type: KeycloakEventType.Ready,\n            args: authenticated\n        });\n    };\n    keycloak.onAuthError = (errorData) => {\n        keycloakSignal.set({\n            type: KeycloakEventType.AuthError,\n            args: errorData\n        });\n    };\n    keycloak.onAuthLogout = () => {\n        keycloakSignal.set({\n            type: KeycloakEventType.AuthLogout\n        });\n    };\n    keycloak.onActionUpdate = (status, action) => {\n        keycloakSignal.set({\n            type: KeycloakEventType.ActionUpdate,\n            args: { status, action }\n        });\n    };\n    keycloak.onAuthRefreshError = () => {\n        keycloakSignal.set({\n            type: KeycloakEventType.AuthRefreshError\n        });\n    };\n    keycloak.onAuthRefreshSuccess = () => {\n        keycloakSignal.set({\n            type: KeycloakEventType.AuthRefreshSuccess\n        });\n    };\n    keycloak.onAuthSuccess = () => {\n        keycloakSignal.set({\n            type: KeycloakEventType.AuthSuccess\n        });\n    };\n    keycloak.onTokenExpired = () => {\n        keycloakSignal.set({\n            type: KeycloakEventType.TokenExpired\n        });\n    };\n    return keycloakSignal;\n};\n/**\n * Injection token for the Keycloak events signal, used for dependency injection.\n */\nconst KEYCLOAK_EVENT_SIGNAL = new InjectionToken('Keycloak Events Signal');\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Structural directive to conditionally display elements based on Keycloak user roles.\n *\n * This directive checks if the authenticated user has at least one of the specified roles.\n * Roles can be validated against a specific **resource (client ID)** or the **realm**.\n *\n * ### Features:\n * - Supports role checking in both **resources (client-level roles)** and the **realm**.\n * - Accepts an array of roles to match.\n * - Optional configuration to check realm-level roles.\n *\n * ### Inputs:\n * - `kaHasRoles` (Required): Array of roles to validate.\n * - `resource` (Optional): The client ID or resource name to validate resource-level roles.\n * - `checkRealm` (Optional): A boolean flag to enable realm role validation (default is `false`).\n *\n * ### Requirements:\n * - A Keycloak instance must be injected via Angular's dependency injection.\n * - The user must be authenticated in Keycloak.\n *\n * @example\n * #### Example 1: Check for Global Realm Roles\n * Show the content only if the user has the `admin` or `editor` role in the realm.\n * ```html\n * <div *kaHasRoles=\"['admin', 'editor']; checkRealm:true\">\n *   <p>This content is visible only to users with 'admin' or 'editor' realm roles.</p>\n * </div>\n * ```\n *\n * @example\n * #### Example 2: Check for Resource Roles\n * Show the content only if the user has the `read` or `write` role for a specific resource (`my-client`).\n * ```html\n * <div *kaHasRoles=\"['read', 'write']; resource:'my-client'\">\n *   <p>This content is visible only to users with 'read' or 'write' roles for 'my-client'.</p>\n * </div>\n * ```\n *\n * @example\n * #### Example 3: Check for Both Resource and Realm Roles\n * Show the content if the user has the roles in either the realm or a resource.\n * ```html\n * <div *kaHasRoles=\"['admin', 'write']; resource:'my-client' checkRealm:true\">\n *   <p>This content is visible to users with 'admin' in the realm or 'write' in 'my-client'.</p>\n * </div>\n * ```\n *\n * @example\n * #### Example 4: Fallback Content When Roles Do Not Match\n * Use an `<ng-template>` to display fallback content if the user lacks the required roles.\n * ```html\n * <div *kaHasRoles=\"['admin']; resource:'my-client'\">\n *   <p>Welcome, Admin!</p>\n * </div>\n * <ng-template #noAccess>\n *   <p>Access Denied</p>\n * </ng-template>\n * ```\n */\nclass HasRolesDirective {\n    constructor() {\n        this.templateRef = inject(TemplateRef);\n        this.viewContainer = inject(ViewContainerRef);\n        this.keycloak = inject(Keycloak);\n        /**\n         * List of roles to validate against the resource or realm.\n         */\n        this.roles = [];\n        /**\n         * Flag to enable realm-level role validation.\n         */\n        this.checkRealm = false;\n        this.viewContainer.clear();\n        const keycloakSignal = inject(KEYCLOAK_EVENT_SIGNAL);\n        effect(() => {\n            const keycloakEvent = keycloakSignal();\n            if (keycloakEvent.type !== KeycloakEventType.Ready) {\n                return;\n            }\n            const authenticated = typeEventArgs(keycloakEvent.args);\n            if (authenticated) {\n                this.render();\n            }\n        });\n    }\n    render() {\n        const hasAccess = this.checkUserRoles();\n        if (hasAccess) {\n            this.viewContainer.createEmbeddedView(this.templateRef);\n        }\n        else {\n            this.viewContainer.clear();\n        }\n    }\n    /**\n     * Checks if the user has at least one of the specified roles in the resource or realm.\n     * @returns True if the user has access, false otherwise.\n     */\n    checkUserRoles() {\n        const hasResourceRole = this.roles.some((role) => this.keycloak.hasResourceRole(role, this.resource));\n        const hasRealmRole = this.checkRealm ? this.roles.some((role) => this.keycloak.hasRealmRole(role)) : false;\n        return hasResourceRole || hasRealmRole;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: HasRolesDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.3\", type: HasRolesDirective, isStandalone: true, selector: \"[kaHasRoles]\", inputs: { roles: [\"kaHasRoles\", \"roles\"], resource: [\"kaHasRolesResource\", \"resource\"], checkRealm: [\"kaHasRolesCheckRealm\", \"checkRealm\"] }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: HasRolesDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[kaHasRoles]'\n                }]\n        }], ctorParameters: () => [], propDecorators: { roles: [{\n                type: Input,\n                args: ['kaHasRoles']\n            }], resource: [{\n                type: Input,\n                args: ['kaHasRolesResource']\n            }], checkRealm: [{\n                type: Input,\n                args: ['kaHasRolesCheckRealm']\n            }] } });\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Service to monitor user activity in an Angular application.\n * Tracks user interactions (e.g., mouse movement, touch, key presses, clicks, and scrolls)\n * and updates the last activity timestamp. Consumers can check for user inactivity\n * based on a configurable timeout.\n *\n * The service is supposed to be used in the client context and for safety, it checks during the startup\n * if it is a browser context.\n */\nclass UserActivityService {\n    constructor() {\n        this.ngZone = inject(NgZone);\n        /**\n         * Signal to store the timestamp of the last user activity.\n         * The timestamp is represented as the number of milliseconds since epoch.\n         */\n        this.lastActivity = signal(Date.now());\n        /**\n         * Subject to signal the destruction of the service.\n         * Used to clean up RxJS subscriptions.\n         */\n        this.destroy$ = new Subject();\n        /**\n         * Computed signal to expose the last user activity as a read-only signal.\n         */\n        this.lastActivitySignal = computed(() => this.lastActivity());\n    }\n    /**\n     * Starts monitoring user activity events (`mousemove`, `touchstart`, `keydown`, `click`, `scroll`)\n     * and updates the last activity timestamp using RxJS with debounce.\n     * The events are processed outside Angular zone for performance optimization.\n     */\n    startMonitoring() {\n        const isBrowser = isPlatformBrowser(inject(PLATFORM_ID));\n        if (!isBrowser) {\n            return;\n        }\n        this.ngZone.runOutsideAngular(() => {\n            const events = ['mousemove', 'touchstart', 'keydown', 'click', 'scroll'];\n            events.forEach((event) => {\n                fromEvent(window, event)\n                    .pipe(debounceTime(300), takeUntil(this.destroy$))\n                    .subscribe(() => this.updateLastActivity());\n            });\n        });\n    }\n    /**\n     * Updates the last activity timestamp to the current time.\n     * This method runs inside Angular's zone to ensure reactivity with Angular signals.\n     */\n    updateLastActivity() {\n        this.ngZone.run(() => {\n            this.lastActivity.set(Date.now());\n        });\n    }\n    /**\n     * Retrieves the timestamp of the last recorded user activity.\n     * @returns {number} The last activity timestamp in milliseconds since epoch.\n     */\n    get lastActivityTime() {\n        return this.lastActivity();\n    }\n    /**\n     * Determines whether the user interacted with the application, meaning it is activily using the application, based on\n     * the specified duration.\n     * @param timeout - The inactivity timeout in milliseconds.\n     * @returns {boolean} `true` if the user is inactive, otherwise `false`.\n     */\n    isActive(timeout) {\n        return Date.now() - this.lastActivityTime < timeout;\n    }\n    /**\n     * Cleans up RxJS subscriptions and resources when the service is destroyed.\n     * This method is automatically called by Angular when the service is removed.\n     */\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: UserActivityService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: UserActivityService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: UserActivityService, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Service to automatically manage the Keycloak token refresh process\n * based on user activity and token expiration events. This service\n * integrates with Keycloak for session management and interacts with\n * user activity monitoring to determine the appropriate action when\n * the token expires.\n *\n * The service listens to `KeycloakSignal` for token-related events\n * (e.g., `TokenExpired`) and provides configurable options for\n * session timeout and inactivity handling.\n */\nclass AutoRefreshTokenService {\n    constructor() {\n        this.keycloak = inject(Keycloak);\n        this.userActivity = inject(UserActivityService);\n        this.options = this.defaultOptions;\n        this.initialized = false;\n        const keycloakSignal = inject(KEYCLOAK_EVENT_SIGNAL);\n        effect(() => {\n            const keycloakEvent = keycloakSignal();\n            if (keycloakEvent.type === KeycloakEventType.TokenExpired) {\n                this.processTokenExpiredEvent();\n            }\n        });\n    }\n    get defaultOptions() {\n        return {\n            sessionTimeout: 300000,\n            onInactivityTimeout: 'logout'\n        };\n    }\n    executeOnInactivityTimeout() {\n        switch (this.options.onInactivityTimeout) {\n            case 'login':\n                this.keycloak.login().catch((error) => console.error('Failed to execute the login call', error));\n                break;\n            case 'logout':\n                this.keycloak.logout().catch((error) => console.error('Failed to execute the logout call', error));\n                break;\n            default:\n                break;\n        }\n    }\n    processTokenExpiredEvent() {\n        if (!this.initialized || !this.keycloak.authenticated) {\n            return;\n        }\n        if (this.userActivity.isActive(this.options.sessionTimeout)) {\n            this.keycloak.updateToken().catch(() => this.executeOnInactivityTimeout());\n        }\n        else {\n            this.executeOnInactivityTimeout();\n        }\n    }\n    start(options) {\n        this.options = { ...this.defaultOptions, ...options };\n        this.initialized = true;\n        this.userActivity.startMonitoring();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: AutoRefreshTokenService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: AutoRefreshTokenService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: AutoRefreshTokenService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [] });\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Enables automatic token refresh and session inactivity handling for a\n * Keycloak-enabled Angular application.\n *\n * This function initializes a service that tracks user interactions, such as\n * mouse movements, touches, key presses, clicks, and scrolls. If user activity\n * is detected, it periodically calls `Keycloak.updateToken` to ensure the bearer\n * token remains valid and does not expire.\n *\n * If the session remains inactive beyond the defined `sessionTimeout`, the\n * specified action (`logout`, `login`, or `none`) will be executed. By default,\n * the service will call `keycloak.logout` upon inactivity timeout.\n *\n * Event tracking uses RxJS observables with a debounce of 300 milliseconds to\n * monitor user interactions. When the Keycloak `OnTokenExpired` event occurs,\n * the service checks the user's last activity timestamp. If the user has been\n * active within the session timeout period, it refreshes the token using `updateToken`.\n *\n *\n * @param options - Configuration options for the auto-refresh token feature.\n *   - `sessionTimeout` (optional): The duration in milliseconds after which\n *     the session is considered inactive. Defaults to `300000` (5 minutes).\n *   - `onInactivityTimeout` (optional): The action to take when session inactivity\n *     exceeds the specified timeout. Defaults to `'logout'`.\n *       - `'login'`: Execute `keycloak.login` function.\n *       - `'logout'`: Logs the user out by calling `keycloak.logout`.\n *       - `'none'`: No action is taken.\n *\n * @returns A `KeycloakFeature` instance that configures and enables the\n * auto-refresh token functionality.\n */\nfunction withAutoRefreshToken(options) {\n    return {\n        configure: () => {\n            const autoRefreshTokenService = inject(AutoRefreshTokenService);\n            autoRefreshTokenService.start(options);\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\nconst mapResourceRoles = (resourceAccess = {}) => {\n    return Object.entries(resourceAccess).reduce((roles, [key, value]) => {\n        roles[key] = value.roles;\n        return roles;\n    }, {});\n};\n/**\n * Creates a custom authorization guard for Angular routes, enabling fine-grained access control.\n *\n * This guard invokes the provided `isAccessAllowed` function to determine if access is permitted\n * based on the current route, router state, and user's authentication and roles data.\n *\n * @template T - The type of the guard function (`CanActivateFn` or `CanActivateChildFn`).\n * @param isAccessAllowed - A callback function that evaluates access conditions. The function receives:\n *   - `route`: The current `ActivatedRouteSnapshot` for the route being accessed.\n *   - `state`: The current `RouterStateSnapshot` representing the router's state.\n *   - `authData`: An `AuthGuardData` object containing the user's authentication status, roles, and Keycloak instance.\n * @returns A guard function of type `T` that can be used as a route `canActivate` or `canActivateChild` guard.\n *\n * @example\n * ```ts\n * import { createAuthGuard } from './auth-guard';\n * import { Routes } from '@angular/router';\n *\n * const isUserAllowed = async (route, state, authData) => {\n *   const { authenticated, grantedRoles } = authData;\n *   return authenticated && grantedRoles.realmRoles.includes('admin');\n * };\n *\n * const routes: Routes = [\n *   {\n *     path: 'admin',\n *     canActivate: [createAuthGuard(isUserAllowed)],\n *     component: AdminComponent,\n *   },\n * ];\n * ```\n */\nconst createAuthGuard = (isAccessAllowed) => {\n    return ((next, state) => {\n        const keycloak = inject(Keycloak);\n        const authenticated = keycloak?.authenticated ?? false;\n        const grantedRoles = {\n            resourceRoles: mapResourceRoles(keycloak?.resourceAccess),\n            realmRoles: keycloak?.realmAccess?.roles ?? []\n        };\n        const authData = { authenticated, keycloak, grantedRoles };\n        return isAccessAllowed(next, state, authData);\n    });\n};\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Default value for the authorization header prefix, used to construct the Authorization token.\n */\nconst BEARER_PREFIX = 'Bearer';\n/**\n * Default name of the authorization header.\n */\nconst AUTHORIZATION_HEADER_NAME = 'Authorization';\n/**\n * Generic factory function to create an interceptor condition with default values.\n *\n * This utility allows you to define custom interceptor conditions while ensuring that\n * default values are applied to any missing fields. By using generics, you can enforce\n * strong typing when creating the fields for the interceptor condition, enhancing type safety.\n *\n * @template T - A type that extends `AuthBearerCondition`.\n * @param value - An object of type `T` (extending `AuthBearerCondition`) to be enhanced with default values.\n * @returns A new object of type `T` with default values assigned to any undefined properties.\n */\nconst createInterceptorCondition = (value) => ({\n    ...value,\n    bearerPrefix: value.bearerPrefix ?? BEARER_PREFIX,\n    authorizationHeaderName: value.authorizationHeaderName ?? AUTHORIZATION_HEADER_NAME,\n    shouldUpdateToken: value.shouldUpdateToken ?? (() => true)\n});\n/**\n * Conditionally updates the Keycloak token based on the provided request and conditions.\n *\n * @param req - The `HttpRequest` object being processed.\n * @param keycloak - The Keycloak instance managing authentication.\n * @param condition - An `AuthBearerCondition` object with the `shouldUpdateToken` function.\n * @returns A `Promise<boolean>` indicating whether the token was successfully updated.\n */\nconst conditionallyUpdateToken = async (req, keycloak, { shouldUpdateToken = (_) => true }) => {\n    if (shouldUpdateToken(req)) {\n        return await keycloak.updateToken().catch(() => false);\n    }\n    return true;\n};\n/**\n * Adds the Authorization header to an HTTP request and forwards it to the next handler.\n *\n * @param req - The original `HttpRequest` object.\n * @param next - The `HttpHandlerFn` function for forwarding the HTTP request.\n * @param keycloak - The Keycloak instance providing the authentication token.\n * @param condition - An `AuthBearerCondition` object specifying header configuration.\n * @returns An `Observable<HttpEvent<unknown>>` representing the HTTP response.\n */\nconst addAuthorizationHeader = (req, next, keycloak, condition) => {\n    const { bearerPrefix = BEARER_PREFIX, authorizationHeaderName = AUTHORIZATION_HEADER_NAME } = condition;\n    const clonedRequest = req.clone({\n        setHeaders: {\n            [authorizationHeaderName]: `${bearerPrefix} ${keycloak.token}`\n        }\n    });\n    return next(clonedRequest);\n};\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Injection token for configuring the `customBearerTokenInterceptor`.\n *\n * This injection token holds an array of `CustomBearerTokenCondition` objects, which define\n * the conditions under which a Bearer token should be included in the `Authorization` header\n * of outgoing HTTP requests. Each condition provides a `shouldAddToken` function that dynamically\n * determines whether the token should be added based on the request, handler, and Keycloak state.\n */\nconst CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG = new InjectionToken('Include the bearer token as implemented by the provided function');\n/**\n * Custom HTTP Interceptor for dynamically adding a Bearer token to requests based on conditions.\n *\n * This interceptor uses a flexible approach where the decision to include a Bearer token in the\n * `Authorization` HTTP header is determined by a user-provided function (`shouldAddToken`).\n * This enables a dynamic and granular control over when tokens are added to HTTP requests.\n *\n * ### Key Features:\n * 1. **Dynamic Token Inclusion**: Uses a condition function (`shouldAddToken`) to decide dynamically\n *    whether to add the token based on the request, Keycloak state, and other factors.\n * 2. **Token Management**: Optionally refreshes the Keycloak token before adding it to the request.\n * 3. **Controlled Authorization**: Adds the Bearer token only when the condition function allows\n *    and the user is authenticated in Keycloak.\n *\n * ### Configuration:\n * The interceptor relies on `CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG`, an injection token that contains\n * an array of `CustomBearerTokenCondition` objects. Each condition specifies a `shouldAddToken` function\n * that determines whether to add the Bearer token for a given request.\n *\n * ### Workflow:\n * 1. Reads the conditions from the `CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG` injection token.\n * 2. Iterates through the conditions and evaluates the `shouldAddToken` function for the request.\n * 3. If a condition matches:\n *    - Optionally refreshes the Keycloak token if needed.\n *    - Adds the Bearer token to the request's `Authorization` header if the user is authenticated.\n * 4. If no conditions match, the request proceeds unchanged.\n *\n * ### Parameters:\n * @param req - The `HttpRequest` object representing the outgoing HTTP request.\n * @param next - The `HttpHandlerFn` for passing the request to the next handler in the chain.\n *\n * @returns An `Observable<HttpEvent<unknown>>` representing the HTTP response.\n *\n * ### Usage Example:\n * ```typescript\n * // Define a custom condition to include the token\n * const customCondition: CustomBearerTokenCondition = {\n *   shouldAddToken: async (req, next, keycloak) => {\n *     // Add token only for requests to the /api endpoint\n *     return req.url.startsWith('/api') && keycloak.authenticated;\n *   },\n * };\n *\n * // Configure the interceptor with the custom condition\n * export const appConfig: ApplicationConfig = {\n *   providers: [\n *     provideHttpClient(withInterceptors([customBearerTokenInterceptor])),\n *     {\n *       provide: CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG,\n *       useValue: [customCondition],\n *     },\n *   ],\n * };\n * ```\n */\nconst customBearerTokenInterceptor = (req, next) => {\n    const conditions = inject(CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG) ?? [];\n    const keycloak = inject(Keycloak);\n    return from(Promise.all(conditions.map(async (condition) => await condition.shouldAddToken(req, next, keycloak)))).pipe(mergeMap$1((evaluatedConditions) => {\n        const matchingConditionIndex = evaluatedConditions.findIndex(Boolean);\n        const matchingCondition = conditions[matchingConditionIndex];\n        if (!matchingCondition) {\n            return next(req);\n        }\n        return from(conditionallyUpdateToken(req, keycloak, matchingCondition)).pipe(mergeMap$1(() => keycloak.authenticated ? addAuthorizationHeader(req, next, keycloak, matchingCondition) : next(req)));\n    }));\n};\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Injection token for configuring the `includeBearerTokenInterceptor`, allowing the specification\n * of conditions under which the Bearer token should be included in HTTP request headers.\n *\n * This configuration supports multiple conditions, enabling customization for different URLs.\n * It also provides options to tailor the Bearer prefix and the Authorization header name as needed.\n */\nconst INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG = new InjectionToken('Include the bearer token when explicitly defined int the URL pattern condition');\nconst findMatchingCondition = ({ method, url }, { urlPattern, httpMethods = [] }) => {\n    const httpMethodTest = httpMethods.length === 0 || httpMethods.join().indexOf(method.toUpperCase()) > -1;\n    const urlTest = urlPattern.test(url);\n    return httpMethodTest && urlTest;\n};\n/**\n * HTTP Interceptor to include a Bearer token in the Authorization header for specific HTTP requests.\n *\n * This interceptor ensures that a Bearer token is added to outgoing HTTP requests based on explicitly\n * defined conditions. By default, the interceptor does not include the Bearer token unless the request\n * matches the provided configuration (`IncludeBearerTokenCondition`). This approach enhances security\n * by preventing sensitive tokens from being unintentionally sent to unauthorized services.\n *\n * ### Features:\n * 1. **Explicit URL Matching**: The interceptor uses regular expressions to match URLs where the Bearer token should be included.\n * 2. **HTTP Method Filtering**: Optional filtering by HTTP methods (e.g., `GET`, `POST`, `PUT`) to refine the conditions for adding the token.\n * 3. **Token Management**: Ensures the Keycloak token is valid by optionally refreshing it before attaching it to the request.\n * 4. **Controlled Authorization**: Sends the token only for requests where the user is authenticated, and the conditions match.\n *\n * ### Workflow:\n * - Reads conditions from `INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG`, which specifies when the Bearer token should be included.\n * - If a request matches the conditions:\n *   1. The Keycloak token is refreshed if needed.\n *   2. The Bearer token is added to the Authorization header.\n *   3. The modified request is passed to the next handler.\n * - If no conditions match, the request proceeds unchanged.\n *\n * ### Security:\n * By explicitly defining URL patterns and optional HTTP methods, this interceptor prevents the leakage of tokens\n * to unintended endpoints, such as third-party APIs or external services. This is especially critical for applications\n * that interact with both internal and external services.\n *\n * @param req - The `HttpRequest` object representing the outgoing HTTP request.\n * @param next - The `HttpHandlerFn` for passing the request to the next handler in the chain.\n * @returns An `Observable<HttpEvent<unknown>>` representing the asynchronous HTTP response.\n *\n * ### Configuration:\n * The interceptor relies on `INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG`, an injection token that holds\n * an array of `IncludeBearerTokenCondition` objects. Each object defines the conditions for including\n * the Bearer token in the request.\n *\n * #### Example Configuration:\n * ```typescript\n * provideHttpClient(\n *   withInterceptors([includeBearerTokenInterceptor]),\n *   {\n *     provide: INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG,\n *     useValue: [\n *       {\n *         urlPattern: /^https:\\/\\/api\\.internal\\.myapp\\.com\\/.*\\/,\n *         httpMethods: ['GET', 'POST'], // Add the token only for GET and POST methods\n *       },\n *     ],\n *   }\n * );\n * ```\n *\n * ### Example Usage:\n * ```typescript\n * export const appConfig: ApplicationConfig = {\n *   providers: [\n *     provideHttpClient(withInterceptors([includeBearerTokenInterceptor])),\n *     provideZoneChangeDetection({ eventCoalescing: true }),\n *     provideRouter(routes),\n *   ],\n * };\n * ```\n *\n * ### Example Matching Condition:\n * ```typescript\n * {\n *   urlPattern: /^(https:\\/\\/internal\\.mycompany\\.com)(\\/.*)?$/i,\n *   httpMethods: ['GET', 'PUT'], // Optional: Match only specific HTTP methods\n * }\n * ```\n */\nconst includeBearerTokenInterceptor = (req, next) => {\n    const conditions = inject(INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG) ?? [];\n    const matchingCondition = conditions.find((condition) => findMatchingCondition(req, condition));\n    if (!matchingCondition) {\n        return next(req);\n    }\n    const keycloak = inject(Keycloak);\n    return from(conditionallyUpdateToken(req, keycloak, matchingCondition)).pipe(mergeMap$1(() => keycloak.authenticated ? addAuthorizationHeader(req, next, keycloak, matchingCondition) : next(req)));\n};\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Provides Keycloak initialization logic for the app initializer phase.\n * Ensures Keycloak is initialized and features are configured.\n *\n * @param keycloak - The Keycloak instance.\n * @param options - ProvideKeycloakOptions for configuration.\n * @returns EnvironmentProviders or an empty array if `initOptions` is not provided.\n */\nconst provideKeycloakInAppInitializer = (keycloak, options) => {\n    const { initOptions, features = [] } = options;\n    if (!initOptions) {\n        return [];\n    }\n    return provideAppInitializer(async () => {\n        const injector = inject(EnvironmentInjector);\n        runInInjectionContext(injector, () => features.forEach((feature) => feature.configure()));\n        await keycloak.init(initOptions).catch((error) => console.error('Keycloak initialization failed', error));\n    });\n};\n/**\n * Configures and provides Keycloak as a dependency in an Angular application.\n *\n * This function initializes a Keycloak instance with the provided configuration and\n * optional initialization options. It integrates Keycloak into Angular dependency\n * injection system, allowing easy consumption throughout the application. Additionally,\n * it supports custom providers and Keycloak Angular features.\n *\n * If `initOptions` is not provided, the Keycloak instance will not be automatically initialized.\n * In such cases, the application must call `keycloak.init()` explicitly.\n *\n * @param options - Configuration object for Keycloak:\n *   - `config`: The Keycloak configuration, including the server URL, realm, and client ID.\n *   - `initOptions` (Optional): Initialization options for the Keycloak instance.\n *   - `providers` (Optional): Additional Angular providers to include.\n *   - `features` (Optional): Keycloak Angular features to configure during initialization.\n *\n * @returns An `EnvironmentProviders` object integrating Keycloak setup and additional providers.\n *\n * @example\n * ```ts\n * import { provideKeycloak } from './keycloak.providers';\n * import { bootstrapApplication } from '@angular/platform-browser';\n * import { AppComponent } from './app/app.component';\n *\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *     provideKeycloak({\n *       config: {\n *         url: 'https://auth-server.example.com',\n *         realm: 'my-realm',\n *         clientId: 'my-client',\n *       },\n *       initOptions: {\n *         onLoad: 'login-required',\n *       },\n *     }),\n *   ],\n * });\n * ```\n */\nfunction provideKeycloak(options) {\n    const keycloak = new Keycloak(options.config);\n    const providers = options.providers ?? [];\n    const keycloakSignal = createKeycloakSignal(keycloak);\n    return makeEnvironmentProviders([\n        {\n            provide: KEYCLOAK_EVENT_SIGNAL,\n            useValue: keycloakSignal\n        },\n        {\n            provide: Keycloak,\n            useValue: keycloak\n        },\n        ...providers,\n        provideKeycloakInAppInitializer(keycloak, options)\n    ]);\n}\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoRefreshTokenService, CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG, CoreModule, HasRolesDirective, INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG, KEYCLOAK_EVENT_SIGNAL, KeycloakAngularModule, KeycloakAuthGuard, KeycloakBearerInterceptor, KeycloakEventType, KeycloakEventTypeLegacy, KeycloakService, UserActivityService, addAuthorizationHeader, conditionallyUpdateToken, createAuthGuard, createInterceptorCondition, createKeycloakSignal, customBearerTokenInterceptor, includeBearerTokenInterceptor, provideKeycloak, typeEventArgs, withAutoRefreshToken };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,cAAc,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,wBAAwB,QAAQ,eAAe;AACzQ,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,sBAAsB;AACrE,SAASC,OAAO,EAAEC,IAAI,EAAEC,aAAa,EAAEC,EAAE,EAAEC,SAAS,EAAEC,QAAQ,IAAIC,UAAU,QAAQ,MAAM;AAC1F,SAASC,GAAG,EAAEF,QAAQ,EAAEG,YAAY,EAAEC,SAAS,QAAQ,gBAAgB;AACvE,OAAOC,QAAQ,MAAM,aAAa;AAClC,SAASC,YAAY,EAAEC,iBAAiB,QAAQ,iBAAiB;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,uBAAuB;AAC3B,CAAC,UAAUA,uBAAuB,EAAE;EAChC;AACJ;AACA;EACIA,uBAAuB,CAACA,uBAAuB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EACnF;AACJ;AACA;AACA;EACIA,uBAAuB,CAACA,uBAAuB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACrF;AACJ;AACA;EACIA,uBAAuB,CAACA,uBAAuB,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB;EACjG;AACJ;AACA;EACIA,uBAAuB,CAACA,uBAAuB,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,GAAG,sBAAsB;EACrG;AACJ;AACA;EACIA,uBAAuB,CAACA,uBAAuB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;EACvF;AACJ;AACA;EACIA,uBAAuB,CAACA,uBAAuB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAC3E;AACJ;AACA;AACA;AACA;EACIA,uBAAuB,CAACA,uBAAuB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACzF;AACJ;AACA;EACIA,uBAAuB,CAACA,uBAAuB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;AAC7F,CAAC,EAAEA,uBAAuB,KAAKA,uBAAuB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,CAAC;EACpBC,WAAWA,CAACC,MAAM,EAAEC,eAAe,EAAE;IACjC,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,eAAe,GAAGA,eAAe;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC5B,IAAI;QACAD,KAAI,CAACE,aAAa,SAASF,KAAI,CAACJ,eAAe,CAACO,UAAU,CAAC,CAAC;QAC5DH,KAAI,CAACI,KAAK,SAASJ,KAAI,CAACJ,eAAe,CAACS,YAAY,CAAC,IAAI,CAAC;QAC1D,aAAaL,KAAI,CAACM,eAAe,CAACR,KAAK,EAAEC,KAAK,CAAC;MACnD,CAAC,CACD,OAAOQ,KAAK,EAAE;QACV,MAAM,IAAIC,KAAK,CAAC,sDAAsD,GAAGD,KAAK,CAAC;MACnF;IAAC;EACL;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,eAAe,CAAC;EAClBf,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAACgB,gBAAgB,GAAG,IAAI/B,OAAO,CAAC,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIgC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACC,SAAS,CAACC,WAAW,GAAIC,SAAS,IAAK;MACxC,IAAI,CAACJ,gBAAgB,CAACK,IAAI,CAAC;QACvBC,IAAI,EAAEF,SAAS;QACfG,IAAI,EAAEzB,uBAAuB,CAAC0B;MAClC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACN,SAAS,CAACO,YAAY,GAAG,MAAM;MAChC,IAAI,CAACT,gBAAgB,CAACK,IAAI,CAAC;QAAEE,IAAI,EAAEzB,uBAAuB,CAAC4B;MAAa,CAAC,CAAC;IAC9E,CAAC;IACD,IAAI,CAACR,SAAS,CAACS,oBAAoB,GAAG,MAAM;MACxC,IAAI,CAACX,gBAAgB,CAACK,IAAI,CAAC;QACvBE,IAAI,EAAEzB,uBAAuB,CAAC8B;MAClC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACV,SAAS,CAACW,kBAAkB,GAAG,MAAM;MACtC,IAAI,CAACb,gBAAgB,CAACK,IAAI,CAAC;QACvBE,IAAI,EAAEzB,uBAAuB,CAACgC;MAClC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACZ,SAAS,CAACa,aAAa,GAAG,MAAM;MACjC,IAAI,CAACf,gBAAgB,CAACK,IAAI,CAAC;QAAEE,IAAI,EAAEzB,uBAAuB,CAACkC;MAAc,CAAC,CAAC;IAC/E,CAAC;IACD,IAAI,CAACd,SAAS,CAACe,cAAc,GAAG,MAAM;MAClC,IAAI,CAACjB,gBAAgB,CAACK,IAAI,CAAC;QACvBE,IAAI,EAAEzB,uBAAuB,CAACoC;MAClC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAChB,SAAS,CAACiB,cAAc,GAAI9B,KAAK,IAAK;MACvC,IAAI,CAACW,gBAAgB,CAACK,IAAI,CAAC;QACvBC,IAAI,EAAEjB,KAAK;QACXkB,IAAI,EAAEzB,uBAAuB,CAACsC;MAClC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAClB,SAAS,CAACmB,OAAO,GAAI7B,aAAa,IAAK;MACxC,IAAI,CAACQ,gBAAgB,CAACK,IAAI,CAAC;QACvBC,IAAI,EAAEd,aAAa;QACnBe,IAAI,EAAEzB,uBAAuB,CAACwC;MAClC,CAAC,CAAC;IACN,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,gBAAgBA,CAACC,kBAAkB,EAAE;IACjC,MAAMC,YAAY,GAAG,EAAE;IACvB,KAAK,MAAMC,IAAI,IAAIF,kBAAkB,EAAE;MACnC,IAAIG,WAAW;MACf,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;QAC1BC,WAAW,GAAG;UAAEC,UAAU,EAAE,IAAIC,MAAM,CAACH,IAAI,EAAE,GAAG,CAAC;UAAEI,WAAW,EAAE;QAAG,CAAC;MACxE,CAAC,MACI;QACDH,WAAW,GAAG;UACVC,UAAU,EAAE,IAAIC,MAAM,CAACH,IAAI,CAACK,GAAG,EAAE,GAAG,CAAC;UACrCD,WAAW,EAAEJ,IAAI,CAACI;QACtB,CAAC;MACL;MACAL,YAAY,CAACO,IAAI,CAACL,WAAW,CAAC;IAClC;IACA,OAAOF,YAAY;EACvB;EACA;AACJ;AACA;AACA;AACA;EACIQ,iBAAiBA,CAAC;IAAEC,uBAAuB,GAAG,IAAI;IAAEC,wBAAwB,GAAG,KAAK;IAAEX,kBAAkB,GAAG,EAAE;IAAEY,uBAAuB,GAAG,eAAe;IAAEC,YAAY,GAAG,QAAQ;IAAEC,WAAW;IAAEC,iBAAiB,GAAG,EAAE;IAAEC,cAAc,GAAGA,CAAA,KAAM,IAAI;IAAEC,iBAAiB,GAAGA,CAAA,KAAM;EAAK,CAAC,EAAE;IACnR,IAAI,CAACC,wBAAwB,GAAGR,uBAAuB;IACvD,IAAI,CAACS,yBAAyB,GAAGR,wBAAwB;IACzD,IAAI,CAACS,wBAAwB,GAAGR,uBAAuB;IACvD,IAAI,CAACS,aAAa,GAAGR,YAAY,CAACS,IAAI,CAAC,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC;IACpD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACzB,gBAAgB,CAACC,kBAAkB,CAAC;IAC9D,IAAI,CAACyB,cAAc,GAAGX,WAAW,GAAGA,WAAW,CAACY,IAAI,KAAK,UAAU,GAAG,KAAK;IAC3E,IAAI,CAACC,kBAAkB,GAAGZ,iBAAiB;IAC3C,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUW,IAAIA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA/D,iBAAA;MACrB+D,MAAI,CAACrB,iBAAiB,CAACoB,OAAO,CAAC;MAC/B,MAAM;QAAEE,MAAM;QAAEjB;MAAY,CAAC,GAAGe,OAAO;MACvCC,MAAI,CAACpD,SAAS,GAAG,IAAIvB,QAAQ,CAAC4E,MAAM,CAAC;MACrCD,MAAI,CAACrD,mBAAmB,CAAC,CAAC;MAC1B,MAAMT,aAAa,SAAS8D,MAAI,CAACpD,SAAS,CAACkD,IAAI,CAACd,WAAW,CAAC;MAC5D,IAAI9C,aAAa,IAAI8D,MAAI,CAACX,yBAAyB,EAAE;QACjD,MAAMW,MAAI,CAACE,eAAe,CAAC,CAAC;MAChC;MACA,OAAOhE,aAAa;IAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUiE,KAAKA,CAACJ,OAAO,GAAG,CAAC,CAAC,EAAE;IAAA,IAAAK,MAAA;IAAA,OAAAnE,iBAAA;MACtB,MAAMmE,MAAI,CAACxD,SAAS,CAACuD,KAAK,CAACJ,OAAO,CAAC;MACnC,IAAIK,MAAI,CAACf,yBAAyB,EAAE;QAChC,MAAMe,MAAI,CAACF,eAAe,CAAC,CAAC;MAChC;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUG,MAAMA,CAACC,WAAW,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAtE,iBAAA;MACtB,MAAM8D,OAAO,GAAG;QACZO;MACJ,CAAC;MACD,MAAMC,MAAI,CAAC3D,SAAS,CAACyD,MAAM,CAACN,OAAO,CAAC;MACpCQ,MAAI,CAACC,YAAY,GAAGC,SAAS;IAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUC,QAAQA,CAACX,OAAO,GAAG;IAAEY,MAAM,EAAE;EAAW,CAAC,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA3E,iBAAA;MAC7C,MAAM2E,MAAI,CAAChE,SAAS,CAAC8D,QAAQ,CAACX,OAAO,CAAC;IAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIc,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IACzB,IAAIC,OAAO;IACXA,OAAO,GAAG,IAAI,CAACpE,SAAS,CAACqE,eAAe,CAACH,IAAI,EAAEC,QAAQ,CAAC;IACxD,IAAI,CAACC,OAAO,EAAE;MACVA,OAAO,GAAG,IAAI,CAACpE,SAAS,CAACsE,YAAY,CAACJ,IAAI,CAAC;IAC/C;IACA,OAAOE,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI3E,YAAYA,CAAC8E,UAAU,GAAG,IAAI,EAAEJ,QAAQ,EAAE;IACtC,IAAI3E,KAAK,GAAG,EAAE;IACd,IAAI,IAAI,CAACQ,SAAS,CAACwE,cAAc,EAAE;MAC/BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1E,SAAS,CAACwE,cAAc,CAAC,CAACG,OAAO,CAAEC,GAAG,IAAK;QACxD,IAAIT,QAAQ,IAAIA,QAAQ,KAAKS,GAAG,EAAE;UAC9B;QACJ;QACA,MAAMJ,cAAc,GAAG,IAAI,CAACxE,SAAS,CAACwE,cAAc,CAACI,GAAG,CAAC;QACzD,MAAMC,WAAW,GAAGL,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE;QACjDhF,KAAK,GAAGA,KAAK,CAACqD,MAAM,CAACgC,WAAW,CAAC;MACrC,CAAC,CAAC;IACN;IACA,IAAIN,UAAU,IAAI,IAAI,CAACvE,SAAS,CAAC8E,WAAW,EAAE;MAC1C,MAAMP,UAAU,GAAG,IAAI,CAACvE,SAAS,CAAC8E,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE;MAC5DtF,KAAK,CAACsC,IAAI,CAAC,GAAGyC,UAAU,CAAC;IAC7B;IACA,OAAO/E,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACID,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACS,SAAS,EAAE;MACjB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACA,SAAS,CAACV,aAAa;EACvC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIyF,cAAcA,CAACC,WAAW,GAAG,CAAC,EAAE;IAC5B,OAAO,IAAI,CAAChF,SAAS,CAAC+E,cAAc,CAACC,WAAW,CAAC;EACrD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUC,WAAWA,CAACD,WAAW,GAAG,IAAI,CAAC/B,kBAAkB,EAAE;IAAA,IAAAiC,MAAA;IAAA,OAAA7F,iBAAA;MACrD;MACA;MACA,IAAI6F,MAAI,CAACnC,cAAc,EAAE;QACrB,IAAImC,MAAI,CAACH,cAAc,CAAC,CAAC,EAAE;UACvB,MAAM,IAAInF,KAAK,CAAC,wDAAwD,CAAC;QAC7E;QACA,OAAO,IAAI;MACf;MACA,IAAI,CAACsF,MAAI,CAAClF,SAAS,EAAE;QACjB,MAAM,IAAIJ,KAAK,CAAC,8CAA8C,CAAC;MACnE;MACA,IAAI;QACA,aAAasF,MAAI,CAAClF,SAAS,CAACiF,WAAW,CAACD,WAAW,CAAC;MACxD,CAAC,CACD,OAAOrF,KAAK,EAAE;QACV,OAAO,KAAK;MAChB;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACU2D,eAAeA,CAAC6B,WAAW,GAAG,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA/F,iBAAA;MACvC,IAAI+F,MAAI,CAACxB,YAAY,IAAI,CAACuB,WAAW,EAAE;QACnC,OAAOC,MAAI,CAACxB,YAAY;MAC5B;MACA,IAAI,CAACwB,MAAI,CAACpF,SAAS,CAACV,aAAa,EAAE;QAC/B,MAAM,IAAIM,KAAK,CAAC,+DAA+D,CAAC;MACpF;MACA,OAAQwF,MAAI,CAACxB,YAAY,SAASwB,MAAI,CAACpF,SAAS,CAACsD,eAAe,CAAC,CAAC;IAAE;EACxE;EACA;AACJ;AACA;EACU+B,QAAQA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAjG,iBAAA;MACb,OAAOiG,MAAI,CAACtF,SAAS,CAACuF,KAAK;IAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAAC5B,YAAY,EAAE;MACpB,MAAM,IAAIhE,KAAK,CAAC,oDAAoD,CAAC;IACzE;IACA,OAAO,IAAI,CAACgE,YAAY,CAAC6B,QAAQ;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC1F,SAAS,CAAC0F,UAAU,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,gBAAgBA,CAACC,OAAO,GAAG,IAAI/H,WAAW,CAAC,CAAC,EAAE;IAC1C,OAAOG,IAAI,CAAC,IAAI,CAACqH,QAAQ,CAAC,CAAC,CAAC,CAACQ,IAAI,CAACvH,GAAG,CAAEiH,KAAK,IAAMA,KAAK,GAAGK,OAAO,CAACE,GAAG,CAAC,IAAI,CAACpD,wBAAwB,EAAE,IAAI,CAACC,aAAa,GAAG4C,KAAK,CAAC,GAAGK,OAAQ,CAAC,CAAC;EACjJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIG,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC/F,SAAS;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIuB,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACuB,aAAa;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAId,uBAAuBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAACQ,wBAAwB;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIwD,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAClG,gBAAgB;EAChC;EACA;IAAS,IAAI,CAACmG,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFtG,eAAe;IAAA,CAAoD;EAAE;EAC/K;IAAS,IAAI,CAACuG,KAAK,kBAD6EzJ,EAAE,CAAA0J,kBAAA;MAAAd,KAAA,EACY1F,eAAe;MAAAyG,OAAA,EAAfzG,eAAe,CAAAoG;IAAA,EAAG;EAAE;AACtI;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAHoG5J,EAAE,CAAA6J,iBAAA,CAGX3G,eAAe,EAAc,CAAC;IAC7GQ,IAAI,EAAEzD;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6J,yBAAyB,CAAC;EAC5B3H,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4H,QAAQ,GAAG7J,MAAM,CAACgD,eAAe,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACU8G,wBAAwBA,CAACC,GAAG,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAxH,iBAAA;MAChC,IAAIwH,MAAI,CAACH,QAAQ,CAACnE,iBAAiB,CAACqE,GAAG,CAAC,EAAE;QACtC,aAAaC,MAAI,CAACH,QAAQ,CAACzB,WAAW,CAAC,CAAC;MAC5C;MACA,OAAO,IAAI;IAAC;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI6B,aAAaA,CAAC;IAAEC,MAAM;IAAElF;EAAI,CAAC,EAAE;IAAEH,UAAU;IAAEE;EAAY,CAAC,EAAE;IACxD,MAAMoF,QAAQ,GAAGpF,WAAW,CAACqF,MAAM,KAAK,CAAC,IAAIrF,WAAW,CAACsF,IAAI,CAAC,CAAC,CAACC,OAAO,CAACJ,MAAM,CAACK,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClG,MAAMC,OAAO,GAAG3F,UAAU,CAAC4F,IAAI,CAACzF,GAAG,CAAC;IACpC,OAAOmF,QAAQ,IAAIK,OAAO;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,SAASA,CAACX,GAAG,EAAEzG,IAAI,EAAE;IACjB,MAAM;MAAE6B,uBAAuB;MAAET;IAAa,CAAC,GAAG,IAAI,CAACmF,QAAQ;IAC/D,IAAI,CAAC1E,uBAAuB,EAAE;MAC1B,OAAO7B,IAAI,CAACqH,MAAM,CAACZ,GAAG,CAAC;IAC3B;IACA,MAAMa,SAAS,GAAG,CAAC,IAAI,CAACf,QAAQ,CAACpE,cAAc,CAACsE,GAAG,CAAC,IAAIrF,YAAY,CAACmG,SAAS,CAAElG,IAAI,IAAK,IAAI,CAACsF,aAAa,CAACF,GAAG,EAAEpF,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5H,IAAIiG,SAAS,EAAE;MACX,OAAOtH,IAAI,CAACqH,MAAM,CAACZ,GAAG,CAAC;IAC3B;IACA,OAAO3I,aAAa,CAAC,CAACD,IAAI,CAAC,IAAI,CAAC2I,wBAAwB,CAACC,GAAG,CAAC,CAAC,EAAE1I,EAAE,CAAC,IAAI,CAACwI,QAAQ,CAACnH,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAACsG,IAAI,CAACzH,QAAQ,CAAC,CAAC,CAACuJ,CAAC,EAAEpI,UAAU,CAAC,KAAMA,UAAU,GAAG,IAAI,CAACqI,4BAA4B,CAAChB,GAAG,EAAEzG,IAAI,CAAC,GAAGA,IAAI,CAACqH,MAAM,CAACZ,GAAG,CAAE,CAAC,CAAC;EACxN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgB,4BAA4BA,CAAChB,GAAG,EAAEzG,IAAI,EAAE;IACpC,OAAO,IAAI,CAACuG,QAAQ,CAACf,gBAAgB,CAACiB,GAAG,CAAChB,OAAO,CAAC,CAACC,IAAI,CAACzH,QAAQ,CAAEyJ,iBAAiB,IAAK;MACpF,MAAMC,KAAK,GAAGlB,GAAG,CAACmB,KAAK,CAAC;QAAEnC,OAAO,EAAEiC;MAAkB,CAAC,CAAC;MACvD,OAAO1H,IAAI,CAACqH,MAAM,CAACM,KAAK,CAAC;IAC7B,CAAC,CAAC,CAAC;EACP;EACA;IAAS,IAAI,CAAC7B,IAAI,YAAA+B,kCAAA7B,CAAA;MAAA,YAAAA,CAAA,IAAwFM,yBAAyB;IAAA,CAAoD;EAAE;EACzL;IAAS,IAAI,CAACL,KAAK,kBAtF6EzJ,EAAE,CAAA0J,kBAAA;MAAAd,KAAA,EAsFYkB,yBAAyB;MAAAH,OAAA,EAAzBG,yBAAyB,CAAAR;IAAA,EAAG;EAAE;AAChJ;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAxFoG5J,EAAE,CAAA6J,iBAAA,CAwFXC,yBAAyB,EAAc,CAAC;IACvHpG,IAAI,EAAEzD;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqL,UAAU,CAAC;EACb;IAAS,IAAI,CAAChC,IAAI,YAAAiC,mBAAA/B,CAAA;MAAA,YAAAA,CAAA,IAAwF8B,UAAU;IAAA,CAAkD;EAAE;EACxK;IAAS,IAAI,CAACE,IAAI,kBA1G8ExL,EAAE,CAAAyL,gBAAA;MAAA/H,IAAA,EA0GS4H;IAAU,EAA4B;EAAE;EACnJ;IAAS,IAAI,CAACI,IAAI,kBA3G8E1L,EAAE,CAAA2L,gBAAA;MAAAC,SAAA,EA2GgC,CAC1H1I,eAAe,EACf;QACI2I,OAAO,EAAE1K,iBAAiB;QAC1B2K,QAAQ,EAAEhC,yBAAyB;QACnCiC,KAAK,EAAE;MACX,CAAC,CACJ;MAAAC,OAAA,GAAYjK,YAAY;IAAA,EAAI;EAAE;AACvC;AACA;EAAA,QAAA6H,SAAA,oBAAAA,SAAA,KApHoG5J,EAAE,CAAA6J,iBAAA,CAoHXyB,UAAU,EAAc,CAAC;IACxG5H,IAAI,EAAEvD,QAAQ;IACdsD,IAAI,EAAE,CAAC;MACCuI,OAAO,EAAE,CAACjK,YAAY,CAAC;MACvB6J,SAAS,EAAE,CACP1I,eAAe,EACf;QACI2I,OAAO,EAAE1K,iBAAiB;QAC1B2K,QAAQ,EAAEhC,yBAAyB;QACnCiC,KAAK,EAAE;MACX,CAAC;IAET,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,qBAAqB,CAAC;EACxB;IAAS,IAAI,CAAC3C,IAAI,YAAA4C,8BAAA1C,CAAA;MAAA,YAAAA,CAAA,IAAwFyC,qBAAqB;IAAA,CAAkD;EAAE;EACnL;IAAS,IAAI,CAACT,IAAI,kBAjJ8ExL,EAAE,CAAAyL,gBAAA;MAAA/H,IAAA,EAiJSuI;IAAqB,EAA0B;EAAE;EAC5J;IAAS,IAAI,CAACP,IAAI,kBAlJ8E1L,EAAE,CAAA2L,gBAAA;MAAAK,OAAA,GAkJ0CV,UAAU;IAAA,EAAI;EAAE;AAChK;AACA;EAAA,QAAA1B,SAAA,oBAAAA,SAAA,KApJoG5J,EAAE,CAAA6J,iBAAA,CAoJXoC,qBAAqB,EAAc,CAAC;IACnHvI,IAAI,EAAEvD,QAAQ;IACdsD,IAAI,EAAE,CAAC;MACCuI,OAAO,EAAE,CAACV,UAAU;IACxB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIa,iBAAiB;AACrB,CAAC,UAAUA,iBAAiB,EAAE;EAC1B;AACJ;AACA;AACA;EACIA,iBAAiB,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EACpF;AACJ;AACA;AACA;EACIA,iBAAiB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EAChE;AACJ;AACA;EACIA,iBAAiB,CAAC,WAAW,CAAC,GAAG,WAAW;EAC5C;AACJ;AACA;AACA;EACIA,iBAAiB,CAAC,YAAY,CAAC,GAAG,YAAY;EAC9C;AACJ;AACA;EACIA,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;EAC1D;AACJ;AACA;EACIA,iBAAiB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAC9D;AACJ;AACA;EACIA,iBAAiB,CAAC,aAAa,CAAC,GAAG,aAAa;EAChD;AACJ;AACA;EACIA,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO;EACpC;AACJ;AACA;AACA;AACA;EACIA,iBAAiB,CAAC,cAAc,CAAC,GAAG,cAAc;EAClD;AACJ;AACA;EACIA,iBAAiB,CAAC,cAAc,CAAC,GAAG,cAAc;AACtD,CAAC,EAAEA,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAI3I,IAAI,IAAKA,IAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4I,oBAAoB,GAAItC,QAAQ,IAAK;EACvC,MAAMuC,cAAc,GAAGlM,MAAM,CAAC;IAC1BsD,IAAI,EAAEyI,iBAAiB,CAACI;EAC5B,CAAC,CAAC;EACF,IAAI,CAACxC,QAAQ,EAAE;IACXuC,cAAc,CAACnD,GAAG,CAAC;MACfzF,IAAI,EAAEyI,iBAAiB,CAACK;IAC5B,CAAC,CAAC;IACF,OAAOF,cAAc;EACzB;EACAvC,QAAQ,CAACvF,OAAO,GAAI7B,aAAa,IAAK;IAClC2J,cAAc,CAACnD,GAAG,CAAC;MACfzF,IAAI,EAAEyI,iBAAiB,CAACM,KAAK;MAC7BhJ,IAAI,EAAEd;IACV,CAAC,CAAC;EACN,CAAC;EACDoH,QAAQ,CAACzG,WAAW,GAAIC,SAAS,IAAK;IAClC+I,cAAc,CAACnD,GAAG,CAAC;MACfzF,IAAI,EAAEyI,iBAAiB,CAACO,SAAS;MACjCjJ,IAAI,EAAEF;IACV,CAAC,CAAC;EACN,CAAC;EACDwG,QAAQ,CAACnG,YAAY,GAAG,MAAM;IAC1B0I,cAAc,CAACnD,GAAG,CAAC;MACfzF,IAAI,EAAEyI,iBAAiB,CAACQ;IAC5B,CAAC,CAAC;EACN,CAAC;EACD5C,QAAQ,CAACzF,cAAc,GAAG,CAACsI,MAAM,EAAExF,MAAM,KAAK;IAC1CkF,cAAc,CAACnD,GAAG,CAAC;MACfzF,IAAI,EAAEyI,iBAAiB,CAACU,YAAY;MACpCpJ,IAAI,EAAE;QAAEmJ,MAAM;QAAExF;MAAO;IAC3B,CAAC,CAAC;EACN,CAAC;EACD2C,QAAQ,CAAC/F,kBAAkB,GAAG,MAAM;IAChCsI,cAAc,CAACnD,GAAG,CAAC;MACfzF,IAAI,EAAEyI,iBAAiB,CAACW;IAC5B,CAAC,CAAC;EACN,CAAC;EACD/C,QAAQ,CAACjG,oBAAoB,GAAG,MAAM;IAClCwI,cAAc,CAACnD,GAAG,CAAC;MACfzF,IAAI,EAAEyI,iBAAiB,CAACY;IAC5B,CAAC,CAAC;EACN,CAAC;EACDhD,QAAQ,CAAC7F,aAAa,GAAG,MAAM;IAC3BoI,cAAc,CAACnD,GAAG,CAAC;MACfzF,IAAI,EAAEyI,iBAAiB,CAACa;IAC5B,CAAC,CAAC;EACN,CAAC;EACDjD,QAAQ,CAAC3F,cAAc,GAAG,MAAM;IAC5BkI,cAAc,CAACnD,GAAG,CAAC;MACfzF,IAAI,EAAEyI,iBAAiB,CAACc;IAC5B,CAAC,CAAC;EACN,CAAC;EACD,OAAOX,cAAc;AACzB,CAAC;AACD;AACA;AACA;AACA,MAAMY,qBAAqB,GAAG,IAAI7M,cAAc,CAAC,wBAAwB,CAAC;;AAE1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8M,iBAAiB,CAAC;EACpBhL,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiL,WAAW,GAAGlN,MAAM,CAACI,WAAW,CAAC;IACtC,IAAI,CAAC+M,aAAa,GAAGnN,MAAM,CAACK,gBAAgB,CAAC;IAC7C,IAAI,CAACwJ,QAAQ,GAAG7J,MAAM,CAAC4B,QAAQ,CAAC;IAChC;AACR;AACA;IACQ,IAAI,CAACe,KAAK,GAAG,EAAE;IACf;AACR;AACA;IACQ,IAAI,CAACyK,UAAU,GAAG,KAAK;IACvB,IAAI,CAACD,aAAa,CAACE,KAAK,CAAC,CAAC;IAC1B,MAAMjB,cAAc,GAAGpM,MAAM,CAACgN,qBAAqB,CAAC;IACpD1M,MAAM,CAAC,MAAM;MACT,MAAMgN,aAAa,GAAGlB,cAAc,CAAC,CAAC;MACtC,IAAIkB,aAAa,CAAC9J,IAAI,KAAKyI,iBAAiB,CAACM,KAAK,EAAE;QAChD;MACJ;MACA,MAAM9J,aAAa,GAAGyJ,aAAa,CAACoB,aAAa,CAAC/J,IAAI,CAAC;MACvD,IAAId,aAAa,EAAE;QACf,IAAI,CAAC8K,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACAA,MAAMA,CAAA,EAAG;IACL,MAAMC,SAAS,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACvC,IAAID,SAAS,EAAE;MACX,IAAI,CAACL,aAAa,CAACO,kBAAkB,CAAC,IAAI,CAACR,WAAW,CAAC;IAC3D,CAAC,MACI;MACD,IAAI,CAACC,aAAa,CAACE,KAAK,CAAC,CAAC;IAC9B;EACJ;EACA;AACJ;AACA;AACA;EACII,cAAcA,CAAA,EAAG;IACb,MAAMjG,eAAe,GAAG,IAAI,CAAC7E,KAAK,CAACgL,IAAI,CAAEtG,IAAI,IAAK,IAAI,CAACwC,QAAQ,CAACrC,eAAe,CAACH,IAAI,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC;IACrG,MAAMG,YAAY,GAAG,IAAI,CAAC2F,UAAU,GAAG,IAAI,CAACzK,KAAK,CAACgL,IAAI,CAAEtG,IAAI,IAAK,IAAI,CAACwC,QAAQ,CAACpC,YAAY,CAACJ,IAAI,CAAC,CAAC,GAAG,KAAK;IAC1G,OAAOG,eAAe,IAAIC,YAAY;EAC1C;EACA;IAAS,IAAI,CAAC2B,IAAI,YAAAwE,0BAAAtE,CAAA;MAAA,YAAAA,CAAA,IAAwF2D,iBAAiB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACY,IAAI,kBAzZ8E/N,EAAE,CAAAgO,iBAAA;MAAAtK,IAAA,EAyZJyJ,iBAAiB;MAAAc,SAAA;MAAAC,MAAA;QAAArL,KAAA;QAAA2E,QAAA;QAAA8F,UAAA;MAAA;MAAAa,UAAA;IAAA,EAA6M;EAAE;AAClU;AACA;EAAA,QAAAvE,SAAA,oBAAAA,SAAA,KA3ZoG5J,EAAE,CAAA6J,iBAAA,CA2ZXsD,iBAAiB,EAAc,CAAC;IAC/GzJ,IAAI,EAAEhD,SAAS;IACf+C,IAAI,EAAE,CAAC;MACC2K,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEvL,KAAK,EAAE,CAAC;MAChDa,IAAI,EAAEjD,KAAK;MACXgD,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE+D,QAAQ,EAAE,CAAC;MACX9D,IAAI,EAAEjD,KAAK;MACXgD,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE6J,UAAU,EAAE,CAAC;MACb5J,IAAI,EAAEjD,KAAK;MACXgD,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4K,mBAAmB,CAAC;EACtBlM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmM,MAAM,GAAGpO,MAAM,CAACS,MAAM,CAAC;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAAC4N,YAAY,GAAGnO,MAAM,CAACoO,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IACtC;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAItN,OAAO,CAAC,CAAC;IAC7B;AACR;AACA;IACQ,IAAI,CAACuN,kBAAkB,GAAG/N,QAAQ,CAAC,MAAM,IAAI,CAAC2N,YAAY,CAAC,CAAC,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACIK,eAAeA,CAAA,EAAG;IACd,MAAMC,SAAS,GAAG7M,iBAAiB,CAAC9B,MAAM,CAACW,WAAW,CAAC,CAAC;IACxD,IAAI,CAACgO,SAAS,EAAE;MACZ;IACJ;IACA,IAAI,CAACP,MAAM,CAACQ,iBAAiB,CAAC,MAAM;MAChC,MAAMC,MAAM,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC;MACxEA,MAAM,CAAC/G,OAAO,CAAEgH,KAAK,IAAK;QACtBxN,SAAS,CAACyN,MAAM,EAAED,KAAK,CAAC,CACnB9F,IAAI,CAACtH,YAAY,CAAC,GAAG,CAAC,EAAEC,SAAS,CAAC,IAAI,CAAC6M,QAAQ,CAAC,CAAC,CACjDQ,SAAS,CAAC,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIA,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACb,MAAM,CAACc,GAAG,CAAC,MAAM;MAClB,IAAI,CAACb,YAAY,CAACpF,GAAG,CAACqF,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI,IAAIY,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACd,YAAY,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIe,QAAQA,CAACC,OAAO,EAAE;IACd,OAAOf,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACY,gBAAgB,GAAGE,OAAO;EACvD;EACA;AACJ;AACA;AACA;EACIC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACd,QAAQ,CAAClL,IAAI,CAAC,CAAC;IACpB,IAAI,CAACkL,QAAQ,CAACe,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACnG,IAAI,YAAAoG,4BAAAlG,CAAA;MAAA,YAAAA,CAAA,IAAwF6E,mBAAmB;IAAA,CAAoD;EAAE;EACnL;IAAS,IAAI,CAAC5E,KAAK,kBA1gB6EzJ,EAAE,CAAA0J,kBAAA;MAAAd,KAAA,EA0gBYyF,mBAAmB;MAAA1E,OAAA,EAAnB0E,mBAAmB,CAAA/E;IAAA,EAAG;EAAE;AAC1I;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KA5gBoG5J,EAAE,CAAA6J,iBAAA,CA4gBXwE,mBAAmB,EAAc,CAAC;IACjH3K,IAAI,EAAEzD;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0P,uBAAuB,CAAC;EAC1BxN,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4H,QAAQ,GAAG7J,MAAM,CAAC4B,QAAQ,CAAC;IAChC,IAAI,CAAC8N,YAAY,GAAG1P,MAAM,CAACmO,mBAAmB,CAAC;IAC/C,IAAI,CAAC7H,OAAO,GAAG,IAAI,CAACqJ,cAAc;IAClC,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,MAAMxD,cAAc,GAAGpM,MAAM,CAACgN,qBAAqB,CAAC;IACpD1M,MAAM,CAAC,MAAM;MACT,MAAMgN,aAAa,GAAGlB,cAAc,CAAC,CAAC;MACtC,IAAIkB,aAAa,CAAC9J,IAAI,KAAKyI,iBAAiB,CAACc,YAAY,EAAE;QACvD,IAAI,CAAC8C,wBAAwB,CAAC,CAAC;MACnC;IACJ,CAAC,CAAC;EACN;EACA,IAAIF,cAAcA,CAAA,EAAG;IACjB,OAAO;MACHG,cAAc,EAAE,MAAM;MACtBC,mBAAmB,EAAE;IACzB,CAAC;EACL;EACAC,0BAA0BA,CAAA,EAAG;IACzB,QAAQ,IAAI,CAAC1J,OAAO,CAACyJ,mBAAmB;MACpC,KAAK,OAAO;QACR,IAAI,CAAClG,QAAQ,CAACnD,KAAK,CAAC,CAAC,CAACuJ,KAAK,CAAEnN,KAAK,IAAKoN,OAAO,CAACpN,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC,CAAC;QAChG;MACJ,KAAK,QAAQ;QACT,IAAI,CAAC+G,QAAQ,CAACjD,MAAM,CAAC,CAAC,CAACqJ,KAAK,CAAEnN,KAAK,IAAKoN,OAAO,CAACpN,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC,CAAC;QAClG;MACJ;QACI;IACR;EACJ;EACA+M,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAAC,IAAI,CAACD,WAAW,IAAI,CAAC,IAAI,CAAC/F,QAAQ,CAACpH,aAAa,EAAE;MACnD;IACJ;IACA,IAAI,IAAI,CAACiN,YAAY,CAACN,QAAQ,CAAC,IAAI,CAAC9I,OAAO,CAACwJ,cAAc,CAAC,EAAE;MACzD,IAAI,CAACjG,QAAQ,CAACzB,WAAW,CAAC,CAAC,CAAC6H,KAAK,CAAC,MAAM,IAAI,CAACD,0BAA0B,CAAC,CAAC,CAAC;IAC9E,CAAC,MACI;MACD,IAAI,CAACA,0BAA0B,CAAC,CAAC;IACrC;EACJ;EACAG,KAAKA,CAAC7J,OAAO,EAAE;IACX,IAAI,CAACA,OAAO,GAAG;MAAE,GAAG,IAAI,CAACqJ,cAAc;MAAE,GAAGrJ;IAAQ,CAAC;IACrD,IAAI,CAACsJ,WAAW,GAAG,IAAI;IACvB,IAAI,CAACF,YAAY,CAAChB,eAAe,CAAC,CAAC;EACvC;EACA;IAAS,IAAI,CAACtF,IAAI,YAAAgH,gCAAA9G,CAAA;MAAA,YAAAA,CAAA,IAAwFmG,uBAAuB;IAAA,CAAoD;EAAE;EACvL;IAAS,IAAI,CAAClG,KAAK,kBAnlB6EzJ,EAAE,CAAA0J,kBAAA;MAAAd,KAAA,EAmlBY+G,uBAAuB;MAAAhG,OAAA,EAAvBgG,uBAAuB,CAAArG;IAAA,EAAG;EAAE;AAC9I;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KArlBoG5J,EAAE,CAAA6J,iBAAA,CAqlBX8F,uBAAuB,EAAc,CAAC;IACrHjM,IAAI,EAAEzD;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsQ,oBAAoBA,CAAC/J,OAAO,EAAE;EACnC,OAAO;IACHgK,SAAS,EAAEA,CAAA,KAAM;MACb,MAAMC,uBAAuB,GAAGvQ,MAAM,CAACyP,uBAAuB,CAAC;MAC/Dc,uBAAuB,CAACJ,KAAK,CAAC7J,OAAO,CAAC;IAC1C;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkK,gBAAgB,GAAGA,CAAC7I,cAAc,GAAG,CAAC,CAAC,KAAK;EAC9C,OAAOC,MAAM,CAAC6I,OAAO,CAAC9I,cAAc,CAAC,CAAC+I,MAAM,CAAC,CAAC/N,KAAK,EAAE,CAACoF,GAAG,EAAE4I,KAAK,CAAC,KAAK;IAClEhO,KAAK,CAACoF,GAAG,CAAC,GAAG4I,KAAK,CAAChO,KAAK;IACxB,OAAOA,KAAK;EAChB,CAAC,EAAE,CAAC,CAAC,CAAC;AACV,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiO,eAAe,GAAI/N,eAAe,IAAK;EACzC,OAAQ,CAACS,IAAI,EAAEhB,KAAK,KAAK;IACrB,MAAMuH,QAAQ,GAAG7J,MAAM,CAAC4B,QAAQ,CAAC;IACjC,MAAMa,aAAa,GAAGoH,QAAQ,EAAEpH,aAAa,IAAI,KAAK;IACtD,MAAMoO,YAAY,GAAG;MACjBC,aAAa,EAAEN,gBAAgB,CAAC3G,QAAQ,EAAElC,cAAc,CAAC;MACzDD,UAAU,EAAEmC,QAAQ,EAAE5B,WAAW,EAAEtF,KAAK,IAAI;IAChD,CAAC;IACD,MAAMoO,QAAQ,GAAG;MAAEtO,aAAa;MAAEoH,QAAQ;MAAEgH;IAAa,CAAC;IAC1D,OAAOhO,eAAe,CAACS,IAAI,EAAEhB,KAAK,EAAEyO,QAAQ,CAAC;EACjD,CAAC;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,QAAQ;AAC9B;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG,eAAe;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAIP,KAAK,KAAM;EAC3C,GAAGA,KAAK;EACRrL,YAAY,EAAEqL,KAAK,CAACrL,YAAY,IAAI0L,aAAa;EACjD3L,uBAAuB,EAAEsL,KAAK,CAACtL,uBAAuB,IAAI4L,yBAAyB;EACnFvL,iBAAiB,EAAEiL,KAAK,CAACjL,iBAAiB,KAAK,MAAM,IAAI;AAC7D,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoE,wBAAwB;EAAA,IAAAqH,IAAA,GAAA3O,iBAAA,CAAG,WAAOuH,GAAG,EAAEF,QAAQ,EAAE;IAAEnE,iBAAiB,GAAIoF,CAAC,IAAK;EAAK,CAAC,EAAK;IAC3F,IAAIpF,iBAAiB,CAACqE,GAAG,CAAC,EAAE;MACxB,aAAaF,QAAQ,CAACzB,WAAW,CAAC,CAAC,CAAC6H,KAAK,CAAC,MAAM,KAAK,CAAC;IAC1D;IACA,OAAO,IAAI;EACf,CAAC;EAAA,gBALKnG,wBAAwBA,CAAAsH,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAH,IAAA,CAAAI,KAAA,OAAAC,SAAA;EAAA;AAAA,GAK7B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAGA,CAAC1H,GAAG,EAAEzG,IAAI,EAAEuG,QAAQ,EAAE6H,SAAS,KAAK;EAC/D,MAAM;IAAEpM,YAAY,GAAG0L,aAAa;IAAE3L,uBAAuB,GAAG4L;EAA0B,CAAC,GAAGS,SAAS;EACvG,MAAMC,aAAa,GAAG5H,GAAG,CAACmB,KAAK,CAAC;IAC5B0G,UAAU,EAAE;MACR,CAACvM,uBAAuB,GAAG,GAAGC,YAAY,IAAIuE,QAAQ,CAACnB,KAAK;IAChE;EACJ,CAAC,CAAC;EACF,OAAOpF,IAAI,CAACqO,aAAa,CAAC;AAC9B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,sCAAsC,GAAG,IAAI1R,cAAc,CAAC,kEAAkE,CAAC;AACrI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2R,4BAA4B,GAAGA,CAAC/H,GAAG,EAAEzG,IAAI,KAAK;EAChD,MAAMyO,UAAU,GAAG/R,MAAM,CAAC6R,sCAAsC,CAAC,IAAI,EAAE;EACvE,MAAMhI,QAAQ,GAAG7J,MAAM,CAAC4B,QAAQ,CAAC;EACjC,OAAOT,IAAI,CAAC6Q,OAAO,CAACC,GAAG,CAACF,UAAU,CAACtQ,GAAG;IAAA,IAAAyQ,KAAA,GAAA1P,iBAAA,CAAC,WAAOkP,SAAS;MAAA,aAAWA,SAAS,CAACjM,cAAc,CAACsE,GAAG,EAAEzG,IAAI,EAAEuG,QAAQ,CAAC;IAAA;IAAA,iBAAAsI,GAAA;MAAA,OAAAD,KAAA,CAAAX,KAAA,OAAAC,SAAA;IAAA;EAAA,IAAC,CAAC,CAAC,CAACxI,IAAI,CAACxH,UAAU,CAAE4Q,mBAAmB,IAAK;IACxJ,MAAMC,sBAAsB,GAAGD,mBAAmB,CAACvH,SAAS,CAACyH,OAAO,CAAC;IACrE,MAAMC,iBAAiB,GAAGR,UAAU,CAACM,sBAAsB,CAAC;IAC5D,IAAI,CAACE,iBAAiB,EAAE;MACpB,OAAOjP,IAAI,CAACyG,GAAG,CAAC;IACpB;IACA,OAAO5I,IAAI,CAAC2I,wBAAwB,CAACC,GAAG,EAAEF,QAAQ,EAAE0I,iBAAiB,CAAC,CAAC,CAACvJ,IAAI,CAACxH,UAAU,CAAC,MAAMqI,QAAQ,CAACpH,aAAa,GAAGgP,sBAAsB,CAAC1H,GAAG,EAAEzG,IAAI,EAAEuG,QAAQ,EAAE0I,iBAAiB,CAAC,GAAGjP,IAAI,CAACyG,GAAG,CAAC,CAAC,CAAC;EACvM,CAAC,CAAC,CAAC;AACP,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyI,uCAAuC,GAAG,IAAIrS,cAAc,CAAC,gFAAgF,CAAC;AACpJ,MAAMsS,qBAAqB,GAAGA,CAAC;EAAEvI,MAAM;EAAElF;AAAI,CAAC,EAAE;EAAEH,UAAU;EAAEE,WAAW,GAAG;AAAG,CAAC,KAAK;EACjF,MAAM2N,cAAc,GAAG3N,WAAW,CAACqF,MAAM,KAAK,CAAC,IAAIrF,WAAW,CAACsF,IAAI,CAAC,CAAC,CAACC,OAAO,CAACJ,MAAM,CAACK,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACxG,MAAMC,OAAO,GAAG3F,UAAU,CAAC4F,IAAI,CAACzF,GAAG,CAAC;EACpC,OAAO0N,cAAc,IAAIlI,OAAO;AACpC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmI,6BAA6B,GAAGA,CAAC5I,GAAG,EAAEzG,IAAI,KAAK;EACjD,MAAMyO,UAAU,GAAG/R,MAAM,CAACwS,uCAAuC,CAAC,IAAI,EAAE;EACxE,MAAMD,iBAAiB,GAAGR,UAAU,CAACa,IAAI,CAAElB,SAAS,IAAKe,qBAAqB,CAAC1I,GAAG,EAAE2H,SAAS,CAAC,CAAC;EAC/F,IAAI,CAACa,iBAAiB,EAAE;IACpB,OAAOjP,IAAI,CAACyG,GAAG,CAAC;EACpB;EACA,MAAMF,QAAQ,GAAG7J,MAAM,CAAC4B,QAAQ,CAAC;EACjC,OAAOT,IAAI,CAAC2I,wBAAwB,CAACC,GAAG,EAAEF,QAAQ,EAAE0I,iBAAiB,CAAC,CAAC,CAACvJ,IAAI,CAACxH,UAAU,CAAC,MAAMqI,QAAQ,CAACpH,aAAa,GAAGgP,sBAAsB,CAAC1H,GAAG,EAAEzG,IAAI,EAAEuG,QAAQ,EAAE0I,iBAAiB,CAAC,GAAGjP,IAAI,CAACyG,GAAG,CAAC,CAAC,CAAC;AACvM,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8I,+BAA+B,GAAGA,CAAChJ,QAAQ,EAAEvD,OAAO,KAAK;EAC3D,MAAM;IAAEf,WAAW;IAAEuN,QAAQ,GAAG;EAAG,CAAC,GAAGxM,OAAO;EAC9C,IAAI,CAACf,WAAW,EAAE;IACd,OAAO,EAAE;EACb;EACA,OAAO3E,qBAAqB,eAAA4B,iBAAA,CAAC,aAAY;IACrC,MAAMuQ,QAAQ,GAAG/S,MAAM,CAACa,mBAAmB,CAAC;IAC5CC,qBAAqB,CAACiS,QAAQ,EAAE,MAAMD,QAAQ,CAAChL,OAAO,CAAEkL,OAAO,IAAKA,OAAO,CAAC1C,SAAS,CAAC,CAAC,CAAC,CAAC;IACzF,MAAMzG,QAAQ,CAACxD,IAAI,CAACd,WAAW,CAAC,CAAC0K,KAAK,CAAEnN,KAAK,IAAKoN,OAAO,CAACpN,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC,CAAC;EAC7G,CAAC,EAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmQ,eAAeA,CAAC3M,OAAO,EAAE;EAC9B,MAAMuD,QAAQ,GAAG,IAAIjI,QAAQ,CAAC0E,OAAO,CAACE,MAAM,CAAC;EAC7C,MAAMkF,SAAS,GAAGpF,OAAO,CAACoF,SAAS,IAAI,EAAE;EACzC,MAAMU,cAAc,GAAGD,oBAAoB,CAACtC,QAAQ,CAAC;EACrD,OAAO9I,wBAAwB,CAAC,CAC5B;IACI4K,OAAO,EAAEqB,qBAAqB;IAC9BkG,QAAQ,EAAE9G;EACd,CAAC,EACD;IACIT,OAAO,EAAE/J,QAAQ;IACjBsR,QAAQ,EAAErJ;EACd,CAAC,EACD,GAAG6B,SAAS,EACZmH,+BAA+B,CAAChJ,QAAQ,EAAEvD,OAAO,CAAC,CACrD,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASmJ,uBAAuB,EAAEoC,sCAAsC,EAAEzG,UAAU,EAAE6B,iBAAiB,EAAEuF,uCAAuC,EAAExF,qBAAqB,EAAEjB,qBAAqB,EAAE/J,iBAAiB,EAAE4H,yBAAyB,EAAEqC,iBAAiB,EAAElK,uBAAuB,EAAEiB,eAAe,EAAEmL,mBAAmB,EAAEsD,sBAAsB,EAAE3H,wBAAwB,EAAE8G,eAAe,EAAEM,0BAA0B,EAAE/E,oBAAoB,EAAE2F,4BAA4B,EAAEa,6BAA6B,EAAEM,eAAe,EAAE/G,aAAa,EAAEmE,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}