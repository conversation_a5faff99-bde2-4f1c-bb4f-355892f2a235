{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/**\n * Parallel coordinate system creater.\n */\nimport Parallel from './Parallel.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nfunction createParallelCoordSys(ecModel, api) {\n  var coordSysList = [];\n  ecModel.eachComponent('parallel', function (parallelModel, idx) {\n    var coordSys = new Parallel(parallelModel, ecModel, api);\n    coordSys.name = 'parallel_' + idx;\n    coordSys.resize(parallelModel, api);\n    parallelModel.coordinateSystem = coordSys;\n    coordSys.model = parallelModel;\n    coordSysList.push(coordSys);\n  });\n  // Inject the coordinateSystems into seriesModel\n  ecModel.eachSeries(function (seriesModel) {\n    if (seriesModel.get('coordinateSystem') === 'parallel') {\n      var parallelModel = seriesModel.getReferringComponents('parallel', SINGLE_REFERRING).models[0];\n      seriesModel.coordinateSystem = parallelModel.coordinateSystem;\n    }\n  });\n  return coordSysList;\n}\nvar parallelCoordSysCreator = {\n  create: createParallelCoordSys\n};\nexport default parallelCoordSysCreator;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "SINGLE_REFERRING", "createParallelCoordSys", "ecModel", "api", "coordSysList", "eachComponent", "parallelModel", "idx", "coordSys", "name", "resize", "coordinateSystem", "model", "push", "eachSeries", "seriesModel", "get", "getReferringComponents", "models", "parallelCoordSysCreator", "create"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/coord/parallel/parallelCreator.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/**\n * Parallel coordinate system creater.\n */\nimport Parallel from './Parallel.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nfunction createParallelCoordSys(ecModel, api) {\n  var coordSysList = [];\n  ecModel.eachComponent('parallel', function (parallelModel, idx) {\n    var coordSys = new Parallel(parallelModel, ecModel, api);\n    coordSys.name = 'parallel_' + idx;\n    coordSys.resize(parallelModel, api);\n    parallelModel.coordinateSystem = coordSys;\n    coordSys.model = parallelModel;\n    coordSysList.push(coordSys);\n  });\n  // Inject the coordinateSystems into seriesModel\n  ecModel.eachSeries(function (seriesModel) {\n    if (seriesModel.get('coordinateSystem') === 'parallel') {\n      var parallelModel = seriesModel.getReferringComponents('parallel', SINGLE_REFERRING).models[0];\n      seriesModel.coordinateSystem = parallelModel.coordinateSystem;\n    }\n  });\n  return coordSysList;\n}\nvar parallelCoordSysCreator = {\n  create: createParallelCoordSys\n};\nexport default parallelCoordSysCreator;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,QAAQ,MAAM,eAAe;AACpC,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,sBAAsBA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAC5C,IAAIC,YAAY,GAAG,EAAE;EACrBF,OAAO,CAACG,aAAa,CAAC,UAAU,EAAE,UAAUC,aAAa,EAAEC,GAAG,EAAE;IAC9D,IAAIC,QAAQ,GAAG,IAAIT,QAAQ,CAACO,aAAa,EAAEJ,OAAO,EAAEC,GAAG,CAAC;IACxDK,QAAQ,CAACC,IAAI,GAAG,WAAW,GAAGF,GAAG;IACjCC,QAAQ,CAACE,MAAM,CAACJ,aAAa,EAAEH,GAAG,CAAC;IACnCG,aAAa,CAACK,gBAAgB,GAAGH,QAAQ;IACzCA,QAAQ,CAACI,KAAK,GAAGN,aAAa;IAC9BF,YAAY,CAACS,IAAI,CAACL,QAAQ,CAAC;EAC7B,CAAC,CAAC;EACF;EACAN,OAAO,CAACY,UAAU,CAAC,UAAUC,WAAW,EAAE;IACxC,IAAIA,WAAW,CAACC,GAAG,CAAC,kBAAkB,CAAC,KAAK,UAAU,EAAE;MACtD,IAAIV,aAAa,GAAGS,WAAW,CAACE,sBAAsB,CAAC,UAAU,EAAEjB,gBAAgB,CAAC,CAACkB,MAAM,CAAC,CAAC,CAAC;MAC9FH,WAAW,CAACJ,gBAAgB,GAAGL,aAAa,CAACK,gBAAgB;IAC/D;EACF,CAAC,CAAC;EACF,OAAOP,YAAY;AACrB;AACA,IAAIe,uBAAuB,GAAG;EAC5BC,MAAM,EAAEnB;AACV,CAAC;AACD,eAAekB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}