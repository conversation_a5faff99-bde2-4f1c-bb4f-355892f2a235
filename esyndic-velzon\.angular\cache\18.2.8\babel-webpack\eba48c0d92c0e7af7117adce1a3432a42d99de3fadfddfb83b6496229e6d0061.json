{"ast": null, "code": "/**\n * A faster alternative to `Function#apply`, this function invokes `func`\n * with the `this` binding of `thisArg` and the arguments of `args`.\n *\n * @private\n * @param {Function} func The function to invoke.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} args The arguments to invoke `func` with.\n * @returns {*} Returns the result of `func`.\n */\nfunction apply(func, thisArg, args) {\n  switch (args.length) {\n    case 0:\n      return func.call(thisArg);\n    case 1:\n      return func.call(thisArg, args[0]);\n    case 2:\n      return func.call(thisArg, args[0], args[1]);\n    case 3:\n      return func.call(thisArg, args[0], args[1], args[2]);\n  }\n  return func.apply(thisArg, args);\n}\nexport default apply;", "map": {"version": 3, "names": ["apply", "func", "thisArg", "args", "length", "call"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_apply.js"], "sourcesContent": ["/**\n * A faster alternative to `Function#apply`, this function invokes `func`\n * with the `this` binding of `thisArg` and the arguments of `args`.\n *\n * @private\n * @param {Function} func The function to invoke.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} args The arguments to invoke `func` with.\n * @returns {*} Returns the result of `func`.\n */\nfunction apply(func, thisArg, args) {\n  switch (args.length) {\n    case 0: return func.call(thisArg);\n    case 1: return func.call(thisArg, args[0]);\n    case 2: return func.call(thisArg, args[0], args[1]);\n    case 3: return func.call(thisArg, args[0], args[1], args[2]);\n  }\n  return func.apply(thisArg, args);\n}\n\nexport default apply;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAKA,CAACC,IAAI,EAAEC,OAAO,EAAEC,IAAI,EAAE;EAClC,QAAQA,IAAI,CAACC,MAAM;IACjB,KAAK,CAAC;MAAE,OAAOH,IAAI,CAACI,IAAI,CAACH,OAAO,CAAC;IACjC,KAAK,CAAC;MAAE,OAAOD,IAAI,CAACI,IAAI,CAACH,OAAO,EAAEC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1C,KAAK,CAAC;MAAE,OAAOF,IAAI,CAACI,IAAI,CAACH,OAAO,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;IACnD,KAAK,CAAC;MAAE,OAAOF,IAAI,CAACI,IAAI,CAACH,OAAO,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;EAC9D;EACA,OAAOF,IAAI,CAACD,KAAK,CAACE,OAAO,EAAEC,IAAI,CAAC;AAClC;AAEA,eAAeH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}