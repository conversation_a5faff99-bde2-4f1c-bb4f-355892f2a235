/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ConnectableObservable } from 'rxjs';
export class DataSource {
}
/** Checks whether an object is a data source. */
export function isDataSource(value) {
    // Check if the value is a DataSource by observing if it has a connect function. Cannot
    // be checked as an `instanceof DataSource` since people could create their own sources
    // that match the interface, but don't extend DataSource. We also can't use `isObservable`
    // here, because of some internal apps.
    return value && typeof value.connect === 'function' && !(value instanceof ConnectableObservable);
}
//# sourceMappingURL=data:application/json;base64,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