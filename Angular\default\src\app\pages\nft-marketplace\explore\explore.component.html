<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Explore Now" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header border-0">
                <div class="d-flex align-items-center">
                    <h5 class="card-title mb-0 flex-grow-1">Explore Product</h5>
                    <div>
                        <a class="btn btn-success" data-bs-toggle="collapse" href="javascript:void(0);" (click)="collapse.toggle()" [attr.aria-expanded]="!isCollapsed"><i class="ri-filter-2-line align-bottom"></i> Filters</a>
                    </div>
                </div>
                <div class="collaps show" id="collapseExample" #collapse="ngbCollapse" [(ngbCollapse)]="isCollapsed">
                    <div class="row row-cols-xxl-5 row-cols-lg-3 row-cols-md-2 row-cols-1 mt-3 g-3">
                        <div class="col">
                            <h6 class="text-uppercase fs-12 mb-2">Search</h6>
                            <input type="text" class="form-control search" placeholder="Search product name" autocomplete="off" [(ngModel)]="searchTerm" (ngModelChange)="performSearch()">
                        </div>
                        <div class="col">
                            <h6 class="text-uppercase fs-12 mb-2">Select Category</h6>
                            <select class="form-control" data-choices name="select-category" data-choices-search-false id="select-category" [(ngModel)]="category" (ngModelChange)="categoryFilter()">
                                <option value="">Select Category</option>
                                <option value="Artwork">Artwork</option>
                                <option value="3d Style">3d Style</option>
                                <option value="Photography">Photography</option>
                                <option value="Collectibles">Collectibles</option>
                                <option value="Crypto Card">Crypto Card</option>
                                <option value="Games">Games</option>
                                <option value="Music">Music</option>
                            </select>
                        </div>
                        <div class="col">
                            <h6 class="text-uppercase fs-12 mb-2">File Type</h6>
                            <select class="form-control" data-choices name="file-type" data-choices-search-false id="file-type" [(ngModel)]="type" (ngModelChange)="typeFilter()">
                                <option value="">File Type</option>
                                <option value="jpg">Images</option>
                                <option value="mp4">Video</option>
                                <option value="mp3">Audio</option>
                                <option value="gif">Gif</option>
                            </select>
                        </div>
                        <div class="col">
                            <h6 class="text-uppercase fs-12 mb-2">Sales Type</h6>
                            <select class="form-control" data-choices name="all-sales-type" data-choices-search-false id="all-sales-type" [(ngModel)]="sale_type" (ngModelChange)="saleFilter()">
                                <option value="">All Sales Type</option>
                                <option value="On Auction">On Auction</option>
                                <option value="Has Offers">Has Offers</option>
                            </select>
                        </div>
                        <div class="col">
                            <h6 class="text-uppercase fs-12 mb-1">Price</h6>
                            <ngx-slider [(value)]="value" [(highValue)]="highValue" [options]="options" class="slider" id="range-product-price"></ngx-slider>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-12">
        <div class="d-flex align-items-center mb-4">
            <div class="flex-grow-1">
                <p class="text-muted fs-14 mb-0">Result: 8745</p>
            </div>
            <div class="flex-shrink-0">
                <div class="dropdown" ngbDropdown>
                    <a class="text-muted fs-14 dropdown-toggle" href="javascript:void(0);" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle>
                        All View
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink" ngbDropdownMenu>
                        <li><a class="dropdown-item" href="javascript:void(0);">Action</a></li>
                        <li><a class="dropdown-item" href="javascript:void(0);">Another action</a></li>
                        <li><a class="dropdown-item" href="javascript:void(0);">Something else here</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- end row -->

<div class="row row-cols-xxl-5 row-cols-xl-4 row-cols-lg-3 row-cols-md-2 row-cols-1" id="explorecard-list">
    @for (data of basicData; track $index) {
    <div class="col list-element">
        <div class="card explore-box card-animate">
            <div class="explore-place-bid-img">
                <input type="hidden" class="form-control" id="1">
                <div class="d-none">undefined</div>
                <img src="{{data.img}}" alt="" class="card-img-top explore-img">
                <div class="bg-overlay"></div>
                <div class="place-bid-btn">
                    <a href="javascript:void(0);" class="btn btn-success"><i class="ri-auction-fill align-bottom me-1"></i> Place Bid</a>
                </div>
            </div>
            <div class="bookmark-icon position-absolute top-0 end-0 p-2">
                <button type="button" class="btn btn-icon heart_icon_{{data.id}}" [ngClass]="{'active': data.isIcon !== true}" data-bs-toggle="button" aria-pressed="true" (click)="activeMenu(data.id)">
                    <i class="mdi mdi-cards-heart fs-16"></i>
                </button>
            </div>
            <div class="card-body">
                <p class="fw-medium mb-0 float-end"><i class="mdi mdi-heart text-danger align-middle"></i>
                    {{data.likes}}k </p>
                <h5 class="mb-1"><a routerLink="/marletplace/item-details">{{data.title}}</a></h5>
                <p class="text-muted mb-0">{{data.category}}</p>
            </div>
            <div class="card-footer border-top border-top-dashed">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 fs-14">
                        <i class="ri-price-tag-3-fill text-warning align-bottom mx-1"></i> Highest: <span class="fw-medium">{{data.highestBid}}</span>
                    </div>
                    <h5 class="flex-shrink-0 fs-14 text-primary mb-0">{{data.price}}</h5>
                </div>
            </div>
        </div>
    </div>
    }
</div>

<div class="text-center mb-3">
    <button class="btn btn-link text-success mt-2" id="loadmore"><i class="mdi mdi-loading mdi-spin fs-20 align-middle me-2"></i> Load More </button>
</div>