{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Scottish Gaelic [gd]\n//! author : <PERSON> : https://github.com/jonashdown\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var months = ['Am Faoilleach', 'An Gearran', 'Am <PERSON>', 'An Giblean', 'An <PERSON>', 'An t-Ògmhios', 'An t-Iuchar', 'An Lùnastal', 'An t-Sultain', 'An Dàmhair', 'An t-Samhain', 'An Dùbhlachd'],\n    monthsShort = ['Faoi', 'Gear', 'Màrt', 'Gibl', 'Cèit', 'Ògmh', 'Iuch', 'Lùn', 'Sult', '<PERSON>àmh', 'Samh', 'Dùbh'],\n    weekdays = ['<PERSON><PERSON><PERSON><PERSON><PERSON>h', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>aoin', '<PERSON><PERSON><PERSON>', 'Disathairne'],\n    weekdaysShort = ['Did', 'Dil', 'Dim', 'Dic', 'Dia', 'Dih', 'Dis'],\n    weekdaysMin = ['Dò', 'Lu', 'Mà', 'Ci', 'Ar', 'Ha', 'Sa'];\n  var gd = moment.defineLocale('gd', {\n    months: months,\n    monthsShort: monthsShort,\n    monthsParseExact: true,\n    weekdays: weekdays,\n    weekdaysShort: weekdaysShort,\n    weekdaysMin: weekdaysMin,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[An-diugh aig] LT',\n      nextDay: '[A-màireach aig] LT',\n      nextWeek: 'dddd [aig] LT',\n      lastDay: '[An-dè aig] LT',\n      lastWeek: 'dddd [seo chaidh] [aig] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'ann an %s',\n      past: 'bho chionn %s',\n      s: 'beagan diogan',\n      ss: '%d diogan',\n      m: 'mionaid',\n      mm: '%d mionaidean',\n      h: 'uair',\n      hh: '%d uairean',\n      d: 'latha',\n      dd: '%d latha',\n      M: 'mìos',\n      MM: '%d mìosan',\n      y: 'bliadhna',\n      yy: '%d bliadhna'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(d|na|mh)/,\n    ordinal: function (number) {\n      var output = number === 1 ? 'd' : number % 10 === 2 ? 'na' : 'mh';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return gd;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "months", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "gd", "defineLocale", "monthsParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "output", "week", "dow", "doy"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/moment/locale/gd.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Scottish Gaelic [gd]\n//! author : <PERSON> : https://github.com/jonashdown\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var months = [\n            'Am Faoilleach',\n            'An Gearran',\n            'Am <PERSON>',\n            'An Giblean',\n            'An <PERSON>an',\n            'An t-Ògmhios',\n            'An t-Iuchar',\n            'An Lùnastal',\n            'An t-Sultain',\n            'An Dàmhair',\n            'An t-Samhain',\n            'An Dùbhlachd',\n        ],\n        monthsShort = [\n            'Faoi',\n            'Gear',\n            'Màrt',\n            'Gibl',\n            'Cèit',\n            'Ògmh',\n            'Iuch',\n            'Lùn',\n            'Sult',\n            '<PERSON>àmh',\n            'Samh',\n            'Dùbh',\n        ],\n        weekdays = [\n            '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON>ao<PERSON>',\n            '<PERSON><PERSON><PERSON>',\n            'Disathairne',\n        ],\n        weekdaysShort = ['Did', 'Dil', 'Dim', 'Dic', 'Dia', 'Dih', 'Dis'],\n        weekdaysMin = ['Dò', 'Lu', 'Mà', 'Ci', 'Ar', 'Ha', 'Sa'];\n\n    var gd = moment.defineLocale('gd', {\n        months: months,\n        monthsShort: monthsShort,\n        monthsParseExact: true,\n        weekdays: weekdays,\n        weekdaysShort: weekdaysShort,\n        weekdaysMin: weekdaysMin,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[An-diugh aig] LT',\n            nextDay: '[A-màireach aig] LT',\n            nextWeek: 'dddd [aig] LT',\n            lastDay: '[An-dè aig] LT',\n            lastWeek: 'dddd [seo chaidh] [aig] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'ann an %s',\n            past: 'bho chionn %s',\n            s: 'beagan diogan',\n            ss: '%d diogan',\n            m: 'mionaid',\n            mm: '%d mionaidean',\n            h: 'uair',\n            hh: '%d uairean',\n            d: 'latha',\n            dd: '%d latha',\n            M: 'mìos',\n            MM: '%d mìosan',\n            y: 'bliadhna',\n            yy: '%d bliadhna',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(d|na|mh)/,\n        ordinal: function (number) {\n            var output = number === 1 ? 'd' : number % 10 === 2 ? 'na' : 'mh';\n            return number + output;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return gd;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,MAAM,GAAG,CACL,eAAe,EACf,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,aAAa,EACb,aAAa,EACb,cAAc,EACd,YAAY,EACZ,cAAc,EACd,cAAc,CACjB;IACDC,WAAW,GAAG,CACV,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;IACDC,QAAQ,GAAG,CACP,aAAa,EACb,SAAS,EACT,SAAS,EACT,WAAW,EACX,WAAW,EACX,UAAU,EACV,aAAa,CAChB;IACDC,aAAa,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACjEC,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAE5D,IAAIC,EAAE,GAAGN,MAAM,CAACO,YAAY,CAAC,IAAI,EAAE;IAC/BN,MAAM,EAAEA,MAAM;IACdC,WAAW,EAAEA,WAAW;IACxBM,gBAAgB,EAAE,IAAI;IACtBL,QAAQ,EAAEA,QAAQ;IAClBC,aAAa,EAAEA,aAAa;IAC5BC,WAAW,EAAEA,WAAW;IACxBI,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,mBAAmB;MAC5BC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,4BAA4B;MACtCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,eAAe;MACrBC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,eAAe;MACnBC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,kBAAkB;IAC1CC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,IAAIC,MAAM,GAAGD,MAAM,KAAK,CAAC,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI;MACjE,OAAOA,MAAM,GAAGC,MAAM;IAC1B,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOtC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}