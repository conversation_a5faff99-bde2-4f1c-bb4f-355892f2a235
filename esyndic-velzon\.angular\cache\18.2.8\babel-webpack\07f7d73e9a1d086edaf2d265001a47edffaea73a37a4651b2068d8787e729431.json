{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { updateCenterAndZoom } from '../../action/roamHelper.js';\nexport function installTreeAction(registers) {\n  registers.registerAction({\n    type: 'treeExpandAndCollapse',\n    event: 'treeExpandAndCollapse',\n    update: 'update'\n  }, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'tree',\n      query: payload\n    }, function (seriesModel) {\n      var dataIndex = payload.dataIndex;\n      var tree = seriesModel.getData().tree;\n      var node = tree.getNodeByDataIndex(dataIndex);\n      node.isExpand = !node.isExpand;\n    });\n  });\n  registers.registerAction({\n    type: 'treeRoam',\n    event: 'treeRoam',\n    // Here we set 'none' instead of 'update', because roam action\n    // just need to update the transform matrix without having to recalculate\n    // the layout. So don't need to go through the whole update process, such\n    // as 'dataPrcocess', 'coordSystemUpdate', 'layout' and so on.\n    update: 'none'\n  }, function (payload, ecModel, api) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'tree',\n      query: payload\n    }, function (seriesModel) {\n      var coordSys = seriesModel.coordinateSystem;\n      var res = updateCenterAndZoom(coordSys, payload, undefined, api);\n      seriesModel.setCenter && seriesModel.setCenter(res.center);\n      seriesModel.setZoom && seriesModel.setZoom(res.zoom);\n    });\n  });\n}", "map": {"version": 3, "names": ["updateCenterAndZoom", "installTreeAction", "registers", "registerAction", "type", "event", "update", "payload", "ecModel", "eachComponent", "mainType", "subType", "query", "seriesModel", "dataIndex", "tree", "getData", "node", "getNodeByDataIndex", "isExpand", "api", "coordSys", "coordinateSystem", "res", "undefined", "setCenter", "center", "setZoom", "zoom"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/chart/tree/treeAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { updateCenterAndZoom } from '../../action/roamHelper.js';\nexport function installTreeAction(registers) {\n  registers.registerAction({\n    type: 'treeExpandAndCollapse',\n    event: 'treeExpandAndCollapse',\n    update: 'update'\n  }, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'tree',\n      query: payload\n    }, function (seriesModel) {\n      var dataIndex = payload.dataIndex;\n      var tree = seriesModel.getData().tree;\n      var node = tree.getNodeByDataIndex(dataIndex);\n      node.isExpand = !node.isExpand;\n    });\n  });\n  registers.registerAction({\n    type: 'treeRoam',\n    event: 'treeRoam',\n    // Here we set 'none' instead of 'update', because roam action\n    // just need to update the transform matrix without having to recalculate\n    // the layout. So don't need to go through the whole update process, such\n    // as 'dataPrcocess', 'coordSystemUpdate', 'layout' and so on.\n    update: 'none'\n  }, function (payload, ecModel, api) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'tree',\n      query: payload\n    }, function (seriesModel) {\n      var coordSys = seriesModel.coordinateSystem;\n      var res = updateCenterAndZoom(coordSys, payload, undefined, api);\n      seriesModel.setCenter && seriesModel.setCenter(res.center);\n      seriesModel.setZoom && seriesModel.setZoom(res.zoom);\n    });\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,4BAA4B;AAChE,OAAO,SAASC,iBAAiBA,CAACC,SAAS,EAAE;EAC3CA,SAAS,CAACC,cAAc,CAAC;IACvBC,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE,uBAAuB;IAC9BC,MAAM,EAAE;EACV,CAAC,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAE;IAC7BA,OAAO,CAACC,aAAa,CAAC;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,MAAM;MACfC,KAAK,EAAEL;IACT,CAAC,EAAE,UAAUM,WAAW,EAAE;MACxB,IAAIC,SAAS,GAAGP,OAAO,CAACO,SAAS;MACjC,IAAIC,IAAI,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC,CAACD,IAAI;MACrC,IAAIE,IAAI,GAAGF,IAAI,CAACG,kBAAkB,CAACJ,SAAS,CAAC;MAC7CG,IAAI,CAACE,QAAQ,GAAG,CAACF,IAAI,CAACE,QAAQ;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;EACFjB,SAAS,CAACC,cAAc,CAAC;IACvBC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,UAAU;IACjB;IACA;IACA;IACA;IACAC,MAAM,EAAE;EACV,CAAC,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAEY,GAAG,EAAE;IAClCZ,OAAO,CAACC,aAAa,CAAC;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,MAAM;MACfC,KAAK,EAAEL;IACT,CAAC,EAAE,UAAUM,WAAW,EAAE;MACxB,IAAIQ,QAAQ,GAAGR,WAAW,CAACS,gBAAgB;MAC3C,IAAIC,GAAG,GAAGvB,mBAAmB,CAACqB,QAAQ,EAAEd,OAAO,EAAEiB,SAAS,EAAEJ,GAAG,CAAC;MAChEP,WAAW,CAACY,SAAS,IAAIZ,WAAW,CAACY,SAAS,CAACF,GAAG,CAACG,MAAM,CAAC;MAC1Db,WAAW,CAACc,OAAO,IAAId,WAAW,CAACc,OAAO,CAACJ,GAAG,CAACK,IAAI,CAAC;IACtD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}