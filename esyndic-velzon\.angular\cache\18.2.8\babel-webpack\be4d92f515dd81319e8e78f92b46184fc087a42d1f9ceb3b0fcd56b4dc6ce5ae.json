{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Telugu [te]\n//! author : <PERSON>hota : https://github.com/kcthota\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var te = moment.defineLocale('te', {\n    months: 'జనవరి_ఫిబ్రవరి_మార్చి_ఏప్రిల్_మే_జూన్_జులై_ఆగస్టు_సెప్టెంబర్_అక్టోబర్_నవంబర్_డిసెంబర్'.split('_'),\n    monthsShort: 'జన._ఫిబ్ర._మార్చి_ఏప్రి._మే_జూన్_జులై_ఆగ._సెప్._అక్టో._నవ._డిసె.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'ఆదివారం_సోమవారం_మంగళవారం_బుధవారం_గురువారం_శుక్రవారం_శనివారం'.split('_'),\n    weekdaysShort: 'ఆది_సోమ_మంగళ_బుధ_గురు_శుక్ర_శని'.split('_'),\n    weekdaysMin: 'ఆ_సో_మం_బు_గు_శు_శ'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm',\n      LTS: 'A h:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm'\n    },\n    calendar: {\n      sameDay: '[నేడు] LT',\n      nextDay: '[రేపు] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[నిన్న] LT',\n      lastWeek: '[గత] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s లో',\n      past: '%s క్రితం',\n      s: 'కొన్ని క్షణాలు',\n      ss: '%d సెకన్లు',\n      m: 'ఒక నిమిషం',\n      mm: '%d నిమిషాలు',\n      h: 'ఒక గంట',\n      hh: '%d గంటలు',\n      d: 'ఒక రోజు',\n      dd: '%d రోజులు',\n      M: 'ఒక నెల',\n      MM: '%d నెలలు',\n      y: 'ఒక సంవత్సరం',\n      yy: '%d సంవత్సరాలు'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}వ/,\n    ordinal: '%dవ',\n    meridiemParse: /రాత్రి|ఉదయం|మధ్యాహ్నం|సాయంత్రం/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'రాత్రి') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'ఉదయం') {\n        return hour;\n      } else if (meridiem === 'మధ్యాహ్నం') {\n        return hour >= 10 ? hour : hour + 12;\n      } else if (meridiem === 'సాయంత్రం') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'రాత్రి';\n      } else if (hour < 10) {\n        return 'ఉదయం';\n      } else if (hour < 17) {\n        return 'మధ్యాహ్నం';\n      } else if (hour < 20) {\n        return 'సాయంత్రం';\n      } else {\n        return 'రాత్రి';\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return te;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "te", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "meridiemParse", "meridiemHour", "hour", "meridiem", "minute", "isLower", "week", "dow", "doy"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/moment/locale/te.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Telugu [te]\n//! author : <PERSON>hota : https://github.com/kcthota\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var te = moment.defineLocale('te', {\n        months: 'జనవరి_ఫిబ్రవరి_మార్చి_ఏప్రిల్_మే_జూన్_జులై_ఆగస్టు_సెప్టెంబర్_అక్టోబర్_నవంబర్_డిసెంబర్'.split(\n            '_'\n        ),\n        monthsShort:\n            'జన._ఫిబ్ర._మార్చి_ఏప్రి._మే_జూన్_జులై_ఆగ._సెప్._అక్టో._నవ._డిసె.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays:\n            'ఆదివారం_సోమవారం_మంగళవారం_బుధవారం_గురువారం_శుక్రవారం_శనివారం'.split(\n                '_'\n            ),\n        weekdaysShort: 'ఆది_సోమ_మంగళ_బుధ_గురు_శుక్ర_శని'.split('_'),\n        weekdaysMin: 'ఆ_సో_మం_బు_గు_శు_శ'.split('_'),\n        longDateFormat: {\n            LT: 'A h:mm',\n            LTS: 'A h:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY, A h:mm',\n            LLLL: 'dddd, D MMMM YYYY, A h:mm',\n        },\n        calendar: {\n            sameDay: '[నేడు] LT',\n            nextDay: '[రేపు] LT',\n            nextWeek: 'dddd, LT',\n            lastDay: '[నిన్న] LT',\n            lastWeek: '[గత] dddd, LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s లో',\n            past: '%s క్రితం',\n            s: 'కొన్ని క్షణాలు',\n            ss: '%d సెకన్లు',\n            m: 'ఒక నిమిషం',\n            mm: '%d నిమిషాలు',\n            h: 'ఒక గంట',\n            hh: '%d గంటలు',\n            d: 'ఒక రోజు',\n            dd: '%d రోజులు',\n            M: 'ఒక నెల',\n            MM: '%d నెలలు',\n            y: 'ఒక సంవత్సరం',\n            yy: '%d సంవత్సరాలు',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}వ/,\n        ordinal: '%dవ',\n        meridiemParse: /రాత్రి|ఉదయం|మధ్యాహ్నం|సాయంత్రం/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (meridiem === 'రాత్రి') {\n                return hour < 4 ? hour : hour + 12;\n            } else if (meridiem === 'ఉదయం') {\n                return hour;\n            } else if (meridiem === 'మధ్యాహ్నం') {\n                return hour >= 10 ? hour : hour + 12;\n            } else if (meridiem === 'సాయంత్రం') {\n                return hour + 12;\n            }\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'రాత్రి';\n            } else if (hour < 10) {\n                return 'ఉదయం';\n            } else if (hour < 17) {\n                return 'మధ్యాహ్నం';\n            } else if (hour < 20) {\n                return 'సాయంత్రం';\n            } else {\n                return 'రాత్రి';\n            }\n        },\n        week: {\n            dow: 0, // Sunday is the first day of the week.\n            doy: 6, // The week that contains Jan 6th is the first week of the year.\n        },\n    });\n\n    return te;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,uFAAuF,CAACC,KAAK,CACjG,GACJ,CAAC;IACDC,WAAW,EACP,kEAAkE,CAACD,KAAK,CACpE,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EACJ,6DAA6D,CAACH,KAAK,CAC/D,GACJ,CAAC;IACLI,aAAa,EAAE,iCAAiC,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC3DK,WAAW,EAAE,oBAAoB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC5CM,cAAc,EAAE;MACZC,EAAE,EAAE,QAAQ;MACZC,GAAG,EAAE,WAAW;MAChBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,qBAAqB;MAC1BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE,eAAe;MACzBC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,WAAW;MACjBC,CAAC,EAAE,gBAAgB;MACnBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,UAAU;IAClCC,OAAO,EAAE,KAAK;IACdC,aAAa,EAAE,gCAAgC;IAC/CC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QACvB,OAAOD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACtC,CAAC,MAAM,IAAIC,QAAQ,KAAK,MAAM,EAAE;QAC5B,OAAOD,IAAI;MACf,CAAC,MAAM,IAAIC,QAAQ,KAAK,WAAW,EAAE;QACjC,OAAOD,IAAI,IAAI,EAAE,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACxC,CAAC,MAAM,IAAIC,QAAQ,KAAK,UAAU,EAAE;QAChC,OAAOD,IAAI,GAAG,EAAE;MACpB;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUD,IAAI,EAAEE,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIH,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,QAAQ;MACnB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,MAAM;MACjB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,WAAW;MACtB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,UAAU;MACrB,CAAC,MAAM;QACH,OAAO,QAAQ;MACnB;IACJ,CAAC;IACDI,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOhD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}