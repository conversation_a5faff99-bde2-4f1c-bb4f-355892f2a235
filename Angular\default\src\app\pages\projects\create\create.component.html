<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Create Project" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
  <div class="col-lg-8">
      <div class="card">
          <div class="card-body">
              <div class="mb-3">
                  <label class="form-label" for="project-title-input">Project Title</label>
                  <input type="text" class="form-control" id="project-title-input"
                      placeholder="Enter project title">
              </div>

              <div class="mb-3">
                  <label class="form-label" for="project-thumbnail-img">Thumbnail Image</label>
                  <input class="form-control" id="project-thumbnail-img" type="file" accept="image/png, image/gif, image/jpeg">
              </div>

              <div class="mb-3">
                  <label class="form-label">Project Description</label>
                  <ckeditor [editor]="Editor" data="
                  <p>It will be as simple as occidental in fact, it will be Occidental. To an English person, it will seem like simplified English, as a skeptical Cambridge friend of mine told me what Occidental is. The European languages are members of the same family. Their separate existence is a myth. For science, music, sport, etc, Europe uses the same vocabulary.</p>
                    <ul>
                      <li>Product Design, Figma (Software), Prototype</li>
                      <li>Four Dashboards : Ecommerce, Analytics, Project etc.</li>
                      <li>Create calendar, chat and email app pages.</li>
                      <li>Add authentication pages</li>
                    </ul>
                    ">
                  </ckeditor>
              </div>

              <div class="row">
                  <div class="col-lg-4">
                      <div class="mb-3 mb-lg-0">
                          <label for="choices-priority-input" class="form-label">Priority</label>
                          <select class="form-select" data-choices data-choices-search-false
                              id="choices-priority-input">
                              <option value="High" selected>High</option>
                              <option value="Medium">Medium</option>
                              <option value="Low">Low</option>
                          </select>
                      </div>
                  </div>
                  <div class="col-lg-4">
                      <div class="mb-3 mb-lg-0">
                          <label for="choices-status-input" class="form-label">Status</label>
                          <select class="form-select" data-choices data-choices-search-false
                              id="choices-status-input">
                              <option value="Inprogress" selected>Inprogress</option>
                              <option value="Completed">Completed</option>
                          </select>
                      </div>
                  </div>
                  <div class="col-lg-4">
                      <div>
                          <label for="datepicker-deadline-input" class="form-label">Deadline</label>
                          <input class="form-control flatpickr-input" type="text" placeholder="Enter publish date" mwlFlatpickr [altInput]="true" [convertModelValue]="true" [dateFormat]="'Y-m-d'">
                      </div>
                  </div>
              </div>
          </div>
          <!-- end card body -->
      </div>
      <!-- end card -->

      <div class="card">
          <div class="card-header">
              <h5 class="card-title mb-0">Attached files</h5>
          </div>
          <div class="card-body">
              <div>
                  <p class="text-muted">Add Attached files here.</p>
                  <dropzone class="dropzone_sec"></dropzone>
              </div>
          </div>
      </div>
      <!-- end card -->
      <div class="text-end mb-4">
        <div class="d-flex gap-1 justify-content-end">
          <button type="submit" class="btn btn-danger w-sm">Delete</button>
          <button type="submit" class="btn btn-secondary w-sm">Draft</button>
          <button type="submit" class="btn btn-success w-sm">Create</button>
        </div>
      </div>
  </div>
  <!-- end col -->
  <div class="col-lg-4">
      <div class="card">
          <div class="card-header">
              <h5 class="card-title mb-0">Privacy</h5>
          </div>
          <div class="card-body">
              <div>
                  <label for="choices-privacy-status-input" class="form-label">Status</label>
                  <select class="form-select" data-choices data-choices-search-false
                      id="choices-privacy-status-input">
                      <option value="Private" selected>Private</option>
                      <option value="Team">Team</option>
                      <option value="Public">Public</option>
                  </select>
              </div>
          </div>
          <!-- end card body -->
      </div>
      <!-- end card -->

      <div class="card">
          <div class="card-header">
              <h5 class="card-title mb-0">Tags</h5>
          </div>
          <div class="card-body">
              <div class="mb-3">
                  <label for="choices-categories-input" class="form-label">Categories</label>
                  <select class="form-select" data-choices data-choices-search-false
                      id="choices-categories-input">
                      <option value="Designing" selected>Designing</option>
                      <option value="Development">Development</option>
                  </select>
              </div>

              <div>
                  <label for="choices-text-input" class="form-label">Skills</label>
                  <ng-select [items]="selectValue" [multiple]="true"></ng-select>
              </div>
          </div>
          <!-- end card body -->
      </div>
      <!-- end card -->

      <div class="card">
          <div class="card-header">
              <h5 class="card-title mb-0">Members</h5>
          </div>
          <div class="card-body">
              <div class="mb-3">
                  <label for="choices-lead-input" class="form-label">Team Lead</label>
                  <select class="form-select" data-choices data-choices-search-false
                      id="choices-lead-input">
                      <option value="Brent Gonzalez" selected>Brent Gonzalez</option>
                      <option value="Darline Williams">Darline Williams</option>
                      <option value="Sylvia Wright">Sylvia Wright</option>
                      <option value="Ellen Smith">Ellen Smith</option>
                      <option value="Jeffrey Salazar">Jeffrey Salazar</option>
                      <option value="Mark Williams">Mark Williams</option>
                  </select>
              </div>

              <div>
                  <label class="form-label">Team Members</label>
                  <div class="avatar-group">
                      <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Brent Gonzalez" placement="top">
                          <div class="avatar-xs">
                              <img src="assets/images/users/avatar-3.jpg" alt="" class="rounded-circle img-fluid">
                          </div>
                      </a>
                      <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Sylvia Wright" placement="top">
                          <div class="avatar-xs">
                              <div class="avatar-title rounded-circle bg-secondary">
                                  S
                              </div>
                          </div>
                      </a>
                      <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Ellen Smith" placement="top">
                          <div class="avatar-xs">
                              <img src="assets/images/users/avatar-4.jpg" alt="" class="rounded-circle img-fluid">
                          </div>
                      </a>
                      <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Add Members" placement="top">
                          <div class="avatar-xs" data-bs-toggle="modal" data-bs-target="#inviteMembersModal">
                              <div class="avatar-title fs-16 rounded-circle bg-light border-dashed border text-primary">
                                  +
                              </div>
                          </div>
                      </a>
                  </div>
              </div>
          </div>
          <!-- end card body -->
      </div>
      <!-- end card -->
  </div>
  <!-- end col -->
</div>
<!-- end row -->
