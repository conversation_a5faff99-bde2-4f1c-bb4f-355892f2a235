# e-Syndic - Property Management System

e-Syndic is a comprehensive full-stack web application for managing co-owned buildings (copropriétés) in Tunisia. It provides a complete solution for building administrators, presidents, owners, and residents to manage their property-related activities.

## Technology Stack

### Backend
- **Framework**: Spring Boot 3.2.0
- **Language**: Java 17
- **Database**: PostgreSQL
- **Authentication**: Keycloak OAuth2
- **Payment Integration**: Paymee API (Tunisia)
- **Build Tool**: Maven

### Frontend
- **Framework**: Angular 17+
- **Template**: Velzon Admin Template
- **Authentication**: Keycloak OAuth2

## Features

### User Management
- Role-based access control (SUPERADMIN, ADMIN, PRESIDENT, OWNER, RESIDENT)
- Keycloak integration for authentication and authorization
- User profile management

### Building Management
- Building registration and management
- Apartment management with owner/resident tracking
- Monthly dues calculation and tracking

### Financial Management
- Payment tracking with FIFO logic
- Paymee payment gateway integration
- Expense management with approval workflow
- Financial reporting

### Assembly Management
- General assembly scheduling and management
- Attendance tracking
- Voting system with quorum management
- Meeting minutes and documentation

### Claims Management
- Incident and claim reporting
- Priority-based claim management
- Assignment and resolution tracking
- Image attachments for claims

## Project Structure

```
e-syndic/
├── backend/                 # Spring Boot backend
│   ├── src/main/java/com/esyndic/
│   │   ├── config/         # Security and application configuration
│   │   ├── controller/     # REST controllers
│   │   ├── entity/         # JPA entities
│   │   ├── repository/     # Spring Data repositories
│   │   └── service/        # Business logic services
│   ├── src/main/resources/
│   │   └── application.yml # Application configuration
│   └── pom.xml            # Maven dependencies
├── database/              # Database scripts
│   ├── schema.sql        # Database schema
│   └── sample_data.sql   # Sample data for testing
└── frontend/             # Angular frontend (to be implemented)
```

## Setup Instructions

### Prerequisites
- Java 17 or higher
- Maven 3.6+
- PostgreSQL 12+
- Keycloak 23.0.0
- Node.js 18+ (for frontend)

### Database Setup

1. Create a PostgreSQL database:
```sql
CREATE DATABASE esyndic;
CREATE USER esyndic_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE esyndic TO esyndic_user;
```

2. Run the schema script:
```bash
psql -U esyndic_user -d esyndic -f database/schema.sql
```

3. (Optional) Load sample data:
```bash
psql -U esyndic_user -d esyndic -f database/sample_data.sql
```

### Keycloak Setup

1. Install and start Keycloak
2. Create a realm named `esyndic`
3. Create client `esyndic-backend` for the backend API
4. Create client `esyndic-frontend` for the Angular frontend
5. Configure realm roles: SUPERADMIN, ADMIN, PRESIDENT, OWNER, RESIDENT
6. Update `application.yml` with your Keycloak configuration

### Backend Setup

1. Navigate to the backend directory:
```bash
cd backend
```

2. Update `src/main/resources/application.yml` with your configuration:
   - Database connection details
   - Keycloak server URL and realm
   - Paymee API credentials

3. Build and run the application:
```bash
mvn clean install
mvn spring-boot:run
```

The backend will start on `http://localhost:8080`

### API Endpoints

#### Health Check
- `GET /api/health` - Application health status
- `GET /api/health/ready` - Readiness check

#### User Management
- `GET /api/users` - Get all users (SUPERADMIN only)
- `GET /api/users/profile` - Get current user profile
- `POST /api/users` - Create user (SUPERADMIN only)
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Deactivate user (SUPERADMIN only)

#### Authentication
All endpoints except health checks require JWT authentication via Keycloak.

## Configuration

### Application Properties

Key configuration properties in `application.yml`:

```yaml
spring:
  datasource:
    url: ****************************************
    username: esyndic_user
    password: your_password
  
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: http://localhost:8080/realms/esyndic/protocol/openid-connect/certs

keycloak:
  auth-server-url: http://localhost:8080
  realm: esyndic
  resource: esyndic-backend

paymee:
  api-url: https://api.paymee.tn
  api-key: your_paymee_api_key
  webhook-secret: your_webhook_secret
```

## Development Status

### Completed
- ✅ Database schema design and implementation
- ✅ JPA entity layer with relationships
- ✅ Repository layer with comprehensive queries
- ✅ Security configuration with Keycloak integration
- ✅ Basic service and controller layers
- ✅ Project structure and build configuration

### In Progress
- 🔄 Complete service layer implementation
- 🔄 REST API controllers for all entities
- 🔄 Angular frontend setup with Velzon template

### Pending
- ⏳ Paymee payment integration
- ⏳ File upload functionality
- ⏳ Email notifications
- ⏳ Reporting and analytics
- ⏳ Unit and integration tests
- ⏳ API documentation (Swagger)

## Contributing

1. Follow the existing code structure and naming conventions
2. Ensure all new endpoints have proper security annotations
3. Write unit tests for new functionality
4. Update documentation for any API changes

## License

This project is proprietary software developed for property management in Tunisia.
