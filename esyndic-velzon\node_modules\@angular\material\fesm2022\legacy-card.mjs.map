{"version": 3, "file": "legacy-card.mjs", "sources": ["../../../../../../src/material/legacy-card/card.ts", "../../../../../../src/material/legacy-card/card.html", "../../../../../../src/material/legacy-card/card-header.html", "../../../../../../src/material/legacy-card/card-title-group.html", "../../../../../../src/material/legacy-card/card-module.ts", "../../../../../../src/material/legacy-card/legacy-card_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Component,\n  ViewEncapsulation,\n  ChangeDetectionStrategy,\n  Directive,\n  Input,\n  Optional,\n  Inject,\n} from '@angular/core';\nimport {ANIMATION_MODULE_TYPE} from '@angular/platform-browser/animations';\n\n/**\n * Content of a card, needed as it's used as a selector in the API.\n * @docs-private\n * @deprecated Use `MatCardContent` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: 'mat-card-content, [mat-card-content], [matCardContent]',\n  host: {'class': 'mat-card-content'},\n})\nexport class MatLegacyCardContent {}\n\n/**\n * Title of a card, needed as it's used as a selector in the API.\n * @docs-private\n * @deprecated Use `MatCardTitle` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n  host: {\n    'class': 'mat-card-title',\n  },\n})\nexport class MatLegacyCardTitle {}\n\n/**\n * Sub-title of a card, needed as it's used as a selector in the API.\n * @docs-private\n * @deprecated Use `MatCardSubtitle` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n  host: {\n    'class': 'mat-card-subtitle',\n  },\n})\nexport class MatLegacyCardSubtitle {}\n\n/**\n * Action section of a card, needed as it's used as a selector in the API.\n * @docs-private\n * @deprecated Use `MatCardActions` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: 'mat-card-actions',\n  exportAs: 'matCardActions',\n  host: {\n    'class': 'mat-card-actions',\n    '[class.mat-card-actions-align-end]': 'align === \"end\"',\n  },\n})\nexport class MatLegacyCardActions {\n  /** Position of the actions inside the card. */\n  @Input() align: 'start' | 'end' = 'start';\n}\n\n/**\n * Footer of a card, needed as it's used as a selector in the API.\n * @docs-private\n * @deprecated Use `MatCardFooter` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: 'mat-card-footer',\n  host: {'class': 'mat-card-footer'},\n})\nexport class MatLegacyCardFooter {}\n\n/**\n * Image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n * @deprecated Use `MatCardImage` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: '[mat-card-image], [matCardImage]',\n  host: {'class': 'mat-card-image'},\n})\nexport class MatLegacyCardImage {}\n\n/**\n * Image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n * @deprecated Use `MatCardSmImage` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: '[mat-card-sm-image], [matCardImageSmall]',\n  host: {'class': 'mat-card-sm-image'},\n})\nexport class MatLegacyCardSmImage {}\n\n/**\n * Image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n * @deprecated Use `MatCardMdImage` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: '[mat-card-md-image], [matCardImageMedium]',\n  host: {'class': 'mat-card-md-image'},\n})\nexport class MatLegacyCardMdImage {}\n\n/**\n * Image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n * @deprecated Use `MatCardLgImage` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: '[mat-card-lg-image], [matCardImageLarge]',\n  host: {'class': 'mat-card-lg-image'},\n})\nexport class MatLegacyCardLgImage {}\n\n/**\n * Large image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n * @deprecated Use `MatCardXlImage` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: '[mat-card-xl-image], [matCardImageXLarge]',\n  host: {'class': 'mat-card-xl-image'},\n})\nexport class MatLegacyCardXlImage {}\n\n/**\n * Avatar image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n * @deprecated Use `MatCardAvatar` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: '[mat-card-avatar], [matCardAvatar]',\n  host: {'class': 'mat-card-avatar'},\n})\nexport class MatLegacyCardAvatar {}\n\n/**\n * A basic content container component that adds the styles of a Material design card.\n *\n * While this component can be used alone, it also provides a number\n * of preset styles for common card sections, including:\n * - mat-card-title\n * - mat-card-subtitle\n * - mat-card-content\n * - mat-card-actions\n * - mat-card-footer\n *\n * @deprecated Use `MatCard` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Component({\n  selector: 'mat-card',\n  exportAs: 'matCard',\n  templateUrl: 'card.html',\n  styleUrls: ['card.css'],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    'class': 'mat-card mat-focus-indicator',\n    '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n  },\n})\nexport class MatLegacyCard {\n  // @breaking-change 9.0.0 `_animationMode` parameter to be made required.\n  constructor(@Optional() @Inject(ANIMATION_MODULE_TYPE) public _animationMode?: string) {}\n}\n\n/**\n * Component intended to be used within the `<mat-card>` component. It adds styles for a\n * preset header section (i.e. a title, subtitle, and avatar layout).\n * @docs-private\n * @deprecated Use `MatCardHeader` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Component({\n  selector: 'mat-card-header',\n  templateUrl: 'card-header.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {'class': 'mat-card-header'},\n})\nexport class MatLegacyCardHeader {}\n\n/**\n * Component intended to be used within the `<mat-card>` component. It adds styles for a preset\n * layout that groups an image with a title section.\n * @docs-private\n * @deprecated Use `MatCardTitleGroup` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Component({\n  selector: 'mat-card-title-group',\n  templateUrl: 'card-title-group.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {'class': 'mat-card-title-group'},\n})\nexport class MatLegacyCardTitleGroup {}\n", "<ng-content></ng-content>\n<ng-content select=\"mat-card-footer\"></ng-content>\n", "<ng-content select=\"[mat-card-avatar], [matCardAvatar]\"></ng-content>\n<div class=\"mat-card-header-text\">\n  <ng-content\n      select=\"mat-card-title, mat-card-subtitle,\n      [mat-card-title], [mat-card-subtitle],\n      [matCardTitle], [matCardSubtitle]\"></ng-content>\n</div>\n<ng-content></ng-content>\n", "<div>\n  <ng-content\n      select=\"mat-card-title, mat-card-subtitle,\n      [mat-card-title], [mat-card-subtitle],\n      [matCardTitle], [matCardSubtitle]\"></ng-content>\n</div>\n<ng-content select=\"img\"></ng-content>\n<ng-content></ng-content>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {\n  MatLegacyCard,\n  MatLegacyCardActions,\n  MatLegacyCardAvatar,\n  MatLegacyCardContent,\n  MatLegacyCardFooter,\n  MatLegacyCardHeader,\n  MatLegacyCardImage,\n  MatLegacyCardLgImage,\n  MatLegacyCardMdImage,\n  MatLegacyCardSmImage,\n  MatLegacyCardSubtitle,\n  MatLegacyCardTitle,\n  MatLegacyCardTitleGroup,\n  MatLegacyCardXlImage,\n} from './card';\n\n/**\n * @deprecated Use `MatCardModule` from `@angular/material/card` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@NgModule({\n  imports: [MatCommonModule],\n  exports: [\n    MatLegacyCard,\n    MatLegacyCardHeader,\n    MatLegacyCardTitleGroup,\n    MatLegacyCardContent,\n    MatLegacyCardTitle,\n    MatLegacyCardSubtitle,\n    MatLegacyCardActions,\n    MatLegacyCardFooter,\n    MatLegacyCardSmImage,\n    MatLegacyCardMdImage,\n    MatLegacyCardLgImage,\n    MatLegacyCardImage,\n    MatLegacyCardXlImage,\n    MatLegacyCardAvatar,\n    MatCommonModule,\n  ],\n  declarations: [\n    MatLegacyCard,\n    MatLegacyCardHeader,\n    MatLegacyCardTitleGroup,\n    MatLegacyCardContent,\n    MatLegacyCardTitle,\n    MatLegacyCardSubtitle,\n    MatLegacyCardActions,\n    MatLegacyCardFooter,\n    MatLegacyCardSmImage,\n    MatLegacyCardMdImage,\n    MatLegacyCardLgImage,\n    MatLegacyCardImage,\n    MatLegacyCardXlImage,\n    MatLegacyCardAvatar,\n  ],\n})\nexport class MatLegacyCardModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;AAmBA;;;;;AAKG;MAKU,oBAAoB,CAAA;8GAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAApB,oBAAoB,EAAA,QAAA,EAAA,wDAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAJhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wDAAwD;AAClE,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,kBAAkB,EAAC;AACpC,iBAAA,CAAA;;AAGD;;;;;AAKG;MAOU,kBAAkB,CAAA;8GAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAlB,kBAAkB,EAAA,QAAA,EAAA,kDAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAN9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAkD,gDAAA,CAAA;AAC5D,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,gBAAgB;AAC1B,qBAAA;AACF,iBAAA,CAAA;;AAGD;;;;;AAKG;MAOU,qBAAqB,CAAA;8GAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAArB,qBAAqB,EAAA,QAAA,EAAA,2DAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAArB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBANjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAA2D,yDAAA,CAAA;AACrE,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,mBAAmB;AAC7B,qBAAA;AACF,iBAAA,CAAA;;AAGD;;;;;AAKG;MASU,oBAAoB,CAAA;AARjC,IAAA,WAAA,GAAA;;QAUW,IAAK,CAAA,KAAA,GAAoB,OAAO,CAAC;AAC3C,KAAA;8GAHY,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAApB,oBAAoB,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,kCAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBARhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,kBAAkB;AAC3B,wBAAA,oCAAoC,EAAE,iBAAiB;AACxD,qBAAA;AACF,iBAAA,CAAA;8BAGU,KAAK,EAAA,CAAA;sBAAb,KAAK;;AAGR;;;;;AAKG;MAKU,mBAAmB,CAAA;8GAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAnB,mBAAmB,EAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAJ/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,iBAAiB,EAAC;AACnC,iBAAA,CAAA;;AAGD;;;;;AAKG;MAKU,kBAAkB,CAAA;8GAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAlB,kBAAkB,EAAA,QAAA,EAAA,kCAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAJ9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kCAAkC;AAC5C,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,gBAAgB,EAAC;AAClC,iBAAA,CAAA;;AAGD;;;;;AAKG;MAKU,oBAAoB,CAAA;8GAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAApB,oBAAoB,EAAA,QAAA,EAAA,0CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAJhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,0CAA0C;AACpD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,mBAAmB,EAAC;AACrC,iBAAA,CAAA;;AAGD;;;;;AAKG;MAKU,oBAAoB,CAAA;8GAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAApB,oBAAoB,EAAA,QAAA,EAAA,2CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAJhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,2CAA2C;AACrD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,mBAAmB,EAAC;AACrC,iBAAA,CAAA;;AAGD;;;;;AAKG;MAKU,oBAAoB,CAAA;8GAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAApB,oBAAoB,EAAA,QAAA,EAAA,0CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAJhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,0CAA0C;AACpD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,mBAAmB,EAAC;AACrC,iBAAA,CAAA;;AAGD;;;;;AAKG;MAKU,oBAAoB,CAAA;8GAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAApB,oBAAoB,EAAA,QAAA,EAAA,2CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAJhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,2CAA2C;AACrD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,mBAAmB,EAAC;AACrC,iBAAA,CAAA;;AAGD;;;;;AAKG;MAKU,mBAAmB,CAAA;8GAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAnB,mBAAmB,EAAA,QAAA,EAAA,oCAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAJ/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oCAAoC;AAC9C,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,iBAAiB,EAAC;AACnC,iBAAA,CAAA;;AAGD;;;;;;;;;;;;;AAaG;MAaU,aAAa,CAAA;;AAExB,IAAA,WAAA,CAA8D,cAAuB,EAAA;QAAvB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAS;KAAI;AAF9E,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,kBAEQ,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAF1C,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,2NC5L1B,mFAEA,EAAA,MAAA,EAAA,CAAA,yzFAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FD0La,aAAa,EAAA,UAAA,EAAA,CAAA;kBAZzB,SAAS;+BACE,UAAU,EAAA,QAAA,EACV,SAAS,EAAA,aAAA,EAGJ,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,8BAA8B;AACvC,wBAAA,iCAAiC,EAAE,qCAAqC;AACzE,qBAAA,EAAA,QAAA,EAAA,mFAAA,EAAA,MAAA,EAAA,CAAA,yzFAAA,CAAA,EAAA,CAAA;;0BAIY,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;AAGvD;;;;;;AAMG;MAQU,mBAAmB,CAAA;8GAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,oGE/MhC,6TAQA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FFuMa,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAP/B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iBAAiB,EAEZ,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA,EAAC,OAAO,EAAE,iBAAiB,EAAC,EAAA,QAAA,EAAA,6TAAA,EAAA,CAAA;;AAIpC;;;;;;AAMG;MAQU,uBAAuB,CAAA;8GAAvB,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAvB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,uBAAuB,8GG/NpC,+PAQA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FHuNa,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAPnC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,sBAAsB,EAEjB,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA,EAAC,OAAO,EAAE,sBAAsB,EAAC,EAAA,QAAA,EAAA,+PAAA,EAAA,CAAA;;;AIlMzC;;;AAGG;MAqCU,mBAAmB,CAAA;8GAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,iBAhB5B,aAAa;YACb,mBAAmB;YACnB,uBAAuB;YACvB,oBAAoB;YACpB,kBAAkB;YAClB,qBAAqB;YACrB,oBAAoB;YACpB,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,oBAAoB;YACpB,kBAAkB;YAClB,oBAAoB;YACpB,mBAAmB,CAAA,EAAA,OAAA,EAAA,CAhCX,eAAe,CAAA,EAAA,OAAA,EAAA,CAEvB,aAAa;YACb,mBAAmB;YACnB,uBAAuB;YACvB,oBAAoB;YACpB,kBAAkB;YAClB,qBAAqB;YACrB,oBAAoB;YACpB,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,oBAAoB;YACpB,kBAAkB;YAClB,oBAAoB;YACpB,mBAAmB;YACnB,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;+GAmBN,mBAAmB,EAAA,OAAA,EAAA,CAnCpB,eAAe,EAgBvB,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAmBN,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBApC/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,CAAC;AAC1B,oBAAA,OAAO,EAAE;wBACP,aAAa;wBACb,mBAAmB;wBACnB,uBAAuB;wBACvB,oBAAoB;wBACpB,kBAAkB;wBAClB,qBAAqB;wBACrB,oBAAoB;wBACpB,mBAAmB;wBACnB,oBAAoB;wBACpB,oBAAoB;wBACpB,oBAAoB;wBACpB,kBAAkB;wBAClB,oBAAoB;wBACpB,mBAAmB;wBACnB,eAAe;AAChB,qBAAA;AACD,oBAAA,YAAY,EAAE;wBACZ,aAAa;wBACb,mBAAmB;wBACnB,uBAAuB;wBACvB,oBAAoB;wBACpB,kBAAkB;wBAClB,qBAAqB;wBACrB,oBAAoB;wBACpB,mBAAmB;wBACnB,oBAAoB;wBACpB,oBAAoB;wBACpB,oBAAoB;wBACpB,kBAAkB;wBAClB,oBAAoB;wBACpB,mBAAmB;AACpB,qBAAA;AACF,iBAAA,CAAA;;;AClED;;AAEG;;;;"}