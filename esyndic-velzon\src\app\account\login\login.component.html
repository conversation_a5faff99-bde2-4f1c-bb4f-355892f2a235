<div class="auth-page-wrapper pt-5">
    <!-- auth page bg -->
    <div class="auth-one-bg-position auth-one-bg" id="auth-particles">
        <div class="bg-overlay"></div>

        <div class="shape">
            <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1440 120">
                <path d="M 0,36 C 144,53.6 432,123.2 720,124 C 1008,124.8 1296,56.8 1440,40L1440 140L0 140z"></path>
            </svg>
        </div>
    </div>

    <!-- auth page content -->
    <div class="auth-page-content">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="text-center mt-sm-5 mb-4 text-white-50">
                        <div>
                            <a routerLink="/" class="d-inline-block auth-logo">
                                <img src="assets/images/logo-light.png" alt="" height="20">
                            </a>
                        </div>
                        <p class="mt-3 fs-15 fw-medium">e-Syndic - Gestion de Copropriété</p>
                    </div>
                </div>
            </div>
            <!-- end row -->

            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6 col-xl-5">
                    <div class="card mt-4">

                        <div class="card-body p-4">
                            <div class="text-center mt-2">
                                <h5 class="text-primary">Bienvenue sur e-Syndic !</h5>
                                <p class="text-muted">Connectez-vous pour accéder à votre espace de gestion.</p>
                            </div>
                            <div class="p-2 mt-4">
                                <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">

                                    <div class="mb-3">
                                        <label class="form-label" for="username">Email</label>
                                        <input type="email" class="form-control" id="email" formControlName="email" [ngClass]="{ 'is-invalid': submitted && f['email'].errors }" placeholder="Enter email">
                                        <div *ngIf="submitted && f['email'].errors" class="invalid-feedback">
                                            <div *ngIf="f['email'].errors['required']">Email is required</div>
                                            <div *ngIf="f['email'].errors['email']">Email must be a valid email address</div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="float-end">
                                            <a routerLink="/pass-reset/basic" class="text-muted">Forgot password?</a>
                                        </div>
                                        <label class="form-label" for="password-input">Password</label>
                                        <div class="position-relative auth-pass-inputgroup mb-3">
                                            <input [type]="fieldTextType ? 'text' : 'password'" class="form-control pe-5" placeholder="Enter password" id="password-input" formControlName="password" [ngClass]="{ 'is-invalid': submitted && f['password'].errors }">
                                            <button class="btn btn-link position-absolute end-0 top-0 text-decoration-none text-muted" type="button" id="password-addon"><i class="mdi align-middle" [ngClass]="{'mdi-eye-off-outline': !fieldTextType, 'mdi-eye-outline': fieldTextType
                                          }" (click)="toggleFieldTextType()"></i></button>
                                            <div *ngIf="submitted && f['password'].errors" class="invalid-feedback">
                                                <span *ngIf="f['password'].errors['required']">Password is required</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label" for="apartment-code">Code Appartement <small class="text-muted">(Optionnel - pour propriétaires/résidents)</small></label>
                                        <input type="text" class="form-control" id="apartment-code" formControlName="apartmentCode" placeholder="Ex: APT001">
                                        <div class="form-text">
                                            <small class="text-muted">Laissez vide si vous êtes administrateur ou super-administrateur</small>
                                        </div>
                                    </div>

                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="" id="auth-remember-check">
                                        <label class="form-check-label" for="auth-remember-check">Se souvenir de moi</label>
                                    </div>

                                    <div class="mt-4">
                                        <button class="btn btn-success w-100" type="submit" [disabled]="submitted">
                                            <span *ngIf="submitted" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                            Se connecter
                                        </button>
                                    </div>

                                    <div class="mt-4 text-center">
                                        <div class="alert alert-info">
                                            <h6 class="mb-2">Comptes de test :</h6>
                                            <small>
                                                <strong>Admin:</strong> admin&#64;esyndic.com / admin123<br>
                                                <strong>Propriétaire:</strong> jean.dupont&#64;email.com / password123 + APT001
                                            </small>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <!-- end card body -->
                    </div>
                    <!-- end card -->

                    <div class="mt-4 text-center">
                        <p class="mb-0">Besoin d'aide ? <a href="mailto:support&#64;esyndic.com" class="fw-semibold text-primary text-decoration-underline">Contactez le support</a></p>
                    </div>

                </div>
            </div>
            <!-- end row -->
        </div>
        <!-- end container -->
    </div>
    <!-- end auth page content -->

    <!-- footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="text-center">
                        <p class="mb-0 text-muted">&copy; {{year}} e-Syndic. Système de gestion de copropriété</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- end Footer -->
</div>
<!-- end auth-page-wrapper -->
<app-toasts aria-live="polite" aria-atomic="true"></app-toasts>