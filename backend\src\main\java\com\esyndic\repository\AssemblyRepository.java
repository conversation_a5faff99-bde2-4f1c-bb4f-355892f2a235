package com.esyndic.repository;

import com.esyndic.entity.Assembly;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface AssemblyRepository extends JpaRepository<Assembly, UUID> {

    List<Assembly> findByBuildingId(UUID buildingId);

    List<Assembly> findByStatus(Assembly.AssemblyStatus status);

    List<Assembly> findByBuildingIdAndStatus(UUID buildingId, Assembly.AssemblyStatus status);

    List<Assembly> findByCreatedById(UUID createdById);

    @Query("SELECT a FROM Assembly a WHERE a.scheduledDate >= :startDate AND a.scheduledDate <= :endDate")
    List<Assembly> findByScheduledDateBetween(@Param("startDate") LocalDateTime startDate, 
                                            @Param("endDate") LocalDateTime endDate);

    @Query("SELECT a FROM Assembly a WHERE a.building.id = :buildingId AND " +
           "a.scheduledDate >= :startDate AND a.scheduledDate <= :endDate")
    List<Assembly> findByBuildingIdAndScheduledDateBetween(@Param("buildingId") UUID buildingId,
                                                         @Param("startDate") LocalDateTime startDate,
                                                         @Param("endDate") LocalDateTime endDate);

    @Query("SELECT a FROM Assembly a WHERE a.scheduledDate < :currentDate AND a.status = 'SCHEDULED'")
    List<Assembly> findOverdueScheduledAssemblies(@Param("currentDate") LocalDateTime currentDate);

    @Query("SELECT a FROM Assembly a WHERE a.building.id = :buildingId AND " +
           "a.scheduledDate < :currentDate AND a.status = 'SCHEDULED'")
    List<Assembly> findOverdueScheduledAssembliesByBuildingId(@Param("buildingId") UUID buildingId,
                                                            @Param("currentDate") LocalDateTime currentDate);

    @Query("SELECT a FROM Assembly a WHERE a.scheduledDate >= :currentDate AND a.status = 'SCHEDULED'")
    List<Assembly> findUpcomingAssemblies(@Param("currentDate") LocalDateTime currentDate);

    @Query("SELECT a FROM Assembly a WHERE a.building.id = :buildingId AND " +
           "a.scheduledDate >= :currentDate AND a.status = 'SCHEDULED'")
    List<Assembly> findUpcomingAssembliesByBuildingId(@Param("buildingId") UUID buildingId,
                                                    @Param("currentDate") LocalDateTime currentDate);

    @Query("SELECT COUNT(a) FROM Assembly a WHERE a.building.id = :buildingId")
    long countAssembliesByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT COUNT(a) FROM Assembly a WHERE a.building.id = :buildingId AND a.status = :status")
    long countAssembliesByBuildingIdAndStatus(@Param("buildingId") UUID buildingId, 
                                            @Param("status") Assembly.AssemblyStatus status);

    @Query("SELECT a FROM Assembly a WHERE a.title LIKE %:title%")
    List<Assembly> findByTitleContaining(@Param("title") String title);

    @Query("SELECT a FROM Assembly a WHERE a.building.id = :buildingId AND a.title LIKE %:title%")
    List<Assembly> findByBuildingIdAndTitleContaining(@Param("buildingId") UUID buildingId, 
                                                    @Param("title") String title);

    @Query("SELECT a FROM Assembly a WHERE a.building.id = :buildingId ORDER BY a.scheduledDate DESC")
    List<Assembly> findByBuildingIdOrderByScheduledDateDesc(@Param("buildingId") UUID buildingId);

    @Query("SELECT a FROM Assembly a WHERE a.quorumAchieved >= a.quorumRequired")
    List<Assembly> findAssembliesWithQuorumMet();

    @Query("SELECT a FROM Assembly a WHERE a.building.id = :buildingId AND a.quorumAchieved >= a.quorumRequired")
    List<Assembly> findAssembliesWithQuorumMetByBuildingId(@Param("buildingId") UUID buildingId);
}
