package com.esyndic.repository;

import com.esyndic.entity.Claim;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface ClaimRepository extends JpaRepository<Claim, UUID> {

    List<Claim> findByBuildingId(UUID buildingId);

    List<Claim> findByApartmentId(UUID apartmentId);

    List<Claim> findBySubmittedById(UUID submittedById);

    List<Claim> findByAssignedToId(UUID assignedToId);

    List<Claim> findByStatus(Claim.ClaimStatus status);

    List<Claim> findByPriority(Claim.ClaimPriority priority);

    List<Claim> findByCategory(String category);

    List<Claim> findByBuildingIdAndStatus(UUID buildingId, Claim.ClaimStatus status);

    List<Claim> findByBuildingIdAndPriority(UUID buildingId, Claim.ClaimPriority priority);

    List<Claim> findByBuildingIdAndCategory(UUID buildingId, String category);

    @Query("SELECT c FROM Claim c WHERE c.createdAt >= :startDate AND c.createdAt <= :endDate")
    List<Claim> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                     @Param("endDate") LocalDateTime endDate);

    @Query("SELECT c FROM Claim c WHERE c.building.id = :buildingId AND " +
           "c.createdAt >= :startDate AND c.createdAt <= :endDate")
    List<Claim> findByBuildingIdAndCreatedAtBetween(@Param("buildingId") UUID buildingId,
                                                  @Param("startDate") LocalDateTime startDate,
                                                  @Param("endDate") LocalDateTime endDate);

    @Query("SELECT c FROM Claim c WHERE c.resolvedAt >= :startDate AND c.resolvedAt <= :endDate")
    List<Claim> findByResolvedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                      @Param("endDate") LocalDateTime endDate);

    @Query("SELECT COUNT(c) FROM Claim c WHERE c.building.id = :buildingId")
    long countClaimsByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT COUNT(c) FROM Claim c WHERE c.building.id = :buildingId AND c.status = :status")
    long countClaimsByBuildingIdAndStatus(@Param("buildingId") UUID buildingId, 
                                        @Param("status") Claim.ClaimStatus status);

    @Query("SELECT COUNT(c) FROM Claim c WHERE c.building.id = :buildingId AND c.priority = :priority")
    long countClaimsByBuildingIdAndPriority(@Param("buildingId") UUID buildingId, 
                                          @Param("priority") Claim.ClaimPriority priority);

    @Query("SELECT COUNT(c) FROM Claim c WHERE c.submittedBy.id = :userId")
    long countClaimsBySubmittedBy(@Param("userId") UUID userId);

    @Query("SELECT COUNT(c) FROM Claim c WHERE c.assignedTo.id = :userId")
    long countClaimsByAssignedTo(@Param("userId") UUID userId);

    @Query("SELECT DISTINCT c.category FROM Claim c WHERE c.building.id = :buildingId AND c.category IS NOT NULL")
    List<String> findDistinctCategoriesByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT c FROM Claim c WHERE c.title LIKE %:title%")
    List<Claim> findByTitleContaining(@Param("title") String title);

    @Query("SELECT c FROM Claim c WHERE c.building.id = :buildingId AND c.title LIKE %:title%")
    List<Claim> findByBuildingIdAndTitleContaining(@Param("buildingId") UUID buildingId, 
                                                 @Param("title") String title);

    @Query("SELECT c FROM Claim c WHERE c.description LIKE %:description%")
    List<Claim> findByDescriptionContaining(@Param("description") String description);

    @Query("SELECT c FROM Claim c WHERE c.building.id = :buildingId ORDER BY c.createdAt DESC")
    List<Claim> findByBuildingIdOrderByCreatedAtDesc(@Param("buildingId") UUID buildingId);

    @Query("SELECT c FROM Claim c WHERE c.status IN ('PENDING', 'IN_PROGRESS') ORDER BY c.priority DESC, c.createdAt ASC")
    List<Claim> findActiveClaims();

    @Query("SELECT c FROM Claim c WHERE c.building.id = :buildingId AND " +
           "c.status IN ('PENDING', 'IN_PROGRESS') ORDER BY c.priority DESC, c.createdAt ASC")
    List<Claim> findActiveClaimsByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT c FROM Claim c WHERE c.priority IN ('HIGH', 'URGENT') AND c.status != 'CLOSED'")
    List<Claim> findHighPriorityClaims();

    @Query("SELECT c FROM Claim c WHERE c.building.id = :buildingId AND " +
           "c.priority IN ('HIGH', 'URGENT') AND c.status != 'CLOSED'")
    List<Claim> findHighPriorityClaimsByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT c FROM Claim c WHERE c.imageFilePath IS NOT NULL AND c.imageFilePath != ''")
    List<Claim> findClaimsWithImages();

    @Query("SELECT c FROM Claim c WHERE c.building.id = :buildingId AND " +
           "c.imageFilePath IS NOT NULL AND c.imageFilePath != ''")
    List<Claim> findClaimsWithImagesByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT c FROM Claim c WHERE c.assignedTo IS NULL AND c.status = 'PENDING'")
    List<Claim> findUnassignedClaims();

    @Query("SELECT c FROM Claim c WHERE c.building.id = :buildingId AND " +
           "c.assignedTo IS NULL AND c.status = 'PENDING'")
    List<Claim> findUnassignedClaimsByBuildingId(@Param("buildingId") UUID buildingId);
}
