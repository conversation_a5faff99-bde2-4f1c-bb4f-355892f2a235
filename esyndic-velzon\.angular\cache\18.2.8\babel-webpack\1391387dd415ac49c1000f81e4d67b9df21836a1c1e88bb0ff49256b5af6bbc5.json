{"ast": null, "code": "import windingLine from './windingLine.js';\nvar EPSILON = 1e-8;\nfunction isAroundEqual(a, b) {\n  return Math.abs(a - b) < EPSILON;\n}\nexport function contain(points, x, y) {\n  var w = 0;\n  var p = points[0];\n  if (!p) {\n    return false;\n  }\n  for (var i = 1; i < points.length; i++) {\n    var p2 = points[i];\n    w += windingLine(p[0], p[1], p2[0], p2[1], x, y);\n    p = p2;\n  }\n  var p0 = points[0];\n  if (!isAroundEqual(p[0], p0[0]) || !isAroundEqual(p[1], p0[1])) {\n    w += windingLine(p[0], p[1], p0[0], p0[1], x, y);\n  }\n  return w !== 0;\n}", "map": {"version": 3, "names": ["windingLine", "EPSILON", "isAroundEqual", "a", "b", "Math", "abs", "contain", "points", "x", "y", "w", "p", "i", "length", "p2", "p0"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/zrender/lib/contain/polygon.js"], "sourcesContent": ["import windingLine from './windingLine.js';\nvar EPSILON = 1e-8;\nfunction isAroundEqual(a, b) {\n    return Math.abs(a - b) < EPSILON;\n}\nexport function contain(points, x, y) {\n    var w = 0;\n    var p = points[0];\n    if (!p) {\n        return false;\n    }\n    for (var i = 1; i < points.length; i++) {\n        var p2 = points[i];\n        w += windingLine(p[0], p[1], p2[0], p2[1], x, y);\n        p = p2;\n    }\n    var p0 = points[0];\n    if (!isAroundEqual(p[0], p0[0]) || !isAroundEqual(p[1], p0[1])) {\n        w += windingLine(p[0], p[1], p0[0], p0[1], x, y);\n    }\n    return w !== 0;\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAC1C,IAAIC,OAAO,GAAG,IAAI;AAClB,SAASC,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOC,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGC,CAAC,CAAC,GAAGH,OAAO;AACpC;AACA,OAAO,SAASM,OAAOA,CAACC,MAAM,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAClC,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAGJ,MAAM,CAAC,CAAC,CAAC;EACjB,IAAI,CAACI,CAAC,EAAE;IACJ,OAAO,KAAK;EAChB;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAIE,EAAE,GAAGP,MAAM,CAACK,CAAC,CAAC;IAClBF,CAAC,IAAIX,WAAW,CAACY,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEG,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAEN,CAAC,EAAEC,CAAC,CAAC;IAChDE,CAAC,GAAGG,EAAE;EACV;EACA,IAAIC,EAAE,GAAGR,MAAM,CAAC,CAAC,CAAC;EAClB,IAAI,CAACN,aAAa,CAACU,CAAC,CAAC,CAAC,CAAC,EAAEI,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,aAAa,CAACU,CAAC,CAAC,CAAC,CAAC,EAAEI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5DL,CAAC,IAAIX,WAAW,CAACY,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEI,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,EAAEC,CAAC,CAAC;EACpD;EACA,OAAOC,CAAC,KAAK,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}