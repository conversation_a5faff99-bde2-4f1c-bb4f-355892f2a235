<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Live Auction" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
    <div class="col-xxl-9">
        <div class="card">
            <div class="card-header border-0">
                <div class="d-lg-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title mb-0">Live Auction</h5>
                    </div>
                    <div class="flex-shrink-0 mt-4 mt-lg-0">
                        <ul class="nav nav-pills filter-btns" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link fw-medium active" type="button" data-filter="all" (click)="activeCategory('allItems')" [ngClass]="{'active': galleryFilter ==='allItems'}">All Items</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link fw-medium" type="button" data-filter="upto-15" (click)="activeCategory('upto-15')" [ngClass]="{'active': galleryFilter ==='upto-15'}">Up to 15%</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link fw-medium" type="button" data-filter="upto-30" (click)="activeCategory('upto-30')" [ngClass]="{'active': galleryFilter ==='upto-30'}">Up to 30%</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link fw-medium" type="button" data-filter="upto-40" (click)="activeCategory('upto-40')" [ngClass]="{'active': galleryFilter ==='upto-40'}">Up to 40%</button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            @for ( data of filterredImages; track $index) {
            <div class="col-xxl-3 col-lg-4 col-md-6 product-item upto-15">
                <div class="card explore-box card-animate">
                    <div class="position-relative rounded overflow-hidden">
                        <img src="{{data.img}}" alt="" class="card-img-top explore-img">
                        <div class="discount-time">
                            <h5 id="auction-time-1" class="mb-0 text-white">{{_days}} : {{_hours}} : {{_minutes}} :
                                {{_seconds}}</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="fw-medium mb-0 float-end"><i class="mdi mdi-heart text-danger align-middle"></i>
                            {{data.likes}}k </p>
                        <h5 class="text-success"><i class="mdi mdi-ethereum"></i> {{data.price}}ETH </h5>
                        <h6 class="fs-16 mb-3"><a routerLink="/marletplace/item-details">{{data.title}}</a></h6>
                        <div>
                            <span class="text-muted float-end">Available: {{data.available}}</span>
                            <span class="text-muted">Sold: {{data.sold}}</span>
                            <ngb-progressbar type="{{data.progressClass}}" [value]="data.size" [striped]="true" class="progress-sm mt-2"></ngb-progressbar>
                        </div>
                    </div>
                </div>
            </div>
            }
            <!--end col-->
        </div>
        <!--end row-->
        <div class="row">
            <div class="col-lg-12">
                <div class="text-center mb-3">
                    <button class="btn btn-link text-success mt-2"><i class="mdi mdi-loading mdi-spin fs-20 align-middle me-2"></i> Load more </button>
                </div>
            </div>
        </div>
    </div>
    <!--end col-->
    <div class="col-xxl-3">
        <div class="card">
            <div class="card-header d-flex align-items-center">
                <h6 class="card-title mb-0 flex-grow-1">Top Drop</h6>
                <a class="text-muted" href="javascript:void(0);">
                    See All <i class="ri-arrow-right-line align-bottom"></i>
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive table-card">
                    <table class="table table-borderless align-middle">
                        <tbody>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/nft/img-03.jpg" alt="" class="avatar-sm object-fit-cover rounded-circle">
                                        <div class="ms-2">
                                            <a routerLink="/marletplace/item-details">
                                                <h6 class="fs-15 mb-1">Creative filtered portrait</h6>
                                            </a>
                                            <p class="mb-0 text-muted">Sold at 34.81 ETH</p>
                                        </div>
                                    </div>
                                </td>
                                <td><small>Just Now</small></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="https://img.themesbrand.com/velzon/images/img-4.gif" alt="" class="avatar-sm object-fit-cover rounded-circle">
                                        <div class="ms-2">
                                            <a routerLink="/marletplace/item-details">
                                                <h6 class="fs-15 mb-1">Patterns arts & culture</h6>
                                            </a>
                                            <p class="mb-0 text-muted">Sold at 147.83 ETH</p>
                                        </div>
                                    </div>
                                </td>
                                <td><small>3 sec ago</small></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="https://img.themesbrand.com/velzon/images/img-3.gif" alt="" class="avatar-sm object-fit-cover rounded-circle">
                                        <div class="ms-2">
                                            <a routerLink="/marletplace/item-details">
                                                <h6 class="fs-15 mb-1">Evolved Reality</h6>
                                            </a>
                                            <p class="mb-0 text-muted">Sold at 34.81 ETH</p>
                                        </div>
                                    </div>
                                </td>
                                <td><small>2 min ago</small></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/nft/img-04.jpg" alt="" class="avatar-sm object-fit-cover rounded-circle">
                                        <div class="ms-2">
                                            <a routerLink="/marletplace/item-details">
                                                <h6 class="fs-15 mb-1">Smillevers Crypto</h6>
                                            </a>
                                            <p class="mb-0 text-muted">Sold at 47.9 ETH</p>
                                        </div>
                                    </div>
                                </td>
                                <td><small>26 min ago</small></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/nft/img-05.jpg" alt="" class="avatar-sm object-fit-cover rounded-circle">
                                        <div class="ms-2">
                                            <a routerLink="/marletplace/item-details">
                                                <h6 class="fs-15 mb-1">Robotic Body Art</h6>
                                            </a>
                                            <p class="mb-0 text-muted">Sold at 134.32 ETH</p>
                                        </div>
                                    </div>
                                </td>
                                <td><small>1 hrs ago</small></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/nft/img-02.jpg" alt="" class="avatar-sm object-fit-cover rounded-circle">
                                        <div class="ms-2">
                                            <a routerLink="/marletplace/item-details">
                                                <h6 class="fs-15 mb-1">Trendy fashion portraits</h6>
                                            </a>
                                            <p class="mb-0 text-muted">Sold at 643.19 ETH</p>
                                        </div>
                                    </div>
                                </td>
                                <td><small>3 hrs ago</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-header d-flex align-items-center">
                <h6 class="card-title mb-0 flex-grow-1">Top Creator</h6>
                <a class="text-muted" routerLink="/marletplace/item-details">
                    See All <i class="ri-arrow-right-line align-bottom"></i>
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive table-card">
                    <table class="table table-borderless align-middle">
                        <tbody>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/users/avatar-1.jpg" alt="" class="avatar-sm object-fit-cover rounded-circle">
                                        <div class="ms-2">
                                            <a href="javascript:void(0);">
                                                <h6 class="fs-15 mb-1">Herbert Stokes</h6>
                                            </a>
                                            <p class="mb-0 text-muted">23 Products</p>
                                        </div>
                                    </div>
                                </td>
                                <td><button class="btn btn-success btn-sm">Follow</button></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/users/avatar-3.jpg" alt="" class="avatar-sm object-fit-cover rounded-circle">
                                        <div class="ms-2">
                                            <a href="javascript:void(0);">
                                                <h6 class="fs-15 mb-1">Thomas Taylor</h6>
                                            </a>
                                            <p class="mb-0 text-muted">123 Products</p>
                                        </div>
                                    </div>
                                </td>
                                <td><button class="btn btn-soft-success btn-sm">Unfllow</button></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/users/avatar-5.jpg" alt="" class="avatar-sm object-fit-cover rounded-circle">
                                        <div class="ms-2">
                                            <a href="javascript:void(0);">
                                                <h6 class="fs-15 mb-1">Henry Baird</h6>
                                            </a>
                                            <p class="mb-0 text-muted">46 Products</p>
                                        </div>
                                    </div>
                                </td>
                                <td><button class="btn btn-success btn-sm">Follow</button></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/users/avatar-10.jpg" alt="" class="avatar-sm object-fit-cover rounded-circle">
                                        <div class="ms-2">
                                            <a href="javascript:void(0);">
                                                <h6 class="fs-15 mb-1">Nancy Martino</h6>
                                            </a>
                                            <p class="mb-0 text-muted">845 Products</p>
                                        </div>
                                    </div>
                                </td>
                                <td><button class="btn btn-success btn-sm">Follow</button></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/users/avatar-8.jpg" alt="" class="avatar-sm object-fit-cover rounded-circle">
                                        <div class="ms-2">
                                            <a href="javascript:void(0);">
                                                <h6 class="fs-15 mb-1">James Price</h6>
                                            </a>
                                            <p class="mb-0 text-muted">318 Products</p>
                                        </div>
                                    </div>
                                </td>
                                <td><button class="btn btn-soft-success btn-sm">Unfllow</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!--end col-->
</div>
<!--end row-->