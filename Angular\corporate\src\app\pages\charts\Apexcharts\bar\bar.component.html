<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Bar Charts" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Basic Bar Chart</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="basicBarChart.series" [chart]="basicBarChart.chart"
                    [dataLabels]="basicBarChart.dataLabels" [plotOptions]="basicBarChart.plotOptions"
                    [xaxis]="basicBarChart.xaxis" [colors]="basicBarChart.colors" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Custom DataLabels Bar</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="customDataLabelsChart.series" [chart]="customDataLabelsChart.chart"
                    [dataLabels]="customDataLabelsChart.dataLabels" [stroke]="customDataLabelsChart.stroke"
                    [colors]="customDataLabelsChart.colors" [title]="customDataLabelsChart.title"
                    [subtitle]="customDataLabelsChart.subtitle" [plotOptions]="customDataLabelsChart.plotOptions"
                    [yaxis]="customDataLabelsChart.yaxis" [xaxis]="customDataLabelsChart.xaxis"
                    [tooltip]="customDataLabelsChart.tooltip" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Stacked Bar Charts</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="stackedBarChart.series" [chart]="stackedBarChart.chart"
                    [dataLabels]="stackedBarChart.dataLabels" [plotOptions]="stackedBarChart.plotOptions"
                    [xaxis]="stackedBarChart.xaxis" [stroke]="stackedBarChart.stroke" [fill]="stackedBarChart.fill"
                    [yaxis]="stackedBarChart.yaxis" [title]="stackedBarChart.title" [tooltip]="stackedBarChart.tooltip"
                    [legend]="stackedBarChart.legend" [colors]="stackedBarChart.colors" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Stacked Bars 100</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="stacked100BarChart.series" [chart]="stacked100BarChart.chart"
                    [dataLabels]="stacked100BarChart.dataLabels" [plotOptions]="stacked100BarChart.plotOptions"
                    [xaxis]="stacked100BarChart.xaxis" [stroke]="stacked100BarChart.stroke"
                    [fill]="stacked100BarChart.fill" [title]="stacked100BarChart.title"
                    [tooltip]="stacked100BarChart.tooltip" [legend]="stacked100BarChart.legend"
                    [colors]="stacked100BarChart.colors" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->


<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Bar with Negative Values</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="barWithNegativeChart.series" [chart]="barWithNegativeChart.chart"
                    [dataLabels]="barWithNegativeChart.dataLabels" [stroke]="barWithNegativeChart.stroke"
                    [colors]="barWithNegativeChart.colors" [title]="barWithNegativeChart.title"
                    [grid]="barWithNegativeChart.grid" [tooltip]="barWithNegativeChart.tooltip"
                    [plotOptions]="barWithNegativeChart.plotOptions" [yaxis]="barWithNegativeChart.yaxis"
                    [xaxis]="barWithNegativeChart.xaxis" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Bar with Markers</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="barWithMarkersChart.series" [chart]="barWithMarkersChart.chart"
                    [legend]="barWithMarkersChart.legend" [dataLabels]="barWithMarkersChart.dataLabels"
                    [colors]="barWithMarkersChart.colors" [plotOptions]="barWithMarkersChart.plotOptions"
                    dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Reversed Bar Chart</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="reversedBarChart.series" [chart]="reversedBarChart.chart"
                    [dataLabels]="reversedBarChart.dataLabels" [plotOptions]="reversedBarChart.plotOptions"
                    [xaxis]="reversedBarChart.xaxis" [yaxis]="reversedBarChart.yaxis"
                    [annotations]="reversedBarChart.annotations" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Patterned Chart</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="patternedChart.series" [chart]="patternedChart.chart"
                    [dataLabels]="patternedChart.dataLabels" [plotOptions]="patternedChart.plotOptions"
                    [yaxis]="patternedChart.yaxis" [legend]="patternedChart.legend" [xaxis]="patternedChart.xaxis"
                    [stroke]="patternedChart.stroke" [tooltip]="patternedChart.tooltip" [fill]="patternedChart.fill"
                    [states]="patternedChart.states" [title]="patternedChart.title" [colors]="patternedChart.colors"
                    dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Grouped Bar Chart</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="groupedBarChart.series" [chart]="groupedBarChart.chart"
                    [dataLabels]="groupedBarChart.dataLabels" [plotOptions]="groupedBarChart.plotOptions"
                    [xaxis]="groupedBarChart.xaxis" [stroke]="groupedBarChart.stroke" [colors]="groupedBarChart.colors"
                    dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Bar with Images</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <div class="live-preview">
                    <apx-chart [series]="barWithImageChart.series" [chart]="barWithImageChart.chart"
                        [dataLabels]="barWithImageChart.dataLabels" [stroke]="barWithImageChart.stroke"
                        [colors]="barWithImageChart.colors" [title]="barWithImageChart.title"
                        [grid]="barWithImageChart.grid" [plotOptions]="barWithImageChart.plotOptions"
                        [yaxis]="barWithImageChart.yaxis" [fill]="barWithImageChart.fill"
                        [labels]="barWithImageChart.labels" dir="ltr"></apx-chart>
                </div>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->