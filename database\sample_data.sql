-- Sample Data for e-Syndic Database
-- This file contains sample data for testing and development purposes

-- Insert sample users (these would normally be synchronized from Keycloak)
INSERT INTO users (id, keycloak_id, username, email, first_name, last_name, phone, is_active) VALUES
(uuid_generate_v4(), 'keycloak-admin-1', 'admin', '<EMAIL>', 'Admin', 'System', '+216 20 123 456', true),
(uuid_generate_v4(), 'keycloak-president-1', 'president1', '<EMAIL>', '<PERSON>', '<PERSON>', '+216 20 234 567', true),
(uuid_generate_v4(), 'keycloak-owner-1', 'owner1', '<EMAIL>', 'Fatma', 'Trab<PERSON>i', '+216 20 345 678', true),
(uuid_generate_v4(), 'keycloak-owner-2', 'owner2', '<EMAIL>', '<PERSON>', '<PERSON><PERSON><PERSON>', '+216 20 456 789', true),
(uuid_generate_v4(), 'keycloak-resident-1', 'resident1', '<EMAIL>', '<PERSON>ina', '<PERSON>ssi', '+216 20 567 890', true),
(uuid_generate_v4(), 'keycloak-resident-2', 'resident2', '<EMAIL>', 'Karim', 'Bouazizi', '+216 20 678 901', true);

-- Insert sample buildings
INSERT INTO buildings (id, name, address, city, postal_code, country, total_apartments, admin_id, president_id, is_active) VALUES
(uuid_generate_v4(), 'Résidence Les Jasmins', '15 Avenue Habib Bourguiba', 'Tunis', '1000', 'Tunisia', 20, 
 (SELECT id FROM users WHERE username = 'admin'), 
 (SELECT id FROM users WHERE username = 'president1'), true),
(uuid_generate_v4(), 'Immeuble El Manar', '42 Rue de la République', 'Ariana', '2080', 'Tunisia', 12, 
 (SELECT id FROM users WHERE username = 'admin'), NULL, true);

-- Insert sample apartments
INSERT INTO apartments (id, building_id, apartment_number, floor_number, area_sqm, owner_id, resident_id, monthly_dues, is_active) VALUES
-- Building 1 apartments
(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins'), 'A101', 1, 85.50, 
 (SELECT id FROM users WHERE username = 'owner1'), 
 (SELECT id FROM users WHERE username = 'owner1'), 150.00, true),
(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins'), 'A102', 1, 92.00, 
 (SELECT id FROM users WHERE username = 'owner2'), 
 (SELECT id FROM users WHERE username = 'resident1'), 160.00, true),
(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins'), 'A201', 2, 85.50, 
 NULL, 
 (SELECT id FROM users WHERE username = 'resident2'), 150.00, true),
(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins'), 'A202', 2, 92.00, 
 NULL, NULL, 160.00, true),

-- Building 2 apartments
(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Immeuble El Manar'), 'B01', 0, 75.00, 
 (SELECT id FROM users WHERE username = 'owner1'), 
 (SELECT id FROM users WHERE username = 'owner1'), 120.00, true),
(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Immeuble El Manar'), 'B02', 0, 80.00, 
 NULL, NULL, 130.00, true);

-- Insert sample assemblies
INSERT INTO assemblies (id, building_id, title, description, scheduled_date, location, agenda, quorum_required, quorum_achieved, status, created_by) VALUES
(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins'), 
 'Assemblée Générale Ordinaire 2024', 
 'Assemblée générale ordinaire annuelle pour l''exercice 2024',
 '2024-12-15 14:00:00', 
 'Salle de réunion - Résidence Les Jasmins',
 'Ordre du jour:\n1. Rapport moral du président\n2. Rapport financier\n3. Budget prévisionnel 2025\n4. Travaux d''entretien\n5. Questions diverses',
 50, 0, 'SCHEDULED', 
 (SELECT id FROM users WHERE username = 'president1')),

(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins'), 
 'Assemblée Extraordinaire - Travaux Ascenseur', 
 'Assemblée extraordinaire pour décider des travaux de rénovation de l''ascenseur',
 '2024-11-20 18:00:00', 
 'Salle de réunion - Résidence Les Jasmins',
 'Ordre du jour:\n1. Présentation des devis\n2. Vote pour les travaux\n3. Modalités de financement',
 66, 75, 'COMPLETED', 
 (SELECT id FROM users WHERE username = 'president1'));

-- Insert sample payments
INSERT INTO payments (id, apartment_id, user_id, amount, payment_type, payment_method, payment_date, due_date, month_year, description, status, created_by) VALUES
-- Payments for apartment A101
(uuid_generate_v4(), 
 (SELECT id FROM apartments WHERE apartment_number = 'A101' AND building_id = (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins')), 
 (SELECT id FROM users WHERE username = 'owner1'), 
 150.00, 'MANUAL', 'BANK_TRANSFER', '2024-01-05 10:30:00', '2024-01-31 23:59:59', '2024-01', 
 'Charges mensuelles janvier 2024', 'COMPLETED', 
 (SELECT id FROM users WHERE username = 'admin')),

(uuid_generate_v4(), 
 (SELECT id FROM apartments WHERE apartment_number = 'A101' AND building_id = (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins')), 
 (SELECT id FROM users WHERE username = 'owner1'), 
 150.00, 'PAYMEE', 'ONLINE', '2024-02-03 14:15:00', '2024-02-29 23:59:59', '2024-02', 
 'Charges mensuelles février 2024', 'COMPLETED', 
 (SELECT id FROM users WHERE username = 'owner1')),

-- Payments for apartment A102
(uuid_generate_v4(), 
 (SELECT id FROM apartments WHERE apartment_number = 'A102' AND building_id = (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins')), 
 (SELECT id FROM users WHERE username = 'owner2'), 
 160.00, 'MANUAL', 'CASH', '2024-01-10 16:00:00', '2024-01-31 23:59:59', '2024-01', 
 'Charges mensuelles janvier 2024', 'COMPLETED', 
 (SELECT id FROM users WHERE username = 'admin'));

-- Insert sample expenses
INSERT INTO expenses (id, building_id, category, supplier, amount, expense_date, description, invoice_number, status, created_by) VALUES
(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins'), 
 'Maintenance', 'Société de Nettoyage CLEAN', 450.00, '2024-01-15', 
 'Nettoyage mensuel des parties communes', 'CLEAN-2024-001', 'APPROVED', 
 (SELECT id FROM users WHERE username = 'admin')),

(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins'), 
 'Utilities', 'STEG', 280.50, '2024-01-20', 
 'Facture électricité parties communes', 'STEG-2024-001', 'APPROVED', 
 (SELECT id FROM users WHERE username = 'admin')),

(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins'), 
 'Repairs', 'Plomberie Ben Salah', 125.00, '2024-01-25', 
 'Réparation fuite d''eau hall d''entrée', 'PBS-2024-003', 'PENDING', 
 (SELECT id FROM users WHERE username = 'president1'));

-- Insert sample claims
INSERT INTO claims (id, building_id, apartment_id, submitted_by, title, description, category, priority, status) VALUES
(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins'), 
 (SELECT id FROM apartments WHERE apartment_number = 'A101' AND building_id = (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins')), 
 (SELECT id FROM users WHERE username = 'owner1'), 
 'Problème d''éclairage dans le hall', 
 'L''éclairage du hall d''entrée ne fonctionne plus depuis 3 jours. Plusieurs ampoules sont grillées.', 
 'MAINTENANCE', 'MEDIUM', 'PENDING'),

(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins'), 
 (SELECT id FROM apartments WHERE apartment_number = 'A102' AND building_id = (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins')), 
 (SELECT id FROM users WHERE username = 'resident1'), 
 'Bruit excessif appartement du dessus', 
 'Bruit excessif provenant de l''appartement du 3ème étage tous les soirs après 22h.', 
 'NOISE', 'HIGH', 'IN_PROGRESS'),

(uuid_generate_v4(), (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins'), 
 NULL, 
 (SELECT id FROM users WHERE username = 'resident2'), 
 'Problème de sécurité - Porte d''entrée', 
 'La porte d''entrée principale ne se ferme pas correctement, problème de sécurité.', 
 'SECURITY', 'URGENT', 'RESOLVED');

-- Insert sample assembly attendance
INSERT INTO assembly_attendance (id, assembly_id, user_id, apartment_id, attended, attendance_time) VALUES
-- For completed assembly
(uuid_generate_v4(), 
 (SELECT id FROM assemblies WHERE title = 'Assemblée Extraordinaire - Travaux Ascenseur'), 
 (SELECT id FROM users WHERE username = 'owner1'), 
 (SELECT id FROM apartments WHERE apartment_number = 'A101' AND building_id = (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins')), 
 true, '2024-11-20 18:05:00'),

(uuid_generate_v4(), 
 (SELECT id FROM assemblies WHERE title = 'Assemblée Extraordinaire - Travaux Ascenseur'), 
 (SELECT id FROM users WHERE username = 'owner2'), 
 (SELECT id FROM apartments WHERE apartment_number = 'A102' AND building_id = (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins')), 
 true, '2024-11-20 18:10:00'),

(uuid_generate_v4(), 
 (SELECT id FROM assemblies WHERE title = 'Assemblée Extraordinaire - Travaux Ascenseur'), 
 (SELECT id FROM users WHERE username = 'resident2'), 
 (SELECT id FROM apartments WHERE apartment_number = 'A201' AND building_id = (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins')), 
 false, NULL);

-- Insert sample assembly votes
INSERT INTO assembly_votes (id, assembly_id, user_id, apartment_id, agenda_item, vote, comments) VALUES
(uuid_generate_v4(), 
 (SELECT id FROM assemblies WHERE title = 'Assemblée Extraordinaire - Travaux Ascenseur'), 
 (SELECT id FROM users WHERE username = 'owner1'), 
 (SELECT id FROM apartments WHERE apartment_number = 'A101' AND building_id = (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins')), 
 'Travaux de rénovation ascenseur', 'YES', 'Nécessaire pour la sécurité'),

(uuid_generate_v4(), 
 (SELECT id FROM assemblies WHERE title = 'Assemblée Extraordinaire - Travaux Ascenseur'), 
 (SELECT id FROM users WHERE username = 'owner2'), 
 (SELECT id FROM apartments WHERE apartment_number = 'A102' AND building_id = (SELECT id FROM buildings WHERE name = 'Résidence Les Jasmins')), 
 'Travaux de rénovation ascenseur', 'YES', 'D''accord avec le devis proposé');

-- Update assembly quorum achieved based on attendance
UPDATE assemblies 
SET quorum_achieved = (
    SELECT ROUND((COUNT(CASE WHEN aa.attended = true THEN 1 END) * 100.0 / COUNT(*))::numeric, 0)::integer
    FROM assembly_attendance aa 
    WHERE aa.assembly_id = assemblies.id
)
WHERE id IN (SELECT id FROM assemblies WHERE title = 'Assemblée Extraordinaire - Travaux Ascenseur');
