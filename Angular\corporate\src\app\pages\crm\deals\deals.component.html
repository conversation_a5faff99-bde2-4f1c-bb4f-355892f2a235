<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Deals" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="card">
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-3">
        <div class="search-box">
          <input type="text" class="form-control search" placeholder="Search for deals...">
          <i class="ri-search-line search-icon"></i>
        </div>
      </div>
      <!--end col-->
      <div class="col-md-auto ms-auto">
        <div class="d-flex hastck gap-2 flex-wrap">
          <div class="d-flex align-items-center gap-2">
            <span class="text-muted text-nowrap">Sort by: </span>
            <select class="form-control mb-0" data-choices data-choices-search-false id="choices-single-default">
              <option value="Owner">Owner</option>
              <option value="Company">Company</option>
              <option value="Date">Date</option>
            </select>
          </div>
          <button data-bs-toggle="modal" data-bs-target="#adddeals" class="btn btn-secondary"
            (click)="openModal(content)"><i class="ri-add-fill align-bottom me-1"></i> Add Deals</button>
          <div class="dropdown" ngbDropdown>
            <button class="btn btn-soft-primary btn-icon fs-14 arrow-none d-block" type="button"
              id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle>
              <i class="ri-settings-4-line"></i>
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1" ngbDropdownMenu>
              <li><a class="dropdown-item" href="javascript:void(0);">Copy</a></li>
              <li><a class="dropdown-item" href="javascript:void(0);">Move to pipline</a></li>
              <li><a class="dropdown-item" href="javascript:void(0);">Add to exceptions</a></li>
              <li><a class="dropdown-item" href="javascript:void(0);">Switch to common form view</a></li>
              <li><a class="dropdown-item" href="javascript:void(0);">Reset form view to default</a></li>
            </ul>
          </div>
        </div>
      </div>
      <!--end col-->
    </div>
    <!--end row-->
  </div>
</div>
<!--end card-->

<div class="row row-cols-xxl-5 row-cols-lg-3 row-cols-md-2 row-cols-1">
  <div class="col">
    <div ngbAccordion class="kanban-accordion" activeIds="lead-1" [closeOthers]="true">
      <div ngbAccordionItem id="lead-1" [collapsed]="false">

        <div ngbAccordionHeader>
          <button ngbAccordionButton class="card-header w-100 bg-danger-subtle p-3 text-start d-block"
            data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="leadDiscovered">
            <h5 class="card-title text-uppercase mb-1 fs-14">Lead Discovered</h5>
            <p class="text-muted mb-0">$265,200 <span class="fw-medium">4 Deals</span></p>
          </button>
        </div>
        <div ngbAccordionCollapse>
          <div ngbAccordionBody>
            <div ngbAccordion activeIds="static-3" [closeOthers]="true">
              <div ngbAccordionItem id="static-1">
                <div ngbAccordionHeader>
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="leadDiscovered1">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-1.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Managing sales team meeting</h6>
                      <p class="text-muted mb-0">$87k - 01 Jan, 2022</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>

              <div ngbAccordionItem id="static-2">
                <div ngbAccordionHeader class="accordion-header card">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="leadDiscovered2">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-2.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Airbnb React Development</h6>
                      <p class="text-muted mb-0">$20.3k - 24 Dec, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>

              <div ngbAccordionItem id="static-3" [collapsed]="false">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="leadDiscovered3">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-3.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Discovery Capital</h6>
                      <p class="text-muted mb-0">$124.3k - 29 Dec, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>

              <div ngbAccordionItem id="static-4">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="leadDiscovered4">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-4.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Airbnb React Development</h6>
                      <p class="text-muted mb-0">$33.6k - 24 Dec, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--end col-->

  <div class="col">
    <div ngbAccordion class="kanban-accordion" activeIds="lead-1" [closeOthers]="true">
      <div ngbAccordionItem id="lead-1" [collapsed]="false">
        <div ngbAccordionHeader>
          <button ngbAccordionButton class="card-header w-100 bg-success-subtle p-3 text-start d-block"
            data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="contactInitiated">
            <h5 class="card-title text-uppercase mb-1 fs-14">Contact Initiated</h5>
            <p class="text-muted mb-0">$108,700 <span class="fw-medium">5 Deals</span></p>
          </button>
        </div>
        <div ngbAccordionCollapse>
          <div ngbAccordionBody>
            <div ngbAccordion [closeOthers]="true">
              <div ngbAccordionItem id="static-1">
                <div ngbAccordionHeader>
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="contactInitiated1">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-5.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Custom Mobile Apps</h6>
                      <p class="text-muted mb-0">$28.7k - 13 Dec, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-2">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="contactInitiated2">
                    <div class="flex-shrink-0">
                      <img src="assets/images/brands/github.png" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Investment Deal for Zoetic Fashion</h6>
                      <p class="text-muted mb-0">$32.8k - 10 Oct, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-3">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="contactInitiated3">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-6.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Modern Design</h6>
                      <p class="text-muted mb-0">$23k - 03 Oct, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-4">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="contactInitiated5">
                    <div class="flex-shrink-0">
                      <img src="assets/images/brands/mail_chimp.png" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Managing Sales</h6>
                      <p class="text-muted mb-0">$13.3k - 04 Sep, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--end col-->

  <div class="col">
    <div ngbAccordion class="kanban-accordion" activeIds="lead-1" [closeOthers]="true">
      <div ngbAccordionItem id="lead-1" [collapsed]="false">
        <div ngbAccordionHeader>
          <button ngbAccordionButton class="card-header w-100 bg-warning-subtle p-3 text-start d-block"
            data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="needsIdentified">
            <h5 class="card-title text-uppercase mb-1 fs-14">Needs Identified</h5>
            <p class="text-muted mb-0">$708,200 <span class="fw-medium">7 Deals</span></p>
          </button>
        </div>
        <div ngbAccordionCollapse>
          <div ngbAccordionBody>
            <div ngbAccordion [closeOthers]="true">
              <div ngbAccordionItem id="static-1">
                <div ngbAccordionHeader>
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="needsIdentified1">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-9.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Art Studio Design</h6>
                      <p class="text-muted mb-0">$147.5k - 24 Sep, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-2">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="needsIdentified2">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-8.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Billing Page Bug</h6>
                      <p class="text-muted mb-0">$15.8k - 17 Dec, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-3">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="needsIdentified3">
                    <div class="flex-shrink-0">
                      <img src="assets/images/brands/dribbble.png" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Food Selection Platform</h6>
                      <p class="text-muted mb-0">$72.5k - 04 Jan, 2022</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-4">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="needsIdentified4">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-1.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Skote React Development</h6>
                      <p class="text-muted mb-0">$89.8 - 21 Nov, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-5">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="needsIdentified5">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-2.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Velzon - Admin Dashboard</h6>
                      <p class="text-muted mb-0">$126.7k - 30 Dec, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-6">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="needsIdentified6">
                    <div class="flex-shrink-0">
                      <img src="assets/images/companies/img-6.png" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Wood Elements Design</h6>
                      <p class="text-muted mb-0">$120.2k - 24 Nov, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-7">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="needsIdentified7">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-10.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">PayPal SEO audit</h6>
                      <p class="text-muted mb-0">$135.7k - 23 Nov, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--end col-->

  <div class="col">
    <div ngbAccordion class="kanban-accordion" activeIds="lead-1" [closeOthers]="true">
      <div ngbAccordionItem id="lead-1"  [collapsed]="false">
        <div ngbAccordionHeader>
          <button ngbAccordionButton class="card-header w-100 bg-info-subtle  p-3 text-start d-block"
            data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="meetingArranged">
            <h5 class="card-title text-uppercase mb-1 fs-14">Meeting Arranged</h5>
            <p class="text-muted mb-0">$44,900 <span class="fw-medium">3 Deals</span></p>
          </button>
        </div>
        <div ngbAccordionCollapse>
          <div ngbAccordionBody>
            <div ngbAccordion [closeOthers]="true">
              <div ngbAccordionItem id="static-1">
                <div ngbAccordionHeader>
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="meetingArranged1">
                    <div class="flex-shrink-0">
                      <img src="assets/images/companies/img-5.png" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">SASS app workflow diagram</h6>
                      <p class="text-muted mb-0">$17.8k - 01 Jan, 2022</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-2">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    role="button" aria-expanded="false" aria-controls="meetingArranged2">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-3.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Uber new brand system</h6>
                      <p class="text-muted mb-0">$24.5k - 22 Dec, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-3">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse"
                    href="javascript:void(0);" role="button" aria-expanded="false" aria-controls="meetingArranged3">
                    <div class="flex-shrink-0">
                      <img src="assets/images/companies/img-8.png" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">TripAdvisor</h6>
                      <p class="text-muted mb-0">$2.6k - 12 Dec, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                    <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--end col-->

  <div class="col">
    <div ngbAccordion class="kanban-accordion" activeIds="lead-1" [closeOthers]="true">
      <div ngbAccordionItem id="lead-1" [collapsed]="false">
        <div ngbAccordionHeader>
          <button ngbAccordionButton class="card-header w-100 bg-secondary-subtle p-3 text-start d-block"
            data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="offerAccepted">
            <h5 class="card-title text-uppercase mb-1 fs-14">Offer Accepted</h5>
            <p class="text-muted mb-0">$819,300 <span class="fw-medium">8 Deals</span></p>
          </button>
        </div>
        <div ngbAccordionCollapse>
          <div ngbAccordionBody>
            <div ngbAccordion [closeOthers]="true">
              <div ngbAccordionItem id="static-1">
                <div ngbAccordionHeader>
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse" role="button"
                    aria-expanded="false" aria-controls="offerAccepted1">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-10.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Coupon Website</h6>
                      <p class="text-muted mb-0">$27.4k - 07 Jan, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                  <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-2">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse" role="button"
                    aria-expanded="false" aria-controls="offerAccepted2">
                    <div class="flex-shrink-0">
                      <img src="assets/images/brands/slack.png" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Marketing Automation Demo</h6>
                      <p class="text-muted mb-0">$94.8 - 19 Nov, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                  <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-3">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse" role="button"
                    aria-expanded="false" aria-controls="offerAccepted3">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-4.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">New Email Design Templates</h6>
                      <p class="text-muted mb-0">$136.9k - 05 Jan, 2022</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                  <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-4">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse" role="button"
                    aria-expanded="false" aria-controls="offerAccepted4">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-7.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Create New Components</h6>
                      <p class="text-muted mb-0">$45.9k - 26 Dec, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                  <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-5">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse" role="button"
                    aria-expanded="false" aria-controls="offerAccepted5">
                    <div class="flex-shrink-0">
                      <img src="assets/images/companies/img-3.png" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">New Test Tickets</h6>
                      <p class="text-muted mb-0">$118k - 01 Jan, 2022</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                  <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-6">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse" role="button"
                    aria-expanded="false" aria-controls="offerAccepted6">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-6.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Recover Deleted Folder</h6>
                      <p class="text-muted mb-0">$87.3k - 03 Jan, 2022</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                  <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-7">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse" role="button"
                    aria-expanded="false" aria-controls="offerAccepted7">
                    <div class="flex-shrink-0">
                      <img src="assets/images/brands/github.png" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Github SEO audit</h6>
                      <p class="text-muted mb-0">$241.2k - 21 Sep, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                  <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
              <div ngbAccordionItem id="static-8">
                <div ngbAccordionHeader class="accordion-header">
                  <button ngbAccordionButton class="d-flex align-items-center text-start" data-bs-toggle="collapse" role="button"
                    aria-expanded="false" aria-controls="offerAccepted8">
                    <div class="flex-shrink-0">
                      <img src="assets/images/users/avatar-2.jpg" alt="" class="avatar-xs rounded-circle" />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-13 mb-1">Urban Modern Design</h6>
                      <p class="text-muted mb-0">$67.8k - 09 Oct, 2021</p>
                    </div>
                  </button>
                </div>
                <div ngbAccordionCollapse>
                  <div ngbAccordionBody>
                  <ng-template [ngTemplateOutlet]="TabContent"></ng-template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--end col-->
</div>
<!--end row-->

<!-- Modal -->
<ng-template #content role="document" let-modal>
  <div class="modal-header bg-light p-3">
    <h5 class="modal-title" id="exampleModalLabel">Create Deals</h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
      (click)="modal.dismiss('Cross click')"></button>
  </div>
  <form>
    <div class="modal-body">
      <div class="mb-3">
        <label for="dealTitle" class="form-label">Deal Title</label>
        <input type="email" class="form-control" id="dealTitle" placeholder="Enter title">
      </div>
      <div class="mb-3">
        <label for="dealValue" class="form-label">Value (USD)</label>
        <input type="text" class="form-control" id="dealValue" placeholder="Enter value">
      </div>
      <div class="mb-3">
        <label for="dealOwner" class="form-label">Deals Owner</label>
        <input type="text" class="form-control" id="dealOwner" placeholder="Enter owner name">
      </div>
      <div class="mb-3">
        <label for="dueDate" class="form-label">Due Date</label>
        <input class="form-control flatpickr-input" type="text" mwlFlatpickr [altInput]="true"
          [convertModelValue]="true" placeholder="Select date">
      </div>
      <div class="mb-3">
        <label for="contactNumber" class="form-label">Contact</label>
        <input type="text" class="form-control" id="contactNumber" placeholder="Enter contact number">
      </div>
      <div class="mb-3">
        <label for="contactNumber" class="form-label">Description</label>
        <textarea class="form-control" id="exampleFormControlTextarea1" rows="3"
          placeholder="Enter description"></textarea>
      </div>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-light" data-bs-dismiss="modal"
        (click)="modal.close('Close click')">Close</button>
      <button type="button" class="btn btn-primary"><i class="ri-save-line align-bottom me-1"></i> Save</button>
    </div>
  </form>
</ng-template>

<ng-template #TabContent>
  <div class="card-body p-3">
    <h6 class="fs-14 mb-1">Nesta Technologies <small class="badge bg-danger-subtle text-danger">4 Days</small></h6>
    <p class="text-muted">As a company grows however, you find it's not as easy to shout across</p>
    <ul class="list-unstyled vstack gap-2 mb-0">
      <li>
        <div class="d-flex">
          <div class="flex-shrink-0 avatar-xxs text-muted">
            <i class="ri-question-answer-line"></i>
          </div>
          <div class="flex-grow-1">
            <h6 class="mb-0">Meeting with Thomas</h6>
            <small class="text-muted">Yesterday at 9:12AM</small>
          </div>
        </div>
      </li>
      <li>
        <div class="d-flex">
          <div class="flex-shrink-0 avatar-xxs text-muted">
            <i class="ri-mac-line"></i>
          </div>
          <div class="flex-grow-1">
            <h6 class="mb-0">Product Demo</h6>
            <small class="text-muted">Monday at 04:41PM</small>
          </div>
        </div>
      </li>
      <li>
        <div class="d-flex">
          <div class="flex-shrink-0 avatar-xxs text-muted">
            <i class="ri-earth-line"></i>
          </div>
          <div class="flex-grow-1">
            <h6 class="mb-0">Marketing Team Meeting</h6>
            <small class="text-muted">Monday at 04:41PM</small>
          </div>
        </div>
      </li>
    </ul>
  </div>
  <div class="card-footer hstack gap-2 p-3">
    <button class="btn btn-primary btn-sm w-100"><i class="ri-phone-line align-bottom me-1"></i> Call</button>
    <button class="btn btn-secondary btn-sm w-100"><i class="ri-question-answer-line align-bottom me-1"></i>
      Message</button>
  </div>
</ng-template>