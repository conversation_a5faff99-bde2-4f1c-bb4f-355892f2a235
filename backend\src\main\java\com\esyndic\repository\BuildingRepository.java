package com.esyndic.repository;

import com.esyndic.entity.Building;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface BuildingRepository extends JpaRepository<Building, UUID> {

    List<Building> findByIsActiveTrue();

    List<Building> findByIsActiveFalse();

    List<Building> findByAdminId(UUID adminId);

    List<Building> findByPresidentId(UUID presidentId);

    List<Building> findByCity(String city);

    @Query("SELECT b FROM Building b WHERE b.name LIKE %:name%")
    List<Building> findByNameContaining(@Param("name") String name);

    @Query("SELECT b FROM Building b WHERE b.address LIKE %:address%")
    List<Building> findByAddressContaining(@Param("address") String address);

    @Query("SELECT b FROM Building b WHERE b.city LIKE %:city%")
    List<Building> findByCityContaining(@Param("city") String city);

    @Query("SELECT COUNT(b) FROM Building b WHERE b.isActive = true")
    long countActiveBuildings();

    @Query("SELECT b FROM Building b WHERE b.admin.id = :userId OR b.president.id = :userId")
    List<Building> findBuildingsByUserRole(@Param("userId") UUID userId);

    @Query("SELECT DISTINCT b FROM Building b JOIN b.apartments a WHERE a.owner.id = :userId OR a.resident.id = :userId")
    List<Building> findBuildingsByUserApartments(@Param("userId") UUID userId);

    @Query("SELECT b FROM Building b WHERE b.totalApartments >= :minApartments AND b.totalApartments <= :maxApartments")
    List<Building> findByApartmentCountRange(@Param("minApartments") Integer minApartments, 
                                           @Param("maxApartments") Integer maxApartments);

    boolean existsByNameAndCity(String name, String city);
}
