{"ast": null, "code": "import baseClamp from './_baseClamp.js';\nimport toInteger from './toInteger.js';\n\n/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/**\n * Converts `value` to a safe integer. A safe integer can be compared and\n * represented correctly.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toSafeInteger(3.2);\n * // => 3\n *\n * _.toSafeInteger(Number.MIN_VALUE);\n * // => 0\n *\n * _.toSafeInteger(Infinity);\n * // => 9007199254740991\n *\n * _.toSafeInteger('3.2');\n * // => 3\n */\nfunction toSafeInteger(value) {\n  return value ? baseClamp(toInteger(value), -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER) : value === 0 ? value : 0;\n}\nexport default toSafeInteger;", "map": {"version": 3, "names": ["baseClamp", "toInteger", "MAX_SAFE_INTEGER", "toSafeInteger", "value"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/toSafeInteger.js"], "sourcesContent": ["import baseClamp from './_baseClamp.js';\nimport toInteger from './toInteger.js';\n\n/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/**\n * Converts `value` to a safe integer. A safe integer can be compared and\n * represented correctly.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toSafeInteger(3.2);\n * // => 3\n *\n * _.toSafeInteger(Number.MIN_VALUE);\n * // => 0\n *\n * _.toSafeInteger(Infinity);\n * // => 9007199254740991\n *\n * _.toSafeInteger('3.2');\n * // => 3\n */\nfunction toSafeInteger(value) {\n  return value\n    ? baseClamp(toInteger(value), -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER)\n    : (value === 0 ? value : 0);\n}\n\nexport default toSafeInteger;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,gBAAgB,GAAG,gBAAgB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,GACRJ,SAAS,CAACC,SAAS,CAACG,KAAK,CAAC,EAAE,CAACF,gBAAgB,EAAEA,gBAAgB,CAAC,GAC/DE,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAE;AAC/B;AAEA,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}