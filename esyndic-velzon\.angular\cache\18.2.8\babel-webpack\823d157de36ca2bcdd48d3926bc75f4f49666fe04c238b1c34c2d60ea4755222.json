{"ast": null, "code": "import baseIndexOf from './_baseIndexOf.js';\n\n/**\n * A specialized version of `_.includes` for arrays without support for\n * specifying an index to search from.\n *\n * @private\n * @param {Array} [array] The array to inspect.\n * @param {*} target The value to search for.\n * @returns {boolean} Returns `true` if `target` is found, else `false`.\n */\nfunction arrayIncludes(array, value) {\n  var length = array == null ? 0 : array.length;\n  return !!length && baseIndexOf(array, value, 0) > -1;\n}\nexport default arrayIncludes;", "map": {"version": 3, "names": ["baseIndexOf", "arrayIncludes", "array", "value", "length"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_arrayIncludes.js"], "sourcesContent": ["import baseIndexOf from './_baseIndexOf.js';\n\n/**\n * A specialized version of `_.includes` for arrays without support for\n * specifying an index to search from.\n *\n * @private\n * @param {Array} [array] The array to inspect.\n * @param {*} target The value to search for.\n * @returns {boolean} Returns `true` if `target` is found, else `false`.\n */\nfunction arrayIncludes(array, value) {\n  var length = array == null ? 0 : array.length;\n  return !!length && baseIndexOf(array, value, 0) > -1;\n}\n\nexport default arrayIncludes;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACnC,IAAIC,MAAM,GAAGF,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACE,MAAM;EAC7C,OAAO,CAAC,CAACA,MAAM,IAAIJ,WAAW,CAACE,KAAK,EAAEC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACtD;AAEA,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}