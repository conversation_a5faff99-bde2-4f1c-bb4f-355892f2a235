{"ast": null, "code": "import LRU from '../core/LRU.js';\nimport { extend, isGradientObject, isString, map } from '../core/util.js';\nvar kCSSColorTable = {\n  'transparent': [0, 0, 0, 0],\n  'aliceblue': [240, 248, 255, 1],\n  'antiquewhite': [250, 235, 215, 1],\n  'aqua': [0, 255, 255, 1],\n  'aquamarine': [127, 255, 212, 1],\n  'azure': [240, 255, 255, 1],\n  'beige': [245, 245, 220, 1],\n  'bisque': [255, 228, 196, 1],\n  'black': [0, 0, 0, 1],\n  'blanchedalmond': [255, 235, 205, 1],\n  'blue': [0, 0, 255, 1],\n  'blueviolet': [138, 43, 226, 1],\n  'brown': [165, 42, 42, 1],\n  'burlywood': [222, 184, 135, 1],\n  'cadetblue': [95, 158, 160, 1],\n  'chartreuse': [127, 255, 0, 1],\n  'chocolate': [210, 105, 30, 1],\n  'coral': [255, 127, 80, 1],\n  'cornflowerblue': [100, 149, 237, 1],\n  'cornsilk': [255, 248, 220, 1],\n  'crimson': [220, 20, 60, 1],\n  'cyan': [0, 255, 255, 1],\n  'darkblue': [0, 0, 139, 1],\n  'darkcyan': [0, 139, 139, 1],\n  'darkgoldenrod': [184, 134, 11, 1],\n  'darkgray': [169, 169, 169, 1],\n  'darkgreen': [0, 100, 0, 1],\n  'darkgrey': [169, 169, 169, 1],\n  'darkkhaki': [189, 183, 107, 1],\n  'darkmagenta': [139, 0, 139, 1],\n  'darkolivegreen': [85, 107, 47, 1],\n  'darkorange': [255, 140, 0, 1],\n  'darkorchid': [153, 50, 204, 1],\n  'darkred': [139, 0, 0, 1],\n  'darksalmon': [233, 150, 122, 1],\n  'darkseagreen': [143, 188, 143, 1],\n  'darkslateblue': [72, 61, 139, 1],\n  'darkslategray': [47, 79, 79, 1],\n  'darkslategrey': [47, 79, 79, 1],\n  'darkturquoise': [0, 206, 209, 1],\n  'darkviolet': [148, 0, 211, 1],\n  'deeppink': [255, 20, 147, 1],\n  'deepskyblue': [0, 191, 255, 1],\n  'dimgray': [105, 105, 105, 1],\n  'dimgrey': [105, 105, 105, 1],\n  'dodgerblue': [30, 144, 255, 1],\n  'firebrick': [178, 34, 34, 1],\n  'floralwhite': [255, 250, 240, 1],\n  'forestgreen': [34, 139, 34, 1],\n  'fuchsia': [255, 0, 255, 1],\n  'gainsboro': [220, 220, 220, 1],\n  'ghostwhite': [248, 248, 255, 1],\n  'gold': [255, 215, 0, 1],\n  'goldenrod': [218, 165, 32, 1],\n  'gray': [128, 128, 128, 1],\n  'green': [0, 128, 0, 1],\n  'greenyellow': [173, 255, 47, 1],\n  'grey': [128, 128, 128, 1],\n  'honeydew': [240, 255, 240, 1],\n  'hotpink': [255, 105, 180, 1],\n  'indianred': [205, 92, 92, 1],\n  'indigo': [75, 0, 130, 1],\n  'ivory': [255, 255, 240, 1],\n  'khaki': [240, 230, 140, 1],\n  'lavender': [230, 230, 250, 1],\n  'lavenderblush': [255, 240, 245, 1],\n  'lawngreen': [124, 252, 0, 1],\n  'lemonchiffon': [255, 250, 205, 1],\n  'lightblue': [173, 216, 230, 1],\n  'lightcoral': [240, 128, 128, 1],\n  'lightcyan': [224, 255, 255, 1],\n  'lightgoldenrodyellow': [250, 250, 210, 1],\n  'lightgray': [211, 211, 211, 1],\n  'lightgreen': [144, 238, 144, 1],\n  'lightgrey': [211, 211, 211, 1],\n  'lightpink': [255, 182, 193, 1],\n  'lightsalmon': [255, 160, 122, 1],\n  'lightseagreen': [32, 178, 170, 1],\n  'lightskyblue': [135, 206, 250, 1],\n  'lightslategray': [119, 136, 153, 1],\n  'lightslategrey': [119, 136, 153, 1],\n  'lightsteelblue': [176, 196, 222, 1],\n  'lightyellow': [255, 255, 224, 1],\n  'lime': [0, 255, 0, 1],\n  'limegreen': [50, 205, 50, 1],\n  'linen': [250, 240, 230, 1],\n  'magenta': [255, 0, 255, 1],\n  'maroon': [128, 0, 0, 1],\n  'mediumaquamarine': [102, 205, 170, 1],\n  'mediumblue': [0, 0, 205, 1],\n  'mediumorchid': [186, 85, 211, 1],\n  'mediumpurple': [147, 112, 219, 1],\n  'mediumseagreen': [60, 179, 113, 1],\n  'mediumslateblue': [123, 104, 238, 1],\n  'mediumspringgreen': [0, 250, 154, 1],\n  'mediumturquoise': [72, 209, 204, 1],\n  'mediumvioletred': [199, 21, 133, 1],\n  'midnightblue': [25, 25, 112, 1],\n  'mintcream': [245, 255, 250, 1],\n  'mistyrose': [255, 228, 225, 1],\n  'moccasin': [255, 228, 181, 1],\n  'navajowhite': [255, 222, 173, 1],\n  'navy': [0, 0, 128, 1],\n  'oldlace': [253, 245, 230, 1],\n  'olive': [128, 128, 0, 1],\n  'olivedrab': [107, 142, 35, 1],\n  'orange': [255, 165, 0, 1],\n  'orangered': [255, 69, 0, 1],\n  'orchid': [218, 112, 214, 1],\n  'palegoldenrod': [238, 232, 170, 1],\n  'palegreen': [152, 251, 152, 1],\n  'paleturquoise': [175, 238, 238, 1],\n  'palevioletred': [219, 112, 147, 1],\n  'papayawhip': [255, 239, 213, 1],\n  'peachpuff': [255, 218, 185, 1],\n  'peru': [205, 133, 63, 1],\n  'pink': [255, 192, 203, 1],\n  'plum': [221, 160, 221, 1],\n  'powderblue': [176, 224, 230, 1],\n  'purple': [128, 0, 128, 1],\n  'red': [255, 0, 0, 1],\n  'rosybrown': [188, 143, 143, 1],\n  'royalblue': [65, 105, 225, 1],\n  'saddlebrown': [139, 69, 19, 1],\n  'salmon': [250, 128, 114, 1],\n  'sandybrown': [244, 164, 96, 1],\n  'seagreen': [46, 139, 87, 1],\n  'seashell': [255, 245, 238, 1],\n  'sienna': [160, 82, 45, 1],\n  'silver': [192, 192, 192, 1],\n  'skyblue': [135, 206, 235, 1],\n  'slateblue': [106, 90, 205, 1],\n  'slategray': [112, 128, 144, 1],\n  'slategrey': [112, 128, 144, 1],\n  'snow': [255, 250, 250, 1],\n  'springgreen': [0, 255, 127, 1],\n  'steelblue': [70, 130, 180, 1],\n  'tan': [210, 180, 140, 1],\n  'teal': [0, 128, 128, 1],\n  'thistle': [216, 191, 216, 1],\n  'tomato': [255, 99, 71, 1],\n  'turquoise': [64, 224, 208, 1],\n  'violet': [238, 130, 238, 1],\n  'wheat': [245, 222, 179, 1],\n  'white': [255, 255, 255, 1],\n  'whitesmoke': [245, 245, 245, 1],\n  'yellow': [255, 255, 0, 1],\n  'yellowgreen': [154, 205, 50, 1]\n};\nfunction clampCssByte(i) {\n  i = Math.round(i);\n  return i < 0 ? 0 : i > 255 ? 255 : i;\n}\nfunction clampCssAngle(i) {\n  i = Math.round(i);\n  return i < 0 ? 0 : i > 360 ? 360 : i;\n}\nfunction clampCssFloat(f) {\n  return f < 0 ? 0 : f > 1 ? 1 : f;\n}\nfunction parseCssInt(val) {\n  var str = val;\n  if (str.length && str.charAt(str.length - 1) === '%') {\n    return clampCssByte(parseFloat(str) / 100 * 255);\n  }\n  return clampCssByte(parseInt(str, 10));\n}\nfunction parseCssFloat(val) {\n  var str = val;\n  if (str.length && str.charAt(str.length - 1) === '%') {\n    return clampCssFloat(parseFloat(str) / 100);\n  }\n  return clampCssFloat(parseFloat(str));\n}\nfunction cssHueToRgb(m1, m2, h) {\n  if (h < 0) {\n    h += 1;\n  } else if (h > 1) {\n    h -= 1;\n  }\n  if (h * 6 < 1) {\n    return m1 + (m2 - m1) * h * 6;\n  }\n  if (h * 2 < 1) {\n    return m2;\n  }\n  if (h * 3 < 2) {\n    return m1 + (m2 - m1) * (2 / 3 - h) * 6;\n  }\n  return m1;\n}\nfunction lerpNumber(a, b, p) {\n  return a + (b - a) * p;\n}\nfunction setRgba(out, r, g, b, a) {\n  out[0] = r;\n  out[1] = g;\n  out[2] = b;\n  out[3] = a;\n  return out;\n}\nfunction copyRgba(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  return out;\n}\nvar colorCache = new LRU(20);\nvar lastRemovedArr = null;\nfunction putToCache(colorStr, rgbaArr) {\n  if (lastRemovedArr) {\n    copyRgba(lastRemovedArr, rgbaArr);\n  }\n  lastRemovedArr = colorCache.put(colorStr, lastRemovedArr || rgbaArr.slice());\n}\nexport function parse(colorStr, rgbaArr) {\n  if (!colorStr) {\n    return;\n  }\n  rgbaArr = rgbaArr || [];\n  var cached = colorCache.get(colorStr);\n  if (cached) {\n    return copyRgba(rgbaArr, cached);\n  }\n  colorStr = colorStr + '';\n  var str = colorStr.replace(/ /g, '').toLowerCase();\n  if (str in kCSSColorTable) {\n    copyRgba(rgbaArr, kCSSColorTable[str]);\n    putToCache(colorStr, rgbaArr);\n    return rgbaArr;\n  }\n  var strLen = str.length;\n  if (str.charAt(0) === '#') {\n    if (strLen === 4 || strLen === 5) {\n      var iv = parseInt(str.slice(1, 4), 16);\n      if (!(iv >= 0 && iv <= 0xfff)) {\n        setRgba(rgbaArr, 0, 0, 0, 1);\n        return;\n      }\n      setRgba(rgbaArr, (iv & 0xf00) >> 4 | (iv & 0xf00) >> 8, iv & 0xf0 | (iv & 0xf0) >> 4, iv & 0xf | (iv & 0xf) << 4, strLen === 5 ? parseInt(str.slice(4), 16) / 0xf : 1);\n      putToCache(colorStr, rgbaArr);\n      return rgbaArr;\n    } else if (strLen === 7 || strLen === 9) {\n      var iv = parseInt(str.slice(1, 7), 16);\n      if (!(iv >= 0 && iv <= 0xffffff)) {\n        setRgba(rgbaArr, 0, 0, 0, 1);\n        return;\n      }\n      setRgba(rgbaArr, (iv & 0xff0000) >> 16, (iv & 0xff00) >> 8, iv & 0xff, strLen === 9 ? parseInt(str.slice(7), 16) / 0xff : 1);\n      putToCache(colorStr, rgbaArr);\n      return rgbaArr;\n    }\n    return;\n  }\n  var op = str.indexOf('(');\n  var ep = str.indexOf(')');\n  if (op !== -1 && ep + 1 === strLen) {\n    var fname = str.substr(0, op);\n    var params = str.substr(op + 1, ep - (op + 1)).split(',');\n    var alpha = 1;\n    switch (fname) {\n      case 'rgba':\n        if (params.length !== 4) {\n          return params.length === 3 ? setRgba(rgbaArr, +params[0], +params[1], +params[2], 1) : setRgba(rgbaArr, 0, 0, 0, 1);\n        }\n        alpha = parseCssFloat(params.pop());\n      case 'rgb':\n        if (params.length >= 3) {\n          setRgba(rgbaArr, parseCssInt(params[0]), parseCssInt(params[1]), parseCssInt(params[2]), params.length === 3 ? alpha : parseCssFloat(params[3]));\n          putToCache(colorStr, rgbaArr);\n          return rgbaArr;\n        } else {\n          setRgba(rgbaArr, 0, 0, 0, 1);\n          return;\n        }\n      case 'hsla':\n        if (params.length !== 4) {\n          setRgba(rgbaArr, 0, 0, 0, 1);\n          return;\n        }\n        params[3] = parseCssFloat(params[3]);\n        hsla2rgba(params, rgbaArr);\n        putToCache(colorStr, rgbaArr);\n        return rgbaArr;\n      case 'hsl':\n        if (params.length !== 3) {\n          setRgba(rgbaArr, 0, 0, 0, 1);\n          return;\n        }\n        hsla2rgba(params, rgbaArr);\n        putToCache(colorStr, rgbaArr);\n        return rgbaArr;\n      default:\n        return;\n    }\n  }\n  setRgba(rgbaArr, 0, 0, 0, 1);\n  return;\n}\nfunction hsla2rgba(hsla, rgba) {\n  var h = (parseFloat(hsla[0]) % 360 + 360) % 360 / 360;\n  var s = parseCssFloat(hsla[1]);\n  var l = parseCssFloat(hsla[2]);\n  var m2 = l <= 0.5 ? l * (s + 1) : l + s - l * s;\n  var m1 = l * 2 - m2;\n  rgba = rgba || [];\n  setRgba(rgba, clampCssByte(cssHueToRgb(m1, m2, h + 1 / 3) * 255), clampCssByte(cssHueToRgb(m1, m2, h) * 255), clampCssByte(cssHueToRgb(m1, m2, h - 1 / 3) * 255), 1);\n  if (hsla.length === 4) {\n    rgba[3] = hsla[3];\n  }\n  return rgba;\n}\nfunction rgba2hsla(rgba) {\n  if (!rgba) {\n    return;\n  }\n  var R = rgba[0] / 255;\n  var G = rgba[1] / 255;\n  var B = rgba[2] / 255;\n  var vMin = Math.min(R, G, B);\n  var vMax = Math.max(R, G, B);\n  var delta = vMax - vMin;\n  var L = (vMax + vMin) / 2;\n  var H;\n  var S;\n  if (delta === 0) {\n    H = 0;\n    S = 0;\n  } else {\n    if (L < 0.5) {\n      S = delta / (vMax + vMin);\n    } else {\n      S = delta / (2 - vMax - vMin);\n    }\n    var deltaR = ((vMax - R) / 6 + delta / 2) / delta;\n    var deltaG = ((vMax - G) / 6 + delta / 2) / delta;\n    var deltaB = ((vMax - B) / 6 + delta / 2) / delta;\n    if (R === vMax) {\n      H = deltaB - deltaG;\n    } else if (G === vMax) {\n      H = 1 / 3 + deltaR - deltaB;\n    } else if (B === vMax) {\n      H = 2 / 3 + deltaG - deltaR;\n    }\n    if (H < 0) {\n      H += 1;\n    }\n    if (H > 1) {\n      H -= 1;\n    }\n  }\n  var hsla = [H * 360, S, L];\n  if (rgba[3] != null) {\n    hsla.push(rgba[3]);\n  }\n  return hsla;\n}\nexport function lift(color, level) {\n  var colorArr = parse(color);\n  if (colorArr) {\n    for (var i = 0; i < 3; i++) {\n      if (level < 0) {\n        colorArr[i] = colorArr[i] * (1 - level) | 0;\n      } else {\n        colorArr[i] = (255 - colorArr[i]) * level + colorArr[i] | 0;\n      }\n      if (colorArr[i] > 255) {\n        colorArr[i] = 255;\n      } else if (colorArr[i] < 0) {\n        colorArr[i] = 0;\n      }\n    }\n    return stringify(colorArr, colorArr.length === 4 ? 'rgba' : 'rgb');\n  }\n}\nexport function toHex(color) {\n  var colorArr = parse(color);\n  if (colorArr) {\n    return ((1 << 24) + (colorArr[0] << 16) + (colorArr[1] << 8) + +colorArr[2]).toString(16).slice(1);\n  }\n}\nexport function fastLerp(normalizedValue, colors, out) {\n  if (!(colors && colors.length) || !(normalizedValue >= 0 && normalizedValue <= 1)) {\n    return;\n  }\n  out = out || [];\n  var value = normalizedValue * (colors.length - 1);\n  var leftIndex = Math.floor(value);\n  var rightIndex = Math.ceil(value);\n  var leftColor = colors[leftIndex];\n  var rightColor = colors[rightIndex];\n  var dv = value - leftIndex;\n  out[0] = clampCssByte(lerpNumber(leftColor[0], rightColor[0], dv));\n  out[1] = clampCssByte(lerpNumber(leftColor[1], rightColor[1], dv));\n  out[2] = clampCssByte(lerpNumber(leftColor[2], rightColor[2], dv));\n  out[3] = clampCssFloat(lerpNumber(leftColor[3], rightColor[3], dv));\n  return out;\n}\nexport var fastMapToColor = fastLerp;\nexport function lerp(normalizedValue, colors, fullOutput) {\n  if (!(colors && colors.length) || !(normalizedValue >= 0 && normalizedValue <= 1)) {\n    return;\n  }\n  var value = normalizedValue * (colors.length - 1);\n  var leftIndex = Math.floor(value);\n  var rightIndex = Math.ceil(value);\n  var leftColor = parse(colors[leftIndex]);\n  var rightColor = parse(colors[rightIndex]);\n  var dv = value - leftIndex;\n  var color = stringify([clampCssByte(lerpNumber(leftColor[0], rightColor[0], dv)), clampCssByte(lerpNumber(leftColor[1], rightColor[1], dv)), clampCssByte(lerpNumber(leftColor[2], rightColor[2], dv)), clampCssFloat(lerpNumber(leftColor[3], rightColor[3], dv))], 'rgba');\n  return fullOutput ? {\n    color: color,\n    leftIndex: leftIndex,\n    rightIndex: rightIndex,\n    value: value\n  } : color;\n}\nexport var mapToColor = lerp;\nexport function modifyHSL(color, h, s, l) {\n  var colorArr = parse(color);\n  if (color) {\n    colorArr = rgba2hsla(colorArr);\n    h != null && (colorArr[0] = clampCssAngle(h));\n    s != null && (colorArr[1] = parseCssFloat(s));\n    l != null && (colorArr[2] = parseCssFloat(l));\n    return stringify(hsla2rgba(colorArr), 'rgba');\n  }\n}\nexport function modifyAlpha(color, alpha) {\n  var colorArr = parse(color);\n  if (colorArr && alpha != null) {\n    colorArr[3] = clampCssFloat(alpha);\n    return stringify(colorArr, 'rgba');\n  }\n}\nexport function stringify(arrColor, type) {\n  if (!arrColor || !arrColor.length) {\n    return;\n  }\n  var colorStr = arrColor[0] + ',' + arrColor[1] + ',' + arrColor[2];\n  if (type === 'rgba' || type === 'hsva' || type === 'hsla') {\n    colorStr += ',' + arrColor[3];\n  }\n  return type + '(' + colorStr + ')';\n}\nexport function lum(color, backgroundLum) {\n  var arr = parse(color);\n  return arr ? (0.299 * arr[0] + 0.587 * arr[1] + 0.114 * arr[2]) * arr[3] / 255 + (1 - arr[3]) * backgroundLum : 0;\n}\nexport function random() {\n  return stringify([Math.round(Math.random() * 255), Math.round(Math.random() * 255), Math.round(Math.random() * 255)], 'rgb');\n}\nvar liftedColorCache = new LRU(100);\nexport function liftColor(color) {\n  if (isString(color)) {\n    var liftedColor = liftedColorCache.get(color);\n    if (!liftedColor) {\n      liftedColor = lift(color, -0.1);\n      liftedColorCache.put(color, liftedColor);\n    }\n    return liftedColor;\n  } else if (isGradientObject(color)) {\n    var ret = extend({}, color);\n    ret.colorStops = map(color.colorStops, function (stop) {\n      return {\n        offset: stop.offset,\n        color: lift(stop.color, -0.1)\n      };\n    });\n    return ret;\n  }\n  return color;\n}", "map": {"version": 3, "names": ["LRU", "extend", "isGradientObject", "isString", "map", "kCSSColorTable", "clampCssByte", "i", "Math", "round", "clampCssAngle", "clampCssFloat", "f", "parseCssInt", "val", "str", "length", "char<PERSON>t", "parseFloat", "parseInt", "parseCssFloat", "cssHueToRgb", "m1", "m2", "h", "lerpNumber", "a", "b", "p", "setRgba", "out", "r", "g", "copyRgba", "colorCache", "lastRemovedArr", "put<PERSON><PERSON><PERSON><PERSON>", "colorStr", "rgbaArr", "put", "slice", "parse", "cached", "get", "replace", "toLowerCase", "strLen", "iv", "op", "indexOf", "ep", "fname", "substr", "params", "split", "alpha", "pop", "hsla2rgba", "hsla", "rgba", "s", "l", "rgba2hsla", "R", "G", "B", "vMin", "min", "vMax", "max", "delta", "L", "H", "S", "deltaR", "deltaG", "deltaB", "push", "lift", "color", "level", "colorArr", "stringify", "toHex", "toString", "fastLerp", "normalizedValue", "colors", "value", "leftIndex", "floor", "rightIndex", "ceil", "leftColor", "rightColor", "dv", "fastMapToColor", "lerp", "fullOutput", "mapToColor", "modifyHSL", "modifyAlpha", "arrColor", "type", "lum", "backgroundLum", "arr", "random", "liftedColorCache", "liftColor", "liftedColor", "ret", "colorStops", "stop", "offset"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/zrender/lib/tool/color.js"], "sourcesContent": ["import LRU from '../core/LRU.js';\nimport { extend, isGradientObject, isString, map } from '../core/util.js';\nvar kCSSColorTable = {\n    'transparent': [0, 0, 0, 0], 'aliceblue': [240, 248, 255, 1],\n    'antiquewhite': [250, 235, 215, 1], 'aqua': [0, 255, 255, 1],\n    'aquamarine': [127, 255, 212, 1], 'azure': [240, 255, 255, 1],\n    'beige': [245, 245, 220, 1], 'bisque': [255, 228, 196, 1],\n    'black': [0, 0, 0, 1], 'blanchedalmond': [255, 235, 205, 1],\n    'blue': [0, 0, 255, 1], 'blueviolet': [138, 43, 226, 1],\n    'brown': [165, 42, 42, 1], 'burlywood': [222, 184, 135, 1],\n    'cadetblue': [95, 158, 160, 1], 'chartreuse': [127, 255, 0, 1],\n    'chocolate': [210, 105, 30, 1], 'coral': [255, 127, 80, 1],\n    'cornflowerblue': [100, 149, 237, 1], 'cornsilk': [255, 248, 220, 1],\n    'crimson': [220, 20, 60, 1], 'cyan': [0, 255, 255, 1],\n    'darkblue': [0, 0, 139, 1], 'darkcyan': [0, 139, 139, 1],\n    'darkgoldenrod': [184, 134, 11, 1], 'darkgray': [169, 169, 169, 1],\n    'darkgreen': [0, 100, 0, 1], 'darkgrey': [169, 169, 169, 1],\n    'darkkhaki': [189, 183, 107, 1], 'darkmagenta': [139, 0, 139, 1],\n    'darkolivegreen': [85, 107, 47, 1], 'darkorange': [255, 140, 0, 1],\n    'darkorchid': [153, 50, 204, 1], 'darkred': [139, 0, 0, 1],\n    'darksalmon': [233, 150, 122, 1], 'darkseagreen': [143, 188, 143, 1],\n    'darkslateblue': [72, 61, 139, 1], 'darkslategray': [47, 79, 79, 1],\n    'darkslategrey': [47, 79, 79, 1], 'darkturquoise': [0, 206, 209, 1],\n    'darkviolet': [148, 0, 211, 1], 'deeppink': [255, 20, 147, 1],\n    'deepskyblue': [0, 191, 255, 1], 'dimgray': [105, 105, 105, 1],\n    'dimgrey': [105, 105, 105, 1], 'dodgerblue': [30, 144, 255, 1],\n    'firebrick': [178, 34, 34, 1], 'floralwhite': [255, 250, 240, 1],\n    'forestgreen': [34, 139, 34, 1], 'fuchsia': [255, 0, 255, 1],\n    'gainsboro': [220, 220, 220, 1], 'ghostwhite': [248, 248, 255, 1],\n    'gold': [255, 215, 0, 1], 'goldenrod': [218, 165, 32, 1],\n    'gray': [128, 128, 128, 1], 'green': [0, 128, 0, 1],\n    'greenyellow': [173, 255, 47, 1], 'grey': [128, 128, 128, 1],\n    'honeydew': [240, 255, 240, 1], 'hotpink': [255, 105, 180, 1],\n    'indianred': [205, 92, 92, 1], 'indigo': [75, 0, 130, 1],\n    'ivory': [255, 255, 240, 1], 'khaki': [240, 230, 140, 1],\n    'lavender': [230, 230, 250, 1], 'lavenderblush': [255, 240, 245, 1],\n    'lawngreen': [124, 252, 0, 1], 'lemonchiffon': [255, 250, 205, 1],\n    'lightblue': [173, 216, 230, 1], 'lightcoral': [240, 128, 128, 1],\n    'lightcyan': [224, 255, 255, 1], 'lightgoldenrodyellow': [250, 250, 210, 1],\n    'lightgray': [211, 211, 211, 1], 'lightgreen': [144, 238, 144, 1],\n    'lightgrey': [211, 211, 211, 1], 'lightpink': [255, 182, 193, 1],\n    'lightsalmon': [255, 160, 122, 1], 'lightseagreen': [32, 178, 170, 1],\n    'lightskyblue': [135, 206, 250, 1], 'lightslategray': [119, 136, 153, 1],\n    'lightslategrey': [119, 136, 153, 1], 'lightsteelblue': [176, 196, 222, 1],\n    'lightyellow': [255, 255, 224, 1], 'lime': [0, 255, 0, 1],\n    'limegreen': [50, 205, 50, 1], 'linen': [250, 240, 230, 1],\n    'magenta': [255, 0, 255, 1], 'maroon': [128, 0, 0, 1],\n    'mediumaquamarine': [102, 205, 170, 1], 'mediumblue': [0, 0, 205, 1],\n    'mediumorchid': [186, 85, 211, 1], 'mediumpurple': [147, 112, 219, 1],\n    'mediumseagreen': [60, 179, 113, 1], 'mediumslateblue': [123, 104, 238, 1],\n    'mediumspringgreen': [0, 250, 154, 1], 'mediumturquoise': [72, 209, 204, 1],\n    'mediumvioletred': [199, 21, 133, 1], 'midnightblue': [25, 25, 112, 1],\n    'mintcream': [245, 255, 250, 1], 'mistyrose': [255, 228, 225, 1],\n    'moccasin': [255, 228, 181, 1], 'navajowhite': [255, 222, 173, 1],\n    'navy': [0, 0, 128, 1], 'oldlace': [253, 245, 230, 1],\n    'olive': [128, 128, 0, 1], 'olivedrab': [107, 142, 35, 1],\n    'orange': [255, 165, 0, 1], 'orangered': [255, 69, 0, 1],\n    'orchid': [218, 112, 214, 1], 'palegoldenrod': [238, 232, 170, 1],\n    'palegreen': [152, 251, 152, 1], 'paleturquoise': [175, 238, 238, 1],\n    'palevioletred': [219, 112, 147, 1], 'papayawhip': [255, 239, 213, 1],\n    'peachpuff': [255, 218, 185, 1], 'peru': [205, 133, 63, 1],\n    'pink': [255, 192, 203, 1], 'plum': [221, 160, 221, 1],\n    'powderblue': [176, 224, 230, 1], 'purple': [128, 0, 128, 1],\n    'red': [255, 0, 0, 1], 'rosybrown': [188, 143, 143, 1],\n    'royalblue': [65, 105, 225, 1], 'saddlebrown': [139, 69, 19, 1],\n    'salmon': [250, 128, 114, 1], 'sandybrown': [244, 164, 96, 1],\n    'seagreen': [46, 139, 87, 1], 'seashell': [255, 245, 238, 1],\n    'sienna': [160, 82, 45, 1], 'silver': [192, 192, 192, 1],\n    'skyblue': [135, 206, 235, 1], 'slateblue': [106, 90, 205, 1],\n    'slategray': [112, 128, 144, 1], 'slategrey': [112, 128, 144, 1],\n    'snow': [255, 250, 250, 1], 'springgreen': [0, 255, 127, 1],\n    'steelblue': [70, 130, 180, 1], 'tan': [210, 180, 140, 1],\n    'teal': [0, 128, 128, 1], 'thistle': [216, 191, 216, 1],\n    'tomato': [255, 99, 71, 1], 'turquoise': [64, 224, 208, 1],\n    'violet': [238, 130, 238, 1], 'wheat': [245, 222, 179, 1],\n    'white': [255, 255, 255, 1], 'whitesmoke': [245, 245, 245, 1],\n    'yellow': [255, 255, 0, 1], 'yellowgreen': [154, 205, 50, 1]\n};\nfunction clampCssByte(i) {\n    i = Math.round(i);\n    return i < 0 ? 0 : i > 255 ? 255 : i;\n}\nfunction clampCssAngle(i) {\n    i = Math.round(i);\n    return i < 0 ? 0 : i > 360 ? 360 : i;\n}\nfunction clampCssFloat(f) {\n    return f < 0 ? 0 : f > 1 ? 1 : f;\n}\nfunction parseCssInt(val) {\n    var str = val;\n    if (str.length && str.charAt(str.length - 1) === '%') {\n        return clampCssByte(parseFloat(str) / 100 * 255);\n    }\n    return clampCssByte(parseInt(str, 10));\n}\nfunction parseCssFloat(val) {\n    var str = val;\n    if (str.length && str.charAt(str.length - 1) === '%') {\n        return clampCssFloat(parseFloat(str) / 100);\n    }\n    return clampCssFloat(parseFloat(str));\n}\nfunction cssHueToRgb(m1, m2, h) {\n    if (h < 0) {\n        h += 1;\n    }\n    else if (h > 1) {\n        h -= 1;\n    }\n    if (h * 6 < 1) {\n        return m1 + (m2 - m1) * h * 6;\n    }\n    if (h * 2 < 1) {\n        return m2;\n    }\n    if (h * 3 < 2) {\n        return m1 + (m2 - m1) * (2 / 3 - h) * 6;\n    }\n    return m1;\n}\nfunction lerpNumber(a, b, p) {\n    return a + (b - a) * p;\n}\nfunction setRgba(out, r, g, b, a) {\n    out[0] = r;\n    out[1] = g;\n    out[2] = b;\n    out[3] = a;\n    return out;\n}\nfunction copyRgba(out, a) {\n    out[0] = a[0];\n    out[1] = a[1];\n    out[2] = a[2];\n    out[3] = a[3];\n    return out;\n}\nvar colorCache = new LRU(20);\nvar lastRemovedArr = null;\nfunction putToCache(colorStr, rgbaArr) {\n    if (lastRemovedArr) {\n        copyRgba(lastRemovedArr, rgbaArr);\n    }\n    lastRemovedArr = colorCache.put(colorStr, lastRemovedArr || (rgbaArr.slice()));\n}\nexport function parse(colorStr, rgbaArr) {\n    if (!colorStr) {\n        return;\n    }\n    rgbaArr = rgbaArr || [];\n    var cached = colorCache.get(colorStr);\n    if (cached) {\n        return copyRgba(rgbaArr, cached);\n    }\n    colorStr = colorStr + '';\n    var str = colorStr.replace(/ /g, '').toLowerCase();\n    if (str in kCSSColorTable) {\n        copyRgba(rgbaArr, kCSSColorTable[str]);\n        putToCache(colorStr, rgbaArr);\n        return rgbaArr;\n    }\n    var strLen = str.length;\n    if (str.charAt(0) === '#') {\n        if (strLen === 4 || strLen === 5) {\n            var iv = parseInt(str.slice(1, 4), 16);\n            if (!(iv >= 0 && iv <= 0xfff)) {\n                setRgba(rgbaArr, 0, 0, 0, 1);\n                return;\n            }\n            setRgba(rgbaArr, ((iv & 0xf00) >> 4) | ((iv & 0xf00) >> 8), (iv & 0xf0) | ((iv & 0xf0) >> 4), (iv & 0xf) | ((iv & 0xf) << 4), strLen === 5 ? parseInt(str.slice(4), 16) / 0xf : 1);\n            putToCache(colorStr, rgbaArr);\n            return rgbaArr;\n        }\n        else if (strLen === 7 || strLen === 9) {\n            var iv = parseInt(str.slice(1, 7), 16);\n            if (!(iv >= 0 && iv <= 0xffffff)) {\n                setRgba(rgbaArr, 0, 0, 0, 1);\n                return;\n            }\n            setRgba(rgbaArr, (iv & 0xff0000) >> 16, (iv & 0xff00) >> 8, iv & 0xff, strLen === 9 ? parseInt(str.slice(7), 16) / 0xff : 1);\n            putToCache(colorStr, rgbaArr);\n            return rgbaArr;\n        }\n        return;\n    }\n    var op = str.indexOf('(');\n    var ep = str.indexOf(')');\n    if (op !== -1 && ep + 1 === strLen) {\n        var fname = str.substr(0, op);\n        var params = str.substr(op + 1, ep - (op + 1)).split(',');\n        var alpha = 1;\n        switch (fname) {\n            case 'rgba':\n                if (params.length !== 4) {\n                    return params.length === 3\n                        ? setRgba(rgbaArr, +params[0], +params[1], +params[2], 1)\n                        : setRgba(rgbaArr, 0, 0, 0, 1);\n                }\n                alpha = parseCssFloat(params.pop());\n            case 'rgb':\n                if (params.length >= 3) {\n                    setRgba(rgbaArr, parseCssInt(params[0]), parseCssInt(params[1]), parseCssInt(params[2]), params.length === 3 ? alpha : parseCssFloat(params[3]));\n                    putToCache(colorStr, rgbaArr);\n                    return rgbaArr;\n                }\n                else {\n                    setRgba(rgbaArr, 0, 0, 0, 1);\n                    return;\n                }\n            case 'hsla':\n                if (params.length !== 4) {\n                    setRgba(rgbaArr, 0, 0, 0, 1);\n                    return;\n                }\n                params[3] = parseCssFloat(params[3]);\n                hsla2rgba(params, rgbaArr);\n                putToCache(colorStr, rgbaArr);\n                return rgbaArr;\n            case 'hsl':\n                if (params.length !== 3) {\n                    setRgba(rgbaArr, 0, 0, 0, 1);\n                    return;\n                }\n                hsla2rgba(params, rgbaArr);\n                putToCache(colorStr, rgbaArr);\n                return rgbaArr;\n            default:\n                return;\n        }\n    }\n    setRgba(rgbaArr, 0, 0, 0, 1);\n    return;\n}\nfunction hsla2rgba(hsla, rgba) {\n    var h = (((parseFloat(hsla[0]) % 360) + 360) % 360) / 360;\n    var s = parseCssFloat(hsla[1]);\n    var l = parseCssFloat(hsla[2]);\n    var m2 = l <= 0.5 ? l * (s + 1) : l + s - l * s;\n    var m1 = l * 2 - m2;\n    rgba = rgba || [];\n    setRgba(rgba, clampCssByte(cssHueToRgb(m1, m2, h + 1 / 3) * 255), clampCssByte(cssHueToRgb(m1, m2, h) * 255), clampCssByte(cssHueToRgb(m1, m2, h - 1 / 3) * 255), 1);\n    if (hsla.length === 4) {\n        rgba[3] = hsla[3];\n    }\n    return rgba;\n}\nfunction rgba2hsla(rgba) {\n    if (!rgba) {\n        return;\n    }\n    var R = rgba[0] / 255;\n    var G = rgba[1] / 255;\n    var B = rgba[2] / 255;\n    var vMin = Math.min(R, G, B);\n    var vMax = Math.max(R, G, B);\n    var delta = vMax - vMin;\n    var L = (vMax + vMin) / 2;\n    var H;\n    var S;\n    if (delta === 0) {\n        H = 0;\n        S = 0;\n    }\n    else {\n        if (L < 0.5) {\n            S = delta / (vMax + vMin);\n        }\n        else {\n            S = delta / (2 - vMax - vMin);\n        }\n        var deltaR = (((vMax - R) / 6) + (delta / 2)) / delta;\n        var deltaG = (((vMax - G) / 6) + (delta / 2)) / delta;\n        var deltaB = (((vMax - B) / 6) + (delta / 2)) / delta;\n        if (R === vMax) {\n            H = deltaB - deltaG;\n        }\n        else if (G === vMax) {\n            H = (1 / 3) + deltaR - deltaB;\n        }\n        else if (B === vMax) {\n            H = (2 / 3) + deltaG - deltaR;\n        }\n        if (H < 0) {\n            H += 1;\n        }\n        if (H > 1) {\n            H -= 1;\n        }\n    }\n    var hsla = [H * 360, S, L];\n    if (rgba[3] != null) {\n        hsla.push(rgba[3]);\n    }\n    return hsla;\n}\nexport function lift(color, level) {\n    var colorArr = parse(color);\n    if (colorArr) {\n        for (var i = 0; i < 3; i++) {\n            if (level < 0) {\n                colorArr[i] = colorArr[i] * (1 - level) | 0;\n            }\n            else {\n                colorArr[i] = ((255 - colorArr[i]) * level + colorArr[i]) | 0;\n            }\n            if (colorArr[i] > 255) {\n                colorArr[i] = 255;\n            }\n            else if (colorArr[i] < 0) {\n                colorArr[i] = 0;\n            }\n        }\n        return stringify(colorArr, colorArr.length === 4 ? 'rgba' : 'rgb');\n    }\n}\nexport function toHex(color) {\n    var colorArr = parse(color);\n    if (colorArr) {\n        return ((1 << 24) + (colorArr[0] << 16) + (colorArr[1] << 8) + (+colorArr[2])).toString(16).slice(1);\n    }\n}\nexport function fastLerp(normalizedValue, colors, out) {\n    if (!(colors && colors.length)\n        || !(normalizedValue >= 0 && normalizedValue <= 1)) {\n        return;\n    }\n    out = out || [];\n    var value = normalizedValue * (colors.length - 1);\n    var leftIndex = Math.floor(value);\n    var rightIndex = Math.ceil(value);\n    var leftColor = colors[leftIndex];\n    var rightColor = colors[rightIndex];\n    var dv = value - leftIndex;\n    out[0] = clampCssByte(lerpNumber(leftColor[0], rightColor[0], dv));\n    out[1] = clampCssByte(lerpNumber(leftColor[1], rightColor[1], dv));\n    out[2] = clampCssByte(lerpNumber(leftColor[2], rightColor[2], dv));\n    out[3] = clampCssFloat(lerpNumber(leftColor[3], rightColor[3], dv));\n    return out;\n}\nexport var fastMapToColor = fastLerp;\nexport function lerp(normalizedValue, colors, fullOutput) {\n    if (!(colors && colors.length)\n        || !(normalizedValue >= 0 && normalizedValue <= 1)) {\n        return;\n    }\n    var value = normalizedValue * (colors.length - 1);\n    var leftIndex = Math.floor(value);\n    var rightIndex = Math.ceil(value);\n    var leftColor = parse(colors[leftIndex]);\n    var rightColor = parse(colors[rightIndex]);\n    var dv = value - leftIndex;\n    var color = stringify([\n        clampCssByte(lerpNumber(leftColor[0], rightColor[0], dv)),\n        clampCssByte(lerpNumber(leftColor[1], rightColor[1], dv)),\n        clampCssByte(lerpNumber(leftColor[2], rightColor[2], dv)),\n        clampCssFloat(lerpNumber(leftColor[3], rightColor[3], dv))\n    ], 'rgba');\n    return fullOutput\n        ? {\n            color: color,\n            leftIndex: leftIndex,\n            rightIndex: rightIndex,\n            value: value\n        }\n        : color;\n}\nexport var mapToColor = lerp;\nexport function modifyHSL(color, h, s, l) {\n    var colorArr = parse(color);\n    if (color) {\n        colorArr = rgba2hsla(colorArr);\n        h != null && (colorArr[0] = clampCssAngle(h));\n        s != null && (colorArr[1] = parseCssFloat(s));\n        l != null && (colorArr[2] = parseCssFloat(l));\n        return stringify(hsla2rgba(colorArr), 'rgba');\n    }\n}\nexport function modifyAlpha(color, alpha) {\n    var colorArr = parse(color);\n    if (colorArr && alpha != null) {\n        colorArr[3] = clampCssFloat(alpha);\n        return stringify(colorArr, 'rgba');\n    }\n}\nexport function stringify(arrColor, type) {\n    if (!arrColor || !arrColor.length) {\n        return;\n    }\n    var colorStr = arrColor[0] + ',' + arrColor[1] + ',' + arrColor[2];\n    if (type === 'rgba' || type === 'hsva' || type === 'hsla') {\n        colorStr += ',' + arrColor[3];\n    }\n    return type + '(' + colorStr + ')';\n}\nexport function lum(color, backgroundLum) {\n    var arr = parse(color);\n    return arr\n        ? (0.299 * arr[0] + 0.587 * arr[1] + 0.114 * arr[2]) * arr[3] / 255\n            + (1 - arr[3]) * backgroundLum\n        : 0;\n}\nexport function random() {\n    return stringify([\n        Math.round(Math.random() * 255),\n        Math.round(Math.random() * 255),\n        Math.round(Math.random() * 255)\n    ], 'rgb');\n}\nvar liftedColorCache = new LRU(100);\nexport function liftColor(color) {\n    if (isString(color)) {\n        var liftedColor = liftedColorCache.get(color);\n        if (!liftedColor) {\n            liftedColor = lift(color, -0.1);\n            liftedColorCache.put(color, liftedColor);\n        }\n        return liftedColor;\n    }\n    else if (isGradientObject(color)) {\n        var ret = extend({}, color);\n        ret.colorStops = map(color.colorStops, function (stop) { return ({\n            offset: stop.offset,\n            color: lift(stop.color, -0.1)\n        }); });\n        return ret;\n    }\n    return color;\n}\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAChC,SAASC,MAAM,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,iBAAiB;AACzE,IAAIC,cAAc,GAAG;EACjB,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAAE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC5D,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC5D,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC7D,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACzD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAAE,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC3D,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EACvD,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC1D,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9D,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EAC1D,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACpE,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACrD,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACxD,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAClE,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAAE,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC3D,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,aAAa,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EAChE,gBAAgB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAClE,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1D,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACpE,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACnE,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,eAAe,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACnE,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,UAAU,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAC7D,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC9D,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC9D,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAChE,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EAC5D,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACjE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAAE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EACxD,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EACnD,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC5D,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC7D,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EACxD,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACxD,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACnE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAAE,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACjE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACjE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,sBAAsB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC3E,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACjE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAChE,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,eAAe,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACrE,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACxE,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC1E,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EACzD,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC1D,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACrD,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EACpE,cAAc,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACrE,gBAAgB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,iBAAiB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC1E,mBAAmB,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,iBAAiB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC3E,iBAAiB,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EACtE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAChE,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACjE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACrD,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAAE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EACzD,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAAE,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;EACxD,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACjE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACpE,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACrE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EAC1D,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACtD,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EAC5D,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAAE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACtD,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAC/D,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EAC7D,UAAU,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC5D,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACxD,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAC7D,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAChE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC3D,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACzD,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACvD,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC1D,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACzD,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAAE,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC7D,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAAE,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;AAC/D,CAAC;AACD,SAASC,YAAYA,CAACC,CAAC,EAAE;EACrBA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC;EACjB,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGA,CAAC;AACxC;AACA,SAASG,aAAaA,CAACH,CAAC,EAAE;EACtBA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC;EACjB,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGA,CAAC;AACxC;AACA,SAASI,aAAaA,CAACC,CAAC,EAAE;EACtB,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC;AACpC;AACA,SAASC,WAAWA,CAACC,GAAG,EAAE;EACtB,IAAIC,GAAG,GAAGD,GAAG;EACb,IAAIC,GAAG,CAACC,MAAM,IAAID,GAAG,CAACE,MAAM,CAACF,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;IAClD,OAAOV,YAAY,CAACY,UAAU,CAACH,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;EACpD;EACA,OAAOT,YAAY,CAACa,QAAQ,CAACJ,GAAG,EAAE,EAAE,CAAC,CAAC;AAC1C;AACA,SAASK,aAAaA,CAACN,GAAG,EAAE;EACxB,IAAIC,GAAG,GAAGD,GAAG;EACb,IAAIC,GAAG,CAACC,MAAM,IAAID,GAAG,CAACE,MAAM,CAACF,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;IAClD,OAAOL,aAAa,CAACO,UAAU,CAACH,GAAG,CAAC,GAAG,GAAG,CAAC;EAC/C;EACA,OAAOJ,aAAa,CAACO,UAAU,CAACH,GAAG,CAAC,CAAC;AACzC;AACA,SAASM,WAAWA,CAACC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAE;EAC5B,IAAIA,CAAC,GAAG,CAAC,EAAE;IACPA,CAAC,IAAI,CAAC;EACV,CAAC,MACI,IAAIA,CAAC,GAAG,CAAC,EAAE;IACZA,CAAC,IAAI,CAAC;EACV;EACA,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IACX,OAAOF,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,IAAIE,CAAC,GAAG,CAAC;EACjC;EACA,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IACX,OAAOD,EAAE;EACb;EACA,IAAIC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IACX,OAAOF,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,KAAK,CAAC,GAAG,CAAC,GAAGE,CAAC,CAAC,GAAG,CAAC;EAC3C;EACA,OAAOF,EAAE;AACb;AACA,SAASG,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOF,CAAC,GAAG,CAACC,CAAC,GAAGD,CAAC,IAAIE,CAAC;AAC1B;AACA,SAASC,OAAOA,CAACC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAEL,CAAC,EAAED,CAAC,EAAE;EAC9BI,GAAG,CAAC,CAAC,CAAC,GAAGC,CAAC;EACVD,GAAG,CAAC,CAAC,CAAC,GAAGE,CAAC;EACVF,GAAG,CAAC,CAAC,CAAC,GAAGH,CAAC;EACVG,GAAG,CAAC,CAAC,CAAC,GAAGJ,CAAC;EACV,OAAOI,GAAG;AACd;AACA,SAASG,QAAQA,CAACH,GAAG,EAAEJ,CAAC,EAAE;EACtBI,GAAG,CAAC,CAAC,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACbI,GAAG,CAAC,CAAC,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACbI,GAAG,CAAC,CAAC,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACbI,GAAG,CAAC,CAAC,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACb,OAAOI,GAAG;AACd;AACA,IAAII,UAAU,GAAG,IAAIlC,GAAG,CAAC,EAAE,CAAC;AAC5B,IAAImC,cAAc,GAAG,IAAI;AACzB,SAASC,UAAUA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACnC,IAAIH,cAAc,EAAE;IAChBF,QAAQ,CAACE,cAAc,EAAEG,OAAO,CAAC;EACrC;EACAH,cAAc,GAAGD,UAAU,CAACK,GAAG,CAACF,QAAQ,EAAEF,cAAc,IAAKG,OAAO,CAACE,KAAK,CAAC,CAAE,CAAC;AAClF;AACA,OAAO,SAASC,KAAKA,CAACJ,QAAQ,EAAEC,OAAO,EAAE;EACrC,IAAI,CAACD,QAAQ,EAAE;IACX;EACJ;EACAC,OAAO,GAAGA,OAAO,IAAI,EAAE;EACvB,IAAII,MAAM,GAAGR,UAAU,CAACS,GAAG,CAACN,QAAQ,CAAC;EACrC,IAAIK,MAAM,EAAE;IACR,OAAOT,QAAQ,CAACK,OAAO,EAAEI,MAAM,CAAC;EACpC;EACAL,QAAQ,GAAGA,QAAQ,GAAG,EAAE;EACxB,IAAItB,GAAG,GAAGsB,QAAQ,CAACO,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EAClD,IAAI9B,GAAG,IAAIV,cAAc,EAAE;IACvB4B,QAAQ,CAACK,OAAO,EAAEjC,cAAc,CAACU,GAAG,CAAC,CAAC;IACtCqB,UAAU,CAACC,QAAQ,EAAEC,OAAO,CAAC;IAC7B,OAAOA,OAAO;EAClB;EACA,IAAIQ,MAAM,GAAG/B,GAAG,CAACC,MAAM;EACvB,IAAID,GAAG,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACvB,IAAI6B,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAIC,EAAE,GAAG5B,QAAQ,CAACJ,GAAG,CAACyB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;MACtC,IAAI,EAAEO,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,KAAK,CAAC,EAAE;QAC3BlB,OAAO,CAACS,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B;MACJ;MACAT,OAAO,CAACS,OAAO,EAAG,CAACS,EAAE,GAAG,KAAK,KAAK,CAAC,GAAK,CAACA,EAAE,GAAG,KAAK,KAAK,CAAE,EAAGA,EAAE,GAAG,IAAI,GAAK,CAACA,EAAE,GAAG,IAAI,KAAK,CAAE,EAAGA,EAAE,GAAG,GAAG,GAAK,CAACA,EAAE,GAAG,GAAG,KAAK,CAAE,EAAED,MAAM,KAAK,CAAC,GAAG3B,QAAQ,CAACJ,GAAG,CAACyB,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;MAClLJ,UAAU,CAACC,QAAQ,EAAEC,OAAO,CAAC;MAC7B,OAAOA,OAAO;IAClB,CAAC,MACI,IAAIQ,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;MACnC,IAAIC,EAAE,GAAG5B,QAAQ,CAACJ,GAAG,CAACyB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;MACtC,IAAI,EAAEO,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,QAAQ,CAAC,EAAE;QAC9BlB,OAAO,CAACS,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B;MACJ;MACAT,OAAO,CAACS,OAAO,EAAE,CAACS,EAAE,GAAG,QAAQ,KAAK,EAAE,EAAE,CAACA,EAAE,GAAG,MAAM,KAAK,CAAC,EAAEA,EAAE,GAAG,IAAI,EAAED,MAAM,KAAK,CAAC,GAAG3B,QAAQ,CAACJ,GAAG,CAACyB,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;MAC5HJ,UAAU,CAACC,QAAQ,EAAEC,OAAO,CAAC;MAC7B,OAAOA,OAAO;IAClB;IACA;EACJ;EACA,IAAIU,EAAE,GAAGjC,GAAG,CAACkC,OAAO,CAAC,GAAG,CAAC;EACzB,IAAIC,EAAE,GAAGnC,GAAG,CAACkC,OAAO,CAAC,GAAG,CAAC;EACzB,IAAID,EAAE,KAAK,CAAC,CAAC,IAAIE,EAAE,GAAG,CAAC,KAAKJ,MAAM,EAAE;IAChC,IAAIK,KAAK,GAAGpC,GAAG,CAACqC,MAAM,CAAC,CAAC,EAAEJ,EAAE,CAAC;IAC7B,IAAIK,MAAM,GAAGtC,GAAG,CAACqC,MAAM,CAACJ,EAAE,GAAG,CAAC,EAAEE,EAAE,IAAIF,EAAE,GAAG,CAAC,CAAC,CAAC,CAACM,KAAK,CAAC,GAAG,CAAC;IACzD,IAAIC,KAAK,GAAG,CAAC;IACb,QAAQJ,KAAK;MACT,KAAK,MAAM;QACP,IAAIE,MAAM,CAACrC,MAAM,KAAK,CAAC,EAAE;UACrB,OAAOqC,MAAM,CAACrC,MAAM,KAAK,CAAC,GACpBa,OAAO,CAACS,OAAO,EAAE,CAACe,MAAM,CAAC,CAAC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GACvDxB,OAAO,CAACS,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACtC;QACAiB,KAAK,GAAGnC,aAAa,CAACiC,MAAM,CAACG,GAAG,CAAC,CAAC,CAAC;MACvC,KAAK,KAAK;QACN,IAAIH,MAAM,CAACrC,MAAM,IAAI,CAAC,EAAE;UACpBa,OAAO,CAACS,OAAO,EAAEzB,WAAW,CAACwC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAExC,WAAW,CAACwC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAExC,WAAW,CAACwC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAACrC,MAAM,KAAK,CAAC,GAAGuC,KAAK,GAAGnC,aAAa,CAACiC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;UAChJjB,UAAU,CAACC,QAAQ,EAAEC,OAAO,CAAC;UAC7B,OAAOA,OAAO;QAClB,CAAC,MACI;UACDT,OAAO,CAACS,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B;QACJ;MACJ,KAAK,MAAM;QACP,IAAIe,MAAM,CAACrC,MAAM,KAAK,CAAC,EAAE;UACrBa,OAAO,CAACS,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B;QACJ;QACAe,MAAM,CAAC,CAAC,CAAC,GAAGjC,aAAa,CAACiC,MAAM,CAAC,CAAC,CAAC,CAAC;QACpCI,SAAS,CAACJ,MAAM,EAAEf,OAAO,CAAC;QAC1BF,UAAU,CAACC,QAAQ,EAAEC,OAAO,CAAC;QAC7B,OAAOA,OAAO;MAClB,KAAK,KAAK;QACN,IAAIe,MAAM,CAACrC,MAAM,KAAK,CAAC,EAAE;UACrBa,OAAO,CAACS,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B;QACJ;QACAmB,SAAS,CAACJ,MAAM,EAAEf,OAAO,CAAC;QAC1BF,UAAU,CAACC,QAAQ,EAAEC,OAAO,CAAC;QAC7B,OAAOA,OAAO;MAClB;QACI;IACR;EACJ;EACAT,OAAO,CAACS,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5B;AACJ;AACA,SAASmB,SAASA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC3B,IAAInC,CAAC,GAAI,CAAEN,UAAU,CAACwC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAI,GAAG,IAAI,GAAG,GAAI,GAAG;EACzD,IAAIE,CAAC,GAAGxC,aAAa,CAACsC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC9B,IAAIG,CAAC,GAAGzC,aAAa,CAACsC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC9B,IAAInC,EAAE,GAAGsC,CAAC,IAAI,GAAG,GAAGA,CAAC,IAAID,CAAC,GAAG,CAAC,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGD,CAAC;EAC/C,IAAItC,EAAE,GAAGuC,CAAC,GAAG,CAAC,GAAGtC,EAAE;EACnBoC,IAAI,GAAGA,IAAI,IAAI,EAAE;EACjB9B,OAAO,CAAC8B,IAAI,EAAErD,YAAY,CAACe,WAAW,CAACC,EAAE,EAAEC,EAAE,EAAEC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAElB,YAAY,CAACe,WAAW,CAACC,EAAE,EAAEC,EAAE,EAAEC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAElB,YAAY,CAACe,WAAW,CAACC,EAAE,EAAEC,EAAE,EAAEC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;EACpK,IAAIkC,IAAI,CAAC1C,MAAM,KAAK,CAAC,EAAE;IACnB2C,IAAI,CAAC,CAAC,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC;EACrB;EACA,OAAOC,IAAI;AACf;AACA,SAASG,SAASA,CAACH,IAAI,EAAE;EACrB,IAAI,CAACA,IAAI,EAAE;IACP;EACJ;EACA,IAAII,CAAC,GAAGJ,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;EACrB,IAAIK,CAAC,GAAGL,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;EACrB,IAAIM,CAAC,GAAGN,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;EACrB,IAAIO,IAAI,GAAG1D,IAAI,CAAC2D,GAAG,CAACJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC5B,IAAIG,IAAI,GAAG5D,IAAI,CAAC6D,GAAG,CAACN,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC5B,IAAIK,KAAK,GAAGF,IAAI,GAAGF,IAAI;EACvB,IAAIK,CAAC,GAAG,CAACH,IAAI,GAAGF,IAAI,IAAI,CAAC;EACzB,IAAIM,CAAC;EACL,IAAIC,CAAC;EACL,IAAIH,KAAK,KAAK,CAAC,EAAE;IACbE,CAAC,GAAG,CAAC;IACLC,CAAC,GAAG,CAAC;EACT,CAAC,MACI;IACD,IAAIF,CAAC,GAAG,GAAG,EAAE;MACTE,CAAC,GAAGH,KAAK,IAAIF,IAAI,GAAGF,IAAI,CAAC;IAC7B,CAAC,MACI;MACDO,CAAC,GAAGH,KAAK,IAAI,CAAC,GAAGF,IAAI,GAAGF,IAAI,CAAC;IACjC;IACA,IAAIQ,MAAM,GAAG,CAAE,CAACN,IAAI,GAAGL,CAAC,IAAI,CAAC,GAAKO,KAAK,GAAG,CAAE,IAAIA,KAAK;IACrD,IAAIK,MAAM,GAAG,CAAE,CAACP,IAAI,GAAGJ,CAAC,IAAI,CAAC,GAAKM,KAAK,GAAG,CAAE,IAAIA,KAAK;IACrD,IAAIM,MAAM,GAAG,CAAE,CAACR,IAAI,GAAGH,CAAC,IAAI,CAAC,GAAKK,KAAK,GAAG,CAAE,IAAIA,KAAK;IACrD,IAAIP,CAAC,KAAKK,IAAI,EAAE;MACZI,CAAC,GAAGI,MAAM,GAAGD,MAAM;IACvB,CAAC,MACI,IAAIX,CAAC,KAAKI,IAAI,EAAE;MACjBI,CAAC,GAAI,CAAC,GAAG,CAAC,GAAIE,MAAM,GAAGE,MAAM;IACjC,CAAC,MACI,IAAIX,CAAC,KAAKG,IAAI,EAAE;MACjBI,CAAC,GAAI,CAAC,GAAG,CAAC,GAAIG,MAAM,GAAGD,MAAM;IACjC;IACA,IAAIF,CAAC,GAAG,CAAC,EAAE;MACPA,CAAC,IAAI,CAAC;IACV;IACA,IAAIA,CAAC,GAAG,CAAC,EAAE;MACPA,CAAC,IAAI,CAAC;IACV;EACJ;EACA,IAAId,IAAI,GAAG,CAACc,CAAC,GAAG,GAAG,EAAEC,CAAC,EAAEF,CAAC,CAAC;EAC1B,IAAIZ,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;IACjBD,IAAI,CAACmB,IAAI,CAAClB,IAAI,CAAC,CAAC,CAAC,CAAC;EACtB;EACA,OAAOD,IAAI;AACf;AACA,OAAO,SAASoB,IAAIA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC/B,IAAIC,QAAQ,GAAGxC,KAAK,CAACsC,KAAK,CAAC;EAC3B,IAAIE,QAAQ,EAAE;IACV,KAAK,IAAI1E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,IAAIyE,KAAK,GAAG,CAAC,EAAE;QACXC,QAAQ,CAAC1E,CAAC,CAAC,GAAG0E,QAAQ,CAAC1E,CAAC,CAAC,IAAI,CAAC,GAAGyE,KAAK,CAAC,GAAG,CAAC;MAC/C,CAAC,MACI;QACDC,QAAQ,CAAC1E,CAAC,CAAC,GAAI,CAAC,GAAG,GAAG0E,QAAQ,CAAC1E,CAAC,CAAC,IAAIyE,KAAK,GAAGC,QAAQ,CAAC1E,CAAC,CAAC,GAAI,CAAC;MACjE;MACA,IAAI0E,QAAQ,CAAC1E,CAAC,CAAC,GAAG,GAAG,EAAE;QACnB0E,QAAQ,CAAC1E,CAAC,CAAC,GAAG,GAAG;MACrB,CAAC,MACI,IAAI0E,QAAQ,CAAC1E,CAAC,CAAC,GAAG,CAAC,EAAE;QACtB0E,QAAQ,CAAC1E,CAAC,CAAC,GAAG,CAAC;MACnB;IACJ;IACA,OAAO2E,SAAS,CAACD,QAAQ,EAAEA,QAAQ,CAACjE,MAAM,KAAK,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC;EACtE;AACJ;AACA,OAAO,SAASmE,KAAKA,CAACJ,KAAK,EAAE;EACzB,IAAIE,QAAQ,GAAGxC,KAAK,CAACsC,KAAK,CAAC;EAC3B,IAAIE,QAAQ,EAAE;IACV,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,KAAKA,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAI,CAACA,QAAQ,CAAC,CAAC,CAAE,EAAEG,QAAQ,CAAC,EAAE,CAAC,CAAC5C,KAAK,CAAC,CAAC,CAAC;EACxG;AACJ;AACA,OAAO,SAAS6C,QAAQA,CAACC,eAAe,EAAEC,MAAM,EAAEzD,GAAG,EAAE;EACnD,IAAI,EAAEyD,MAAM,IAAIA,MAAM,CAACvE,MAAM,CAAC,IACvB,EAAEsE,eAAe,IAAI,CAAC,IAAIA,eAAe,IAAI,CAAC,CAAC,EAAE;IACpD;EACJ;EACAxD,GAAG,GAAGA,GAAG,IAAI,EAAE;EACf,IAAI0D,KAAK,GAAGF,eAAe,IAAIC,MAAM,CAACvE,MAAM,GAAG,CAAC,CAAC;EACjD,IAAIyE,SAAS,GAAGjF,IAAI,CAACkF,KAAK,CAACF,KAAK,CAAC;EACjC,IAAIG,UAAU,GAAGnF,IAAI,CAACoF,IAAI,CAACJ,KAAK,CAAC;EACjC,IAAIK,SAAS,GAAGN,MAAM,CAACE,SAAS,CAAC;EACjC,IAAIK,UAAU,GAAGP,MAAM,CAACI,UAAU,CAAC;EACnC,IAAII,EAAE,GAAGP,KAAK,GAAGC,SAAS;EAC1B3D,GAAG,CAAC,CAAC,CAAC,GAAGxB,YAAY,CAACmB,UAAU,CAACoE,SAAS,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC;EAClEjE,GAAG,CAAC,CAAC,CAAC,GAAGxB,YAAY,CAACmB,UAAU,CAACoE,SAAS,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC;EAClEjE,GAAG,CAAC,CAAC,CAAC,GAAGxB,YAAY,CAACmB,UAAU,CAACoE,SAAS,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC;EAClEjE,GAAG,CAAC,CAAC,CAAC,GAAGnB,aAAa,CAACc,UAAU,CAACoE,SAAS,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC;EACnE,OAAOjE,GAAG;AACd;AACA,OAAO,IAAIkE,cAAc,GAAGX,QAAQ;AACpC,OAAO,SAASY,IAAIA,CAACX,eAAe,EAAEC,MAAM,EAAEW,UAAU,EAAE;EACtD,IAAI,EAAEX,MAAM,IAAIA,MAAM,CAACvE,MAAM,CAAC,IACvB,EAAEsE,eAAe,IAAI,CAAC,IAAIA,eAAe,IAAI,CAAC,CAAC,EAAE;IACpD;EACJ;EACA,IAAIE,KAAK,GAAGF,eAAe,IAAIC,MAAM,CAACvE,MAAM,GAAG,CAAC,CAAC;EACjD,IAAIyE,SAAS,GAAGjF,IAAI,CAACkF,KAAK,CAACF,KAAK,CAAC;EACjC,IAAIG,UAAU,GAAGnF,IAAI,CAACoF,IAAI,CAACJ,KAAK,CAAC;EACjC,IAAIK,SAAS,GAAGpD,KAAK,CAAC8C,MAAM,CAACE,SAAS,CAAC,CAAC;EACxC,IAAIK,UAAU,GAAGrD,KAAK,CAAC8C,MAAM,CAACI,UAAU,CAAC,CAAC;EAC1C,IAAII,EAAE,GAAGP,KAAK,GAAGC,SAAS;EAC1B,IAAIV,KAAK,GAAGG,SAAS,CAAC,CAClB5E,YAAY,CAACmB,UAAU,CAACoE,SAAS,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,EACzDzF,YAAY,CAACmB,UAAU,CAACoE,SAAS,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,EACzDzF,YAAY,CAACmB,UAAU,CAACoE,SAAS,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,EACzDpF,aAAa,CAACc,UAAU,CAACoE,SAAS,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAC7D,EAAE,MAAM,CAAC;EACV,OAAOG,UAAU,GACX;IACEnB,KAAK,EAAEA,KAAK;IACZU,SAAS,EAAEA,SAAS;IACpBE,UAAU,EAAEA,UAAU;IACtBH,KAAK,EAAEA;EACX,CAAC,GACCT,KAAK;AACf;AACA,OAAO,IAAIoB,UAAU,GAAGF,IAAI;AAC5B,OAAO,SAASG,SAASA,CAACrB,KAAK,EAAEvD,CAAC,EAAEoC,CAAC,EAAEC,CAAC,EAAE;EACtC,IAAIoB,QAAQ,GAAGxC,KAAK,CAACsC,KAAK,CAAC;EAC3B,IAAIA,KAAK,EAAE;IACPE,QAAQ,GAAGnB,SAAS,CAACmB,QAAQ,CAAC;IAC9BzD,CAAC,IAAI,IAAI,KAAKyD,QAAQ,CAAC,CAAC,CAAC,GAAGvE,aAAa,CAACc,CAAC,CAAC,CAAC;IAC7CoC,CAAC,IAAI,IAAI,KAAKqB,QAAQ,CAAC,CAAC,CAAC,GAAG7D,aAAa,CAACwC,CAAC,CAAC,CAAC;IAC7CC,CAAC,IAAI,IAAI,KAAKoB,QAAQ,CAAC,CAAC,CAAC,GAAG7D,aAAa,CAACyC,CAAC,CAAC,CAAC;IAC7C,OAAOqB,SAAS,CAACzB,SAAS,CAACwB,QAAQ,CAAC,EAAE,MAAM,CAAC;EACjD;AACJ;AACA,OAAO,SAASoB,WAAWA,CAACtB,KAAK,EAAExB,KAAK,EAAE;EACtC,IAAI0B,QAAQ,GAAGxC,KAAK,CAACsC,KAAK,CAAC;EAC3B,IAAIE,QAAQ,IAAI1B,KAAK,IAAI,IAAI,EAAE;IAC3B0B,QAAQ,CAAC,CAAC,CAAC,GAAGtE,aAAa,CAAC4C,KAAK,CAAC;IAClC,OAAO2B,SAAS,CAACD,QAAQ,EAAE,MAAM,CAAC;EACtC;AACJ;AACA,OAAO,SAASC,SAASA,CAACoB,QAAQ,EAAEC,IAAI,EAAE;EACtC,IAAI,CAACD,QAAQ,IAAI,CAACA,QAAQ,CAACtF,MAAM,EAAE;IAC/B;EACJ;EACA,IAAIqB,QAAQ,GAAGiE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,QAAQ,CAAC,CAAC,CAAC;EAClE,IAAIC,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM,EAAE;IACvDlE,QAAQ,IAAI,GAAG,GAAGiE,QAAQ,CAAC,CAAC,CAAC;EACjC;EACA,OAAOC,IAAI,GAAG,GAAG,GAAGlE,QAAQ,GAAG,GAAG;AACtC;AACA,OAAO,SAASmE,GAAGA,CAACzB,KAAK,EAAE0B,aAAa,EAAE;EACtC,IAAIC,GAAG,GAAGjE,KAAK,CAACsC,KAAK,CAAC;EACtB,OAAO2B,GAAG,GACJ,CAAC,KAAK,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,GAAGA,GAAG,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAC7D,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,IAAID,aAAa,GAChC,CAAC;AACX;AACA,OAAO,SAASE,MAAMA,CAAA,EAAG;EACrB,OAAOzB,SAAS,CAAC,CACb1E,IAAI,CAACC,KAAK,CAACD,IAAI,CAACmG,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,EAC/BnG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACmG,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,EAC/BnG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACmG,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAClC,EAAE,KAAK,CAAC;AACb;AACA,IAAIC,gBAAgB,GAAG,IAAI5G,GAAG,CAAC,GAAG,CAAC;AACnC,OAAO,SAAS6G,SAASA,CAAC9B,KAAK,EAAE;EAC7B,IAAI5E,QAAQ,CAAC4E,KAAK,CAAC,EAAE;IACjB,IAAI+B,WAAW,GAAGF,gBAAgB,CAACjE,GAAG,CAACoC,KAAK,CAAC;IAC7C,IAAI,CAAC+B,WAAW,EAAE;MACdA,WAAW,GAAGhC,IAAI,CAACC,KAAK,EAAE,CAAC,GAAG,CAAC;MAC/B6B,gBAAgB,CAACrE,GAAG,CAACwC,KAAK,EAAE+B,WAAW,CAAC;IAC5C;IACA,OAAOA,WAAW;EACtB,CAAC,MACI,IAAI5G,gBAAgB,CAAC6E,KAAK,CAAC,EAAE;IAC9B,IAAIgC,GAAG,GAAG9G,MAAM,CAAC,CAAC,CAAC,EAAE8E,KAAK,CAAC;IAC3BgC,GAAG,CAACC,UAAU,GAAG5G,GAAG,CAAC2E,KAAK,CAACiC,UAAU,EAAE,UAAUC,IAAI,EAAE;MAAE,OAAQ;QAC7DC,MAAM,EAAED,IAAI,CAACC,MAAM;QACnBnC,KAAK,EAAED,IAAI,CAACmC,IAAI,CAAClC,KAAK,EAAE,CAAC,GAAG;MAChC,CAAC;IAAG,CAAC,CAAC;IACN,OAAOgC,GAAG;EACd;EACA,OAAOhC,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}