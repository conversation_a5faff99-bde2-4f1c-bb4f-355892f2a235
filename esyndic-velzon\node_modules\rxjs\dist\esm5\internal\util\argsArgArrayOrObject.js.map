{"version": 3, "file": "argsArgArrayOrObject.js", "sourceRoot": "", "sources": ["../../../../src/internal/util/argsArgArrayOrObject.ts"], "names": [], "mappings": "AAAQ,IAAA,OAAO,GAAK,KAAK,QAAV,CAAW;AAClB,IAAA,cAAc,GAA4C,MAAM,eAAlD,EAAa,WAAW,GAAoB,MAAM,UAA1B,EAAQ,OAAO,GAAK,MAAM,KAAX,CAAY;AAQzE,MAAM,UAAU,oBAAoB,CAAiC,IAAuB;IAC1F,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,IAAM,OAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,OAAO,CAAC,OAAK,CAAC,EAAE;YAClB,OAAO,EAAE,IAAI,EAAE,OAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;SACpC;QACD,IAAI,MAAM,CAAC,OAAK,CAAC,EAAE;YACjB,IAAM,IAAI,GAAG,OAAO,CAAC,OAAK,CAAC,CAAC;YAC5B,OAAO;gBACL,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,UAAC,GAAG,IAAK,OAAA,OAAK,CAAC,GAAG,CAAC,EAAV,CAAU,CAAC;gBACnC,IAAI,MAAA;aACL,CAAC;SACH;KACF;IAED,OAAO,EAAE,IAAI,EAAE,IAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAC3C,CAAC;AAED,SAAS,MAAM,CAAC,GAAQ;IACtB,OAAO,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,cAAc,CAAC,GAAG,CAAC,KAAK,WAAW,CAAC;AAC/E,CAAC"}