{"ast": null, "code": "/**\n * [js-sha256]{@link https://github.com/emn178/js-sha256}\n *\n * @version 0.11.1\n * <AUTHOR> <PERSON><PERSON><PERSON> [<EMAIL>]\n * @copyright Chen, <PERSON><PERSON><PERSON><PERSON> 2014-2025\n * @license MIT\n */\n/*jslint bitwise: true */\n(function () {\n  'use strict';\n\n  var ERROR = 'input is invalid type';\n  var WINDOW = typeof window === 'object';\n  var root = WINDOW ? window : {};\n  if (root.JS_SHA256_NO_WINDOW) {\n    WINDOW = false;\n  }\n  var WEB_WORKER = !WINDOW && typeof self === 'object';\n  var NODE_JS = !root.JS_SHA256_NO_NODE_JS && typeof process === 'object' && process.versions && process.versions.node && process.type != 'renderer';\n  if (NODE_JS) {\n    root = global;\n  } else if (WEB_WORKER) {\n    root = self;\n  }\n  var COMMON_JS = !root.JS_SHA256_NO_COMMON_JS && typeof module === 'object' && module.exports;\n  var AMD = typeof define === 'function' && define.amd;\n  var ARRAY_BUFFER = !root.JS_SHA256_NO_ARRAY_BUFFER && typeof ArrayBuffer !== 'undefined';\n  var HEX_CHARS = '0123456789abcdef'.split('');\n  var EXTRA = [-**********, 8388608, 32768, 128];\n  var SHIFT = [24, 16, 8, 0];\n  var K = [0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5, 0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2];\n  var OUTPUT_TYPES = ['hex', 'array', 'digest', 'arrayBuffer'];\n  var blocks = [];\n  if (root.JS_SHA256_NO_NODE_JS || !Array.isArray) {\n    Array.isArray = function (obj) {\n      return Object.prototype.toString.call(obj) === '[object Array]';\n    };\n  }\n  if (ARRAY_BUFFER && (root.JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW || !ArrayBuffer.isView)) {\n    ArrayBuffer.isView = function (obj) {\n      return typeof obj === 'object' && obj.buffer && obj.buffer.constructor === ArrayBuffer;\n    };\n  }\n  var createOutputMethod = function (outputType, is224) {\n    return function (message) {\n      return new Sha256(is224, true).update(message)[outputType]();\n    };\n  };\n  var createMethod = function (is224) {\n    var method = createOutputMethod('hex', is224);\n    if (NODE_JS) {\n      method = nodeWrap(method, is224);\n    }\n    method.create = function () {\n      return new Sha256(is224);\n    };\n    method.update = function (message) {\n      return method.create().update(message);\n    };\n    for (var i = 0; i < OUTPUT_TYPES.length; ++i) {\n      var type = OUTPUT_TYPES[i];\n      method[type] = createOutputMethod(type, is224);\n    }\n    return method;\n  };\n  var nodeWrap = function (method, is224) {\n    var crypto = require('crypto');\n    var Buffer = require('buffer').Buffer;\n    var algorithm = is224 ? 'sha224' : 'sha256';\n    var bufferFrom;\n    if (Buffer.from && !root.JS_SHA256_NO_BUFFER_FROM) {\n      bufferFrom = Buffer.from;\n    } else {\n      bufferFrom = function (message) {\n        return new Buffer(message);\n      };\n    }\n    var nodeMethod = function (message) {\n      if (typeof message === 'string') {\n        return crypto.createHash(algorithm).update(message, 'utf8').digest('hex');\n      } else {\n        if (message === null || message === undefined) {\n          throw new Error(ERROR);\n        } else if (message.constructor === ArrayBuffer) {\n          message = new Uint8Array(message);\n        }\n      }\n      if (Array.isArray(message) || ArrayBuffer.isView(message) || message.constructor === Buffer) {\n        return crypto.createHash(algorithm).update(bufferFrom(message)).digest('hex');\n      } else {\n        return method(message);\n      }\n    };\n    return nodeMethod;\n  };\n  var createHmacOutputMethod = function (outputType, is224) {\n    return function (key, message) {\n      return new HmacSha256(key, is224, true).update(message)[outputType]();\n    };\n  };\n  var createHmacMethod = function (is224) {\n    var method = createHmacOutputMethod('hex', is224);\n    method.create = function (key) {\n      return new HmacSha256(key, is224);\n    };\n    method.update = function (key, message) {\n      return method.create(key).update(message);\n    };\n    for (var i = 0; i < OUTPUT_TYPES.length; ++i) {\n      var type = OUTPUT_TYPES[i];\n      method[type] = createHmacOutputMethod(type, is224);\n    }\n    return method;\n  };\n  function Sha256(is224, sharedMemory) {\n    if (sharedMemory) {\n      blocks[0] = blocks[16] = blocks[1] = blocks[2] = blocks[3] = blocks[4] = blocks[5] = blocks[6] = blocks[7] = blocks[8] = blocks[9] = blocks[10] = blocks[11] = blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n      this.blocks = blocks;\n    } else {\n      this.blocks = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\n    }\n    if (is224) {\n      this.h0 = 0xc1059ed8;\n      this.h1 = 0x367cd507;\n      this.h2 = 0x3070dd17;\n      this.h3 = 0xf70e5939;\n      this.h4 = 0xffc00b31;\n      this.h5 = 0x68581511;\n      this.h6 = 0x64f98fa7;\n      this.h7 = 0xbefa4fa4;\n    } else {\n      // 256\n      this.h0 = 0x6a09e667;\n      this.h1 = 0xbb67ae85;\n      this.h2 = 0x3c6ef372;\n      this.h3 = 0xa54ff53a;\n      this.h4 = 0x510e527f;\n      this.h5 = 0x9b05688c;\n      this.h6 = 0x1f83d9ab;\n      this.h7 = 0x5be0cd19;\n    }\n    this.block = this.start = this.bytes = this.hBytes = 0;\n    this.finalized = this.hashed = false;\n    this.first = true;\n    this.is224 = is224;\n  }\n  Sha256.prototype.update = function (message) {\n    if (this.finalized) {\n      return;\n    }\n    var notString,\n      type = typeof message;\n    if (type !== 'string') {\n      if (type === 'object') {\n        if (message === null) {\n          throw new Error(ERROR);\n        } else if (ARRAY_BUFFER && message.constructor === ArrayBuffer) {\n          message = new Uint8Array(message);\n        } else if (!Array.isArray(message)) {\n          if (!ARRAY_BUFFER || !ArrayBuffer.isView(message)) {\n            throw new Error(ERROR);\n          }\n        }\n      } else {\n        throw new Error(ERROR);\n      }\n      notString = true;\n    }\n    var code,\n      index = 0,\n      i,\n      length = message.length,\n      blocks = this.blocks;\n    while (index < length) {\n      if (this.hashed) {\n        this.hashed = false;\n        blocks[0] = this.block;\n        this.block = blocks[16] = blocks[1] = blocks[2] = blocks[3] = blocks[4] = blocks[5] = blocks[6] = blocks[7] = blocks[8] = blocks[9] = blocks[10] = blocks[11] = blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n      }\n      if (notString) {\n        for (i = this.start; index < length && i < 64; ++index) {\n          blocks[i >>> 2] |= message[index] << SHIFT[i++ & 3];\n        }\n      } else {\n        for (i = this.start; index < length && i < 64; ++index) {\n          code = message.charCodeAt(index);\n          if (code < 0x80) {\n            blocks[i >>> 2] |= code << SHIFT[i++ & 3];\n          } else if (code < 0x800) {\n            blocks[i >>> 2] |= (0xc0 | code >>> 6) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | code & 0x3f) << SHIFT[i++ & 3];\n          } else if (code < 0xd800 || code >= 0xe000) {\n            blocks[i >>> 2] |= (0xe0 | code >>> 12) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | code >>> 6 & 0x3f) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | code & 0x3f) << SHIFT[i++ & 3];\n          } else {\n            code = 0x10000 + ((code & 0x3ff) << 10 | message.charCodeAt(++index) & 0x3ff);\n            blocks[i >>> 2] |= (0xf0 | code >>> 18) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | code >>> 12 & 0x3f) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | code >>> 6 & 0x3f) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | code & 0x3f) << SHIFT[i++ & 3];\n          }\n        }\n      }\n      this.lastByteIndex = i;\n      this.bytes += i - this.start;\n      if (i >= 64) {\n        this.block = blocks[16];\n        this.start = i - 64;\n        this.hash();\n        this.hashed = true;\n      } else {\n        this.start = i;\n      }\n    }\n    if (this.bytes > 4294967295) {\n      this.hBytes += this.bytes / 4294967296 << 0;\n      this.bytes = this.bytes % 4294967296;\n    }\n    return this;\n  };\n  Sha256.prototype.finalize = function () {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    var blocks = this.blocks,\n      i = this.lastByteIndex;\n    blocks[16] = this.block;\n    blocks[i >>> 2] |= EXTRA[i & 3];\n    this.block = blocks[16];\n    if (i >= 56) {\n      if (!this.hashed) {\n        this.hash();\n      }\n      blocks[0] = this.block;\n      blocks[16] = blocks[1] = blocks[2] = blocks[3] = blocks[4] = blocks[5] = blocks[6] = blocks[7] = blocks[8] = blocks[9] = blocks[10] = blocks[11] = blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n    }\n    blocks[14] = this.hBytes << 3 | this.bytes >>> 29;\n    blocks[15] = this.bytes << 3;\n    this.hash();\n  };\n  Sha256.prototype.hash = function () {\n    var a = this.h0,\n      b = this.h1,\n      c = this.h2,\n      d = this.h3,\n      e = this.h4,\n      f = this.h5,\n      g = this.h6,\n      h = this.h7,\n      blocks = this.blocks,\n      j,\n      s0,\n      s1,\n      maj,\n      t1,\n      t2,\n      ch,\n      ab,\n      da,\n      cd,\n      bc;\n    for (j = 16; j < 64; ++j) {\n      // rightrotate\n      t1 = blocks[j - 15];\n      s0 = (t1 >>> 7 | t1 << 25) ^ (t1 >>> 18 | t1 << 14) ^ t1 >>> 3;\n      t1 = blocks[j - 2];\n      s1 = (t1 >>> 17 | t1 << 15) ^ (t1 >>> 19 | t1 << 13) ^ t1 >>> 10;\n      blocks[j] = blocks[j - 16] + s0 + blocks[j - 7] + s1 << 0;\n    }\n    bc = b & c;\n    for (j = 0; j < 64; j += 4) {\n      if (this.first) {\n        if (this.is224) {\n          ab = 300032;\n          t1 = blocks[0] - 1413257819;\n          h = t1 - 150054599 << 0;\n          d = t1 + 24177077 << 0;\n        } else {\n          ab = 704751109;\n          t1 = blocks[0] - 210244248;\n          h = t1 - 1521486534 << 0;\n          d = t1 + 143694565 << 0;\n        }\n        this.first = false;\n      } else {\n        s0 = (a >>> 2 | a << 30) ^ (a >>> 13 | a << 19) ^ (a >>> 22 | a << 10);\n        s1 = (e >>> 6 | e << 26) ^ (e >>> 11 | e << 21) ^ (e >>> 25 | e << 7);\n        ab = a & b;\n        maj = ab ^ a & c ^ bc;\n        ch = e & f ^ ~e & g;\n        t1 = h + s1 + ch + K[j] + blocks[j];\n        t2 = s0 + maj;\n        h = d + t1 << 0;\n        d = t1 + t2 << 0;\n      }\n      s0 = (d >>> 2 | d << 30) ^ (d >>> 13 | d << 19) ^ (d >>> 22 | d << 10);\n      s1 = (h >>> 6 | h << 26) ^ (h >>> 11 | h << 21) ^ (h >>> 25 | h << 7);\n      da = d & a;\n      maj = da ^ d & b ^ ab;\n      ch = h & e ^ ~h & f;\n      t1 = g + s1 + ch + K[j + 1] + blocks[j + 1];\n      t2 = s0 + maj;\n      g = c + t1 << 0;\n      c = t1 + t2 << 0;\n      s0 = (c >>> 2 | c << 30) ^ (c >>> 13 | c << 19) ^ (c >>> 22 | c << 10);\n      s1 = (g >>> 6 | g << 26) ^ (g >>> 11 | g << 21) ^ (g >>> 25 | g << 7);\n      cd = c & d;\n      maj = cd ^ c & a ^ da;\n      ch = g & h ^ ~g & e;\n      t1 = f + s1 + ch + K[j + 2] + blocks[j + 2];\n      t2 = s0 + maj;\n      f = b + t1 << 0;\n      b = t1 + t2 << 0;\n      s0 = (b >>> 2 | b << 30) ^ (b >>> 13 | b << 19) ^ (b >>> 22 | b << 10);\n      s1 = (f >>> 6 | f << 26) ^ (f >>> 11 | f << 21) ^ (f >>> 25 | f << 7);\n      bc = b & c;\n      maj = bc ^ b & d ^ cd;\n      ch = f & g ^ ~f & h;\n      t1 = e + s1 + ch + K[j + 3] + blocks[j + 3];\n      t2 = s0 + maj;\n      e = a + t1 << 0;\n      a = t1 + t2 << 0;\n      this.chromeBugWorkAround = true;\n    }\n    this.h0 = this.h0 + a << 0;\n    this.h1 = this.h1 + b << 0;\n    this.h2 = this.h2 + c << 0;\n    this.h3 = this.h3 + d << 0;\n    this.h4 = this.h4 + e << 0;\n    this.h5 = this.h5 + f << 0;\n    this.h6 = this.h6 + g << 0;\n    this.h7 = this.h7 + h << 0;\n  };\n  Sha256.prototype.hex = function () {\n    this.finalize();\n    var h0 = this.h0,\n      h1 = this.h1,\n      h2 = this.h2,\n      h3 = this.h3,\n      h4 = this.h4,\n      h5 = this.h5,\n      h6 = this.h6,\n      h7 = this.h7;\n    var hex = HEX_CHARS[h0 >>> 28 & 0x0F] + HEX_CHARS[h0 >>> 24 & 0x0F] + HEX_CHARS[h0 >>> 20 & 0x0F] + HEX_CHARS[h0 >>> 16 & 0x0F] + HEX_CHARS[h0 >>> 12 & 0x0F] + HEX_CHARS[h0 >>> 8 & 0x0F] + HEX_CHARS[h0 >>> 4 & 0x0F] + HEX_CHARS[h0 & 0x0F] + HEX_CHARS[h1 >>> 28 & 0x0F] + HEX_CHARS[h1 >>> 24 & 0x0F] + HEX_CHARS[h1 >>> 20 & 0x0F] + HEX_CHARS[h1 >>> 16 & 0x0F] + HEX_CHARS[h1 >>> 12 & 0x0F] + HEX_CHARS[h1 >>> 8 & 0x0F] + HEX_CHARS[h1 >>> 4 & 0x0F] + HEX_CHARS[h1 & 0x0F] + HEX_CHARS[h2 >>> 28 & 0x0F] + HEX_CHARS[h2 >>> 24 & 0x0F] + HEX_CHARS[h2 >>> 20 & 0x0F] + HEX_CHARS[h2 >>> 16 & 0x0F] + HEX_CHARS[h2 >>> 12 & 0x0F] + HEX_CHARS[h2 >>> 8 & 0x0F] + HEX_CHARS[h2 >>> 4 & 0x0F] + HEX_CHARS[h2 & 0x0F] + HEX_CHARS[h3 >>> 28 & 0x0F] + HEX_CHARS[h3 >>> 24 & 0x0F] + HEX_CHARS[h3 >>> 20 & 0x0F] + HEX_CHARS[h3 >>> 16 & 0x0F] + HEX_CHARS[h3 >>> 12 & 0x0F] + HEX_CHARS[h3 >>> 8 & 0x0F] + HEX_CHARS[h3 >>> 4 & 0x0F] + HEX_CHARS[h3 & 0x0F] + HEX_CHARS[h4 >>> 28 & 0x0F] + HEX_CHARS[h4 >>> 24 & 0x0F] + HEX_CHARS[h4 >>> 20 & 0x0F] + HEX_CHARS[h4 >>> 16 & 0x0F] + HEX_CHARS[h4 >>> 12 & 0x0F] + HEX_CHARS[h4 >>> 8 & 0x0F] + HEX_CHARS[h4 >>> 4 & 0x0F] + HEX_CHARS[h4 & 0x0F] + HEX_CHARS[h5 >>> 28 & 0x0F] + HEX_CHARS[h5 >>> 24 & 0x0F] + HEX_CHARS[h5 >>> 20 & 0x0F] + HEX_CHARS[h5 >>> 16 & 0x0F] + HEX_CHARS[h5 >>> 12 & 0x0F] + HEX_CHARS[h5 >>> 8 & 0x0F] + HEX_CHARS[h5 >>> 4 & 0x0F] + HEX_CHARS[h5 & 0x0F] + HEX_CHARS[h6 >>> 28 & 0x0F] + HEX_CHARS[h6 >>> 24 & 0x0F] + HEX_CHARS[h6 >>> 20 & 0x0F] + HEX_CHARS[h6 >>> 16 & 0x0F] + HEX_CHARS[h6 >>> 12 & 0x0F] + HEX_CHARS[h6 >>> 8 & 0x0F] + HEX_CHARS[h6 >>> 4 & 0x0F] + HEX_CHARS[h6 & 0x0F];\n    if (!this.is224) {\n      hex += HEX_CHARS[h7 >>> 28 & 0x0F] + HEX_CHARS[h7 >>> 24 & 0x0F] + HEX_CHARS[h7 >>> 20 & 0x0F] + HEX_CHARS[h7 >>> 16 & 0x0F] + HEX_CHARS[h7 >>> 12 & 0x0F] + HEX_CHARS[h7 >>> 8 & 0x0F] + HEX_CHARS[h7 >>> 4 & 0x0F] + HEX_CHARS[h7 & 0x0F];\n    }\n    return hex;\n  };\n  Sha256.prototype.toString = Sha256.prototype.hex;\n  Sha256.prototype.digest = function () {\n    this.finalize();\n    var h0 = this.h0,\n      h1 = this.h1,\n      h2 = this.h2,\n      h3 = this.h3,\n      h4 = this.h4,\n      h5 = this.h5,\n      h6 = this.h6,\n      h7 = this.h7;\n    var arr = [h0 >>> 24 & 0xFF, h0 >>> 16 & 0xFF, h0 >>> 8 & 0xFF, h0 & 0xFF, h1 >>> 24 & 0xFF, h1 >>> 16 & 0xFF, h1 >>> 8 & 0xFF, h1 & 0xFF, h2 >>> 24 & 0xFF, h2 >>> 16 & 0xFF, h2 >>> 8 & 0xFF, h2 & 0xFF, h3 >>> 24 & 0xFF, h3 >>> 16 & 0xFF, h3 >>> 8 & 0xFF, h3 & 0xFF, h4 >>> 24 & 0xFF, h4 >>> 16 & 0xFF, h4 >>> 8 & 0xFF, h4 & 0xFF, h5 >>> 24 & 0xFF, h5 >>> 16 & 0xFF, h5 >>> 8 & 0xFF, h5 & 0xFF, h6 >>> 24 & 0xFF, h6 >>> 16 & 0xFF, h6 >>> 8 & 0xFF, h6 & 0xFF];\n    if (!this.is224) {\n      arr.push(h7 >>> 24 & 0xFF, h7 >>> 16 & 0xFF, h7 >>> 8 & 0xFF, h7 & 0xFF);\n    }\n    return arr;\n  };\n  Sha256.prototype.array = Sha256.prototype.digest;\n  Sha256.prototype.arrayBuffer = function () {\n    this.finalize();\n    var buffer = new ArrayBuffer(this.is224 ? 28 : 32);\n    var dataView = new DataView(buffer);\n    dataView.setUint32(0, this.h0);\n    dataView.setUint32(4, this.h1);\n    dataView.setUint32(8, this.h2);\n    dataView.setUint32(12, this.h3);\n    dataView.setUint32(16, this.h4);\n    dataView.setUint32(20, this.h5);\n    dataView.setUint32(24, this.h6);\n    if (!this.is224) {\n      dataView.setUint32(28, this.h7);\n    }\n    return buffer;\n  };\n  function HmacSha256(key, is224, sharedMemory) {\n    var i,\n      type = typeof key;\n    if (type === 'string') {\n      var bytes = [],\n        length = key.length,\n        index = 0,\n        code;\n      for (i = 0; i < length; ++i) {\n        code = key.charCodeAt(i);\n        if (code < 0x80) {\n          bytes[index++] = code;\n        } else if (code < 0x800) {\n          bytes[index++] = 0xc0 | code >>> 6;\n          bytes[index++] = 0x80 | code & 0x3f;\n        } else if (code < 0xd800 || code >= 0xe000) {\n          bytes[index++] = 0xe0 | code >>> 12;\n          bytes[index++] = 0x80 | code >>> 6 & 0x3f;\n          bytes[index++] = 0x80 | code & 0x3f;\n        } else {\n          code = 0x10000 + ((code & 0x3ff) << 10 | key.charCodeAt(++i) & 0x3ff);\n          bytes[index++] = 0xf0 | code >>> 18;\n          bytes[index++] = 0x80 | code >>> 12 & 0x3f;\n          bytes[index++] = 0x80 | code >>> 6 & 0x3f;\n          bytes[index++] = 0x80 | code & 0x3f;\n        }\n      }\n      key = bytes;\n    } else {\n      if (type === 'object') {\n        if (key === null) {\n          throw new Error(ERROR);\n        } else if (ARRAY_BUFFER && key.constructor === ArrayBuffer) {\n          key = new Uint8Array(key);\n        } else if (!Array.isArray(key)) {\n          if (!ARRAY_BUFFER || !ArrayBuffer.isView(key)) {\n            throw new Error(ERROR);\n          }\n        }\n      } else {\n        throw new Error(ERROR);\n      }\n    }\n    if (key.length > 64) {\n      key = new Sha256(is224, true).update(key).array();\n    }\n    var oKeyPad = [],\n      iKeyPad = [];\n    for (i = 0; i < 64; ++i) {\n      var b = key[i] || 0;\n      oKeyPad[i] = 0x5c ^ b;\n      iKeyPad[i] = 0x36 ^ b;\n    }\n    Sha256.call(this, is224, sharedMemory);\n    this.update(iKeyPad);\n    this.oKeyPad = oKeyPad;\n    this.inner = true;\n    this.sharedMemory = sharedMemory;\n  }\n  HmacSha256.prototype = new Sha256();\n  HmacSha256.prototype.finalize = function () {\n    Sha256.prototype.finalize.call(this);\n    if (this.inner) {\n      this.inner = false;\n      var innerHash = this.array();\n      Sha256.call(this, this.is224, this.sharedMemory);\n      this.update(this.oKeyPad);\n      this.update(innerHash);\n      Sha256.prototype.finalize.call(this);\n    }\n  };\n  var exports = createMethod();\n  exports.sha256 = exports;\n  exports.sha224 = createMethod(true);\n  exports.sha256.hmac = createHmacMethod();\n  exports.sha224.hmac = createHmacMethod(true);\n  if (COMMON_JS) {\n    module.exports = exports;\n  } else {\n    root.sha256 = exports.sha256;\n    root.sha224 = exports.sha224;\n    if (AMD) {\n      define(function () {\n        return exports;\n      });\n    }\n  }\n})();", "map": {"version": 3, "names": ["ERROR", "WINDOW", "window", "root", "JS_SHA256_NO_WINDOW", "WEB_WORKER", "self", "NODE_JS", "JS_SHA256_NO_NODE_JS", "process", "versions", "node", "type", "global", "COMMON_JS", "JS_SHA256_NO_COMMON_JS", "module", "exports", "AMD", "define", "amd", "ARRAY_BUFFER", "JS_SHA256_NO_ARRAY_BUFFER", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HEX_CHARS", "split", "EXTRA", "SHIFT", "K", "OUTPUT_TYPES", "blocks", "Array", "isArray", "obj", "Object", "prototype", "toString", "call", "JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "constructor", "createOutputMethod", "outputType", "is224", "message", "Sha256", "update", "createMethod", "method", "nodeWrap", "create", "i", "length", "crypto", "require", "<PERSON><PERSON><PERSON>", "algorithm", "bufferFrom", "from", "JS_SHA256_NO_BUFFER_FROM", "nodeMethod", "createHash", "digest", "undefined", "Error", "Uint8Array", "createHmacOutputMethod", "key", "HmacSha256", "createHmacMethod", "sharedMemory", "h0", "h1", "h2", "h3", "h4", "h5", "h6", "h7", "block", "start", "bytes", "hBytes", "finalized", "hashed", "first", "notString", "code", "index", "charCodeAt", "lastByteIndex", "hash", "finalize", "a", "b", "c", "d", "e", "f", "g", "h", "j", "s0", "s1", "maj", "t1", "t2", "ch", "ab", "da", "cd", "bc", "chromeBugWorkAround", "hex", "arr", "push", "array", "arrayBuffer", "dataView", "DataView", "setUint32", "oKeyPad", "iKeyPad", "inner", "innerHash", "sha256", "sha224", "hmac"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/js-sha256/src/sha256.js"], "sourcesContent": ["/**\n * [js-sha256]{@link https://github.com/emn178/js-sha256}\n *\n * @version 0.11.1\n * <AUTHOR> <PERSON><PERSON><PERSON> [<EMAIL>]\n * @copyright Chen, <PERSON><PERSON><PERSON><PERSON> 2014-2025\n * @license MIT\n */\n/*jslint bitwise: true */\n(function () {\n  'use strict';\n\n  var ERROR = 'input is invalid type';\n  var WINDOW = typeof window === 'object';\n  var root = WINDOW ? window : {};\n  if (root.JS_SHA256_NO_WINDOW) {\n    WINDOW = false;\n  }\n  var WEB_WORKER = !WINDOW && typeof self === 'object';\n  var NODE_JS = !root.JS_SHA256_NO_NODE_JS && typeof process === 'object' && process.versions && process.versions.node && process.type != 'renderer';\n  if (NODE_JS) {\n    root = global;\n  } else if (WEB_WORKER) {\n    root = self;\n  }\n  var COMMON_JS = !root.JS_SHA256_NO_COMMON_JS && typeof module === 'object' && module.exports;\n  var AMD = typeof define === 'function' && define.amd;\n  var ARRAY_BUFFER = !root.JS_SHA256_NO_ARRAY_BUFFER && typeof ArrayBuffer !== 'undefined';\n  var HEX_CHARS = '0123456789abcdef'.split('');\n  var EXTRA = [-**********, 8388608, 32768, 128];\n  var SHIFT = [24, 16, 8, 0];\n  var K = [\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n  ];\n  var OUTPUT_TYPES = ['hex', 'array', 'digest', 'arrayBuffer'];\n\n  var blocks = [];\n\n  if (root.JS_SHA256_NO_NODE_JS || !Array.isArray) {\n    Array.isArray = function (obj) {\n      return Object.prototype.toString.call(obj) === '[object Array]';\n    };\n  }\n\n  if (ARRAY_BUFFER && (root.JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW || !ArrayBuffer.isView)) {\n    ArrayBuffer.isView = function (obj) {\n      return typeof obj === 'object' && obj.buffer && obj.buffer.constructor === ArrayBuffer;\n    };\n  }\n\n  var createOutputMethod = function (outputType, is224) {\n    return function (message) {\n      return new Sha256(is224, true).update(message)[outputType]();\n    };\n  };\n\n  var createMethod = function (is224) {\n    var method = createOutputMethod('hex', is224);\n    if (NODE_JS) {\n      method = nodeWrap(method, is224);\n    }\n    method.create = function () {\n      return new Sha256(is224);\n    };\n    method.update = function (message) {\n      return method.create().update(message);\n    };\n    for (var i = 0; i < OUTPUT_TYPES.length; ++i) {\n      var type = OUTPUT_TYPES[i];\n      method[type] = createOutputMethod(type, is224);\n    }\n    return method;\n  };\n\n  var nodeWrap = function (method, is224) {\n    var crypto = require('crypto')\n    var Buffer = require('buffer').Buffer;\n    var algorithm = is224 ? 'sha224' : 'sha256';\n    var bufferFrom;\n    if (Buffer.from && !root.JS_SHA256_NO_BUFFER_FROM) {\n      bufferFrom = Buffer.from;\n    } else {\n      bufferFrom = function (message) {\n        return new Buffer(message);\n      };\n    }\n    var nodeMethod = function (message) {\n      if (typeof message === 'string') {\n        return crypto.createHash(algorithm).update(message, 'utf8').digest('hex');\n      } else {\n        if (message === null || message === undefined) {\n          throw new Error(ERROR);\n        } else if (message.constructor === ArrayBuffer) {\n          message = new Uint8Array(message);\n        }\n      }\n      if (Array.isArray(message) || ArrayBuffer.isView(message) ||\n        message.constructor === Buffer) {\n        return crypto.createHash(algorithm).update(bufferFrom(message)).digest('hex');\n      } else {\n        return method(message);\n      }\n    };\n    return nodeMethod;\n  };\n\n  var createHmacOutputMethod = function (outputType, is224) {\n    return function (key, message) {\n      return new HmacSha256(key, is224, true).update(message)[outputType]();\n    };\n  };\n\n  var createHmacMethod = function (is224) {\n    var method = createHmacOutputMethod('hex', is224);\n    method.create = function (key) {\n      return new HmacSha256(key, is224);\n    };\n    method.update = function (key, message) {\n      return method.create(key).update(message);\n    };\n    for (var i = 0; i < OUTPUT_TYPES.length; ++i) {\n      var type = OUTPUT_TYPES[i];\n      method[type] = createHmacOutputMethod(type, is224);\n    }\n    return method;\n  };\n\n  function Sha256(is224, sharedMemory) {\n    if (sharedMemory) {\n      blocks[0] = blocks[16] = blocks[1] = blocks[2] = blocks[3] =\n        blocks[4] = blocks[5] = blocks[6] = blocks[7] =\n        blocks[8] = blocks[9] = blocks[10] = blocks[11] =\n        blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n      this.blocks = blocks;\n    } else {\n      this.blocks = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\n    }\n\n    if (is224) {\n      this.h0 = 0xc1059ed8;\n      this.h1 = 0x367cd507;\n      this.h2 = 0x3070dd17;\n      this.h3 = 0xf70e5939;\n      this.h4 = 0xffc00b31;\n      this.h5 = 0x68581511;\n      this.h6 = 0x64f98fa7;\n      this.h7 = 0xbefa4fa4;\n    } else { // 256\n      this.h0 = 0x6a09e667;\n      this.h1 = 0xbb67ae85;\n      this.h2 = 0x3c6ef372;\n      this.h3 = 0xa54ff53a;\n      this.h4 = 0x510e527f;\n      this.h5 = 0x9b05688c;\n      this.h6 = 0x1f83d9ab;\n      this.h7 = 0x5be0cd19;\n    }\n\n    this.block = this.start = this.bytes = this.hBytes = 0;\n    this.finalized = this.hashed = false;\n    this.first = true;\n    this.is224 = is224;\n  }\n\n  Sha256.prototype.update = function (message) {\n    if (this.finalized) {\n      return;\n    }\n    var notString, type = typeof message;\n    if (type !== 'string') {\n      if (type === 'object') {\n        if (message === null) {\n          throw new Error(ERROR);\n        } else if (ARRAY_BUFFER && message.constructor === ArrayBuffer) {\n          message = new Uint8Array(message);\n        } else if (!Array.isArray(message)) {\n          if (!ARRAY_BUFFER || !ArrayBuffer.isView(message)) {\n            throw new Error(ERROR);\n          }\n        }\n      } else {\n        throw new Error(ERROR);\n      }\n      notString = true;\n    }\n    var code, index = 0, i, length = message.length, blocks = this.blocks;\n    while (index < length) {\n      if (this.hashed) {\n        this.hashed = false;\n        blocks[0] = this.block;\n        this.block = blocks[16] = blocks[1] = blocks[2] = blocks[3] =\n          blocks[4] = blocks[5] = blocks[6] = blocks[7] =\n          blocks[8] = blocks[9] = blocks[10] = blocks[11] =\n          blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n      }\n\n      if (notString) {\n        for (i = this.start; index < length && i < 64; ++index) {\n          blocks[i >>> 2] |= message[index] << SHIFT[i++ & 3];\n        }\n      } else {\n        for (i = this.start; index < length && i < 64; ++index) {\n          code = message.charCodeAt(index);\n          if (code < 0x80) {\n            blocks[i >>> 2] |= code << SHIFT[i++ & 3];\n          } else if (code < 0x800) {\n            blocks[i >>> 2] |= (0xc0 | (code >>> 6)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | (code & 0x3f)) << SHIFT[i++ & 3];\n          } else if (code < 0xd800 || code >= 0xe000) {\n            blocks[i >>> 2] |= (0xe0 | (code >>> 12)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | ((code >>> 6) & 0x3f)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | (code & 0x3f)) << SHIFT[i++ & 3];\n          } else {\n            code = 0x10000 + (((code & 0x3ff) << 10) | (message.charCodeAt(++index) & 0x3ff));\n            blocks[i >>> 2] |= (0xf0 | (code >>> 18)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | ((code >>> 12) & 0x3f)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | ((code >>> 6) & 0x3f)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | (code & 0x3f)) << SHIFT[i++ & 3];\n          }\n        }\n      }\n\n      this.lastByteIndex = i;\n      this.bytes += i - this.start;\n      if (i >= 64) {\n        this.block = blocks[16];\n        this.start = i - 64;\n        this.hash();\n        this.hashed = true;\n      } else {\n        this.start = i;\n      }\n    }\n    if (this.bytes > 4294967295) {\n      this.hBytes += this.bytes / 4294967296 << 0;\n      this.bytes = this.bytes % 4294967296;\n    }\n    return this;\n  };\n\n  Sha256.prototype.finalize = function () {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    var blocks = this.blocks, i = this.lastByteIndex;\n    blocks[16] = this.block;\n    blocks[i >>> 2] |= EXTRA[i & 3];\n    this.block = blocks[16];\n    if (i >= 56) {\n      if (!this.hashed) {\n        this.hash();\n      }\n      blocks[0] = this.block;\n      blocks[16] = blocks[1] = blocks[2] = blocks[3] =\n        blocks[4] = blocks[5] = blocks[6] = blocks[7] =\n        blocks[8] = blocks[9] = blocks[10] = blocks[11] =\n        blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n    }\n    blocks[14] = this.hBytes << 3 | this.bytes >>> 29;\n    blocks[15] = this.bytes << 3;\n    this.hash();\n  };\n\n  Sha256.prototype.hash = function () {\n    var a = this.h0, b = this.h1, c = this.h2, d = this.h3, e = this.h4, f = this.h5, g = this.h6,\n      h = this.h7, blocks = this.blocks, j, s0, s1, maj, t1, t2, ch, ab, da, cd, bc;\n\n    for (j = 16; j < 64; ++j) {\n      // rightrotate\n      t1 = blocks[j - 15];\n      s0 = ((t1 >>> 7) | (t1 << 25)) ^ ((t1 >>> 18) | (t1 << 14)) ^ (t1 >>> 3);\n      t1 = blocks[j - 2];\n      s1 = ((t1 >>> 17) | (t1 << 15)) ^ ((t1 >>> 19) | (t1 << 13)) ^ (t1 >>> 10);\n      blocks[j] = blocks[j - 16] + s0 + blocks[j - 7] + s1 << 0;\n    }\n\n    bc = b & c;\n    for (j = 0; j < 64; j += 4) {\n      if (this.first) {\n        if (this.is224) {\n          ab = 300032;\n          t1 = blocks[0] - 1413257819;\n          h = t1 - 150054599 << 0;\n          d = t1 + 24177077 << 0;\n        } else {\n          ab = 704751109;\n          t1 = blocks[0] - 210244248;\n          h = t1 - 1521486534 << 0;\n          d = t1 + 143694565 << 0;\n        }\n        this.first = false;\n      } else {\n        s0 = ((a >>> 2) | (a << 30)) ^ ((a >>> 13) | (a << 19)) ^ ((a >>> 22) | (a << 10));\n        s1 = ((e >>> 6) | (e << 26)) ^ ((e >>> 11) | (e << 21)) ^ ((e >>> 25) | (e << 7));\n        ab = a & b;\n        maj = ab ^ (a & c) ^ bc;\n        ch = (e & f) ^ (~e & g);\n        t1 = h + s1 + ch + K[j] + blocks[j];\n        t2 = s0 + maj;\n        h = d + t1 << 0;\n        d = t1 + t2 << 0;\n      }\n      s0 = ((d >>> 2) | (d << 30)) ^ ((d >>> 13) | (d << 19)) ^ ((d >>> 22) | (d << 10));\n      s1 = ((h >>> 6) | (h << 26)) ^ ((h >>> 11) | (h << 21)) ^ ((h >>> 25) | (h << 7));\n      da = d & a;\n      maj = da ^ (d & b) ^ ab;\n      ch = (h & e) ^ (~h & f);\n      t1 = g + s1 + ch + K[j + 1] + blocks[j + 1];\n      t2 = s0 + maj;\n      g = c + t1 << 0;\n      c = t1 + t2 << 0;\n      s0 = ((c >>> 2) | (c << 30)) ^ ((c >>> 13) | (c << 19)) ^ ((c >>> 22) | (c << 10));\n      s1 = ((g >>> 6) | (g << 26)) ^ ((g >>> 11) | (g << 21)) ^ ((g >>> 25) | (g << 7));\n      cd = c & d;\n      maj = cd ^ (c & a) ^ da;\n      ch = (g & h) ^ (~g & e);\n      t1 = f + s1 + ch + K[j + 2] + blocks[j + 2];\n      t2 = s0 + maj;\n      f = b + t1 << 0;\n      b = t1 + t2 << 0;\n      s0 = ((b >>> 2) | (b << 30)) ^ ((b >>> 13) | (b << 19)) ^ ((b >>> 22) | (b << 10));\n      s1 = ((f >>> 6) | (f << 26)) ^ ((f >>> 11) | (f << 21)) ^ ((f >>> 25) | (f << 7));\n      bc = b & c;\n      maj = bc ^ (b & d) ^ cd;\n      ch = (f & g) ^ (~f & h);\n      t1 = e + s1 + ch + K[j + 3] + blocks[j + 3];\n      t2 = s0 + maj;\n      e = a + t1 << 0;\n      a = t1 + t2 << 0;\n      this.chromeBugWorkAround = true;\n    }\n\n    this.h0 = this.h0 + a << 0;\n    this.h1 = this.h1 + b << 0;\n    this.h2 = this.h2 + c << 0;\n    this.h3 = this.h3 + d << 0;\n    this.h4 = this.h4 + e << 0;\n    this.h5 = this.h5 + f << 0;\n    this.h6 = this.h6 + g << 0;\n    this.h7 = this.h7 + h << 0;\n  };\n\n  Sha256.prototype.hex = function () {\n    this.finalize();\n\n    var h0 = this.h0, h1 = this.h1, h2 = this.h2, h3 = this.h3, h4 = this.h4, h5 = this.h5,\n      h6 = this.h6, h7 = this.h7;\n\n    var hex = HEX_CHARS[(h0 >>> 28) & 0x0F] + HEX_CHARS[(h0 >>> 24) & 0x0F] +\n      HEX_CHARS[(h0 >>> 20) & 0x0F] + HEX_CHARS[(h0 >>> 16) & 0x0F] +\n      HEX_CHARS[(h0 >>> 12) & 0x0F] + HEX_CHARS[(h0 >>> 8) & 0x0F] +\n      HEX_CHARS[(h0 >>> 4) & 0x0F] + HEX_CHARS[h0 & 0x0F] +\n      HEX_CHARS[(h1 >>> 28) & 0x0F] + HEX_CHARS[(h1 >>> 24) & 0x0F] +\n      HEX_CHARS[(h1 >>> 20) & 0x0F] + HEX_CHARS[(h1 >>> 16) & 0x0F] +\n      HEX_CHARS[(h1 >>> 12) & 0x0F] + HEX_CHARS[(h1 >>> 8) & 0x0F] +\n      HEX_CHARS[(h1 >>> 4) & 0x0F] + HEX_CHARS[h1 & 0x0F] +\n      HEX_CHARS[(h2 >>> 28) & 0x0F] + HEX_CHARS[(h2 >>> 24) & 0x0F] +\n      HEX_CHARS[(h2 >>> 20) & 0x0F] + HEX_CHARS[(h2 >>> 16) & 0x0F] +\n      HEX_CHARS[(h2 >>> 12) & 0x0F] + HEX_CHARS[(h2 >>> 8) & 0x0F] +\n      HEX_CHARS[(h2 >>> 4) & 0x0F] + HEX_CHARS[h2 & 0x0F] +\n      HEX_CHARS[(h3 >>> 28) & 0x0F] + HEX_CHARS[(h3 >>> 24) & 0x0F] +\n      HEX_CHARS[(h3 >>> 20) & 0x0F] + HEX_CHARS[(h3 >>> 16) & 0x0F] +\n      HEX_CHARS[(h3 >>> 12) & 0x0F] + HEX_CHARS[(h3 >>> 8) & 0x0F] +\n      HEX_CHARS[(h3 >>> 4) & 0x0F] + HEX_CHARS[h3 & 0x0F] +\n      HEX_CHARS[(h4 >>> 28) & 0x0F] + HEX_CHARS[(h4 >>> 24) & 0x0F] +\n      HEX_CHARS[(h4 >>> 20) & 0x0F] + HEX_CHARS[(h4 >>> 16) & 0x0F] +\n      HEX_CHARS[(h4 >>> 12) & 0x0F] + HEX_CHARS[(h4 >>> 8) & 0x0F] +\n      HEX_CHARS[(h4 >>> 4) & 0x0F] + HEX_CHARS[h4 & 0x0F] +\n      HEX_CHARS[(h5 >>> 28) & 0x0F] + HEX_CHARS[(h5 >>> 24) & 0x0F] +\n      HEX_CHARS[(h5 >>> 20) & 0x0F] + HEX_CHARS[(h5 >>> 16) & 0x0F] +\n      HEX_CHARS[(h5 >>> 12) & 0x0F] + HEX_CHARS[(h5 >>> 8) & 0x0F] +\n      HEX_CHARS[(h5 >>> 4) & 0x0F] + HEX_CHARS[h5 & 0x0F] +\n      HEX_CHARS[(h6 >>> 28) & 0x0F] + HEX_CHARS[(h6 >>> 24) & 0x0F] +\n      HEX_CHARS[(h6 >>> 20) & 0x0F] + HEX_CHARS[(h6 >>> 16) & 0x0F] +\n      HEX_CHARS[(h6 >>> 12) & 0x0F] + HEX_CHARS[(h6 >>> 8) & 0x0F] +\n      HEX_CHARS[(h6 >>> 4) & 0x0F] + HEX_CHARS[h6 & 0x0F];\n    if (!this.is224) {\n      hex += HEX_CHARS[(h7 >>> 28) & 0x0F] + HEX_CHARS[(h7 >>> 24) & 0x0F] +\n        HEX_CHARS[(h7 >>> 20) & 0x0F] + HEX_CHARS[(h7 >>> 16) & 0x0F] +\n        HEX_CHARS[(h7 >>> 12) & 0x0F] + HEX_CHARS[(h7 >>> 8) & 0x0F] +\n        HEX_CHARS[(h7 >>> 4) & 0x0F] + HEX_CHARS[h7 & 0x0F];\n    }\n    return hex;\n  };\n\n  Sha256.prototype.toString = Sha256.prototype.hex;\n\n  Sha256.prototype.digest = function () {\n    this.finalize();\n\n    var h0 = this.h0, h1 = this.h1, h2 = this.h2, h3 = this.h3, h4 = this.h4, h5 = this.h5,\n      h6 = this.h6, h7 = this.h7;\n\n    var arr = [\n      (h0 >>> 24) & 0xFF, (h0 >>> 16) & 0xFF, (h0 >>> 8) & 0xFF, h0 & 0xFF,\n      (h1 >>> 24) & 0xFF, (h1 >>> 16) & 0xFF, (h1 >>> 8) & 0xFF, h1 & 0xFF,\n      (h2 >>> 24) & 0xFF, (h2 >>> 16) & 0xFF, (h2 >>> 8) & 0xFF, h2 & 0xFF,\n      (h3 >>> 24) & 0xFF, (h3 >>> 16) & 0xFF, (h3 >>> 8) & 0xFF, h3 & 0xFF,\n      (h4 >>> 24) & 0xFF, (h4 >>> 16) & 0xFF, (h4 >>> 8) & 0xFF, h4 & 0xFF,\n      (h5 >>> 24) & 0xFF, (h5 >>> 16) & 0xFF, (h5 >>> 8) & 0xFF, h5 & 0xFF,\n      (h6 >>> 24) & 0xFF, (h6 >>> 16) & 0xFF, (h6 >>> 8) & 0xFF, h6 & 0xFF\n    ];\n    if (!this.is224) {\n      arr.push((h7 >>> 24) & 0xFF, (h7 >>> 16) & 0xFF, (h7 >>> 8) & 0xFF, h7 & 0xFF);\n    }\n    return arr;\n  };\n\n  Sha256.prototype.array = Sha256.prototype.digest;\n\n  Sha256.prototype.arrayBuffer = function () {\n    this.finalize();\n\n    var buffer = new ArrayBuffer(this.is224 ? 28 : 32);\n    var dataView = new DataView(buffer);\n    dataView.setUint32(0, this.h0);\n    dataView.setUint32(4, this.h1);\n    dataView.setUint32(8, this.h2);\n    dataView.setUint32(12, this.h3);\n    dataView.setUint32(16, this.h4);\n    dataView.setUint32(20, this.h5);\n    dataView.setUint32(24, this.h6);\n    if (!this.is224) {\n      dataView.setUint32(28, this.h7);\n    }\n    return buffer;\n  };\n\n  function HmacSha256(key, is224, sharedMemory) {\n    var i, type = typeof key;\n    if (type === 'string') {\n      var bytes = [], length = key.length, index = 0, code;\n      for (i = 0; i < length; ++i) {\n        code = key.charCodeAt(i);\n        if (code < 0x80) {\n          bytes[index++] = code;\n        } else if (code < 0x800) {\n          bytes[index++] = (0xc0 | (code >>> 6));\n          bytes[index++] = (0x80 | (code & 0x3f));\n        } else if (code < 0xd800 || code >= 0xe000) {\n          bytes[index++] = (0xe0 | (code >>> 12));\n          bytes[index++] = (0x80 | ((code >>> 6) & 0x3f));\n          bytes[index++] = (0x80 | (code & 0x3f));\n        } else {\n          code = 0x10000 + (((code & 0x3ff) << 10) | (key.charCodeAt(++i) & 0x3ff));\n          bytes[index++] = (0xf0 | (code >>> 18));\n          bytes[index++] = (0x80 | ((code >>> 12) & 0x3f));\n          bytes[index++] = (0x80 | ((code >>> 6) & 0x3f));\n          bytes[index++] = (0x80 | (code & 0x3f));\n        }\n      }\n      key = bytes;\n    } else {\n      if (type === 'object') {\n        if (key === null) {\n          throw new Error(ERROR);\n        } else if (ARRAY_BUFFER && key.constructor === ArrayBuffer) {\n          key = new Uint8Array(key);\n        } else if (!Array.isArray(key)) {\n          if (!ARRAY_BUFFER || !ArrayBuffer.isView(key)) {\n            throw new Error(ERROR);\n          }\n        }\n      } else {\n        throw new Error(ERROR);\n      }\n    }\n\n    if (key.length > 64) {\n      key = (new Sha256(is224, true)).update(key).array();\n    }\n\n    var oKeyPad = [], iKeyPad = [];\n    for (i = 0; i < 64; ++i) {\n      var b = key[i] || 0;\n      oKeyPad[i] = 0x5c ^ b;\n      iKeyPad[i] = 0x36 ^ b;\n    }\n\n    Sha256.call(this, is224, sharedMemory);\n\n    this.update(iKeyPad);\n    this.oKeyPad = oKeyPad;\n    this.inner = true;\n    this.sharedMemory = sharedMemory;\n  }\n  HmacSha256.prototype = new Sha256();\n\n  HmacSha256.prototype.finalize = function () {\n    Sha256.prototype.finalize.call(this);\n    if (this.inner) {\n      this.inner = false;\n      var innerHash = this.array();\n      Sha256.call(this, this.is224, this.sharedMemory);\n      this.update(this.oKeyPad);\n      this.update(innerHash);\n      Sha256.prototype.finalize.call(this);\n    }\n  };\n\n  var exports = createMethod();\n  exports.sha256 = exports;\n  exports.sha224 = createMethod(true);\n  exports.sha256.hmac = createHmacMethod();\n  exports.sha224.hmac = createHmacMethod(true);\n\n  if (COMMON_JS) {\n    module.exports = exports;\n  } else {\n    root.sha256 = exports.sha256;\n    root.sha224 = exports.sha224;\n    if (AMD) {\n      define(function () {\n        return exports;\n      });\n    }\n  }\n})();\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,YAAY;EACX,YAAY;;EAEZ,IAAIA,KAAK,GAAG,uBAAuB;EACnC,IAAIC,MAAM,GAAG,OAAOC,MAAM,KAAK,QAAQ;EACvC,IAAIC,IAAI,GAAGF,MAAM,GAAGC,MAAM,GAAG,CAAC,CAAC;EAC/B,IAAIC,IAAI,CAACC,mBAAmB,EAAE;IAC5BH,MAAM,GAAG,KAAK;EAChB;EACA,IAAII,UAAU,GAAG,CAACJ,MAAM,IAAI,OAAOK,IAAI,KAAK,QAAQ;EACpD,IAAIC,OAAO,GAAG,CAACJ,IAAI,CAACK,oBAAoB,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACC,QAAQ,IAAID,OAAO,CAACC,QAAQ,CAACC,IAAI,IAAIF,OAAO,CAACG,IAAI,IAAI,UAAU;EAClJ,IAAIL,OAAO,EAAE;IACXJ,IAAI,GAAGU,MAAM;EACf,CAAC,MAAM,IAAIR,UAAU,EAAE;IACrBF,IAAI,GAAGG,IAAI;EACb;EACA,IAAIQ,SAAS,GAAG,CAACX,IAAI,CAACY,sBAAsB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,OAAO;EAC5F,IAAIC,GAAG,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG;EACpD,IAAIC,YAAY,GAAG,CAAClB,IAAI,CAACmB,yBAAyB,IAAI,OAAOC,WAAW,KAAK,WAAW;EACxF,IAAIC,SAAS,GAAG,kBAAkB,CAACC,KAAK,CAAC,EAAE,CAAC;EAC5C,IAAIC,KAAK,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC;EAC9C,IAAIC,KAAK,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1B,IAAIC,CAAC,GAAG,CACN,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAC/F;EACD,IAAIC,YAAY,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC;EAE5D,IAAIC,MAAM,GAAG,EAAE;EAEf,IAAI3B,IAAI,CAACK,oBAAoB,IAAI,CAACuB,KAAK,CAACC,OAAO,EAAE;IAC/CD,KAAK,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAE;MAC7B,OAAOC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,GAAG,CAAC,KAAK,gBAAgB;IACjE,CAAC;EACH;EAEA,IAAIZ,YAAY,KAAKlB,IAAI,CAACmC,iCAAiC,IAAI,CAACf,WAAW,CAACgB,MAAM,CAAC,EAAE;IACnFhB,WAAW,CAACgB,MAAM,GAAG,UAAUN,GAAG,EAAE;MAClC,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACO,MAAM,IAAIP,GAAG,CAACO,MAAM,CAACC,WAAW,KAAKlB,WAAW;IACxF,CAAC;EACH;EAEA,IAAImB,kBAAkB,GAAG,SAAAA,CAAUC,UAAU,EAAEC,KAAK,EAAE;IACpD,OAAO,UAAUC,OAAO,EAAE;MACxB,OAAO,IAAIC,MAAM,CAACF,KAAK,EAAE,IAAI,CAAC,CAACG,MAAM,CAACF,OAAO,CAAC,CAACF,UAAU,CAAC,CAAC,CAAC;IAC9D,CAAC;EACH,CAAC;EAED,IAAIK,YAAY,GAAG,SAAAA,CAAUJ,KAAK,EAAE;IAClC,IAAIK,MAAM,GAAGP,kBAAkB,CAAC,KAAK,EAAEE,KAAK,CAAC;IAC7C,IAAIrC,OAAO,EAAE;MACX0C,MAAM,GAAGC,QAAQ,CAACD,MAAM,EAAEL,KAAK,CAAC;IAClC;IACAK,MAAM,CAACE,MAAM,GAAG,YAAY;MAC1B,OAAO,IAAIL,MAAM,CAACF,KAAK,CAAC;IAC1B,CAAC;IACDK,MAAM,CAACF,MAAM,GAAG,UAAUF,OAAO,EAAE;MACjC,OAAOI,MAAM,CAACE,MAAM,CAAC,CAAC,CAACJ,MAAM,CAACF,OAAO,CAAC;IACxC,CAAC;IACD,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,YAAY,CAACwB,MAAM,EAAE,EAAED,CAAC,EAAE;MAC5C,IAAIxC,IAAI,GAAGiB,YAAY,CAACuB,CAAC,CAAC;MAC1BH,MAAM,CAACrC,IAAI,CAAC,GAAG8B,kBAAkB,CAAC9B,IAAI,EAAEgC,KAAK,CAAC;IAChD;IACA,OAAOK,MAAM;EACf,CAAC;EAED,IAAIC,QAAQ,GAAG,SAAAA,CAAUD,MAAM,EAAEL,KAAK,EAAE;IACtC,IAAIU,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;IAC9B,IAAIC,MAAM,GAAGD,OAAO,CAAC,QAAQ,CAAC,CAACC,MAAM;IACrC,IAAIC,SAAS,GAAGb,KAAK,GAAG,QAAQ,GAAG,QAAQ;IAC3C,IAAIc,UAAU;IACd,IAAIF,MAAM,CAACG,IAAI,IAAI,CAACxD,IAAI,CAACyD,wBAAwB,EAAE;MACjDF,UAAU,GAAGF,MAAM,CAACG,IAAI;IAC1B,CAAC,MAAM;MACLD,UAAU,GAAG,SAAAA,CAAUb,OAAO,EAAE;QAC9B,OAAO,IAAIW,MAAM,CAACX,OAAO,CAAC;MAC5B,CAAC;IACH;IACA,IAAIgB,UAAU,GAAG,SAAAA,CAAUhB,OAAO,EAAE;MAClC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAOS,MAAM,CAACQ,UAAU,CAACL,SAAS,CAAC,CAACV,MAAM,CAACF,OAAO,EAAE,MAAM,CAAC,CAACkB,MAAM,CAAC,KAAK,CAAC;MAC3E,CAAC,MAAM;QACL,IAAIlB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKmB,SAAS,EAAE;UAC7C,MAAM,IAAIC,KAAK,CAACjE,KAAK,CAAC;QACxB,CAAC,MAAM,IAAI6C,OAAO,CAACJ,WAAW,KAAKlB,WAAW,EAAE;UAC9CsB,OAAO,GAAG,IAAIqB,UAAU,CAACrB,OAAO,CAAC;QACnC;MACF;MACA,IAAId,KAAK,CAACC,OAAO,CAACa,OAAO,CAAC,IAAItB,WAAW,CAACgB,MAAM,CAACM,OAAO,CAAC,IACvDA,OAAO,CAACJ,WAAW,KAAKe,MAAM,EAAE;QAChC,OAAOF,MAAM,CAACQ,UAAU,CAACL,SAAS,CAAC,CAACV,MAAM,CAACW,UAAU,CAACb,OAAO,CAAC,CAAC,CAACkB,MAAM,CAAC,KAAK,CAAC;MAC/E,CAAC,MAAM;QACL,OAAOd,MAAM,CAACJ,OAAO,CAAC;MACxB;IACF,CAAC;IACD,OAAOgB,UAAU;EACnB,CAAC;EAED,IAAIM,sBAAsB,GAAG,SAAAA,CAAUxB,UAAU,EAAEC,KAAK,EAAE;IACxD,OAAO,UAAUwB,GAAG,EAAEvB,OAAO,EAAE;MAC7B,OAAO,IAAIwB,UAAU,CAACD,GAAG,EAAExB,KAAK,EAAE,IAAI,CAAC,CAACG,MAAM,CAACF,OAAO,CAAC,CAACF,UAAU,CAAC,CAAC,CAAC;IACvE,CAAC;EACH,CAAC;EAED,IAAI2B,gBAAgB,GAAG,SAAAA,CAAU1B,KAAK,EAAE;IACtC,IAAIK,MAAM,GAAGkB,sBAAsB,CAAC,KAAK,EAAEvB,KAAK,CAAC;IACjDK,MAAM,CAACE,MAAM,GAAG,UAAUiB,GAAG,EAAE;MAC7B,OAAO,IAAIC,UAAU,CAACD,GAAG,EAAExB,KAAK,CAAC;IACnC,CAAC;IACDK,MAAM,CAACF,MAAM,GAAG,UAAUqB,GAAG,EAAEvB,OAAO,EAAE;MACtC,OAAOI,MAAM,CAACE,MAAM,CAACiB,GAAG,CAAC,CAACrB,MAAM,CAACF,OAAO,CAAC;IAC3C,CAAC;IACD,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,YAAY,CAACwB,MAAM,EAAE,EAAED,CAAC,EAAE;MAC5C,IAAIxC,IAAI,GAAGiB,YAAY,CAACuB,CAAC,CAAC;MAC1BH,MAAM,CAACrC,IAAI,CAAC,GAAGuD,sBAAsB,CAACvD,IAAI,EAAEgC,KAAK,CAAC;IACpD;IACA,OAAOK,MAAM;EACf,CAAC;EAED,SAASH,MAAMA,CAACF,KAAK,EAAE2B,YAAY,EAAE;IACnC,IAAIA,YAAY,EAAE;MAChBzC,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GACxDA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAC7CA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAC/CA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC;MACvD,IAAI,CAACA,MAAM,GAAGA,MAAM;IACtB,CAAC,MAAM;MACL,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnE;IAEA,IAAIc,KAAK,EAAE;MACT,IAAI,CAAC4B,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;IACtB,CAAC,MAAM;MAAE;MACP,IAAI,CAACP,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;MACpB,IAAI,CAACC,EAAE,GAAG,UAAU;IACtB;IAEA,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,MAAM,GAAG,CAAC;IACtD,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,MAAM,GAAG,KAAK;IACpC,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC1C,KAAK,GAAGA,KAAK;EACpB;EAEAE,MAAM,CAACX,SAAS,CAACY,MAAM,GAAG,UAAUF,OAAO,EAAE;IAC3C,IAAI,IAAI,CAACuC,SAAS,EAAE;MAClB;IACF;IACA,IAAIG,SAAS;MAAE3E,IAAI,GAAG,OAAOiC,OAAO;IACpC,IAAIjC,IAAI,KAAK,QAAQ,EAAE;MACrB,IAAIA,IAAI,KAAK,QAAQ,EAAE;QACrB,IAAIiC,OAAO,KAAK,IAAI,EAAE;UACpB,MAAM,IAAIoB,KAAK,CAACjE,KAAK,CAAC;QACxB,CAAC,MAAM,IAAIqB,YAAY,IAAIwB,OAAO,CAACJ,WAAW,KAAKlB,WAAW,EAAE;UAC9DsB,OAAO,GAAG,IAAIqB,UAAU,CAACrB,OAAO,CAAC;QACnC,CAAC,MAAM,IAAI,CAACd,KAAK,CAACC,OAAO,CAACa,OAAO,CAAC,EAAE;UAClC,IAAI,CAACxB,YAAY,IAAI,CAACE,WAAW,CAACgB,MAAM,CAACM,OAAO,CAAC,EAAE;YACjD,MAAM,IAAIoB,KAAK,CAACjE,KAAK,CAAC;UACxB;QACF;MACF,CAAC,MAAM;QACL,MAAM,IAAIiE,KAAK,CAACjE,KAAK,CAAC;MACxB;MACAuF,SAAS,GAAG,IAAI;IAClB;IACA,IAAIC,IAAI;MAAEC,KAAK,GAAG,CAAC;MAAErC,CAAC;MAAEC,MAAM,GAAGR,OAAO,CAACQ,MAAM;MAAEvB,MAAM,GAAG,IAAI,CAACA,MAAM;IACrE,OAAO2D,KAAK,GAAGpC,MAAM,EAAE;MACrB,IAAI,IAAI,CAACgC,MAAM,EAAE;QACf,IAAI,CAACA,MAAM,GAAG,KAAK;QACnBvD,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAACkD,KAAK;QACtB,IAAI,CAACA,KAAK,GAAGlD,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GACzDA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAC7CA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAC/CA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC;MACzD;MAEA,IAAIyD,SAAS,EAAE;QACb,KAAKnC,CAAC,GAAG,IAAI,CAAC6B,KAAK,EAAEQ,KAAK,GAAGpC,MAAM,IAAID,CAAC,GAAG,EAAE,EAAE,EAAEqC,KAAK,EAAE;UACtD3D,MAAM,CAACsB,CAAC,KAAK,CAAC,CAAC,IAAIP,OAAO,CAAC4C,KAAK,CAAC,IAAI9D,KAAK,CAACyB,CAAC,EAAE,GAAG,CAAC,CAAC;QACrD;MACF,CAAC,MAAM;QACL,KAAKA,CAAC,GAAG,IAAI,CAAC6B,KAAK,EAAEQ,KAAK,GAAGpC,MAAM,IAAID,CAAC,GAAG,EAAE,EAAE,EAAEqC,KAAK,EAAE;UACtDD,IAAI,GAAG3C,OAAO,CAAC6C,UAAU,CAACD,KAAK,CAAC;UAChC,IAAID,IAAI,GAAG,IAAI,EAAE;YACf1D,MAAM,CAACsB,CAAC,KAAK,CAAC,CAAC,IAAIoC,IAAI,IAAI7D,KAAK,CAACyB,CAAC,EAAE,GAAG,CAAC,CAAC;UAC3C,CAAC,MAAM,IAAIoC,IAAI,GAAG,KAAK,EAAE;YACvB1D,MAAM,CAACsB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,GAAIoC,IAAI,KAAK,CAAE,KAAK7D,KAAK,CAACyB,CAAC,EAAE,GAAG,CAAC,CAAC;YAC1DtB,MAAM,CAACsB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,GAAIoC,IAAI,GAAG,IAAK,KAAK7D,KAAK,CAACyB,CAAC,EAAE,GAAG,CAAC,CAAC;UAC7D,CAAC,MAAM,IAAIoC,IAAI,GAAG,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAE;YAC1C1D,MAAM,CAACsB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,GAAIoC,IAAI,KAAK,EAAG,KAAK7D,KAAK,CAACyB,CAAC,EAAE,GAAG,CAAC,CAAC;YAC3DtB,MAAM,CAACsB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,GAAKoC,IAAI,KAAK,CAAC,GAAI,IAAK,KAAK7D,KAAK,CAACyB,CAAC,EAAE,GAAG,CAAC,CAAC;YACnEtB,MAAM,CAACsB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,GAAIoC,IAAI,GAAG,IAAK,KAAK7D,KAAK,CAACyB,CAAC,EAAE,GAAG,CAAC,CAAC;UAC7D,CAAC,MAAM;YACLoC,IAAI,GAAG,OAAO,IAAK,CAACA,IAAI,GAAG,KAAK,KAAK,EAAE,GAAK3C,OAAO,CAAC6C,UAAU,CAAC,EAAED,KAAK,CAAC,GAAG,KAAM,CAAC;YACjF3D,MAAM,CAACsB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,GAAIoC,IAAI,KAAK,EAAG,KAAK7D,KAAK,CAACyB,CAAC,EAAE,GAAG,CAAC,CAAC;YAC3DtB,MAAM,CAACsB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,GAAKoC,IAAI,KAAK,EAAE,GAAI,IAAK,KAAK7D,KAAK,CAACyB,CAAC,EAAE,GAAG,CAAC,CAAC;YACpEtB,MAAM,CAACsB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,GAAKoC,IAAI,KAAK,CAAC,GAAI,IAAK,KAAK7D,KAAK,CAACyB,CAAC,EAAE,GAAG,CAAC,CAAC;YACnEtB,MAAM,CAACsB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,GAAIoC,IAAI,GAAG,IAAK,KAAK7D,KAAK,CAACyB,CAAC,EAAE,GAAG,CAAC,CAAC;UAC7D;QACF;MACF;MAEA,IAAI,CAACuC,aAAa,GAAGvC,CAAC;MACtB,IAAI,CAAC8B,KAAK,IAAI9B,CAAC,GAAG,IAAI,CAAC6B,KAAK;MAC5B,IAAI7B,CAAC,IAAI,EAAE,EAAE;QACX,IAAI,CAAC4B,KAAK,GAAGlD,MAAM,CAAC,EAAE,CAAC;QACvB,IAAI,CAACmD,KAAK,GAAG7B,CAAC,GAAG,EAAE;QACnB,IAAI,CAACwC,IAAI,CAAC,CAAC;QACX,IAAI,CAACP,MAAM,GAAG,IAAI;MACpB,CAAC,MAAM;QACL,IAAI,CAACJ,KAAK,GAAG7B,CAAC;MAChB;IACF;IACA,IAAI,IAAI,CAAC8B,KAAK,GAAG,UAAU,EAAE;MAC3B,IAAI,CAACC,MAAM,IAAI,IAAI,CAACD,KAAK,GAAG,UAAU,IAAI,CAAC;MAC3C,IAAI,CAACA,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,UAAU;IACtC;IACA,OAAO,IAAI;EACb,CAAC;EAEDpC,MAAM,CAACX,SAAS,CAAC0D,QAAQ,GAAG,YAAY;IACtC,IAAI,IAAI,CAACT,SAAS,EAAE;MAClB;IACF;IACA,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,IAAItD,MAAM,GAAG,IAAI,CAACA,MAAM;MAAEsB,CAAC,GAAG,IAAI,CAACuC,aAAa;IAChD7D,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAACkD,KAAK;IACvBlD,MAAM,CAACsB,CAAC,KAAK,CAAC,CAAC,IAAI1B,KAAK,CAAC0B,CAAC,GAAG,CAAC,CAAC;IAC/B,IAAI,CAAC4B,KAAK,GAAGlD,MAAM,CAAC,EAAE,CAAC;IACvB,IAAIsB,CAAC,IAAI,EAAE,EAAE;MACX,IAAI,CAAC,IAAI,CAACiC,MAAM,EAAE;QAChB,IAAI,CAACO,IAAI,CAAC,CAAC;MACb;MACA9D,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAACkD,KAAK;MACtBlD,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAC5CA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAC7CA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAC/CA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC;IACzD;IACAA,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAACqD,MAAM,IAAI,CAAC,GAAG,IAAI,CAACD,KAAK,KAAK,EAAE;IACjDpD,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAACoD,KAAK,IAAI,CAAC;IAC5B,IAAI,CAACU,IAAI,CAAC,CAAC;EACb,CAAC;EAED9C,MAAM,CAACX,SAAS,CAACyD,IAAI,GAAG,YAAY;IAClC,IAAIE,CAAC,GAAG,IAAI,CAACtB,EAAE;MAAEuB,CAAC,GAAG,IAAI,CAACtB,EAAE;MAAEuB,CAAC,GAAG,IAAI,CAACtB,EAAE;MAAEuB,CAAC,GAAG,IAAI,CAACtB,EAAE;MAAEuB,CAAC,GAAG,IAAI,CAACtB,EAAE;MAAEuB,CAAC,GAAG,IAAI,CAACtB,EAAE;MAAEuB,CAAC,GAAG,IAAI,CAACtB,EAAE;MAC3FuB,CAAC,GAAG,IAAI,CAACtB,EAAE;MAAEjD,MAAM,GAAG,IAAI,CAACA,MAAM;MAAEwE,CAAC;MAAEC,EAAE;MAAEC,EAAE;MAAEC,GAAG;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;IAE/E,KAAKV,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MACxB;MACAI,EAAE,GAAG5E,MAAM,CAACwE,CAAC,GAAG,EAAE,CAAC;MACnBC,EAAE,GAAG,CAAEG,EAAE,KAAK,CAAC,GAAKA,EAAE,IAAI,EAAG,KAAMA,EAAE,KAAK,EAAE,GAAKA,EAAE,IAAI,EAAG,CAAC,GAAIA,EAAE,KAAK,CAAE;MACxEA,EAAE,GAAG5E,MAAM,CAACwE,CAAC,GAAG,CAAC,CAAC;MAClBE,EAAE,GAAG,CAAEE,EAAE,KAAK,EAAE,GAAKA,EAAE,IAAI,EAAG,KAAMA,EAAE,KAAK,EAAE,GAAKA,EAAE,IAAI,EAAG,CAAC,GAAIA,EAAE,KAAK,EAAG;MAC1E5E,MAAM,CAACwE,CAAC,CAAC,GAAGxE,MAAM,CAACwE,CAAC,GAAG,EAAE,CAAC,GAAGC,EAAE,GAAGzE,MAAM,CAACwE,CAAC,GAAG,CAAC,CAAC,GAAGE,EAAE,IAAI,CAAC;IAC3D;IAEAQ,EAAE,GAAGjB,CAAC,GAAGC,CAAC;IACV,KAAKM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC1B,IAAI,IAAI,CAAChB,KAAK,EAAE;QACd,IAAI,IAAI,CAAC1C,KAAK,EAAE;UACdiE,EAAE,GAAG,MAAM;UACXH,EAAE,GAAG5E,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;UAC3BuE,CAAC,GAAGK,EAAE,GAAG,SAAS,IAAI,CAAC;UACvBT,CAAC,GAAGS,EAAE,GAAG,QAAQ,IAAI,CAAC;QACxB,CAAC,MAAM;UACLG,EAAE,GAAG,SAAS;UACdH,EAAE,GAAG5E,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS;UAC1BuE,CAAC,GAAGK,EAAE,GAAG,UAAU,IAAI,CAAC;UACxBT,CAAC,GAAGS,EAAE,GAAG,SAAS,IAAI,CAAC;QACzB;QACA,IAAI,CAACpB,KAAK,GAAG,KAAK;MACpB,CAAC,MAAM;QACLiB,EAAE,GAAG,CAAET,CAAC,KAAK,CAAC,GAAKA,CAAC,IAAI,EAAG,KAAMA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC,IAAKA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC;QAClFU,EAAE,GAAG,CAAEN,CAAC,KAAK,CAAC,GAAKA,CAAC,IAAI,EAAG,KAAMA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC,IAAKA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,CAAE,CAAC;QACjFW,EAAE,GAAGf,CAAC,GAAGC,CAAC;QACVU,GAAG,GAAGI,EAAE,GAAIf,CAAC,GAAGE,CAAE,GAAGgB,EAAE;QACvBJ,EAAE,GAAIV,CAAC,GAAGC,CAAC,GAAK,CAACD,CAAC,GAAGE,CAAE;QACvBM,EAAE,GAAGL,CAAC,GAAGG,EAAE,GAAGI,EAAE,GAAGhF,CAAC,CAAC0E,CAAC,CAAC,GAAGxE,MAAM,CAACwE,CAAC,CAAC;QACnCK,EAAE,GAAGJ,EAAE,GAAGE,GAAG;QACbJ,CAAC,GAAGJ,CAAC,GAAGS,EAAE,IAAI,CAAC;QACfT,CAAC,GAAGS,EAAE,GAAGC,EAAE,IAAI,CAAC;MAClB;MACAJ,EAAE,GAAG,CAAEN,CAAC,KAAK,CAAC,GAAKA,CAAC,IAAI,EAAG,KAAMA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC,IAAKA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC;MAClFO,EAAE,GAAG,CAAEH,CAAC,KAAK,CAAC,GAAKA,CAAC,IAAI,EAAG,KAAMA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC,IAAKA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,CAAE,CAAC;MACjFS,EAAE,GAAGb,CAAC,GAAGH,CAAC;MACVW,GAAG,GAAGK,EAAE,GAAIb,CAAC,GAAGF,CAAE,GAAGc,EAAE;MACvBD,EAAE,GAAIP,CAAC,GAAGH,CAAC,GAAK,CAACG,CAAC,GAAGF,CAAE;MACvBO,EAAE,GAAGN,CAAC,GAAGI,EAAE,GAAGI,EAAE,GAAGhF,CAAC,CAAC0E,CAAC,GAAG,CAAC,CAAC,GAAGxE,MAAM,CAACwE,CAAC,GAAG,CAAC,CAAC;MAC3CK,EAAE,GAAGJ,EAAE,GAAGE,GAAG;MACbL,CAAC,GAAGJ,CAAC,GAAGU,EAAE,IAAI,CAAC;MACfV,CAAC,GAAGU,EAAE,GAAGC,EAAE,IAAI,CAAC;MAChBJ,EAAE,GAAG,CAAEP,CAAC,KAAK,CAAC,GAAKA,CAAC,IAAI,EAAG,KAAMA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC,IAAKA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC;MAClFQ,EAAE,GAAG,CAAEJ,CAAC,KAAK,CAAC,GAAKA,CAAC,IAAI,EAAG,KAAMA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC,IAAKA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,CAAE,CAAC;MACjFW,EAAE,GAAGf,CAAC,GAAGC,CAAC;MACVQ,GAAG,GAAGM,EAAE,GAAIf,CAAC,GAAGF,CAAE,GAAGgB,EAAE;MACvBF,EAAE,GAAIR,CAAC,GAAGC,CAAC,GAAK,CAACD,CAAC,GAAGF,CAAE;MACvBQ,EAAE,GAAGP,CAAC,GAAGK,EAAE,GAAGI,EAAE,GAAGhF,CAAC,CAAC0E,CAAC,GAAG,CAAC,CAAC,GAAGxE,MAAM,CAACwE,CAAC,GAAG,CAAC,CAAC;MAC3CK,EAAE,GAAGJ,EAAE,GAAGE,GAAG;MACbN,CAAC,GAAGJ,CAAC,GAAGW,EAAE,IAAI,CAAC;MACfX,CAAC,GAAGW,EAAE,GAAGC,EAAE,IAAI,CAAC;MAChBJ,EAAE,GAAG,CAAER,CAAC,KAAK,CAAC,GAAKA,CAAC,IAAI,EAAG,KAAMA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC,IAAKA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC;MAClFS,EAAE,GAAG,CAAEL,CAAC,KAAK,CAAC,GAAKA,CAAC,IAAI,EAAG,KAAMA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC,IAAKA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,CAAE,CAAC;MACjFa,EAAE,GAAGjB,CAAC,GAAGC,CAAC;MACVS,GAAG,GAAGO,EAAE,GAAIjB,CAAC,GAAGE,CAAE,GAAGc,EAAE;MACvBH,EAAE,GAAIT,CAAC,GAAGC,CAAC,GAAK,CAACD,CAAC,GAAGE,CAAE;MACvBK,EAAE,GAAGR,CAAC,GAAGM,EAAE,GAAGI,EAAE,GAAGhF,CAAC,CAAC0E,CAAC,GAAG,CAAC,CAAC,GAAGxE,MAAM,CAACwE,CAAC,GAAG,CAAC,CAAC;MAC3CK,EAAE,GAAGJ,EAAE,GAAGE,GAAG;MACbP,CAAC,GAAGJ,CAAC,GAAGY,EAAE,IAAI,CAAC;MACfZ,CAAC,GAAGY,EAAE,GAAGC,EAAE,IAAI,CAAC;MAChB,IAAI,CAACM,mBAAmB,GAAG,IAAI;IACjC;IAEA,IAAI,CAACzC,EAAE,GAAG,IAAI,CAACA,EAAE,GAAGsB,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACrB,EAAE,GAAG,IAAI,CAACA,EAAE,GAAGsB,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACrB,EAAE,GAAG,IAAI,CAACA,EAAE,GAAGsB,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACrB,EAAE,GAAG,IAAI,CAACA,EAAE,GAAGsB,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACrB,EAAE,GAAG,IAAI,CAACA,EAAE,GAAGsB,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACrB,EAAE,GAAG,IAAI,CAACA,EAAE,GAAGsB,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACrB,EAAE,GAAG,IAAI,CAACA,EAAE,GAAGsB,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACrB,EAAE,GAAG,IAAI,CAACA,EAAE,GAAGsB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAEDvD,MAAM,CAACX,SAAS,CAAC+E,GAAG,GAAG,YAAY;IACjC,IAAI,CAACrB,QAAQ,CAAC,CAAC;IAEf,IAAIrB,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;MACpFC,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;IAE5B,IAAImC,GAAG,GAAG1F,SAAS,CAAEgD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGhD,SAAS,CAAEgD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GACrEhD,SAAS,CAAEgD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGhD,SAAS,CAAEgD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DhD,SAAS,CAAEgD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGhD,SAAS,CAAEgD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAC5DhD,SAAS,CAAEgD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAAGhD,SAAS,CAACgD,EAAE,GAAG,IAAI,CAAC,GACnDhD,SAAS,CAAEiD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGjD,SAAS,CAAEiD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DjD,SAAS,CAAEiD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGjD,SAAS,CAAEiD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DjD,SAAS,CAAEiD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGjD,SAAS,CAAEiD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAC5DjD,SAAS,CAAEiD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAAGjD,SAAS,CAACiD,EAAE,GAAG,IAAI,CAAC,GACnDjD,SAAS,CAAEkD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGlD,SAAS,CAAEkD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DlD,SAAS,CAAEkD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGlD,SAAS,CAAEkD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DlD,SAAS,CAAEkD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGlD,SAAS,CAAEkD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAC5DlD,SAAS,CAAEkD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAAGlD,SAAS,CAACkD,EAAE,GAAG,IAAI,CAAC,GACnDlD,SAAS,CAAEmD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGnD,SAAS,CAAEmD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DnD,SAAS,CAAEmD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGnD,SAAS,CAAEmD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DnD,SAAS,CAAEmD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGnD,SAAS,CAAEmD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAC5DnD,SAAS,CAAEmD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAAGnD,SAAS,CAACmD,EAAE,GAAG,IAAI,CAAC,GACnDnD,SAAS,CAAEoD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGpD,SAAS,CAAEoD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DpD,SAAS,CAAEoD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGpD,SAAS,CAAEoD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DpD,SAAS,CAAEoD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGpD,SAAS,CAAEoD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAC5DpD,SAAS,CAAEoD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAAGpD,SAAS,CAACoD,EAAE,GAAG,IAAI,CAAC,GACnDpD,SAAS,CAAEqD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGrD,SAAS,CAAEqD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DrD,SAAS,CAAEqD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGrD,SAAS,CAAEqD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DrD,SAAS,CAAEqD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGrD,SAAS,CAAEqD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAC5DrD,SAAS,CAAEqD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAAGrD,SAAS,CAACqD,EAAE,GAAG,IAAI,CAAC,GACnDrD,SAAS,CAAEsD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGtD,SAAS,CAAEsD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DtD,SAAS,CAAEsD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGtD,SAAS,CAAEsD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DtD,SAAS,CAAEsD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGtD,SAAS,CAAEsD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAC5DtD,SAAS,CAAEsD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAAGtD,SAAS,CAACsD,EAAE,GAAG,IAAI,CAAC;IACrD,IAAI,CAAC,IAAI,CAAClC,KAAK,EAAE;MACfsE,GAAG,IAAI1F,SAAS,CAAEuD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGvD,SAAS,CAAEuD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAClEvD,SAAS,CAAEuD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGvD,SAAS,CAAEuD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAC7DvD,SAAS,CAAEuD,EAAE,KAAK,EAAE,GAAI,IAAI,CAAC,GAAGvD,SAAS,CAAEuD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAC5DvD,SAAS,CAAEuD,EAAE,KAAK,CAAC,GAAI,IAAI,CAAC,GAAGvD,SAAS,CAACuD,EAAE,GAAG,IAAI,CAAC;IACvD;IACA,OAAOmC,GAAG;EACZ,CAAC;EAEDpE,MAAM,CAACX,SAAS,CAACC,QAAQ,GAAGU,MAAM,CAACX,SAAS,CAAC+E,GAAG;EAEhDpE,MAAM,CAACX,SAAS,CAAC4B,MAAM,GAAG,YAAY;IACpC,IAAI,CAAC8B,QAAQ,CAAC,CAAC;IAEf,IAAIrB,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;MACpFC,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;IAE5B,IAAIoC,GAAG,GAAG,CACP3C,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,CAAC,GAAI,IAAI,EAAEA,EAAE,GAAG,IAAI,EACnEC,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,CAAC,GAAI,IAAI,EAAEA,EAAE,GAAG,IAAI,EACnEC,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,CAAC,GAAI,IAAI,EAAEA,EAAE,GAAG,IAAI,EACnEC,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,CAAC,GAAI,IAAI,EAAEA,EAAE,GAAG,IAAI,EACnEC,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,CAAC,GAAI,IAAI,EAAEA,EAAE,GAAG,IAAI,EACnEC,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,CAAC,GAAI,IAAI,EAAEA,EAAE,GAAG,IAAI,EACnEC,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,CAAC,GAAI,IAAI,EAAEA,EAAE,GAAG,IAAI,CACrE;IACD,IAAI,CAAC,IAAI,CAAClC,KAAK,EAAE;MACfuE,GAAG,CAACC,IAAI,CAAErC,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,EAAE,GAAI,IAAI,EAAGA,EAAE,KAAK,CAAC,GAAI,IAAI,EAAEA,EAAE,GAAG,IAAI,CAAC;IAChF;IACA,OAAOoC,GAAG;EACZ,CAAC;EAEDrE,MAAM,CAACX,SAAS,CAACkF,KAAK,GAAGvE,MAAM,CAACX,SAAS,CAAC4B,MAAM;EAEhDjB,MAAM,CAACX,SAAS,CAACmF,WAAW,GAAG,YAAY;IACzC,IAAI,CAACzB,QAAQ,CAAC,CAAC;IAEf,IAAIrD,MAAM,GAAG,IAAIjB,WAAW,CAAC,IAAI,CAACqB,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;IAClD,IAAI2E,QAAQ,GAAG,IAAIC,QAAQ,CAAChF,MAAM,CAAC;IACnC+E,QAAQ,CAACE,SAAS,CAAC,CAAC,EAAE,IAAI,CAACjD,EAAE,CAAC;IAC9B+C,QAAQ,CAACE,SAAS,CAAC,CAAC,EAAE,IAAI,CAAChD,EAAE,CAAC;IAC9B8C,QAAQ,CAACE,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC/C,EAAE,CAAC;IAC9B6C,QAAQ,CAACE,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC9C,EAAE,CAAC;IAC/B4C,QAAQ,CAACE,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC7C,EAAE,CAAC;IAC/B2C,QAAQ,CAACE,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC5C,EAAE,CAAC;IAC/B0C,QAAQ,CAACE,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC3C,EAAE,CAAC;IAC/B,IAAI,CAAC,IAAI,CAAClC,KAAK,EAAE;MACf2E,QAAQ,CAACE,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC1C,EAAE,CAAC;IACjC;IACA,OAAOvC,MAAM;EACf,CAAC;EAED,SAAS6B,UAAUA,CAACD,GAAG,EAAExB,KAAK,EAAE2B,YAAY,EAAE;IAC5C,IAAInB,CAAC;MAAExC,IAAI,GAAG,OAAOwD,GAAG;IACxB,IAAIxD,IAAI,KAAK,QAAQ,EAAE;MACrB,IAAIsE,KAAK,GAAG,EAAE;QAAE7B,MAAM,GAAGe,GAAG,CAACf,MAAM;QAAEoC,KAAK,GAAG,CAAC;QAAED,IAAI;MACpD,KAAKpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,MAAM,EAAE,EAAED,CAAC,EAAE;QAC3BoC,IAAI,GAAGpB,GAAG,CAACsB,UAAU,CAACtC,CAAC,CAAC;QACxB,IAAIoC,IAAI,GAAG,IAAI,EAAE;UACfN,KAAK,CAACO,KAAK,EAAE,CAAC,GAAGD,IAAI;QACvB,CAAC,MAAM,IAAIA,IAAI,GAAG,KAAK,EAAE;UACvBN,KAAK,CAACO,KAAK,EAAE,CAAC,GAAI,IAAI,GAAID,IAAI,KAAK,CAAG;UACtCN,KAAK,CAACO,KAAK,EAAE,CAAC,GAAI,IAAI,GAAID,IAAI,GAAG,IAAM;QACzC,CAAC,MAAM,IAAIA,IAAI,GAAG,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAE;UAC1CN,KAAK,CAACO,KAAK,EAAE,CAAC,GAAI,IAAI,GAAID,IAAI,KAAK,EAAI;UACvCN,KAAK,CAACO,KAAK,EAAE,CAAC,GAAI,IAAI,GAAKD,IAAI,KAAK,CAAC,GAAI,IAAM;UAC/CN,KAAK,CAACO,KAAK,EAAE,CAAC,GAAI,IAAI,GAAID,IAAI,GAAG,IAAM;QACzC,CAAC,MAAM;UACLA,IAAI,GAAG,OAAO,IAAK,CAACA,IAAI,GAAG,KAAK,KAAK,EAAE,GAAKpB,GAAG,CAACsB,UAAU,CAAC,EAAEtC,CAAC,CAAC,GAAG,KAAM,CAAC;UACzE8B,KAAK,CAACO,KAAK,EAAE,CAAC,GAAI,IAAI,GAAID,IAAI,KAAK,EAAI;UACvCN,KAAK,CAACO,KAAK,EAAE,CAAC,GAAI,IAAI,GAAKD,IAAI,KAAK,EAAE,GAAI,IAAM;UAChDN,KAAK,CAACO,KAAK,EAAE,CAAC,GAAI,IAAI,GAAKD,IAAI,KAAK,CAAC,GAAI,IAAM;UAC/CN,KAAK,CAACO,KAAK,EAAE,CAAC,GAAI,IAAI,GAAID,IAAI,GAAG,IAAM;QACzC;MACF;MACApB,GAAG,GAAGc,KAAK;IACb,CAAC,MAAM;MACL,IAAItE,IAAI,KAAK,QAAQ,EAAE;QACrB,IAAIwD,GAAG,KAAK,IAAI,EAAE;UAChB,MAAM,IAAIH,KAAK,CAACjE,KAAK,CAAC;QACxB,CAAC,MAAM,IAAIqB,YAAY,IAAI+C,GAAG,CAAC3B,WAAW,KAAKlB,WAAW,EAAE;UAC1D6C,GAAG,GAAG,IAAIF,UAAU,CAACE,GAAG,CAAC;QAC3B,CAAC,MAAM,IAAI,CAACrC,KAAK,CAACC,OAAO,CAACoC,GAAG,CAAC,EAAE;UAC9B,IAAI,CAAC/C,YAAY,IAAI,CAACE,WAAW,CAACgB,MAAM,CAAC6B,GAAG,CAAC,EAAE;YAC7C,MAAM,IAAIH,KAAK,CAACjE,KAAK,CAAC;UACxB;QACF;MACF,CAAC,MAAM;QACL,MAAM,IAAIiE,KAAK,CAACjE,KAAK,CAAC;MACxB;IACF;IAEA,IAAIoE,GAAG,CAACf,MAAM,GAAG,EAAE,EAAE;MACnBe,GAAG,GAAI,IAAItB,MAAM,CAACF,KAAK,EAAE,IAAI,CAAC,CAAEG,MAAM,CAACqB,GAAG,CAAC,CAACiD,KAAK,CAAC,CAAC;IACrD;IAEA,IAAIK,OAAO,GAAG,EAAE;MAAEC,OAAO,GAAG,EAAE;IAC9B,KAAKvE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MACvB,IAAI2C,CAAC,GAAG3B,GAAG,CAAChB,CAAC,CAAC,IAAI,CAAC;MACnBsE,OAAO,CAACtE,CAAC,CAAC,GAAG,IAAI,GAAG2C,CAAC;MACrB4B,OAAO,CAACvE,CAAC,CAAC,GAAG,IAAI,GAAG2C,CAAC;IACvB;IAEAjD,MAAM,CAACT,IAAI,CAAC,IAAI,EAAEO,KAAK,EAAE2B,YAAY,CAAC;IAEtC,IAAI,CAACxB,MAAM,CAAC4E,OAAO,CAAC;IACpB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,KAAK,GAAG,IAAI;IACjB,IAAI,CAACrD,YAAY,GAAGA,YAAY;EAClC;EACAF,UAAU,CAAClC,SAAS,GAAG,IAAIW,MAAM,CAAC,CAAC;EAEnCuB,UAAU,CAAClC,SAAS,CAAC0D,QAAQ,GAAG,YAAY;IAC1C/C,MAAM,CAACX,SAAS,CAAC0D,QAAQ,CAACxD,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,IAAI,CAACuF,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,GAAG,KAAK;MAClB,IAAIC,SAAS,GAAG,IAAI,CAACR,KAAK,CAAC,CAAC;MAC5BvE,MAAM,CAACT,IAAI,CAAC,IAAI,EAAE,IAAI,CAACO,KAAK,EAAE,IAAI,CAAC2B,YAAY,CAAC;MAChD,IAAI,CAACxB,MAAM,CAAC,IAAI,CAAC2E,OAAO,CAAC;MACzB,IAAI,CAAC3E,MAAM,CAAC8E,SAAS,CAAC;MACtB/E,MAAM,CAACX,SAAS,CAAC0D,QAAQ,CAACxD,IAAI,CAAC,IAAI,CAAC;IACtC;EACF,CAAC;EAED,IAAIpB,OAAO,GAAG+B,YAAY,CAAC,CAAC;EAC5B/B,OAAO,CAAC6G,MAAM,GAAG7G,OAAO;EACxBA,OAAO,CAAC8G,MAAM,GAAG/E,YAAY,CAAC,IAAI,CAAC;EACnC/B,OAAO,CAAC6G,MAAM,CAACE,IAAI,GAAG1D,gBAAgB,CAAC,CAAC;EACxCrD,OAAO,CAAC8G,MAAM,CAACC,IAAI,GAAG1D,gBAAgB,CAAC,IAAI,CAAC;EAE5C,IAAIxD,SAAS,EAAE;IACbE,MAAM,CAACC,OAAO,GAAGA,OAAO;EAC1B,CAAC,MAAM;IACLd,IAAI,CAAC2H,MAAM,GAAG7G,OAAO,CAAC6G,MAAM;IAC5B3H,IAAI,CAAC4H,MAAM,GAAG9G,OAAO,CAAC8G,MAAM;IAC5B,IAAI7G,GAAG,EAAE;MACPC,MAAM,CAAC,YAAY;QACjB,OAAOF,OAAO;MAChB,CAAC,CAAC;IACJ;EACF;AACF,CAAC,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}