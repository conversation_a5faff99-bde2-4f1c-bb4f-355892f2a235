{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport LineDraw from '../helper/LineDraw.js';\nimport EffectLine from '../helper/EffectLine.js';\nimport Line from '../helper/Line.js';\nimport Polyline from '../helper/Polyline.js';\nimport EffectPolyline from '../helper/EffectPolyline.js';\nimport LargeLineDraw from '../helper/LargeLineDraw.js';\nimport linesLayout from './linesLayout.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport ChartView from '../../view/Chart.js';\nvar LinesView = /** @class */function (_super) {\n  __extends(LinesView, _super);\n  function LinesView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LinesView.type;\n    return _this;\n  }\n  LinesView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var lineDraw = this._updateLineDraw(data, seriesModel);\n    var zlevel = seriesModel.get('zlevel');\n    var trailLength = seriesModel.get(['effect', 'trailLength']);\n    var zr = api.getZr();\n    // Avoid the drag cause ghost shadow\n    // FIXME Better way ?\n    // SVG doesn't support\n    var isSvg = zr.painter.getType() === 'svg';\n    if (!isSvg) {\n      zr.painter.getLayer(zlevel).clear(true);\n    }\n    // Config layer with motion blur\n    if (this._lastZlevel != null && !isSvg) {\n      zr.configLayer(this._lastZlevel, {\n        motionBlur: false\n      });\n    }\n    if (this._showEffect(seriesModel) && trailLength > 0) {\n      if (!isSvg) {\n        zr.configLayer(zlevel, {\n          motionBlur: true,\n          lastFrameAlpha: Math.max(Math.min(trailLength / 10 + 0.9, 1), 0)\n        });\n      } else if (process.env.NODE_ENV !== 'production') {\n        console.warn('SVG render mode doesn\\'t support lines with trail effect');\n      }\n    }\n    lineDraw.updateData(data);\n    var clipPath = seriesModel.get('clip', true) && createClipPath(seriesModel.coordinateSystem, false, seriesModel);\n    if (clipPath) {\n      this.group.setClipPath(clipPath);\n    } else {\n      this.group.removeClipPath();\n    }\n    this._lastZlevel = zlevel;\n    this._finished = true;\n  };\n  LinesView.prototype.incrementalPrepareRender = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var lineDraw = this._updateLineDraw(data, seriesModel);\n    lineDraw.incrementalPrepareUpdate(data);\n    this._clearLayer(api);\n    this._finished = false;\n  };\n  LinesView.prototype.incrementalRender = function (taskParams, seriesModel, ecModel) {\n    this._lineDraw.incrementalUpdate(taskParams, seriesModel.getData());\n    this._finished = taskParams.end === seriesModel.getData().count();\n  };\n  LinesView.prototype.eachRendered = function (cb) {\n    this._lineDraw && this._lineDraw.eachRendered(cb);\n  };\n  LinesView.prototype.updateTransform = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var pipelineContext = seriesModel.pipelineContext;\n    if (!this._finished || pipelineContext.large || pipelineContext.progressiveRender) {\n      // TODO Don't have to do update in large mode. Only do it when there are millions of data.\n      return {\n        update: true\n      };\n    } else {\n      // TODO Use same logic with ScatterView.\n      // Manually update layout\n      var res = linesLayout.reset(seriesModel, ecModel, api);\n      if (res.progress) {\n        res.progress({\n          start: 0,\n          end: data.count(),\n          count: data.count()\n        }, data);\n      }\n      // Not in large mode\n      this._lineDraw.updateLayout();\n      this._clearLayer(api);\n    }\n  };\n  LinesView.prototype._updateLineDraw = function (data, seriesModel) {\n    var lineDraw = this._lineDraw;\n    var hasEffect = this._showEffect(seriesModel);\n    var isPolyline = !!seriesModel.get('polyline');\n    var pipelineContext = seriesModel.pipelineContext;\n    var isLargeDraw = pipelineContext.large;\n    if (process.env.NODE_ENV !== 'production') {\n      if (hasEffect && isLargeDraw) {\n        console.warn('Large lines not support effect');\n      }\n    }\n    if (!lineDraw || hasEffect !== this._hasEffet || isPolyline !== this._isPolyline || isLargeDraw !== this._isLargeDraw) {\n      if (lineDraw) {\n        lineDraw.remove();\n      }\n      lineDraw = this._lineDraw = isLargeDraw ? new LargeLineDraw() : new LineDraw(isPolyline ? hasEffect ? EffectPolyline : Polyline : hasEffect ? EffectLine : Line);\n      this._hasEffet = hasEffect;\n      this._isPolyline = isPolyline;\n      this._isLargeDraw = isLargeDraw;\n    }\n    this.group.add(lineDraw.group);\n    return lineDraw;\n  };\n  LinesView.prototype._showEffect = function (seriesModel) {\n    return !!seriesModel.get(['effect', 'show']);\n  };\n  LinesView.prototype._clearLayer = function (api) {\n    // Not use motion when dragging or zooming\n    var zr = api.getZr();\n    var isSvg = zr.painter.getType() === 'svg';\n    if (!isSvg && this._lastZlevel != null) {\n      zr.painter.getLayer(this._lastZlevel).clear(true);\n    }\n  };\n  LinesView.prototype.remove = function (ecModel, api) {\n    this._lineDraw && this._lineDraw.remove();\n    this._lineDraw = null;\n    // Clear motion when lineDraw is removed\n    this._clearLayer(api);\n  };\n  LinesView.prototype.dispose = function (ecModel, api) {\n    this.remove(ecModel, api);\n  };\n  LinesView.type = 'lines';\n  return LinesView;\n}(ChartView);\nexport default LinesView;", "map": {"version": 3, "names": ["__extends", "LineDraw", "EffectLine", "Line", "Polyline", "EffectPolyline", "LargeLineDraw", "linesLayout", "createClipPath", "ChartView", "LinesView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "seriesModel", "ecModel", "api", "data", "getData", "lineDraw", "_updateLineDraw", "zlevel", "get", "trailLength", "zr", "getZr", "isSvg", "painter", "getType", "<PERSON><PERSON><PERSON><PERSON>", "clear", "_lastZ<PERSON><PERSON>", "config<PERSON><PERSON>er", "motionBlur", "_showEffect", "lastFrameAlpha", "Math", "max", "min", "process", "env", "NODE_ENV", "console", "warn", "updateData", "clipPath", "coordinateSystem", "group", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "removeClip<PERSON>ath", "_finished", "incrementalPrepareRender", "incrementalPrepareUpdate", "_clearLayer", "incrementalRender", "taskParams", "_lineDraw", "incrementalUpdate", "end", "count", "eachRendered", "cb", "updateTransform", "pipelineContext", "large", "progressiveRender", "update", "res", "reset", "progress", "start", "updateLayout", "hasEffect", "isPolyline", "isLargeDraw", "_hasEffet", "_isPolyline", "_isLargeDraw", "remove", "add", "dispose"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/chart/lines/LinesView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport LineDraw from '../helper/LineDraw.js';\nimport EffectLine from '../helper/EffectLine.js';\nimport Line from '../helper/Line.js';\nimport Polyline from '../helper/Polyline.js';\nimport EffectPolyline from '../helper/EffectPolyline.js';\nimport LargeLineDraw from '../helper/LargeLineDraw.js';\nimport linesLayout from './linesLayout.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport ChartView from '../../view/Chart.js';\nvar LinesView = /** @class */function (_super) {\n  __extends(LinesView, _super);\n  function LinesView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LinesView.type;\n    return _this;\n  }\n  LinesView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var lineDraw = this._updateLineDraw(data, seriesModel);\n    var zlevel = seriesModel.get('zlevel');\n    var trailLength = seriesModel.get(['effect', 'trailLength']);\n    var zr = api.getZr();\n    // Avoid the drag cause ghost shadow\n    // FIXME Better way ?\n    // SVG doesn't support\n    var isSvg = zr.painter.getType() === 'svg';\n    if (!isSvg) {\n      zr.painter.getLayer(zlevel).clear(true);\n    }\n    // Config layer with motion blur\n    if (this._lastZlevel != null && !isSvg) {\n      zr.configLayer(this._lastZlevel, {\n        motionBlur: false\n      });\n    }\n    if (this._showEffect(seriesModel) && trailLength > 0) {\n      if (!isSvg) {\n        zr.configLayer(zlevel, {\n          motionBlur: true,\n          lastFrameAlpha: Math.max(Math.min(trailLength / 10 + 0.9, 1), 0)\n        });\n      } else if (process.env.NODE_ENV !== 'production') {\n        console.warn('SVG render mode doesn\\'t support lines with trail effect');\n      }\n    }\n    lineDraw.updateData(data);\n    var clipPath = seriesModel.get('clip', true) && createClipPath(seriesModel.coordinateSystem, false, seriesModel);\n    if (clipPath) {\n      this.group.setClipPath(clipPath);\n    } else {\n      this.group.removeClipPath();\n    }\n    this._lastZlevel = zlevel;\n    this._finished = true;\n  };\n  LinesView.prototype.incrementalPrepareRender = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var lineDraw = this._updateLineDraw(data, seriesModel);\n    lineDraw.incrementalPrepareUpdate(data);\n    this._clearLayer(api);\n    this._finished = false;\n  };\n  LinesView.prototype.incrementalRender = function (taskParams, seriesModel, ecModel) {\n    this._lineDraw.incrementalUpdate(taskParams, seriesModel.getData());\n    this._finished = taskParams.end === seriesModel.getData().count();\n  };\n  LinesView.prototype.eachRendered = function (cb) {\n    this._lineDraw && this._lineDraw.eachRendered(cb);\n  };\n  LinesView.prototype.updateTransform = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var pipelineContext = seriesModel.pipelineContext;\n    if (!this._finished || pipelineContext.large || pipelineContext.progressiveRender) {\n      // TODO Don't have to do update in large mode. Only do it when there are millions of data.\n      return {\n        update: true\n      };\n    } else {\n      // TODO Use same logic with ScatterView.\n      // Manually update layout\n      var res = linesLayout.reset(seriesModel, ecModel, api);\n      if (res.progress) {\n        res.progress({\n          start: 0,\n          end: data.count(),\n          count: data.count()\n        }, data);\n      }\n      // Not in large mode\n      this._lineDraw.updateLayout();\n      this._clearLayer(api);\n    }\n  };\n  LinesView.prototype._updateLineDraw = function (data, seriesModel) {\n    var lineDraw = this._lineDraw;\n    var hasEffect = this._showEffect(seriesModel);\n    var isPolyline = !!seriesModel.get('polyline');\n    var pipelineContext = seriesModel.pipelineContext;\n    var isLargeDraw = pipelineContext.large;\n    if (process.env.NODE_ENV !== 'production') {\n      if (hasEffect && isLargeDraw) {\n        console.warn('Large lines not support effect');\n      }\n    }\n    if (!lineDraw || hasEffect !== this._hasEffet || isPolyline !== this._isPolyline || isLargeDraw !== this._isLargeDraw) {\n      if (lineDraw) {\n        lineDraw.remove();\n      }\n      lineDraw = this._lineDraw = isLargeDraw ? new LargeLineDraw() : new LineDraw(isPolyline ? hasEffect ? EffectPolyline : Polyline : hasEffect ? EffectLine : Line);\n      this._hasEffet = hasEffect;\n      this._isPolyline = isPolyline;\n      this._isLargeDraw = isLargeDraw;\n    }\n    this.group.add(lineDraw.group);\n    return lineDraw;\n  };\n  LinesView.prototype._showEffect = function (seriesModel) {\n    return !!seriesModel.get(['effect', 'show']);\n  };\n  LinesView.prototype._clearLayer = function (api) {\n    // Not use motion when dragging or zooming\n    var zr = api.getZr();\n    var isSvg = zr.painter.getType() === 'svg';\n    if (!isSvg && this._lastZlevel != null) {\n      zr.painter.getLayer(this._lastZlevel).clear(true);\n    }\n  };\n  LinesView.prototype.remove = function (ecModel, api) {\n    this._lineDraw && this._lineDraw.remove();\n    this._lineDraw = null;\n    // Clear motion when lineDraw is removed\n    this._clearLayer(api);\n  };\n  LinesView.prototype.dispose = function (ecModel, api) {\n    this.remove(ecModel, api);\n  };\n  LinesView.type = 'lines';\n  return LinesView;\n}(ChartView);\nexport default LinesView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,cAAc,QAAQ,yCAAyC;AACxE,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,IAAIC,SAAS,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC7CX,SAAS,CAACU,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAAA,EAAG;IACnB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,SAAS,CAACK,IAAI;IAC3B,OAAOH,KAAK;EACd;EACAF,SAAS,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAChE,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;IAChC,IAAIC,QAAQ,GAAG,IAAI,CAACC,eAAe,CAACH,IAAI,EAAEH,WAAW,CAAC;IACtD,IAAIO,MAAM,GAAGP,WAAW,CAACQ,GAAG,CAAC,QAAQ,CAAC;IACtC,IAAIC,WAAW,GAAGT,WAAW,CAACQ,GAAG,CAAC,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IAC5D,IAAIE,EAAE,GAAGR,GAAG,CAACS,KAAK,CAAC,CAAC;IACpB;IACA;IACA;IACA,IAAIC,KAAK,GAAGF,EAAE,CAACG,OAAO,CAACC,OAAO,CAAC,CAAC,KAAK,KAAK;IAC1C,IAAI,CAACF,KAAK,EAAE;MACVF,EAAE,CAACG,OAAO,CAACE,QAAQ,CAACR,MAAM,CAAC,CAACS,KAAK,CAAC,IAAI,CAAC;IACzC;IACA;IACA,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,IAAI,CAACL,KAAK,EAAE;MACtCF,EAAE,CAACQ,WAAW,CAAC,IAAI,CAACD,WAAW,EAAE;QAC/BE,UAAU,EAAE;MACd,CAAC,CAAC;IACJ;IACA,IAAI,IAAI,CAACC,WAAW,CAACpB,WAAW,CAAC,IAAIS,WAAW,GAAG,CAAC,EAAE;MACpD,IAAI,CAACG,KAAK,EAAE;QACVF,EAAE,CAACQ,WAAW,CAACX,MAAM,EAAE;UACrBY,UAAU,EAAE,IAAI;UAChBE,cAAc,EAAEC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACf,WAAW,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;QACjE,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QAChDC,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;MAC1E;IACF;IACAxB,QAAQ,CAACyB,UAAU,CAAC3B,IAAI,CAAC;IACzB,IAAI4B,QAAQ,GAAG/B,WAAW,CAACQ,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,IAAIlB,cAAc,CAACU,WAAW,CAACgC,gBAAgB,EAAE,KAAK,EAAEhC,WAAW,CAAC;IAChH,IAAI+B,QAAQ,EAAE;MACZ,IAAI,CAACE,KAAK,CAACC,WAAW,CAACH,QAAQ,CAAC;IAClC,CAAC,MAAM;MACL,IAAI,CAACE,KAAK,CAACE,cAAc,CAAC,CAAC;IAC7B;IACA,IAAI,CAAClB,WAAW,GAAGV,MAAM;IACzB,IAAI,CAAC6B,SAAS,GAAG,IAAI;EACvB,CAAC;EACD5C,SAAS,CAACM,SAAS,CAACuC,wBAAwB,GAAG,UAAUrC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAClF,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;IAChC,IAAIC,QAAQ,GAAG,IAAI,CAACC,eAAe,CAACH,IAAI,EAAEH,WAAW,CAAC;IACtDK,QAAQ,CAACiC,wBAAwB,CAACnC,IAAI,CAAC;IACvC,IAAI,CAACoC,WAAW,CAACrC,GAAG,CAAC;IACrB,IAAI,CAACkC,SAAS,GAAG,KAAK;EACxB,CAAC;EACD5C,SAAS,CAACM,SAAS,CAAC0C,iBAAiB,GAAG,UAAUC,UAAU,EAAEzC,WAAW,EAAEC,OAAO,EAAE;IAClF,IAAI,CAACyC,SAAS,CAACC,iBAAiB,CAACF,UAAU,EAAEzC,WAAW,CAACI,OAAO,CAAC,CAAC,CAAC;IACnE,IAAI,CAACgC,SAAS,GAAGK,UAAU,CAACG,GAAG,KAAK5C,WAAW,CAACI,OAAO,CAAC,CAAC,CAACyC,KAAK,CAAC,CAAC;EACnE,CAAC;EACDrD,SAAS,CAACM,SAAS,CAACgD,YAAY,GAAG,UAAUC,EAAE,EAAE;IAC/C,IAAI,CAACL,SAAS,IAAI,IAAI,CAACA,SAAS,CAACI,YAAY,CAACC,EAAE,CAAC;EACnD,CAAC;EACDvD,SAAS,CAACM,SAAS,CAACkD,eAAe,GAAG,UAAUhD,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACzE,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;IAChC,IAAI6C,eAAe,GAAGjD,WAAW,CAACiD,eAAe;IACjD,IAAI,CAAC,IAAI,CAACb,SAAS,IAAIa,eAAe,CAACC,KAAK,IAAID,eAAe,CAACE,iBAAiB,EAAE;MACjF;MACA,OAAO;QACLC,MAAM,EAAE;MACV,CAAC;IACH,CAAC,MAAM;MACL;MACA;MACA,IAAIC,GAAG,GAAGhE,WAAW,CAACiE,KAAK,CAACtD,WAAW,EAAEC,OAAO,EAAEC,GAAG,CAAC;MACtD,IAAImD,GAAG,CAACE,QAAQ,EAAE;QAChBF,GAAG,CAACE,QAAQ,CAAC;UACXC,KAAK,EAAE,CAAC;UACRZ,GAAG,EAAEzC,IAAI,CAAC0C,KAAK,CAAC,CAAC;UACjBA,KAAK,EAAE1C,IAAI,CAAC0C,KAAK,CAAC;QACpB,CAAC,EAAE1C,IAAI,CAAC;MACV;MACA;MACA,IAAI,CAACuC,SAAS,CAACe,YAAY,CAAC,CAAC;MAC7B,IAAI,CAAClB,WAAW,CAACrC,GAAG,CAAC;IACvB;EACF,CAAC;EACDV,SAAS,CAACM,SAAS,CAACQ,eAAe,GAAG,UAAUH,IAAI,EAAEH,WAAW,EAAE;IACjE,IAAIK,QAAQ,GAAG,IAAI,CAACqC,SAAS;IAC7B,IAAIgB,SAAS,GAAG,IAAI,CAACtC,WAAW,CAACpB,WAAW,CAAC;IAC7C,IAAI2D,UAAU,GAAG,CAAC,CAAC3D,WAAW,CAACQ,GAAG,CAAC,UAAU,CAAC;IAC9C,IAAIyC,eAAe,GAAGjD,WAAW,CAACiD,eAAe;IACjD,IAAIW,WAAW,GAAGX,eAAe,CAACC,KAAK;IACvC,IAAIzB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI+B,SAAS,IAAIE,WAAW,EAAE;QAC5BhC,OAAO,CAACC,IAAI,CAAC,gCAAgC,CAAC;MAChD;IACF;IACA,IAAI,CAACxB,QAAQ,IAAIqD,SAAS,KAAK,IAAI,CAACG,SAAS,IAAIF,UAAU,KAAK,IAAI,CAACG,WAAW,IAAIF,WAAW,KAAK,IAAI,CAACG,YAAY,EAAE;MACrH,IAAI1D,QAAQ,EAAE;QACZA,QAAQ,CAAC2D,MAAM,CAAC,CAAC;MACnB;MACA3D,QAAQ,GAAG,IAAI,CAACqC,SAAS,GAAGkB,WAAW,GAAG,IAAIxE,aAAa,CAAC,CAAC,GAAG,IAAIL,QAAQ,CAAC4E,UAAU,GAAGD,SAAS,GAAGvE,cAAc,GAAGD,QAAQ,GAAGwE,SAAS,GAAG1E,UAAU,GAAGC,IAAI,CAAC;MAChK,IAAI,CAAC4E,SAAS,GAAGH,SAAS;MAC1B,IAAI,CAACI,WAAW,GAAGH,UAAU;MAC7B,IAAI,CAACI,YAAY,GAAGH,WAAW;IACjC;IACA,IAAI,CAAC3B,KAAK,CAACgC,GAAG,CAAC5D,QAAQ,CAAC4B,KAAK,CAAC;IAC9B,OAAO5B,QAAQ;EACjB,CAAC;EACDb,SAAS,CAACM,SAAS,CAACsB,WAAW,GAAG,UAAUpB,WAAW,EAAE;IACvD,OAAO,CAAC,CAACA,WAAW,CAACQ,GAAG,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;EAC9C,CAAC;EACDhB,SAAS,CAACM,SAAS,CAACyC,WAAW,GAAG,UAAUrC,GAAG,EAAE;IAC/C;IACA,IAAIQ,EAAE,GAAGR,GAAG,CAACS,KAAK,CAAC,CAAC;IACpB,IAAIC,KAAK,GAAGF,EAAE,CAACG,OAAO,CAACC,OAAO,CAAC,CAAC,KAAK,KAAK;IAC1C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACK,WAAW,IAAI,IAAI,EAAE;MACtCP,EAAE,CAACG,OAAO,CAACE,QAAQ,CAAC,IAAI,CAACE,WAAW,CAAC,CAACD,KAAK,CAAC,IAAI,CAAC;IACnD;EACF,CAAC;EACDxB,SAAS,CAACM,SAAS,CAACkE,MAAM,GAAG,UAAU/D,OAAO,EAAEC,GAAG,EAAE;IACnD,IAAI,CAACwC,SAAS,IAAI,IAAI,CAACA,SAAS,CAACsB,MAAM,CAAC,CAAC;IACzC,IAAI,CAACtB,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACH,WAAW,CAACrC,GAAG,CAAC;EACvB,CAAC;EACDV,SAAS,CAACM,SAAS,CAACoE,OAAO,GAAG,UAAUjE,OAAO,EAAEC,GAAG,EAAE;IACpD,IAAI,CAAC8D,MAAM,CAAC/D,OAAO,EAAEC,GAAG,CAAC;EAC3B,CAAC;EACDV,SAAS,CAACK,IAAI,GAAG,OAAO;EACxB,OAAOL,SAAS;AAClB,CAAC,CAACD,SAAS,CAAC;AACZ,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}