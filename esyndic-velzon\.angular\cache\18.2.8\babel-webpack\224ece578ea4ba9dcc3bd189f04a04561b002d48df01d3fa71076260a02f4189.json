{"ast": null, "code": "/**\n * A specialized version of `_.forEachRight` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns `array`.\n */\nfunction arrayEachRight(array, iteratee) {\n  var length = array == null ? 0 : array.length;\n  while (length--) {\n    if (iteratee(array[length], length, array) === false) {\n      break;\n    }\n  }\n  return array;\n}\nexport default arrayEachRight;", "map": {"version": 3, "names": ["arrayEachRight", "array", "iteratee", "length"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_arrayEachRight.js"], "sourcesContent": ["/**\n * A specialized version of `_.forEachRight` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns `array`.\n */\nfunction arrayEachRight(array, iteratee) {\n  var length = array == null ? 0 : array.length;\n\n  while (length--) {\n    if (iteratee(array[length], length, array) === false) {\n      break;\n    }\n  }\n  return array;\n}\n\nexport default arrayEachRight;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAcA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACvC,IAAIC,MAAM,GAAGF,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACE,MAAM;EAE7C,OAAOA,MAAM,EAAE,EAAE;IACf,IAAID,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,EAAEA,MAAM,EAAEF,KAAK,CAAC,KAAK,KAAK,EAAE;MACpD;IACF;EACF;EACA,OAAOA,KAAK;AACd;AAEA,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}