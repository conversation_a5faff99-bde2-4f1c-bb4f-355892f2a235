{"ast": null, "code": "/*!\n * Chart.js v4.4.3\n * https://www.chartjs.org\n * (c) 2024 Chart.js Contributors\n * Released under the MIT License\n */\nimport { Color } from '@kurkle/color';\n\n/**\n * @namespace Chart.helpers\n */ /**\n    * An empty function that can be used, for example, for optional callback.\n    */\nfunction noop() {\n  /* noop */}\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */\nconst uid = (() => {\n  let id = 0;\n  return () => id++;\n})();\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nfunction isNullOrUndef(value) {\n  return value === null || typeof value === 'undefined';\n}\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */\nfunction isArray(value) {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nfunction isObject(value) {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */\nfunction isNumberFinite(value) {\n  return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */\nfunction finiteOrDefault(value, defaultValue) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */\nfunction valueOrDefault(value, defaultValue) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 : +value / dimension;\nconst toDimension = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 * dimension : +value;\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */\nfunction callback(fn, args, thisArg) {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n  let i, len, keys;\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */\nfunction _elementsEqual(a0, a1) {\n  let i, ilen, v0, v1;\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */\nfunction clone(source) {\n  if (isArray(source)) {\n    return source.map(clone);\n  }\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone(source[keys[k]]);\n    }\n    return target;\n  }\n  return source;\n}\nfunction isValidKey(key) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */\nfunction _merger(key, target, source, options) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone(sval);\n  }\n}\nfunction merge(target, source, options) {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n  if (!isObject(target)) {\n    return target;\n  }\n  options = options || {};\n  const merger = options.merger || _merger;\n  let current;\n  for (let i = 0; i < ilen; ++i) {\n    current = sources[i];\n    if (!isObject(current)) {\n      continue;\n    }\n    const keys = Object.keys(current);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, current, options);\n    }\n  }\n  return target;\n}\nfunction mergeIf(target, source) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return merge(target, source, {\n    merger: _mergerIf\n  });\n}\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */\nfunction _mergerIf(key, target, source) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone(sval);\n  }\n}\n/**\n * @private\n */\nfunction _deprecated(scope, value, previous, current) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous + '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n  // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n  '': v => v,\n  // default resolvers\n  x: o => o.x,\n  y: o => o.y\n};\n/**\n * @private\n */\nfunction _splitKey(key) {\n  const parts = key.split('.');\n  const keys = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\nfunction _getKeyResolver(key) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\nfunction resolveObjectKey(obj, key) {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\n/**\n * @private\n */\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = value => typeof value !== 'undefined';\nconst isFunction = value => typeof value === 'function';\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nconst setsEqual = (a, b) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n  return true;\n};\n/**\n * @param e - The event\n * @private\n */\nfunction _isClickEvent(e) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */\nconst PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction almostEquals(x, y, epsilon) {\n  return Math.abs(x - y) < epsilon;\n}\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */\nfunction niceNum(range) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */\nfunction _factorize(value) {\n  const result = [];\n  const sqrt = Math.sqrt(value);\n  let i;\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) {\n    result.push(sqrt);\n  }\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\nfunction isNumber(n) {\n  return !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostWhole(x, epsilon) {\n  const rounded = Math.round(x);\n  return rounded - epsilon <= x && rounded + epsilon >= x;\n}\n/**\n * @private\n */\nfunction _setMinAndMaxByKey(array, target, property) {\n  let i, ilen, value;\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\nfunction toRadians(degrees) {\n  return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n  return radians * (180 / PI);\n}\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */\nfunction _decimalPlaces(x) {\n  if (!isNumberFinite(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\n// Gets the angle from vertical upright to the point about a centre.\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n  if (angle < -0.5 * PI) {\n    angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n  }\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */\nfunction _angleDiff(a, b) {\n  return (a - b + PITAU) % TAU - PI;\n}\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */\nfunction _normalizeAngle(a) {\n  return (a % TAU + TAU) % TAU;\n}\n/**\n * @private\n */\nfunction _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || sameAngleIsFullCircle && s === e || angleToStart > angleToEnd && startToAngle < endToAngle;\n}\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */\nfunction _limitValue(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/**\n * @param {number} value\n * @private\n */\nfunction _int16Range(value) {\n  return _limitValue(value, -32768, 32767);\n}\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */\nfunction _isBetween(value, start, end, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\nfunction _lookup(table, value, cmp) {\n  cmp = cmp || (index => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid;\n  while (hi - lo > 1) {\n    mid = lo + hi >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n  return {\n    lo,\n    hi\n  };\n}\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */\nconst _lookupByKey = (table, key, value, last) => _lookup(table, value, last ? index => {\n  const ti = table[index][key];\n  return ti < value || ti === value && table[index + 1][key] === value;\n} : index => table[index][key] < value);\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */\nconst _rlookupByKey = (table, key, value) => _lookup(table, value, index => table[index][key] >= value);\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */\nfunction _filterBetween(values, min, max) {\n  let start = 0;\n  let end = values.length;\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n  return start > 0 || end < values.length ? values.slice(start, end) : values;\n}\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\nfunction listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n  arrayEvents.forEach(key => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n        array._chartjs.listeners.forEach(object => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n        return res;\n      }\n    });\n  });\n}\nfunction unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n  if (listeners.length > 0) {\n    return;\n  }\n  arrayEvents.forEach(key => {\n    delete array[key];\n  });\n  delete array._chartjs;\n}\n/**\n * @param items\n */\nfunction _arrayUnique(items) {\n  const set = new Set(items);\n  if (set.size === items.length) {\n    return items;\n  }\n  return Array.from(set);\n}\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n/**\n* Request animation polyfill\n*/\nconst requestAnimFrame = function () {\n  if (typeof window === 'undefined') {\n    return function (callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}();\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */\nfunction throttled(fn, thisArg) {\n  let argsToUse = [];\n  let ticking = false;\n  return function (...args) {\n    // Save the args for use later\n    argsToUse = args;\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, argsToUse);\n      });\n    }\n  };\n}\n/**\n * Debounces calling `fn` for `delay` ms\n */\nfunction debounce(fn, delay) {\n  let timeout;\n  return function (...args) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */\nconst _toLeftRightCenter = align => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */\nconst _alignStartEnd = (align, start, end) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */\nconst _textX = (align, left, right, rtl) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n/**\n * Return start and count of visible points.\n * @private\n */\nfunction _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled) {\n  const pointCount = points.length;\n  let start = 0;\n  let count = pointCount;\n  if (meta._sorted) {\n    const {\n      iScale,\n      _parsed\n    } = meta;\n    const axis = iScale.axis;\n    const {\n      min,\n      max,\n      minDefined,\n      maxDefined\n    } = iScale.getUserBounds();\n    if (minDefined) {\n      start = _limitValue(Math.min(\n      // @ts-expect-error Need to type _parsed\n      _lookupByKey(_parsed, axis, min).lo,\n      // @ts-expect-error Need to fix types on _lookupByKey\n      animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo), 0, pointCount - 1);\n    }\n    if (maxDefined) {\n      count = _limitValue(Math.max(\n      // @ts-expect-error Need to type _parsed\n      _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n      // @ts-expect-error Need to fix types on _lookupByKey\n      animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1), start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n  return {\n    start,\n    count\n  };\n}\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */\nfunction _scaleRangesChanged(meta) {\n  const {\n    xScale,\n    yScale,\n    _scaleRanges\n  } = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min || _scaleRanges.xmax !== xScale.max || _scaleRanges.ymin !== yScale.min || _scaleRanges.ymax !== yScale.max;\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\nconst atEdge = t => t === 0 || t === 1;\nconst elasticIn = (t, s, p) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n/**\n * Easing functions adapted from Robert Penner's easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */\nconst effects = {\n  linear: t => t,\n  easeInQuad: t => t * t,\n  easeOutQuad: t => -t * (t - 2),\n  easeInOutQuad: t => (t /= 0.5) < 1 ? 0.5 * t * t : -0.5 * (--t * (t - 2) - 1),\n  easeInCubic: t => t * t * t,\n  easeOutCubic: t => (t -= 1) * t * t + 1,\n  easeInOutCubic: t => (t /= 0.5) < 1 ? 0.5 * t * t * t : 0.5 * ((t -= 2) * t * t + 2),\n  easeInQuart: t => t * t * t * t,\n  easeOutQuart: t => -((t -= 1) * t * t * t - 1),\n  easeInOutQuart: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t : -0.5 * ((t -= 2) * t * t * t - 2),\n  easeInQuint: t => t * t * t * t * t,\n  easeOutQuint: t => (t -= 1) * t * t * t * t + 1,\n  easeInOutQuint: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t * t : 0.5 * ((t -= 2) * t * t * t * t + 2),\n  easeInSine: t => -Math.cos(t * HALF_PI) + 1,\n  easeOutSine: t => Math.sin(t * HALF_PI),\n  easeInOutSine: t => -0.5 * (Math.cos(PI * t) - 1),\n  easeInExpo: t => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),\n  easeOutExpo: t => t === 1 ? 1 : -Math.pow(2, -10 * t) + 1,\n  easeInOutExpo: t => atEdge(t) ? t : t < 0.5 ? 0.5 * Math.pow(2, 10 * (t * 2 - 1)) : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n  easeInCirc: t => t >= 1 ? t : -(Math.sqrt(1 - t * t) - 1),\n  easeOutCirc: t => Math.sqrt(1 - (t -= 1) * t),\n  easeInOutCirc: t => (t /= 0.5) < 1 ? -0.5 * (Math.sqrt(1 - t * t) - 1) : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n  easeInElastic: t => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n  easeOutElastic: t => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n  easeInOutElastic(t) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t : t < 0.5 ? 0.5 * elasticIn(t * 2, s, p) : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n  easeInBack(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n  easeOutBack(t) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n  easeInOutBack(t) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= 1.525) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2);\n  },\n  easeInBounce: t => 1 - effects.easeOutBounce(1 - t),\n  easeOutBounce(t) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < 1 / d) {\n      return m * t * t;\n    }\n    if (t < 2 / d) {\n      return m * (t -= 1.5 / d) * t + 0.75;\n    }\n    if (t < 2.5 / d) {\n      return m * (t -= 2.25 / d) * t + 0.9375;\n    }\n    return m * (t -= 2.625 / d) * t + 0.984375;\n  },\n  easeInOutBounce: t => t < 0.5 ? effects.easeInBounce(t * 2) * 0.5 : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5\n};\nfunction isPatternOrGradient(value) {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n  return false;\n}\nfunction color(value) {\n  return isPatternOrGradient(value) ? value : new Color(value);\n}\nfunction getHoverColor(value) {\n  return isPatternOrGradient(value) ? value : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\nconst numbers = ['x', 'y', 'borderWidth', 'radius', 'tension'];\nconst colors = ['color', 'borderColor', 'backgroundColor'];\nfunction applyAnimationsDefaults(defaults) {\n  defaults.set('animation', {\n    delay: undefined,\n    duration: 1000,\n    easing: 'easeOutQuart',\n    fn: undefined,\n    from: undefined,\n    loop: undefined,\n    to: undefined,\n    type: undefined\n  });\n  defaults.describe('animation', {\n    _fallback: false,\n    _indexable: false,\n    _scriptable: name => name !== 'onProgress' && name !== 'onComplete' && name !== 'fn'\n  });\n  defaults.set('animations', {\n    colors: {\n      type: 'color',\n      properties: colors\n    },\n    numbers: {\n      type: 'number',\n      properties: numbers\n    }\n  });\n  defaults.describe('animations', {\n    _fallback: 'animation'\n  });\n  defaults.set('transitions', {\n    active: {\n      animation: {\n        duration: 400\n      }\n    },\n    resize: {\n      animation: {\n        duration: 0\n      }\n    },\n    show: {\n      animations: {\n        colors: {\n          from: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          duration: 0\n        }\n      }\n    },\n    hide: {\n      animations: {\n        colors: {\n          to: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          easing: 'linear',\n          fn: v => v | 0\n        }\n      }\n    }\n  });\n}\nfunction applyLayoutsDefaults(defaults) {\n  defaults.set('layout', {\n    autoPadding: true,\n    padding: {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    }\n  });\n}\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\nfunction formatNumber(num, locale, options) {\n  return getNumberFormat(locale, options).format(num);\n}\nconst formatters = {\n  values(value) {\n    return isArray(value) ? value : '' + value;\n  },\n  numeric(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const locale = this.chart.options.locale;\n    let notation;\n    let delta = tickValue;\n    if (ticks.length > 1) {\n      const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n      if (maxTick < 1e-4 || maxTick > 1e+15) {\n        notation = 'scientific';\n      }\n      delta = calculateDelta(tickValue, ticks);\n    }\n    const logDelta = log10(Math.abs(delta));\n    const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n    const options = {\n      notation,\n      minimumFractionDigits: numDecimal,\n      maximumFractionDigits: numDecimal\n    };\n    Object.assign(options, this.options.ticks.format);\n    return formatNumber(tickValue, locale, options);\n  },\n  logarithmic(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const remain = ticks[index].significand || tickValue / Math.pow(10, Math.floor(log10(tickValue)));\n    if ([1, 2, 3, 5, 10, 15].includes(remain) || index > 0.8 * ticks.length) {\n      return formatters.numeric.call(this, tickValue, index, ticks);\n    }\n    return '';\n  }\n};\nfunction calculateDelta(tickValue, ticks) {\n  let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n  if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n    delta = tickValue - Math.floor(tickValue);\n  }\n  return delta;\n}\nvar Ticks = {\n  formatters\n};\nfunction applyScaleDefaults(defaults) {\n  defaults.set('scale', {\n    display: true,\n    offset: false,\n    reverse: false,\n    beginAtZero: false,\n    bounds: 'ticks',\n    clip: true,\n    grace: 0,\n    grid: {\n      display: true,\n      lineWidth: 1,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickLength: 8,\n      tickWidth: (_ctx, options) => options.lineWidth,\n      tickColor: (_ctx, options) => options.color,\n      offset: false\n    },\n    border: {\n      display: true,\n      dash: [],\n      dashOffset: 0.0,\n      width: 1\n    },\n    title: {\n      display: false,\n      text: '',\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n    ticks: {\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      textStrokeWidth: 0,\n      textStrokeColor: '',\n      padding: 3,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 3,\n      labelOffset: 0,\n      callback: Ticks.formatters.values,\n      minor: {},\n      major: {},\n      align: 'center',\n      crossAlign: 'near',\n      showLabelBackdrop: false,\n      backdropColor: 'rgba(255, 255, 255, 0.75)',\n      backdropPadding: 2\n    }\n  });\n  defaults.route('scale.ticks', 'color', '', 'color');\n  defaults.route('scale.grid', 'color', '', 'borderColor');\n  defaults.route('scale.border', 'color', '', 'borderColor');\n  defaults.route('scale.title', 'color', '', 'color');\n  defaults.describe('scale', {\n    _fallback: false,\n    _scriptable: name => !name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n    _indexable: name => name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash'\n  });\n  defaults.describe('scales', {\n    _fallback: 'scale'\n  });\n  defaults.describe('scale.ticks', {\n    _scriptable: name => name !== 'backdropPadding' && name !== 'callback',\n    _indexable: name => name !== 'backdropPadding'\n  });\n}\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\nfunction getScope$1(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope$1(root, scope), values);\n  }\n  return merge(getScope$1(root, ''), scope);\n}\nclass Defaults {\n  constructor(_descriptors, _appliers) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = context => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove'];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n    this.describe(_descriptors);\n    this.apply(_appliers);\n  }\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n  get(scope) {\n    return getScope$1(this, scope);\n  }\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope$1(this, scope);\n    const targetScopeObject = getScope$1(this, targetScope);\n    const privateName = '_' + name;\n    Object.defineProperties(scopeObject, {\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n  apply(appliers) {\n    appliers.forEach(apply => apply(this));\n  }\n}\nvar defaults = /* #__PURE__ */new Defaults({\n  _scriptable: name => !name.startsWith('on'),\n  _indexable: name => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false\n  }\n}, [applyAnimationsDefaults, applyLayoutsDefaults, applyScaleDefaults]);\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */\nfunction toFontString(font) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n  return (font.style ? font.style + ' ' : '') + (font.weight ? font.weight + ' ' : '') + font.size + 'px ' + font.family;\n}\n/**\n * @private\n */\nfunction _measureText(ctx, data, gc, longest, string) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\n/**\n * @private\n */ // eslint-disable-next-line complexity\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n  ctx.save();\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i, j, jlen, thing, nestedThing;\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n    // Undefined strings and arrays should not be measured\n    if (thing !== undefined && thing !== null && !isArray(thing)) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      // if it is an array lets measure each element\n      // to do maybe simplify this function a bit so we can do this more recursively?\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        // Undefined strings and arrays should not be measured\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n  ctx.restore();\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */\nfunction _alignPixel(chart, pixel, width) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n/**\n * Clears the entire canvas.\n */\nfunction clearCanvas(canvas, ctx) {\n  if (!ctx && !canvas) {\n    return;\n  }\n  ctx = ctx || canvas.getContext('2d');\n  ctx.save();\n  // canvas.width and canvas.height do not consider the canvas transform,\n  // while clearRect does\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  drawPointLegend(ctx, options, x, y, null);\n}\n// eslint-disable-next-line complexity\nfunction drawPointLegend(ctx, options, x, y, w) {\n  let type, xOffset, yOffset, size, cornerRadius, width, xOffsetW, yOffsetW;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n  ctx.beginPath();\n  switch (style) {\n    // Default includes circle\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      width = w ? w / 2 : radius;\n      ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n      // NOTE: the rounded rect implementation changed to use `arc` instead of\n      // `quadraticCurveTo` since it generates better results when rect is\n      // almost a circle. 0.516 (instead of 0.5) produces results with visually\n      // closer proportion to the previous impl and it is inscribed in the\n      // circle with `radius`. For more details, see the following PRs:\n      // https://github.com/chartjs/Chart.js/issues/5597\n      // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'star':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      rad += QUARTER_PI;\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n      break;\n    case false:\n      ctx.closePath();\n      break;\n  }\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */\nfunction _isPointInArea(point, area, margin) {\n  margin = margin || 0.5; // margin - default is to match rounded decimals\n  return !area || point && point.x > area.left - margin && point.x < area.right + margin && point.y > area.top - margin && point.y < area.bottom + margin;\n}\nfunction clipArea(ctx, area) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\nfunction unclipArea(ctx) {\n  ctx.restore();\n}\n/**\n * @private\n */\nfunction _steppedLineTo(ctx, previous, target, flip, mode) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\n/**\n * @private\n */\nfunction _bezierCurveTo(ctx, previous, target, flip) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(flip ? previous.cp1x : previous.cp2x, flip ? previous.cp1y : previous.cp2y, flip ? target.cp2x : target.cp1x, flip ? target.cp2y : target.cp1y, target.x, target.y);\n}\nfunction setRenderOpts(ctx, opts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n  if (opts.strikethrough || opts.underline) {\n    /**\n    * Now that IE11 support has been dropped, we can use more\n    * of the TextMetrics object. The actual bounding boxes\n    * are unflagged in Chrome, Firefox, Edge, and Safari so they\n    * can be safely used.\n    * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n    */\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\nfunction drawBackdrop(ctx, opts) {\n  const oldColor = ctx.fillStyle;\n  ctx.fillStyle = opts.color;\n  ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n  ctx.fillStyle = oldColor;\n}\n/**\n * Render text onto the canvas\n */\nfunction renderText(ctx, text, x, y, font, opts = {}) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i, line;\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n    if (opts.backdrop) {\n      drawBackdrop(ctx, opts.backdrop);\n    }\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n    y += Number(font.lineHeight);\n  }\n  ctx.restore();\n}\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */\nfunction addRoundedRectPath(ctx, rect) {\n  const {\n    x,\n    y,\n    w,\n    h,\n    radius\n  } = rect;\n  // top left arc\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, 1.5 * PI, PI, true);\n  // line from top left to bottom left\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n  // bottom left arc\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n  // line from bottom left to bottom right\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n  // bottom right arc\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n  // line from bottom right to top right\n  ctx.lineTo(x + w, y + radius.topRight);\n  // top right arc\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n  // line from top right to top left\n  ctx.lineTo(x + radius.topLeft, y);\n}\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n/**\n * @alias Chart.helpers.options\n * @namespace\n */ /**\n    * Converts the given line height `value` in pixels for a specific font `size`.\n    * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n    * @param size - The font size (in pixels) used to resolve relative `value`.\n    * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n    * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n    * @since 2.7.0\n    */\nfunction toLineHeight(value, size) {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n  value = +matches[2];\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n  }\n  return size * value;\n}\nconst numberOrZero = v => +v || 0;\nfunction _readValueToProps(value, props) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value) ? objProps ? prop => valueOrDefault(value[prop], value[props[prop]]) : prop => value[prop] : () => value;\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */\nfunction toTRBL(value) {\n  return _readValueToProps(value, {\n    top: 'y',\n    right: 'x',\n    bottom: 'y',\n    left: 'x'\n  });\n}\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */\nfunction toTRBLCorners(value) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */\nfunction toPadding(value) {\n  const obj = toTRBL(value);\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n  return obj;\n}\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */\nfunction toFont(options, fallback) {\n  options = options || {};\n  fallback = fallback || defaults.font;\n  let size = valueOrDefault(options.size, fallback.size);\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = undefined;\n  }\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n  font.string = toFontString(font);\n  return font;\n}\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */\nfunction resolve(inputs, context, index, info) {\n  let cacheable = true;\n  let i, ilen, value;\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */\nfunction _addGrace(minmax, grace, beginAtZero) {\n  const {\n    min,\n    max\n  } = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value, add) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\nfunction createContext(parentContext, context) {\n  return Object.assign(Object.create(parentContext), context);\n}\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */\nfunction _createResolver(scopes, prefixes = [''], rootScopes, fallback, getTarget = () => scopes[0]) {\n  const finalRootScopes = rootScopes || scopes;\n  if (typeof fallback === 'undefined') {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: finalRootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: scope => _createResolver([scope, ...scopes], prefixes, finalRootScopes, fallback)\n  };\n  return new Proxy(cache, {\n    /**\n    * A trap for the delete operator.\n    */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete target._keys; // remove cached keys\n      delete scopes[0][prop]; // remove from top level scope\n      return true;\n    },\n    /**\n    * A trap for getting property values.\n    */\n    get(target, prop) {\n      return _cached(target, prop, () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n    /**\n    * A trap for Object.getOwnPropertyDescriptor.\n    * Also used by Object.hasOwnProperty.\n    */\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n    /**\n    * A trap for Object.getPrototypeOf.\n    */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n    /**\n    * A trap for the in operator.\n    */\n    has(target, prop) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n    /**\n    * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n    */\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n    /**\n    * A trap for setting property values.\n    */\n    set(target, prop, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value; // set to top level scope + cache\n      delete target._keys; // remove cached keys\n      return true;\n    }\n  });\n}\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */\nfunction _attachContext(proxy, context, subProxy, descriptorDefaults) {\n  const cache = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: ctx => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: scope => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    /**\n    * A trap for the delete operator.\n    */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete proxy[prop]; // remove from proxy\n      return true;\n    },\n    /**\n    * A trap for getting property values.\n    */\n    get(target, prop, receiver) {\n      return _cached(target, prop, () => _resolveWithContext(target, prop, receiver));\n    },\n    /**\n    * A trap for Object.getOwnPropertyDescriptor.\n    * Also used by Object.hasOwnProperty.\n    */\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys ? Reflect.has(proxy, prop) ? {\n        enumerable: true,\n        configurable: true\n      } : undefined : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n    /**\n    * A trap for Object.getPrototypeOf.\n    */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n    /**\n    * A trap for the in operator.\n    */\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n    /**\n    * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n    */\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n    /**\n    * A trap for setting property values.\n    */\n    set(target, prop, value) {\n      proxy[prop] = value; // set to proxy\n      delete target[prop]; // remove from cache\n      return true;\n    }\n  });\n}\n/**\n * @private\n */\nfunction _descriptors(proxy, defaults = {\n  scriptable: true,\n  indexable: true\n}) {\n  const {\n    _scriptable = defaults.scriptable,\n    _indexable = defaults.indexable,\n    _allKeys = defaults.allKeys\n  } = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\nconst readKey = (prefix, name) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value) => isObject(value) && prop !== 'adapters' && (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n  if (Object.prototype.hasOwnProperty.call(target, prop) || prop === 'constructor') {\n    return target[prop];\n  }\n  const value = resolve();\n  // cache the resolved value\n  target[prop] = value;\n  return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  let value = _proxy[prop]; // resolve from proxy\n  // resolve with context\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    // if the resolved value is an object, create a sub resolver for it\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\nfunction _resolveScriptable(prop, getValue, target, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _stack\n  } = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  let value = getValue(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    // When scriptable option returns an object, create a resolver on that.\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n    return value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    // Array of objects, return array or resolvers\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent) => key === true ? parent : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n        // When we reach the descriptor that defines a new _fallback, return that.\n        // The fallback will resume to that new scope.\n        return fallback;\n      }\n    } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n      // Fallback to `false` results to `false`, when falling back to different key.\n      // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n      return null;\n    }\n  }\n  return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (typeof fallback !== 'undefined' && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback, () => subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    // For array of objects, the object is used to store updated values\n    return value;\n  }\n  return target || {};\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n  let value;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (typeof value !== 'undefined') {\n      return needsSubResolver(prop, value) ? createSubResolver(scopes, proxy, prop, value) : value;\n    }\n  }\n}\nfunction _resolve(key, scopes) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n  }\n}\nfunction getKeysFromAllScopes(target) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n  const set = new Set();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\nfunction _parseObjectDataRadialScale(meta, data, start, count) {\n  const {\n    iScale\n  } = meta;\n  const {\n    key = 'r'\n  } = this._parsing;\n  const parsed = new Array(count);\n  let i, ilen, index, item;\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i) => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = indexAxis => indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n  // Props to Rob Spencer at scaled innovation for his post on splining between points\n  // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n  // This function must also respect \"skipped\" points\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n  // If all points are the same, s01 & s02 will be inf\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n  const fa = t * s01; // scaling factor for triangle Ta\n  const fb = t * s12;\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n/**\n * Adjust tangents to ensure monotonic properties\n */\nfunction monotoneAdjust(points, deltaK, mK) {\n  const pointsLen = points.length;\n  let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\nfunction monotoneCompute(points, mK, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */\nfunction splineCurveMonotone(points, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK = Array(pointsLen).fill(0);\n  const mK = Array(pointsLen);\n  // Calculate slopes (deltaK) and initialize tangents (mK)\n  let i, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n      // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i] : !pointAfter ? deltaK[i - 1] : sign(deltaK[i - 1]) !== sign(deltaK[i]) ? 0 : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n  monotoneAdjust(points, deltaK, mK);\n  monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n  return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n/**\n * @private\n */\nfunction _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n  let i, ilen, point, controlPoints;\n  // Only consider points that are drawn in case the spanGaps option is used\n  if (options.spanGaps) {\n    points = points.filter(pt => !pt.skip);\n  }\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(prev, point, points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen], options.tension);\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n\n/**\n * Note: typedefs are auto-exported, so use a made-up `dom` namespace where\n * necessary to avoid duplicates with `export * from './helpers`; see\n * https://github.com/microsoft/TypeScript/issues/46011\n * @typedef { import('../core/core.controller.js').default } dom.Chart\n * @typedef { import('../../types').ChartEvent } ChartEvent\n */ /**\n    * @private\n    */\nfunction _isDomSupported() {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n/**\n * @private\n */\nfunction _getParentNode(domNode) {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = parent.host;\n  }\n  return parent;\n}\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */\nfunction parseMaxStyle(styleValue, node, parentProperty) {\n  let valueInPixels;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n    if (styleValue.indexOf('%') !== -1) {\n      // percentage * size in dimension\n      valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n  return valueInPixels;\n}\nconst getComputedStyle = element => element.ownerDocument.defaultView.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n  return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles, style, suffix) {\n  const result = {};\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\nconst useOffsetPos = (x, y, target) => (x > 0 || y > 0) && (!target || !target.shadowRoot);\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */\nfunction getCanvasPosition(e, canvas) {\n  const touches = e.touches;\n  const source = touches && touches.length ? touches[0] : e;\n  const {\n    offsetX,\n    offsetY\n  } = source;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {\n    x,\n    y,\n    box\n  };\n}\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */\nfunction getRelativePosition(event, chart) {\n  if ('native' in event) {\n    return event;\n  }\n  const {\n    canvas,\n    currentDevicePixelRatio\n  } = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {\n    x,\n    y,\n    box\n  } = getCanvasPosition(event, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n  let {\n    width,\n    height\n  } = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\nfunction getContainerSize(canvas, width, height) {\n  let maxWidth, maxHeight;\n  if (width === undefined || height === undefined) {\n    const container = canvas && _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect(); // this is the border box of the container\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\nconst round1 = v => Math.round(v * 10) / 10;\n// eslint-disable-next-line complexity\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {\n    width,\n    height\n  } = containerSize;\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    // https://github.com/chartjs/Chart.js/issues/4659\n    // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n    height = round1(width / 2);\n  }\n  const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n  if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n    height = containerSize.height;\n    width = round1(Math.floor(height * aspectRatio));\n  }\n  return {\n    width,\n    height\n  };\n}\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */\nfunction retinaScale(chart, forceRatio, forceStyle) {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n  chart.height = Math.floor(chart.height);\n  chart.width = Math.floor(chart.width);\n  const canvas = chart.canvas;\n  // If no style has been set on the canvas, the render size is used as display size,\n  // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n  // See https://github.com/chartjs/Chart.js/issues/3575\n  if (canvas.style && (forceStyle || !canvas.style.height && !canvas.style.width)) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n  if (chart.currentDevicePixelRatio !== pixelRatio || canvas.height !== deviceHeight || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */\nconst supportsEventListenerOptions = function () {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n    if (_isDomSupported()) {\n      window.addEventListener('test', null, options);\n      window.removeEventListener('test', null, options);\n    }\n  } catch (e) {\n    // continue regardless of error\n  }\n  return passiveSupported;\n}();\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */\nfunction readUsedSize(element, property) {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n\n/**\n * @private\n */\nfunction _pointInLine(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n/**\n * @private\n */\nfunction _steppedInterpolation(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y : mode === 'after' ? t < 1 ? p1.y : p2.y : t > 0 ? p2.y : p1.y\n  };\n}\n/**\n * @private\n */\nfunction _bezierInterpolation(p1, p2, t, mode) {\n  const cp1 = {\n    x: p1.cp2x,\n    y: p1.cp2y\n  };\n  const cp2 = {\n    x: p2.cp1x,\n    y: p2.cp1y\n  };\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\nconst getRightToLeftAdapter = function (rectX, width) {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    }\n  };\n};\nconst getLeftToRightAdapter = function () {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) {},\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) {\n      return x;\n    }\n  };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n  let style, original;\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [style.getPropertyValue('direction'), style.getPropertyPriority('direction')];\n    style.setProperty('direction', direction, 'important');\n    ctx.prevTextDirection = original;\n  }\n}\nfunction restoreTextDirection(ctx, original) {\n  if (original !== undefined) {\n    delete ctx.prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\nfunction normalizeSegment({\n  start,\n  end,\n  count,\n  loop,\n  style\n}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\nfunction getSegment(segment, points, bounds) {\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const {\n    between,\n    normalize\n  } = propertyFn(property);\n  const count = points.length;\n  let {\n    start,\n    end,\n    loop\n  } = segment;\n  let i, ilen;\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n  if (end < start) {\n    end += count;\n  }\n  return {\n    start,\n    end,\n    loop,\n    style: segment.style\n  };\n}\nfunction _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const count = points.length;\n  const {\n    compare,\n    between,\n    normalize\n  } = propertyFn(property);\n  const {\n    start,\n    end,\n    loop,\n    style\n  } = getSegment(segment, points, bounds);\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n    if (point.skip) {\n      continue;\n    }\n    value = normalize(point[property]);\n    if (value === prevValue) {\n      continue;\n    }\n    inside = between(value, startBound, endBound);\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({\n        start: subStart,\n        end: i,\n        loop,\n        count,\n        style\n      }));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n  if (subStart !== null) {\n    result.push(normalizeSegment({\n      start: subStart,\n      end,\n      loop,\n      count,\n      style\n    }));\n  }\n  return result;\n}\nfunction _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n  if (loop && !spanGaps) {\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n  while (start < count && points[start].skip) {\n    start++;\n  }\n  start %= count;\n  if (loop) {\n    end += start;\n  }\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n  end %= count;\n  return {\n    start,\n    end\n  };\n}\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({\n          start: start % count,\n          end: (end - 1) % count,\n          loop\n        });\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n  if (last !== null) {\n    result.push({\n      start: start % count,\n      end: last % count,\n      loop\n    });\n  }\n  return result;\n}\nfunction _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n  if (!count) {\n    return [];\n  }\n  const loop = !!line._loop;\n  const {\n    start,\n    end\n  } = findStartAndEnd(points, count, loop, spanGaps);\n  if (spanGaps === true) {\n    return splitByStyles(line, [{\n      start,\n      end,\n      loop\n    }], points, segmentOptions);\n  }\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {\n    _datasetIndex: datasetIndex,\n    options: {\n      spanGaps\n    }\n  } = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({\n        start: s % count,\n        end: e % count,\n        loop: l,\n        style: st\n      });\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n  return result;\n}\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\nfunction styleChanged(style, prevStyle) {\n  if (!prevStyle) {\n    return false;\n  }\n  const cache = [];\n  const replacer = function (key, value) {\n    if (!isPatternOrGradient(value)) {\n      return value;\n    }\n    if (!cache.includes(value)) {\n      cache.push(value);\n    }\n    return cache.indexOf(value);\n  };\n  return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\nexport { unclipArea as $, _rlookupByKey as A, _lookupByKey as B, _isPointInArea as C, getAngleFromPoint as D, toPadding as E, each as F, getMaximumSize as G, HALF_PI as H, _getParentNode as I, readUsedSize as J, supportsEventListenerOptions as K, throttled as L, _isDomSupported as M, _factorize as N, finiteOrDefault as O, PI as P, callback as Q, _addGrace as R, _limitValue as S, TAU as T, toDegrees as U, _measureText as V, _int16Range as W, _alignPixel as X, clipArea as Y, renderText as Z, _arrayUnique as _, resolve as a, fontString as a$, toFont as a0, _toLeftRightCenter as a1, _alignStartEnd as a2, overrides as a3, merge as a4, _capitalize as a5, descriptors as a6, isFunction as a7, _attachContext as a8, _createResolver as a9, overrideTextDirection as aA, _textX as aB, restoreTextDirection as aC, drawPointLegend as aD, distanceBetweenPoints as aE, noop as aF, _setMinAndMaxByKey as aG, niceNum as aH, almostWhole as aI, almostEquals as aJ, _decimalPlaces as aK, Ticks as aL, log10 as aM, _longestText as aN, _filterBetween as aO, _lookup as aP, isPatternOrGradient as aQ, getHoverColor as aR, clone as aS, _merger as aT, _mergerIf as aU, _deprecated as aV, _splitKey as aW, toFontString as aX, splineCurve as aY, splineCurveMonotone as aZ, getStyle as a_, _descriptors as aa, mergeIf as ab, uid as ac, debounce as ad, retinaScale as ae, clearCanvas as af, setsEqual as ag, _elementsEqual as ah, _isClickEvent as ai, _isBetween as aj, _readValueToProps as ak, _updateBezierControlPoints as al, _computeSegments as am, _boundSegments as an, _steppedInterpolation as ao, _bezierInterpolation as ap, _pointInLine as aq, _steppedLineTo as ar, _bezierCurveTo as as, drawPoint as at, addRoundedRectPath as au, toTRBL as av, toTRBLCorners as aw, _boundSegment as ax, _normalizeAngle as ay, getRtlAdapter as az, isArray as b, toLineHeight as b0, PITAU as b1, INFINITY as b2, RAD_PER_DEG as b3, QUARTER_PI as b4, TWO_THIRDS_PI as b5, _angleDiff as b6, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, defined as h, isObject as i, createContext as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, _getStartAndCountOfVisiblePoints as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _scaleRangesChanged as w, isNumber as x, _parseObjectDataRadialScale as y, getRelativePosition as z };", "map": {"version": 3, "names": ["Color", "noop", "uid", "id", "isNullOrUndef", "value", "isArray", "Array", "type", "Object", "prototype", "toString", "call", "slice", "isObject", "isNumberFinite", "Number", "isFinite", "finiteOrDefault", "defaultValue", "valueOrDefault", "toPercentage", "dimension", "endsWith", "parseFloat", "toDimension", "callback", "fn", "args", "thisArg", "apply", "each", "loopable", "reverse", "i", "len", "keys", "length", "_elementsEqual", "a0", "a1", "ilen", "v0", "v1", "datasetIndex", "index", "clone", "source", "map", "target", "create", "klen", "k", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "_merger", "options", "tval", "sval", "merge", "sources", "merger", "current", "mergeIf", "_mergerIf", "hasOwnProperty", "_deprecated", "scope", "previous", "undefined", "console", "warn", "keyResolvers", "v", "x", "o", "y", "_splitKey", "parts", "split", "tmp", "part", "push", "_getKeyResolver", "obj", "resolveObjectKey", "resolver", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "defined", "isFunction", "setsEqual", "a", "b", "size", "item", "has", "_isClickEvent", "e", "PI", "Math", "TAU", "PITAU", "INFINITY", "POSITIVE_INFINITY", "RAD_PER_DEG", "HALF_PI", "QUARTER_PI", "TWO_THIRDS_PI", "log10", "sign", "almostEquals", "epsilon", "abs", "niceNum", "range", "roundedRange", "round", "niceRange", "pow", "floor", "fraction", "niceFraction", "_factorize", "result", "sqrt", "sort", "pop", "isNumber", "n", "isNaN", "almostWhole", "rounded", "_setMinAndMaxByKey", "array", "property", "min", "max", "toRadians", "degrees", "toDegrees", "radians", "_decimalPlaces", "p", "getAngleFromPoint", "centrePoint", "anglePoint", "distanceFromXCenter", "distanceFromYCenter", "radialDistanceFromCenter", "angle", "atan2", "distance", "distanceBetweenPoints", "pt1", "pt2", "_angleDiff", "_normalizeAngle", "_angleBetween", "start", "end", "sameAngleIsFullCircle", "s", "angleToStart", "angleToEnd", "startToAngle", "endToAngle", "_limitValue", "_int16Range", "_isBetween", "_lookup", "table", "cmp", "hi", "lo", "mid", "_lookup<PERSON><PERSON><PERSON><PERSON>", "last", "ti", "_rlookupByKey", "_filterBetween", "values", "arrayEvents", "listenArrayEvents", "listener", "_chartjs", "listeners", "defineProperty", "configurable", "enumerable", "for<PERSON>ach", "method", "base", "res", "object", "unlistenArrayEvents", "stub", "splice", "_arrayUnique", "items", "set", "Set", "from", "fontString", "pixelSize", "fontStyle", "fontFamily", "requestAnimFrame", "window", "requestAnimationFrame", "throttled", "argsToUse", "ticking", "debounce", "delay", "timeout", "clearTimeout", "setTimeout", "_toLeftRightCenter", "align", "_alignStartEnd", "_textX", "left", "right", "rtl", "check", "_getStartAndCountOfVisiblePoints", "meta", "points", "animationsDisabled", "pointCount", "count", "_sorted", "iScale", "_parsed", "axis", "minDefined", "maxDefined", "getUserBounds", "getPixelForValue", "_scaleRangesChanged", "xScale", "yScale", "_scaleRanges", "newRang<PERSON>", "xmin", "xmax", "ymin", "ymax", "changed", "assign", "atEdge", "t", "elasticIn", "sin", "elasticOut", "effects", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "cos", "easeOutSine", "easeInOutSine", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "easeOutCirc", "easeInOutCirc", "easeInElastic", "easeOutElastic", "easeInOutElastic", "easeInBack", "easeOutBack", "easeInOutBack", "easeInBounce", "easeOutBounce", "m", "d", "easeInOutBounce", "isPatternOrGradient", "color", "getHoverColor", "saturate", "darken", "hexString", "numbers", "colors", "applyAnimationsDefaults", "defaults", "duration", "easing", "loop", "to", "describe", "_fallback", "_indexable", "_scriptable", "name", "properties", "active", "animation", "resize", "show", "animations", "visible", "hide", "applyLayoutsDefaults", "autoPadding", "padding", "top", "bottom", "intlCache", "Map", "getNumberFormat", "locale", "cache<PERSON>ey", "JSON", "stringify", "formatter", "get", "Intl", "NumberFormat", "formatNumber", "num", "format", "formatters", "numeric", "tickValue", "ticks", "chart", "notation", "delta", "maxTick", "calculateDelta", "log<PERSON><PERSON><PERSON>", "numDecimal", "minimumFractionDigits", "maximumFractionDigits", "logarithmic", "remain", "significand", "includes", "Ticks", "applyScaleDefaults", "display", "offset", "beginAtZero", "bounds", "clip", "grace", "grid", "lineWidth", "drawOnChartArea", "drawTicks", "tick<PERSON><PERSON>th", "tickWidth", "_ctx", "tickColor", "border", "dash", "dashOffset", "width", "title", "text", "minRotation", "maxRotation", "mirror", "textStrokeWidth", "textStrokeColor", "autoSkip", "autoSkipPadding", "labelOffset", "minor", "major", "crossAlign", "showLabelBackdrop", "backdropColor", "backdropPadding", "route", "startsWith", "overrides", "descriptors", "getScope$1", "node", "root", "De<PERSON>ults", "constructor", "_descriptors", "_appliers", "backgroundColor", "borderColor", "datasets", "devicePixelRatio", "context", "platform", "getDevicePixelRatio", "elements", "events", "font", "family", "style", "lineHeight", "weight", "hover", "hoverBackgroundColor", "ctx", "hoverBorderColor", "hoverColor", "indexAxis", "interaction", "mode", "intersect", "includeInvisible", "maintainAspectRatio", "onHover", "onClick", "parsing", "plugins", "responsive", "scale", "scales", "showLine", "drawActiveElementsOnTop", "override", "targetScope", "targetName", "scopeObject", "targetScopeObject", "privateName", "defineProperties", "writable", "local", "appliers", "toFontString", "_measureText", "data", "gc", "longest", "string", "textWidth", "measureText", "_longestText", "arrayOfThings", "cache", "garbageCollect", "save", "j", "jlen", "thing", "nestedThing", "restore", "gcLen", "_alignPixel", "pixel", "currentDevicePixelRatio", "halfWidth", "clearCanvas", "canvas", "getContext", "resetTransform", "clearRect", "height", "drawPoint", "drawPointLegend", "w", "xOffset", "yOffset", "cornerRadius", "xOffsetW", "yOffsetW", "pointStyle", "rotation", "radius", "rad", "translate", "rotate", "drawImage", "beginPath", "ellipse", "arc", "closePath", "moveTo", "lineTo", "SQRT1_2", "rect", "fill", "borderWidth", "stroke", "_isPointInArea", "point", "area", "margin", "clipArea", "unclipArea", "_steppedLineTo", "flip", "midpoint", "_bezierCurveTo", "bezierCurveTo", "cp1x", "cp2x", "cp1y", "cp2y", "setRenderOpts", "opts", "translation", "fillStyle", "textAlign", "textBaseline", "decorateText", "line", "strikethrough", "underline", "metrics", "actualBoundingBoxLeft", "actualBoundingBoxRight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "yDecoration", "strokeStyle", "decorationWidth", "drawBackdrop", "oldColor", "fillRect", "renderText", "lines", "strokeWidth", "strokeColor", "backdrop", "strokeText", "max<PERSON><PERSON><PERSON>", "fillText", "addRoundedRectPath", "h", "topLeft", "bottomLeft", "bottomRight", "topRight", "LINE_HEIGHT", "FONT_STYLE", "toLineHeight", "matches", "match", "numberOrZero", "_readValueToProps", "props", "ret", "objProps", "read", "prop", "toTRBL", "toTRBLCorners", "toPadding", "toFont", "fallback", "parseInt", "resolve", "inputs", "info", "cacheable", "_addGrace", "minmax", "change", "keepZero", "add", "createContext", "parentContext", "_createResolver", "scopes", "prefixes", "rootScopes", "get<PERSON><PERSON><PERSON>", "finalRootScopes", "_resolve", "Symbol", "toStringTag", "_cacheable", "_scopes", "_rootScopes", "_getTarget", "Proxy", "deleteProperty", "_keys", "_cached", "_resolveWithPrefixes", "getOwnPropertyDescriptor", "Reflect", "getPrototypeOf", "getKeysFromAllScopes", "ownKeys", "storage", "_storage", "_attachContext", "proxy", "subProxy", "descriptor<PERSON><PERSON><PERSON><PERSON>", "_proxy", "_context", "_subProxy", "_stack", "setContext", "receiver", "_resolveWithContext", "allKeys", "scriptable", "indexable", "_allKeys", "isScriptable", "isIndexable", "read<PERSON><PERSON>", "prefix", "needsSubResolver", "_resolveScriptable", "_resolveArray", "getValue", "Error", "join", "delete", "createSubResolver", "arr", "filter", "<PERSON><PERSON><PERSON><PERSON>", "getScope", "parent", "addScopes", "parentScopes", "parentFallback", "allScopes", "addScopesFromKey", "subGetTarget", "resolveKeysFromAllScopes", "_parseObjectDataRadialScale", "_parsing", "parsed", "r", "parse", "EPSILON", "getPoint", "skip", "getValueAxis", "splineCurve", "firstPoint", "middlePoint", "afterPoint", "next", "d01", "d12", "s01", "s12", "fa", "fb", "monotoneAdjust", "deltaK", "mK", "pointsLen", "alphaK", "betaK", "tauK", "squaredMagnitude", "pointCurrent", "pointAfter", "monotoneCompute", "valueAxis", "pointBefore", "iPixel", "vPixel", "splineCurveMonotone", "slopeDel<PERSON>", "capControlPoint", "pt", "capBezierPoints", "inArea", "inAreaPrev", "inAreaNext", "_updateBezierControlPoints", "controlPoints", "spanGaps", "cubicInterpolationMode", "prev", "tension", "_isDomSupported", "document", "_getParentNode", "domNode", "parentNode", "host", "parseMaxStyle", "styleValue", "parentProperty", "valueInPixels", "getComputedStyle", "element", "ownerDocument", "defaultView", "getStyle", "el", "getPropertyValue", "positions", "getPositionedStyle", "styles", "suffix", "pos", "useOffsetPos", "shadowRoot", "getCanvasPosition", "touches", "offsetX", "offsetY", "box", "getBoundingClientRect", "clientX", "clientY", "getRelativePosition", "event", "borderBox", "boxSizing", "paddings", "borders", "getContainerSize", "maxHeight", "container", "clientWidth", "clientHeight", "containerStyle", "containerBorder", "containerPadding", "round1", "getMaximumSize", "bb<PERSON><PERSON><PERSON>", "bbHeight", "aspectRatio", "margins", "containerSize", "maintainHeight", "retinaScale", "forceRatio", "forceStyle", "pixelRatio", "deviceHeight", "deviceWidth", "setTransform", "supportsEventListenerOptions", "passiveSupported", "passive", "addEventListener", "removeEventListener", "readUsedSize", "_pointInLine", "p1", "p2", "_steppedInterpolation", "_bezierInterpolation", "cp1", "cp2", "c", "getRightToLeftAdapter", "rectX", "<PERSON><PERSON><PERSON><PERSON>", "xPlus", "leftForLtr", "itemWidth", "getLeftToRightAdapter", "_itemWidth", "getRtlAdapter", "overrideTextDirection", "direction", "original", "getPropertyPriority", "setProperty", "prevTextDirection", "restoreTextDirection", "propertyFn", "between", "compare", "normalize", "normalizeSegment", "getSegment", "segment", "startBound", "endBound", "_boundSegment", "inside", "subStart", "prevValue", "startIsBefore", "endIsBefore", "shouldStart", "shouldStop", "_boundSegments", "segments", "sub", "findStartAndEnd", "solidSegments", "cur", "stop", "_computeSegments", "segmentOptions", "_loop", "splitByStyles", "completeLoop", "_fullLoop", "doSplitByStyles", "chartContext", "_chart", "baseStyle", "readStyle", "_datasetIndex", "prevStyle", "addStyle", "l", "st", "dir", "p0", "p0DataIndex", "p1DataIndex", "styleChanged", "borderCapStyle", "borderDash", "borderDashOffset", "borderJoinStyle", "replacer", "$", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "_", "a$", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "aA", "aB", "aC", "aD", "aE", "aF", "aG", "aH", "aI", "aJ", "aK", "aL", "aM", "aN", "aO", "aP", "aQ", "aR", "aS", "aT", "aU", "aV", "aW", "aX", "aY", "aZ", "a_", "aa", "ab", "ac", "ad", "ae", "af", "ag", "ah", "ai", "aj", "ak", "al", "am", "an", "ao", "ap", "aq", "ar", "as", "at", "au", "av", "aw", "ax", "ay", "az", "b0", "b1", "b2", "b3", "b4", "b5", "b6", "f", "g", "q", "u", "z"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/chart.js/dist/chunks/helpers.segment.js"], "sourcesContent": ["/*!\n * Chart.js v4.4.3\n * https://www.chartjs.org\n * (c) 2024 Chart.js Contributors\n * Released under the MIT License\n */\nimport { Color } from '@kurkle/color';\n\n/**\n * @namespace Chart.helpers\n */ /**\n * An empty function that can be used, for example, for optional callback.\n */ function noop() {\n/* noop */ }\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */ const uid = (()=>{\n    let id = 0;\n    return ()=>id++;\n})();\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */ function isNullOrUndef(value) {\n    return value === null || typeof value === 'undefined';\n}\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */ function isArray(value) {\n    if (Array.isArray && Array.isArray(value)) {\n        return true;\n    }\n    const type = Object.prototype.toString.call(value);\n    if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n        return true;\n    }\n    return false;\n}\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */ function isObject(value) {\n    return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */ function isNumberFinite(value) {\n    return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */ function finiteOrDefault(value, defaultValue) {\n    return isNumberFinite(value) ? value : defaultValue;\n}\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */ function valueOrDefault(value, defaultValue) {\n    return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension)=>typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 : +value / dimension;\nconst toDimension = (value, dimension)=>typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 * dimension : +value;\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */ function callback(fn, args, thisArg) {\n    if (fn && typeof fn.call === 'function') {\n        return fn.apply(thisArg, args);\n    }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n    let i, len, keys;\n    if (isArray(loopable)) {\n        len = loopable.length;\n        if (reverse) {\n            for(i = len - 1; i >= 0; i--){\n                fn.call(thisArg, loopable[i], i);\n            }\n        } else {\n            for(i = 0; i < len; i++){\n                fn.call(thisArg, loopable[i], i);\n            }\n        }\n    } else if (isObject(loopable)) {\n        keys = Object.keys(loopable);\n        len = keys.length;\n        for(i = 0; i < len; i++){\n            fn.call(thisArg, loopable[keys[i]], keys[i]);\n        }\n    }\n}\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */ function _elementsEqual(a0, a1) {\n    let i, ilen, v0, v1;\n    if (!a0 || !a1 || a0.length !== a1.length) {\n        return false;\n    }\n    for(i = 0, ilen = a0.length; i < ilen; ++i){\n        v0 = a0[i];\n        v1 = a1[i];\n        if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */ function clone(source) {\n    if (isArray(source)) {\n        return source.map(clone);\n    }\n    if (isObject(source)) {\n        const target = Object.create(null);\n        const keys = Object.keys(source);\n        const klen = keys.length;\n        let k = 0;\n        for(; k < klen; ++k){\n            target[keys[k]] = clone(source[keys[k]]);\n        }\n        return target;\n    }\n    return source;\n}\nfunction isValidKey(key) {\n    return [\n        '__proto__',\n        'prototype',\n        'constructor'\n    ].indexOf(key) === -1;\n}\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */ function _merger(key, target, source, options) {\n    if (!isValidKey(key)) {\n        return;\n    }\n    const tval = target[key];\n    const sval = source[key];\n    if (isObject(tval) && isObject(sval)) {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        merge(tval, sval, options);\n    } else {\n        target[key] = clone(sval);\n    }\n}\nfunction merge(target, source, options) {\n    const sources = isArray(source) ? source : [\n        source\n    ];\n    const ilen = sources.length;\n    if (!isObject(target)) {\n        return target;\n    }\n    options = options || {};\n    const merger = options.merger || _merger;\n    let current;\n    for(let i = 0; i < ilen; ++i){\n        current = sources[i];\n        if (!isObject(current)) {\n            continue;\n        }\n        const keys = Object.keys(current);\n        for(let k = 0, klen = keys.length; k < klen; ++k){\n            merger(keys[k], target, current, options);\n        }\n    }\n    return target;\n}\nfunction mergeIf(target, source) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    return merge(target, source, {\n        merger: _mergerIf\n    });\n}\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */ function _mergerIf(key, target, source) {\n    if (!isValidKey(key)) {\n        return;\n    }\n    const tval = target[key];\n    const sval = source[key];\n    if (isObject(tval) && isObject(sval)) {\n        mergeIf(tval, sval);\n    } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n        target[key] = clone(sval);\n    }\n}\n/**\n * @private\n */ function _deprecated(scope, value, previous, current) {\n    if (value !== undefined) {\n        console.warn(scope + ': \"' + previous + '\" is deprecated. Please use \"' + current + '\" instead');\n    }\n}\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n    // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n    '': (v)=>v,\n    // default resolvers\n    x: (o)=>o.x,\n    y: (o)=>o.y\n};\n/**\n * @private\n */ function _splitKey(key) {\n    const parts = key.split('.');\n    const keys = [];\n    let tmp = '';\n    for (const part of parts){\n        tmp += part;\n        if (tmp.endsWith('\\\\')) {\n            tmp = tmp.slice(0, -1) + '.';\n        } else {\n            keys.push(tmp);\n            tmp = '';\n        }\n    }\n    return keys;\n}\nfunction _getKeyResolver(key) {\n    const keys = _splitKey(key);\n    return (obj)=>{\n        for (const k of keys){\n            if (k === '') {\n                break;\n            }\n            obj = obj && obj[k];\n        }\n        return obj;\n    };\n}\nfunction resolveObjectKey(obj, key) {\n    const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n    return resolver(obj);\n}\n/**\n * @private\n */ function _capitalize(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = (value)=>typeof value !== 'undefined';\nconst isFunction = (value)=>typeof value === 'function';\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nconst setsEqual = (a, b)=>{\n    if (a.size !== b.size) {\n        return false;\n    }\n    for (const item of a){\n        if (!b.has(item)) {\n            return false;\n        }\n    }\n    return true;\n};\n/**\n * @param e - The event\n * @private\n */ function _isClickEvent(e) {\n    return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */ const PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction almostEquals(x, y, epsilon) {\n    return Math.abs(x - y) < epsilon;\n}\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */ function niceNum(range) {\n    const roundedRange = Math.round(range);\n    range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n    const niceRange = Math.pow(10, Math.floor(log10(range)));\n    const fraction = range / niceRange;\n    const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n    return niceFraction * niceRange;\n}\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */ function _factorize(value) {\n    const result = [];\n    const sqrt = Math.sqrt(value);\n    let i;\n    for(i = 1; i < sqrt; i++){\n        if (value % i === 0) {\n            result.push(i);\n            result.push(value / i);\n        }\n    }\n    if (sqrt === (sqrt | 0)) {\n        result.push(sqrt);\n    }\n    result.sort((a, b)=>a - b).pop();\n    return result;\n}\nfunction isNumber(n) {\n    return !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostWhole(x, epsilon) {\n    const rounded = Math.round(x);\n    return rounded - epsilon <= x && rounded + epsilon >= x;\n}\n/**\n * @private\n */ function _setMinAndMaxByKey(array, target, property) {\n    let i, ilen, value;\n    for(i = 0, ilen = array.length; i < ilen; i++){\n        value = array[i][property];\n        if (!isNaN(value)) {\n            target.min = Math.min(target.min, value);\n            target.max = Math.max(target.max, value);\n        }\n    }\n}\nfunction toRadians(degrees) {\n    return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n    return radians * (180 / PI);\n}\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */ function _decimalPlaces(x) {\n    if (!isNumberFinite(x)) {\n        return;\n    }\n    let e = 1;\n    let p = 0;\n    while(Math.round(x * e) / e !== x){\n        e *= 10;\n        p++;\n    }\n    return p;\n}\n// Gets the angle from vertical upright to the point about a centre.\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n    const distanceFromXCenter = anglePoint.x - centrePoint.x;\n    const distanceFromYCenter = anglePoint.y - centrePoint.y;\n    const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n    let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n    if (angle < -0.5 * PI) {\n        angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n    }\n    return {\n        angle,\n        distance: radialDistanceFromCenter\n    };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n    return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */ function _angleDiff(a, b) {\n    return (a - b + PITAU) % TAU - PI;\n}\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */ function _normalizeAngle(a) {\n    return (a % TAU + TAU) % TAU;\n}\n/**\n * @private\n */ function _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n    const a = _normalizeAngle(angle);\n    const s = _normalizeAngle(start);\n    const e = _normalizeAngle(end);\n    const angleToStart = _normalizeAngle(s - a);\n    const angleToEnd = _normalizeAngle(e - a);\n    const startToAngle = _normalizeAngle(a - s);\n    const endToAngle = _normalizeAngle(a - e);\n    return a === s || a === e || sameAngleIsFullCircle && s === e || angleToStart > angleToEnd && startToAngle < endToAngle;\n}\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */ function _limitValue(value, min, max) {\n    return Math.max(min, Math.min(max, value));\n}\n/**\n * @param {number} value\n * @private\n */ function _int16Range(value) {\n    return _limitValue(value, -32768, 32767);\n}\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */ function _isBetween(value, start, end, epsilon = 1e-6) {\n    return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\n\nfunction _lookup(table, value, cmp) {\n    cmp = cmp || ((index)=>table[index] < value);\n    let hi = table.length - 1;\n    let lo = 0;\n    let mid;\n    while(hi - lo > 1){\n        mid = lo + hi >> 1;\n        if (cmp(mid)) {\n            lo = mid;\n        } else {\n            hi = mid;\n        }\n    }\n    return {\n        lo,\n        hi\n    };\n}\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */ const _lookupByKey = (table, key, value, last)=>_lookup(table, value, last ? (index)=>{\n        const ti = table[index][key];\n        return ti < value || ti === value && table[index + 1][key] === value;\n    } : (index)=>table[index][key] < value);\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */ const _rlookupByKey = (table, key, value)=>_lookup(table, value, (index)=>table[index][key] >= value);\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */ function _filterBetween(values, min, max) {\n    let start = 0;\n    let end = values.length;\n    while(start < end && values[start] < min){\n        start++;\n    }\n    while(end > start && values[end - 1] > max){\n        end--;\n    }\n    return start > 0 || end < values.length ? values.slice(start, end) : values;\n}\nconst arrayEvents = [\n    'push',\n    'pop',\n    'shift',\n    'splice',\n    'unshift'\n];\nfunction listenArrayEvents(array, listener) {\n    if (array._chartjs) {\n        array._chartjs.listeners.push(listener);\n        return;\n    }\n    Object.defineProperty(array, '_chartjs', {\n        configurable: true,\n        enumerable: false,\n        value: {\n            listeners: [\n                listener\n            ]\n        }\n    });\n    arrayEvents.forEach((key)=>{\n        const method = '_onData' + _capitalize(key);\n        const base = array[key];\n        Object.defineProperty(array, key, {\n            configurable: true,\n            enumerable: false,\n            value (...args) {\n                const res = base.apply(this, args);\n                array._chartjs.listeners.forEach((object)=>{\n                    if (typeof object[method] === 'function') {\n                        object[method](...args);\n                    }\n                });\n                return res;\n            }\n        });\n    });\n}\nfunction unlistenArrayEvents(array, listener) {\n    const stub = array._chartjs;\n    if (!stub) {\n        return;\n    }\n    const listeners = stub.listeners;\n    const index = listeners.indexOf(listener);\n    if (index !== -1) {\n        listeners.splice(index, 1);\n    }\n    if (listeners.length > 0) {\n        return;\n    }\n    arrayEvents.forEach((key)=>{\n        delete array[key];\n    });\n    delete array._chartjs;\n}\n/**\n * @param items\n */ function _arrayUnique(items) {\n    const set = new Set(items);\n    if (set.size === items.length) {\n        return items;\n    }\n    return Array.from(set);\n}\n\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n    return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n/**\n* Request animation polyfill\n*/ const requestAnimFrame = function() {\n    if (typeof window === 'undefined') {\n        return function(callback) {\n            return callback();\n        };\n    }\n    return window.requestAnimationFrame;\n}();\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */ function throttled(fn, thisArg) {\n    let argsToUse = [];\n    let ticking = false;\n    return function(...args) {\n        // Save the args for use later\n        argsToUse = args;\n        if (!ticking) {\n            ticking = true;\n            requestAnimFrame.call(window, ()=>{\n                ticking = false;\n                fn.apply(thisArg, argsToUse);\n            });\n        }\n    };\n}\n/**\n * Debounces calling `fn` for `delay` ms\n */ function debounce(fn, delay) {\n    let timeout;\n    return function(...args) {\n        if (delay) {\n            clearTimeout(timeout);\n            timeout = setTimeout(fn, delay, args);\n        } else {\n            fn.apply(this, args);\n        }\n        return delay;\n    };\n}\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */ const _toLeftRightCenter = (align)=>align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */ const _alignStartEnd = (align, start, end)=>align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */ const _textX = (align, left, right, rtl)=>{\n    const check = rtl ? 'left' : 'right';\n    return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n/**\n * Return start and count of visible points.\n * @private\n */ function _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled) {\n    const pointCount = points.length;\n    let start = 0;\n    let count = pointCount;\n    if (meta._sorted) {\n        const { iScale , _parsed  } = meta;\n        const axis = iScale.axis;\n        const { min , max , minDefined , maxDefined  } = iScale.getUserBounds();\n        if (minDefined) {\n            start = _limitValue(Math.min(// @ts-expect-error Need to type _parsed\n            _lookupByKey(_parsed, axis, min).lo, // @ts-expect-error Need to fix types on _lookupByKey\n            animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo), 0, pointCount - 1);\n        }\n        if (maxDefined) {\n            count = _limitValue(Math.max(// @ts-expect-error Need to type _parsed\n            _lookupByKey(_parsed, iScale.axis, max, true).hi + 1, // @ts-expect-error Need to fix types on _lookupByKey\n            animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1), start, pointCount) - start;\n        } else {\n            count = pointCount - start;\n        }\n    }\n    return {\n        start,\n        count\n    };\n}\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */ function _scaleRangesChanged(meta) {\n    const { xScale , yScale , _scaleRanges  } = meta;\n    const newRanges = {\n        xmin: xScale.min,\n        xmax: xScale.max,\n        ymin: yScale.min,\n        ymax: yScale.max\n    };\n    if (!_scaleRanges) {\n        meta._scaleRanges = newRanges;\n        return true;\n    }\n    const changed = _scaleRanges.xmin !== xScale.min || _scaleRanges.xmax !== xScale.max || _scaleRanges.ymin !== yScale.min || _scaleRanges.ymax !== yScale.max;\n    Object.assign(_scaleRanges, newRanges);\n    return changed;\n}\n\nconst atEdge = (t)=>t === 0 || t === 1;\nconst elasticIn = (t, s, p)=>-(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p)=>Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n/**\n * Easing functions adapted from Robert Penner's easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */ const effects = {\n    linear: (t)=>t,\n    easeInQuad: (t)=>t * t,\n    easeOutQuad: (t)=>-t * (t - 2),\n    easeInOutQuad: (t)=>(t /= 0.5) < 1 ? 0.5 * t * t : -0.5 * (--t * (t - 2) - 1),\n    easeInCubic: (t)=>t * t * t,\n    easeOutCubic: (t)=>(t -= 1) * t * t + 1,\n    easeInOutCubic: (t)=>(t /= 0.5) < 1 ? 0.5 * t * t * t : 0.5 * ((t -= 2) * t * t + 2),\n    easeInQuart: (t)=>t * t * t * t,\n    easeOutQuart: (t)=>-((t -= 1) * t * t * t - 1),\n    easeInOutQuart: (t)=>(t /= 0.5) < 1 ? 0.5 * t * t * t * t : -0.5 * ((t -= 2) * t * t * t - 2),\n    easeInQuint: (t)=>t * t * t * t * t,\n    easeOutQuint: (t)=>(t -= 1) * t * t * t * t + 1,\n    easeInOutQuint: (t)=>(t /= 0.5) < 1 ? 0.5 * t * t * t * t * t : 0.5 * ((t -= 2) * t * t * t * t + 2),\n    easeInSine: (t)=>-Math.cos(t * HALF_PI) + 1,\n    easeOutSine: (t)=>Math.sin(t * HALF_PI),\n    easeInOutSine: (t)=>-0.5 * (Math.cos(PI * t) - 1),\n    easeInExpo: (t)=>t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),\n    easeOutExpo: (t)=>t === 1 ? 1 : -Math.pow(2, -10 * t) + 1,\n    easeInOutExpo: (t)=>atEdge(t) ? t : t < 0.5 ? 0.5 * Math.pow(2, 10 * (t * 2 - 1)) : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n    easeInCirc: (t)=>t >= 1 ? t : -(Math.sqrt(1 - t * t) - 1),\n    easeOutCirc: (t)=>Math.sqrt(1 - (t -= 1) * t),\n    easeInOutCirc: (t)=>(t /= 0.5) < 1 ? -0.5 * (Math.sqrt(1 - t * t) - 1) : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n    easeInElastic: (t)=>atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n    easeOutElastic: (t)=>atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n    easeInOutElastic (t) {\n        const s = 0.1125;\n        const p = 0.45;\n        return atEdge(t) ? t : t < 0.5 ? 0.5 * elasticIn(t * 2, s, p) : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n    },\n    easeInBack (t) {\n        const s = 1.70158;\n        return t * t * ((s + 1) * t - s);\n    },\n    easeOutBack (t) {\n        const s = 1.70158;\n        return (t -= 1) * t * ((s + 1) * t + s) + 1;\n    },\n    easeInOutBack (t) {\n        let s = 1.70158;\n        if ((t /= 0.5) < 1) {\n            return 0.5 * (t * t * (((s *= 1.525) + 1) * t - s));\n        }\n        return 0.5 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2);\n    },\n    easeInBounce: (t)=>1 - effects.easeOutBounce(1 - t),\n    easeOutBounce (t) {\n        const m = 7.5625;\n        const d = 2.75;\n        if (t < 1 / d) {\n            return m * t * t;\n        }\n        if (t < 2 / d) {\n            return m * (t -= 1.5 / d) * t + 0.75;\n        }\n        if (t < 2.5 / d) {\n            return m * (t -= 2.25 / d) * t + 0.9375;\n        }\n        return m * (t -= 2.625 / d) * t + 0.984375;\n    },\n    easeInOutBounce: (t)=>t < 0.5 ? effects.easeInBounce(t * 2) * 0.5 : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5\n};\n\nfunction isPatternOrGradient(value) {\n    if (value && typeof value === 'object') {\n        const type = value.toString();\n        return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n    }\n    return false;\n}\nfunction color(value) {\n    return isPatternOrGradient(value) ? value : new Color(value);\n}\nfunction getHoverColor(value) {\n    return isPatternOrGradient(value) ? value : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\n\nconst numbers = [\n    'x',\n    'y',\n    'borderWidth',\n    'radius',\n    'tension'\n];\nconst colors = [\n    'color',\n    'borderColor',\n    'backgroundColor'\n];\nfunction applyAnimationsDefaults(defaults) {\n    defaults.set('animation', {\n        delay: undefined,\n        duration: 1000,\n        easing: 'easeOutQuart',\n        fn: undefined,\n        from: undefined,\n        loop: undefined,\n        to: undefined,\n        type: undefined\n    });\n    defaults.describe('animation', {\n        _fallback: false,\n        _indexable: false,\n        _scriptable: (name)=>name !== 'onProgress' && name !== 'onComplete' && name !== 'fn'\n    });\n    defaults.set('animations', {\n        colors: {\n            type: 'color',\n            properties: colors\n        },\n        numbers: {\n            type: 'number',\n            properties: numbers\n        }\n    });\n    defaults.describe('animations', {\n        _fallback: 'animation'\n    });\n    defaults.set('transitions', {\n        active: {\n            animation: {\n                duration: 400\n            }\n        },\n        resize: {\n            animation: {\n                duration: 0\n            }\n        },\n        show: {\n            animations: {\n                colors: {\n                    from: 'transparent'\n                },\n                visible: {\n                    type: 'boolean',\n                    duration: 0\n                }\n            }\n        },\n        hide: {\n            animations: {\n                colors: {\n                    to: 'transparent'\n                },\n                visible: {\n                    type: 'boolean',\n                    easing: 'linear',\n                    fn: (v)=>v | 0\n                }\n            }\n        }\n    });\n}\n\nfunction applyLayoutsDefaults(defaults) {\n    defaults.set('layout', {\n        autoPadding: true,\n        padding: {\n            top: 0,\n            right: 0,\n            bottom: 0,\n            left: 0\n        }\n    });\n}\n\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n    options = options || {};\n    const cacheKey = locale + JSON.stringify(options);\n    let formatter = intlCache.get(cacheKey);\n    if (!formatter) {\n        formatter = new Intl.NumberFormat(locale, options);\n        intlCache.set(cacheKey, formatter);\n    }\n    return formatter;\n}\nfunction formatNumber(num, locale, options) {\n    return getNumberFormat(locale, options).format(num);\n}\n\nconst formatters = {\n values (value) {\n        return isArray(value) ?  value : '' + value;\n    },\n numeric (tickValue, index, ticks) {\n        if (tickValue === 0) {\n            return '0';\n        }\n        const locale = this.chart.options.locale;\n        let notation;\n        let delta = tickValue;\n        if (ticks.length > 1) {\n            const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n            if (maxTick < 1e-4 || maxTick > 1e+15) {\n                notation = 'scientific';\n            }\n            delta = calculateDelta(tickValue, ticks);\n        }\n        const logDelta = log10(Math.abs(delta));\n        const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n        const options = {\n            notation,\n            minimumFractionDigits: numDecimal,\n            maximumFractionDigits: numDecimal\n        };\n        Object.assign(options, this.options.ticks.format);\n        return formatNumber(tickValue, locale, options);\n    },\n logarithmic (tickValue, index, ticks) {\n        if (tickValue === 0) {\n            return '0';\n        }\n        const remain = ticks[index].significand || tickValue / Math.pow(10, Math.floor(log10(tickValue)));\n        if ([\n            1,\n            2,\n            3,\n            5,\n            10,\n            15\n        ].includes(remain) || index > 0.8 * ticks.length) {\n            return formatters.numeric.call(this, tickValue, index, ticks);\n        }\n        return '';\n    }\n};\nfunction calculateDelta(tickValue, ticks) {\n    let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n    if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n        delta = tickValue - Math.floor(tickValue);\n    }\n    return delta;\n}\n var Ticks = {\n    formatters\n};\n\nfunction applyScaleDefaults(defaults) {\n    defaults.set('scale', {\n        display: true,\n        offset: false,\n        reverse: false,\n        beginAtZero: false,\n bounds: 'ticks',\n        clip: true,\n grace: 0,\n        grid: {\n            display: true,\n            lineWidth: 1,\n            drawOnChartArea: true,\n            drawTicks: true,\n            tickLength: 8,\n            tickWidth: (_ctx, options)=>options.lineWidth,\n            tickColor: (_ctx, options)=>options.color,\n            offset: false\n        },\n        border: {\n            display: true,\n            dash: [],\n            dashOffset: 0.0,\n            width: 1\n        },\n        title: {\n            display: false,\n            text: '',\n            padding: {\n                top: 4,\n                bottom: 4\n            }\n        },\n        ticks: {\n            minRotation: 0,\n            maxRotation: 50,\n            mirror: false,\n            textStrokeWidth: 0,\n            textStrokeColor: '',\n            padding: 3,\n            display: true,\n            autoSkip: true,\n            autoSkipPadding: 3,\n            labelOffset: 0,\n            callback: Ticks.formatters.values,\n            minor: {},\n            major: {},\n            align: 'center',\n            crossAlign: 'near',\n            showLabelBackdrop: false,\n            backdropColor: 'rgba(255, 255, 255, 0.75)',\n            backdropPadding: 2\n        }\n    });\n    defaults.route('scale.ticks', 'color', '', 'color');\n    defaults.route('scale.grid', 'color', '', 'borderColor');\n    defaults.route('scale.border', 'color', '', 'borderColor');\n    defaults.route('scale.title', 'color', '', 'color');\n    defaults.describe('scale', {\n        _fallback: false,\n        _scriptable: (name)=>!name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n        _indexable: (name)=>name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash'\n    });\n    defaults.describe('scales', {\n        _fallback: 'scale'\n    });\n    defaults.describe('scale.ticks', {\n        _scriptable: (name)=>name !== 'backdropPadding' && name !== 'callback',\n        _indexable: (name)=>name !== 'backdropPadding'\n    });\n}\n\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\n function getScope$1(node, key) {\n    if (!key) {\n        return node;\n    }\n    const keys = key.split('.');\n    for(let i = 0, n = keys.length; i < n; ++i){\n        const k = keys[i];\n        node = node[k] || (node[k] = Object.create(null));\n    }\n    return node;\n}\nfunction set(root, scope, values) {\n    if (typeof scope === 'string') {\n        return merge(getScope$1(root, scope), values);\n    }\n    return merge(getScope$1(root, ''), scope);\n}\n class Defaults {\n    constructor(_descriptors, _appliers){\n        this.animation = undefined;\n        this.backgroundColor = 'rgba(0,0,0,0.1)';\n        this.borderColor = 'rgba(0,0,0,0.1)';\n        this.color = '#666';\n        this.datasets = {};\n        this.devicePixelRatio = (context)=>context.chart.platform.getDevicePixelRatio();\n        this.elements = {};\n        this.events = [\n            'mousemove',\n            'mouseout',\n            'click',\n            'touchstart',\n            'touchmove'\n        ];\n        this.font = {\n            family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n            size: 12,\n            style: 'normal',\n            lineHeight: 1.2,\n            weight: null\n        };\n        this.hover = {};\n        this.hoverBackgroundColor = (ctx, options)=>getHoverColor(options.backgroundColor);\n        this.hoverBorderColor = (ctx, options)=>getHoverColor(options.borderColor);\n        this.hoverColor = (ctx, options)=>getHoverColor(options.color);\n        this.indexAxis = 'x';\n        this.interaction = {\n            mode: 'nearest',\n            intersect: true,\n            includeInvisible: false\n        };\n        this.maintainAspectRatio = true;\n        this.onHover = null;\n        this.onClick = null;\n        this.parsing = true;\n        this.plugins = {};\n        this.responsive = true;\n        this.scale = undefined;\n        this.scales = {};\n        this.showLine = true;\n        this.drawActiveElementsOnTop = true;\n        this.describe(_descriptors);\n        this.apply(_appliers);\n    }\n set(scope, values) {\n        return set(this, scope, values);\n    }\n get(scope) {\n        return getScope$1(this, scope);\n    }\n describe(scope, values) {\n        return set(descriptors, scope, values);\n    }\n    override(scope, values) {\n        return set(overrides, scope, values);\n    }\n route(scope, name, targetScope, targetName) {\n        const scopeObject = getScope$1(this, scope);\n        const targetScopeObject = getScope$1(this, targetScope);\n        const privateName = '_' + name;\n        Object.defineProperties(scopeObject, {\n            [privateName]: {\n                value: scopeObject[name],\n                writable: true\n            },\n            [name]: {\n                enumerable: true,\n                get () {\n                    const local = this[privateName];\n                    const target = targetScopeObject[targetName];\n                    if (isObject(local)) {\n                        return Object.assign({}, target, local);\n                    }\n                    return valueOrDefault(local, target);\n                },\n                set (value) {\n                    this[privateName] = value;\n                }\n            }\n        });\n    }\n    apply(appliers) {\n        appliers.forEach((apply)=>apply(this));\n    }\n}\nvar defaults = /* #__PURE__ */ new Defaults({\n    _scriptable: (name)=>!name.startsWith('on'),\n    _indexable: (name)=>name !== 'events',\n    hover: {\n        _fallback: 'interaction'\n    },\n    interaction: {\n        _scriptable: false,\n        _indexable: false\n    }\n}, [\n    applyAnimationsDefaults,\n    applyLayoutsDefaults,\n    applyScaleDefaults\n]);\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */ function toFontString(font) {\n    if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n        return null;\n    }\n    return (font.style ? font.style + ' ' : '') + (font.weight ? font.weight + ' ' : '') + font.size + 'px ' + font.family;\n}\n/**\n * @private\n */ function _measureText(ctx, data, gc, longest, string) {\n    let textWidth = data[string];\n    if (!textWidth) {\n        textWidth = data[string] = ctx.measureText(string).width;\n        gc.push(string);\n    }\n    if (textWidth > longest) {\n        longest = textWidth;\n    }\n    return longest;\n}\n/**\n * @private\n */ // eslint-disable-next-line complexity\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n    cache = cache || {};\n    let data = cache.data = cache.data || {};\n    let gc = cache.garbageCollect = cache.garbageCollect || [];\n    if (cache.font !== font) {\n        data = cache.data = {};\n        gc = cache.garbageCollect = [];\n        cache.font = font;\n    }\n    ctx.save();\n    ctx.font = font;\n    let longest = 0;\n    const ilen = arrayOfThings.length;\n    let i, j, jlen, thing, nestedThing;\n    for(i = 0; i < ilen; i++){\n        thing = arrayOfThings[i];\n        // Undefined strings and arrays should not be measured\n        if (thing !== undefined && thing !== null && !isArray(thing)) {\n            longest = _measureText(ctx, data, gc, longest, thing);\n        } else if (isArray(thing)) {\n            // if it is an array lets measure each element\n            // to do maybe simplify this function a bit so we can do this more recursively?\n            for(j = 0, jlen = thing.length; j < jlen; j++){\n                nestedThing = thing[j];\n                // Undefined strings and arrays should not be measured\n                if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n                    longest = _measureText(ctx, data, gc, longest, nestedThing);\n                }\n            }\n        }\n    }\n    ctx.restore();\n    const gcLen = gc.length / 2;\n    if (gcLen > arrayOfThings.length) {\n        for(i = 0; i < gcLen; i++){\n            delete data[gc[i]];\n        }\n        gc.splice(0, gcLen);\n    }\n    return longest;\n}\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */ function _alignPixel(chart, pixel, width) {\n    const devicePixelRatio = chart.currentDevicePixelRatio;\n    const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n    return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n/**\n * Clears the entire canvas.\n */ function clearCanvas(canvas, ctx) {\n    if (!ctx && !canvas) {\n        return;\n    }\n    ctx = ctx || canvas.getContext('2d');\n    ctx.save();\n    // canvas.width and canvas.height do not consider the canvas transform,\n    // while clearRect does\n    ctx.resetTransform();\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n    ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    drawPointLegend(ctx, options, x, y, null);\n}\n// eslint-disable-next-line complexity\nfunction drawPointLegend(ctx, options, x, y, w) {\n    let type, xOffset, yOffset, size, cornerRadius, width, xOffsetW, yOffsetW;\n    const style = options.pointStyle;\n    const rotation = options.rotation;\n    const radius = options.radius;\n    let rad = (rotation || 0) * RAD_PER_DEG;\n    if (style && typeof style === 'object') {\n        type = style.toString();\n        if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n            ctx.save();\n            ctx.translate(x, y);\n            ctx.rotate(rad);\n            ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n            ctx.restore();\n            return;\n        }\n    }\n    if (isNaN(radius) || radius <= 0) {\n        return;\n    }\n    ctx.beginPath();\n    switch(style){\n        // Default includes circle\n        default:\n            if (w) {\n                ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n            } else {\n                ctx.arc(x, y, radius, 0, TAU);\n            }\n            ctx.closePath();\n            break;\n        case 'triangle':\n            width = w ? w / 2 : radius;\n            ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n            rad += TWO_THIRDS_PI;\n            ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n            rad += TWO_THIRDS_PI;\n            ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n            ctx.closePath();\n            break;\n        case 'rectRounded':\n            // NOTE: the rounded rect implementation changed to use `arc` instead of\n            // `quadraticCurveTo` since it generates better results when rect is\n            // almost a circle. 0.516 (instead of 0.5) produces results with visually\n            // closer proportion to the previous impl and it is inscribed in the\n            // circle with `radius`. For more details, see the following PRs:\n            // https://github.com/chartjs/Chart.js/issues/5597\n            // https://github.com/chartjs/Chart.js/issues/5858\n            cornerRadius = radius * 0.516;\n            size = radius - cornerRadius;\n            xOffset = Math.cos(rad + QUARTER_PI) * size;\n            xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n            yOffset = Math.sin(rad + QUARTER_PI) * size;\n            yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n            ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n            ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n            ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n            ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n            ctx.closePath();\n            break;\n        case 'rect':\n            if (!rotation) {\n                size = Math.SQRT1_2 * radius;\n                width = w ? w / 2 : size;\n                ctx.rect(x - width, y - size, 2 * width, 2 * size);\n                break;\n            }\n            rad += QUARTER_PI;\n        /* falls through */ case 'rectRot':\n            xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n            xOffset = Math.cos(rad) * radius;\n            yOffset = Math.sin(rad) * radius;\n            yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n            ctx.moveTo(x - xOffsetW, y - yOffset);\n            ctx.lineTo(x + yOffsetW, y - xOffset);\n            ctx.lineTo(x + xOffsetW, y + yOffset);\n            ctx.lineTo(x - yOffsetW, y + xOffset);\n            ctx.closePath();\n            break;\n        case 'crossRot':\n            rad += QUARTER_PI;\n        /* falls through */ case 'cross':\n            xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n            xOffset = Math.cos(rad) * radius;\n            yOffset = Math.sin(rad) * radius;\n            yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n            ctx.moveTo(x - xOffsetW, y - yOffset);\n            ctx.lineTo(x + xOffsetW, y + yOffset);\n            ctx.moveTo(x + yOffsetW, y - xOffset);\n            ctx.lineTo(x - yOffsetW, y + xOffset);\n            break;\n        case 'star':\n            xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n            xOffset = Math.cos(rad) * radius;\n            yOffset = Math.sin(rad) * radius;\n            yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n            ctx.moveTo(x - xOffsetW, y - yOffset);\n            ctx.lineTo(x + xOffsetW, y + yOffset);\n            ctx.moveTo(x + yOffsetW, y - xOffset);\n            ctx.lineTo(x - yOffsetW, y + xOffset);\n            rad += QUARTER_PI;\n            xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n            xOffset = Math.cos(rad) * radius;\n            yOffset = Math.sin(rad) * radius;\n            yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n            ctx.moveTo(x - xOffsetW, y - yOffset);\n            ctx.lineTo(x + xOffsetW, y + yOffset);\n            ctx.moveTo(x + yOffsetW, y - xOffset);\n            ctx.lineTo(x - yOffsetW, y + xOffset);\n            break;\n        case 'line':\n            xOffset = w ? w / 2 : Math.cos(rad) * radius;\n            yOffset = Math.sin(rad) * radius;\n            ctx.moveTo(x - xOffset, y - yOffset);\n            ctx.lineTo(x + xOffset, y + yOffset);\n            break;\n        case 'dash':\n            ctx.moveTo(x, y);\n            ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n            break;\n        case false:\n            ctx.closePath();\n            break;\n    }\n    ctx.fill();\n    if (options.borderWidth > 0) {\n        ctx.stroke();\n    }\n}\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */ function _isPointInArea(point, area, margin) {\n    margin = margin || 0.5; // margin - default is to match rounded decimals\n    return !area || point && point.x > area.left - margin && point.x < area.right + margin && point.y > area.top - margin && point.y < area.bottom + margin;\n}\nfunction clipArea(ctx, area) {\n    ctx.save();\n    ctx.beginPath();\n    ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n    ctx.clip();\n}\nfunction unclipArea(ctx) {\n    ctx.restore();\n}\n/**\n * @private\n */ function _steppedLineTo(ctx, previous, target, flip, mode) {\n    if (!previous) {\n        return ctx.lineTo(target.x, target.y);\n    }\n    if (mode === 'middle') {\n        const midpoint = (previous.x + target.x) / 2.0;\n        ctx.lineTo(midpoint, previous.y);\n        ctx.lineTo(midpoint, target.y);\n    } else if (mode === 'after' !== !!flip) {\n        ctx.lineTo(previous.x, target.y);\n    } else {\n        ctx.lineTo(target.x, previous.y);\n    }\n    ctx.lineTo(target.x, target.y);\n}\n/**\n * @private\n */ function _bezierCurveTo(ctx, previous, target, flip) {\n    if (!previous) {\n        return ctx.lineTo(target.x, target.y);\n    }\n    ctx.bezierCurveTo(flip ? previous.cp1x : previous.cp2x, flip ? previous.cp1y : previous.cp2y, flip ? target.cp2x : target.cp1x, flip ? target.cp2y : target.cp1y, target.x, target.y);\n}\nfunction setRenderOpts(ctx, opts) {\n    if (opts.translation) {\n        ctx.translate(opts.translation[0], opts.translation[1]);\n    }\n    if (!isNullOrUndef(opts.rotation)) {\n        ctx.rotate(opts.rotation);\n    }\n    if (opts.color) {\n        ctx.fillStyle = opts.color;\n    }\n    if (opts.textAlign) {\n        ctx.textAlign = opts.textAlign;\n    }\n    if (opts.textBaseline) {\n        ctx.textBaseline = opts.textBaseline;\n    }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n    if (opts.strikethrough || opts.underline) {\n        /**\n     * Now that IE11 support has been dropped, we can use more\n     * of the TextMetrics object. The actual bounding boxes\n     * are unflagged in Chrome, Firefox, Edge, and Safari so they\n     * can be safely used.\n     * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n     */ const metrics = ctx.measureText(line);\n        const left = x - metrics.actualBoundingBoxLeft;\n        const right = x + metrics.actualBoundingBoxRight;\n        const top = y - metrics.actualBoundingBoxAscent;\n        const bottom = y + metrics.actualBoundingBoxDescent;\n        const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n        ctx.strokeStyle = ctx.fillStyle;\n        ctx.beginPath();\n        ctx.lineWidth = opts.decorationWidth || 2;\n        ctx.moveTo(left, yDecoration);\n        ctx.lineTo(right, yDecoration);\n        ctx.stroke();\n    }\n}\nfunction drawBackdrop(ctx, opts) {\n    const oldColor = ctx.fillStyle;\n    ctx.fillStyle = opts.color;\n    ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n    ctx.fillStyle = oldColor;\n}\n/**\n * Render text onto the canvas\n */ function renderText(ctx, text, x, y, font, opts = {}) {\n    const lines = isArray(text) ? text : [\n        text\n    ];\n    const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n    let i, line;\n    ctx.save();\n    ctx.font = font.string;\n    setRenderOpts(ctx, opts);\n    for(i = 0; i < lines.length; ++i){\n        line = lines[i];\n        if (opts.backdrop) {\n            drawBackdrop(ctx, opts.backdrop);\n        }\n        if (stroke) {\n            if (opts.strokeColor) {\n                ctx.strokeStyle = opts.strokeColor;\n            }\n            if (!isNullOrUndef(opts.strokeWidth)) {\n                ctx.lineWidth = opts.strokeWidth;\n            }\n            ctx.strokeText(line, x, y, opts.maxWidth);\n        }\n        ctx.fillText(line, x, y, opts.maxWidth);\n        decorateText(ctx, x, y, line, opts);\n        y += Number(font.lineHeight);\n    }\n    ctx.restore();\n}\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */ function addRoundedRectPath(ctx, rect) {\n    const { x , y , w , h , radius  } = rect;\n    // top left arc\n    ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, 1.5 * PI, PI, true);\n    // line from top left to bottom left\n    ctx.lineTo(x, y + h - radius.bottomLeft);\n    // bottom left arc\n    ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n    // line from bottom left to bottom right\n    ctx.lineTo(x + w - radius.bottomRight, y + h);\n    // bottom right arc\n    ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n    // line from bottom right to top right\n    ctx.lineTo(x + w, y + radius.topRight);\n    // top right arc\n    ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n    // line from top right to top left\n    ctx.lineTo(x + radius.topLeft, y);\n}\n\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n/**\n * @alias Chart.helpers.options\n * @namespace\n */ /**\n * Converts the given line height `value` in pixels for a specific font `size`.\n * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n * @param size - The font size (in pixels) used to resolve relative `value`.\n * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n * @since 2.7.0\n */ function toLineHeight(value, size) {\n    const matches = ('' + value).match(LINE_HEIGHT);\n    if (!matches || matches[1] === 'normal') {\n        return size * 1.2;\n    }\n    value = +matches[2];\n    switch(matches[3]){\n        case 'px':\n            return value;\n        case '%':\n            value /= 100;\n            break;\n    }\n    return size * value;\n}\nconst numberOrZero = (v)=>+v || 0;\nfunction _readValueToProps(value, props) {\n    const ret = {};\n    const objProps = isObject(props);\n    const keys = objProps ? Object.keys(props) : props;\n    const read = isObject(value) ? objProps ? (prop)=>valueOrDefault(value[prop], value[props[prop]]) : (prop)=>value[prop] : ()=>value;\n    for (const prop of keys){\n        ret[prop] = numberOrZero(read(prop));\n    }\n    return ret;\n}\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */ function toTRBL(value) {\n    return _readValueToProps(value, {\n        top: 'y',\n        right: 'x',\n        bottom: 'y',\n        left: 'x'\n    });\n}\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */ function toTRBLCorners(value) {\n    return _readValueToProps(value, [\n        'topLeft',\n        'topRight',\n        'bottomLeft',\n        'bottomRight'\n    ]);\n}\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */ function toPadding(value) {\n    const obj = toTRBL(value);\n    obj.width = obj.left + obj.right;\n    obj.height = obj.top + obj.bottom;\n    return obj;\n}\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */ function toFont(options, fallback) {\n    options = options || {};\n    fallback = fallback || defaults.font;\n    let size = valueOrDefault(options.size, fallback.size);\n    if (typeof size === 'string') {\n        size = parseInt(size, 10);\n    }\n    let style = valueOrDefault(options.style, fallback.style);\n    if (style && !('' + style).match(FONT_STYLE)) {\n        console.warn('Invalid font style specified: \"' + style + '\"');\n        style = undefined;\n    }\n    const font = {\n        family: valueOrDefault(options.family, fallback.family),\n        lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n        size,\n        style,\n        weight: valueOrDefault(options.weight, fallback.weight),\n        string: ''\n    };\n    font.string = toFontString(font);\n    return font;\n}\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */ function resolve(inputs, context, index, info) {\n    let cacheable = true;\n    let i, ilen, value;\n    for(i = 0, ilen = inputs.length; i < ilen; ++i){\n        value = inputs[i];\n        if (value === undefined) {\n            continue;\n        }\n        if (context !== undefined && typeof value === 'function') {\n            value = value(context);\n            cacheable = false;\n        }\n        if (index !== undefined && isArray(value)) {\n            value = value[index % value.length];\n            cacheable = false;\n        }\n        if (value !== undefined) {\n            if (info && !cacheable) {\n                info.cacheable = false;\n            }\n            return value;\n        }\n    }\n}\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */ function _addGrace(minmax, grace, beginAtZero) {\n    const { min , max  } = minmax;\n    const change = toDimension(grace, (max - min) / 2);\n    const keepZero = (value, add)=>beginAtZero && value === 0 ? 0 : value + add;\n    return {\n        min: keepZero(min, -Math.abs(change)),\n        max: keepZero(max, change)\n    };\n}\nfunction createContext(parentContext, context) {\n    return Object.assign(Object.create(parentContext), context);\n}\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */ function _createResolver(scopes, prefixes = [\n    ''\n], rootScopes, fallback, getTarget = ()=>scopes[0]) {\n    const finalRootScopes = rootScopes || scopes;\n    if (typeof fallback === 'undefined') {\n        fallback = _resolve('_fallback', scopes);\n    }\n    const cache = {\n        [Symbol.toStringTag]: 'Object',\n        _cacheable: true,\n        _scopes: scopes,\n        _rootScopes: finalRootScopes,\n        _fallback: fallback,\n        _getTarget: getTarget,\n        override: (scope)=>_createResolver([\n                scope,\n                ...scopes\n            ], prefixes, finalRootScopes, fallback)\n    };\n    return new Proxy(cache, {\n        /**\n     * A trap for the delete operator.\n     */ deleteProperty (target, prop) {\n            delete target[prop]; // remove from cache\n            delete target._keys; // remove cached keys\n            delete scopes[0][prop]; // remove from top level scope\n            return true;\n        },\n        /**\n     * A trap for getting property values.\n     */ get (target, prop) {\n            return _cached(target, prop, ()=>_resolveWithPrefixes(prop, prefixes, scopes, target));\n        },\n        /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */ getOwnPropertyDescriptor (target, prop) {\n            return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n        },\n        /**\n     * A trap for Object.getPrototypeOf.\n     */ getPrototypeOf () {\n            return Reflect.getPrototypeOf(scopes[0]);\n        },\n        /**\n     * A trap for the in operator.\n     */ has (target, prop) {\n            return getKeysFromAllScopes(target).includes(prop);\n        },\n        /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */ ownKeys (target) {\n            return getKeysFromAllScopes(target);\n        },\n        /**\n     * A trap for setting property values.\n     */ set (target, prop, value) {\n            const storage = target._storage || (target._storage = getTarget());\n            target[prop] = storage[prop] = value; // set to top level scope + cache\n            delete target._keys; // remove cached keys\n            return true;\n        }\n    });\n}\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */ function _attachContext(proxy, context, subProxy, descriptorDefaults) {\n    const cache = {\n        _cacheable: false,\n        _proxy: proxy,\n        _context: context,\n        _subProxy: subProxy,\n        _stack: new Set(),\n        _descriptors: _descriptors(proxy, descriptorDefaults),\n        setContext: (ctx)=>_attachContext(proxy, ctx, subProxy, descriptorDefaults),\n        override: (scope)=>_attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n    };\n    return new Proxy(cache, {\n        /**\n     * A trap for the delete operator.\n     */ deleteProperty (target, prop) {\n            delete target[prop]; // remove from cache\n            delete proxy[prop]; // remove from proxy\n            return true;\n        },\n        /**\n     * A trap for getting property values.\n     */ get (target, prop, receiver) {\n            return _cached(target, prop, ()=>_resolveWithContext(target, prop, receiver));\n        },\n        /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */ getOwnPropertyDescriptor (target, prop) {\n            return target._descriptors.allKeys ? Reflect.has(proxy, prop) ? {\n                enumerable: true,\n                configurable: true\n            } : undefined : Reflect.getOwnPropertyDescriptor(proxy, prop);\n        },\n        /**\n     * A trap for Object.getPrototypeOf.\n     */ getPrototypeOf () {\n            return Reflect.getPrototypeOf(proxy);\n        },\n        /**\n     * A trap for the in operator.\n     */ has (target, prop) {\n            return Reflect.has(proxy, prop);\n        },\n        /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */ ownKeys () {\n            return Reflect.ownKeys(proxy);\n        },\n        /**\n     * A trap for setting property values.\n     */ set (target, prop, value) {\n            proxy[prop] = value; // set to proxy\n            delete target[prop]; // remove from cache\n            return true;\n        }\n    });\n}\n/**\n * @private\n */ function _descriptors(proxy, defaults = {\n    scriptable: true,\n    indexable: true\n}) {\n    const { _scriptable =defaults.scriptable , _indexable =defaults.indexable , _allKeys =defaults.allKeys  } = proxy;\n    return {\n        allKeys: _allKeys,\n        scriptable: _scriptable,\n        indexable: _indexable,\n        isScriptable: isFunction(_scriptable) ? _scriptable : ()=>_scriptable,\n        isIndexable: isFunction(_indexable) ? _indexable : ()=>_indexable\n    };\n}\nconst readKey = (prefix, name)=>prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value)=>isObject(value) && prop !== 'adapters' && (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n    if (Object.prototype.hasOwnProperty.call(target, prop) || prop === 'constructor') {\n        return target[prop];\n    }\n    const value = resolve();\n    // cache the resolved value\n    target[prop] = value;\n    return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n    const { _proxy , _context , _subProxy , _descriptors: descriptors  } = target;\n    let value = _proxy[prop]; // resolve from proxy\n    // resolve with context\n    if (isFunction(value) && descriptors.isScriptable(prop)) {\n        value = _resolveScriptable(prop, value, target, receiver);\n    }\n    if (isArray(value) && value.length) {\n        value = _resolveArray(prop, value, target, descriptors.isIndexable);\n    }\n    if (needsSubResolver(prop, value)) {\n        // if the resolved value is an object, create a sub resolver for it\n        value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n    }\n    return value;\n}\nfunction _resolveScriptable(prop, getValue, target, receiver) {\n    const { _proxy , _context , _subProxy , _stack  } = target;\n    if (_stack.has(prop)) {\n        throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n    }\n    _stack.add(prop);\n    let value = getValue(_context, _subProxy || receiver);\n    _stack.delete(prop);\n    if (needsSubResolver(prop, value)) {\n        // When scriptable option returns an object, create a resolver on that.\n        value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n    }\n    return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n    const { _proxy , _context , _subProxy , _descriptors: descriptors  } = target;\n    if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n        return value[_context.index % value.length];\n    } else if (isObject(value[0])) {\n        // Array of objects, return array or resolvers\n        const arr = value;\n        const scopes = _proxy._scopes.filter((s)=>s !== arr);\n        value = [];\n        for (const item of arr){\n            const resolver = createSubResolver(scopes, _proxy, prop, item);\n            value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n        }\n    }\n    return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n    return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent)=>key === true ? parent : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n    for (const parent of parentScopes){\n        const scope = getScope(key, parent);\n        if (scope) {\n            set.add(scope);\n            const fallback = resolveFallback(scope._fallback, key, value);\n            if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n                // When we reach the descriptor that defines a new _fallback, return that.\n                // The fallback will resume to that new scope.\n                return fallback;\n            }\n        } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n            // Fallback to `false` results to `false`, when falling back to different key.\n            // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n            return null;\n        }\n    }\n    return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n    const rootScopes = resolver._rootScopes;\n    const fallback = resolveFallback(resolver._fallback, prop, value);\n    const allScopes = [\n        ...parentScopes,\n        ...rootScopes\n    ];\n    const set = new Set();\n    set.add(value);\n    let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n    if (key === null) {\n        return false;\n    }\n    if (typeof fallback !== 'undefined' && fallback !== prop) {\n        key = addScopesFromKey(set, allScopes, fallback, key, value);\n        if (key === null) {\n            return false;\n        }\n    }\n    return _createResolver(Array.from(set), [\n        ''\n    ], rootScopes, fallback, ()=>subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n    while(key){\n        key = addScopes(set, allScopes, key, fallback, item);\n    }\n    return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n    const parent = resolver._getTarget();\n    if (!(prop in parent)) {\n        parent[prop] = {};\n    }\n    const target = parent[prop];\n    if (isArray(target) && isObject(value)) {\n        // For array of objects, the object is used to store updated values\n        return value;\n    }\n    return target || {};\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n    let value;\n    for (const prefix of prefixes){\n        value = _resolve(readKey(prefix, prop), scopes);\n        if (typeof value !== 'undefined') {\n            return needsSubResolver(prop, value) ? createSubResolver(scopes, proxy, prop, value) : value;\n        }\n    }\n}\nfunction _resolve(key, scopes) {\n    for (const scope of scopes){\n        if (!scope) {\n            continue;\n        }\n        const value = scope[key];\n        if (typeof value !== 'undefined') {\n            return value;\n        }\n    }\n}\nfunction getKeysFromAllScopes(target) {\n    let keys = target._keys;\n    if (!keys) {\n        keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n    }\n    return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n    const set = new Set();\n    for (const scope of scopes){\n        for (const key of Object.keys(scope).filter((k)=>!k.startsWith('_'))){\n            set.add(key);\n        }\n    }\n    return Array.from(set);\n}\nfunction _parseObjectDataRadialScale(meta, data, start, count) {\n    const { iScale  } = meta;\n    const { key ='r'  } = this._parsing;\n    const parsed = new Array(count);\n    let i, ilen, index, item;\n    for(i = 0, ilen = count; i < ilen; ++i){\n        index = i + start;\n        item = data[index];\n        parsed[i] = {\n            r: iScale.parse(resolveObjectKey(item, key), index)\n        };\n    }\n    return parsed;\n}\n\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i)=>i < points.length && !points[i].skip && points[i];\nconst getValueAxis = (indexAxis)=>indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n    // Props to Rob Spencer at scaled innovation for his post on splining between points\n    // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n    // This function must also respect \"skipped\" points\n    const previous = firstPoint.skip ? middlePoint : firstPoint;\n    const current = middlePoint;\n    const next = afterPoint.skip ? middlePoint : afterPoint;\n    const d01 = distanceBetweenPoints(current, previous);\n    const d12 = distanceBetweenPoints(next, current);\n    let s01 = d01 / (d01 + d12);\n    let s12 = d12 / (d01 + d12);\n    // If all points are the same, s01 & s02 will be inf\n    s01 = isNaN(s01) ? 0 : s01;\n    s12 = isNaN(s12) ? 0 : s12;\n    const fa = t * s01; // scaling factor for triangle Ta\n    const fb = t * s12;\n    return {\n        previous: {\n            x: current.x - fa * (next.x - previous.x),\n            y: current.y - fa * (next.y - previous.y)\n        },\n        next: {\n            x: current.x + fb * (next.x - previous.x),\n            y: current.y + fb * (next.y - previous.y)\n        }\n    };\n}\n/**\n * Adjust tangents to ensure monotonic properties\n */ function monotoneAdjust(points, deltaK, mK) {\n    const pointsLen = points.length;\n    let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n    let pointAfter = getPoint(points, 0);\n    for(let i = 0; i < pointsLen - 1; ++i){\n        pointCurrent = pointAfter;\n        pointAfter = getPoint(points, i + 1);\n        if (!pointCurrent || !pointAfter) {\n            continue;\n        }\n        if (almostEquals(deltaK[i], 0, EPSILON)) {\n            mK[i] = mK[i + 1] = 0;\n            continue;\n        }\n        alphaK = mK[i] / deltaK[i];\n        betaK = mK[i + 1] / deltaK[i];\n        squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n        if (squaredMagnitude <= 9) {\n            continue;\n        }\n        tauK = 3 / Math.sqrt(squaredMagnitude);\n        mK[i] = alphaK * tauK * deltaK[i];\n        mK[i + 1] = betaK * tauK * deltaK[i];\n    }\n}\nfunction monotoneCompute(points, mK, indexAxis = 'x') {\n    const valueAxis = getValueAxis(indexAxis);\n    const pointsLen = points.length;\n    let delta, pointBefore, pointCurrent;\n    let pointAfter = getPoint(points, 0);\n    for(let i = 0; i < pointsLen; ++i){\n        pointBefore = pointCurrent;\n        pointCurrent = pointAfter;\n        pointAfter = getPoint(points, i + 1);\n        if (!pointCurrent) {\n            continue;\n        }\n        const iPixel = pointCurrent[indexAxis];\n        const vPixel = pointCurrent[valueAxis];\n        if (pointBefore) {\n            delta = (iPixel - pointBefore[indexAxis]) / 3;\n            pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n            pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n        }\n        if (pointAfter) {\n            delta = (pointAfter[indexAxis] - iPixel) / 3;\n            pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n            pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n        }\n    }\n}\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */ function splineCurveMonotone(points, indexAxis = 'x') {\n    const valueAxis = getValueAxis(indexAxis);\n    const pointsLen = points.length;\n    const deltaK = Array(pointsLen).fill(0);\n    const mK = Array(pointsLen);\n    // Calculate slopes (deltaK) and initialize tangents (mK)\n    let i, pointBefore, pointCurrent;\n    let pointAfter = getPoint(points, 0);\n    for(i = 0; i < pointsLen; ++i){\n        pointBefore = pointCurrent;\n        pointCurrent = pointAfter;\n        pointAfter = getPoint(points, i + 1);\n        if (!pointCurrent) {\n            continue;\n        }\n        if (pointAfter) {\n            const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n            // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n            deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n        }\n        mK[i] = !pointBefore ? deltaK[i] : !pointAfter ? deltaK[i - 1] : sign(deltaK[i - 1]) !== sign(deltaK[i]) ? 0 : (deltaK[i - 1] + deltaK[i]) / 2;\n    }\n    monotoneAdjust(points, deltaK, mK);\n    monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n    return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n    let i, ilen, point, inArea, inAreaPrev;\n    let inAreaNext = _isPointInArea(points[0], area);\n    for(i = 0, ilen = points.length; i < ilen; ++i){\n        inAreaPrev = inArea;\n        inArea = inAreaNext;\n        inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n        if (!inArea) {\n            continue;\n        }\n        point = points[i];\n        if (inAreaPrev) {\n            point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n            point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n        }\n        if (inAreaNext) {\n            point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n            point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n        }\n    }\n}\n/**\n * @private\n */ function _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n    let i, ilen, point, controlPoints;\n    // Only consider points that are drawn in case the spanGaps option is used\n    if (options.spanGaps) {\n        points = points.filter((pt)=>!pt.skip);\n    }\n    if (options.cubicInterpolationMode === 'monotone') {\n        splineCurveMonotone(points, indexAxis);\n    } else {\n        let prev = loop ? points[points.length - 1] : points[0];\n        for(i = 0, ilen = points.length; i < ilen; ++i){\n            point = points[i];\n            controlPoints = splineCurve(prev, point, points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen], options.tension);\n            point.cp1x = controlPoints.previous.x;\n            point.cp1y = controlPoints.previous.y;\n            point.cp2x = controlPoints.next.x;\n            point.cp2y = controlPoints.next.y;\n            prev = point;\n        }\n    }\n    if (options.capBezierPoints) {\n        capBezierPoints(points, area);\n    }\n}\n\n/**\n * Note: typedefs are auto-exported, so use a made-up `dom` namespace where\n * necessary to avoid duplicates with `export * from './helpers`; see\n * https://github.com/microsoft/TypeScript/issues/46011\n * @typedef { import('../core/core.controller.js').default } dom.Chart\n * @typedef { import('../../types').ChartEvent } ChartEvent\n */ /**\n * @private\n */ function _isDomSupported() {\n    return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n/**\n * @private\n */ function _getParentNode(domNode) {\n    let parent = domNode.parentNode;\n    if (parent && parent.toString() === '[object ShadowRoot]') {\n        parent = parent.host;\n    }\n    return parent;\n}\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */ function parseMaxStyle(styleValue, node, parentProperty) {\n    let valueInPixels;\n    if (typeof styleValue === 'string') {\n        valueInPixels = parseInt(styleValue, 10);\n        if (styleValue.indexOf('%') !== -1) {\n            // percentage * size in dimension\n            valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n        }\n    } else {\n        valueInPixels = styleValue;\n    }\n    return valueInPixels;\n}\nconst getComputedStyle = (element)=>element.ownerDocument.defaultView.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n    return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = [\n    'top',\n    'right',\n    'bottom',\n    'left'\n];\nfunction getPositionedStyle(styles, style, suffix) {\n    const result = {};\n    suffix = suffix ? '-' + suffix : '';\n    for(let i = 0; i < 4; i++){\n        const pos = positions[i];\n        result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n    }\n    result.width = result.left + result.right;\n    result.height = result.top + result.bottom;\n    return result;\n}\nconst useOffsetPos = (x, y, target)=>(x > 0 || y > 0) && (!target || !target.shadowRoot);\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */ function getCanvasPosition(e, canvas) {\n    const touches = e.touches;\n    const source = touches && touches.length ? touches[0] : e;\n    const { offsetX , offsetY  } = source;\n    let box = false;\n    let x, y;\n    if (useOffsetPos(offsetX, offsetY, e.target)) {\n        x = offsetX;\n        y = offsetY;\n    } else {\n        const rect = canvas.getBoundingClientRect();\n        x = source.clientX - rect.left;\n        y = source.clientY - rect.top;\n        box = true;\n    }\n    return {\n        x,\n        y,\n        box\n    };\n}\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */ function getRelativePosition(event, chart) {\n    if ('native' in event) {\n        return event;\n    }\n    const { canvas , currentDevicePixelRatio  } = chart;\n    const style = getComputedStyle(canvas);\n    const borderBox = style.boxSizing === 'border-box';\n    const paddings = getPositionedStyle(style, 'padding');\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const { x , y , box  } = getCanvasPosition(event, canvas);\n    const xOffset = paddings.left + (box && borders.left);\n    const yOffset = paddings.top + (box && borders.top);\n    let { width , height  } = chart;\n    if (borderBox) {\n        width -= paddings.width + borders.width;\n        height -= paddings.height + borders.height;\n    }\n    return {\n        x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n        y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n    };\n}\nfunction getContainerSize(canvas, width, height) {\n    let maxWidth, maxHeight;\n    if (width === undefined || height === undefined) {\n        const container = canvas && _getParentNode(canvas);\n        if (!container) {\n            width = canvas.clientWidth;\n            height = canvas.clientHeight;\n        } else {\n            const rect = container.getBoundingClientRect(); // this is the border box of the container\n            const containerStyle = getComputedStyle(container);\n            const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n            const containerPadding = getPositionedStyle(containerStyle, 'padding');\n            width = rect.width - containerPadding.width - containerBorder.width;\n            height = rect.height - containerPadding.height - containerBorder.height;\n            maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n            maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n        }\n    }\n    return {\n        width,\n        height,\n        maxWidth: maxWidth || INFINITY,\n        maxHeight: maxHeight || INFINITY\n    };\n}\nconst round1 = (v)=>Math.round(v * 10) / 10;\n// eslint-disable-next-line complexity\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n    const style = getComputedStyle(canvas);\n    const margins = getPositionedStyle(style, 'margin');\n    const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n    const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n    const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n    let { width , height  } = containerSize;\n    if (style.boxSizing === 'content-box') {\n        const borders = getPositionedStyle(style, 'border', 'width');\n        const paddings = getPositionedStyle(style, 'padding');\n        width -= paddings.width + borders.width;\n        height -= paddings.height + borders.height;\n    }\n    width = Math.max(0, width - margins.width);\n    height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n    width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n    height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n    if (width && !height) {\n        // https://github.com/chartjs/Chart.js/issues/4659\n        // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n        height = round1(width / 2);\n    }\n    const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n    if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n        height = containerSize.height;\n        width = round1(Math.floor(height * aspectRatio));\n    }\n    return {\n        width,\n        height\n    };\n}\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */ function retinaScale(chart, forceRatio, forceStyle) {\n    const pixelRatio = forceRatio || 1;\n    const deviceHeight = Math.floor(chart.height * pixelRatio);\n    const deviceWidth = Math.floor(chart.width * pixelRatio);\n    chart.height = Math.floor(chart.height);\n    chart.width = Math.floor(chart.width);\n    const canvas = chart.canvas;\n    // If no style has been set on the canvas, the render size is used as display size,\n    // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n    // See https://github.com/chartjs/Chart.js/issues/3575\n    if (canvas.style && (forceStyle || !canvas.style.height && !canvas.style.width)) {\n        canvas.style.height = `${chart.height}px`;\n        canvas.style.width = `${chart.width}px`;\n    }\n    if (chart.currentDevicePixelRatio !== pixelRatio || canvas.height !== deviceHeight || canvas.width !== deviceWidth) {\n        chart.currentDevicePixelRatio = pixelRatio;\n        canvas.height = deviceHeight;\n        canvas.width = deviceWidth;\n        chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n        return true;\n    }\n    return false;\n}\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */ const supportsEventListenerOptions = function() {\n    let passiveSupported = false;\n    try {\n        const options = {\n            get passive () {\n                passiveSupported = true;\n                return false;\n            }\n        };\n        if (_isDomSupported()) {\n            window.addEventListener('test', null, options);\n            window.removeEventListener('test', null, options);\n        }\n    } catch (e) {\n    // continue regardless of error\n    }\n    return passiveSupported;\n}();\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */ function readUsedSize(element, property) {\n    const value = getStyle(element, property);\n    const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n    return matches ? +matches[1] : undefined;\n}\n\n/**\n * @private\n */ function _pointInLine(p1, p2, t, mode) {\n    return {\n        x: p1.x + t * (p2.x - p1.x),\n        y: p1.y + t * (p2.y - p1.y)\n    };\n}\n/**\n * @private\n */ function _steppedInterpolation(p1, p2, t, mode) {\n    return {\n        x: p1.x + t * (p2.x - p1.x),\n        y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y : mode === 'after' ? t < 1 ? p1.y : p2.y : t > 0 ? p2.y : p1.y\n    };\n}\n/**\n * @private\n */ function _bezierInterpolation(p1, p2, t, mode) {\n    const cp1 = {\n        x: p1.cp2x,\n        y: p1.cp2y\n    };\n    const cp2 = {\n        x: p2.cp1x,\n        y: p2.cp1y\n    };\n    const a = _pointInLine(p1, cp1, t);\n    const b = _pointInLine(cp1, cp2, t);\n    const c = _pointInLine(cp2, p2, t);\n    const d = _pointInLine(a, b, t);\n    const e = _pointInLine(b, c, t);\n    return _pointInLine(d, e, t);\n}\n\nconst getRightToLeftAdapter = function(rectX, width) {\n    return {\n        x (x) {\n            return rectX + rectX + width - x;\n        },\n        setWidth (w) {\n            width = w;\n        },\n        textAlign (align) {\n            if (align === 'center') {\n                return align;\n            }\n            return align === 'right' ? 'left' : 'right';\n        },\n        xPlus (x, value) {\n            return x - value;\n        },\n        leftForLtr (x, itemWidth) {\n            return x - itemWidth;\n        }\n    };\n};\nconst getLeftToRightAdapter = function() {\n    return {\n        x (x) {\n            return x;\n        },\n        setWidth (w) {},\n        textAlign (align) {\n            return align;\n        },\n        xPlus (x, value) {\n            return x + value;\n        },\n        leftForLtr (x, _itemWidth) {\n            return x;\n        }\n    };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n    return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n    let style, original;\n    if (direction === 'ltr' || direction === 'rtl') {\n        style = ctx.canvas.style;\n        original = [\n            style.getPropertyValue('direction'),\n            style.getPropertyPriority('direction')\n        ];\n        style.setProperty('direction', direction, 'important');\n        ctx.prevTextDirection = original;\n    }\n}\nfunction restoreTextDirection(ctx, original) {\n    if (original !== undefined) {\n        delete ctx.prevTextDirection;\n        ctx.canvas.style.setProperty('direction', original[0], original[1]);\n    }\n}\n\nfunction propertyFn(property) {\n    if (property === 'angle') {\n        return {\n            between: _angleBetween,\n            compare: _angleDiff,\n            normalize: _normalizeAngle\n        };\n    }\n    return {\n        between: _isBetween,\n        compare: (a, b)=>a - b,\n        normalize: (x)=>x\n    };\n}\nfunction normalizeSegment({ start , end , count , loop , style  }) {\n    return {\n        start: start % count,\n        end: end % count,\n        loop: loop && (end - start + 1) % count === 0,\n        style\n    };\n}\nfunction getSegment(segment, points, bounds) {\n    const { property , start: startBound , end: endBound  } = bounds;\n    const { between , normalize  } = propertyFn(property);\n    const count = points.length;\n    let { start , end , loop  } = segment;\n    let i, ilen;\n    if (loop) {\n        start += count;\n        end += count;\n        for(i = 0, ilen = count; i < ilen; ++i){\n            if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n                break;\n            }\n            start--;\n            end--;\n        }\n        start %= count;\n        end %= count;\n    }\n    if (end < start) {\n        end += count;\n    }\n    return {\n        start,\n        end,\n        loop,\n        style: segment.style\n    };\n}\n function _boundSegment(segment, points, bounds) {\n    if (!bounds) {\n        return [\n            segment\n        ];\n    }\n    const { property , start: startBound , end: endBound  } = bounds;\n    const count = points.length;\n    const { compare , between , normalize  } = propertyFn(property);\n    const { start , end , loop , style  } = getSegment(segment, points, bounds);\n    const result = [];\n    let inside = false;\n    let subStart = null;\n    let value, point, prevValue;\n    const startIsBefore = ()=>between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n    const endIsBefore = ()=>compare(endBound, value) === 0 || between(endBound, prevValue, value);\n    const shouldStart = ()=>inside || startIsBefore();\n    const shouldStop = ()=>!inside || endIsBefore();\n    for(let i = start, prev = start; i <= end; ++i){\n        point = points[i % count];\n        if (point.skip) {\n            continue;\n        }\n        value = normalize(point[property]);\n        if (value === prevValue) {\n            continue;\n        }\n        inside = between(value, startBound, endBound);\n        if (subStart === null && shouldStart()) {\n            subStart = compare(value, startBound) === 0 ? i : prev;\n        }\n        if (subStart !== null && shouldStop()) {\n            result.push(normalizeSegment({\n                start: subStart,\n                end: i,\n                loop,\n                count,\n                style\n            }));\n            subStart = null;\n        }\n        prev = i;\n        prevValue = value;\n    }\n    if (subStart !== null) {\n        result.push(normalizeSegment({\n            start: subStart,\n            end,\n            loop,\n            count,\n            style\n        }));\n    }\n    return result;\n}\n function _boundSegments(line, bounds) {\n    const result = [];\n    const segments = line.segments;\n    for(let i = 0; i < segments.length; i++){\n        const sub = _boundSegment(segments[i], line.points, bounds);\n        if (sub.length) {\n            result.push(...sub);\n        }\n    }\n    return result;\n}\n function findStartAndEnd(points, count, loop, spanGaps) {\n    let start = 0;\n    let end = count - 1;\n    if (loop && !spanGaps) {\n        while(start < count && !points[start].skip){\n            start++;\n        }\n    }\n    while(start < count && points[start].skip){\n        start++;\n    }\n    start %= count;\n    if (loop) {\n        end += start;\n    }\n    while(end > start && points[end % count].skip){\n        end--;\n    }\n    end %= count;\n    return {\n        start,\n        end\n    };\n}\n function solidSegments(points, start, max, loop) {\n    const count = points.length;\n    const result = [];\n    let last = start;\n    let prev = points[start];\n    let end;\n    for(end = start + 1; end <= max; ++end){\n        const cur = points[end % count];\n        if (cur.skip || cur.stop) {\n            if (!prev.skip) {\n                loop = false;\n                result.push({\n                    start: start % count,\n                    end: (end - 1) % count,\n                    loop\n                });\n                start = last = cur.stop ? end : null;\n            }\n        } else {\n            last = end;\n            if (prev.skip) {\n                start = end;\n            }\n        }\n        prev = cur;\n    }\n    if (last !== null) {\n        result.push({\n            start: start % count,\n            end: last % count,\n            loop\n        });\n    }\n    return result;\n}\n function _computeSegments(line, segmentOptions) {\n    const points = line.points;\n    const spanGaps = line.options.spanGaps;\n    const count = points.length;\n    if (!count) {\n        return [];\n    }\n    const loop = !!line._loop;\n    const { start , end  } = findStartAndEnd(points, count, loop, spanGaps);\n    if (spanGaps === true) {\n        return splitByStyles(line, [\n            {\n                start,\n                end,\n                loop\n            }\n        ], points, segmentOptions);\n    }\n    const max = end < start ? end + count : end;\n    const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n    return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\n function splitByStyles(line, segments, points, segmentOptions) {\n    if (!segmentOptions || !segmentOptions.setContext || !points) {\n        return segments;\n    }\n    return doSplitByStyles(line, segments, points, segmentOptions);\n}\n function doSplitByStyles(line, segments, points, segmentOptions) {\n    const chartContext = line._chart.getContext();\n    const baseStyle = readStyle(line.options);\n    const { _datasetIndex: datasetIndex , options: { spanGaps  }  } = line;\n    const count = points.length;\n    const result = [];\n    let prevStyle = baseStyle;\n    let start = segments[0].start;\n    let i = start;\n    function addStyle(s, e, l, st) {\n        const dir = spanGaps ? -1 : 1;\n        if (s === e) {\n            return;\n        }\n        s += count;\n        while(points[s % count].skip){\n            s -= dir;\n        }\n        while(points[e % count].skip){\n            e += dir;\n        }\n        if (s % count !== e % count) {\n            result.push({\n                start: s % count,\n                end: e % count,\n                loop: l,\n                style: st\n            });\n            prevStyle = st;\n            start = e % count;\n        }\n    }\n    for (const segment of segments){\n        start = spanGaps ? start : segment.start;\n        let prev = points[start % count];\n        let style;\n        for(i = start + 1; i <= segment.end; i++){\n            const pt = points[i % count];\n            style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n                type: 'segment',\n                p0: prev,\n                p1: pt,\n                p0DataIndex: (i - 1) % count,\n                p1DataIndex: i % count,\n                datasetIndex\n            })));\n            if (styleChanged(style, prevStyle)) {\n                addStyle(start, i - 1, segment.loop, prevStyle);\n            }\n            prev = pt;\n            prevStyle = style;\n        }\n        if (start < i - 1) {\n            addStyle(start, i - 1, segment.loop, prevStyle);\n        }\n    }\n    return result;\n}\nfunction readStyle(options) {\n    return {\n        backgroundColor: options.backgroundColor,\n        borderCapStyle: options.borderCapStyle,\n        borderDash: options.borderDash,\n        borderDashOffset: options.borderDashOffset,\n        borderJoinStyle: options.borderJoinStyle,\n        borderWidth: options.borderWidth,\n        borderColor: options.borderColor\n    };\n}\nfunction styleChanged(style, prevStyle) {\n    if (!prevStyle) {\n        return false;\n    }\n    const cache = [];\n    const replacer = function(key, value) {\n        if (!isPatternOrGradient(value)) {\n            return value;\n        }\n        if (!cache.includes(value)) {\n            cache.push(value);\n        }\n        return cache.indexOf(value);\n    };\n    return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\n\nexport { unclipArea as $, _rlookupByKey as A, _lookupByKey as B, _isPointInArea as C, getAngleFromPoint as D, toPadding as E, each as F, getMaximumSize as G, HALF_PI as H, _getParentNode as I, readUsedSize as J, supportsEventListenerOptions as K, throttled as L, _isDomSupported as M, _factorize as N, finiteOrDefault as O, PI as P, callback as Q, _addGrace as R, _limitValue as S, TAU as T, toDegrees as U, _measureText as V, _int16Range as W, _alignPixel as X, clipArea as Y, renderText as Z, _arrayUnique as _, resolve as a, fontString as a$, toFont as a0, _toLeftRightCenter as a1, _alignStartEnd as a2, overrides as a3, merge as a4, _capitalize as a5, descriptors as a6, isFunction as a7, _attachContext as a8, _createResolver as a9, overrideTextDirection as aA, _textX as aB, restoreTextDirection as aC, drawPointLegend as aD, distanceBetweenPoints as aE, noop as aF, _setMinAndMaxByKey as aG, niceNum as aH, almostWhole as aI, almostEquals as aJ, _decimalPlaces as aK, Ticks as aL, log10 as aM, _longestText as aN, _filterBetween as aO, _lookup as aP, isPatternOrGradient as aQ, getHoverColor as aR, clone as aS, _merger as aT, _mergerIf as aU, _deprecated as aV, _splitKey as aW, toFontString as aX, splineCurve as aY, splineCurveMonotone as aZ, getStyle as a_, _descriptors as aa, mergeIf as ab, uid as ac, debounce as ad, retinaScale as ae, clearCanvas as af, setsEqual as ag, _elementsEqual as ah, _isClickEvent as ai, _isBetween as aj, _readValueToProps as ak, _updateBezierControlPoints as al, _computeSegments as am, _boundSegments as an, _steppedInterpolation as ao, _bezierInterpolation as ap, _pointInLine as aq, _steppedLineTo as ar, _bezierCurveTo as as, drawPoint as at, addRoundedRectPath as au, toTRBL as av, toTRBLCorners as aw, _boundSegment as ax, _normalizeAngle as ay, getRtlAdapter as az, isArray as b, toLineHeight as b0, PITAU as b1, INFINITY as b2, RAD_PER_DEG as b3, QUARTER_PI as b4, TWO_THIRDS_PI as b5, _angleDiff as b6, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, defined as h, isObject as i, createContext as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, _getStartAndCountOfVisiblePoints as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _scaleRangesChanged as w, isNumber as x, _parseObjectDataRadialScale as y, getRelativePosition as z };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAK,QAAQ,eAAe;;AAErC;AACA;AACA,GAFA,CAEI;AACJ;AACA;AAAI,SAASC,IAAIA,CAAA,EAAG;EACpB;AACA;AACA;AACA;AAAI,MAAMC,GAAG,GAAG,CAAC,MAAI;EACjB,IAAIC,EAAE,GAAG,CAAC;EACV,OAAO,MAAIA,EAAE,EAAE;AACnB,CAAC,EAAE,CAAC;AACJ;AACA;AACA;AACA;AACA;AAAI,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC9B,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW;AACzD;AACA;AACA;AACA;AACA;AACA;AAAI,SAASC,OAAOA,CAACD,KAAK,EAAE;EACxB,IAAIE,KAAK,CAACD,OAAO,IAAIC,KAAK,CAACD,OAAO,CAACD,KAAK,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,MAAMG,IAAI,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC;EAClD,IAAIG,IAAI,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,IAAIL,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IAC/D,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AAAI,SAASC,QAAQA,CAACT,KAAK,EAAE;EACzB,OAAOA,KAAK,KAAK,IAAI,IAAII,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC,KAAK,iBAAiB;AACxF;AACA;AACA;AACA;AACA;AAAI,SAASU,cAAcA,CAACV,KAAK,EAAE;EAC/B,OAAO,CAAC,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYW,MAAM,KAAKC,QAAQ,CAAC,CAACZ,KAAK,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA;AAAI,SAASa,eAAeA,CAACb,KAAK,EAAEc,YAAY,EAAE;EAC9C,OAAOJ,cAAc,CAACV,KAAK,CAAC,GAAGA,KAAK,GAAGc,YAAY;AACvD;AACA;AACA;AACA;AACA;AACA;AAAI,SAASC,cAAcA,CAACf,KAAK,EAAEc,YAAY,EAAE;EAC7C,OAAO,OAAOd,KAAK,KAAK,WAAW,GAAGc,YAAY,GAAGd,KAAK;AAC9D;AACA,MAAMgB,YAAY,GAAGA,CAAChB,KAAK,EAAEiB,SAAS,KAAG,OAAOjB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACkB,QAAQ,CAAC,GAAG,CAAC,GAAGC,UAAU,CAACnB,KAAK,CAAC,GAAG,GAAG,GAAG,CAACA,KAAK,GAAGiB,SAAS;AACxI,MAAMG,WAAW,GAAGA,CAACpB,KAAK,EAAEiB,SAAS,KAAG,OAAOjB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACkB,QAAQ,CAAC,GAAG,CAAC,GAAGC,UAAU,CAACnB,KAAK,CAAC,GAAG,GAAG,GAAGiB,SAAS,GAAG,CAACjB,KAAK;AACvI;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASqB,QAAQA,CAACC,EAAE,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACrC,IAAIF,EAAE,IAAI,OAAOA,EAAE,CAACf,IAAI,KAAK,UAAU,EAAE;IACrC,OAAOe,EAAE,CAACG,KAAK,CAACD,OAAO,EAAED,IAAI,CAAC;EAClC;AACJ;AACA,SAASG,IAAIA,CAACC,QAAQ,EAAEL,EAAE,EAAEE,OAAO,EAAEI,OAAO,EAAE;EAC1C,IAAIC,CAAC,EAAEC,GAAG,EAAEC,IAAI;EAChB,IAAI9B,OAAO,CAAC0B,QAAQ,CAAC,EAAE;IACnBG,GAAG,GAAGH,QAAQ,CAACK,MAAM;IACrB,IAAIJ,OAAO,EAAE;MACT,KAAIC,CAAC,GAAGC,GAAG,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAC;QACzBP,EAAE,CAACf,IAAI,CAACiB,OAAO,EAAEG,QAAQ,CAACE,CAAC,CAAC,EAAEA,CAAC,CAAC;MACpC;IACJ,CAAC,MAAM;MACH,KAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAC;QACpBP,EAAE,CAACf,IAAI,CAACiB,OAAO,EAAEG,QAAQ,CAACE,CAAC,CAAC,EAAEA,CAAC,CAAC;MACpC;IACJ;EACJ,CAAC,MAAM,IAAIpB,QAAQ,CAACkB,QAAQ,CAAC,EAAE;IAC3BI,IAAI,GAAG3B,MAAM,CAAC2B,IAAI,CAACJ,QAAQ,CAAC;IAC5BG,GAAG,GAAGC,IAAI,CAACC,MAAM;IACjB,KAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAC;MACpBP,EAAE,CAACf,IAAI,CAACiB,OAAO,EAAEG,QAAQ,CAACI,IAAI,CAACF,CAAC,CAAC,CAAC,EAAEE,IAAI,CAACF,CAAC,CAAC,CAAC;IAChD;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASI,cAAcA,CAACC,EAAE,EAAEC,EAAE,EAAE;EAChC,IAAIN,CAAC,EAAEO,IAAI,EAAEC,EAAE,EAAEC,EAAE;EACnB,IAAI,CAACJ,EAAE,IAAI,CAACC,EAAE,IAAID,EAAE,CAACF,MAAM,KAAKG,EAAE,CAACH,MAAM,EAAE;IACvC,OAAO,KAAK;EAChB;EACA,KAAIH,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGF,EAAE,CAACF,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;IACvCQ,EAAE,GAAGH,EAAE,CAACL,CAAC,CAAC;IACVS,EAAE,GAAGH,EAAE,CAACN,CAAC,CAAC;IACV,IAAIQ,EAAE,CAACE,YAAY,KAAKD,EAAE,CAACC,YAAY,IAAIF,EAAE,CAACG,KAAK,KAAKF,EAAE,CAACE,KAAK,EAAE;MAC9D,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AAAI,SAASC,KAAKA,CAACC,MAAM,EAAE;EACvB,IAAIzC,OAAO,CAACyC,MAAM,CAAC,EAAE;IACjB,OAAOA,MAAM,CAACC,GAAG,CAACF,KAAK,CAAC;EAC5B;EACA,IAAIhC,QAAQ,CAACiC,MAAM,CAAC,EAAE;IAClB,MAAME,MAAM,GAAGxC,MAAM,CAACyC,MAAM,CAAC,IAAI,CAAC;IAClC,MAAMd,IAAI,GAAG3B,MAAM,CAAC2B,IAAI,CAACW,MAAM,CAAC;IAChC,MAAMI,IAAI,GAAGf,IAAI,CAACC,MAAM;IACxB,IAAIe,CAAC,GAAG,CAAC;IACT,OAAMA,CAAC,GAAGD,IAAI,EAAE,EAAEC,CAAC,EAAC;MAChBH,MAAM,CAACb,IAAI,CAACgB,CAAC,CAAC,CAAC,GAAGN,KAAK,CAACC,MAAM,CAACX,IAAI,CAACgB,CAAC,CAAC,CAAC,CAAC;IAC5C;IACA,OAAOH,MAAM;EACjB;EACA,OAAOF,MAAM;AACjB;AACA,SAASM,UAAUA,CAACC,GAAG,EAAE;EACrB,OAAO,CACH,WAAW,EACX,WAAW,EACX,aAAa,CAChB,CAACC,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AAAI,SAASE,OAAOA,CAACF,GAAG,EAAEL,MAAM,EAAEF,MAAM,EAAEU,OAAO,EAAE;EAC/C,IAAI,CAACJ,UAAU,CAACC,GAAG,CAAC,EAAE;IAClB;EACJ;EACA,MAAMI,IAAI,GAAGT,MAAM,CAACK,GAAG,CAAC;EACxB,MAAMK,IAAI,GAAGZ,MAAM,CAACO,GAAG,CAAC;EACxB,IAAIxC,QAAQ,CAAC4C,IAAI,CAAC,IAAI5C,QAAQ,CAAC6C,IAAI,CAAC,EAAE;IAClC;IACAC,KAAK,CAACF,IAAI,EAAEC,IAAI,EAAEF,OAAO,CAAC;EAC9B,CAAC,MAAM;IACHR,MAAM,CAACK,GAAG,CAAC,GAAGR,KAAK,CAACa,IAAI,CAAC;EAC7B;AACJ;AACA,SAASC,KAAKA,CAACX,MAAM,EAAEF,MAAM,EAAEU,OAAO,EAAE;EACpC,MAAMI,OAAO,GAAGvD,OAAO,CAACyC,MAAM,CAAC,GAAGA,MAAM,GAAG,CACvCA,MAAM,CACT;EACD,MAAMN,IAAI,GAAGoB,OAAO,CAACxB,MAAM;EAC3B,IAAI,CAACvB,QAAQ,CAACmC,MAAM,CAAC,EAAE;IACnB,OAAOA,MAAM;EACjB;EACAQ,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAMK,MAAM,GAAGL,OAAO,CAACK,MAAM,IAAIN,OAAO;EACxC,IAAIO,OAAO;EACX,KAAI,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;IACzB6B,OAAO,GAAGF,OAAO,CAAC3B,CAAC,CAAC;IACpB,IAAI,CAACpB,QAAQ,CAACiD,OAAO,CAAC,EAAE;MACpB;IACJ;IACA,MAAM3B,IAAI,GAAG3B,MAAM,CAAC2B,IAAI,CAAC2B,OAAO,CAAC;IACjC,KAAI,IAAIX,CAAC,GAAG,CAAC,EAAED,IAAI,GAAGf,IAAI,CAACC,MAAM,EAAEe,CAAC,GAAGD,IAAI,EAAE,EAAEC,CAAC,EAAC;MAC7CU,MAAM,CAAC1B,IAAI,CAACgB,CAAC,CAAC,EAAEH,MAAM,EAAEc,OAAO,EAAEN,OAAO,CAAC;IAC7C;EACJ;EACA,OAAOR,MAAM;AACjB;AACA,SAASe,OAAOA,CAACf,MAAM,EAAEF,MAAM,EAAE;EAC7B;EACA,OAAOa,KAAK,CAACX,MAAM,EAAEF,MAAM,EAAE;IACzBe,MAAM,EAAEG;EACZ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AAAI,SAASA,SAASA,CAACX,GAAG,EAAEL,MAAM,EAAEF,MAAM,EAAE;EACxC,IAAI,CAACM,UAAU,CAACC,GAAG,CAAC,EAAE;IAClB;EACJ;EACA,MAAMI,IAAI,GAAGT,MAAM,CAACK,GAAG,CAAC;EACxB,MAAMK,IAAI,GAAGZ,MAAM,CAACO,GAAG,CAAC;EACxB,IAAIxC,QAAQ,CAAC4C,IAAI,CAAC,IAAI5C,QAAQ,CAAC6C,IAAI,CAAC,EAAE;IAClCK,OAAO,CAACN,IAAI,EAAEC,IAAI,CAAC;EACvB,CAAC,MAAM,IAAI,CAAClD,MAAM,CAACC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAM,EAAEK,GAAG,CAAC,EAAE;IAC3DL,MAAM,CAACK,GAAG,CAAC,GAAGR,KAAK,CAACa,IAAI,CAAC;EAC7B;AACJ;AACA;AACA;AACA;AAAI,SAASQ,WAAWA,CAACC,KAAK,EAAE/D,KAAK,EAAEgE,QAAQ,EAAEN,OAAO,EAAE;EACtD,IAAI1D,KAAK,KAAKiE,SAAS,EAAE;IACrBC,OAAO,CAACC,IAAI,CAACJ,KAAK,GAAG,KAAK,GAAGC,QAAQ,GAAG,+BAA+B,GAAGN,OAAO,GAAG,WAAW,CAAC;EACpG;AACJ;AACA;AACA,MAAMU,YAAY,GAAG;EACjB;EACA,EAAE,EAAGC,CAAC,IAAGA,CAAC;EACV;EACAC,CAAC,EAAGC,CAAC,IAAGA,CAAC,CAACD,CAAC;EACXE,CAAC,EAAGD,CAAC,IAAGA,CAAC,CAACC;AACd,CAAC;AACD;AACA;AACA;AAAI,SAASC,SAASA,CAACxB,GAAG,EAAE;EACxB,MAAMyB,KAAK,GAAGzB,GAAG,CAAC0B,KAAK,CAAC,GAAG,CAAC;EAC5B,MAAM5C,IAAI,GAAG,EAAE;EACf,IAAI6C,GAAG,GAAG,EAAE;EACZ,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAC;IACrBE,GAAG,IAAIC,IAAI;IACX,IAAID,GAAG,CAAC1D,QAAQ,CAAC,IAAI,CAAC,EAAE;MACpB0D,GAAG,GAAGA,GAAG,CAACpE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG;IAChC,CAAC,MAAM;MACHuB,IAAI,CAAC+C,IAAI,CAACF,GAAG,CAAC;MACdA,GAAG,GAAG,EAAE;IACZ;EACJ;EACA,OAAO7C,IAAI;AACf;AACA,SAASgD,eAAeA,CAAC9B,GAAG,EAAE;EAC1B,MAAMlB,IAAI,GAAG0C,SAAS,CAACxB,GAAG,CAAC;EAC3B,OAAQ+B,GAAG,IAAG;IACV,KAAK,MAAMjC,CAAC,IAAIhB,IAAI,EAAC;MACjB,IAAIgB,CAAC,KAAK,EAAE,EAAE;QACV;MACJ;MACAiC,GAAG,GAAGA,GAAG,IAAIA,GAAG,CAACjC,CAAC,CAAC;IACvB;IACA,OAAOiC,GAAG;EACd,CAAC;AACL;AACA,SAASC,gBAAgBA,CAACD,GAAG,EAAE/B,GAAG,EAAE;EAChC,MAAMiC,QAAQ,GAAGd,YAAY,CAACnB,GAAG,CAAC,KAAKmB,YAAY,CAACnB,GAAG,CAAC,GAAG8B,eAAe,CAAC9B,GAAG,CAAC,CAAC;EAChF,OAAOiC,QAAQ,CAACF,GAAG,CAAC;AACxB;AACA;AACA;AACA;AAAI,SAASG,WAAWA,CAACC,GAAG,EAAE;EAC1B,OAAOA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAAC5E,KAAK,CAAC,CAAC,CAAC;AACrD;AACA,MAAM+E,OAAO,GAAIvF,KAAK,IAAG,OAAOA,KAAK,KAAK,WAAW;AACrD,MAAMwF,UAAU,GAAIxF,KAAK,IAAG,OAAOA,KAAK,KAAK,UAAU;AACvD;AACA,MAAMyF,SAAS,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAG;EACtB,IAAID,CAAC,CAACE,IAAI,KAAKD,CAAC,CAACC,IAAI,EAAE;IACnB,OAAO,KAAK;EAChB;EACA,KAAK,MAAMC,IAAI,IAAIH,CAAC,EAAC;IACjB,IAAI,CAACC,CAAC,CAACG,GAAG,CAACD,IAAI,CAAC,EAAE;MACd,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AAAI,SAASE,aAAaA,CAACC,CAAC,EAAE;EAC1B,OAAOA,CAAC,CAAC7F,IAAI,KAAK,SAAS,IAAI6F,CAAC,CAAC7F,IAAI,KAAK,OAAO,IAAI6F,CAAC,CAAC7F,IAAI,KAAK,aAAa;AACjF;;AAEA;AACA;AACA;AACA;AAAI,MAAM8F,EAAE,GAAGC,IAAI,CAACD,EAAE;AACtB,MAAME,GAAG,GAAG,CAAC,GAAGF,EAAE;AAClB,MAAMG,KAAK,GAAGD,GAAG,GAAGF,EAAE;AACtB,MAAMI,QAAQ,GAAG1F,MAAM,CAAC2F,iBAAiB;AACzC,MAAMC,WAAW,GAAGN,EAAE,GAAG,GAAG;AAC5B,MAAMO,OAAO,GAAGP,EAAE,GAAG,CAAC;AACtB,MAAMQ,UAAU,GAAGR,EAAE,GAAG,CAAC;AACzB,MAAMS,aAAa,GAAGT,EAAE,GAAG,CAAC,GAAG,CAAC;AAChC,MAAMU,KAAK,GAAGT,IAAI,CAACS,KAAK;AACxB,MAAMC,IAAI,GAAGV,IAAI,CAACU,IAAI;AACtB,SAASC,YAAYA,CAACvC,CAAC,EAAEE,CAAC,EAAEsC,OAAO,EAAE;EACjC,OAAOZ,IAAI,CAACa,GAAG,CAACzC,CAAC,GAAGE,CAAC,CAAC,GAAGsC,OAAO;AACpC;AACA;AACA;AACA;AAAI,SAASE,OAAOA,CAACC,KAAK,EAAE;EACxB,MAAMC,YAAY,GAAGhB,IAAI,CAACiB,KAAK,CAACF,KAAK,CAAC;EACtCA,KAAK,GAAGJ,YAAY,CAACI,KAAK,EAAEC,YAAY,EAAED,KAAK,GAAG,IAAI,CAAC,GAAGC,YAAY,GAAGD,KAAK;EAC9E,MAAMG,SAAS,GAAGlB,IAAI,CAACmB,GAAG,CAAC,EAAE,EAAEnB,IAAI,CAACoB,KAAK,CAACX,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC;EACxD,MAAMM,QAAQ,GAAGN,KAAK,GAAGG,SAAS;EAClC,MAAMI,YAAY,GAAGD,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAGA,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAGA,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;EACnF,OAAOC,YAAY,GAAGJ,SAAS;AACnC;AACA;AACA;AACA;AACA;AAAI,SAASK,UAAUA,CAACzH,KAAK,EAAE;EAC3B,MAAM0H,MAAM,GAAG,EAAE;EACjB,MAAMC,IAAI,GAAGzB,IAAI,CAACyB,IAAI,CAAC3H,KAAK,CAAC;EAC7B,IAAI6B,CAAC;EACL,KAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8F,IAAI,EAAE9F,CAAC,EAAE,EAAC;IACrB,IAAI7B,KAAK,GAAG6B,CAAC,KAAK,CAAC,EAAE;MACjB6F,MAAM,CAAC5C,IAAI,CAACjD,CAAC,CAAC;MACd6F,MAAM,CAAC5C,IAAI,CAAC9E,KAAK,GAAG6B,CAAC,CAAC;IAC1B;EACJ;EACA,IAAI8F,IAAI,MAAMA,IAAI,GAAG,CAAC,CAAC,EAAE;IACrBD,MAAM,CAAC5C,IAAI,CAAC6C,IAAI,CAAC;EACrB;EACAD,MAAM,CAACE,IAAI,CAAC,CAAClC,CAAC,EAAEC,CAAC,KAAGD,CAAC,GAAGC,CAAC,CAAC,CAACkC,GAAG,CAAC,CAAC;EAChC,OAAOH,MAAM;AACjB;AACA,SAASI,QAAQA,CAACC,CAAC,EAAE;EACjB,OAAO,CAACC,KAAK,CAAC7G,UAAU,CAAC4G,CAAC,CAAC,CAAC,IAAInH,QAAQ,CAACmH,CAAC,CAAC;AAC/C;AACA,SAASE,WAAWA,CAAC3D,CAAC,EAAEwC,OAAO,EAAE;EAC7B,MAAMoB,OAAO,GAAGhC,IAAI,CAACiB,KAAK,CAAC7C,CAAC,CAAC;EAC7B,OAAO4D,OAAO,GAAGpB,OAAO,IAAIxC,CAAC,IAAI4D,OAAO,GAAGpB,OAAO,IAAIxC,CAAC;AAC3D;AACA;AACA;AACA;AAAI,SAAS6D,kBAAkBA,CAACC,KAAK,EAAExF,MAAM,EAAEyF,QAAQ,EAAE;EACrD,IAAIxG,CAAC,EAAEO,IAAI,EAAEpC,KAAK;EAClB,KAAI6B,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGgG,KAAK,CAACpG,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAEP,CAAC,EAAE,EAAC;IAC1C7B,KAAK,GAAGoI,KAAK,CAACvG,CAAC,CAAC,CAACwG,QAAQ,CAAC;IAC1B,IAAI,CAACL,KAAK,CAAChI,KAAK,CAAC,EAAE;MACf4C,MAAM,CAAC0F,GAAG,GAAGpC,IAAI,CAACoC,GAAG,CAAC1F,MAAM,CAAC0F,GAAG,EAAEtI,KAAK,CAAC;MACxC4C,MAAM,CAAC2F,GAAG,GAAGrC,IAAI,CAACqC,GAAG,CAAC3F,MAAM,CAAC2F,GAAG,EAAEvI,KAAK,CAAC;IAC5C;EACJ;AACJ;AACA,SAASwI,SAASA,CAACC,OAAO,EAAE;EACxB,OAAOA,OAAO,IAAIxC,EAAE,GAAG,GAAG,CAAC;AAC/B;AACA,SAASyC,SAASA,CAACC,OAAO,EAAE;EACxB,OAAOA,OAAO,IAAI,GAAG,GAAG1C,EAAE,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS2C,cAAcA,CAACtE,CAAC,EAAE;EAC3B,IAAI,CAAC5D,cAAc,CAAC4D,CAAC,CAAC,EAAE;IACpB;EACJ;EACA,IAAI0B,CAAC,GAAG,CAAC;EACT,IAAI6C,CAAC,GAAG,CAAC;EACT,OAAM3C,IAAI,CAACiB,KAAK,CAAC7C,CAAC,GAAG0B,CAAC,CAAC,GAAGA,CAAC,KAAK1B,CAAC,EAAC;IAC9B0B,CAAC,IAAI,EAAE;IACP6C,CAAC,EAAE;EACP;EACA,OAAOA,CAAC;AACZ;AACA;AACA,SAASC,iBAAiBA,CAACC,WAAW,EAAEC,UAAU,EAAE;EAChD,MAAMC,mBAAmB,GAAGD,UAAU,CAAC1E,CAAC,GAAGyE,WAAW,CAACzE,CAAC;EACxD,MAAM4E,mBAAmB,GAAGF,UAAU,CAACxE,CAAC,GAAGuE,WAAW,CAACvE,CAAC;EACxD,MAAM2E,wBAAwB,GAAGjD,IAAI,CAACyB,IAAI,CAACsB,mBAAmB,GAAGA,mBAAmB,GAAGC,mBAAmB,GAAGA,mBAAmB,CAAC;EACjI,IAAIE,KAAK,GAAGlD,IAAI,CAACmD,KAAK,CAACH,mBAAmB,EAAED,mBAAmB,CAAC;EAChE,IAAIG,KAAK,GAAG,CAAC,GAAG,GAAGnD,EAAE,EAAE;IACnBmD,KAAK,IAAIjD,GAAG,CAAC,CAAC;EAClB;EACA,OAAO;IACHiD,KAAK;IACLE,QAAQ,EAAEH;EACd,CAAC;AACL;AACA,SAASI,qBAAqBA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACrC,OAAOvD,IAAI,CAACyB,IAAI,CAACzB,IAAI,CAACmB,GAAG,CAACoC,GAAG,CAACnF,CAAC,GAAGkF,GAAG,CAAClF,CAAC,EAAE,CAAC,CAAC,GAAG4B,IAAI,CAACmB,GAAG,CAACoC,GAAG,CAACjF,CAAC,GAAGgF,GAAG,CAAChF,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7E;AACA;AACA;AACA;AACA;AAAI,SAASkF,UAAUA,CAAChE,CAAC,EAAEC,CAAC,EAAE;EAC1B,OAAO,CAACD,CAAC,GAAGC,CAAC,GAAGS,KAAK,IAAID,GAAG,GAAGF,EAAE;AACrC;AACA;AACA;AACA;AACA;AAAI,SAAS0D,eAAeA,CAACjE,CAAC,EAAE;EAC5B,OAAO,CAACA,CAAC,GAAGS,GAAG,GAAGA,GAAG,IAAIA,GAAG;AAChC;AACA;AACA;AACA;AAAI,SAASyD,aAAaA,CAACR,KAAK,EAAES,KAAK,EAAEC,GAAG,EAAEC,qBAAqB,EAAE;EACjE,MAAMrE,CAAC,GAAGiE,eAAe,CAACP,KAAK,CAAC;EAChC,MAAMY,CAAC,GAAGL,eAAe,CAACE,KAAK,CAAC;EAChC,MAAM7D,CAAC,GAAG2D,eAAe,CAACG,GAAG,CAAC;EAC9B,MAAMG,YAAY,GAAGN,eAAe,CAACK,CAAC,GAAGtE,CAAC,CAAC;EAC3C,MAAMwE,UAAU,GAAGP,eAAe,CAAC3D,CAAC,GAAGN,CAAC,CAAC;EACzC,MAAMyE,YAAY,GAAGR,eAAe,CAACjE,CAAC,GAAGsE,CAAC,CAAC;EAC3C,MAAMI,UAAU,GAAGT,eAAe,CAACjE,CAAC,GAAGM,CAAC,CAAC;EACzC,OAAON,CAAC,KAAKsE,CAAC,IAAItE,CAAC,KAAKM,CAAC,IAAI+D,qBAAqB,IAAIC,CAAC,KAAKhE,CAAC,IAAIiE,YAAY,GAAGC,UAAU,IAAIC,YAAY,GAAGC,UAAU;AAC3H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASC,WAAWA,CAACrK,KAAK,EAAEsI,GAAG,EAAEC,GAAG,EAAE;EACtC,OAAOrC,IAAI,CAACqC,GAAG,CAACD,GAAG,EAAEpC,IAAI,CAACoC,GAAG,CAACC,GAAG,EAAEvI,KAAK,CAAC,CAAC;AAC9C;AACA;AACA;AACA;AACA;AAAI,SAASsK,WAAWA,CAACtK,KAAK,EAAE;EAC5B,OAAOqK,WAAW,CAACrK,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASuK,UAAUA,CAACvK,KAAK,EAAE6J,KAAK,EAAEC,GAAG,EAAEhD,OAAO,GAAG,IAAI,EAAE;EACvD,OAAO9G,KAAK,IAAIkG,IAAI,CAACoC,GAAG,CAACuB,KAAK,EAAEC,GAAG,CAAC,GAAGhD,OAAO,IAAI9G,KAAK,IAAIkG,IAAI,CAACqC,GAAG,CAACsB,KAAK,EAAEC,GAAG,CAAC,GAAGhD,OAAO;AAC7F;AAEA,SAAS0D,OAAOA,CAACC,KAAK,EAAEzK,KAAK,EAAE0K,GAAG,EAAE;EAChCA,GAAG,GAAGA,GAAG,KAAMlI,KAAK,IAAGiI,KAAK,CAACjI,KAAK,CAAC,GAAGxC,KAAK,CAAC;EAC5C,IAAI2K,EAAE,GAAGF,KAAK,CAACzI,MAAM,GAAG,CAAC;EACzB,IAAI4I,EAAE,GAAG,CAAC;EACV,IAAIC,GAAG;EACP,OAAMF,EAAE,GAAGC,EAAE,GAAG,CAAC,EAAC;IACdC,GAAG,GAAGD,EAAE,GAAGD,EAAE,IAAI,CAAC;IAClB,IAAID,GAAG,CAACG,GAAG,CAAC,EAAE;MACVD,EAAE,GAAGC,GAAG;IACZ,CAAC,MAAM;MACHF,EAAE,GAAGE,GAAG;IACZ;EACJ;EACA,OAAO;IACHD,EAAE;IACFD;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,MAAMG,YAAY,GAAGA,CAACL,KAAK,EAAExH,GAAG,EAAEjD,KAAK,EAAE+K,IAAI,KAAGP,OAAO,CAACC,KAAK,EAAEzK,KAAK,EAAE+K,IAAI,GAAIvI,KAAK,IAAG;EAClF,MAAMwI,EAAE,GAAGP,KAAK,CAACjI,KAAK,CAAC,CAACS,GAAG,CAAC;EAC5B,OAAO+H,EAAE,GAAGhL,KAAK,IAAIgL,EAAE,KAAKhL,KAAK,IAAIyK,KAAK,CAACjI,KAAK,GAAG,CAAC,CAAC,CAACS,GAAG,CAAC,KAAKjD,KAAK;AACxE,CAAC,GAAIwC,KAAK,IAAGiI,KAAK,CAACjI,KAAK,CAAC,CAACS,GAAG,CAAC,GAAGjD,KAAK,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,MAAMiL,aAAa,GAAGA,CAACR,KAAK,EAAExH,GAAG,EAAEjD,KAAK,KAAGwK,OAAO,CAACC,KAAK,EAAEzK,KAAK,EAAGwC,KAAK,IAAGiI,KAAK,CAACjI,KAAK,CAAC,CAACS,GAAG,CAAC,IAAIjD,KAAK,CAAC;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASkL,cAAcA,CAACC,MAAM,EAAE7C,GAAG,EAAEC,GAAG,EAAE;EAC1C,IAAIsB,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGqB,MAAM,CAACnJ,MAAM;EACvB,OAAM6H,KAAK,GAAGC,GAAG,IAAIqB,MAAM,CAACtB,KAAK,CAAC,GAAGvB,GAAG,EAAC;IACrCuB,KAAK,EAAE;EACX;EACA,OAAMC,GAAG,GAAGD,KAAK,IAAIsB,MAAM,CAACrB,GAAG,GAAG,CAAC,CAAC,GAAGvB,GAAG,EAAC;IACvCuB,GAAG,EAAE;EACT;EACA,OAAOD,KAAK,GAAG,CAAC,IAAIC,GAAG,GAAGqB,MAAM,CAACnJ,MAAM,GAAGmJ,MAAM,CAAC3K,KAAK,CAACqJ,KAAK,EAAEC,GAAG,CAAC,GAAGqB,MAAM;AAC/E;AACA,MAAMC,WAAW,GAAG,CAChB,MAAM,EACN,KAAK,EACL,OAAO,EACP,QAAQ,EACR,SAAS,CACZ;AACD,SAASC,iBAAiBA,CAACjD,KAAK,EAAEkD,QAAQ,EAAE;EACxC,IAAIlD,KAAK,CAACmD,QAAQ,EAAE;IAChBnD,KAAK,CAACmD,QAAQ,CAACC,SAAS,CAAC1G,IAAI,CAACwG,QAAQ,CAAC;IACvC;EACJ;EACAlL,MAAM,CAACqL,cAAc,CAACrD,KAAK,EAAE,UAAU,EAAE;IACrCsD,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,KAAK;IACjB3L,KAAK,EAAE;MACHwL,SAAS,EAAE,CACPF,QAAQ;IAEhB;EACJ,CAAC,CAAC;EACFF,WAAW,CAACQ,OAAO,CAAE3I,GAAG,IAAG;IACvB,MAAM4I,MAAM,GAAG,SAAS,GAAG1G,WAAW,CAAClC,GAAG,CAAC;IAC3C,MAAM6I,IAAI,GAAG1D,KAAK,CAACnF,GAAG,CAAC;IACvB7C,MAAM,CAACqL,cAAc,CAACrD,KAAK,EAAEnF,GAAG,EAAE;MAC9ByI,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,KAAK;MACjB3L,KAAKA,CAAE,GAAGuB,IAAI,EAAE;QACZ,MAAMwK,GAAG,GAAGD,IAAI,CAACrK,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;QAClC6G,KAAK,CAACmD,QAAQ,CAACC,SAAS,CAACI,OAAO,CAAEI,MAAM,IAAG;UACvC,IAAI,OAAOA,MAAM,CAACH,MAAM,CAAC,KAAK,UAAU,EAAE;YACtCG,MAAM,CAACH,MAAM,CAAC,CAAC,GAAGtK,IAAI,CAAC;UAC3B;QACJ,CAAC,CAAC;QACF,OAAOwK,GAAG;MACd;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,SAASE,mBAAmBA,CAAC7D,KAAK,EAAEkD,QAAQ,EAAE;EAC1C,MAAMY,IAAI,GAAG9D,KAAK,CAACmD,QAAQ;EAC3B,IAAI,CAACW,IAAI,EAAE;IACP;EACJ;EACA,MAAMV,SAAS,GAAGU,IAAI,CAACV,SAAS;EAChC,MAAMhJ,KAAK,GAAGgJ,SAAS,CAACtI,OAAO,CAACoI,QAAQ,CAAC;EACzC,IAAI9I,KAAK,KAAK,CAAC,CAAC,EAAE;IACdgJ,SAAS,CAACW,MAAM,CAAC3J,KAAK,EAAE,CAAC,CAAC;EAC9B;EACA,IAAIgJ,SAAS,CAACxJ,MAAM,GAAG,CAAC,EAAE;IACtB;EACJ;EACAoJ,WAAW,CAACQ,OAAO,CAAE3I,GAAG,IAAG;IACvB,OAAOmF,KAAK,CAACnF,GAAG,CAAC;EACrB,CAAC,CAAC;EACF,OAAOmF,KAAK,CAACmD,QAAQ;AACzB;AACA;AACA;AACA;AAAI,SAASa,YAAYA,CAACC,KAAK,EAAE;EAC7B,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAACF,KAAK,CAAC;EAC1B,IAAIC,GAAG,CAAC1G,IAAI,KAAKyG,KAAK,CAACrK,MAAM,EAAE;IAC3B,OAAOqK,KAAK;EAChB;EACA,OAAOnM,KAAK,CAACsM,IAAI,CAACF,GAAG,CAAC;AAC1B;AAEA,SAASG,UAAUA,CAACC,SAAS,EAAEC,SAAS,EAAEC,UAAU,EAAE;EAClD,OAAOD,SAAS,GAAG,GAAG,GAAGD,SAAS,GAAG,KAAK,GAAGE,UAAU;AAC3D;AACA;AACA;AACA;AAAG,MAAMC,gBAAgB,GAAG,YAAW;EACnC,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAO,UAASzL,QAAQ,EAAE;MACtB,OAAOA,QAAQ,CAAC,CAAC;IACrB,CAAC;EACL;EACA,OAAOyL,MAAM,CAACC,qBAAqB;AACvC,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AAAI,SAASC,SAASA,CAAC1L,EAAE,EAAEE,OAAO,EAAE;EAChC,IAAIyL,SAAS,GAAG,EAAE;EAClB,IAAIC,OAAO,GAAG,KAAK;EACnB,OAAO,UAAS,GAAG3L,IAAI,EAAE;IACrB;IACA0L,SAAS,GAAG1L,IAAI;IAChB,IAAI,CAAC2L,OAAO,EAAE;MACVA,OAAO,GAAG,IAAI;MACdL,gBAAgB,CAACtM,IAAI,CAACuM,MAAM,EAAE,MAAI;QAC9BI,OAAO,GAAG,KAAK;QACf5L,EAAE,CAACG,KAAK,CAACD,OAAO,EAAEyL,SAAS,CAAC;MAChC,CAAC,CAAC;IACN;EACJ,CAAC;AACL;AACA;AACA;AACA;AAAI,SAASE,QAAQA,CAAC7L,EAAE,EAAE8L,KAAK,EAAE;EAC7B,IAAIC,OAAO;EACX,OAAO,UAAS,GAAG9L,IAAI,EAAE;IACrB,IAAI6L,KAAK,EAAE;MACPE,YAAY,CAACD,OAAO,CAAC;MACrBA,OAAO,GAAGE,UAAU,CAACjM,EAAE,EAAE8L,KAAK,EAAE7L,IAAI,CAAC;IACzC,CAAC,MAAM;MACHD,EAAE,CAACG,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;IACxB;IACA,OAAO6L,KAAK;EAChB,CAAC;AACL;AACA;AACA;AACA;AACA;AAAI,MAAMI,kBAAkB,GAAIC,KAAK,IAAGA,KAAK,KAAK,OAAO,GAAG,MAAM,GAAGA,KAAK,KAAK,KAAK,GAAG,OAAO,GAAG,QAAQ;AACzG;AACA;AACA;AACA;AAAI,MAAMC,cAAc,GAAGA,CAACD,KAAK,EAAE5D,KAAK,EAAEC,GAAG,KAAG2D,KAAK,KAAK,OAAO,GAAG5D,KAAK,GAAG4D,KAAK,KAAK,KAAK,GAAG3D,GAAG,GAAG,CAACD,KAAK,GAAGC,GAAG,IAAI,CAAC;AACrH;AACA;AACA;AACA;AAAI,MAAM6D,MAAM,GAAGA,CAACF,KAAK,EAAEG,IAAI,EAAEC,KAAK,EAAEC,GAAG,KAAG;EAC1C,MAAMC,KAAK,GAAGD,GAAG,GAAG,MAAM,GAAG,OAAO;EACpC,OAAOL,KAAK,KAAKM,KAAK,GAAGF,KAAK,GAAGJ,KAAK,KAAK,QAAQ,GAAG,CAACG,IAAI,GAAGC,KAAK,IAAI,CAAC,GAAGD,IAAI;AACnF,CAAC;AACD;AACA;AACA;AACA;AAAI,SAASI,gCAAgCA,CAACC,IAAI,EAAEC,MAAM,EAAEC,kBAAkB,EAAE;EAC5E,MAAMC,UAAU,GAAGF,MAAM,CAAClM,MAAM;EAChC,IAAI6H,KAAK,GAAG,CAAC;EACb,IAAIwE,KAAK,GAAGD,UAAU;EACtB,IAAIH,IAAI,CAACK,OAAO,EAAE;IACd,MAAM;MAAEC,MAAM;MAAGC;IAAS,CAAC,GAAGP,IAAI;IAClC,MAAMQ,IAAI,GAAGF,MAAM,CAACE,IAAI;IACxB,MAAM;MAAEnG,GAAG;MAAGC,GAAG;MAAGmG,UAAU;MAAGC;IAAY,CAAC,GAAGJ,MAAM,CAACK,aAAa,CAAC,CAAC;IACvE,IAAIF,UAAU,EAAE;MACZ7E,KAAK,GAAGQ,WAAW,CAACnE,IAAI,CAACoC,GAAG;MAAC;MAC7BwC,YAAY,CAAC0D,OAAO,EAAEC,IAAI,EAAEnG,GAAG,CAAC,CAACsC,EAAE;MAAE;MACrCuD,kBAAkB,GAAGC,UAAU,GAAGtD,YAAY,CAACoD,MAAM,EAAEO,IAAI,EAAEF,MAAM,CAACM,gBAAgB,CAACvG,GAAG,CAAC,CAAC,CAACsC,EAAE,CAAC,EAAE,CAAC,EAAEwD,UAAU,GAAG,CAAC,CAAC;IACtH;IACA,IAAIO,UAAU,EAAE;MACZN,KAAK,GAAGhE,WAAW,CAACnE,IAAI,CAACqC,GAAG;MAAC;MAC7BuC,YAAY,CAAC0D,OAAO,EAAED,MAAM,CAACE,IAAI,EAAElG,GAAG,EAAE,IAAI,CAAC,CAACoC,EAAE,GAAG,CAAC;MAAE;MACtDwD,kBAAkB,GAAG,CAAC,GAAGrD,YAAY,CAACoD,MAAM,EAAEO,IAAI,EAAEF,MAAM,CAACM,gBAAgB,CAACtG,GAAG,CAAC,EAAE,IAAI,CAAC,CAACoC,EAAE,GAAG,CAAC,CAAC,EAAEd,KAAK,EAAEuE,UAAU,CAAC,GAAGvE,KAAK;IAC/H,CAAC,MAAM;MACHwE,KAAK,GAAGD,UAAU,GAAGvE,KAAK;IAC9B;EACJ;EACA,OAAO;IACHA,KAAK;IACLwE;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASS,mBAAmBA,CAACb,IAAI,EAAE;EACnC,MAAM;IAAEc,MAAM;IAAGC,MAAM;IAAGC;EAAc,CAAC,GAAGhB,IAAI;EAChD,MAAMiB,SAAS,GAAG;IACdC,IAAI,EAAEJ,MAAM,CAACzG,GAAG;IAChB8G,IAAI,EAAEL,MAAM,CAACxG,GAAG;IAChB8G,IAAI,EAAEL,MAAM,CAAC1G,GAAG;IAChBgH,IAAI,EAAEN,MAAM,CAACzG;EACjB,CAAC;EACD,IAAI,CAAC0G,YAAY,EAAE;IACfhB,IAAI,CAACgB,YAAY,GAAGC,SAAS;IAC7B,OAAO,IAAI;EACf;EACA,MAAMK,OAAO,GAAGN,YAAY,CAACE,IAAI,KAAKJ,MAAM,CAACzG,GAAG,IAAI2G,YAAY,CAACG,IAAI,KAAKL,MAAM,CAACxG,GAAG,IAAI0G,YAAY,CAACI,IAAI,KAAKL,MAAM,CAAC1G,GAAG,IAAI2G,YAAY,CAACK,IAAI,KAAKN,MAAM,CAACzG,GAAG;EAC5JnI,MAAM,CAACoP,MAAM,CAACP,YAAY,EAAEC,SAAS,CAAC;EACtC,OAAOK,OAAO;AAClB;AAEA,MAAME,MAAM,GAAIC,CAAC,IAAGA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC;AACtC,MAAMC,SAAS,GAAGA,CAACD,CAAC,EAAE1F,CAAC,EAAEnB,CAAC,KAAG,EAAE3C,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIqI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAGxJ,IAAI,CAAC0J,GAAG,CAAC,CAACF,CAAC,GAAG1F,CAAC,IAAI7D,GAAG,GAAG0C,CAAC,CAAC,CAAC;AACxF,MAAMgH,UAAU,GAAGA,CAACH,CAAC,EAAE1F,CAAC,EAAEnB,CAAC,KAAG3C,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAGqI,CAAC,CAAC,GAAGxJ,IAAI,CAAC0J,GAAG,CAAC,CAACF,CAAC,GAAG1F,CAAC,IAAI7D,GAAG,GAAG0C,CAAC,CAAC,GAAG,CAAC;AACpF;AACA;AACA;AACA;AACA;AAAI,MAAMiH,OAAO,GAAG;EAChBC,MAAM,EAAGL,CAAC,IAAGA,CAAC;EACdM,UAAU,EAAGN,CAAC,IAAGA,CAAC,GAAGA,CAAC;EACtBO,WAAW,EAAGP,CAAC,IAAG,CAACA,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;EAC9BQ,aAAa,EAAGR,CAAC,IAAG,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,IAAI,EAAEA,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7ES,WAAW,EAAGT,CAAC,IAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EAC3BU,YAAY,EAAGV,CAAC,IAAG,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAG,CAAC;EACvCW,cAAc,EAAGX,CAAC,IAAG,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EACpFY,WAAW,EAAGZ,CAAC,IAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EAC/Ba,YAAY,EAAGb,CAAC,IAAG,EAAE,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EAC9Cc,cAAc,EAAGd,CAAC,IAAG,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EAC7Fe,WAAW,EAAGf,CAAC,IAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EACnCgB,YAAY,EAAGhB,CAAC,IAAG,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC;EAC/CiB,cAAc,EAAGjB,CAAC,IAAG,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EACpGkB,UAAU,EAAGlB,CAAC,IAAG,CAACxJ,IAAI,CAAC2K,GAAG,CAACnB,CAAC,GAAGlJ,OAAO,CAAC,GAAG,CAAC;EAC3CsK,WAAW,EAAGpB,CAAC,IAAGxJ,IAAI,CAAC0J,GAAG,CAACF,CAAC,GAAGlJ,OAAO,CAAC;EACvCuK,aAAa,EAAGrB,CAAC,IAAG,CAAC,GAAG,IAAIxJ,IAAI,CAAC2K,GAAG,CAAC5K,EAAE,GAAGyJ,CAAC,CAAC,GAAG,CAAC,CAAC;EACjDsB,UAAU,EAAGtB,CAAC,IAAGA,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGxJ,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIqI,CAAC,GAAG,CAAC,CAAC,CAAC;EACxDuB,WAAW,EAAGvB,CAAC,IAAGA,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAACxJ,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAGqI,CAAC,CAAC,GAAG,CAAC;EACzDwB,aAAa,EAAGxB,CAAC,IAAGD,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGxJ,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIqI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAACxJ,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAIqI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAC/HyB,UAAU,EAAGzB,CAAC,IAAGA,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,EAAExJ,IAAI,CAACyB,IAAI,CAAC,CAAC,GAAG+H,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,CAAC;EACzD0B,WAAW,EAAG1B,CAAC,IAAGxJ,IAAI,CAACyB,IAAI,CAAC,CAAC,GAAG,CAAC+H,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC;EAC7C2B,aAAa,EAAG3B,CAAC,IAAG,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAIxJ,IAAI,CAACyB,IAAI,CAAC,CAAC,GAAG+H,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAIxJ,IAAI,CAACyB,IAAI,CAAC,CAAC,GAAG,CAAC+H,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,GAAG,CAAC,CAAC;EAChH4B,aAAa,EAAG5B,CAAC,IAAGD,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGC,SAAS,CAACD,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC;EAC5D6B,cAAc,EAAG7B,CAAC,IAAGD,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGG,UAAU,CAACH,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC;EAC9D8B,gBAAgBA,CAAE9B,CAAC,EAAE;IACjB,MAAM1F,CAAC,GAAG,MAAM;IAChB,MAAMnB,CAAC,GAAG,IAAI;IACd,OAAO4G,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGC,SAAS,CAACD,CAAC,GAAG,CAAC,EAAE1F,CAAC,EAAEnB,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGgH,UAAU,CAACH,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE1F,CAAC,EAAEnB,CAAC,CAAC;EAC3G,CAAC;EACD4I,UAAUA,CAAE/B,CAAC,EAAE;IACX,MAAM1F,CAAC,GAAG,OAAO;IACjB,OAAO0F,CAAC,GAAGA,CAAC,IAAI,CAAC1F,CAAC,GAAG,CAAC,IAAI0F,CAAC,GAAG1F,CAAC,CAAC;EACpC,CAAC;EACD0H,WAAWA,CAAEhC,CAAC,EAAE;IACZ,MAAM1F,CAAC,GAAG,OAAO;IACjB,OAAO,CAAC0F,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC1F,CAAC,GAAG,CAAC,IAAI0F,CAAC,GAAG1F,CAAC,CAAC,GAAG,CAAC;EAC/C,CAAC;EACD2H,aAAaA,CAAEjC,CAAC,EAAE;IACd,IAAI1F,CAAC,GAAG,OAAO;IACf,IAAI,CAAC0F,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE;MAChB,OAAO,GAAG,IAAIA,CAAC,GAAGA,CAAC,IAAI,CAAC,CAAC1F,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI0F,CAAC,GAAG1F,CAAC,CAAC,CAAC;IACvD;IACA,OAAO,GAAG,IAAI,CAAC0F,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC1F,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI0F,CAAC,GAAG1F,CAAC,CAAC,GAAG,CAAC,CAAC;EAClE,CAAC;EACD4H,YAAY,EAAGlC,CAAC,IAAG,CAAC,GAAGI,OAAO,CAAC+B,aAAa,CAAC,CAAC,GAAGnC,CAAC,CAAC;EACnDmC,aAAaA,CAAEnC,CAAC,EAAE;IACd,MAAMoC,CAAC,GAAG,MAAM;IAChB,MAAMC,CAAC,GAAG,IAAI;IACd,IAAIrC,CAAC,GAAG,CAAC,GAAGqC,CAAC,EAAE;MACX,OAAOD,CAAC,GAAGpC,CAAC,GAAGA,CAAC;IACpB;IACA,IAAIA,CAAC,GAAG,CAAC,GAAGqC,CAAC,EAAE;MACX,OAAOD,CAAC,IAAIpC,CAAC,IAAI,GAAG,GAAGqC,CAAC,CAAC,GAAGrC,CAAC,GAAG,IAAI;IACxC;IACA,IAAIA,CAAC,GAAG,GAAG,GAAGqC,CAAC,EAAE;MACb,OAAOD,CAAC,IAAIpC,CAAC,IAAI,IAAI,GAAGqC,CAAC,CAAC,GAAGrC,CAAC,GAAG,MAAM;IAC3C;IACA,OAAOoC,CAAC,IAAIpC,CAAC,IAAI,KAAK,GAAGqC,CAAC,CAAC,GAAGrC,CAAC,GAAG,QAAQ;EAC9C,CAAC;EACDsC,eAAe,EAAGtC,CAAC,IAAGA,CAAC,GAAG,GAAG,GAAGI,OAAO,CAAC8B,YAAY,CAAClC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGI,OAAO,CAAC+B,aAAa,CAACnC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG;AACjH,CAAC;AAED,SAASuC,mBAAmBA,CAACjS,KAAK,EAAE;EAChC,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACpC,MAAMG,IAAI,GAAGH,KAAK,CAACM,QAAQ,CAAC,CAAC;IAC7B,OAAOH,IAAI,KAAK,wBAAwB,IAAIA,IAAI,KAAK,yBAAyB;EAClF;EACA,OAAO,KAAK;AAChB;AACA,SAAS+R,KAAKA,CAAClS,KAAK,EAAE;EAClB,OAAOiS,mBAAmB,CAACjS,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAIL,KAAK,CAACK,KAAK,CAAC;AAChE;AACA,SAASmS,aAAaA,CAACnS,KAAK,EAAE;EAC1B,OAAOiS,mBAAmB,CAACjS,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAIL,KAAK,CAACK,KAAK,CAAC,CAACoS,QAAQ,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CAACC,SAAS,CAAC,CAAC;AACtG;AAEA,MAAMC,OAAO,GAAG,CACZ,GAAG,EACH,GAAG,EACH,aAAa,EACb,QAAQ,EACR,SAAS,CACZ;AACD,MAAMC,MAAM,GAAG,CACX,OAAO,EACP,aAAa,EACb,iBAAiB,CACpB;AACD,SAASC,uBAAuBA,CAACC,QAAQ,EAAE;EACvCA,QAAQ,CAACpG,GAAG,CAAC,WAAW,EAAE;IACtBc,KAAK,EAAEnJ,SAAS;IAChB0O,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,cAAc;IACtBtR,EAAE,EAAE2C,SAAS;IACbuI,IAAI,EAAEvI,SAAS;IACf4O,IAAI,EAAE5O,SAAS;IACf6O,EAAE,EAAE7O,SAAS;IACb9D,IAAI,EAAE8D;EACV,CAAC,CAAC;EACFyO,QAAQ,CAACK,QAAQ,CAAC,WAAW,EAAE;IAC3BC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAGC,IAAI,IAAGA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK;EACpF,CAAC,CAAC;EACFT,QAAQ,CAACpG,GAAG,CAAC,YAAY,EAAE;IACvBkG,MAAM,EAAE;MACJrS,IAAI,EAAE,OAAO;MACbiT,UAAU,EAAEZ;IAChB,CAAC;IACDD,OAAO,EAAE;MACLpS,IAAI,EAAE,QAAQ;MACdiT,UAAU,EAAEb;IAChB;EACJ,CAAC,CAAC;EACFG,QAAQ,CAACK,QAAQ,CAAC,YAAY,EAAE;IAC5BC,SAAS,EAAE;EACf,CAAC,CAAC;EACFN,QAAQ,CAACpG,GAAG,CAAC,aAAa,EAAE;IACxB+G,MAAM,EAAE;MACJC,SAAS,EAAE;QACPX,QAAQ,EAAE;MACd;IACJ,CAAC;IACDY,MAAM,EAAE;MACJD,SAAS,EAAE;QACPX,QAAQ,EAAE;MACd;IACJ,CAAC;IACDa,IAAI,EAAE;MACFC,UAAU,EAAE;QACRjB,MAAM,EAAE;UACJhG,IAAI,EAAE;QACV,CAAC;QACDkH,OAAO,EAAE;UACLvT,IAAI,EAAE,SAAS;UACfwS,QAAQ,EAAE;QACd;MACJ;IACJ,CAAC;IACDgB,IAAI,EAAE;MACFF,UAAU,EAAE;QACRjB,MAAM,EAAE;UACJM,EAAE,EAAE;QACR,CAAC;QACDY,OAAO,EAAE;UACLvT,IAAI,EAAE,SAAS;UACfyS,MAAM,EAAE,QAAQ;UAChBtR,EAAE,EAAG+C,CAAC,IAAGA,CAAC,GAAG;QACjB;MACJ;IACJ;EACJ,CAAC,CAAC;AACN;AAEA,SAASuP,oBAAoBA,CAAClB,QAAQ,EAAE;EACpCA,QAAQ,CAACpG,GAAG,CAAC,QAAQ,EAAE;IACnBuH,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE;MACLC,GAAG,EAAE,CAAC;MACNlG,KAAK,EAAE,CAAC;MACRmG,MAAM,EAAE,CAAC;MACTpG,IAAI,EAAE;IACV;EACJ,CAAC,CAAC;AACN;AAEA,MAAMqG,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B,SAASC,eAAeA,CAACC,MAAM,EAAEhR,OAAO,EAAE;EACtCA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAMiR,QAAQ,GAAGD,MAAM,GAAGE,IAAI,CAACC,SAAS,CAACnR,OAAO,CAAC;EACjD,IAAIoR,SAAS,GAAGP,SAAS,CAACQ,GAAG,CAACJ,QAAQ,CAAC;EACvC,IAAI,CAACG,SAAS,EAAE;IACZA,SAAS,GAAG,IAAIE,IAAI,CAACC,YAAY,CAACP,MAAM,EAAEhR,OAAO,CAAC;IAClD6Q,SAAS,CAAC3H,GAAG,CAAC+H,QAAQ,EAAEG,SAAS,CAAC;EACtC;EACA,OAAOA,SAAS;AACpB;AACA,SAASI,YAAYA,CAACC,GAAG,EAAET,MAAM,EAAEhR,OAAO,EAAE;EACxC,OAAO+Q,eAAe,CAACC,MAAM,EAAEhR,OAAO,CAAC,CAAC0R,MAAM,CAACD,GAAG,CAAC;AACvD;AAEA,MAAME,UAAU,GAAG;EAClB5J,MAAMA,CAAEnL,KAAK,EAAE;IACR,OAAOC,OAAO,CAACD,KAAK,CAAC,GAAIA,KAAK,GAAG,EAAE,GAAGA,KAAK;EAC/C,CAAC;EACJgV,OAAOA,CAAEC,SAAS,EAAEzS,KAAK,EAAE0S,KAAK,EAAE;IAC3B,IAAID,SAAS,KAAK,CAAC,EAAE;MACjB,OAAO,GAAG;IACd;IACA,MAAMb,MAAM,GAAG,IAAI,CAACe,KAAK,CAAC/R,OAAO,CAACgR,MAAM;IACxC,IAAIgB,QAAQ;IACZ,IAAIC,KAAK,GAAGJ,SAAS;IACrB,IAAIC,KAAK,CAAClT,MAAM,GAAG,CAAC,EAAE;MAClB,MAAMsT,OAAO,GAAGpP,IAAI,CAACqC,GAAG,CAACrC,IAAI,CAACa,GAAG,CAACmO,KAAK,CAAC,CAAC,CAAC,CAAClV,KAAK,CAAC,EAAEkG,IAAI,CAACa,GAAG,CAACmO,KAAK,CAACA,KAAK,CAAClT,MAAM,GAAG,CAAC,CAAC,CAAChC,KAAK,CAAC,CAAC;MAC3F,IAAIsV,OAAO,GAAG,IAAI,IAAIA,OAAO,GAAG,KAAK,EAAE;QACnCF,QAAQ,GAAG,YAAY;MAC3B;MACAC,KAAK,GAAGE,cAAc,CAACN,SAAS,EAAEC,KAAK,CAAC;IAC5C;IACA,MAAMM,QAAQ,GAAG7O,KAAK,CAACT,IAAI,CAACa,GAAG,CAACsO,KAAK,CAAC,CAAC;IACvC,MAAMI,UAAU,GAAGzN,KAAK,CAACwN,QAAQ,CAAC,GAAG,CAAC,GAAGtP,IAAI,CAACqC,GAAG,CAACrC,IAAI,CAACoC,GAAG,CAAC,CAAC,CAAC,GAAGpC,IAAI,CAACoB,KAAK,CAACkO,QAAQ,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7F,MAAMpS,OAAO,GAAG;MACZgS,QAAQ;MACRM,qBAAqB,EAAED,UAAU;MACjCE,qBAAqB,EAAEF;IAC3B,CAAC;IACDrV,MAAM,CAACoP,MAAM,CAACpM,OAAO,EAAE,IAAI,CAACA,OAAO,CAAC8R,KAAK,CAACJ,MAAM,CAAC;IACjD,OAAOF,YAAY,CAACK,SAAS,EAAEb,MAAM,EAAEhR,OAAO,CAAC;EACnD,CAAC;EACJwS,WAAWA,CAAEX,SAAS,EAAEzS,KAAK,EAAE0S,KAAK,EAAE;IAC/B,IAAID,SAAS,KAAK,CAAC,EAAE;MACjB,OAAO,GAAG;IACd;IACA,MAAMY,MAAM,GAAGX,KAAK,CAAC1S,KAAK,CAAC,CAACsT,WAAW,IAAIb,SAAS,GAAG/O,IAAI,CAACmB,GAAG,CAAC,EAAE,EAAEnB,IAAI,CAACoB,KAAK,CAACX,KAAK,CAACsO,SAAS,CAAC,CAAC,CAAC;IACjG,IAAI,CACA,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,EAAE,EACF,EAAE,CACL,CAACc,QAAQ,CAACF,MAAM,CAAC,IAAIrT,KAAK,GAAG,GAAG,GAAG0S,KAAK,CAAClT,MAAM,EAAE;MAC9C,OAAO+S,UAAU,CAACC,OAAO,CAACzU,IAAI,CAAC,IAAI,EAAE0U,SAAS,EAAEzS,KAAK,EAAE0S,KAAK,CAAC;IACjE;IACA,OAAO,EAAE;EACb;AACJ,CAAC;AACD,SAASK,cAAcA,CAACN,SAAS,EAAEC,KAAK,EAAE;EACtC,IAAIG,KAAK,GAAGH,KAAK,CAAClT,MAAM,GAAG,CAAC,GAAGkT,KAAK,CAAC,CAAC,CAAC,CAAClV,KAAK,GAAGkV,KAAK,CAAC,CAAC,CAAC,CAAClV,KAAK,GAAGkV,KAAK,CAAC,CAAC,CAAC,CAAClV,KAAK,GAAGkV,KAAK,CAAC,CAAC,CAAC,CAAClV,KAAK;EAChG,IAAIkG,IAAI,CAACa,GAAG,CAACsO,KAAK,CAAC,IAAI,CAAC,IAAIJ,SAAS,KAAK/O,IAAI,CAACoB,KAAK,CAAC2N,SAAS,CAAC,EAAE;IAC7DI,KAAK,GAAGJ,SAAS,GAAG/O,IAAI,CAACoB,KAAK,CAAC2N,SAAS,CAAC;EAC7C;EACA,OAAOI,KAAK;AAChB;AACC,IAAIW,KAAK,GAAG;EACTjB;AACJ,CAAC;AAED,SAASkB,kBAAkBA,CAACvD,QAAQ,EAAE;EAClCA,QAAQ,CAACpG,GAAG,CAAC,OAAO,EAAE;IAClB4J,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,KAAK;IACbvU,OAAO,EAAE,KAAK;IACdwU,WAAW,EAAE,KAAK;IACzBC,MAAM,EAAE,OAAO;IACRC,IAAI,EAAE,IAAI;IACjBC,KAAK,EAAE,CAAC;IACDC,IAAI,EAAE;MACFN,OAAO,EAAE,IAAI;MACbO,SAAS,EAAE,CAAC;MACZC,eAAe,EAAE,IAAI;MACrBC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAEA,CAACC,IAAI,EAAE1T,OAAO,KAAGA,OAAO,CAACqT,SAAS;MAC7CM,SAAS,EAAEA,CAACD,IAAI,EAAE1T,OAAO,KAAGA,OAAO,CAAC8O,KAAK;MACzCiE,MAAM,EAAE;IACZ,CAAC;IACDa,MAAM,EAAE;MACJd,OAAO,EAAE,IAAI;MACbe,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,GAAG;MACfC,KAAK,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACHlB,OAAO,EAAE,KAAK;MACdmB,IAAI,EAAE,EAAE;MACRvD,OAAO,EAAE;QACLC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE;MACZ;IACJ,CAAC;IACDkB,KAAK,EAAE;MACHoC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,KAAK;MACbC,eAAe,EAAE,CAAC;MAClBC,eAAe,EAAE,EAAE;MACnB5D,OAAO,EAAE,CAAC;MACVoC,OAAO,EAAE,IAAI;MACbyB,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,CAAC;MAClBC,WAAW,EAAE,CAAC;MACdxW,QAAQ,EAAE2U,KAAK,CAACjB,UAAU,CAAC5J,MAAM;MACjC2M,KAAK,EAAE,CAAC,CAAC;MACTC,KAAK,EAAE,CAAC,CAAC;MACTtK,KAAK,EAAE,QAAQ;MACfuK,UAAU,EAAE,MAAM;MAClBC,iBAAiB,EAAE,KAAK;MACxBC,aAAa,EAAE,2BAA2B;MAC1CC,eAAe,EAAE;IACrB;EACJ,CAAC,CAAC;EACFzF,QAAQ,CAAC0F,KAAK,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,CAAC;EACnD1F,QAAQ,CAAC0F,KAAK,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,EAAE,aAAa,CAAC;EACxD1F,QAAQ,CAAC0F,KAAK,CAAC,cAAc,EAAE,OAAO,EAAE,EAAE,EAAE,aAAa,CAAC;EAC1D1F,QAAQ,CAAC0F,KAAK,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,CAAC;EACnD1F,QAAQ,CAACK,QAAQ,CAAC,OAAO,EAAE;IACvBC,SAAS,EAAE,KAAK;IAChBE,WAAW,EAAGC,IAAI,IAAG,CAACA,IAAI,CAACkF,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAClF,IAAI,CAACkF,UAAU,CAAC,OAAO,CAAC,IAAIlF,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,QAAQ;IACxHF,UAAU,EAAGE,IAAI,IAAGA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,gBAAgB,IAAIA,IAAI,KAAK;EACvF,CAAC,CAAC;EACFT,QAAQ,CAACK,QAAQ,CAAC,QAAQ,EAAE;IACxBC,SAAS,EAAE;EACf,CAAC,CAAC;EACFN,QAAQ,CAACK,QAAQ,CAAC,aAAa,EAAE;IAC7BG,WAAW,EAAGC,IAAI,IAAGA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,UAAU;IACtEF,UAAU,EAAGE,IAAI,IAAGA,IAAI,KAAK;EACjC,CAAC,CAAC;AACN;AAEA,MAAMmF,SAAS,GAAGlY,MAAM,CAACyC,MAAM,CAAC,IAAI,CAAC;AACrC,MAAM0V,WAAW,GAAGnY,MAAM,CAACyC,MAAM,CAAC,IAAI,CAAC;AACtC,SAAS2V,UAAUA,CAACC,IAAI,EAAExV,GAAG,EAAE;EAC5B,IAAI,CAACA,GAAG,EAAE;IACN,OAAOwV,IAAI;EACf;EACA,MAAM1W,IAAI,GAAGkB,GAAG,CAAC0B,KAAK,CAAC,GAAG,CAAC;EAC3B,KAAI,IAAI9C,CAAC,GAAG,CAAC,EAAEkG,CAAC,GAAGhG,IAAI,CAACC,MAAM,EAAEH,CAAC,GAAGkG,CAAC,EAAE,EAAElG,CAAC,EAAC;IACvC,MAAMkB,CAAC,GAAGhB,IAAI,CAACF,CAAC,CAAC;IACjB4W,IAAI,GAAGA,IAAI,CAAC1V,CAAC,CAAC,KAAK0V,IAAI,CAAC1V,CAAC,CAAC,GAAG3C,MAAM,CAACyC,MAAM,CAAC,IAAI,CAAC,CAAC;EACrD;EACA,OAAO4V,IAAI;AACf;AACA,SAASnM,GAAGA,CAACoM,IAAI,EAAE3U,KAAK,EAAEoH,MAAM,EAAE;EAC9B,IAAI,OAAOpH,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAOR,KAAK,CAACiV,UAAU,CAACE,IAAI,EAAE3U,KAAK,CAAC,EAAEoH,MAAM,CAAC;EACjD;EACA,OAAO5H,KAAK,CAACiV,UAAU,CAACE,IAAI,EAAE,EAAE,CAAC,EAAE3U,KAAK,CAAC;AAC7C;AACC,MAAM4U,QAAQ,CAAC;EACZC,WAAWA,CAACC,YAAY,EAAEC,SAAS,EAAC;IAChC,IAAI,CAACxF,SAAS,GAAGrP,SAAS;IAC1B,IAAI,CAAC8U,eAAe,GAAG,iBAAiB;IACxC,IAAI,CAACC,WAAW,GAAG,iBAAiB;IACpC,IAAI,CAAC9G,KAAK,GAAG,MAAM;IACnB,IAAI,CAAC+G,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,gBAAgB,GAAIC,OAAO,IAAGA,OAAO,CAAChE,KAAK,CAACiE,QAAQ,CAACC,mBAAmB,CAAC,CAAC;IAC/E,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,MAAM,GAAG,CACV,WAAW,EACX,UAAU,EACV,OAAO,EACP,YAAY,EACZ,WAAW,CACd;IACD,IAAI,CAACC,IAAI,GAAG;MACRC,MAAM,EAAE,oDAAoD;MAC5D7T,IAAI,EAAE,EAAE;MACR8T,KAAK,EAAE,QAAQ;MACfC,UAAU,EAAE,GAAG;MACfC,MAAM,EAAE;IACZ,CAAC;IACD,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,oBAAoB,GAAG,CAACC,GAAG,EAAE3W,OAAO,KAAG+O,aAAa,CAAC/O,OAAO,CAAC2V,eAAe,CAAC;IAClF,IAAI,CAACiB,gBAAgB,GAAG,CAACD,GAAG,EAAE3W,OAAO,KAAG+O,aAAa,CAAC/O,OAAO,CAAC4V,WAAW,CAAC;IAC1E,IAAI,CAACiB,UAAU,GAAG,CAACF,GAAG,EAAE3W,OAAO,KAAG+O,aAAa,CAAC/O,OAAO,CAAC8O,KAAK,CAAC;IAC9D,IAAI,CAACgI,SAAS,GAAG,GAAG;IACpB,IAAI,CAACC,WAAW,GAAG;MACfC,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,IAAI;MACfC,gBAAgB,EAAE;IACtB,CAAC;IACD,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,KAAK,GAAG5W,SAAS;IACtB,IAAI,CAAC6W,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACjI,QAAQ,CAAC8F,YAAY,CAAC;IAC3B,IAAI,CAACpX,KAAK,CAACqX,SAAS,CAAC;EACzB;EACHxM,GAAGA,CAACvI,KAAK,EAAEoH,MAAM,EAAE;IACZ,OAAOmB,GAAG,CAAC,IAAI,EAAEvI,KAAK,EAAEoH,MAAM,CAAC;EACnC;EACHsJ,GAAGA,CAAC1Q,KAAK,EAAE;IACJ,OAAOyU,UAAU,CAAC,IAAI,EAAEzU,KAAK,CAAC;EAClC;EACHgP,QAAQA,CAAChP,KAAK,EAAEoH,MAAM,EAAE;IACjB,OAAOmB,GAAG,CAACiM,WAAW,EAAExU,KAAK,EAAEoH,MAAM,CAAC;EAC1C;EACA8P,QAAQA,CAAClX,KAAK,EAAEoH,MAAM,EAAE;IACpB,OAAOmB,GAAG,CAACgM,SAAS,EAAEvU,KAAK,EAAEoH,MAAM,CAAC;EACxC;EACHiN,KAAKA,CAACrU,KAAK,EAAEoP,IAAI,EAAE+H,WAAW,EAAEC,UAAU,EAAE;IACrC,MAAMC,WAAW,GAAG5C,UAAU,CAAC,IAAI,EAAEzU,KAAK,CAAC;IAC3C,MAAMsX,iBAAiB,GAAG7C,UAAU,CAAC,IAAI,EAAE0C,WAAW,CAAC;IACvD,MAAMI,WAAW,GAAG,GAAG,GAAGnI,IAAI;IAC9B/S,MAAM,CAACmb,gBAAgB,CAACH,WAAW,EAAE;MACjC,CAACE,WAAW,GAAG;QACXtb,KAAK,EAAEob,WAAW,CAACjI,IAAI,CAAC;QACxBqI,QAAQ,EAAE;MACd,CAAC;MACD,CAACrI,IAAI,GAAG;QACJxH,UAAU,EAAE,IAAI;QAChB8I,GAAGA,CAAA,EAAI;UACH,MAAMgH,KAAK,GAAG,IAAI,CAACH,WAAW,CAAC;UAC/B,MAAM1Y,MAAM,GAAGyY,iBAAiB,CAACF,UAAU,CAAC;UAC5C,IAAI1a,QAAQ,CAACgb,KAAK,CAAC,EAAE;YACjB,OAAOrb,MAAM,CAACoP,MAAM,CAAC,CAAC,CAAC,EAAE5M,MAAM,EAAE6Y,KAAK,CAAC;UAC3C;UACA,OAAO1a,cAAc,CAAC0a,KAAK,EAAE7Y,MAAM,CAAC;QACxC,CAAC;QACD0J,GAAGA,CAAEtM,KAAK,EAAE;UACR,IAAI,CAACsb,WAAW,CAAC,GAAGtb,KAAK;QAC7B;MACJ;IACJ,CAAC,CAAC;EACN;EACAyB,KAAKA,CAACia,QAAQ,EAAE;IACZA,QAAQ,CAAC9P,OAAO,CAAEnK,KAAK,IAAGA,KAAK,CAAC,IAAI,CAAC,CAAC;EAC1C;AACJ;AACA,IAAIiR,QAAQ,GAAG,eAAgB,IAAIiG,QAAQ,CAAC;EACxCzF,WAAW,EAAGC,IAAI,IAAG,CAACA,IAAI,CAACkF,UAAU,CAAC,IAAI,CAAC;EAC3CpF,UAAU,EAAGE,IAAI,IAAGA,IAAI,KAAK,QAAQ;EACrC0G,KAAK,EAAE;IACH7G,SAAS,EAAE;EACf,CAAC;EACDmH,WAAW,EAAE;IACTjH,WAAW,EAAE,KAAK;IAClBD,UAAU,EAAE;EAChB;AACJ,CAAC,EAAE,CACCR,uBAAuB,EACvBmB,oBAAoB,EACpBqC,kBAAkB,CACrB,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS0F,YAAYA,CAACnC,IAAI,EAAE;EAC5B,IAAI,CAACA,IAAI,IAAIzZ,aAAa,CAACyZ,IAAI,CAAC5T,IAAI,CAAC,IAAI7F,aAAa,CAACyZ,IAAI,CAACC,MAAM,CAAC,EAAE;IACjE,OAAO,IAAI;EACf;EACA,OAAO,CAACD,IAAI,CAACE,KAAK,GAAGF,IAAI,CAACE,KAAK,GAAG,GAAG,GAAG,EAAE,KAAKF,IAAI,CAACI,MAAM,GAAGJ,IAAI,CAACI,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGJ,IAAI,CAAC5T,IAAI,GAAG,KAAK,GAAG4T,IAAI,CAACC,MAAM;AAC1H;AACA;AACA;AACA;AAAI,SAASmC,YAAYA,CAAC7B,GAAG,EAAE8B,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEC,MAAM,EAAE;EACtD,IAAIC,SAAS,GAAGJ,IAAI,CAACG,MAAM,CAAC;EAC5B,IAAI,CAACC,SAAS,EAAE;IACZA,SAAS,GAAGJ,IAAI,CAACG,MAAM,CAAC,GAAGjC,GAAG,CAACmC,WAAW,CAACF,MAAM,CAAC,CAAC7E,KAAK;IACxD2E,EAAE,CAAChX,IAAI,CAACkX,MAAM,CAAC;EACnB;EACA,IAAIC,SAAS,GAAGF,OAAO,EAAE;IACrBA,OAAO,GAAGE,SAAS;EACvB;EACA,OAAOF,OAAO;AAClB;AACA;AACA;AACA,GAFA,CAEI;AACJ,SAASI,YAAYA,CAACpC,GAAG,EAAEP,IAAI,EAAE4C,aAAa,EAAEC,KAAK,EAAE;EACnDA,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC;EACnB,IAAIR,IAAI,GAAGQ,KAAK,CAACR,IAAI,GAAGQ,KAAK,CAACR,IAAI,IAAI,CAAC,CAAC;EACxC,IAAIC,EAAE,GAAGO,KAAK,CAACC,cAAc,GAAGD,KAAK,CAACC,cAAc,IAAI,EAAE;EAC1D,IAAID,KAAK,CAAC7C,IAAI,KAAKA,IAAI,EAAE;IACrBqC,IAAI,GAAGQ,KAAK,CAACR,IAAI,GAAG,CAAC,CAAC;IACtBC,EAAE,GAAGO,KAAK,CAACC,cAAc,GAAG,EAAE;IAC9BD,KAAK,CAAC7C,IAAI,GAAGA,IAAI;EACrB;EACAO,GAAG,CAACwC,IAAI,CAAC,CAAC;EACVxC,GAAG,CAACP,IAAI,GAAGA,IAAI;EACf,IAAIuC,OAAO,GAAG,CAAC;EACf,MAAM3Z,IAAI,GAAGga,aAAa,CAACpa,MAAM;EACjC,IAAIH,CAAC,EAAE2a,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAEC,WAAW;EAClC,KAAI9a,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,IAAI,EAAEP,CAAC,EAAE,EAAC;IACrB6a,KAAK,GAAGN,aAAa,CAACva,CAAC,CAAC;IACxB;IACA,IAAI6a,KAAK,KAAKzY,SAAS,IAAIyY,KAAK,KAAK,IAAI,IAAI,CAACzc,OAAO,CAACyc,KAAK,CAAC,EAAE;MAC1DX,OAAO,GAAGH,YAAY,CAAC7B,GAAG,EAAE8B,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEW,KAAK,CAAC;IACzD,CAAC,MAAM,IAAIzc,OAAO,CAACyc,KAAK,CAAC,EAAE;MACvB;MACA;MACA,KAAIF,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAGC,KAAK,CAAC1a,MAAM,EAAEwa,CAAC,GAAGC,IAAI,EAAED,CAAC,EAAE,EAAC;QAC1CG,WAAW,GAAGD,KAAK,CAACF,CAAC,CAAC;QACtB;QACA,IAAIG,WAAW,KAAK1Y,SAAS,IAAI0Y,WAAW,KAAK,IAAI,IAAI,CAAC1c,OAAO,CAAC0c,WAAW,CAAC,EAAE;UAC5EZ,OAAO,GAAGH,YAAY,CAAC7B,GAAG,EAAE8B,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEY,WAAW,CAAC;QAC/D;MACJ;IACJ;EACJ;EACA5C,GAAG,CAAC6C,OAAO,CAAC,CAAC;EACb,MAAMC,KAAK,GAAGf,EAAE,CAAC9Z,MAAM,GAAG,CAAC;EAC3B,IAAI6a,KAAK,GAAGT,aAAa,CAACpa,MAAM,EAAE;IAC9B,KAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgb,KAAK,EAAEhb,CAAC,EAAE,EAAC;MACtB,OAAOga,IAAI,CAACC,EAAE,CAACja,CAAC,CAAC,CAAC;IACtB;IACAia,EAAE,CAAC3P,MAAM,CAAC,CAAC,EAAE0Q,KAAK,CAAC;EACvB;EACA,OAAOd,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASe,WAAWA,CAAC3H,KAAK,EAAE4H,KAAK,EAAE5F,KAAK,EAAE;EAC1C,MAAM+B,gBAAgB,GAAG/D,KAAK,CAAC6H,uBAAuB;EACtD,MAAMC,SAAS,GAAG9F,KAAK,KAAK,CAAC,GAAGjR,IAAI,CAACqC,GAAG,CAAC4O,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC;EAC5D,OAAOjR,IAAI,CAACiB,KAAK,CAAC,CAAC4V,KAAK,GAAGE,SAAS,IAAI/D,gBAAgB,CAAC,GAAGA,gBAAgB,GAAG+D,SAAS;AAC5F;AACA;AACA;AACA;AAAI,SAASC,WAAWA,CAACC,MAAM,EAAEpD,GAAG,EAAE;EAClC,IAAI,CAACA,GAAG,IAAI,CAACoD,MAAM,EAAE;IACjB;EACJ;EACApD,GAAG,GAAGA,GAAG,IAAIoD,MAAM,CAACC,UAAU,CAAC,IAAI,CAAC;EACpCrD,GAAG,CAACwC,IAAI,CAAC,CAAC;EACV;EACA;EACAxC,GAAG,CAACsD,cAAc,CAAC,CAAC;EACpBtD,GAAG,CAACuD,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEH,MAAM,CAAChG,KAAK,EAAEgG,MAAM,CAACI,MAAM,CAAC;EAChDxD,GAAG,CAAC6C,OAAO,CAAC,CAAC;AACjB;AACA,SAASY,SAASA,CAACzD,GAAG,EAAE3W,OAAO,EAAEkB,CAAC,EAAEE,CAAC,EAAE;EACnC;EACAiZ,eAAe,CAAC1D,GAAG,EAAE3W,OAAO,EAAEkB,CAAC,EAAEE,CAAC,EAAE,IAAI,CAAC;AAC7C;AACA;AACA,SAASiZ,eAAeA,CAAC1D,GAAG,EAAE3W,OAAO,EAAEkB,CAAC,EAAEE,CAAC,EAAEkZ,CAAC,EAAE;EAC5C,IAAIvd,IAAI,EAAEwd,OAAO,EAAEC,OAAO,EAAEhY,IAAI,EAAEiY,YAAY,EAAE1G,KAAK,EAAE2G,QAAQ,EAAEC,QAAQ;EACzE,MAAMrE,KAAK,GAAGtW,OAAO,CAAC4a,UAAU;EAChC,MAAMC,QAAQ,GAAG7a,OAAO,CAAC6a,QAAQ;EACjC,MAAMC,MAAM,GAAG9a,OAAO,CAAC8a,MAAM;EAC7B,IAAIC,GAAG,GAAG,CAACF,QAAQ,IAAI,CAAC,IAAI1X,WAAW;EACvC,IAAImT,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACpCvZ,IAAI,GAAGuZ,KAAK,CAACpZ,QAAQ,CAAC,CAAC;IACvB,IAAIH,IAAI,KAAK,2BAA2B,IAAIA,IAAI,KAAK,4BAA4B,EAAE;MAC/E4Z,GAAG,CAACwC,IAAI,CAAC,CAAC;MACVxC,GAAG,CAACqE,SAAS,CAAC9Z,CAAC,EAAEE,CAAC,CAAC;MACnBuV,GAAG,CAACsE,MAAM,CAACF,GAAG,CAAC;MACfpE,GAAG,CAACuE,SAAS,CAAC5E,KAAK,EAAE,CAACA,KAAK,CAACvC,KAAK,GAAG,CAAC,EAAE,CAACuC,KAAK,CAAC6D,MAAM,GAAG,CAAC,EAAE7D,KAAK,CAACvC,KAAK,EAAEuC,KAAK,CAAC6D,MAAM,CAAC;MACpFxD,GAAG,CAAC6C,OAAO,CAAC,CAAC;MACb;IACJ;EACJ;EACA,IAAI5U,KAAK,CAACkW,MAAM,CAAC,IAAIA,MAAM,IAAI,CAAC,EAAE;IAC9B;EACJ;EACAnE,GAAG,CAACwE,SAAS,CAAC,CAAC;EACf,QAAO7E,KAAK;IACR;IACA;MACI,IAAIgE,CAAC,EAAE;QACH3D,GAAG,CAACyE,OAAO,CAACla,CAAC,EAAEE,CAAC,EAAEkZ,CAAC,GAAG,CAAC,EAAEQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE/X,GAAG,CAAC;MAC/C,CAAC,MAAM;QACH4T,GAAG,CAAC0E,GAAG,CAACna,CAAC,EAAEE,CAAC,EAAE0Z,MAAM,EAAE,CAAC,EAAE/X,GAAG,CAAC;MACjC;MACA4T,GAAG,CAAC2E,SAAS,CAAC,CAAC;MACf;IACJ,KAAK,UAAU;MACXvH,KAAK,GAAGuG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM;MAC1BnE,GAAG,CAAC4E,MAAM,CAACra,CAAC,GAAG4B,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,GAAGhH,KAAK,EAAE3S,CAAC,GAAG0B,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM,CAAC;MACjEC,GAAG,IAAIzX,aAAa;MACpBqT,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAG4B,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,GAAGhH,KAAK,EAAE3S,CAAC,GAAG0B,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM,CAAC;MACjEC,GAAG,IAAIzX,aAAa;MACpBqT,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAG4B,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,GAAGhH,KAAK,EAAE3S,CAAC,GAAG0B,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM,CAAC;MACjEnE,GAAG,CAAC2E,SAAS,CAAC,CAAC;MACf;IACJ,KAAK,aAAa;MACd;MACA;MACA;MACA;MACA;MACA;MACA;MACAb,YAAY,GAAGK,MAAM,GAAG,KAAK;MAC7BtY,IAAI,GAAGsY,MAAM,GAAGL,YAAY;MAC5BF,OAAO,GAAGzX,IAAI,CAAC2K,GAAG,CAACsN,GAAG,GAAG1X,UAAU,CAAC,GAAGb,IAAI;MAC3CkY,QAAQ,GAAG5X,IAAI,CAAC2K,GAAG,CAACsN,GAAG,GAAG1X,UAAU,CAAC,IAAIiX,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGG,YAAY,GAAGjY,IAAI,CAAC;MACzEgY,OAAO,GAAG1X,IAAI,CAAC0J,GAAG,CAACuO,GAAG,GAAG1X,UAAU,CAAC,GAAGb,IAAI;MAC3CmY,QAAQ,GAAG7X,IAAI,CAAC0J,GAAG,CAACuO,GAAG,GAAG1X,UAAU,CAAC,IAAIiX,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGG,YAAY,GAAGjY,IAAI,CAAC;MACzEmU,GAAG,CAAC0E,GAAG,CAACna,CAAC,GAAGwZ,QAAQ,EAAEtZ,CAAC,GAAGoZ,OAAO,EAAEC,YAAY,EAAEM,GAAG,GAAGlY,EAAE,EAAEkY,GAAG,GAAG3X,OAAO,CAAC;MACzEuT,GAAG,CAAC0E,GAAG,CAACna,CAAC,GAAGyZ,QAAQ,EAAEvZ,CAAC,GAAGmZ,OAAO,EAAEE,YAAY,EAAEM,GAAG,GAAG3X,OAAO,EAAE2X,GAAG,CAAC;MACpEpE,GAAG,CAAC0E,GAAG,CAACna,CAAC,GAAGwZ,QAAQ,EAAEtZ,CAAC,GAAGoZ,OAAO,EAAEC,YAAY,EAAEM,GAAG,EAAEA,GAAG,GAAG3X,OAAO,CAAC;MACpEuT,GAAG,CAAC0E,GAAG,CAACna,CAAC,GAAGyZ,QAAQ,EAAEvZ,CAAC,GAAGmZ,OAAO,EAAEE,YAAY,EAAEM,GAAG,GAAG3X,OAAO,EAAE2X,GAAG,GAAGlY,EAAE,CAAC;MACzE8T,GAAG,CAAC2E,SAAS,CAAC,CAAC;MACf;IACJ,KAAK,MAAM;MACP,IAAI,CAACT,QAAQ,EAAE;QACXrY,IAAI,GAAGM,IAAI,CAAC2Y,OAAO,GAAGX,MAAM;QAC5B/G,KAAK,GAAGuG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG9X,IAAI;QACxBmU,GAAG,CAAC+E,IAAI,CAACxa,CAAC,GAAG6S,KAAK,EAAE3S,CAAC,GAAGoB,IAAI,EAAE,CAAC,GAAGuR,KAAK,EAAE,CAAC,GAAGvR,IAAI,CAAC;QAClD;MACJ;MACAuY,GAAG,IAAI1X,UAAU;IACrB;IAAoB,KAAK,SAAS;MAC9BqX,QAAQ,GAAG5X,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CP,OAAO,GAAGzX,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM;MAChCN,OAAO,GAAG1X,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,GAAGD,MAAM;MAChCH,QAAQ,GAAG7X,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CnE,GAAG,CAAC4E,MAAM,CAACra,CAAC,GAAGwZ,QAAQ,EAAEtZ,CAAC,GAAGoZ,OAAO,CAAC;MACrC7D,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAGyZ,QAAQ,EAAEvZ,CAAC,GAAGmZ,OAAO,CAAC;MACrC5D,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAGwZ,QAAQ,EAAEtZ,CAAC,GAAGoZ,OAAO,CAAC;MACrC7D,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAGyZ,QAAQ,EAAEvZ,CAAC,GAAGmZ,OAAO,CAAC;MACrC5D,GAAG,CAAC2E,SAAS,CAAC,CAAC;MACf;IACJ,KAAK,UAAU;MACXP,GAAG,IAAI1X,UAAU;IACrB;IAAoB,KAAK,OAAO;MAC5BqX,QAAQ,GAAG5X,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CP,OAAO,GAAGzX,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM;MAChCN,OAAO,GAAG1X,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,GAAGD,MAAM;MAChCH,QAAQ,GAAG7X,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CnE,GAAG,CAAC4E,MAAM,CAACra,CAAC,GAAGwZ,QAAQ,EAAEtZ,CAAC,GAAGoZ,OAAO,CAAC;MACrC7D,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAGwZ,QAAQ,EAAEtZ,CAAC,GAAGoZ,OAAO,CAAC;MACrC7D,GAAG,CAAC4E,MAAM,CAACra,CAAC,GAAGyZ,QAAQ,EAAEvZ,CAAC,GAAGmZ,OAAO,CAAC;MACrC5D,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAGyZ,QAAQ,EAAEvZ,CAAC,GAAGmZ,OAAO,CAAC;MACrC;IACJ,KAAK,MAAM;MACPG,QAAQ,GAAG5X,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CP,OAAO,GAAGzX,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM;MAChCN,OAAO,GAAG1X,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,GAAGD,MAAM;MAChCH,QAAQ,GAAG7X,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CnE,GAAG,CAAC4E,MAAM,CAACra,CAAC,GAAGwZ,QAAQ,EAAEtZ,CAAC,GAAGoZ,OAAO,CAAC;MACrC7D,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAGwZ,QAAQ,EAAEtZ,CAAC,GAAGoZ,OAAO,CAAC;MACrC7D,GAAG,CAAC4E,MAAM,CAACra,CAAC,GAAGyZ,QAAQ,EAAEvZ,CAAC,GAAGmZ,OAAO,CAAC;MACrC5D,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAGyZ,QAAQ,EAAEvZ,CAAC,GAAGmZ,OAAO,CAAC;MACrCQ,GAAG,IAAI1X,UAAU;MACjBqX,QAAQ,GAAG5X,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CP,OAAO,GAAGzX,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM;MAChCN,OAAO,GAAG1X,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,GAAGD,MAAM;MAChCH,QAAQ,GAAG7X,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CnE,GAAG,CAAC4E,MAAM,CAACra,CAAC,GAAGwZ,QAAQ,EAAEtZ,CAAC,GAAGoZ,OAAO,CAAC;MACrC7D,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAGwZ,QAAQ,EAAEtZ,CAAC,GAAGoZ,OAAO,CAAC;MACrC7D,GAAG,CAAC4E,MAAM,CAACra,CAAC,GAAGyZ,QAAQ,EAAEvZ,CAAC,GAAGmZ,OAAO,CAAC;MACrC5D,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAGyZ,QAAQ,EAAEvZ,CAAC,GAAGmZ,OAAO,CAAC;MACrC;IACJ,KAAK,MAAM;MACPA,OAAO,GAAGD,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGxX,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM;MAC5CN,OAAO,GAAG1X,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,GAAGD,MAAM;MAChCnE,GAAG,CAAC4E,MAAM,CAACra,CAAC,GAAGqZ,OAAO,EAAEnZ,CAAC,GAAGoZ,OAAO,CAAC;MACpC7D,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAGqZ,OAAO,EAAEnZ,CAAC,GAAGoZ,OAAO,CAAC;MACpC;IACJ,KAAK,MAAM;MACP7D,GAAG,CAAC4E,MAAM,CAACra,CAAC,EAAEE,CAAC,CAAC;MAChBuV,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAG4B,IAAI,CAAC2K,GAAG,CAACsN,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC,EAAE1Z,CAAC,GAAG0B,IAAI,CAAC0J,GAAG,CAACuO,GAAG,CAAC,GAAGD,MAAM,CAAC;MAChF;IACJ,KAAK,KAAK;MACNnE,GAAG,CAAC2E,SAAS,CAAC,CAAC;MACf;EACR;EACA3E,GAAG,CAACgF,IAAI,CAAC,CAAC;EACV,IAAI3b,OAAO,CAAC4b,WAAW,GAAG,CAAC,EAAE;IACzBjF,GAAG,CAACkF,MAAM,CAAC,CAAC;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASC,cAAcA,CAACC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC7CA,MAAM,GAAGA,MAAM,IAAI,GAAG,CAAC,CAAC;EACxB,OAAO,CAACD,IAAI,IAAID,KAAK,IAAIA,KAAK,CAAC7a,CAAC,GAAG8a,IAAI,CAACxR,IAAI,GAAGyR,MAAM,IAAIF,KAAK,CAAC7a,CAAC,GAAG8a,IAAI,CAACvR,KAAK,GAAGwR,MAAM,IAAIF,KAAK,CAAC3a,CAAC,GAAG4a,IAAI,CAACrL,GAAG,GAAGsL,MAAM,IAAIF,KAAK,CAAC3a,CAAC,GAAG4a,IAAI,CAACpL,MAAM,GAAGqL,MAAM;AAC3J;AACA,SAASC,QAAQA,CAACvF,GAAG,EAAEqF,IAAI,EAAE;EACzBrF,GAAG,CAACwC,IAAI,CAAC,CAAC;EACVxC,GAAG,CAACwE,SAAS,CAAC,CAAC;EACfxE,GAAG,CAAC+E,IAAI,CAACM,IAAI,CAACxR,IAAI,EAAEwR,IAAI,CAACrL,GAAG,EAAEqL,IAAI,CAACvR,KAAK,GAAGuR,IAAI,CAACxR,IAAI,EAAEwR,IAAI,CAACpL,MAAM,GAAGoL,IAAI,CAACrL,GAAG,CAAC;EAC7EgG,GAAG,CAACzD,IAAI,CAAC,CAAC;AACd;AACA,SAASiJ,UAAUA,CAACxF,GAAG,EAAE;EACrBA,GAAG,CAAC6C,OAAO,CAAC,CAAC;AACjB;AACA;AACA;AACA;AAAI,SAAS4C,cAAcA,CAACzF,GAAG,EAAE/V,QAAQ,EAAEpB,MAAM,EAAE6c,IAAI,EAAErF,IAAI,EAAE;EAC3D,IAAI,CAACpW,QAAQ,EAAE;IACX,OAAO+V,GAAG,CAAC6E,MAAM,CAAChc,MAAM,CAAC0B,CAAC,EAAE1B,MAAM,CAAC4B,CAAC,CAAC;EACzC;EACA,IAAI4V,IAAI,KAAK,QAAQ,EAAE;IACnB,MAAMsF,QAAQ,GAAG,CAAC1b,QAAQ,CAACM,CAAC,GAAG1B,MAAM,CAAC0B,CAAC,IAAI,GAAG;IAC9CyV,GAAG,CAAC6E,MAAM,CAACc,QAAQ,EAAE1b,QAAQ,CAACQ,CAAC,CAAC;IAChCuV,GAAG,CAAC6E,MAAM,CAACc,QAAQ,EAAE9c,MAAM,CAAC4B,CAAC,CAAC;EAClC,CAAC,MAAM,IAAI4V,IAAI,KAAK,OAAO,KAAK,CAAC,CAACqF,IAAI,EAAE;IACpC1F,GAAG,CAAC6E,MAAM,CAAC5a,QAAQ,CAACM,CAAC,EAAE1B,MAAM,CAAC4B,CAAC,CAAC;EACpC,CAAC,MAAM;IACHuV,GAAG,CAAC6E,MAAM,CAAChc,MAAM,CAAC0B,CAAC,EAAEN,QAAQ,CAACQ,CAAC,CAAC;EACpC;EACAuV,GAAG,CAAC6E,MAAM,CAAChc,MAAM,CAAC0B,CAAC,EAAE1B,MAAM,CAAC4B,CAAC,CAAC;AAClC;AACA;AACA;AACA;AAAI,SAASmb,cAAcA,CAAC5F,GAAG,EAAE/V,QAAQ,EAAEpB,MAAM,EAAE6c,IAAI,EAAE;EACrD,IAAI,CAACzb,QAAQ,EAAE;IACX,OAAO+V,GAAG,CAAC6E,MAAM,CAAChc,MAAM,CAAC0B,CAAC,EAAE1B,MAAM,CAAC4B,CAAC,CAAC;EACzC;EACAuV,GAAG,CAAC6F,aAAa,CAACH,IAAI,GAAGzb,QAAQ,CAAC6b,IAAI,GAAG7b,QAAQ,CAAC8b,IAAI,EAAEL,IAAI,GAAGzb,QAAQ,CAAC+b,IAAI,GAAG/b,QAAQ,CAACgc,IAAI,EAAEP,IAAI,GAAG7c,MAAM,CAACkd,IAAI,GAAGld,MAAM,CAACid,IAAI,EAAEJ,IAAI,GAAG7c,MAAM,CAACod,IAAI,GAAGpd,MAAM,CAACmd,IAAI,EAAEnd,MAAM,CAAC0B,CAAC,EAAE1B,MAAM,CAAC4B,CAAC,CAAC;AACzL;AACA,SAASyb,aAAaA,CAAClG,GAAG,EAAEmG,IAAI,EAAE;EAC9B,IAAIA,IAAI,CAACC,WAAW,EAAE;IAClBpG,GAAG,CAACqE,SAAS,CAAC8B,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,EAAED,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;EAC3D;EACA,IAAI,CAACpgB,aAAa,CAACmgB,IAAI,CAACjC,QAAQ,CAAC,EAAE;IAC/BlE,GAAG,CAACsE,MAAM,CAAC6B,IAAI,CAACjC,QAAQ,CAAC;EAC7B;EACA,IAAIiC,IAAI,CAAChO,KAAK,EAAE;IACZ6H,GAAG,CAACqG,SAAS,GAAGF,IAAI,CAAChO,KAAK;EAC9B;EACA,IAAIgO,IAAI,CAACG,SAAS,EAAE;IAChBtG,GAAG,CAACsG,SAAS,GAAGH,IAAI,CAACG,SAAS;EAClC;EACA,IAAIH,IAAI,CAACI,YAAY,EAAE;IACnBvG,GAAG,CAACuG,YAAY,GAAGJ,IAAI,CAACI,YAAY;EACxC;AACJ;AACA,SAASC,YAAYA,CAACxG,GAAG,EAAEzV,CAAC,EAAEE,CAAC,EAAEgc,IAAI,EAAEN,IAAI,EAAE;EACzC,IAAIA,IAAI,CAACO,aAAa,IAAIP,IAAI,CAACQ,SAAS,EAAE;IACtC;AACR;AACA;AACA;AACA;AACA;AACA;IAAQ,MAAMC,OAAO,GAAG5G,GAAG,CAACmC,WAAW,CAACsE,IAAI,CAAC;IACrC,MAAM5S,IAAI,GAAGtJ,CAAC,GAAGqc,OAAO,CAACC,qBAAqB;IAC9C,MAAM/S,KAAK,GAAGvJ,CAAC,GAAGqc,OAAO,CAACE,sBAAsB;IAChD,MAAM9M,GAAG,GAAGvP,CAAC,GAAGmc,OAAO,CAACG,uBAAuB;IAC/C,MAAM9M,MAAM,GAAGxP,CAAC,GAAGmc,OAAO,CAACI,wBAAwB;IACnD,MAAMC,WAAW,GAAGd,IAAI,CAACO,aAAa,GAAG,CAAC1M,GAAG,GAAGC,MAAM,IAAI,CAAC,GAAGA,MAAM;IACpE+F,GAAG,CAACkH,WAAW,GAAGlH,GAAG,CAACqG,SAAS;IAC/BrG,GAAG,CAACwE,SAAS,CAAC,CAAC;IACfxE,GAAG,CAACtD,SAAS,GAAGyJ,IAAI,CAACgB,eAAe,IAAI,CAAC;IACzCnH,GAAG,CAAC4E,MAAM,CAAC/Q,IAAI,EAAEoT,WAAW,CAAC;IAC7BjH,GAAG,CAAC6E,MAAM,CAAC/Q,KAAK,EAAEmT,WAAW,CAAC;IAC9BjH,GAAG,CAACkF,MAAM,CAAC,CAAC;EAChB;AACJ;AACA,SAASkC,YAAYA,CAACpH,GAAG,EAAEmG,IAAI,EAAE;EAC7B,MAAMkB,QAAQ,GAAGrH,GAAG,CAACqG,SAAS;EAC9BrG,GAAG,CAACqG,SAAS,GAAGF,IAAI,CAAChO,KAAK;EAC1B6H,GAAG,CAACsH,QAAQ,CAACnB,IAAI,CAACtS,IAAI,EAAEsS,IAAI,CAACnM,GAAG,EAAEmM,IAAI,CAAC/I,KAAK,EAAE+I,IAAI,CAAC3C,MAAM,CAAC;EAC1DxD,GAAG,CAACqG,SAAS,GAAGgB,QAAQ;AAC5B;AACA;AACA;AACA;AAAI,SAASE,UAAUA,CAACvH,GAAG,EAAE1C,IAAI,EAAE/S,CAAC,EAAEE,CAAC,EAAEgV,IAAI,EAAE0G,IAAI,GAAG,CAAC,CAAC,EAAE;EACtD,MAAMqB,KAAK,GAAGthB,OAAO,CAACoX,IAAI,CAAC,GAAGA,IAAI,GAAG,CACjCA,IAAI,CACP;EACD,MAAM4H,MAAM,GAAGiB,IAAI,CAACsB,WAAW,GAAG,CAAC,IAAItB,IAAI,CAACuB,WAAW,KAAK,EAAE;EAC9D,IAAI5f,CAAC,EAAE2e,IAAI;EACXzG,GAAG,CAACwC,IAAI,CAAC,CAAC;EACVxC,GAAG,CAACP,IAAI,GAAGA,IAAI,CAACwC,MAAM;EACtBiE,aAAa,CAAClG,GAAG,EAAEmG,IAAI,CAAC;EACxB,KAAIre,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0f,KAAK,CAACvf,MAAM,EAAE,EAAEH,CAAC,EAAC;IAC7B2e,IAAI,GAAGe,KAAK,CAAC1f,CAAC,CAAC;IACf,IAAIqe,IAAI,CAACwB,QAAQ,EAAE;MACfP,YAAY,CAACpH,GAAG,EAAEmG,IAAI,CAACwB,QAAQ,CAAC;IACpC;IACA,IAAIzC,MAAM,EAAE;MACR,IAAIiB,IAAI,CAACuB,WAAW,EAAE;QAClB1H,GAAG,CAACkH,WAAW,GAAGf,IAAI,CAACuB,WAAW;MACtC;MACA,IAAI,CAAC1hB,aAAa,CAACmgB,IAAI,CAACsB,WAAW,CAAC,EAAE;QAClCzH,GAAG,CAACtD,SAAS,GAAGyJ,IAAI,CAACsB,WAAW;MACpC;MACAzH,GAAG,CAAC4H,UAAU,CAACnB,IAAI,EAAElc,CAAC,EAAEE,CAAC,EAAE0b,IAAI,CAAC0B,QAAQ,CAAC;IAC7C;IACA7H,GAAG,CAAC8H,QAAQ,CAACrB,IAAI,EAAElc,CAAC,EAAEE,CAAC,EAAE0b,IAAI,CAAC0B,QAAQ,CAAC;IACvCrB,YAAY,CAACxG,GAAG,EAAEzV,CAAC,EAAEE,CAAC,EAAEgc,IAAI,EAAEN,IAAI,CAAC;IACnC1b,CAAC,IAAI7D,MAAM,CAAC6Y,IAAI,CAACG,UAAU,CAAC;EAChC;EACAI,GAAG,CAAC6C,OAAO,CAAC,CAAC;AACjB;AACA;AACA;AACA;AACA;AACA;AAAI,SAASkF,kBAAkBA,CAAC/H,GAAG,EAAE+E,IAAI,EAAE;EACvC,MAAM;IAAExa,CAAC;IAAGE,CAAC;IAAGkZ,CAAC;IAAGqE,CAAC;IAAG7D;EAAQ,CAAC,GAAGY,IAAI;EACxC;EACA/E,GAAG,CAAC0E,GAAG,CAACna,CAAC,GAAG4Z,MAAM,CAAC8D,OAAO,EAAExd,CAAC,GAAG0Z,MAAM,CAAC8D,OAAO,EAAE9D,MAAM,CAAC8D,OAAO,EAAE,GAAG,GAAG/b,EAAE,EAAEA,EAAE,EAAE,IAAI,CAAC;EACnF;EACA8T,GAAG,CAAC6E,MAAM,CAACta,CAAC,EAAEE,CAAC,GAAGud,CAAC,GAAG7D,MAAM,CAAC+D,UAAU,CAAC;EACxC;EACAlI,GAAG,CAAC0E,GAAG,CAACna,CAAC,GAAG4Z,MAAM,CAAC+D,UAAU,EAAEzd,CAAC,GAAGud,CAAC,GAAG7D,MAAM,CAAC+D,UAAU,EAAE/D,MAAM,CAAC+D,UAAU,EAAEhc,EAAE,EAAEO,OAAO,EAAE,IAAI,CAAC;EAC/F;EACAuT,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAGoZ,CAAC,GAAGQ,MAAM,CAACgE,WAAW,EAAE1d,CAAC,GAAGud,CAAC,CAAC;EAC7C;EACAhI,GAAG,CAAC0E,GAAG,CAACna,CAAC,GAAGoZ,CAAC,GAAGQ,MAAM,CAACgE,WAAW,EAAE1d,CAAC,GAAGud,CAAC,GAAG7D,MAAM,CAACgE,WAAW,EAAEhE,MAAM,CAACgE,WAAW,EAAE1b,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC;EACrG;EACAuT,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAGoZ,CAAC,EAAElZ,CAAC,GAAG0Z,MAAM,CAACiE,QAAQ,CAAC;EACtC;EACApI,GAAG,CAAC0E,GAAG,CAACna,CAAC,GAAGoZ,CAAC,GAAGQ,MAAM,CAACiE,QAAQ,EAAE3d,CAAC,GAAG0Z,MAAM,CAACiE,QAAQ,EAAEjE,MAAM,CAACiE,QAAQ,EAAE,CAAC,EAAE,CAAC3b,OAAO,EAAE,IAAI,CAAC;EACzF;EACAuT,GAAG,CAAC6E,MAAM,CAACta,CAAC,GAAG4Z,MAAM,CAAC8D,OAAO,EAAExd,CAAC,CAAC;AACrC;AAEA,MAAM4d,WAAW,GAAG,sCAAsC;AAC1D,MAAMC,UAAU,GAAG,uEAAuE;AAC1F;AACA;AACA;AACA,GAHA,CAGI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASC,YAAYA,CAACtiB,KAAK,EAAE4F,IAAI,EAAE;EACnC,MAAM2c,OAAO,GAAG,CAAC,EAAE,GAAGviB,KAAK,EAAEwiB,KAAK,CAACJ,WAAW,CAAC;EAC/C,IAAI,CAACG,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IACrC,OAAO3c,IAAI,GAAG,GAAG;EACrB;EACA5F,KAAK,GAAG,CAACuiB,OAAO,CAAC,CAAC,CAAC;EACnB,QAAOA,OAAO,CAAC,CAAC,CAAC;IACb,KAAK,IAAI;MACL,OAAOviB,KAAK;IAChB,KAAK,GAAG;MACJA,KAAK,IAAI,GAAG;MACZ;EACR;EACA,OAAO4F,IAAI,GAAG5F,KAAK;AACvB;AACA,MAAMyiB,YAAY,GAAIpe,CAAC,IAAG,CAACA,CAAC,IAAI,CAAC;AACjC,SAASqe,iBAAiBA,CAAC1iB,KAAK,EAAE2iB,KAAK,EAAE;EACrC,MAAMC,GAAG,GAAG,CAAC,CAAC;EACd,MAAMC,QAAQ,GAAGpiB,QAAQ,CAACkiB,KAAK,CAAC;EAChC,MAAM5gB,IAAI,GAAG8gB,QAAQ,GAAGziB,MAAM,CAAC2B,IAAI,CAAC4gB,KAAK,CAAC,GAAGA,KAAK;EAClD,MAAMG,IAAI,GAAGriB,QAAQ,CAACT,KAAK,CAAC,GAAG6iB,QAAQ,GAAIE,IAAI,IAAGhiB,cAAc,CAACf,KAAK,CAAC+iB,IAAI,CAAC,EAAE/iB,KAAK,CAAC2iB,KAAK,CAACI,IAAI,CAAC,CAAC,CAAC,GAAIA,IAAI,IAAG/iB,KAAK,CAAC+iB,IAAI,CAAC,GAAG,MAAI/iB,KAAK;EACnI,KAAK,MAAM+iB,IAAI,IAAIhhB,IAAI,EAAC;IACpB6gB,GAAG,CAACG,IAAI,CAAC,GAAGN,YAAY,CAACK,IAAI,CAACC,IAAI,CAAC,CAAC;EACxC;EACA,OAAOH,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASI,MAAMA,CAAChjB,KAAK,EAAE;EACvB,OAAO0iB,iBAAiB,CAAC1iB,KAAK,EAAE;IAC5B+T,GAAG,EAAE,GAAG;IACRlG,KAAK,EAAE,GAAG;IACVmG,MAAM,EAAE,GAAG;IACXpG,IAAI,EAAE;EACV,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASqV,aAAaA,CAACjjB,KAAK,EAAE;EAC9B,OAAO0iB,iBAAiB,CAAC1iB,KAAK,EAAE,CAC5B,SAAS,EACT,UAAU,EACV,YAAY,EACZ,aAAa,CAChB,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASkjB,SAASA,CAACljB,KAAK,EAAE;EAC1B,MAAMgF,GAAG,GAAGge,MAAM,CAAChjB,KAAK,CAAC;EACzBgF,GAAG,CAACmS,KAAK,GAAGnS,GAAG,CAAC4I,IAAI,GAAG5I,GAAG,CAAC6I,KAAK;EAChC7I,GAAG,CAACuY,MAAM,GAAGvY,GAAG,CAAC+O,GAAG,GAAG/O,GAAG,CAACgP,MAAM;EACjC,OAAOhP,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASme,MAAMA,CAAC/f,OAAO,EAAEggB,QAAQ,EAAE;EACnChgB,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvBggB,QAAQ,GAAGA,QAAQ,IAAI1Q,QAAQ,CAAC8G,IAAI;EACpC,IAAI5T,IAAI,GAAG7E,cAAc,CAACqC,OAAO,CAACwC,IAAI,EAAEwd,QAAQ,CAACxd,IAAI,CAAC;EACtD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1BA,IAAI,GAAGyd,QAAQ,CAACzd,IAAI,EAAE,EAAE,CAAC;EAC7B;EACA,IAAI8T,KAAK,GAAG3Y,cAAc,CAACqC,OAAO,CAACsW,KAAK,EAAE0J,QAAQ,CAAC1J,KAAK,CAAC;EACzD,IAAIA,KAAK,IAAI,CAAC,CAAC,EAAE,GAAGA,KAAK,EAAE8I,KAAK,CAACH,UAAU,CAAC,EAAE;IAC1Cne,OAAO,CAACC,IAAI,CAAC,iCAAiC,GAAGuV,KAAK,GAAG,GAAG,CAAC;IAC7DA,KAAK,GAAGzV,SAAS;EACrB;EACA,MAAMuV,IAAI,GAAG;IACTC,MAAM,EAAE1Y,cAAc,CAACqC,OAAO,CAACqW,MAAM,EAAE2J,QAAQ,CAAC3J,MAAM,CAAC;IACvDE,UAAU,EAAE2I,YAAY,CAACvhB,cAAc,CAACqC,OAAO,CAACuW,UAAU,EAAEyJ,QAAQ,CAACzJ,UAAU,CAAC,EAAE/T,IAAI,CAAC;IACvFA,IAAI;IACJ8T,KAAK;IACLE,MAAM,EAAE7Y,cAAc,CAACqC,OAAO,CAACwW,MAAM,EAAEwJ,QAAQ,CAACxJ,MAAM,CAAC;IACvDoC,MAAM,EAAE;EACZ,CAAC;EACDxC,IAAI,CAACwC,MAAM,GAAGL,YAAY,CAACnC,IAAI,CAAC;EAChC,OAAOA,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS8J,OAAOA,CAACC,MAAM,EAAEpK,OAAO,EAAE3W,KAAK,EAAEghB,IAAI,EAAE;EAC/C,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAI5hB,CAAC,EAAEO,IAAI,EAAEpC,KAAK;EAClB,KAAI6B,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGmhB,MAAM,CAACvhB,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;IAC3C7B,KAAK,GAAGujB,MAAM,CAAC1hB,CAAC,CAAC;IACjB,IAAI7B,KAAK,KAAKiE,SAAS,EAAE;MACrB;IACJ;IACA,IAAIkV,OAAO,KAAKlV,SAAS,IAAI,OAAOjE,KAAK,KAAK,UAAU,EAAE;MACtDA,KAAK,GAAGA,KAAK,CAACmZ,OAAO,CAAC;MACtBsK,SAAS,GAAG,KAAK;IACrB;IACA,IAAIjhB,KAAK,KAAKyB,SAAS,IAAIhE,OAAO,CAACD,KAAK,CAAC,EAAE;MACvCA,KAAK,GAAGA,KAAK,CAACwC,KAAK,GAAGxC,KAAK,CAACgC,MAAM,CAAC;MACnCyhB,SAAS,GAAG,KAAK;IACrB;IACA,IAAIzjB,KAAK,KAAKiE,SAAS,EAAE;MACrB,IAAIuf,IAAI,IAAI,CAACC,SAAS,EAAE;QACpBD,IAAI,CAACC,SAAS,GAAG,KAAK;MAC1B;MACA,OAAOzjB,KAAK;IAChB;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS0jB,SAASA,CAACC,MAAM,EAAEpN,KAAK,EAAEH,WAAW,EAAE;EAC/C,MAAM;IAAE9N,GAAG;IAAGC;EAAK,CAAC,GAAGob,MAAM;EAC7B,MAAMC,MAAM,GAAGxiB,WAAW,CAACmV,KAAK,EAAE,CAAChO,GAAG,GAAGD,GAAG,IAAI,CAAC,CAAC;EAClD,MAAMub,QAAQ,GAAGA,CAAC7jB,KAAK,EAAE8jB,GAAG,KAAG1N,WAAW,IAAIpW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAG8jB,GAAG;EAC3E,OAAO;IACHxb,GAAG,EAAEub,QAAQ,CAACvb,GAAG,EAAE,CAACpC,IAAI,CAACa,GAAG,CAAC6c,MAAM,CAAC,CAAC;IACrCrb,GAAG,EAAEsb,QAAQ,CAACtb,GAAG,EAAEqb,MAAM;EAC7B,CAAC;AACL;AACA,SAASG,aAAaA,CAACC,aAAa,EAAE7K,OAAO,EAAE;EAC3C,OAAO/Y,MAAM,CAACoP,MAAM,CAACpP,MAAM,CAACyC,MAAM,CAACmhB,aAAa,CAAC,EAAE7K,OAAO,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS8K,eAAeA,CAACC,MAAM,EAAEC,QAAQ,GAAG,CAC5C,EAAE,CACL,EAAEC,UAAU,EAAEhB,QAAQ,EAAEiB,SAAS,GAAGA,CAAA,KAAIH,MAAM,CAAC,CAAC,CAAC,EAAE;EAChD,MAAMI,eAAe,GAAGF,UAAU,IAAIF,MAAM;EAC5C,IAAI,OAAOd,QAAQ,KAAK,WAAW,EAAE;IACjCA,QAAQ,GAAGmB,QAAQ,CAAC,WAAW,EAAEL,MAAM,CAAC;EAC5C;EACA,MAAM7H,KAAK,GAAG;IACV,CAACmI,MAAM,CAACC,WAAW,GAAG,QAAQ;IAC9BC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAET,MAAM;IACfU,WAAW,EAAEN,eAAe;IAC5BtR,SAAS,EAAEoQ,QAAQ;IACnByB,UAAU,EAAER,SAAS;IACrBpJ,QAAQ,EAAGlX,KAAK,IAAGkgB,eAAe,CAAC,CAC3BlgB,KAAK,EACL,GAAGmgB,MAAM,CACZ,EAAEC,QAAQ,EAAEG,eAAe,EAAElB,QAAQ;EAC9C,CAAC;EACD,OAAO,IAAI0B,KAAK,CAACzI,KAAK,EAAE;IACpB;AACR;AACA;IAAQ0I,cAAcA,CAAEniB,MAAM,EAAEmgB,IAAI,EAAE;MAC1B,OAAOngB,MAAM,CAACmgB,IAAI,CAAC,CAAC,CAAC;MACrB,OAAOngB,MAAM,CAACoiB,KAAK,CAAC,CAAC;MACrB,OAAOd,MAAM,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC,CAAC;MACxB,OAAO,IAAI;IACf,CAAC;IACD;AACR;AACA;IAAQtO,GAAGA,CAAE7R,MAAM,EAAEmgB,IAAI,EAAE;MACf,OAAOkC,OAAO,CAACriB,MAAM,EAAEmgB,IAAI,EAAE,MAAImC,oBAAoB,CAACnC,IAAI,EAAEoB,QAAQ,EAAED,MAAM,EAAEthB,MAAM,CAAC,CAAC;IAC1F,CAAC;IACD;AACR;AACA;AACA;IAAQuiB,wBAAwBA,CAAEviB,MAAM,EAAEmgB,IAAI,EAAE;MACpC,OAAOqC,OAAO,CAACD,wBAAwB,CAACviB,MAAM,CAAC+hB,OAAO,CAAC,CAAC,CAAC,EAAE5B,IAAI,CAAC;IACpE,CAAC;IACD;AACR;AACA;IAAQsC,cAAcA,CAAA,EAAI;MACd,OAAOD,OAAO,CAACC,cAAc,CAACnB,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IACD;AACR;AACA;IAAQpe,GAAGA,CAAElD,MAAM,EAAEmgB,IAAI,EAAE;MACf,OAAOuC,oBAAoB,CAAC1iB,MAAM,CAAC,CAACmT,QAAQ,CAACgN,IAAI,CAAC;IACtD,CAAC;IACD;AACR;AACA;IAAQwC,OAAOA,CAAE3iB,MAAM,EAAE;MACb,OAAO0iB,oBAAoB,CAAC1iB,MAAM,CAAC;IACvC,CAAC;IACD;AACR;AACA;IAAQ0J,GAAGA,CAAE1J,MAAM,EAAEmgB,IAAI,EAAE/iB,KAAK,EAAE;MACtB,MAAMwlB,OAAO,GAAG5iB,MAAM,CAAC6iB,QAAQ,KAAK7iB,MAAM,CAAC6iB,QAAQ,GAAGpB,SAAS,CAAC,CAAC,CAAC;MAClEzhB,MAAM,CAACmgB,IAAI,CAAC,GAAGyC,OAAO,CAACzC,IAAI,CAAC,GAAG/iB,KAAK,CAAC,CAAC;MACtC,OAAO4C,MAAM,CAACoiB,KAAK,CAAC,CAAC;MACrB,OAAO,IAAI;IACf;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASU,cAAcA,CAACC,KAAK,EAAExM,OAAO,EAAEyM,QAAQ,EAAEC,kBAAkB,EAAE;EACtE,MAAMxJ,KAAK,GAAG;IACVqI,UAAU,EAAE,KAAK;IACjBoB,MAAM,EAAEH,KAAK;IACbI,QAAQ,EAAE5M,OAAO;IACjB6M,SAAS,EAAEJ,QAAQ;IACnBK,MAAM,EAAE,IAAI1Z,GAAG,CAAC,CAAC;IACjBsM,YAAY,EAAEA,YAAY,CAAC8M,KAAK,EAAEE,kBAAkB,CAAC;IACrDK,UAAU,EAAGnM,GAAG,IAAG2L,cAAc,CAACC,KAAK,EAAE5L,GAAG,EAAE6L,QAAQ,EAAEC,kBAAkB,CAAC;IAC3E5K,QAAQ,EAAGlX,KAAK,IAAG2hB,cAAc,CAACC,KAAK,CAAC1K,QAAQ,CAAClX,KAAK,CAAC,EAAEoV,OAAO,EAAEyM,QAAQ,EAAEC,kBAAkB;EAClG,CAAC;EACD,OAAO,IAAIf,KAAK,CAACzI,KAAK,EAAE;IACpB;AACR;AACA;IAAQ0I,cAAcA,CAAEniB,MAAM,EAAEmgB,IAAI,EAAE;MAC1B,OAAOngB,MAAM,CAACmgB,IAAI,CAAC,CAAC,CAAC;MACrB,OAAO4C,KAAK,CAAC5C,IAAI,CAAC,CAAC,CAAC;MACpB,OAAO,IAAI;IACf,CAAC;IACD;AACR;AACA;IAAQtO,GAAGA,CAAE7R,MAAM,EAAEmgB,IAAI,EAAEoD,QAAQ,EAAE;MACzB,OAAOlB,OAAO,CAACriB,MAAM,EAAEmgB,IAAI,EAAE,MAAIqD,mBAAmB,CAACxjB,MAAM,EAAEmgB,IAAI,EAAEoD,QAAQ,CAAC,CAAC;IACjF,CAAC;IACD;AACR;AACA;AACA;IAAQhB,wBAAwBA,CAAEviB,MAAM,EAAEmgB,IAAI,EAAE;MACpC,OAAOngB,MAAM,CAACiW,YAAY,CAACwN,OAAO,GAAGjB,OAAO,CAACtf,GAAG,CAAC6f,KAAK,EAAE5C,IAAI,CAAC,GAAG;QAC5DpX,UAAU,EAAE,IAAI;QAChBD,YAAY,EAAE;MAClB,CAAC,GAAGzH,SAAS,GAAGmhB,OAAO,CAACD,wBAAwB,CAACQ,KAAK,EAAE5C,IAAI,CAAC;IACjE,CAAC;IACD;AACR;AACA;IAAQsC,cAAcA,CAAA,EAAI;MACd,OAAOD,OAAO,CAACC,cAAc,CAACM,KAAK,CAAC;IACxC,CAAC;IACD;AACR;AACA;IAAQ7f,GAAGA,CAAElD,MAAM,EAAEmgB,IAAI,EAAE;MACf,OAAOqC,OAAO,CAACtf,GAAG,CAAC6f,KAAK,EAAE5C,IAAI,CAAC;IACnC,CAAC;IACD;AACR;AACA;IAAQwC,OAAOA,CAAA,EAAI;MACP,OAAOH,OAAO,CAACG,OAAO,CAACI,KAAK,CAAC;IACjC,CAAC;IACD;AACR;AACA;IAAQrZ,GAAGA,CAAE1J,MAAM,EAAEmgB,IAAI,EAAE/iB,KAAK,EAAE;MACtB2lB,KAAK,CAAC5C,IAAI,CAAC,GAAG/iB,KAAK,CAAC,CAAC;MACrB,OAAO4C,MAAM,CAACmgB,IAAI,CAAC,CAAC,CAAC;MACrB,OAAO,IAAI;IACf;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AAAI,SAASlK,YAAYA,CAAC8M,KAAK,EAAEjT,QAAQ,GAAG;EACxC4T,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE;AACf,CAAC,EAAE;EACC,MAAM;IAAErT,WAAW,GAAER,QAAQ,CAAC4T,UAAU;IAAGrT,UAAU,GAAEP,QAAQ,CAAC6T,SAAS;IAAGC,QAAQ,GAAE9T,QAAQ,CAAC2T;EAAS,CAAC,GAAGV,KAAK;EACjH,OAAO;IACHU,OAAO,EAAEG,QAAQ;IACjBF,UAAU,EAAEpT,WAAW;IACvBqT,SAAS,EAAEtT,UAAU;IACrBwT,YAAY,EAAEjhB,UAAU,CAAC0N,WAAW,CAAC,GAAGA,WAAW,GAAG,MAAIA,WAAW;IACrEwT,WAAW,EAAElhB,UAAU,CAACyN,UAAU,CAAC,GAAGA,UAAU,GAAG,MAAIA;EAC3D,CAAC;AACL;AACA,MAAM0T,OAAO,GAAGA,CAACC,MAAM,EAAEzT,IAAI,KAAGyT,MAAM,GAAGA,MAAM,GAAGzhB,WAAW,CAACgO,IAAI,CAAC,GAAGA,IAAI;AAC1E,MAAM0T,gBAAgB,GAAGA,CAAC9D,IAAI,EAAE/iB,KAAK,KAAGS,QAAQ,CAACT,KAAK,CAAC,IAAI+iB,IAAI,KAAK,UAAU,KAAK3iB,MAAM,CAACilB,cAAc,CAACrlB,KAAK,CAAC,KAAK,IAAI,IAAIA,KAAK,CAAC4Y,WAAW,KAAKxY,MAAM,CAAC;AACzJ,SAAS6kB,OAAOA,CAACriB,MAAM,EAAEmgB,IAAI,EAAEO,OAAO,EAAE;EACpC,IAAIljB,MAAM,CAACC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAM,EAAEmgB,IAAI,CAAC,IAAIA,IAAI,KAAK,aAAa,EAAE;IAC9E,OAAOngB,MAAM,CAACmgB,IAAI,CAAC;EACvB;EACA,MAAM/iB,KAAK,GAAGsjB,OAAO,CAAC,CAAC;EACvB;EACA1gB,MAAM,CAACmgB,IAAI,CAAC,GAAG/iB,KAAK;EACpB,OAAOA,KAAK;AAChB;AACA,SAASomB,mBAAmBA,CAACxjB,MAAM,EAAEmgB,IAAI,EAAEoD,QAAQ,EAAE;EACjD,MAAM;IAAEL,MAAM;IAAGC,QAAQ;IAAGC,SAAS;IAAGnN,YAAY,EAAEN;EAAa,CAAC,GAAG3V,MAAM;EAC7E,IAAI5C,KAAK,GAAG8lB,MAAM,CAAC/C,IAAI,CAAC,CAAC,CAAC;EAC1B;EACA,IAAIvd,UAAU,CAACxF,KAAK,CAAC,IAAIuY,WAAW,CAACkO,YAAY,CAAC1D,IAAI,CAAC,EAAE;IACrD/iB,KAAK,GAAG8mB,kBAAkB,CAAC/D,IAAI,EAAE/iB,KAAK,EAAE4C,MAAM,EAAEujB,QAAQ,CAAC;EAC7D;EACA,IAAIlmB,OAAO,CAACD,KAAK,CAAC,IAAIA,KAAK,CAACgC,MAAM,EAAE;IAChChC,KAAK,GAAG+mB,aAAa,CAAChE,IAAI,EAAE/iB,KAAK,EAAE4C,MAAM,EAAE2V,WAAW,CAACmO,WAAW,CAAC;EACvE;EACA,IAAIG,gBAAgB,CAAC9D,IAAI,EAAE/iB,KAAK,CAAC,EAAE;IAC/B;IACAA,KAAK,GAAG0lB,cAAc,CAAC1lB,KAAK,EAAE+lB,QAAQ,EAAEC,SAAS,IAAIA,SAAS,CAACjD,IAAI,CAAC,EAAExK,WAAW,CAAC;EACtF;EACA,OAAOvY,KAAK;AAChB;AACA,SAAS8mB,kBAAkBA,CAAC/D,IAAI,EAAEiE,QAAQ,EAAEpkB,MAAM,EAAEujB,QAAQ,EAAE;EAC1D,MAAM;IAAEL,MAAM;IAAGC,QAAQ;IAAGC,SAAS;IAAGC;EAAQ,CAAC,GAAGrjB,MAAM;EAC1D,IAAIqjB,MAAM,CAACngB,GAAG,CAACid,IAAI,CAAC,EAAE;IAClB,MAAM,IAAIkE,KAAK,CAAC,sBAAsB,GAAG/mB,KAAK,CAACsM,IAAI,CAACyZ,MAAM,CAAC,CAACiB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAGnE,IAAI,CAAC;EACzF;EACAkD,MAAM,CAACnC,GAAG,CAACf,IAAI,CAAC;EAChB,IAAI/iB,KAAK,GAAGgnB,QAAQ,CAACjB,QAAQ,EAAEC,SAAS,IAAIG,QAAQ,CAAC;EACrDF,MAAM,CAACkB,MAAM,CAACpE,IAAI,CAAC;EACnB,IAAI8D,gBAAgB,CAAC9D,IAAI,EAAE/iB,KAAK,CAAC,EAAE;IAC/B;IACAA,KAAK,GAAGonB,iBAAiB,CAACtB,MAAM,CAACnB,OAAO,EAAEmB,MAAM,EAAE/C,IAAI,EAAE/iB,KAAK,CAAC;EAClE;EACA,OAAOA,KAAK;AAChB;AACA,SAAS+mB,aAAaA,CAAChE,IAAI,EAAE/iB,KAAK,EAAE4C,MAAM,EAAE8jB,WAAW,EAAE;EACrD,MAAM;IAAEZ,MAAM;IAAGC,QAAQ;IAAGC,SAAS;IAAGnN,YAAY,EAAEN;EAAa,CAAC,GAAG3V,MAAM;EAC7E,IAAI,OAAOmjB,QAAQ,CAACvjB,KAAK,KAAK,WAAW,IAAIkkB,WAAW,CAAC3D,IAAI,CAAC,EAAE;IAC5D,OAAO/iB,KAAK,CAAC+lB,QAAQ,CAACvjB,KAAK,GAAGxC,KAAK,CAACgC,MAAM,CAAC;EAC/C,CAAC,MAAM,IAAIvB,QAAQ,CAACT,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3B;IACA,MAAMqnB,GAAG,GAAGrnB,KAAK;IACjB,MAAMkkB,MAAM,GAAG4B,MAAM,CAACnB,OAAO,CAAC2C,MAAM,CAAEtd,CAAC,IAAGA,CAAC,KAAKqd,GAAG,CAAC;IACpDrnB,KAAK,GAAG,EAAE;IACV,KAAK,MAAM6F,IAAI,IAAIwhB,GAAG,EAAC;MACnB,MAAMniB,QAAQ,GAAGkiB,iBAAiB,CAAClD,MAAM,EAAE4B,MAAM,EAAE/C,IAAI,EAAEld,IAAI,CAAC;MAC9D7F,KAAK,CAAC8E,IAAI,CAAC4gB,cAAc,CAACxgB,QAAQ,EAAE6gB,QAAQ,EAAEC,SAAS,IAAIA,SAAS,CAACjD,IAAI,CAAC,EAAExK,WAAW,CAAC,CAAC;IAC7F;EACJ;EACA,OAAOvY,KAAK;AAChB;AACA,SAASunB,eAAeA,CAACnE,QAAQ,EAAEL,IAAI,EAAE/iB,KAAK,EAAE;EAC5C,OAAOwF,UAAU,CAAC4d,QAAQ,CAAC,GAAGA,QAAQ,CAACL,IAAI,EAAE/iB,KAAK,CAAC,GAAGojB,QAAQ;AAClE;AACA,MAAMoE,QAAQ,GAAGA,CAACvkB,GAAG,EAAEwkB,MAAM,KAAGxkB,GAAG,KAAK,IAAI,GAAGwkB,MAAM,GAAG,OAAOxkB,GAAG,KAAK,QAAQ,GAAGgC,gBAAgB,CAACwiB,MAAM,EAAExkB,GAAG,CAAC,GAAGgB,SAAS;AAC3H,SAASyjB,SAASA,CAACpb,GAAG,EAAEqb,YAAY,EAAE1kB,GAAG,EAAE2kB,cAAc,EAAE5nB,KAAK,EAAE;EAC9D,KAAK,MAAMynB,MAAM,IAAIE,YAAY,EAAC;IAC9B,MAAM5jB,KAAK,GAAGyjB,QAAQ,CAACvkB,GAAG,EAAEwkB,MAAM,CAAC;IACnC,IAAI1jB,KAAK,EAAE;MACPuI,GAAG,CAACwX,GAAG,CAAC/f,KAAK,CAAC;MACd,MAAMqf,QAAQ,GAAGmE,eAAe,CAACxjB,KAAK,CAACiP,SAAS,EAAE/P,GAAG,EAAEjD,KAAK,CAAC;MAC7D,IAAI,OAAOojB,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAKngB,GAAG,IAAImgB,QAAQ,KAAKwE,cAAc,EAAE;QACpF;QACA;QACA,OAAOxE,QAAQ;MACnB;IACJ,CAAC,MAAM,IAAIrf,KAAK,KAAK,KAAK,IAAI,OAAO6jB,cAAc,KAAK,WAAW,IAAI3kB,GAAG,KAAK2kB,cAAc,EAAE;MAC3F;MACA;MACA,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA,SAASR,iBAAiBA,CAACO,YAAY,EAAEziB,QAAQ,EAAE6d,IAAI,EAAE/iB,KAAK,EAAE;EAC5D,MAAMokB,UAAU,GAAGlf,QAAQ,CAAC0f,WAAW;EACvC,MAAMxB,QAAQ,GAAGmE,eAAe,CAACriB,QAAQ,CAAC8N,SAAS,EAAE+P,IAAI,EAAE/iB,KAAK,CAAC;EACjE,MAAM6nB,SAAS,GAAG,CACd,GAAGF,YAAY,EACf,GAAGvD,UAAU,CAChB;EACD,MAAM9X,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrBD,GAAG,CAACwX,GAAG,CAAC9jB,KAAK,CAAC;EACd,IAAIiD,GAAG,GAAG6kB,gBAAgB,CAACxb,GAAG,EAAEub,SAAS,EAAE9E,IAAI,EAAEK,QAAQ,IAAIL,IAAI,EAAE/iB,KAAK,CAAC;EACzE,IAAIiD,GAAG,KAAK,IAAI,EAAE;IACd,OAAO,KAAK;EAChB;EACA,IAAI,OAAOmgB,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAKL,IAAI,EAAE;IACtD9f,GAAG,GAAG6kB,gBAAgB,CAACxb,GAAG,EAAEub,SAAS,EAAEzE,QAAQ,EAAEngB,GAAG,EAAEjD,KAAK,CAAC;IAC5D,IAAIiD,GAAG,KAAK,IAAI,EAAE;MACd,OAAO,KAAK;IAChB;EACJ;EACA,OAAOghB,eAAe,CAAC/jB,KAAK,CAACsM,IAAI,CAACF,GAAG,CAAC,EAAE,CACpC,EAAE,CACL,EAAE8X,UAAU,EAAEhB,QAAQ,EAAE,MAAI2E,YAAY,CAAC7iB,QAAQ,EAAE6d,IAAI,EAAE/iB,KAAK,CAAC,CAAC;AACrE;AACA,SAAS8nB,gBAAgBA,CAACxb,GAAG,EAAEub,SAAS,EAAE5kB,GAAG,EAAEmgB,QAAQ,EAAEvd,IAAI,EAAE;EAC3D,OAAM5C,GAAG,EAAC;IACNA,GAAG,GAAGykB,SAAS,CAACpb,GAAG,EAAEub,SAAS,EAAE5kB,GAAG,EAAEmgB,QAAQ,EAAEvd,IAAI,CAAC;EACxD;EACA,OAAO5C,GAAG;AACd;AACA,SAAS8kB,YAAYA,CAAC7iB,QAAQ,EAAE6d,IAAI,EAAE/iB,KAAK,EAAE;EACzC,MAAMynB,MAAM,GAAGviB,QAAQ,CAAC2f,UAAU,CAAC,CAAC;EACpC,IAAI,EAAE9B,IAAI,IAAI0E,MAAM,CAAC,EAAE;IACnBA,MAAM,CAAC1E,IAAI,CAAC,GAAG,CAAC,CAAC;EACrB;EACA,MAAMngB,MAAM,GAAG6kB,MAAM,CAAC1E,IAAI,CAAC;EAC3B,IAAI9iB,OAAO,CAAC2C,MAAM,CAAC,IAAInC,QAAQ,CAACT,KAAK,CAAC,EAAE;IACpC;IACA,OAAOA,KAAK;EAChB;EACA,OAAO4C,MAAM,IAAI,CAAC,CAAC;AACvB;AACA,SAASsiB,oBAAoBA,CAACnC,IAAI,EAAEoB,QAAQ,EAAED,MAAM,EAAEyB,KAAK,EAAE;EACzD,IAAI3lB,KAAK;EACT,KAAK,MAAM4mB,MAAM,IAAIzC,QAAQ,EAAC;IAC1BnkB,KAAK,GAAGukB,QAAQ,CAACoC,OAAO,CAACC,MAAM,EAAE7D,IAAI,CAAC,EAAEmB,MAAM,CAAC;IAC/C,IAAI,OAAOlkB,KAAK,KAAK,WAAW,EAAE;MAC9B,OAAO6mB,gBAAgB,CAAC9D,IAAI,EAAE/iB,KAAK,CAAC,GAAGonB,iBAAiB,CAAClD,MAAM,EAAEyB,KAAK,EAAE5C,IAAI,EAAE/iB,KAAK,CAAC,GAAGA,KAAK;IAChG;EACJ;AACJ;AACA,SAASukB,QAAQA,CAACthB,GAAG,EAAEihB,MAAM,EAAE;EAC3B,KAAK,MAAMngB,KAAK,IAAImgB,MAAM,EAAC;IACvB,IAAI,CAACngB,KAAK,EAAE;MACR;IACJ;IACA,MAAM/D,KAAK,GAAG+D,KAAK,CAACd,GAAG,CAAC;IACxB,IAAI,OAAOjD,KAAK,KAAK,WAAW,EAAE;MAC9B,OAAOA,KAAK;IAChB;EACJ;AACJ;AACA,SAASslB,oBAAoBA,CAAC1iB,MAAM,EAAE;EAClC,IAAIb,IAAI,GAAGa,MAAM,CAACoiB,KAAK;EACvB,IAAI,CAACjjB,IAAI,EAAE;IACPA,IAAI,GAAGa,MAAM,CAACoiB,KAAK,GAAGgD,wBAAwB,CAACplB,MAAM,CAAC+hB,OAAO,CAAC;EAClE;EACA,OAAO5iB,IAAI;AACf;AACA,SAASimB,wBAAwBA,CAAC9D,MAAM,EAAE;EACtC,MAAM5X,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB,KAAK,MAAMxI,KAAK,IAAImgB,MAAM,EAAC;IACvB,KAAK,MAAMjhB,GAAG,IAAI7C,MAAM,CAAC2B,IAAI,CAACgC,KAAK,CAAC,CAACujB,MAAM,CAAEvkB,CAAC,IAAG,CAACA,CAAC,CAACsV,UAAU,CAAC,GAAG,CAAC,CAAC,EAAC;MACjE/L,GAAG,CAACwX,GAAG,CAAC7gB,GAAG,CAAC;IAChB;EACJ;EACA,OAAO/C,KAAK,CAACsM,IAAI,CAACF,GAAG,CAAC;AAC1B;AACA,SAAS2b,2BAA2BA,CAACha,IAAI,EAAE4N,IAAI,EAAEhS,KAAK,EAAEwE,KAAK,EAAE;EAC3D,MAAM;IAAEE;EAAQ,CAAC,GAAGN,IAAI;EACxB,MAAM;IAAEhL,GAAG,GAAE;EAAK,CAAC,GAAG,IAAI,CAACilB,QAAQ;EACnC,MAAMC,MAAM,GAAG,IAAIjoB,KAAK,CAACmO,KAAK,CAAC;EAC/B,IAAIxM,CAAC,EAAEO,IAAI,EAAEI,KAAK,EAAEqD,IAAI;EACxB,KAAIhE,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGiM,KAAK,EAAExM,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;IACnCW,KAAK,GAAGX,CAAC,GAAGgI,KAAK;IACjBhE,IAAI,GAAGgW,IAAI,CAACrZ,KAAK,CAAC;IAClB2lB,MAAM,CAACtmB,CAAC,CAAC,GAAG;MACRumB,CAAC,EAAE7Z,MAAM,CAAC8Z,KAAK,CAACpjB,gBAAgB,CAACY,IAAI,EAAE5C,GAAG,CAAC,EAAET,KAAK;IACtD,CAAC;EACL;EACA,OAAO2lB,MAAM;AACjB;AAEA,MAAMG,OAAO,GAAG3nB,MAAM,CAAC2nB,OAAO,IAAI,KAAK;AACvC,MAAMC,QAAQ,GAAGA,CAACra,MAAM,EAAErM,CAAC,KAAGA,CAAC,GAAGqM,MAAM,CAAClM,MAAM,IAAI,CAACkM,MAAM,CAACrM,CAAC,CAAC,CAAC2mB,IAAI,IAAIta,MAAM,CAACrM,CAAC,CAAC;AAC/E,MAAM4mB,YAAY,GAAIvO,SAAS,IAAGA,SAAS,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AAC/D,SAASwO,WAAWA,CAACC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEnZ,CAAC,EAAE;EACzD;EACA;EACA;EACA,MAAM1L,QAAQ,GAAG2kB,UAAU,CAACH,IAAI,GAAGI,WAAW,GAAGD,UAAU;EAC3D,MAAMjlB,OAAO,GAAGklB,WAAW;EAC3B,MAAME,IAAI,GAAGD,UAAU,CAACL,IAAI,GAAGI,WAAW,GAAGC,UAAU;EACvD,MAAME,GAAG,GAAGxf,qBAAqB,CAAC7F,OAAO,EAAEM,QAAQ,CAAC;EACpD,MAAMglB,GAAG,GAAGzf,qBAAqB,CAACuf,IAAI,EAAEplB,OAAO,CAAC;EAChD,IAAIulB,GAAG,GAAGF,GAAG,IAAIA,GAAG,GAAGC,GAAG,CAAC;EAC3B,IAAIE,GAAG,GAAGF,GAAG,IAAID,GAAG,GAAGC,GAAG,CAAC;EAC3B;EACAC,GAAG,GAAGjhB,KAAK,CAACihB,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG;EAC1BC,GAAG,GAAGlhB,KAAK,CAACkhB,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG;EAC1B,MAAMC,EAAE,GAAGzZ,CAAC,GAAGuZ,GAAG,CAAC,CAAC;EACpB,MAAMG,EAAE,GAAG1Z,CAAC,GAAGwZ,GAAG;EAClB,OAAO;IACHllB,QAAQ,EAAE;MACNM,CAAC,EAAEZ,OAAO,CAACY,CAAC,GAAG6kB,EAAE,IAAIL,IAAI,CAACxkB,CAAC,GAAGN,QAAQ,CAACM,CAAC,CAAC;MACzCE,CAAC,EAAEd,OAAO,CAACc,CAAC,GAAG2kB,EAAE,IAAIL,IAAI,CAACtkB,CAAC,GAAGR,QAAQ,CAACQ,CAAC;IAC5C,CAAC;IACDskB,IAAI,EAAE;MACFxkB,CAAC,EAAEZ,OAAO,CAACY,CAAC,GAAG8kB,EAAE,IAAIN,IAAI,CAACxkB,CAAC,GAAGN,QAAQ,CAACM,CAAC,CAAC;MACzCE,CAAC,EAAEd,OAAO,CAACc,CAAC,GAAG4kB,EAAE,IAAIN,IAAI,CAACtkB,CAAC,GAAGR,QAAQ,CAACQ,CAAC;IAC5C;EACJ,CAAC;AACL;AACA;AACA;AACA;AAAI,SAAS6kB,cAAcA,CAACnb,MAAM,EAAEob,MAAM,EAAEC,EAAE,EAAE;EAC5C,MAAMC,SAAS,GAAGtb,MAAM,CAAClM,MAAM;EAC/B,IAAIynB,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,gBAAgB,EAAEC,YAAY;EACvD,IAAIC,UAAU,GAAGvB,QAAQ,CAACra,MAAM,EAAE,CAAC,CAAC;EACpC,KAAI,IAAIrM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2nB,SAAS,GAAG,CAAC,EAAE,EAAE3nB,CAAC,EAAC;IAClCgoB,YAAY,GAAGC,UAAU;IACzBA,UAAU,GAAGvB,QAAQ,CAACra,MAAM,EAAErM,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAACgoB,YAAY,IAAI,CAACC,UAAU,EAAE;MAC9B;IACJ;IACA,IAAIjjB,YAAY,CAACyiB,MAAM,CAACznB,CAAC,CAAC,EAAE,CAAC,EAAEymB,OAAO,CAAC,EAAE;MACrCiB,EAAE,CAAC1nB,CAAC,CAAC,GAAG0nB,EAAE,CAAC1nB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACrB;IACJ;IACA4nB,MAAM,GAAGF,EAAE,CAAC1nB,CAAC,CAAC,GAAGynB,MAAM,CAACznB,CAAC,CAAC;IAC1B6nB,KAAK,GAAGH,EAAE,CAAC1nB,CAAC,GAAG,CAAC,CAAC,GAAGynB,MAAM,CAACznB,CAAC,CAAC;IAC7B+nB,gBAAgB,GAAG1jB,IAAI,CAACmB,GAAG,CAACoiB,MAAM,EAAE,CAAC,CAAC,GAAGvjB,IAAI,CAACmB,GAAG,CAACqiB,KAAK,EAAE,CAAC,CAAC;IAC3D,IAAIE,gBAAgB,IAAI,CAAC,EAAE;MACvB;IACJ;IACAD,IAAI,GAAG,CAAC,GAAGzjB,IAAI,CAACyB,IAAI,CAACiiB,gBAAgB,CAAC;IACtCL,EAAE,CAAC1nB,CAAC,CAAC,GAAG4nB,MAAM,GAAGE,IAAI,GAAGL,MAAM,CAACznB,CAAC,CAAC;IACjC0nB,EAAE,CAAC1nB,CAAC,GAAG,CAAC,CAAC,GAAG6nB,KAAK,GAAGC,IAAI,GAAGL,MAAM,CAACznB,CAAC,CAAC;EACxC;AACJ;AACA,SAASkoB,eAAeA,CAAC7b,MAAM,EAAEqb,EAAE,EAAErP,SAAS,GAAG,GAAG,EAAE;EAClD,MAAM8P,SAAS,GAAGvB,YAAY,CAACvO,SAAS,CAAC;EACzC,MAAMsP,SAAS,GAAGtb,MAAM,CAAClM,MAAM;EAC/B,IAAIqT,KAAK,EAAE4U,WAAW,EAAEJ,YAAY;EACpC,IAAIC,UAAU,GAAGvB,QAAQ,CAACra,MAAM,EAAE,CAAC,CAAC;EACpC,KAAI,IAAIrM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2nB,SAAS,EAAE,EAAE3nB,CAAC,EAAC;IAC9BooB,WAAW,GAAGJ,YAAY;IAC1BA,YAAY,GAAGC,UAAU;IACzBA,UAAU,GAAGvB,QAAQ,CAACra,MAAM,EAAErM,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAACgoB,YAAY,EAAE;MACf;IACJ;IACA,MAAMK,MAAM,GAAGL,YAAY,CAAC3P,SAAS,CAAC;IACtC,MAAMiQ,MAAM,GAAGN,YAAY,CAACG,SAAS,CAAC;IACtC,IAAIC,WAAW,EAAE;MACb5U,KAAK,GAAG,CAAC6U,MAAM,GAAGD,WAAW,CAAC/P,SAAS,CAAC,IAAI,CAAC;MAC7C2P,YAAY,CAAC,MAAM3P,SAAS,EAAE,CAAC,GAAGgQ,MAAM,GAAG7U,KAAK;MAChDwU,YAAY,CAAC,MAAMG,SAAS,EAAE,CAAC,GAAGG,MAAM,GAAG9U,KAAK,GAAGkU,EAAE,CAAC1nB,CAAC,CAAC;IAC5D;IACA,IAAIioB,UAAU,EAAE;MACZzU,KAAK,GAAG,CAACyU,UAAU,CAAC5P,SAAS,CAAC,GAAGgQ,MAAM,IAAI,CAAC;MAC5CL,YAAY,CAAC,MAAM3P,SAAS,EAAE,CAAC,GAAGgQ,MAAM,GAAG7U,KAAK;MAChDwU,YAAY,CAAC,MAAMG,SAAS,EAAE,CAAC,GAAGG,MAAM,GAAG9U,KAAK,GAAGkU,EAAE,CAAC1nB,CAAC,CAAC;IAC5D;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASuoB,mBAAmBA,CAAClc,MAAM,EAAEgM,SAAS,GAAG,GAAG,EAAE;EACtD,MAAM8P,SAAS,GAAGvB,YAAY,CAACvO,SAAS,CAAC;EACzC,MAAMsP,SAAS,GAAGtb,MAAM,CAAClM,MAAM;EAC/B,MAAMsnB,MAAM,GAAGppB,KAAK,CAACspB,SAAS,CAAC,CAACzK,IAAI,CAAC,CAAC,CAAC;EACvC,MAAMwK,EAAE,GAAGrpB,KAAK,CAACspB,SAAS,CAAC;EAC3B;EACA,IAAI3nB,CAAC,EAAEooB,WAAW,EAAEJ,YAAY;EAChC,IAAIC,UAAU,GAAGvB,QAAQ,CAACra,MAAM,EAAE,CAAC,CAAC;EACpC,KAAIrM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2nB,SAAS,EAAE,EAAE3nB,CAAC,EAAC;IAC1BooB,WAAW,GAAGJ,YAAY;IAC1BA,YAAY,GAAGC,UAAU;IACzBA,UAAU,GAAGvB,QAAQ,CAACra,MAAM,EAAErM,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAACgoB,YAAY,EAAE;MACf;IACJ;IACA,IAAIC,UAAU,EAAE;MACZ,MAAMO,UAAU,GAAGP,UAAU,CAAC5P,SAAS,CAAC,GAAG2P,YAAY,CAAC3P,SAAS,CAAC;MAClE;MACAoP,MAAM,CAACznB,CAAC,CAAC,GAAGwoB,UAAU,KAAK,CAAC,GAAG,CAACP,UAAU,CAACE,SAAS,CAAC,GAAGH,YAAY,CAACG,SAAS,CAAC,IAAIK,UAAU,GAAG,CAAC;IACrG;IACAd,EAAE,CAAC1nB,CAAC,CAAC,GAAG,CAACooB,WAAW,GAAGX,MAAM,CAACznB,CAAC,CAAC,GAAG,CAACioB,UAAU,GAAGR,MAAM,CAACznB,CAAC,GAAG,CAAC,CAAC,GAAG+E,IAAI,CAAC0iB,MAAM,CAACznB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK+E,IAAI,CAAC0iB,MAAM,CAACznB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAACynB,MAAM,CAACznB,CAAC,GAAG,CAAC,CAAC,GAAGynB,MAAM,CAACznB,CAAC,CAAC,IAAI,CAAC;EAClJ;EACAwnB,cAAc,CAACnb,MAAM,EAAEob,MAAM,EAAEC,EAAE,CAAC;EAClCQ,eAAe,CAAC7b,MAAM,EAAEqb,EAAE,EAAErP,SAAS,CAAC;AAC1C;AACA,SAASoQ,eAAeA,CAACC,EAAE,EAAEjiB,GAAG,EAAEC,GAAG,EAAE;EACnC,OAAOrC,IAAI,CAACqC,GAAG,CAACrC,IAAI,CAACoC,GAAG,CAACiiB,EAAE,EAAEhiB,GAAG,CAAC,EAAED,GAAG,CAAC;AAC3C;AACA,SAASkiB,eAAeA,CAACtc,MAAM,EAAEkR,IAAI,EAAE;EACnC,IAAIvd,CAAC,EAAEO,IAAI,EAAE+c,KAAK,EAAEsL,MAAM,EAAEC,UAAU;EACtC,IAAIC,UAAU,GAAGzL,cAAc,CAAChR,MAAM,CAAC,CAAC,CAAC,EAAEkR,IAAI,CAAC;EAChD,KAAIvd,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAG8L,MAAM,CAAClM,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;IAC3C6oB,UAAU,GAAGD,MAAM;IACnBA,MAAM,GAAGE,UAAU;IACnBA,UAAU,GAAG9oB,CAAC,GAAGO,IAAI,GAAG,CAAC,IAAI8c,cAAc,CAAChR,MAAM,CAACrM,CAAC,GAAG,CAAC,CAAC,EAAEud,IAAI,CAAC;IAChE,IAAI,CAACqL,MAAM,EAAE;MACT;IACJ;IACAtL,KAAK,GAAGjR,MAAM,CAACrM,CAAC,CAAC;IACjB,IAAI6oB,UAAU,EAAE;MACZvL,KAAK,CAACU,IAAI,GAAGyK,eAAe,CAACnL,KAAK,CAACU,IAAI,EAAET,IAAI,CAACxR,IAAI,EAAEwR,IAAI,CAACvR,KAAK,CAAC;MAC/DsR,KAAK,CAACY,IAAI,GAAGuK,eAAe,CAACnL,KAAK,CAACY,IAAI,EAAEX,IAAI,CAACrL,GAAG,EAAEqL,IAAI,CAACpL,MAAM,CAAC;IACnE;IACA,IAAI2W,UAAU,EAAE;MACZxL,KAAK,CAACW,IAAI,GAAGwK,eAAe,CAACnL,KAAK,CAACW,IAAI,EAAEV,IAAI,CAACxR,IAAI,EAAEwR,IAAI,CAACvR,KAAK,CAAC;MAC/DsR,KAAK,CAACa,IAAI,GAAGsK,eAAe,CAACnL,KAAK,CAACa,IAAI,EAAEZ,IAAI,CAACrL,GAAG,EAAEqL,IAAI,CAACpL,MAAM,CAAC;IACnE;EACJ;AACJ;AACA;AACA;AACA;AAAI,SAAS4W,0BAA0BA,CAAC1c,MAAM,EAAE9K,OAAO,EAAEgc,IAAI,EAAEvM,IAAI,EAAEqH,SAAS,EAAE;EAC5E,IAAIrY,CAAC,EAAEO,IAAI,EAAE+c,KAAK,EAAE0L,aAAa;EACjC;EACA,IAAIznB,OAAO,CAAC0nB,QAAQ,EAAE;IAClB5c,MAAM,GAAGA,MAAM,CAACoZ,MAAM,CAAEiD,EAAE,IAAG,CAACA,EAAE,CAAC/B,IAAI,CAAC;EAC1C;EACA,IAAIplB,OAAO,CAAC2nB,sBAAsB,KAAK,UAAU,EAAE;IAC/CX,mBAAmB,CAAClc,MAAM,EAAEgM,SAAS,CAAC;EAC1C,CAAC,MAAM;IACH,IAAI8Q,IAAI,GAAGnY,IAAI,GAAG3E,MAAM,CAACA,MAAM,CAAClM,MAAM,GAAG,CAAC,CAAC,GAAGkM,MAAM,CAAC,CAAC,CAAC;IACvD,KAAIrM,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAG8L,MAAM,CAAClM,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;MAC3Csd,KAAK,GAAGjR,MAAM,CAACrM,CAAC,CAAC;MACjBgpB,aAAa,GAAGnC,WAAW,CAACsC,IAAI,EAAE7L,KAAK,EAAEjR,MAAM,CAAChI,IAAI,CAACoC,GAAG,CAACzG,CAAC,GAAG,CAAC,EAAEO,IAAI,IAAIyQ,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGzQ,IAAI,CAAC,EAAEgB,OAAO,CAAC6nB,OAAO,CAAC;MAChH9L,KAAK,CAACU,IAAI,GAAGgL,aAAa,CAAC7mB,QAAQ,CAACM,CAAC;MACrC6a,KAAK,CAACY,IAAI,GAAG8K,aAAa,CAAC7mB,QAAQ,CAACQ,CAAC;MACrC2a,KAAK,CAACW,IAAI,GAAG+K,aAAa,CAAC/B,IAAI,CAACxkB,CAAC;MACjC6a,KAAK,CAACa,IAAI,GAAG6K,aAAa,CAAC/B,IAAI,CAACtkB,CAAC;MACjCwmB,IAAI,GAAG7L,KAAK;IAChB;EACJ;EACA,IAAI/b,OAAO,CAAConB,eAAe,EAAE;IACzBA,eAAe,CAACtc,MAAM,EAAEkR,IAAI,CAAC;EACjC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GANA,CAMI;AACJ;AACA;AAAI,SAAS8L,eAAeA,CAAA,EAAG;EAC3B,OAAO,OAAOpe,MAAM,KAAK,WAAW,IAAI,OAAOqe,QAAQ,KAAK,WAAW;AAC3E;AACA;AACA;AACA;AAAI,SAASC,cAAcA,CAACC,OAAO,EAAE;EACjC,IAAI5D,MAAM,GAAG4D,OAAO,CAACC,UAAU;EAC/B,IAAI7D,MAAM,IAAIA,MAAM,CAACnnB,QAAQ,CAAC,CAAC,KAAK,qBAAqB,EAAE;IACvDmnB,MAAM,GAAGA,MAAM,CAAC8D,IAAI;EACxB;EACA,OAAO9D,MAAM;AACjB;AACA;AACA;AACA;AACA;AAAI,SAAS+D,aAAaA,CAACC,UAAU,EAAEhT,IAAI,EAAEiT,cAAc,EAAE;EACzD,IAAIC,aAAa;EACjB,IAAI,OAAOF,UAAU,KAAK,QAAQ,EAAE;IAChCE,aAAa,GAAGtI,QAAQ,CAACoI,UAAU,EAAE,EAAE,CAAC;IACxC,IAAIA,UAAU,CAACvoB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAChC;MACAyoB,aAAa,GAAGA,aAAa,GAAG,GAAG,GAAGlT,IAAI,CAAC6S,UAAU,CAACI,cAAc,CAAC;IACzE;EACJ,CAAC,MAAM;IACHC,aAAa,GAAGF,UAAU;EAC9B;EACA,OAAOE,aAAa;AACxB;AACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAGA,OAAO,CAACC,aAAa,CAACC,WAAW,CAACH,gBAAgB,CAACC,OAAO,EAAE,IAAI,CAAC;AACrG,SAASG,QAAQA,CAACC,EAAE,EAAE5jB,QAAQ,EAAE;EAC5B,OAAOujB,gBAAgB,CAACK,EAAE,CAAC,CAACC,gBAAgB,CAAC7jB,QAAQ,CAAC;AAC1D;AACA,MAAM8jB,SAAS,GAAG,CACd,KAAK,EACL,OAAO,EACP,QAAQ,EACR,MAAM,CACT;AACD,SAASC,kBAAkBA,CAACC,MAAM,EAAE3S,KAAK,EAAE4S,MAAM,EAAE;EAC/C,MAAM5kB,MAAM,GAAG,CAAC,CAAC;EACjB4kB,MAAM,GAAGA,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE;EACnC,KAAI,IAAIzqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAC;IACtB,MAAM0qB,GAAG,GAAGJ,SAAS,CAACtqB,CAAC,CAAC;IACxB6F,MAAM,CAAC6kB,GAAG,CAAC,GAAGprB,UAAU,CAACkrB,MAAM,CAAC3S,KAAK,GAAG,GAAG,GAAG6S,GAAG,GAAGD,MAAM,CAAC,CAAC,IAAI,CAAC;EACrE;EACA5kB,MAAM,CAACyP,KAAK,GAAGzP,MAAM,CAACkG,IAAI,GAAGlG,MAAM,CAACmG,KAAK;EACzCnG,MAAM,CAAC6V,MAAM,GAAG7V,MAAM,CAACqM,GAAG,GAAGrM,MAAM,CAACsM,MAAM;EAC1C,OAAOtM,MAAM;AACjB;AACA,MAAM8kB,YAAY,GAAGA,CAACloB,CAAC,EAAEE,CAAC,EAAE5B,MAAM,KAAG,CAAC0B,CAAC,GAAG,CAAC,IAAIE,CAAC,GAAG,CAAC,MAAM,CAAC5B,MAAM,IAAI,CAACA,MAAM,CAAC6pB,UAAU,CAAC;AACxF;AACA;AACA;AACA;AACA;AAAI,SAASC,iBAAiBA,CAAC1mB,CAAC,EAAEmX,MAAM,EAAE;EACtC,MAAMwP,OAAO,GAAG3mB,CAAC,CAAC2mB,OAAO;EACzB,MAAMjqB,MAAM,GAAGiqB,OAAO,IAAIA,OAAO,CAAC3qB,MAAM,GAAG2qB,OAAO,CAAC,CAAC,CAAC,GAAG3mB,CAAC;EACzD,MAAM;IAAE4mB,OAAO;IAAGC;EAAS,CAAC,GAAGnqB,MAAM;EACrC,IAAIoqB,GAAG,GAAG,KAAK;EACf,IAAIxoB,CAAC,EAAEE,CAAC;EACR,IAAIgoB,YAAY,CAACI,OAAO,EAAEC,OAAO,EAAE7mB,CAAC,CAACpD,MAAM,CAAC,EAAE;IAC1C0B,CAAC,GAAGsoB,OAAO;IACXpoB,CAAC,GAAGqoB,OAAO;EACf,CAAC,MAAM;IACH,MAAM/N,IAAI,GAAG3B,MAAM,CAAC4P,qBAAqB,CAAC,CAAC;IAC3CzoB,CAAC,GAAG5B,MAAM,CAACsqB,OAAO,GAAGlO,IAAI,CAAClR,IAAI;IAC9BpJ,CAAC,GAAG9B,MAAM,CAACuqB,OAAO,GAAGnO,IAAI,CAAC/K,GAAG;IAC7B+Y,GAAG,GAAG,IAAI;EACd;EACA,OAAO;IACHxoB,CAAC;IACDE,CAAC;IACDsoB;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASI,mBAAmBA,CAACC,KAAK,EAAEhY,KAAK,EAAE;EAC3C,IAAI,QAAQ,IAAIgY,KAAK,EAAE;IACnB,OAAOA,KAAK;EAChB;EACA,MAAM;IAAEhQ,MAAM;IAAGH;EAAyB,CAAC,GAAG7H,KAAK;EACnD,MAAMuE,KAAK,GAAGkS,gBAAgB,CAACzO,MAAM,CAAC;EACtC,MAAMiQ,SAAS,GAAG1T,KAAK,CAAC2T,SAAS,KAAK,YAAY;EAClD,MAAMC,QAAQ,GAAGlB,kBAAkB,CAAC1S,KAAK,EAAE,SAAS,CAAC;EACrD,MAAM6T,OAAO,GAAGnB,kBAAkB,CAAC1S,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;EAC5D,MAAM;IAAEpV,CAAC;IAAGE,CAAC;IAAGsoB;EAAK,CAAC,GAAGJ,iBAAiB,CAACS,KAAK,EAAEhQ,MAAM,CAAC;EACzD,MAAMQ,OAAO,GAAG2P,QAAQ,CAAC1f,IAAI,IAAIkf,GAAG,IAAIS,OAAO,CAAC3f,IAAI,CAAC;EACrD,MAAMgQ,OAAO,GAAG0P,QAAQ,CAACvZ,GAAG,IAAI+Y,GAAG,IAAIS,OAAO,CAACxZ,GAAG,CAAC;EACnD,IAAI;IAAEoD,KAAK;IAAGoG;EAAQ,CAAC,GAAGpI,KAAK;EAC/B,IAAIiY,SAAS,EAAE;IACXjW,KAAK,IAAImW,QAAQ,CAACnW,KAAK,GAAGoW,OAAO,CAACpW,KAAK;IACvCoG,MAAM,IAAI+P,QAAQ,CAAC/P,MAAM,GAAGgQ,OAAO,CAAChQ,MAAM;EAC9C;EACA,OAAO;IACHjZ,CAAC,EAAE4B,IAAI,CAACiB,KAAK,CAAC,CAAC7C,CAAC,GAAGqZ,OAAO,IAAIxG,KAAK,GAAGgG,MAAM,CAAChG,KAAK,GAAG6F,uBAAuB,CAAC;IAC7ExY,CAAC,EAAE0B,IAAI,CAACiB,KAAK,CAAC,CAAC3C,CAAC,GAAGoZ,OAAO,IAAIL,MAAM,GAAGJ,MAAM,CAACI,MAAM,GAAGP,uBAAuB;EAClF,CAAC;AACL;AACA,SAASwQ,gBAAgBA,CAACrQ,MAAM,EAAEhG,KAAK,EAAEoG,MAAM,EAAE;EAC7C,IAAIqE,QAAQ,EAAE6L,SAAS;EACvB,IAAItW,KAAK,KAAKlT,SAAS,IAAIsZ,MAAM,KAAKtZ,SAAS,EAAE;IAC7C,MAAMypB,SAAS,GAAGvQ,MAAM,IAAIiO,cAAc,CAACjO,MAAM,CAAC;IAClD,IAAI,CAACuQ,SAAS,EAAE;MACZvW,KAAK,GAAGgG,MAAM,CAACwQ,WAAW;MAC1BpQ,MAAM,GAAGJ,MAAM,CAACyQ,YAAY;IAChC,CAAC,MAAM;MACH,MAAM9O,IAAI,GAAG4O,SAAS,CAACX,qBAAqB,CAAC,CAAC,CAAC,CAAC;MAChD,MAAMc,cAAc,GAAGjC,gBAAgB,CAAC8B,SAAS,CAAC;MAClD,MAAMI,eAAe,GAAG1B,kBAAkB,CAACyB,cAAc,EAAE,QAAQ,EAAE,OAAO,CAAC;MAC7E,MAAME,gBAAgB,GAAG3B,kBAAkB,CAACyB,cAAc,EAAE,SAAS,CAAC;MACtE1W,KAAK,GAAG2H,IAAI,CAAC3H,KAAK,GAAG4W,gBAAgB,CAAC5W,KAAK,GAAG2W,eAAe,CAAC3W,KAAK;MACnEoG,MAAM,GAAGuB,IAAI,CAACvB,MAAM,GAAGwQ,gBAAgB,CAACxQ,MAAM,GAAGuQ,eAAe,CAACvQ,MAAM;MACvEqE,QAAQ,GAAG4J,aAAa,CAACqC,cAAc,CAACjM,QAAQ,EAAE8L,SAAS,EAAE,aAAa,CAAC;MAC3ED,SAAS,GAAGjC,aAAa,CAACqC,cAAc,CAACJ,SAAS,EAAEC,SAAS,EAAE,cAAc,CAAC;IAClF;EACJ;EACA,OAAO;IACHvW,KAAK;IACLoG,MAAM;IACNqE,QAAQ,EAAEA,QAAQ,IAAIvb,QAAQ;IAC9BonB,SAAS,EAAEA,SAAS,IAAIpnB;EAC5B,CAAC;AACL;AACA,MAAM2nB,MAAM,GAAI3pB,CAAC,IAAG6B,IAAI,CAACiB,KAAK,CAAC9C,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;AAC3C;AACA,SAAS4pB,cAAcA,CAAC9Q,MAAM,EAAE+Q,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EAC5D,MAAM1U,KAAK,GAAGkS,gBAAgB,CAACzO,MAAM,CAAC;EACtC,MAAMkR,OAAO,GAAGjC,kBAAkB,CAAC1S,KAAK,EAAE,QAAQ,CAAC;EACnD,MAAMkI,QAAQ,GAAG4J,aAAa,CAAC9R,KAAK,CAACkI,QAAQ,EAAEzE,MAAM,EAAE,aAAa,CAAC,IAAI9W,QAAQ;EACjF,MAAMonB,SAAS,GAAGjC,aAAa,CAAC9R,KAAK,CAAC+T,SAAS,EAAEtQ,MAAM,EAAE,cAAc,CAAC,IAAI9W,QAAQ;EACpF,MAAMioB,aAAa,GAAGd,gBAAgB,CAACrQ,MAAM,EAAE+Q,OAAO,EAAEC,QAAQ,CAAC;EACjE,IAAI;IAAEhX,KAAK;IAAGoG;EAAQ,CAAC,GAAG+Q,aAAa;EACvC,IAAI5U,KAAK,CAAC2T,SAAS,KAAK,aAAa,EAAE;IACnC,MAAME,OAAO,GAAGnB,kBAAkB,CAAC1S,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;IAC5D,MAAM4T,QAAQ,GAAGlB,kBAAkB,CAAC1S,KAAK,EAAE,SAAS,CAAC;IACrDvC,KAAK,IAAImW,QAAQ,CAACnW,KAAK,GAAGoW,OAAO,CAACpW,KAAK;IACvCoG,MAAM,IAAI+P,QAAQ,CAAC/P,MAAM,GAAGgQ,OAAO,CAAChQ,MAAM;EAC9C;EACApG,KAAK,GAAGjR,IAAI,CAACqC,GAAG,CAAC,CAAC,EAAE4O,KAAK,GAAGkX,OAAO,CAAClX,KAAK,CAAC;EAC1CoG,MAAM,GAAGrX,IAAI,CAACqC,GAAG,CAAC,CAAC,EAAE6lB,WAAW,GAAGjX,KAAK,GAAGiX,WAAW,GAAG7Q,MAAM,GAAG8Q,OAAO,CAAC9Q,MAAM,CAAC;EACjFpG,KAAK,GAAG6W,MAAM,CAAC9nB,IAAI,CAACoC,GAAG,CAAC6O,KAAK,EAAEyK,QAAQ,EAAE0M,aAAa,CAAC1M,QAAQ,CAAC,CAAC;EACjErE,MAAM,GAAGyQ,MAAM,CAAC9nB,IAAI,CAACoC,GAAG,CAACiV,MAAM,EAAEkQ,SAAS,EAAEa,aAAa,CAACb,SAAS,CAAC,CAAC;EACrE,IAAItW,KAAK,IAAI,CAACoG,MAAM,EAAE;IAClB;IACA;IACAA,MAAM,GAAGyQ,MAAM,CAAC7W,KAAK,GAAG,CAAC,CAAC;EAC9B;EACA,MAAMoX,cAAc,GAAGL,OAAO,KAAKjqB,SAAS,IAAIkqB,QAAQ,KAAKlqB,SAAS;EACtE,IAAIsqB,cAAc,IAAIH,WAAW,IAAIE,aAAa,CAAC/Q,MAAM,IAAIA,MAAM,GAAG+Q,aAAa,CAAC/Q,MAAM,EAAE;IACxFA,MAAM,GAAG+Q,aAAa,CAAC/Q,MAAM;IAC7BpG,KAAK,GAAG6W,MAAM,CAAC9nB,IAAI,CAACoB,KAAK,CAACiW,MAAM,GAAG6Q,WAAW,CAAC,CAAC;EACpD;EACA,OAAO;IACHjX,KAAK;IACLoG;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASiR,WAAWA,CAACrZ,KAAK,EAAEsZ,UAAU,EAAEC,UAAU,EAAE;EACpD,MAAMC,UAAU,GAAGF,UAAU,IAAI,CAAC;EAClC,MAAMG,YAAY,GAAG1oB,IAAI,CAACoB,KAAK,CAAC6N,KAAK,CAACoI,MAAM,GAAGoR,UAAU,CAAC;EAC1D,MAAME,WAAW,GAAG3oB,IAAI,CAACoB,KAAK,CAAC6N,KAAK,CAACgC,KAAK,GAAGwX,UAAU,CAAC;EACxDxZ,KAAK,CAACoI,MAAM,GAAGrX,IAAI,CAACoB,KAAK,CAAC6N,KAAK,CAACoI,MAAM,CAAC;EACvCpI,KAAK,CAACgC,KAAK,GAAGjR,IAAI,CAACoB,KAAK,CAAC6N,KAAK,CAACgC,KAAK,CAAC;EACrC,MAAMgG,MAAM,GAAGhI,KAAK,CAACgI,MAAM;EAC3B;EACA;EACA;EACA,IAAIA,MAAM,CAACzD,KAAK,KAAKgV,UAAU,IAAI,CAACvR,MAAM,CAACzD,KAAK,CAAC6D,MAAM,IAAI,CAACJ,MAAM,CAACzD,KAAK,CAACvC,KAAK,CAAC,EAAE;IAC7EgG,MAAM,CAACzD,KAAK,CAAC6D,MAAM,GAAG,GAAGpI,KAAK,CAACoI,MAAM,IAAI;IACzCJ,MAAM,CAACzD,KAAK,CAACvC,KAAK,GAAG,GAAGhC,KAAK,CAACgC,KAAK,IAAI;EAC3C;EACA,IAAIhC,KAAK,CAAC6H,uBAAuB,KAAK2R,UAAU,IAAIxR,MAAM,CAACI,MAAM,KAAKqR,YAAY,IAAIzR,MAAM,CAAChG,KAAK,KAAK0X,WAAW,EAAE;IAChH1Z,KAAK,CAAC6H,uBAAuB,GAAG2R,UAAU;IAC1CxR,MAAM,CAACI,MAAM,GAAGqR,YAAY;IAC5BzR,MAAM,CAAChG,KAAK,GAAG0X,WAAW;IAC1B1Z,KAAK,CAAC4E,GAAG,CAAC+U,YAAY,CAACH,UAAU,EAAE,CAAC,EAAE,CAAC,EAAEA,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1D,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AAAI,MAAMI,4BAA4B,GAAG,YAAW;EAChD,IAAIC,gBAAgB,GAAG,KAAK;EAC5B,IAAI;IACA,MAAM5rB,OAAO,GAAG;MACZ,IAAI6rB,OAAOA,CAAA,EAAI;QACXD,gBAAgB,GAAG,IAAI;QACvB,OAAO,KAAK;MAChB;IACJ,CAAC;IACD,IAAI9D,eAAe,CAAC,CAAC,EAAE;MACnBpe,MAAM,CAACoiB,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE9rB,OAAO,CAAC;MAC9C0J,MAAM,CAACqiB,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE/rB,OAAO,CAAC;IACrD;EACJ,CAAC,CAAC,OAAO4C,CAAC,EAAE;IACZ;EAAA;EAEA,OAAOgpB,gBAAgB;AAC3B,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASI,YAAYA,CAACvD,OAAO,EAAExjB,QAAQ,EAAE;EACzC,MAAMrI,KAAK,GAAGgsB,QAAQ,CAACH,OAAO,EAAExjB,QAAQ,CAAC;EACzC,MAAMka,OAAO,GAAGviB,KAAK,IAAIA,KAAK,CAACwiB,KAAK,CAAC,mBAAmB,CAAC;EACzD,OAAOD,OAAO,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAGte,SAAS;AAC5C;;AAEA;AACA;AACA;AAAI,SAASorB,YAAYA,CAACC,EAAE,EAAEC,EAAE,EAAE7f,CAAC,EAAE0K,IAAI,EAAE;EACvC,OAAO;IACH9V,CAAC,EAAEgrB,EAAE,CAAChrB,CAAC,GAAGoL,CAAC,IAAI6f,EAAE,CAACjrB,CAAC,GAAGgrB,EAAE,CAAChrB,CAAC,CAAC;IAC3BE,CAAC,EAAE8qB,EAAE,CAAC9qB,CAAC,GAAGkL,CAAC,IAAI6f,EAAE,CAAC/qB,CAAC,GAAG8qB,EAAE,CAAC9qB,CAAC;EAC9B,CAAC;AACL;AACA;AACA;AACA;AAAI,SAASgrB,qBAAqBA,CAACF,EAAE,EAAEC,EAAE,EAAE7f,CAAC,EAAE0K,IAAI,EAAE;EAChD,OAAO;IACH9V,CAAC,EAAEgrB,EAAE,CAAChrB,CAAC,GAAGoL,CAAC,IAAI6f,EAAE,CAACjrB,CAAC,GAAGgrB,EAAE,CAAChrB,CAAC,CAAC;IAC3BE,CAAC,EAAE4V,IAAI,KAAK,QAAQ,GAAG1K,CAAC,GAAG,GAAG,GAAG4f,EAAE,CAAC9qB,CAAC,GAAG+qB,EAAE,CAAC/qB,CAAC,GAAG4V,IAAI,KAAK,OAAO,GAAG1K,CAAC,GAAG,CAAC,GAAG4f,EAAE,CAAC9qB,CAAC,GAAG+qB,EAAE,CAAC/qB,CAAC,GAAGkL,CAAC,GAAG,CAAC,GAAG6f,EAAE,CAAC/qB,CAAC,GAAG8qB,EAAE,CAAC9qB;EAC9G,CAAC;AACL;AACA;AACA;AACA;AAAI,SAASirB,oBAAoBA,CAACH,EAAE,EAAEC,EAAE,EAAE7f,CAAC,EAAE0K,IAAI,EAAE;EAC/C,MAAMsV,GAAG,GAAG;IACRprB,CAAC,EAAEgrB,EAAE,CAACxP,IAAI;IACVtb,CAAC,EAAE8qB,EAAE,CAACtP;EACV,CAAC;EACD,MAAM2P,GAAG,GAAG;IACRrrB,CAAC,EAAEirB,EAAE,CAAC1P,IAAI;IACVrb,CAAC,EAAE+qB,EAAE,CAACxP;EACV,CAAC;EACD,MAAMra,CAAC,GAAG2pB,YAAY,CAACC,EAAE,EAAEI,GAAG,EAAEhgB,CAAC,CAAC;EAClC,MAAM/J,CAAC,GAAG0pB,YAAY,CAACK,GAAG,EAAEC,GAAG,EAAEjgB,CAAC,CAAC;EACnC,MAAMkgB,CAAC,GAAGP,YAAY,CAACM,GAAG,EAAEJ,EAAE,EAAE7f,CAAC,CAAC;EAClC,MAAMqC,CAAC,GAAGsd,YAAY,CAAC3pB,CAAC,EAAEC,CAAC,EAAE+J,CAAC,CAAC;EAC/B,MAAM1J,CAAC,GAAGqpB,YAAY,CAAC1pB,CAAC,EAAEiqB,CAAC,EAAElgB,CAAC,CAAC;EAC/B,OAAO2f,YAAY,CAACtd,CAAC,EAAE/L,CAAC,EAAE0J,CAAC,CAAC;AAChC;AAEA,MAAMmgB,qBAAqB,GAAG,SAAAA,CAASC,KAAK,EAAE3Y,KAAK,EAAE;EACjD,OAAO;IACH7S,CAACA,CAAEA,CAAC,EAAE;MACF,OAAOwrB,KAAK,GAAGA,KAAK,GAAG3Y,KAAK,GAAG7S,CAAC;IACpC,CAAC;IACDyrB,QAAQA,CAAErS,CAAC,EAAE;MACTvG,KAAK,GAAGuG,CAAC;IACb,CAAC;IACD2C,SAASA,CAAE5S,KAAK,EAAE;MACd,IAAIA,KAAK,KAAK,QAAQ,EAAE;QACpB,OAAOA,KAAK;MAChB;MACA,OAAOA,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAC/C,CAAC;IACDuiB,KAAKA,CAAE1rB,CAAC,EAAEtE,KAAK,EAAE;MACb,OAAOsE,CAAC,GAAGtE,KAAK;IACpB,CAAC;IACDiwB,UAAUA,CAAE3rB,CAAC,EAAE4rB,SAAS,EAAE;MACtB,OAAO5rB,CAAC,GAAG4rB,SAAS;IACxB;EACJ,CAAC;AACL,CAAC;AACD,MAAMC,qBAAqB,GAAG,SAAAA,CAAA,EAAW;EACrC,OAAO;IACH7rB,CAACA,CAAEA,CAAC,EAAE;MACF,OAAOA,CAAC;IACZ,CAAC;IACDyrB,QAAQA,CAAErS,CAAC,EAAE,CAAC,CAAC;IACf2C,SAASA,CAAE5S,KAAK,EAAE;MACd,OAAOA,KAAK;IAChB,CAAC;IACDuiB,KAAKA,CAAE1rB,CAAC,EAAEtE,KAAK,EAAE;MACb,OAAOsE,CAAC,GAAGtE,KAAK;IACpB,CAAC;IACDiwB,UAAUA,CAAE3rB,CAAC,EAAE8rB,UAAU,EAAE;MACvB,OAAO9rB,CAAC;IACZ;EACJ,CAAC;AACL,CAAC;AACD,SAAS+rB,aAAaA,CAACviB,GAAG,EAAEgiB,KAAK,EAAE3Y,KAAK,EAAE;EACtC,OAAOrJ,GAAG,GAAG+hB,qBAAqB,CAACC,KAAK,EAAE3Y,KAAK,CAAC,GAAGgZ,qBAAqB,CAAC,CAAC;AAC9E;AACA,SAASG,qBAAqBA,CAACvW,GAAG,EAAEwW,SAAS,EAAE;EAC3C,IAAI7W,KAAK,EAAE8W,QAAQ;EACnB,IAAID,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,KAAK,EAAE;IAC5C7W,KAAK,GAAGK,GAAG,CAACoD,MAAM,CAACzD,KAAK;IACxB8W,QAAQ,GAAG,CACP9W,KAAK,CAACwS,gBAAgB,CAAC,WAAW,CAAC,EACnCxS,KAAK,CAAC+W,mBAAmB,CAAC,WAAW,CAAC,CACzC;IACD/W,KAAK,CAACgX,WAAW,CAAC,WAAW,EAAEH,SAAS,EAAE,WAAW,CAAC;IACtDxW,GAAG,CAAC4W,iBAAiB,GAAGH,QAAQ;EACpC;AACJ;AACA,SAASI,oBAAoBA,CAAC7W,GAAG,EAAEyW,QAAQ,EAAE;EACzC,IAAIA,QAAQ,KAAKvsB,SAAS,EAAE;IACxB,OAAO8V,GAAG,CAAC4W,iBAAiB;IAC5B5W,GAAG,CAACoD,MAAM,CAACzD,KAAK,CAACgX,WAAW,CAAC,WAAW,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;EACvE;AACJ;AAEA,SAASK,UAAUA,CAACxoB,QAAQ,EAAE;EAC1B,IAAIA,QAAQ,KAAK,OAAO,EAAE;IACtB,OAAO;MACHyoB,OAAO,EAAElnB,aAAa;MACtBmnB,OAAO,EAAErnB,UAAU;MACnBsnB,SAAS,EAAErnB;IACf,CAAC;EACL;EACA,OAAO;IACHmnB,OAAO,EAAEvmB,UAAU;IACnBwmB,OAAO,EAAEA,CAACrrB,CAAC,EAAEC,CAAC,KAAGD,CAAC,GAAGC,CAAC;IACtBqrB,SAAS,EAAG1sB,CAAC,IAAGA;EACpB,CAAC;AACL;AACA,SAAS2sB,gBAAgBA,CAAC;EAAEpnB,KAAK;EAAGC,GAAG;EAAGuE,KAAK;EAAGwE,IAAI;EAAG6G;AAAO,CAAC,EAAE;EAC/D,OAAO;IACH7P,KAAK,EAAEA,KAAK,GAAGwE,KAAK;IACpBvE,GAAG,EAAEA,GAAG,GAAGuE,KAAK;IAChBwE,IAAI,EAAEA,IAAI,IAAI,CAAC/I,GAAG,GAAGD,KAAK,GAAG,CAAC,IAAIwE,KAAK,KAAK,CAAC;IAC7CqL;EACJ,CAAC;AACL;AACA,SAASwX,UAAUA,CAACC,OAAO,EAAEjjB,MAAM,EAAEmI,MAAM,EAAE;EACzC,MAAM;IAAEhO,QAAQ;IAAGwB,KAAK,EAAEunB,UAAU;IAAGtnB,GAAG,EAAEunB;EAAU,CAAC,GAAGhb,MAAM;EAChE,MAAM;IAAEya,OAAO;IAAGE;EAAW,CAAC,GAAGH,UAAU,CAACxoB,QAAQ,CAAC;EACrD,MAAMgG,KAAK,GAAGH,MAAM,CAAClM,MAAM;EAC3B,IAAI;IAAE6H,KAAK;IAAGC,GAAG;IAAG+I;EAAM,CAAC,GAAGse,OAAO;EACrC,IAAItvB,CAAC,EAAEO,IAAI;EACX,IAAIyQ,IAAI,EAAE;IACNhJ,KAAK,IAAIwE,KAAK;IACdvE,GAAG,IAAIuE,KAAK;IACZ,KAAIxM,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGiM,KAAK,EAAExM,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;MACnC,IAAI,CAACivB,OAAO,CAACE,SAAS,CAAC9iB,MAAM,CAACrE,KAAK,GAAGwE,KAAK,CAAC,CAAChG,QAAQ,CAAC,CAAC,EAAE+oB,UAAU,EAAEC,QAAQ,CAAC,EAAE;QAC5E;MACJ;MACAxnB,KAAK,EAAE;MACPC,GAAG,EAAE;IACT;IACAD,KAAK,IAAIwE,KAAK;IACdvE,GAAG,IAAIuE,KAAK;EAChB;EACA,IAAIvE,GAAG,GAAGD,KAAK,EAAE;IACbC,GAAG,IAAIuE,KAAK;EAChB;EACA,OAAO;IACHxE,KAAK;IACLC,GAAG;IACH+I,IAAI;IACJ6G,KAAK,EAAEyX,OAAO,CAACzX;EACnB,CAAC;AACL;AACC,SAAS4X,aAAaA,CAACH,OAAO,EAAEjjB,MAAM,EAAEmI,MAAM,EAAE;EAC7C,IAAI,CAACA,MAAM,EAAE;IACT,OAAO,CACH8a,OAAO,CACV;EACL;EACA,MAAM;IAAE9oB,QAAQ;IAAGwB,KAAK,EAAEunB,UAAU;IAAGtnB,GAAG,EAAEunB;EAAU,CAAC,GAAGhb,MAAM;EAChE,MAAMhI,KAAK,GAAGH,MAAM,CAAClM,MAAM;EAC3B,MAAM;IAAE+uB,OAAO;IAAGD,OAAO;IAAGE;EAAW,CAAC,GAAGH,UAAU,CAACxoB,QAAQ,CAAC;EAC/D,MAAM;IAAEwB,KAAK;IAAGC,GAAG;IAAG+I,IAAI;IAAG6G;EAAO,CAAC,GAAGwX,UAAU,CAACC,OAAO,EAAEjjB,MAAM,EAAEmI,MAAM,CAAC;EAC3E,MAAM3O,MAAM,GAAG,EAAE;EACjB,IAAI6pB,MAAM,GAAG,KAAK;EAClB,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAIxxB,KAAK,EAAEmf,KAAK,EAAEsS,SAAS;EAC3B,MAAMC,aAAa,GAAGA,CAAA,KAAIZ,OAAO,CAACM,UAAU,EAAEK,SAAS,EAAEzxB,KAAK,CAAC,IAAI+wB,OAAO,CAACK,UAAU,EAAEK,SAAS,CAAC,KAAK,CAAC;EACvG,MAAME,WAAW,GAAGA,CAAA,KAAIZ,OAAO,CAACM,QAAQ,EAAErxB,KAAK,CAAC,KAAK,CAAC,IAAI8wB,OAAO,CAACO,QAAQ,EAAEI,SAAS,EAAEzxB,KAAK,CAAC;EAC7F,MAAM4xB,WAAW,GAAGA,CAAA,KAAIL,MAAM,IAAIG,aAAa,CAAC,CAAC;EACjD,MAAMG,UAAU,GAAGA,CAAA,KAAI,CAACN,MAAM,IAAII,WAAW,CAAC,CAAC;EAC/C,KAAI,IAAI9vB,CAAC,GAAGgI,KAAK,EAAEmhB,IAAI,GAAGnhB,KAAK,EAAEhI,CAAC,IAAIiI,GAAG,EAAE,EAAEjI,CAAC,EAAC;IAC3Csd,KAAK,GAAGjR,MAAM,CAACrM,CAAC,GAAGwM,KAAK,CAAC;IACzB,IAAI8Q,KAAK,CAACqJ,IAAI,EAAE;MACZ;IACJ;IACAxoB,KAAK,GAAGgxB,SAAS,CAAC7R,KAAK,CAAC9W,QAAQ,CAAC,CAAC;IAClC,IAAIrI,KAAK,KAAKyxB,SAAS,EAAE;MACrB;IACJ;IACAF,MAAM,GAAGT,OAAO,CAAC9wB,KAAK,EAAEoxB,UAAU,EAAEC,QAAQ,CAAC;IAC7C,IAAIG,QAAQ,KAAK,IAAI,IAAII,WAAW,CAAC,CAAC,EAAE;MACpCJ,QAAQ,GAAGT,OAAO,CAAC/wB,KAAK,EAAEoxB,UAAU,CAAC,KAAK,CAAC,GAAGvvB,CAAC,GAAGmpB,IAAI;IAC1D;IACA,IAAIwG,QAAQ,KAAK,IAAI,IAAIK,UAAU,CAAC,CAAC,EAAE;MACnCnqB,MAAM,CAAC5C,IAAI,CAACmsB,gBAAgB,CAAC;QACzBpnB,KAAK,EAAE2nB,QAAQ;QACf1nB,GAAG,EAAEjI,CAAC;QACNgR,IAAI;QACJxE,KAAK;QACLqL;MACJ,CAAC,CAAC,CAAC;MACH8X,QAAQ,GAAG,IAAI;IACnB;IACAxG,IAAI,GAAGnpB,CAAC;IACR4vB,SAAS,GAAGzxB,KAAK;EACrB;EACA,IAAIwxB,QAAQ,KAAK,IAAI,EAAE;IACnB9pB,MAAM,CAAC5C,IAAI,CAACmsB,gBAAgB,CAAC;MACzBpnB,KAAK,EAAE2nB,QAAQ;MACf1nB,GAAG;MACH+I,IAAI;MACJxE,KAAK;MACLqL;IACJ,CAAC,CAAC,CAAC;EACP;EACA,OAAOhS,MAAM;AACjB;AACC,SAASoqB,cAAcA,CAACtR,IAAI,EAAEnK,MAAM,EAAE;EACnC,MAAM3O,MAAM,GAAG,EAAE;EACjB,MAAMqqB,QAAQ,GAAGvR,IAAI,CAACuR,QAAQ;EAC9B,KAAI,IAAIlwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkwB,QAAQ,CAAC/vB,MAAM,EAAEH,CAAC,EAAE,EAAC;IACpC,MAAMmwB,GAAG,GAAGV,aAAa,CAACS,QAAQ,CAAClwB,CAAC,CAAC,EAAE2e,IAAI,CAACtS,MAAM,EAAEmI,MAAM,CAAC;IAC3D,IAAI2b,GAAG,CAAChwB,MAAM,EAAE;MACZ0F,MAAM,CAAC5C,IAAI,CAAC,GAAGktB,GAAG,CAAC;IACvB;EACJ;EACA,OAAOtqB,MAAM;AACjB;AACC,SAASuqB,eAAeA,CAAC/jB,MAAM,EAAEG,KAAK,EAAEwE,IAAI,EAAEiY,QAAQ,EAAE;EACrD,IAAIjhB,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGuE,KAAK,GAAG,CAAC;EACnB,IAAIwE,IAAI,IAAI,CAACiY,QAAQ,EAAE;IACnB,OAAMjhB,KAAK,GAAGwE,KAAK,IAAI,CAACH,MAAM,CAACrE,KAAK,CAAC,CAAC2e,IAAI,EAAC;MACvC3e,KAAK,EAAE;IACX;EACJ;EACA,OAAMA,KAAK,GAAGwE,KAAK,IAAIH,MAAM,CAACrE,KAAK,CAAC,CAAC2e,IAAI,EAAC;IACtC3e,KAAK,EAAE;EACX;EACAA,KAAK,IAAIwE,KAAK;EACd,IAAIwE,IAAI,EAAE;IACN/I,GAAG,IAAID,KAAK;EAChB;EACA,OAAMC,GAAG,GAAGD,KAAK,IAAIqE,MAAM,CAACpE,GAAG,GAAGuE,KAAK,CAAC,CAACma,IAAI,EAAC;IAC1C1e,GAAG,EAAE;EACT;EACAA,GAAG,IAAIuE,KAAK;EACZ,OAAO;IACHxE,KAAK;IACLC;EACJ,CAAC;AACL;AACC,SAASooB,aAAaA,CAAChkB,MAAM,EAAErE,KAAK,EAAEtB,GAAG,EAAEsK,IAAI,EAAE;EAC9C,MAAMxE,KAAK,GAAGH,MAAM,CAAClM,MAAM;EAC3B,MAAM0F,MAAM,GAAG,EAAE;EACjB,IAAIqD,IAAI,GAAGlB,KAAK;EAChB,IAAImhB,IAAI,GAAG9c,MAAM,CAACrE,KAAK,CAAC;EACxB,IAAIC,GAAG;EACP,KAAIA,GAAG,GAAGD,KAAK,GAAG,CAAC,EAAEC,GAAG,IAAIvB,GAAG,EAAE,EAAEuB,GAAG,EAAC;IACnC,MAAMqoB,GAAG,GAAGjkB,MAAM,CAACpE,GAAG,GAAGuE,KAAK,CAAC;IAC/B,IAAI8jB,GAAG,CAAC3J,IAAI,IAAI2J,GAAG,CAACC,IAAI,EAAE;MACtB,IAAI,CAACpH,IAAI,CAACxC,IAAI,EAAE;QACZ3V,IAAI,GAAG,KAAK;QACZnL,MAAM,CAAC5C,IAAI,CAAC;UACR+E,KAAK,EAAEA,KAAK,GAAGwE,KAAK;UACpBvE,GAAG,EAAE,CAACA,GAAG,GAAG,CAAC,IAAIuE,KAAK;UACtBwE;QACJ,CAAC,CAAC;QACFhJ,KAAK,GAAGkB,IAAI,GAAGonB,GAAG,CAACC,IAAI,GAAGtoB,GAAG,GAAG,IAAI;MACxC;IACJ,CAAC,MAAM;MACHiB,IAAI,GAAGjB,GAAG;MACV,IAAIkhB,IAAI,CAACxC,IAAI,EAAE;QACX3e,KAAK,GAAGC,GAAG;MACf;IACJ;IACAkhB,IAAI,GAAGmH,GAAG;EACd;EACA,IAAIpnB,IAAI,KAAK,IAAI,EAAE;IACfrD,MAAM,CAAC5C,IAAI,CAAC;MACR+E,KAAK,EAAEA,KAAK,GAAGwE,KAAK;MACpBvE,GAAG,EAAEiB,IAAI,GAAGsD,KAAK;MACjBwE;IACJ,CAAC,CAAC;EACN;EACA,OAAOnL,MAAM;AACjB;AACC,SAAS2qB,gBAAgBA,CAAC7R,IAAI,EAAE8R,cAAc,EAAE;EAC7C,MAAMpkB,MAAM,GAAGsS,IAAI,CAACtS,MAAM;EAC1B,MAAM4c,QAAQ,GAAGtK,IAAI,CAACpd,OAAO,CAAC0nB,QAAQ;EACtC,MAAMzc,KAAK,GAAGH,MAAM,CAAClM,MAAM;EAC3B,IAAI,CAACqM,KAAK,EAAE;IACR,OAAO,EAAE;EACb;EACA,MAAMwE,IAAI,GAAG,CAAC,CAAC2N,IAAI,CAAC+R,KAAK;EACzB,MAAM;IAAE1oB,KAAK;IAAGC;EAAK,CAAC,GAAGmoB,eAAe,CAAC/jB,MAAM,EAAEG,KAAK,EAAEwE,IAAI,EAAEiY,QAAQ,CAAC;EACvE,IAAIA,QAAQ,KAAK,IAAI,EAAE;IACnB,OAAO0H,aAAa,CAAChS,IAAI,EAAE,CACvB;MACI3W,KAAK;MACLC,GAAG;MACH+I;IACJ,CAAC,CACJ,EAAE3E,MAAM,EAAEokB,cAAc,CAAC;EAC9B;EACA,MAAM/pB,GAAG,GAAGuB,GAAG,GAAGD,KAAK,GAAGC,GAAG,GAAGuE,KAAK,GAAGvE,GAAG;EAC3C,MAAM2oB,YAAY,GAAG,CAAC,CAACjS,IAAI,CAACkS,SAAS,IAAI7oB,KAAK,KAAK,CAAC,IAAIC,GAAG,KAAKuE,KAAK,GAAG,CAAC;EACzE,OAAOmkB,aAAa,CAAChS,IAAI,EAAE0R,aAAa,CAAChkB,MAAM,EAAErE,KAAK,EAAEtB,GAAG,EAAEkqB,YAAY,CAAC,EAAEvkB,MAAM,EAAEokB,cAAc,CAAC;AACvG;AACC,SAASE,aAAaA,CAAChS,IAAI,EAAEuR,QAAQ,EAAE7jB,MAAM,EAAEokB,cAAc,EAAE;EAC5D,IAAI,CAACA,cAAc,IAAI,CAACA,cAAc,CAACpM,UAAU,IAAI,CAAChY,MAAM,EAAE;IAC1D,OAAO6jB,QAAQ;EACnB;EACA,OAAOY,eAAe,CAACnS,IAAI,EAAEuR,QAAQ,EAAE7jB,MAAM,EAAEokB,cAAc,CAAC;AAClE;AACC,SAASK,eAAeA,CAACnS,IAAI,EAAEuR,QAAQ,EAAE7jB,MAAM,EAAEokB,cAAc,EAAE;EAC9D,MAAMM,YAAY,GAAGpS,IAAI,CAACqS,MAAM,CAACzV,UAAU,CAAC,CAAC;EAC7C,MAAM0V,SAAS,GAAGC,SAAS,CAACvS,IAAI,CAACpd,OAAO,CAAC;EACzC,MAAM;IAAE4vB,aAAa,EAAEzwB,YAAY;IAAGa,OAAO,EAAE;MAAE0nB;IAAU;EAAG,CAAC,GAAGtK,IAAI;EACtE,MAAMnS,KAAK,GAAGH,MAAM,CAAClM,MAAM;EAC3B,MAAM0F,MAAM,GAAG,EAAE;EACjB,IAAIurB,SAAS,GAAGH,SAAS;EACzB,IAAIjpB,KAAK,GAAGkoB,QAAQ,CAAC,CAAC,CAAC,CAACloB,KAAK;EAC7B,IAAIhI,CAAC,GAAGgI,KAAK;EACb,SAASqpB,QAAQA,CAAClpB,CAAC,EAAEhE,CAAC,EAAEmtB,CAAC,EAAEC,EAAE,EAAE;IAC3B,MAAMC,GAAG,GAAGvI,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;IAC7B,IAAI9gB,CAAC,KAAKhE,CAAC,EAAE;MACT;IACJ;IACAgE,CAAC,IAAIqE,KAAK;IACV,OAAMH,MAAM,CAAClE,CAAC,GAAGqE,KAAK,CAAC,CAACma,IAAI,EAAC;MACzBxe,CAAC,IAAIqpB,GAAG;IACZ;IACA,OAAMnlB,MAAM,CAAClI,CAAC,GAAGqI,KAAK,CAAC,CAACma,IAAI,EAAC;MACzBxiB,CAAC,IAAIqtB,GAAG;IACZ;IACA,IAAIrpB,CAAC,GAAGqE,KAAK,KAAKrI,CAAC,GAAGqI,KAAK,EAAE;MACzB3G,MAAM,CAAC5C,IAAI,CAAC;QACR+E,KAAK,EAAEG,CAAC,GAAGqE,KAAK;QAChBvE,GAAG,EAAE9D,CAAC,GAAGqI,KAAK;QACdwE,IAAI,EAAEsgB,CAAC;QACPzZ,KAAK,EAAE0Z;MACX,CAAC,CAAC;MACFH,SAAS,GAAGG,EAAE;MACdvpB,KAAK,GAAG7D,CAAC,GAAGqI,KAAK;IACrB;EACJ;EACA,KAAK,MAAM8iB,OAAO,IAAIY,QAAQ,EAAC;IAC3BloB,KAAK,GAAGihB,QAAQ,GAAGjhB,KAAK,GAAGsnB,OAAO,CAACtnB,KAAK;IACxC,IAAImhB,IAAI,GAAG9c,MAAM,CAACrE,KAAK,GAAGwE,KAAK,CAAC;IAChC,IAAIqL,KAAK;IACT,KAAI7X,CAAC,GAAGgI,KAAK,GAAG,CAAC,EAAEhI,CAAC,IAAIsvB,OAAO,CAACrnB,GAAG,EAAEjI,CAAC,EAAE,EAAC;MACrC,MAAM0oB,EAAE,GAAGrc,MAAM,CAACrM,CAAC,GAAGwM,KAAK,CAAC;MAC5BqL,KAAK,GAAGqZ,SAAS,CAACT,cAAc,CAACpM,UAAU,CAACnC,aAAa,CAAC6O,YAAY,EAAE;QACpEzyB,IAAI,EAAE,SAAS;QACfmzB,EAAE,EAAEtI,IAAI;QACRsE,EAAE,EAAE/E,EAAE;QACNgJ,WAAW,EAAE,CAAC1xB,CAAC,GAAG,CAAC,IAAIwM,KAAK;QAC5BmlB,WAAW,EAAE3xB,CAAC,GAAGwM,KAAK;QACtB9L;MACJ,CAAC,CAAC,CAAC,CAAC;MACJ,IAAIkxB,YAAY,CAAC/Z,KAAK,EAAEuZ,SAAS,CAAC,EAAE;QAChCC,QAAQ,CAACrpB,KAAK,EAAEhI,CAAC,GAAG,CAAC,EAAEsvB,OAAO,CAACte,IAAI,EAAEogB,SAAS,CAAC;MACnD;MACAjI,IAAI,GAAGT,EAAE;MACT0I,SAAS,GAAGvZ,KAAK;IACrB;IACA,IAAI7P,KAAK,GAAGhI,CAAC,GAAG,CAAC,EAAE;MACfqxB,QAAQ,CAACrpB,KAAK,EAAEhI,CAAC,GAAG,CAAC,EAAEsvB,OAAO,CAACte,IAAI,EAAEogB,SAAS,CAAC;IACnD;EACJ;EACA,OAAOvrB,MAAM;AACjB;AACA,SAASqrB,SAASA,CAAC3vB,OAAO,EAAE;EACxB,OAAO;IACH2V,eAAe,EAAE3V,OAAO,CAAC2V,eAAe;IACxC2a,cAAc,EAAEtwB,OAAO,CAACswB,cAAc;IACtCC,UAAU,EAAEvwB,OAAO,CAACuwB,UAAU;IAC9BC,gBAAgB,EAAExwB,OAAO,CAACwwB,gBAAgB;IAC1CC,eAAe,EAAEzwB,OAAO,CAACywB,eAAe;IACxC7U,WAAW,EAAE5b,OAAO,CAAC4b,WAAW;IAChChG,WAAW,EAAE5V,OAAO,CAAC4V;EACzB,CAAC;AACL;AACA,SAASya,YAAYA,CAAC/Z,KAAK,EAAEuZ,SAAS,EAAE;EACpC,IAAI,CAACA,SAAS,EAAE;IACZ,OAAO,KAAK;EAChB;EACA,MAAM5W,KAAK,GAAG,EAAE;EAChB,MAAMyX,QAAQ,GAAG,SAAAA,CAAS7wB,GAAG,EAAEjD,KAAK,EAAE;IAClC,IAAI,CAACiS,mBAAmB,CAACjS,KAAK,CAAC,EAAE;MAC7B,OAAOA,KAAK;IAChB;IACA,IAAI,CAACqc,KAAK,CAACtG,QAAQ,CAAC/V,KAAK,CAAC,EAAE;MACxBqc,KAAK,CAACvX,IAAI,CAAC9E,KAAK,CAAC;IACrB;IACA,OAAOqc,KAAK,CAACnZ,OAAO,CAAClD,KAAK,CAAC;EAC/B,CAAC;EACD,OAAOsU,IAAI,CAACC,SAAS,CAACmF,KAAK,EAAEoa,QAAQ,CAAC,KAAKxf,IAAI,CAACC,SAAS,CAAC0e,SAAS,EAAEa,QAAQ,CAAC;AAClF;AAEA,SAASvU,UAAU,IAAIwU,CAAC,EAAE9oB,aAAa,IAAI+oB,CAAC,EAAElpB,YAAY,IAAImpB,CAAC,EAAE/U,cAAc,IAAIgV,CAAC,EAAEprB,iBAAiB,IAAIqrB,CAAC,EAAEjR,SAAS,IAAIkR,CAAC,EAAE1yB,IAAI,IAAI2yB,CAAC,EAAEpG,cAAc,IAAIqG,CAAC,EAAE9tB,OAAO,IAAI+tB,CAAC,EAAEnJ,cAAc,IAAIoJ,CAAC,EAAEpF,YAAY,IAAIqF,CAAC,EAAE1F,4BAA4B,IAAI2F,CAAC,EAAE1nB,SAAS,IAAI2nB,CAAC,EAAEzJ,eAAe,IAAI0J,CAAC,EAAEntB,UAAU,IAAIotB,CAAC,EAAEh0B,eAAe,IAAIi0B,CAAC,EAAE7uB,EAAE,IAAI8uB,CAAC,EAAE1zB,QAAQ,IAAI2zB,CAAC,EAAEtR,SAAS,IAAIuR,CAAC,EAAE5qB,WAAW,IAAI6qB,CAAC,EAAE/uB,GAAG,IAAIgvB,CAAC,EAAEzsB,SAAS,IAAI0sB,CAAC,EAAExZ,YAAY,IAAIyZ,CAAC,EAAE/qB,WAAW,IAAIgrB,CAAC,EAAExY,WAAW,IAAIyY,CAAC,EAAEjW,QAAQ,IAAIkW,CAAC,EAAElU,UAAU,IAAImU,CAAC,EAAErpB,YAAY,IAAIspB,CAAC,EAAEpS,OAAO,IAAI5d,CAAC,EAAE+G,UAAU,IAAIkpB,EAAE,EAAExS,MAAM,IAAIjhB,EAAE,EAAEsL,kBAAkB,IAAIrL,EAAE,EAAEuL,cAAc,IAAIkoB,EAAE,EAAEtd,SAAS,IAAIud,EAAE,EAAEtyB,KAAK,IAAIuyB,EAAE,EAAE3wB,WAAW,IAAI4wB,EAAE,EAAExd,WAAW,IAAIyd,EAAE,EAAExwB,UAAU,IAAIywB,EAAE,EAAEvQ,cAAc,IAAIwQ,EAAE,EAAEjS,eAAe,IAAIkS,EAAE,EAAE7F,qBAAqB,IAAI8F,EAAE,EAAEzoB,MAAM,IAAI0oB,EAAE,EAAEzF,oBAAoB,IAAI0F,EAAE,EAAE7Y,eAAe,IAAI8Y,EAAE,EAAEhtB,qBAAqB,IAAIitB,EAAE,EAAE52B,IAAI,IAAI62B,EAAE,EAAEtuB,kBAAkB,IAAIuuB,EAAE,EAAE1vB,OAAO,IAAI2vB,EAAE,EAAE1uB,WAAW,IAAI2uB,EAAE,EAAE/vB,YAAY,IAAIgwB,EAAE,EAAEjuB,cAAc,IAAIkuB,EAAE,EAAE9gB,KAAK,IAAI+gB,EAAE,EAAEpwB,KAAK,IAAIqwB,EAAE,EAAE7a,YAAY,IAAI8a,EAAE,EAAE/rB,cAAc,IAAIgsB,EAAE,EAAE1sB,OAAO,IAAI2sB,EAAE,EAAEllB,mBAAmB,IAAImlB,EAAE,EAAEjlB,aAAa,IAAIklB,EAAE,EAAE50B,KAAK,IAAI60B,EAAE,EAAEn0B,OAAO,IAAIo0B,EAAE,EAAE3zB,SAAS,IAAI4zB,EAAE,EAAE1zB,WAAW,IAAI2zB,EAAE,EAAEhzB,SAAS,IAAIizB,EAAE,EAAE/b,YAAY,IAAIgc,EAAE,EAAEjP,WAAW,IAAIkP,EAAE,EAAExN,mBAAmB,IAAIyN,EAAE,EAAE7L,QAAQ,IAAI8L,EAAE,EAAEjf,YAAY,IAAIkf,EAAE,EAAEp0B,OAAO,IAAIq0B,EAAE,EAAEn4B,GAAG,IAAIo4B,EAAE,EAAE9qB,QAAQ,IAAI+qB,EAAE,EAAE1J,WAAW,IAAI2J,EAAE,EAAEjb,WAAW,IAAIkb,EAAE,EAAE3yB,SAAS,IAAI4yB,EAAE,EAAEp2B,cAAc,IAAIq2B,EAAE,EAAEvyB,aAAa,IAAIwyB,EAAE,EAAEhuB,UAAU,IAAIiuB,EAAE,EAAE9V,iBAAiB,IAAI+V,EAAE,EAAE7N,0BAA0B,IAAI8N,EAAE,EAAErG,gBAAgB,IAAIsG,EAAE,EAAE7G,cAAc,IAAI8G,EAAE,EAAEpJ,qBAAqB,IAAIqJ,EAAE,EAAEpJ,oBAAoB,IAAIqJ,EAAE,EAAEzJ,YAAY,IAAI0J,EAAE,EAAEvZ,cAAc,IAAIwZ,EAAE,EAAErZ,cAAc,IAAIsZ,EAAE,EAAEzb,SAAS,IAAI0b,EAAE,EAAEpX,kBAAkB,IAAIqX,EAAE,EAAEnW,MAAM,IAAIoW,EAAE,EAAEnW,aAAa,IAAIoW,EAAE,EAAE/H,aAAa,IAAIgI,EAAE,EAAE3vB,eAAe,IAAI4vB,EAAE,EAAElJ,aAAa,IAAImJ,EAAE,EAAEv5B,OAAO,IAAI0F,CAAC,EAAE2c,YAAY,IAAImX,EAAE,EAAErzB,KAAK,IAAIszB,EAAE,EAAErzB,QAAQ,IAAIszB,EAAE,EAAEpzB,WAAW,IAAIqzB,EAAE,EAAEnzB,UAAU,IAAIozB,EAAE,EAAEnzB,aAAa,IAAIozB,EAAE,EAAEpwB,UAAU,IAAIqwB,EAAE,EAAE7nB,KAAK,IAAI0d,CAAC,EAAEld,QAAQ,IAAIX,CAAC,EAAEjC,OAAO,IAAI9J,CAAC,EAAEf,gBAAgB,IAAI+0B,CAAC,EAAEt5B,cAAc,IAAIu5B,CAAC,EAAE10B,OAAO,IAAIwc,CAAC,EAAEthB,QAAQ,IAAIoB,CAAC,EAAEkiB,aAAa,IAAIvH,CAAC,EAAEzc,aAAa,IAAIgD,CAAC,EAAEsI,iBAAiB,IAAI8nB,CAAC,EAAEnyB,YAAY,IAAI8Q,CAAC,EAAE1Q,WAAW,IAAI2G,CAAC,EAAE6M,YAAY,IAAIrQ,CAAC,EAAEqF,aAAa,IAAIf,CAAC,EAAEmF,gCAAgC,IAAIksB,CAAC,EAAErtB,gBAAgB,IAAIub,CAAC,EAAExhB,IAAI,IAAIoD,CAAC,EAAExB,SAAS,IAAIkH,CAAC,EAAEzD,mBAAmB,IAAIkuB,CAAC,EAAEp5B,cAAc,IAAIsD,CAAC,EAAEyK,mBAAmB,IAAI4O,CAAC,EAAE5V,QAAQ,IAAIxD,CAAC,EAAE2jB,2BAA2B,IAAIzjB,CAAC,EAAE0oB,mBAAmB,IAAIkN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}