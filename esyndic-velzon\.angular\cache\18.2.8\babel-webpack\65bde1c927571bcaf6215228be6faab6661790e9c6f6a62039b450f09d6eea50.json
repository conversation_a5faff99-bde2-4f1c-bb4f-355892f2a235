{"ast": null, "code": "import _asyncToGenerator from \"C:/e-syndic/esyndic-velzon/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport sha256 from 'js-sha256';\nimport { jwtDecode } from 'jwt-decode';\n\n/*\n * Copyright 2016 Red Hat, Inc. and/or its affiliates\n * and other contributors as indicated by the <AUTHOR>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nif (typeof Promise === 'undefined') {\n  throw Error('Keycloak requires an environment that supports Promises. Make sure that you include the appropriate polyfill.');\n}\nfunction Keycloak(config) {\n  if (!(this instanceof Keycloak)) {\n    throw new Error(\"The 'Keycloak' constructor must be invoked with 'new'.\");\n  }\n  var kc = this;\n  var adapter;\n  var refreshQueue = [];\n  var callbackStorage;\n  var loginIframe = {\n    enable: true,\n    callbackList: [],\n    interval: 5\n  };\n  var scripts = document.getElementsByTagName('script');\n  for (var i = 0; i < scripts.length; i++) {\n    if ((scripts[i].src.indexOf('keycloak.js') !== -1 || scripts[i].src.indexOf('keycloak.min.js') !== -1) && scripts[i].src.indexOf('version=') !== -1) {\n      kc.iframeVersion = scripts[i].src.substring(scripts[i].src.indexOf('version=') + 8).split('&')[0];\n    }\n  }\n  var useNonce = true;\n  var logInfo = createLogger(console.info);\n  var logWarn = createLogger(console.warn);\n  kc.init = function (initOptions) {\n    if (kc.didInitialize) {\n      throw new Error(\"A 'Keycloak' instance can only be initialized once.\");\n    }\n    kc.didInitialize = true;\n    kc.authenticated = false;\n    callbackStorage = createCallbackStorage();\n    var adapters = ['default', 'cordova', 'cordova-native'];\n    if (initOptions && adapters.indexOf(initOptions.adapter) > -1) {\n      adapter = loadAdapter(initOptions.adapter);\n    } else if (initOptions && typeof initOptions.adapter === \"object\") {\n      adapter = initOptions.adapter;\n    } else {\n      if (window.Cordova || window.cordova) {\n        adapter = loadAdapter('cordova');\n      } else {\n        adapter = loadAdapter();\n      }\n    }\n    if (initOptions) {\n      if (typeof initOptions.useNonce !== 'undefined') {\n        useNonce = initOptions.useNonce;\n      }\n      if (typeof initOptions.checkLoginIframe !== 'undefined') {\n        loginIframe.enable = initOptions.checkLoginIframe;\n      }\n      if (initOptions.checkLoginIframeInterval) {\n        loginIframe.interval = initOptions.checkLoginIframeInterval;\n      }\n      if (initOptions.onLoad === 'login-required') {\n        kc.loginRequired = true;\n      }\n      if (initOptions.responseMode) {\n        if (initOptions.responseMode === 'query' || initOptions.responseMode === 'fragment') {\n          kc.responseMode = initOptions.responseMode;\n        } else {\n          throw 'Invalid value for responseMode';\n        }\n      }\n      if (initOptions.flow) {\n        switch (initOptions.flow) {\n          case 'standard':\n            kc.responseType = 'code';\n            break;\n          case 'implicit':\n            kc.responseType = 'id_token token';\n            break;\n          case 'hybrid':\n            kc.responseType = 'code id_token token';\n            break;\n          default:\n            throw 'Invalid value for flow';\n        }\n        kc.flow = initOptions.flow;\n      }\n      if (initOptions.timeSkew != null) {\n        kc.timeSkew = initOptions.timeSkew;\n      }\n      if (initOptions.redirectUri) {\n        kc.redirectUri = initOptions.redirectUri;\n      }\n      if (initOptions.silentCheckSsoRedirectUri) {\n        kc.silentCheckSsoRedirectUri = initOptions.silentCheckSsoRedirectUri;\n      }\n      if (typeof initOptions.silentCheckSsoFallback === 'boolean') {\n        kc.silentCheckSsoFallback = initOptions.silentCheckSsoFallback;\n      } else {\n        kc.silentCheckSsoFallback = true;\n      }\n      if (initOptions.pkceMethod) {\n        if (initOptions.pkceMethod !== \"S256\") {\n          throw new TypeError(`Invalid value for 'pkceMethod', expected 'S256' but got '${initOptions.pkceMethod}'.`);\n        }\n        kc.pkceMethod = initOptions.pkceMethod;\n      } else {\n        kc.pkceMethod = \"S256\";\n      }\n      if (typeof initOptions.enableLogging === 'boolean') {\n        kc.enableLogging = initOptions.enableLogging;\n      } else {\n        kc.enableLogging = false;\n      }\n      if (initOptions.logoutMethod === 'POST') {\n        kc.logoutMethod = 'POST';\n      } else {\n        kc.logoutMethod = 'GET';\n      }\n      if (typeof initOptions.scope === 'string') {\n        kc.scope = initOptions.scope;\n      }\n      if (typeof initOptions.acrValues === 'string') {\n        kc.acrValues = initOptions.acrValues;\n      }\n      if (typeof initOptions.messageReceiveTimeout === 'number' && initOptions.messageReceiveTimeout > 0) {\n        kc.messageReceiveTimeout = initOptions.messageReceiveTimeout;\n      } else {\n        kc.messageReceiveTimeout = 10000;\n      }\n    }\n    if (!kc.responseMode) {\n      kc.responseMode = 'fragment';\n    }\n    if (!kc.responseType) {\n      kc.responseType = 'code';\n      kc.flow = 'standard';\n    }\n    var promise = createPromise();\n    var initPromise = createPromise();\n    initPromise.promise.then(function () {\n      kc.onReady && kc.onReady(kc.authenticated);\n      promise.setSuccess(kc.authenticated);\n    }).catch(function (error) {\n      promise.setError(error);\n    });\n    var configPromise = loadConfig();\n    function onLoad() {\n      var doLogin = function (prompt) {\n        if (!prompt) {\n          options.prompt = 'none';\n        }\n        if (initOptions && initOptions.locale) {\n          options.locale = initOptions.locale;\n        }\n        kc.login(options).then(function () {\n          initPromise.setSuccess();\n        }).catch(function (error) {\n          initPromise.setError(error);\n        });\n      };\n      var checkSsoSilently = function () {\n        var ifrm = document.createElement(\"iframe\");\n        var src = kc.createLoginUrl({\n          prompt: 'none',\n          redirectUri: kc.silentCheckSsoRedirectUri\n        });\n        ifrm.setAttribute(\"src\", src);\n        ifrm.setAttribute(\"sandbox\", \"allow-storage-access-by-user-activation allow-scripts allow-same-origin\");\n        ifrm.setAttribute(\"title\", \"keycloak-silent-check-sso\");\n        ifrm.style.display = \"none\";\n        document.body.appendChild(ifrm);\n        var messageCallback = function (event) {\n          if (event.origin !== window.location.origin || ifrm.contentWindow !== event.source) {\n            return;\n          }\n          var oauth = parseCallback(event.data);\n          processCallback(oauth, initPromise);\n          document.body.removeChild(ifrm);\n          window.removeEventListener(\"message\", messageCallback);\n        };\n        window.addEventListener(\"message\", messageCallback);\n      };\n      var options = {};\n      switch (initOptions.onLoad) {\n        case 'check-sso':\n          if (loginIframe.enable) {\n            setupCheckLoginIframe().then(function () {\n              checkLoginIframe().then(function (unchanged) {\n                if (!unchanged) {\n                  kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                } else {\n                  initPromise.setSuccess();\n                }\n              }).catch(function (error) {\n                initPromise.setError(error);\n              });\n            });\n          } else {\n            kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n          }\n          break;\n        case 'login-required':\n          doLogin(true);\n          break;\n        default:\n          throw 'Invalid value for onLoad';\n      }\n    }\n    function processInit() {\n      var callback = parseCallback(window.location.href);\n      if (callback) {\n        window.history.replaceState(window.history.state, null, callback.newUrl);\n      }\n      if (callback && callback.valid) {\n        return setupCheckLoginIframe().then(function () {\n          processCallback(callback, initPromise);\n        }).catch(function (error) {\n          initPromise.setError(error);\n        });\n      } else if (initOptions) {\n        if (initOptions.token && initOptions.refreshToken) {\n          setToken(initOptions.token, initOptions.refreshToken, initOptions.idToken);\n          if (loginIframe.enable) {\n            setupCheckLoginIframe().then(function () {\n              checkLoginIframe().then(function (unchanged) {\n                if (unchanged) {\n                  kc.onAuthSuccess && kc.onAuthSuccess();\n                  initPromise.setSuccess();\n                  scheduleCheckIframe();\n                } else {\n                  initPromise.setSuccess();\n                }\n              }).catch(function (error) {\n                initPromise.setError(error);\n              });\n            });\n          } else {\n            kc.updateToken(-1).then(function () {\n              kc.onAuthSuccess && kc.onAuthSuccess();\n              initPromise.setSuccess();\n            }).catch(function (error) {\n              kc.onAuthError && kc.onAuthError();\n              if (initOptions.onLoad) {\n                onLoad();\n              } else {\n                initPromise.setError(error);\n              }\n            });\n          }\n        } else if (initOptions.onLoad) {\n          onLoad();\n        } else {\n          initPromise.setSuccess();\n        }\n      } else {\n        initPromise.setSuccess();\n      }\n    }\n    function domReady() {\n      var promise = createPromise();\n      var checkReadyState = function () {\n        if (document.readyState === 'interactive' || document.readyState === 'complete') {\n          document.removeEventListener('readystatechange', checkReadyState);\n          promise.setSuccess();\n        }\n      };\n      document.addEventListener('readystatechange', checkReadyState);\n      checkReadyState(); // just in case the event was already fired and we missed it (in case the init is done later than at the load time, i.e. it's done from code)\n\n      return promise.promise;\n    }\n    configPromise.then(function () {\n      domReady().then(check3pCookiesSupported).then(processInit).catch(function (error) {\n        promise.setError(error);\n      });\n    });\n    configPromise.catch(function (error) {\n      promise.setError(error);\n    });\n    return promise.promise;\n  };\n  kc.login = function (options) {\n    return adapter.login(options);\n  };\n  function generateRandomData(len) {\n    // use web crypto APIs if possible\n    var array = null;\n    var crypto = window.crypto || window.msCrypto;\n    if (crypto && crypto.getRandomValues && window.Uint8Array) {\n      array = new Uint8Array(len);\n      crypto.getRandomValues(array);\n      return array;\n    }\n\n    // fallback to Math random\n    array = new Array(len);\n    for (var j = 0; j < array.length; j++) {\n      array[j] = Math.floor(256 * Math.random());\n    }\n    return array;\n  }\n  function generateCodeVerifier(len) {\n    return generateRandomString(len, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');\n  }\n  function generateRandomString(len, alphabet) {\n    var randomData = generateRandomData(len);\n    var chars = new Array(len);\n    for (var i = 0; i < len; i++) {\n      chars[i] = alphabet.charCodeAt(randomData[i] % alphabet.length);\n    }\n    return String.fromCharCode.apply(null, chars);\n  }\n  function generatePkceChallenge(pkceMethod, codeVerifier) {\n    if (pkceMethod !== \"S256\") {\n      throw new TypeError(`Invalid value for 'pkceMethod', expected 'S256' but got '${pkceMethod}'.`);\n    }\n\n    // hash codeVerifier, then encode as url-safe base64 without padding\n    const hashBytes = new Uint8Array(sha256.arrayBuffer(codeVerifier));\n    const encodedHash = bytesToBase64(hashBytes).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/\\=/g, '');\n    return encodedHash;\n  }\n  function buildClaimsParameter(requestedAcr) {\n    var claims = {\n      id_token: {\n        acr: requestedAcr\n      }\n    };\n    return JSON.stringify(claims);\n  }\n  kc.createLoginUrl = function (options) {\n    var state = createUUID();\n    var nonce = createUUID();\n    var redirectUri = adapter.redirectUri(options);\n    var callbackState = {\n      state: state,\n      nonce: nonce,\n      redirectUri: encodeURIComponent(redirectUri)\n    };\n    if (options && options.prompt) {\n      callbackState.prompt = options.prompt;\n    }\n    var baseUrl;\n    if (options && options.action == 'register') {\n      baseUrl = kc.endpoints.register();\n    } else {\n      baseUrl = kc.endpoints.authorize();\n    }\n    var scope = options && options.scope || kc.scope;\n    if (!scope) {\n      // if scope is not set, default to \"openid\"\n      scope = \"openid\";\n    } else if (scope.indexOf(\"openid\") === -1) {\n      // if openid scope is missing, prefix the given scopes with it\n      scope = \"openid \" + scope;\n    }\n    var url = baseUrl + '?client_id=' + encodeURIComponent(kc.clientId) + '&redirect_uri=' + encodeURIComponent(redirectUri) + '&state=' + encodeURIComponent(state) + '&response_mode=' + encodeURIComponent(kc.responseMode) + '&response_type=' + encodeURIComponent(kc.responseType) + '&scope=' + encodeURIComponent(scope);\n    if (useNonce) {\n      url = url + '&nonce=' + encodeURIComponent(nonce);\n    }\n    if (options && options.prompt) {\n      url += '&prompt=' + encodeURIComponent(options.prompt);\n    }\n    if (options && options.maxAge) {\n      url += '&max_age=' + encodeURIComponent(options.maxAge);\n    }\n    if (options && options.loginHint) {\n      url += '&login_hint=' + encodeURIComponent(options.loginHint);\n    }\n    if (options && options.idpHint) {\n      url += '&kc_idp_hint=' + encodeURIComponent(options.idpHint);\n    }\n    if (options && options.action && options.action != 'register') {\n      url += '&kc_action=' + encodeURIComponent(options.action);\n    }\n    if (options && options.locale) {\n      url += '&ui_locales=' + encodeURIComponent(options.locale);\n    }\n    if (options && options.acr) {\n      var claimsParameter = buildClaimsParameter(options.acr);\n      url += '&claims=' + encodeURIComponent(claimsParameter);\n    }\n    if (options && options.acrValues || kc.acrValues) {\n      url += '&acr_values=' + encodeURIComponent(options.acrValues || kc.acrValues);\n    }\n    if (kc.pkceMethod) {\n      var codeVerifier = generateCodeVerifier(96);\n      callbackState.pkceCodeVerifier = codeVerifier;\n      var pkceChallenge = generatePkceChallenge(kc.pkceMethod, codeVerifier);\n      url += '&code_challenge=' + pkceChallenge;\n      url += '&code_challenge_method=' + kc.pkceMethod;\n    }\n    callbackStorage.add(callbackState);\n    return url;\n  };\n  kc.logout = function (options) {\n    return adapter.logout(options);\n  };\n  kc.createLogoutUrl = function (options) {\n    const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n    if (logoutMethod === 'POST') {\n      return kc.endpoints.logout();\n    }\n    var url = kc.endpoints.logout() + '?client_id=' + encodeURIComponent(kc.clientId) + '&post_logout_redirect_uri=' + encodeURIComponent(adapter.redirectUri(options, false));\n    if (kc.idToken) {\n      url += '&id_token_hint=' + encodeURIComponent(kc.idToken);\n    }\n    return url;\n  };\n  kc.register = function (options) {\n    return adapter.register(options);\n  };\n  kc.createRegisterUrl = function (options) {\n    if (!options) {\n      options = {};\n    }\n    options.action = 'register';\n    return kc.createLoginUrl(options);\n  };\n  kc.createAccountUrl = function (options) {\n    var realm = getRealmUrl();\n    var url = undefined;\n    if (typeof realm !== 'undefined') {\n      url = realm + '/account' + '?referrer=' + encodeURIComponent(kc.clientId) + '&referrer_uri=' + encodeURIComponent(adapter.redirectUri(options));\n    }\n    return url;\n  };\n  kc.accountManagement = function () {\n    return adapter.accountManagement();\n  };\n  kc.hasRealmRole = function (role) {\n    var access = kc.realmAccess;\n    return !!access && access.roles.indexOf(role) >= 0;\n  };\n  kc.hasResourceRole = function (role, resource) {\n    if (!kc.resourceAccess) {\n      return false;\n    }\n    var access = kc.resourceAccess[resource || kc.clientId];\n    return !!access && access.roles.indexOf(role) >= 0;\n  };\n  kc.loadUserProfile = function () {\n    var url = getRealmUrl() + '/account';\n    var req = new XMLHttpRequest();\n    req.open('GET', url, true);\n    req.setRequestHeader('Accept', 'application/json');\n    req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n    var promise = createPromise();\n    req.onreadystatechange = function () {\n      if (req.readyState == 4) {\n        if (req.status == 200) {\n          kc.profile = JSON.parse(req.responseText);\n          promise.setSuccess(kc.profile);\n        } else {\n          promise.setError();\n        }\n      }\n    };\n    req.send();\n    return promise.promise;\n  };\n  kc.loadUserInfo = function () {\n    var url = kc.endpoints.userinfo();\n    var req = new XMLHttpRequest();\n    req.open('GET', url, true);\n    req.setRequestHeader('Accept', 'application/json');\n    req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n    var promise = createPromise();\n    req.onreadystatechange = function () {\n      if (req.readyState == 4) {\n        if (req.status == 200) {\n          kc.userInfo = JSON.parse(req.responseText);\n          promise.setSuccess(kc.userInfo);\n        } else {\n          promise.setError();\n        }\n      }\n    };\n    req.send();\n    return promise.promise;\n  };\n  kc.isTokenExpired = function (minValidity) {\n    if (!kc.tokenParsed || !kc.refreshToken && kc.flow != 'implicit') {\n      throw 'Not authenticated';\n    }\n    if (kc.timeSkew == null) {\n      logInfo('[KEYCLOAK] Unable to determine if token is expired as timeskew is not set');\n      return true;\n    }\n    var expiresIn = kc.tokenParsed['exp'] - Math.ceil(new Date().getTime() / 1000) + kc.timeSkew;\n    if (minValidity) {\n      if (isNaN(minValidity)) {\n        throw 'Invalid minValidity';\n      }\n      expiresIn -= minValidity;\n    }\n    return expiresIn < 0;\n  };\n  kc.updateToken = function (minValidity) {\n    var promise = createPromise();\n    if (!kc.refreshToken) {\n      promise.setError();\n      return promise.promise;\n    }\n    minValidity = minValidity || 5;\n    var exec = function () {\n      var refreshToken = false;\n      if (minValidity == -1) {\n        refreshToken = true;\n        logInfo('[KEYCLOAK] Refreshing token: forced refresh');\n      } else if (!kc.tokenParsed || kc.isTokenExpired(minValidity)) {\n        refreshToken = true;\n        logInfo('[KEYCLOAK] Refreshing token: token expired');\n      }\n      if (!refreshToken) {\n        promise.setSuccess(false);\n      } else {\n        var params = 'grant_type=refresh_token&' + 'refresh_token=' + kc.refreshToken;\n        var url = kc.endpoints.token();\n        refreshQueue.push(promise);\n        if (refreshQueue.length == 1) {\n          var req = new XMLHttpRequest();\n          req.open('POST', url, true);\n          req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n          req.withCredentials = true;\n          params += '&client_id=' + encodeURIComponent(kc.clientId);\n          var timeLocal = new Date().getTime();\n          req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n              if (req.status == 200) {\n                logInfo('[KEYCLOAK] Token refreshed');\n                timeLocal = (timeLocal + new Date().getTime()) / 2;\n                var tokenResponse = JSON.parse(req.responseText);\n                setToken(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], timeLocal);\n                kc.onAuthRefreshSuccess && kc.onAuthRefreshSuccess();\n                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                  p.setSuccess(true);\n                }\n              } else {\n                logWarn('[KEYCLOAK] Failed to refresh token');\n                if (req.status == 400) {\n                  kc.clearToken();\n                }\n                kc.onAuthRefreshError && kc.onAuthRefreshError();\n                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                  p.setError(true);\n                }\n              }\n            }\n          };\n          req.send(params);\n        }\n      }\n    };\n    if (loginIframe.enable) {\n      var iframePromise = checkLoginIframe();\n      iframePromise.then(function () {\n        exec();\n      }).catch(function (error) {\n        promise.setError(error);\n      });\n    } else {\n      exec();\n    }\n    return promise.promise;\n  };\n  kc.clearToken = function () {\n    if (kc.token) {\n      setToken(null, null, null);\n      kc.onAuthLogout && kc.onAuthLogout();\n      if (kc.loginRequired) {\n        kc.login();\n      }\n    }\n  };\n  function getRealmUrl() {\n    if (typeof kc.authServerUrl !== 'undefined') {\n      if (kc.authServerUrl.charAt(kc.authServerUrl.length - 1) == '/') {\n        return kc.authServerUrl + 'realms/' + encodeURIComponent(kc.realm);\n      } else {\n        return kc.authServerUrl + '/realms/' + encodeURIComponent(kc.realm);\n      }\n    } else {\n      return undefined;\n    }\n  }\n  function getOrigin() {\n    if (!window.location.origin) {\n      return window.location.protocol + \"//\" + window.location.hostname + (window.location.port ? ':' + window.location.port : '');\n    } else {\n      return window.location.origin;\n    }\n  }\n  function processCallback(oauth, promise) {\n    var code = oauth.code;\n    var error = oauth.error;\n    var prompt = oauth.prompt;\n    var timeLocal = new Date().getTime();\n    if (oauth['kc_action_status']) {\n      kc.onActionUpdate && kc.onActionUpdate(oauth['kc_action_status']);\n    }\n    if (error) {\n      if (prompt != 'none') {\n        var errorData = {\n          error: error,\n          error_description: oauth.error_description\n        };\n        kc.onAuthError && kc.onAuthError(errorData);\n        promise && promise.setError(errorData);\n      } else {\n        promise && promise.setSuccess();\n      }\n      return;\n    } else if (kc.flow != 'standard' && (oauth.access_token || oauth.id_token)) {\n      authSuccess(oauth.access_token, null, oauth.id_token, true);\n    }\n    if (kc.flow != 'implicit' && code) {\n      var params = 'code=' + code + '&grant_type=authorization_code';\n      var url = kc.endpoints.token();\n      var req = new XMLHttpRequest();\n      req.open('POST', url, true);\n      req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n      params += '&client_id=' + encodeURIComponent(kc.clientId);\n      params += '&redirect_uri=' + oauth.redirectUri;\n      if (oauth.pkceCodeVerifier) {\n        params += '&code_verifier=' + oauth.pkceCodeVerifier;\n      }\n      req.withCredentials = true;\n      req.onreadystatechange = function () {\n        if (req.readyState == 4) {\n          if (req.status == 200) {\n            var tokenResponse = JSON.parse(req.responseText);\n            authSuccess(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], kc.flow === 'standard');\n            scheduleCheckIframe();\n          } else {\n            kc.onAuthError && kc.onAuthError();\n            promise && promise.setError();\n          }\n        }\n      };\n      req.send(params);\n    }\n    function authSuccess(accessToken, refreshToken, idToken, fulfillPromise) {\n      timeLocal = (timeLocal + new Date().getTime()) / 2;\n      setToken(accessToken, refreshToken, idToken, timeLocal);\n      if (useNonce && kc.idTokenParsed && kc.idTokenParsed.nonce != oauth.storedNonce) {\n        logInfo('[KEYCLOAK] Invalid nonce, clearing token');\n        kc.clearToken();\n        promise && promise.setError();\n      } else {\n        if (fulfillPromise) {\n          kc.onAuthSuccess && kc.onAuthSuccess();\n          promise && promise.setSuccess();\n        }\n      }\n    }\n  }\n  function loadConfig(url) {\n    var promise = createPromise();\n    var configUrl;\n    if (!config) {\n      configUrl = 'keycloak.json';\n    } else if (typeof config === 'string') {\n      configUrl = config;\n    }\n    function setupOidcEndoints(oidcConfiguration) {\n      if (!oidcConfiguration) {\n        kc.endpoints = {\n          authorize: function () {\n            return getRealmUrl() + '/protocol/openid-connect/auth';\n          },\n          token: function () {\n            return getRealmUrl() + '/protocol/openid-connect/token';\n          },\n          logout: function () {\n            return getRealmUrl() + '/protocol/openid-connect/logout';\n          },\n          checkSessionIframe: function () {\n            var src = getRealmUrl() + '/protocol/openid-connect/login-status-iframe.html';\n            if (kc.iframeVersion) {\n              src = src + '?version=' + kc.iframeVersion;\n            }\n            return src;\n          },\n          thirdPartyCookiesIframe: function () {\n            var src = getRealmUrl() + '/protocol/openid-connect/3p-cookies/step1.html';\n            if (kc.iframeVersion) {\n              src = src + '?version=' + kc.iframeVersion;\n            }\n            return src;\n          },\n          register: function () {\n            return getRealmUrl() + '/protocol/openid-connect/registrations';\n          },\n          userinfo: function () {\n            return getRealmUrl() + '/protocol/openid-connect/userinfo';\n          }\n        };\n      } else {\n        kc.endpoints = {\n          authorize: function () {\n            return oidcConfiguration.authorization_endpoint;\n          },\n          token: function () {\n            return oidcConfiguration.token_endpoint;\n          },\n          logout: function () {\n            if (!oidcConfiguration.end_session_endpoint) {\n              throw \"Not supported by the OIDC server\";\n            }\n            return oidcConfiguration.end_session_endpoint;\n          },\n          checkSessionIframe: function () {\n            if (!oidcConfiguration.check_session_iframe) {\n              throw \"Not supported by the OIDC server\";\n            }\n            return oidcConfiguration.check_session_iframe;\n          },\n          register: function () {\n            throw 'Redirection to \"Register user\" page not supported in standard OIDC mode';\n          },\n          userinfo: function () {\n            if (!oidcConfiguration.userinfo_endpoint) {\n              throw \"Not supported by the OIDC server\";\n            }\n            return oidcConfiguration.userinfo_endpoint;\n          }\n        };\n      }\n    }\n    if (configUrl) {\n      var req = new XMLHttpRequest();\n      req.open('GET', configUrl, true);\n      req.setRequestHeader('Accept', 'application/json');\n      req.onreadystatechange = function () {\n        if (req.readyState == 4) {\n          if (req.status == 200 || fileLoaded(req)) {\n            var config = JSON.parse(req.responseText);\n            kc.authServerUrl = config['auth-server-url'];\n            kc.realm = config['realm'];\n            kc.clientId = config['resource'];\n            setupOidcEndoints(null);\n            promise.setSuccess();\n          } else {\n            promise.setError();\n          }\n        }\n      };\n      req.send();\n    } else {\n      if (!config.clientId) {\n        throw 'clientId missing';\n      }\n      kc.clientId = config.clientId;\n      var oidcProvider = config['oidcProvider'];\n      if (!oidcProvider) {\n        if (!config['url']) {\n          var scripts = document.getElementsByTagName('script');\n          for (var i = 0; i < scripts.length; i++) {\n            if (scripts[i].src.match(/.*keycloak\\.js/)) {\n              config.url = scripts[i].src.substr(0, scripts[i].src.indexOf('/js/keycloak.js'));\n              break;\n            }\n          }\n        }\n        if (!config.realm) {\n          throw 'realm missing';\n        }\n        kc.authServerUrl = config.url;\n        kc.realm = config.realm;\n        setupOidcEndoints(null);\n        promise.setSuccess();\n      } else {\n        if (typeof oidcProvider === 'string') {\n          var oidcProviderConfigUrl;\n          if (oidcProvider.charAt(oidcProvider.length - 1) == '/') {\n            oidcProviderConfigUrl = oidcProvider + '.well-known/openid-configuration';\n          } else {\n            oidcProviderConfigUrl = oidcProvider + '/.well-known/openid-configuration';\n          }\n          var req = new XMLHttpRequest();\n          req.open('GET', oidcProviderConfigUrl, true);\n          req.setRequestHeader('Accept', 'application/json');\n          req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n              if (req.status == 200 || fileLoaded(req)) {\n                var oidcProviderConfig = JSON.parse(req.responseText);\n                setupOidcEndoints(oidcProviderConfig);\n                promise.setSuccess();\n              } else {\n                promise.setError();\n              }\n            }\n          };\n          req.send();\n        } else {\n          setupOidcEndoints(oidcProvider);\n          promise.setSuccess();\n        }\n      }\n    }\n    return promise.promise;\n  }\n  function fileLoaded(xhr) {\n    return xhr.status == 0 && xhr.responseText && xhr.responseURL.startsWith('file:');\n  }\n  function setToken(token, refreshToken, idToken, timeLocal) {\n    if (kc.tokenTimeoutHandle) {\n      clearTimeout(kc.tokenTimeoutHandle);\n      kc.tokenTimeoutHandle = null;\n    }\n    if (refreshToken) {\n      kc.refreshToken = refreshToken;\n      kc.refreshTokenParsed = jwtDecode(refreshToken);\n    } else {\n      delete kc.refreshToken;\n      delete kc.refreshTokenParsed;\n    }\n    if (idToken) {\n      kc.idToken = idToken;\n      kc.idTokenParsed = jwtDecode(idToken);\n    } else {\n      delete kc.idToken;\n      delete kc.idTokenParsed;\n    }\n    if (token) {\n      kc.token = token;\n      kc.tokenParsed = jwtDecode(token);\n      kc.sessionId = kc.tokenParsed.session_state;\n      kc.authenticated = true;\n      kc.subject = kc.tokenParsed.sub;\n      kc.realmAccess = kc.tokenParsed.realm_access;\n      kc.resourceAccess = kc.tokenParsed.resource_access;\n      if (timeLocal) {\n        kc.timeSkew = Math.floor(timeLocal / 1000) - kc.tokenParsed.iat;\n      }\n      if (kc.timeSkew != null) {\n        logInfo('[KEYCLOAK] Estimated time difference between browser and server is ' + kc.timeSkew + ' seconds');\n        if (kc.onTokenExpired) {\n          var expiresIn = (kc.tokenParsed['exp'] - new Date().getTime() / 1000 + kc.timeSkew) * 1000;\n          logInfo('[KEYCLOAK] Token expires in ' + Math.round(expiresIn / 1000) + ' s');\n          if (expiresIn <= 0) {\n            kc.onTokenExpired();\n          } else {\n            kc.tokenTimeoutHandle = setTimeout(kc.onTokenExpired, expiresIn);\n          }\n        }\n      }\n    } else {\n      delete kc.token;\n      delete kc.tokenParsed;\n      delete kc.subject;\n      delete kc.realmAccess;\n      delete kc.resourceAccess;\n      kc.authenticated = false;\n    }\n  }\n  function createUUID() {\n    var hexDigits = '0123456789abcdef';\n    var s = generateRandomString(36, hexDigits).split(\"\");\n    s[14] = '4';\n    s[19] = hexDigits.substr(s[19] & 0x3 | 0x8, 1);\n    s[8] = s[13] = s[18] = s[23] = '-';\n    var uuid = s.join('');\n    return uuid;\n  }\n  function parseCallback(url) {\n    var oauth = parseCallbackUrl(url);\n    if (!oauth) {\n      return;\n    }\n    var oauthState = callbackStorage.get(oauth.state);\n    if (oauthState) {\n      oauth.valid = true;\n      oauth.redirectUri = oauthState.redirectUri;\n      oauth.storedNonce = oauthState.nonce;\n      oauth.prompt = oauthState.prompt;\n      oauth.pkceCodeVerifier = oauthState.pkceCodeVerifier;\n    }\n    return oauth;\n  }\n  function parseCallbackUrl(url) {\n    var supportedParams;\n    switch (kc.flow) {\n      case 'standard':\n        supportedParams = ['code', 'state', 'session_state', 'kc_action_status', 'iss'];\n        break;\n      case 'implicit':\n        supportedParams = ['access_token', 'token_type', 'id_token', 'state', 'session_state', 'expires_in', 'kc_action_status', 'iss'];\n        break;\n      case 'hybrid':\n        supportedParams = ['access_token', 'token_type', 'id_token', 'code', 'state', 'session_state', 'expires_in', 'kc_action_status', 'iss'];\n        break;\n    }\n    supportedParams.push('error');\n    supportedParams.push('error_description');\n    supportedParams.push('error_uri');\n    var queryIndex = url.indexOf('?');\n    var fragmentIndex = url.indexOf('#');\n    var newUrl;\n    var parsed;\n    if (kc.responseMode === 'query' && queryIndex !== -1) {\n      newUrl = url.substring(0, queryIndex);\n      parsed = parseCallbackParams(url.substring(queryIndex + 1, fragmentIndex !== -1 ? fragmentIndex : url.length), supportedParams);\n      if (parsed.paramsString !== '') {\n        newUrl += '?' + parsed.paramsString;\n      }\n      if (fragmentIndex !== -1) {\n        newUrl += url.substring(fragmentIndex);\n      }\n    } else if (kc.responseMode === 'fragment' && fragmentIndex !== -1) {\n      newUrl = url.substring(0, fragmentIndex);\n      parsed = parseCallbackParams(url.substring(fragmentIndex + 1), supportedParams);\n      if (parsed.paramsString !== '') {\n        newUrl += '#' + parsed.paramsString;\n      }\n    }\n    if (parsed && parsed.oauthParams) {\n      if (kc.flow === 'standard' || kc.flow === 'hybrid') {\n        if ((parsed.oauthParams.code || parsed.oauthParams.error) && parsed.oauthParams.state) {\n          parsed.oauthParams.newUrl = newUrl;\n          return parsed.oauthParams;\n        }\n      } else if (kc.flow === 'implicit') {\n        if ((parsed.oauthParams.access_token || parsed.oauthParams.error) && parsed.oauthParams.state) {\n          parsed.oauthParams.newUrl = newUrl;\n          return parsed.oauthParams;\n        }\n      }\n    }\n  }\n  function parseCallbackParams(paramsString, supportedParams) {\n    var p = paramsString.split('&');\n    var result = {\n      paramsString: '',\n      oauthParams: {}\n    };\n    for (var i = 0; i < p.length; i++) {\n      var split = p[i].indexOf(\"=\");\n      var key = p[i].slice(0, split);\n      if (supportedParams.indexOf(key) !== -1) {\n        result.oauthParams[key] = p[i].slice(split + 1);\n      } else {\n        if (result.paramsString !== '') {\n          result.paramsString += '&';\n        }\n        result.paramsString += p[i];\n      }\n    }\n    return result;\n  }\n  function createPromise() {\n    // Need to create a native Promise which also preserves the\n    // interface of the custom promise type previously used by the API\n    var p = {\n      setSuccess: function (result) {\n        p.resolve(result);\n      },\n      setError: function (result) {\n        p.reject(result);\n      }\n    };\n    p.promise = new Promise(function (resolve, reject) {\n      p.resolve = resolve;\n      p.reject = reject;\n    });\n    return p;\n  }\n\n  // Function to extend existing native Promise with timeout\n  function applyTimeoutToPromise(promise, timeout, errorMessage) {\n    var timeoutHandle = null;\n    var timeoutPromise = new Promise(function (resolve, reject) {\n      timeoutHandle = setTimeout(function () {\n        reject({\n          \"error\": errorMessage || \"Promise is not settled within timeout of \" + timeout + \"ms\"\n        });\n      }, timeout);\n    });\n    return Promise.race([promise, timeoutPromise]).finally(function () {\n      clearTimeout(timeoutHandle);\n    });\n  }\n  function setupCheckLoginIframe() {\n    var promise = createPromise();\n    if (!loginIframe.enable) {\n      promise.setSuccess();\n      return promise.promise;\n    }\n    if (loginIframe.iframe) {\n      promise.setSuccess();\n      return promise.promise;\n    }\n    var iframe = document.createElement('iframe');\n    loginIframe.iframe = iframe;\n    iframe.onload = function () {\n      var authUrl = kc.endpoints.authorize();\n      if (authUrl.charAt(0) === '/') {\n        loginIframe.iframeOrigin = getOrigin();\n      } else {\n        loginIframe.iframeOrigin = authUrl.substring(0, authUrl.indexOf('/', 8));\n      }\n      promise.setSuccess();\n    };\n    var src = kc.endpoints.checkSessionIframe();\n    iframe.setAttribute('src', src);\n    iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n    iframe.setAttribute('title', 'keycloak-session-iframe');\n    iframe.style.display = 'none';\n    document.body.appendChild(iframe);\n    var messageCallback = function (event) {\n      if (event.origin !== loginIframe.iframeOrigin || loginIframe.iframe.contentWindow !== event.source) {\n        return;\n      }\n      if (!(event.data == 'unchanged' || event.data == 'changed' || event.data == 'error')) {\n        return;\n      }\n      if (event.data != 'unchanged') {\n        kc.clearToken();\n      }\n      var callbacks = loginIframe.callbackList.splice(0, loginIframe.callbackList.length);\n      for (var i = callbacks.length - 1; i >= 0; --i) {\n        var promise = callbacks[i];\n        if (event.data == 'error') {\n          promise.setError();\n        } else {\n          promise.setSuccess(event.data == 'unchanged');\n        }\n      }\n    };\n    window.addEventListener('message', messageCallback, false);\n    return promise.promise;\n  }\n  function scheduleCheckIframe() {\n    if (loginIframe.enable) {\n      if (kc.token) {\n        setTimeout(function () {\n          checkLoginIframe().then(function (unchanged) {\n            if (unchanged) {\n              scheduleCheckIframe();\n            }\n          });\n        }, loginIframe.interval * 1000);\n      }\n    }\n  }\n  function checkLoginIframe() {\n    var promise = createPromise();\n    if (loginIframe.iframe && loginIframe.iframeOrigin) {\n      var msg = kc.clientId + ' ' + (kc.sessionId ? kc.sessionId : '');\n      loginIframe.callbackList.push(promise);\n      var origin = loginIframe.iframeOrigin;\n      if (loginIframe.callbackList.length == 1) {\n        loginIframe.iframe.contentWindow.postMessage(msg, origin);\n      }\n    } else {\n      promise.setSuccess();\n    }\n    return promise.promise;\n  }\n  function check3pCookiesSupported() {\n    var promise = createPromise();\n    if (loginIframe.enable || kc.silentCheckSsoRedirectUri) {\n      var iframe = document.createElement('iframe');\n      iframe.setAttribute('src', kc.endpoints.thirdPartyCookiesIframe());\n      iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n      iframe.setAttribute('title', 'keycloak-3p-check-iframe');\n      iframe.style.display = 'none';\n      document.body.appendChild(iframe);\n      var messageCallback = function (event) {\n        if (iframe.contentWindow !== event.source) {\n          return;\n        }\n        if (event.data !== \"supported\" && event.data !== \"unsupported\") {\n          return;\n        } else if (event.data === \"unsupported\") {\n          logWarn(\"[KEYCLOAK] Your browser is blocking access to 3rd-party cookies, this means:\\n\\n\" + \" - It is not possible to retrieve tokens without redirecting to the Keycloak server (a.k.a. no support for silent authentication).\\n\" + \" - It is not possible to automatically detect changes to the session status (such as the user logging out in another tab).\\n\\n\" + \"For more information see: https://www.keycloak.org/docs/latest/securing_apps/#_modern_browsers\");\n          loginIframe.enable = false;\n          if (kc.silentCheckSsoFallback) {\n            kc.silentCheckSsoRedirectUri = false;\n          }\n        }\n        document.body.removeChild(iframe);\n        window.removeEventListener(\"message\", messageCallback);\n        promise.setSuccess();\n      };\n      window.addEventListener('message', messageCallback, false);\n    } else {\n      promise.setSuccess();\n    }\n    return applyTimeoutToPromise(promise.promise, kc.messageReceiveTimeout, \"Timeout when waiting for 3rd party check iframe message.\");\n  }\n  function loadAdapter(type) {\n    if (!type || type == 'default') {\n      return {\n        login: function (options) {\n          window.location.assign(kc.createLoginUrl(options));\n          return createPromise().promise;\n        },\n        logout: function () {\n          var _ref = _asyncToGenerator(function* (options) {\n            const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n            if (logoutMethod === \"GET\") {\n              window.location.replace(kc.createLogoutUrl(options));\n              return;\n            }\n            const logoutUrl = kc.createLogoutUrl(options);\n            const response = yield fetch(logoutUrl, {\n              method: \"POST\",\n              headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n              },\n              body: new URLSearchParams({\n                id_token_hint: kc.idToken,\n                client_id: kc.clientId,\n                post_logout_redirect_uri: adapter.redirectUri(options, false)\n              })\n            });\n            if (response.redirected) {\n              window.location.href = response.url;\n              return;\n            }\n            if (response.ok) {\n              window.location.reload();\n              return;\n            }\n            throw new Error(\"Logout failed, request returned an error code.\");\n          });\n          return function logout(_x) {\n            return _ref.apply(this, arguments);\n          };\n        }(),\n        register: function (options) {\n          window.location.assign(kc.createRegisterUrl(options));\n          return createPromise().promise;\n        },\n        accountManagement: function () {\n          var accountUrl = kc.createAccountUrl();\n          if (typeof accountUrl !== 'undefined') {\n            window.location.href = accountUrl;\n          } else {\n            throw \"Not supported by the OIDC server\";\n          }\n          return createPromise().promise;\n        },\n        redirectUri: function (options, encodeHash) {\n          if (options && options.redirectUri) {\n            return options.redirectUri;\n          } else if (kc.redirectUri) {\n            return kc.redirectUri;\n          } else {\n            return location.href;\n          }\n        }\n      };\n    }\n    if (type == 'cordova') {\n      loginIframe.enable = false;\n      var cordovaOpenWindowWrapper = function (loginUrl, target, options) {\n        if (window.cordova && window.cordova.InAppBrowser) {\n          // Use inappbrowser for IOS and Android if available\n          return window.cordova.InAppBrowser.open(loginUrl, target, options);\n        } else {\n          return window.open(loginUrl, target, options);\n        }\n      };\n      var shallowCloneCordovaOptions = function (userOptions) {\n        if (userOptions && userOptions.cordovaOptions) {\n          return Object.keys(userOptions.cordovaOptions).reduce(function (options, optionName) {\n            options[optionName] = userOptions.cordovaOptions[optionName];\n            return options;\n          }, {});\n        } else {\n          return {};\n        }\n      };\n      var formatCordovaOptions = function (cordovaOptions) {\n        return Object.keys(cordovaOptions).reduce(function (options, optionName) {\n          options.push(optionName + \"=\" + cordovaOptions[optionName]);\n          return options;\n        }, []).join(\",\");\n      };\n      var createCordovaOptions = function (userOptions) {\n        var cordovaOptions = shallowCloneCordovaOptions(userOptions);\n        cordovaOptions.location = 'no';\n        if (userOptions && userOptions.prompt == 'none') {\n          cordovaOptions.hidden = 'yes';\n        }\n        return formatCordovaOptions(cordovaOptions);\n      };\n      var getCordovaRedirectUri = function () {\n        return kc.redirectUri || 'http://localhost';\n      };\n      return {\n        login: function (options) {\n          var promise = createPromise();\n          var cordovaOptions = createCordovaOptions(options);\n          var loginUrl = kc.createLoginUrl(options);\n          var ref = cordovaOpenWindowWrapper(loginUrl, '_blank', cordovaOptions);\n          var completed = false;\n          var closed = false;\n          var closeBrowser = function () {\n            closed = true;\n            ref.close();\n          };\n          ref.addEventListener('loadstart', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              var callback = parseCallback(event.url);\n              processCallback(callback, promise);\n              closeBrowser();\n              completed = true;\n            }\n          });\n          ref.addEventListener('loaderror', function (event) {\n            if (!completed) {\n              if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                var callback = parseCallback(event.url);\n                processCallback(callback, promise);\n                closeBrowser();\n                completed = true;\n              } else {\n                promise.setError();\n                closeBrowser();\n              }\n            }\n          });\n          ref.addEventListener('exit', function (event) {\n            if (!closed) {\n              promise.setError({\n                reason: \"closed_by_user\"\n              });\n            }\n          });\n          return promise.promise;\n        },\n        logout: function (options) {\n          var promise = createPromise();\n          var logoutUrl = kc.createLogoutUrl(options);\n          var ref = cordovaOpenWindowWrapper(logoutUrl, '_blank', 'location=no,hidden=yes,clearcache=yes');\n          var error;\n          ref.addEventListener('loadstart', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              ref.close();\n            }\n          });\n          ref.addEventListener('loaderror', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              ref.close();\n            } else {\n              error = true;\n              ref.close();\n            }\n          });\n          ref.addEventListener('exit', function (event) {\n            if (error) {\n              promise.setError();\n            } else {\n              kc.clearToken();\n              promise.setSuccess();\n            }\n          });\n          return promise.promise;\n        },\n        register: function (options) {\n          var promise = createPromise();\n          var registerUrl = kc.createRegisterUrl();\n          var cordovaOptions = createCordovaOptions(options);\n          var ref = cordovaOpenWindowWrapper(registerUrl, '_blank', cordovaOptions);\n          ref.addEventListener('loadstart', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              ref.close();\n              var oauth = parseCallback(event.url);\n              processCallback(oauth, promise);\n            }\n          });\n          return promise.promise;\n        },\n        accountManagement: function () {\n          var accountUrl = kc.createAccountUrl();\n          if (typeof accountUrl !== 'undefined') {\n            var ref = cordovaOpenWindowWrapper(accountUrl, '_blank', 'location=no');\n            ref.addEventListener('loadstart', function (event) {\n              if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                ref.close();\n              }\n            });\n          } else {\n            throw \"Not supported by the OIDC server\";\n          }\n        },\n        redirectUri: function (options) {\n          return getCordovaRedirectUri();\n        }\n      };\n    }\n    if (type == 'cordova-native') {\n      loginIframe.enable = false;\n      return {\n        login: function (options) {\n          var promise = createPromise();\n          var loginUrl = kc.createLoginUrl(options);\n          universalLinks.subscribe('keycloak', function (event) {\n            universalLinks.unsubscribe('keycloak');\n            window.cordova.plugins.browsertab.close();\n            var oauth = parseCallback(event.url);\n            processCallback(oauth, promise);\n          });\n          window.cordova.plugins.browsertab.openUrl(loginUrl);\n          return promise.promise;\n        },\n        logout: function (options) {\n          var promise = createPromise();\n          var logoutUrl = kc.createLogoutUrl(options);\n          universalLinks.subscribe('keycloak', function (event) {\n            universalLinks.unsubscribe('keycloak');\n            window.cordova.plugins.browsertab.close();\n            kc.clearToken();\n            promise.setSuccess();\n          });\n          window.cordova.plugins.browsertab.openUrl(logoutUrl);\n          return promise.promise;\n        },\n        register: function (options) {\n          var promise = createPromise();\n          var registerUrl = kc.createRegisterUrl(options);\n          universalLinks.subscribe('keycloak', function (event) {\n            universalLinks.unsubscribe('keycloak');\n            window.cordova.plugins.browsertab.close();\n            var oauth = parseCallback(event.url);\n            processCallback(oauth, promise);\n          });\n          window.cordova.plugins.browsertab.openUrl(registerUrl);\n          return promise.promise;\n        },\n        accountManagement: function () {\n          var accountUrl = kc.createAccountUrl();\n          if (typeof accountUrl !== 'undefined') {\n            window.cordova.plugins.browsertab.openUrl(accountUrl);\n          } else {\n            throw \"Not supported by the OIDC server\";\n          }\n        },\n        redirectUri: function (options) {\n          if (options && options.redirectUri) {\n            return options.redirectUri;\n          } else if (kc.redirectUri) {\n            return kc.redirectUri;\n          } else {\n            return \"http://localhost\";\n          }\n        }\n      };\n    }\n    throw 'invalid adapter type: ' + type;\n  }\n  var LocalStorage = function () {\n    if (!(this instanceof LocalStorage)) {\n      return new LocalStorage();\n    }\n    localStorage.setItem('kc-test', 'test');\n    localStorage.removeItem('kc-test');\n    var cs = this;\n    function clearExpired() {\n      var time = new Date().getTime();\n      for (var i = 0; i < localStorage.length; i++) {\n        var key = localStorage.key(i);\n        if (key && key.indexOf('kc-callback-') == 0) {\n          var value = localStorage.getItem(key);\n          if (value) {\n            try {\n              var expires = JSON.parse(value).expires;\n              if (!expires || expires < time) {\n                localStorage.removeItem(key);\n              }\n            } catch (err) {\n              localStorage.removeItem(key);\n            }\n          }\n        }\n      }\n    }\n    cs.get = function (state) {\n      if (!state) {\n        return;\n      }\n      var key = 'kc-callback-' + state;\n      var value = localStorage.getItem(key);\n      if (value) {\n        localStorage.removeItem(key);\n        value = JSON.parse(value);\n      }\n      clearExpired();\n      return value;\n    };\n    cs.add = function (state) {\n      clearExpired();\n      var key = 'kc-callback-' + state.state;\n      state.expires = new Date().getTime() + 60 * 60 * 1000;\n      localStorage.setItem(key, JSON.stringify(state));\n    };\n  };\n  var CookieStorage = function () {\n    if (!(this instanceof CookieStorage)) {\n      return new CookieStorage();\n    }\n    var cs = this;\n    cs.get = function (state) {\n      if (!state) {\n        return;\n      }\n      var value = getCookie('kc-callback-' + state);\n      setCookie('kc-callback-' + state, '', cookieExpiration(-100));\n      if (value) {\n        return JSON.parse(value);\n      }\n    };\n    cs.add = function (state) {\n      setCookie('kc-callback-' + state.state, JSON.stringify(state), cookieExpiration(60));\n    };\n    cs.removeItem = function (key) {\n      setCookie(key, '', cookieExpiration(-100));\n    };\n    var cookieExpiration = function (minutes) {\n      var exp = new Date();\n      exp.setTime(exp.getTime() + minutes * 60 * 1000);\n      return exp;\n    };\n    var getCookie = function (key) {\n      var name = key + '=';\n      var ca = document.cookie.split(';');\n      for (var i = 0; i < ca.length; i++) {\n        var c = ca[i];\n        while (c.charAt(0) == ' ') {\n          c = c.substring(1);\n        }\n        if (c.indexOf(name) == 0) {\n          return c.substring(name.length, c.length);\n        }\n      }\n      return '';\n    };\n    var setCookie = function (key, value, expirationDate) {\n      var cookie = key + '=' + value + '; ' + 'expires=' + expirationDate.toUTCString() + '; ';\n      document.cookie = cookie;\n    };\n  };\n  function createCallbackStorage() {\n    try {\n      return new LocalStorage();\n    } catch (err) {}\n    return new CookieStorage();\n  }\n  function createLogger(fn) {\n    return function () {\n      if (kc.enableLogging) {\n        fn.apply(console, Array.prototype.slice.call(arguments));\n      }\n    };\n  }\n}\n\n// See: https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\nfunction bytesToBase64(bytes) {\n  const binString = String.fromCodePoint(...bytes);\n  return btoa(binString);\n}\nexport { Keycloak as default };", "map": {"version": 3, "names": ["sha256", "jwtDecode", "Promise", "Error", "Keycloak", "config", "kc", "adapter", "refreshQueue", "callbackStorage", "loginIframe", "enable", "callbackList", "interval", "scripts", "document", "getElementsByTagName", "i", "length", "src", "indexOf", "iframeVersion", "substring", "split", "useNonce", "logInfo", "createLogger", "console", "info", "log<PERSON>arn", "warn", "init", "initOptions", "didInitialize", "authenticated", "createCallbackStorage", "adapters", "loadAdapter", "window", "Cordova", "<PERSON><PERSON>", "checkLoginIframe", "checkLoginIframeInterval", "onLoad", "loginRequired", "responseMode", "flow", "responseType", "timeSkew", "redirectUri", "silentCheckSsoRedirectUri", "silentCheckSsoFallback", "pkceMethod", "TypeError", "enableLogging", "logoutMethod", "scope", "acr<PERSON><PERSON><PERSON>", "messageReceiveTimeout", "promise", "createPromise", "initPromise", "then", "onReady", "setSuccess", "catch", "error", "setError", "config<PERSON>rom<PERSON>", "loadConfig", "do<PERSON><PERSON><PERSON>", "prompt", "options", "locale", "login", "checkSsoSilently", "ifrm", "createElement", "createLoginUrl", "setAttribute", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "messageCallback", "event", "origin", "location", "contentWindow", "source", "o<PERSON>h", "parse<PERSON><PERSON>back", "data", "processCallback", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "addEventListener", "setupCheckLoginIframe", "unchanged", "processInit", "callback", "href", "history", "replaceState", "state", "newUrl", "valid", "token", "refreshToken", "setToken", "idToken", "onAuthSuccess", "scheduleCheckIframe", "updateToken", "onAuthError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "checkReadyState", "readyState", "check3pCookiesSupported", "generateRandomData", "len", "array", "crypto", "msCrypto", "getRandomValues", "Uint8Array", "Array", "j", "Math", "floor", "random", "generateCodeVerifier", "generateRandomString", "alphabet", "randomData", "chars", "charCodeAt", "String", "fromCharCode", "apply", "generatePkceChallenge", "codeVerifier", "hashBytes", "arrayBuffer", "encodedHash", "bytesToBase64", "replace", "buildClaimsParameter", "requestedAcr", "claims", "id_token", "acr", "JSON", "stringify", "createUUID", "nonce", "callbackState", "encodeURIComponent", "baseUrl", "action", "endpoints", "register", "authorize", "url", "clientId", "maxAge", "loginHint", "idpHint", "claimsParameter", "pkceCodeVerifier", "pkceChallenge", "add", "logout", "createLogoutUrl", "createRegisterUrl", "createAccountUrl", "realm", "getRealmUrl", "undefined", "accountManagement", "hasRealmRole", "role", "access", "realmAccess", "roles", "hasResourceRole", "resource", "resourceAccess", "loadUserProfile", "req", "XMLHttpRequest", "open", "setRequestHeader", "onreadystatechange", "status", "profile", "parse", "responseText", "send", "loadUserInfo", "userinfo", "userInfo", "isTokenExpired", "minValidity", "tokenParsed", "expiresIn", "ceil", "Date", "getTime", "isNaN", "exec", "params", "push", "withCredentials", "timeLocal", "tokenResponse", "onAuthRefreshSuccess", "p", "pop", "clearToken", "onAuthRefreshError", "iframePromise", "onAuthLogout", "authServerUrl", "char<PERSON>t", "<PERSON><PERSON><PERSON><PERSON>", "protocol", "hostname", "port", "code", "onActionUpdate", "errorData", "error_description", "access_token", "authSuccess", "accessToken", "fulfillPromise", "idTokenParsed", "storedNonce", "configUrl", "setupOidcEndoints", "oidcConfiguration", "checkSessionIframe", "thirdPartyCookiesIframe", "authorization_endpoint", "token_endpoint", "end_session_endpoint", "check_session_iframe", "userinfo_endpoint", "fileLoaded", "oidcProvider", "match", "substr", "oidcProviderConfigUrl", "oidcProviderConfig", "xhr", "responseURL", "startsWith", "tokenTimeoutHandle", "clearTimeout", "refreshTokenParsed", "sessionId", "session_state", "subject", "sub", "realm_access", "resource_access", "iat", "onTokenExpired", "round", "setTimeout", "hexDigits", "s", "uuid", "join", "parseCallbackUrl", "oauthState", "get", "supportedParams", "queryIndex", "fragmentIndex", "parsed", "parseCallbackParams", "paramsString", "oauthParams", "result", "key", "slice", "resolve", "reject", "applyTimeoutToPromise", "timeout", "errorMessage", "timeoutH<PERSON>le", "timeoutPromise", "race", "finally", "iframe", "onload", "authUrl", "iframe<PERSON><PERSON>in", "callbacks", "splice", "msg", "postMessage", "type", "assign", "_ref", "_asyncToGenerator", "logoutUrl", "response", "fetch", "method", "headers", "URLSearchParams", "id_token_hint", "client_id", "post_logout_redirect_uri", "redirected", "ok", "reload", "_x", "arguments", "accountUrl", "encodeHash", "cordovaOpenWindowWrapper", "loginUrl", "target", "InAppBrowser", "shallowCloneCordovaOptions", "userOptions", "cordovaOptions", "Object", "keys", "reduce", "optionName", "formatCordovaOptions", "createCordovaOptions", "hidden", "getCordovaRedirectUri", "ref", "completed", "closed", "<PERSON><PERSON>rowser", "close", "reason", "registerUrl", "universalLinks", "subscribe", "unsubscribe", "plugins", "browsertab", "openUrl", "LocalStorage", "localStorage", "setItem", "removeItem", "cs", "clearExpired", "time", "value", "getItem", "expires", "err", "Cookie<PERSON>torage", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "cookieExpiration", "minutes", "exp", "setTime", "name", "ca", "cookie", "c", "expirationDate", "toUTCString", "fn", "prototype", "call", "bytes", "binString", "fromCodePoint", "btoa", "default"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/keycloak-js/dist/keycloak.mjs"], "sourcesContent": ["import sha256 from 'js-sha256';\nimport { jwtDecode } from 'jwt-decode';\n\n/*\n * Copyright 2016 Red Hat, Inc. and/or its affiliates\n * and other contributors as indicated by the <AUTHOR>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nif (typeof Promise === 'undefined') {\n    throw Error('Keycloak requires an environment that supports Promises. Make sure that you include the appropriate polyfill.');\n}\n\nfunction Keycloak (config) {\n    if (!(this instanceof Keycloak)) {\n        throw new Error(\"The 'Keycloak' constructor must be invoked with 'new'.\")\n    }\n\n    var kc = this;\n    var adapter;\n    var refreshQueue = [];\n    var callbackStorage;\n\n    var loginIframe = {\n        enable: true,\n        callbackList: [],\n        interval: 5\n    };\n\n    var scripts = document.getElementsByTagName('script');\n    for (var i = 0; i < scripts.length; i++) {\n        if ((scripts[i].src.indexOf('keycloak.js') !== -1 || scripts[i].src.indexOf('keycloak.min.js') !== -1) && scripts[i].src.indexOf('version=') !== -1) {\n            kc.iframeVersion = scripts[i].src.substring(scripts[i].src.indexOf('version=') + 8).split('&')[0];\n        }\n    }\n\n    var useNonce = true;\n    var logInfo = createLogger(console.info);\n    var logWarn = createLogger(console.warn);\n\n    kc.init = function (initOptions) {\n        if (kc.didInitialize) {\n            throw new Error(\"A 'Keycloak' instance can only be initialized once.\");\n        }\n\n        kc.didInitialize = true;\n\n        kc.authenticated = false;\n\n        callbackStorage = createCallbackStorage();\n        var adapters = ['default', 'cordova', 'cordova-native'];\n\n        if (initOptions && adapters.indexOf(initOptions.adapter) > -1) {\n            adapter = loadAdapter(initOptions.adapter);\n        } else if (initOptions && typeof initOptions.adapter === \"object\") {\n            adapter = initOptions.adapter;\n        } else {\n            if (window.Cordova || window.cordova) {\n                adapter = loadAdapter('cordova');\n            } else {\n                adapter = loadAdapter();\n            }\n        }\n\n        if (initOptions) {\n            if (typeof initOptions.useNonce !== 'undefined') {\n                useNonce = initOptions.useNonce;\n            }\n\n            if (typeof initOptions.checkLoginIframe !== 'undefined') {\n                loginIframe.enable = initOptions.checkLoginIframe;\n            }\n\n            if (initOptions.checkLoginIframeInterval) {\n                loginIframe.interval = initOptions.checkLoginIframeInterval;\n            }\n\n            if (initOptions.onLoad === 'login-required') {\n                kc.loginRequired = true;\n            }\n\n            if (initOptions.responseMode) {\n                if (initOptions.responseMode === 'query' || initOptions.responseMode === 'fragment') {\n                    kc.responseMode = initOptions.responseMode;\n                } else {\n                    throw 'Invalid value for responseMode';\n                }\n            }\n\n            if (initOptions.flow) {\n                switch (initOptions.flow) {\n                    case 'standard':\n                        kc.responseType = 'code';\n                        break;\n                    case 'implicit':\n                        kc.responseType = 'id_token token';\n                        break;\n                    case 'hybrid':\n                        kc.responseType = 'code id_token token';\n                        break;\n                    default:\n                        throw 'Invalid value for flow';\n                }\n                kc.flow = initOptions.flow;\n            }\n\n            if (initOptions.timeSkew != null) {\n                kc.timeSkew = initOptions.timeSkew;\n            }\n\n            if(initOptions.redirectUri) {\n                kc.redirectUri = initOptions.redirectUri;\n            }\n\n            if (initOptions.silentCheckSsoRedirectUri) {\n                kc.silentCheckSsoRedirectUri = initOptions.silentCheckSsoRedirectUri;\n            }\n\n            if (typeof initOptions.silentCheckSsoFallback === 'boolean') {\n                kc.silentCheckSsoFallback = initOptions.silentCheckSsoFallback;\n            } else {\n                kc.silentCheckSsoFallback = true;\n            }\n\n            if (initOptions.pkceMethod) {\n                if (initOptions.pkceMethod !== \"S256\") {\n                    throw new TypeError(`Invalid value for 'pkceMethod', expected 'S256' but got '${initOptions.pkceMethod}'.`);\n                }\n                kc.pkceMethod = initOptions.pkceMethod;\n            } else {\n                kc.pkceMethod = \"S256\";\n            }\n\n            if (typeof initOptions.enableLogging === 'boolean') {\n                kc.enableLogging = initOptions.enableLogging;\n            } else {\n                kc.enableLogging = false;\n            }\n\n            if (initOptions.logoutMethod === 'POST') {\n                kc.logoutMethod = 'POST';\n            } else {\n                kc.logoutMethod = 'GET';\n            }\n\n            if (typeof initOptions.scope === 'string') {\n                kc.scope = initOptions.scope;\n            }\n\n            if (typeof initOptions.acrValues === 'string') {\n                kc.acrValues = initOptions.acrValues;\n            }\n\n            if (typeof initOptions.messageReceiveTimeout === 'number' && initOptions.messageReceiveTimeout > 0) {\n                kc.messageReceiveTimeout = initOptions.messageReceiveTimeout;\n            } else {\n                kc.messageReceiveTimeout = 10000;\n            }\n        }\n\n        if (!kc.responseMode) {\n            kc.responseMode = 'fragment';\n        }\n        if (!kc.responseType) {\n            kc.responseType = 'code';\n            kc.flow = 'standard';\n        }\n\n        var promise = createPromise();\n\n        var initPromise = createPromise();\n        initPromise.promise.then(function() {\n            kc.onReady && kc.onReady(kc.authenticated);\n            promise.setSuccess(kc.authenticated);\n        }).catch(function(error) {\n            promise.setError(error);\n        });\n\n        var configPromise = loadConfig();\n\n        function onLoad() {\n            var doLogin = function(prompt) {\n                if (!prompt) {\n                    options.prompt = 'none';\n                }\n\n                if (initOptions && initOptions.locale) {\n                    options.locale = initOptions.locale;\n                }\n                kc.login(options).then(function () {\n                    initPromise.setSuccess();\n                }).catch(function (error) {\n                    initPromise.setError(error);\n                });\n            };\n\n            var checkSsoSilently = function() {\n                var ifrm = document.createElement(\"iframe\");\n                var src = kc.createLoginUrl({prompt: 'none', redirectUri: kc.silentCheckSsoRedirectUri});\n                ifrm.setAttribute(\"src\", src);\n                ifrm.setAttribute(\"sandbox\", \"allow-storage-access-by-user-activation allow-scripts allow-same-origin\");\n                ifrm.setAttribute(\"title\", \"keycloak-silent-check-sso\");\n                ifrm.style.display = \"none\";\n                document.body.appendChild(ifrm);\n\n                var messageCallback = function(event) {\n                    if (event.origin !== window.location.origin || ifrm.contentWindow !== event.source) {\n                        return;\n                    }\n\n                    var oauth = parseCallback(event.data);\n                    processCallback(oauth, initPromise);\n\n                    document.body.removeChild(ifrm);\n                    window.removeEventListener(\"message\", messageCallback);\n                };\n\n                window.addEventListener(\"message\", messageCallback);\n            };\n\n            var options = {};\n            switch (initOptions.onLoad) {\n                case 'check-sso':\n                    if (loginIframe.enable) {\n                        setupCheckLoginIframe().then(function() {\n                            checkLoginIframe().then(function (unchanged) {\n                                if (!unchanged) {\n                                    kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                                } else {\n                                    initPromise.setSuccess();\n                                }\n                            }).catch(function (error) {\n                                initPromise.setError(error);\n                            });\n                        });\n                    } else {\n                        kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                    }\n                    break;\n                case 'login-required':\n                    doLogin(true);\n                    break;\n                default:\n                    throw 'Invalid value for onLoad';\n            }\n        }\n\n        function processInit() {\n            var callback = parseCallback(window.location.href);\n\n            if (callback) {\n                window.history.replaceState(window.history.state, null, callback.newUrl);\n            }\n\n            if (callback && callback.valid) {\n                return setupCheckLoginIframe().then(function() {\n                    processCallback(callback, initPromise);\n                }).catch(function (error) {\n                    initPromise.setError(error);\n                });\n            } else if (initOptions) {\n                if (initOptions.token && initOptions.refreshToken) {\n                    setToken(initOptions.token, initOptions.refreshToken, initOptions.idToken);\n\n                    if (loginIframe.enable) {\n                        setupCheckLoginIframe().then(function() {\n                            checkLoginIframe().then(function (unchanged) {\n                                if (unchanged) {\n                                    kc.onAuthSuccess && kc.onAuthSuccess();\n                                    initPromise.setSuccess();\n                                    scheduleCheckIframe();\n                                } else {\n                                    initPromise.setSuccess();\n                                }\n                            }).catch(function (error) {\n                                initPromise.setError(error);\n                            });\n                        });\n                    } else {\n                        kc.updateToken(-1).then(function() {\n                            kc.onAuthSuccess && kc.onAuthSuccess();\n                            initPromise.setSuccess();\n                        }).catch(function(error) {\n                            kc.onAuthError && kc.onAuthError();\n                            if (initOptions.onLoad) {\n                                onLoad();\n                            } else {\n                                initPromise.setError(error);\n                            }\n                        });\n                    }\n                } else if (initOptions.onLoad) {\n                    onLoad();\n                } else {\n                    initPromise.setSuccess();\n                }\n            } else {\n                initPromise.setSuccess();\n            }\n        }\n\n        function domReady() {\n            var promise = createPromise();\n\n            var checkReadyState = function () {\n                if (document.readyState === 'interactive' || document.readyState === 'complete') {\n                    document.removeEventListener('readystatechange', checkReadyState);\n                    promise.setSuccess();\n                }\n            };\n            document.addEventListener('readystatechange', checkReadyState);\n\n            checkReadyState(); // just in case the event was already fired and we missed it (in case the init is done later than at the load time, i.e. it's done from code)\n\n            return promise.promise;\n        }\n\n        configPromise.then(function () {\n            domReady()\n                .then(check3pCookiesSupported)\n                .then(processInit)\n                .catch(function (error) {\n                    promise.setError(error);\n                });\n        });\n        configPromise.catch(function (error) {\n            promise.setError(error);\n        });\n\n        return promise.promise;\n    };\n\n    kc.login = function (options) {\n        return adapter.login(options);\n    };\n\n    function generateRandomData(len) {\n        // use web crypto APIs if possible\n        var array = null;\n        var crypto = window.crypto || window.msCrypto;\n        if (crypto && crypto.getRandomValues && window.Uint8Array) {\n            array = new Uint8Array(len);\n            crypto.getRandomValues(array);\n            return array;\n        }\n\n        // fallback to Math random\n        array = new Array(len);\n        for (var j = 0; j < array.length; j++) {\n            array[j] = Math.floor(256 * Math.random());\n        }\n        return array;\n    }\n\n    function generateCodeVerifier(len) {\n        return generateRandomString(len, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');\n    }\n\n    function generateRandomString(len, alphabet){\n        var randomData = generateRandomData(len);\n        var chars = new Array(len);\n        for (var i = 0; i < len; i++) {\n            chars[i] = alphabet.charCodeAt(randomData[i] % alphabet.length);\n        }\n        return String.fromCharCode.apply(null, chars);\n    }\n\n    function generatePkceChallenge(pkceMethod, codeVerifier) {\n        if (pkceMethod !== \"S256\") {\n            throw new TypeError(`Invalid value for 'pkceMethod', expected 'S256' but got '${pkceMethod}'.`);\n        }\n\n        // hash codeVerifier, then encode as url-safe base64 without padding\n        const hashBytes = new Uint8Array(sha256.arrayBuffer(codeVerifier));\n        const encodedHash = bytesToBase64(hashBytes)\n            .replace(/\\+/g, '-')\n            .replace(/\\//g, '_')\n            .replace(/\\=/g, '');\n\n        return encodedHash;\n    }\n\n    function buildClaimsParameter(requestedAcr){\n        var claims = {\n            id_token: {\n                acr: requestedAcr\n            }\n        };\n        return JSON.stringify(claims);\n    }\n\n    kc.createLoginUrl = function(options) {\n        var state = createUUID();\n        var nonce = createUUID();\n\n        var redirectUri = adapter.redirectUri(options);\n\n        var callbackState = {\n            state: state,\n            nonce: nonce,\n            redirectUri: encodeURIComponent(redirectUri)\n        };\n\n        if (options && options.prompt) {\n            callbackState.prompt = options.prompt;\n        }\n\n        var baseUrl;\n        if (options && options.action == 'register') {\n            baseUrl = kc.endpoints.register();\n        } else {\n            baseUrl = kc.endpoints.authorize();\n        }\n\n        var scope = options && options.scope || kc.scope;\n        if (!scope) {\n            // if scope is not set, default to \"openid\"\n            scope = \"openid\";\n        } else if (scope.indexOf(\"openid\") === -1) {\n            // if openid scope is missing, prefix the given scopes with it\n            scope = \"openid \" + scope;\n        }\n\n        var url = baseUrl\n            + '?client_id=' + encodeURIComponent(kc.clientId)\n            + '&redirect_uri=' + encodeURIComponent(redirectUri)\n            + '&state=' + encodeURIComponent(state)\n            + '&response_mode=' + encodeURIComponent(kc.responseMode)\n            + '&response_type=' + encodeURIComponent(kc.responseType)\n            + '&scope=' + encodeURIComponent(scope);\n        if (useNonce) {\n            url = url + '&nonce=' + encodeURIComponent(nonce);\n        }\n\n        if (options && options.prompt) {\n            url += '&prompt=' + encodeURIComponent(options.prompt);\n        }\n\n        if (options && options.maxAge) {\n            url += '&max_age=' + encodeURIComponent(options.maxAge);\n        }\n\n        if (options && options.loginHint) {\n            url += '&login_hint=' + encodeURIComponent(options.loginHint);\n        }\n\n        if (options && options.idpHint) {\n            url += '&kc_idp_hint=' + encodeURIComponent(options.idpHint);\n        }\n\n        if (options && options.action && options.action != 'register') {\n            url += '&kc_action=' + encodeURIComponent(options.action);\n        }\n\n        if (options && options.locale) {\n            url += '&ui_locales=' + encodeURIComponent(options.locale);\n        }\n\n        if (options && options.acr) {\n            var claimsParameter = buildClaimsParameter(options.acr);\n            url += '&claims=' + encodeURIComponent(claimsParameter);\n        }\n\n        if ((options && options.acrValues) || kc.acrValues) {\n            url += '&acr_values=' + encodeURIComponent(options.acrValues || kc.acrValues);\n        }\n\n        if (kc.pkceMethod) {\n            var codeVerifier = generateCodeVerifier(96);\n            callbackState.pkceCodeVerifier = codeVerifier;\n            var pkceChallenge = generatePkceChallenge(kc.pkceMethod, codeVerifier);\n            url += '&code_challenge=' + pkceChallenge;\n            url += '&code_challenge_method=' + kc.pkceMethod;\n        }\n\n        callbackStorage.add(callbackState);\n\n        return url;\n    };\n\n    kc.logout = function(options) {\n        return adapter.logout(options);\n    };\n\n    kc.createLogoutUrl = function(options) {\n\n        const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n        if (logoutMethod === 'POST') {\n            return kc.endpoints.logout();\n        }\n\n        var url = kc.endpoints.logout()\n            + '?client_id=' + encodeURIComponent(kc.clientId)\n            + '&post_logout_redirect_uri=' + encodeURIComponent(adapter.redirectUri(options, false));\n\n        if (kc.idToken) {\n            url += '&id_token_hint=' + encodeURIComponent(kc.idToken);\n        }\n\n        return url;\n    };\n\n    kc.register = function (options) {\n        return adapter.register(options);\n    };\n\n    kc.createRegisterUrl = function(options) {\n        if (!options) {\n            options = {};\n        }\n        options.action = 'register';\n        return kc.createLoginUrl(options);\n    };\n\n    kc.createAccountUrl = function(options) {\n        var realm = getRealmUrl();\n        var url = undefined;\n        if (typeof realm !== 'undefined') {\n            url = realm\n            + '/account'\n            + '?referrer=' + encodeURIComponent(kc.clientId)\n            + '&referrer_uri=' + encodeURIComponent(adapter.redirectUri(options));\n        }\n        return url;\n    };\n\n    kc.accountManagement = function() {\n        return adapter.accountManagement();\n    };\n\n    kc.hasRealmRole = function (role) {\n        var access = kc.realmAccess;\n        return !!access && access.roles.indexOf(role) >= 0;\n    };\n\n    kc.hasResourceRole = function(role, resource) {\n        if (!kc.resourceAccess) {\n            return false;\n        }\n\n        var access = kc.resourceAccess[resource || kc.clientId];\n        return !!access && access.roles.indexOf(role) >= 0;\n    };\n\n    kc.loadUserProfile = function() {\n        var url = getRealmUrl() + '/account';\n        var req = new XMLHttpRequest();\n        req.open('GET', url, true);\n        req.setRequestHeader('Accept', 'application/json');\n        req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n\n        var promise = createPromise();\n\n        req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n                if (req.status == 200) {\n                    kc.profile = JSON.parse(req.responseText);\n                    promise.setSuccess(kc.profile);\n                } else {\n                    promise.setError();\n                }\n            }\n        };\n\n        req.send();\n\n        return promise.promise;\n    };\n\n    kc.loadUserInfo = function() {\n        var url = kc.endpoints.userinfo();\n        var req = new XMLHttpRequest();\n        req.open('GET', url, true);\n        req.setRequestHeader('Accept', 'application/json');\n        req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n\n        var promise = createPromise();\n\n        req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n                if (req.status == 200) {\n                    kc.userInfo = JSON.parse(req.responseText);\n                    promise.setSuccess(kc.userInfo);\n                } else {\n                    promise.setError();\n                }\n            }\n        };\n\n        req.send();\n\n        return promise.promise;\n    };\n\n    kc.isTokenExpired = function(minValidity) {\n        if (!kc.tokenParsed || (!kc.refreshToken && kc.flow != 'implicit' )) {\n            throw 'Not authenticated';\n        }\n\n        if (kc.timeSkew == null) {\n            logInfo('[KEYCLOAK] Unable to determine if token is expired as timeskew is not set');\n            return true;\n        }\n\n        var expiresIn = kc.tokenParsed['exp'] - Math.ceil(new Date().getTime() / 1000) + kc.timeSkew;\n        if (minValidity) {\n            if (isNaN(minValidity)) {\n                throw 'Invalid minValidity';\n            }\n            expiresIn -= minValidity;\n        }\n        return expiresIn < 0;\n    };\n\n    kc.updateToken = function(minValidity) {\n        var promise = createPromise();\n\n        if (!kc.refreshToken) {\n            promise.setError();\n            return promise.promise;\n        }\n\n        minValidity = minValidity || 5;\n\n        var exec = function() {\n            var refreshToken = false;\n            if (minValidity == -1) {\n                refreshToken = true;\n                logInfo('[KEYCLOAK] Refreshing token: forced refresh');\n            } else if (!kc.tokenParsed || kc.isTokenExpired(minValidity)) {\n                refreshToken = true;\n                logInfo('[KEYCLOAK] Refreshing token: token expired');\n            }\n\n            if (!refreshToken) {\n                promise.setSuccess(false);\n            } else {\n                var params = 'grant_type=refresh_token&' + 'refresh_token=' + kc.refreshToken;\n                var url = kc.endpoints.token();\n\n                refreshQueue.push(promise);\n\n                if (refreshQueue.length == 1) {\n                    var req = new XMLHttpRequest();\n                    req.open('POST', url, true);\n                    req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n                    req.withCredentials = true;\n\n                    params += '&client_id=' + encodeURIComponent(kc.clientId);\n\n                    var timeLocal = new Date().getTime();\n\n                    req.onreadystatechange = function () {\n                        if (req.readyState == 4) {\n                            if (req.status == 200) {\n                                logInfo('[KEYCLOAK] Token refreshed');\n\n                                timeLocal = (timeLocal + new Date().getTime()) / 2;\n\n                                var tokenResponse = JSON.parse(req.responseText);\n\n                                setToken(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], timeLocal);\n\n                                kc.onAuthRefreshSuccess && kc.onAuthRefreshSuccess();\n                                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                                    p.setSuccess(true);\n                                }\n                            } else {\n                                logWarn('[KEYCLOAK] Failed to refresh token');\n\n                                if (req.status == 400) {\n                                    kc.clearToken();\n                                }\n\n                                kc.onAuthRefreshError && kc.onAuthRefreshError();\n                                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                                    p.setError(true);\n                                }\n                            }\n                        }\n                    };\n\n                    req.send(params);\n                }\n            }\n        };\n\n        if (loginIframe.enable) {\n            var iframePromise = checkLoginIframe();\n            iframePromise.then(function() {\n                exec();\n            }).catch(function(error) {\n                promise.setError(error);\n            });\n        } else {\n            exec();\n        }\n\n        return promise.promise;\n    };\n\n    kc.clearToken = function() {\n        if (kc.token) {\n            setToken(null, null, null);\n            kc.onAuthLogout && kc.onAuthLogout();\n            if (kc.loginRequired) {\n                kc.login();\n            }\n        }\n    };\n\n    function getRealmUrl() {\n        if (typeof kc.authServerUrl !== 'undefined') {\n            if (kc.authServerUrl.charAt(kc.authServerUrl.length - 1) == '/') {\n                return kc.authServerUrl + 'realms/' + encodeURIComponent(kc.realm);\n            } else {\n                return kc.authServerUrl + '/realms/' + encodeURIComponent(kc.realm);\n            }\n        } else {\n            return undefined;\n        }\n    }\n\n    function getOrigin() {\n        if (!window.location.origin) {\n            return window.location.protocol + \"//\" + window.location.hostname + (window.location.port ? ':' + window.location.port: '');\n        } else {\n            return window.location.origin;\n        }\n    }\n\n    function processCallback(oauth, promise) {\n        var code = oauth.code;\n        var error = oauth.error;\n        var prompt = oauth.prompt;\n\n        var timeLocal = new Date().getTime();\n\n        if (oauth['kc_action_status']) {\n            kc.onActionUpdate && kc.onActionUpdate(oauth['kc_action_status']);\n        }\n\n        if (error) {\n            if (prompt != 'none') {\n                var errorData = { error: error, error_description: oauth.error_description };\n                kc.onAuthError && kc.onAuthError(errorData);\n                promise && promise.setError(errorData);\n            } else {\n                promise && promise.setSuccess();\n            }\n            return;\n        } else if ((kc.flow != 'standard') && (oauth.access_token || oauth.id_token)) {\n            authSuccess(oauth.access_token, null, oauth.id_token, true);\n        }\n\n        if ((kc.flow != 'implicit') && code) {\n            var params = 'code=' + code + '&grant_type=authorization_code';\n            var url = kc.endpoints.token();\n\n            var req = new XMLHttpRequest();\n            req.open('POST', url, true);\n            req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n\n            params += '&client_id=' + encodeURIComponent(kc.clientId);\n            params += '&redirect_uri=' + oauth.redirectUri;\n\n            if (oauth.pkceCodeVerifier) {\n                params += '&code_verifier=' + oauth.pkceCodeVerifier;\n            }\n\n            req.withCredentials = true;\n\n            req.onreadystatechange = function() {\n                if (req.readyState == 4) {\n                    if (req.status == 200) {\n\n                        var tokenResponse = JSON.parse(req.responseText);\n                        authSuccess(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], kc.flow === 'standard');\n                        scheduleCheckIframe();\n                    } else {\n                        kc.onAuthError && kc.onAuthError();\n                        promise && promise.setError();\n                    }\n                }\n            };\n\n            req.send(params);\n        }\n\n        function authSuccess(accessToken, refreshToken, idToken, fulfillPromise) {\n            timeLocal = (timeLocal + new Date().getTime()) / 2;\n\n            setToken(accessToken, refreshToken, idToken, timeLocal);\n\n            if (useNonce && (kc.idTokenParsed && kc.idTokenParsed.nonce != oauth.storedNonce)) {\n                logInfo('[KEYCLOAK] Invalid nonce, clearing token');\n                kc.clearToken();\n                promise && promise.setError();\n            } else {\n                if (fulfillPromise) {\n                    kc.onAuthSuccess && kc.onAuthSuccess();\n                    promise && promise.setSuccess();\n                }\n            }\n        }\n\n    }\n\n    function loadConfig(url) {\n        var promise = createPromise();\n        var configUrl;\n\n        if (!config) {\n            configUrl = 'keycloak.json';\n        } else if (typeof config === 'string') {\n            configUrl = config;\n        }\n\n        function setupOidcEndoints(oidcConfiguration) {\n            if (! oidcConfiguration) {\n                kc.endpoints = {\n                    authorize: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/auth';\n                    },\n                    token: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/token';\n                    },\n                    logout: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/logout';\n                    },\n                    checkSessionIframe: function() {\n                        var src = getRealmUrl() + '/protocol/openid-connect/login-status-iframe.html';\n                        if (kc.iframeVersion) {\n                            src = src + '?version=' + kc.iframeVersion;\n                        }\n                        return src;\n                    },\n                    thirdPartyCookiesIframe: function() {\n                        var src = getRealmUrl() + '/protocol/openid-connect/3p-cookies/step1.html';\n                        if (kc.iframeVersion) {\n                            src = src + '?version=' + kc.iframeVersion;\n                        }\n                        return src;\n                    },\n                    register: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/registrations';\n                    },\n                    userinfo: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/userinfo';\n                    }\n                };\n            } else {\n                kc.endpoints = {\n                    authorize: function() {\n                        return oidcConfiguration.authorization_endpoint;\n                    },\n                    token: function() {\n                        return oidcConfiguration.token_endpoint;\n                    },\n                    logout: function() {\n                        if (!oidcConfiguration.end_session_endpoint) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.end_session_endpoint;\n                    },\n                    checkSessionIframe: function() {\n                        if (!oidcConfiguration.check_session_iframe) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.check_session_iframe;\n                    },\n                    register: function() {\n                        throw 'Redirection to \"Register user\" page not supported in standard OIDC mode';\n                    },\n                    userinfo: function() {\n                        if (!oidcConfiguration.userinfo_endpoint) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.userinfo_endpoint;\n                    }\n                };\n            }\n        }\n\n        if (configUrl) {\n            var req = new XMLHttpRequest();\n            req.open('GET', configUrl, true);\n            req.setRequestHeader('Accept', 'application/json');\n\n            req.onreadystatechange = function () {\n                if (req.readyState == 4) {\n                    if (req.status == 200 || fileLoaded(req)) {\n                        var config = JSON.parse(req.responseText);\n\n                        kc.authServerUrl = config['auth-server-url'];\n                        kc.realm = config['realm'];\n                        kc.clientId = config['resource'];\n                        setupOidcEndoints(null);\n                        promise.setSuccess();\n                    } else {\n                        promise.setError();\n                    }\n                }\n            };\n\n            req.send();\n        } else {\n            if (!config.clientId) {\n                throw 'clientId missing';\n            }\n\n            kc.clientId = config.clientId;\n\n            var oidcProvider = config['oidcProvider'];\n            if (!oidcProvider) {\n                if (!config['url']) {\n                    var scripts = document.getElementsByTagName('script');\n                    for (var i = 0; i < scripts.length; i++) {\n                        if (scripts[i].src.match(/.*keycloak\\.js/)) {\n                            config.url = scripts[i].src.substr(0, scripts[i].src.indexOf('/js/keycloak.js'));\n                            break;\n                        }\n                    }\n                }\n                if (!config.realm) {\n                    throw 'realm missing';\n                }\n\n                kc.authServerUrl = config.url;\n                kc.realm = config.realm;\n                setupOidcEndoints(null);\n                promise.setSuccess();\n            } else {\n                if (typeof oidcProvider === 'string') {\n                    var oidcProviderConfigUrl;\n                    if (oidcProvider.charAt(oidcProvider.length - 1) == '/') {\n                        oidcProviderConfigUrl = oidcProvider + '.well-known/openid-configuration';\n                    } else {\n                        oidcProviderConfigUrl = oidcProvider + '/.well-known/openid-configuration';\n                    }\n                    var req = new XMLHttpRequest();\n                    req.open('GET', oidcProviderConfigUrl, true);\n                    req.setRequestHeader('Accept', 'application/json');\n\n                    req.onreadystatechange = function () {\n                        if (req.readyState == 4) {\n                            if (req.status == 200 || fileLoaded(req)) {\n                                var oidcProviderConfig = JSON.parse(req.responseText);\n                                setupOidcEndoints(oidcProviderConfig);\n                                promise.setSuccess();\n                            } else {\n                                promise.setError();\n                            }\n                        }\n                    };\n\n                    req.send();\n                } else {\n                    setupOidcEndoints(oidcProvider);\n                    promise.setSuccess();\n                }\n            }\n        }\n\n        return promise.promise;\n    }\n\n    function fileLoaded(xhr) {\n        return xhr.status == 0 && xhr.responseText && xhr.responseURL.startsWith('file:');\n    }\n\n    function setToken(token, refreshToken, idToken, timeLocal) {\n        if (kc.tokenTimeoutHandle) {\n            clearTimeout(kc.tokenTimeoutHandle);\n            kc.tokenTimeoutHandle = null;\n        }\n\n        if (refreshToken) {\n            kc.refreshToken = refreshToken;\n            kc.refreshTokenParsed = jwtDecode(refreshToken);\n        } else {\n            delete kc.refreshToken;\n            delete kc.refreshTokenParsed;\n        }\n\n        if (idToken) {\n            kc.idToken = idToken;\n            kc.idTokenParsed = jwtDecode(idToken);\n        } else {\n            delete kc.idToken;\n            delete kc.idTokenParsed;\n        }\n\n        if (token) {\n            kc.token = token;\n            kc.tokenParsed = jwtDecode(token);\n            kc.sessionId = kc.tokenParsed.session_state;\n            kc.authenticated = true;\n            kc.subject = kc.tokenParsed.sub;\n            kc.realmAccess = kc.tokenParsed.realm_access;\n            kc.resourceAccess = kc.tokenParsed.resource_access;\n\n            if (timeLocal) {\n                kc.timeSkew = Math.floor(timeLocal / 1000) - kc.tokenParsed.iat;\n            }\n\n            if (kc.timeSkew != null) {\n                logInfo('[KEYCLOAK] Estimated time difference between browser and server is ' + kc.timeSkew + ' seconds');\n\n                if (kc.onTokenExpired) {\n                    var expiresIn = (kc.tokenParsed['exp'] - (new Date().getTime() / 1000) + kc.timeSkew) * 1000;\n                    logInfo('[KEYCLOAK] Token expires in ' + Math.round(expiresIn / 1000) + ' s');\n                    if (expiresIn <= 0) {\n                        kc.onTokenExpired();\n                    } else {\n                        kc.tokenTimeoutHandle = setTimeout(kc.onTokenExpired, expiresIn);\n                    }\n                }\n            }\n        } else {\n            delete kc.token;\n            delete kc.tokenParsed;\n            delete kc.subject;\n            delete kc.realmAccess;\n            delete kc.resourceAccess;\n\n            kc.authenticated = false;\n        }\n    }\n\n    function createUUID() {\n        var hexDigits = '0123456789abcdef';\n        var s = generateRandomString(36, hexDigits).split(\"\");\n        s[14] = '4';\n        s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);\n        s[8] = s[13] = s[18] = s[23] = '-';\n        var uuid = s.join('');\n        return uuid;\n    }\n\n    function parseCallback(url) {\n        var oauth = parseCallbackUrl(url);\n        if (!oauth) {\n            return;\n        }\n\n        var oauthState = callbackStorage.get(oauth.state);\n\n        if (oauthState) {\n            oauth.valid = true;\n            oauth.redirectUri = oauthState.redirectUri;\n            oauth.storedNonce = oauthState.nonce;\n            oauth.prompt = oauthState.prompt;\n            oauth.pkceCodeVerifier = oauthState.pkceCodeVerifier;\n        }\n\n        return oauth;\n    }\n\n    function parseCallbackUrl(url) {\n        var supportedParams;\n        switch (kc.flow) {\n            case 'standard':\n                supportedParams = ['code', 'state', 'session_state', 'kc_action_status', 'iss'];\n                break;\n            case 'implicit':\n                supportedParams = ['access_token', 'token_type', 'id_token', 'state', 'session_state', 'expires_in', 'kc_action_status', 'iss'];\n                break;\n            case 'hybrid':\n                supportedParams = ['access_token', 'token_type', 'id_token', 'code', 'state', 'session_state', 'expires_in', 'kc_action_status', 'iss'];\n                break;\n        }\n\n        supportedParams.push('error');\n        supportedParams.push('error_description');\n        supportedParams.push('error_uri');\n\n        var queryIndex = url.indexOf('?');\n        var fragmentIndex = url.indexOf('#');\n\n        var newUrl;\n        var parsed;\n\n        if (kc.responseMode === 'query' && queryIndex !== -1) {\n            newUrl = url.substring(0, queryIndex);\n            parsed = parseCallbackParams(url.substring(queryIndex + 1, fragmentIndex !== -1 ? fragmentIndex : url.length), supportedParams);\n            if (parsed.paramsString !== '') {\n                newUrl += '?' + parsed.paramsString;\n            }\n            if (fragmentIndex !== -1) {\n                newUrl += url.substring(fragmentIndex);\n            }\n        } else if (kc.responseMode === 'fragment' && fragmentIndex !== -1) {\n            newUrl = url.substring(0, fragmentIndex);\n            parsed = parseCallbackParams(url.substring(fragmentIndex + 1), supportedParams);\n            if (parsed.paramsString !== '') {\n                newUrl += '#' + parsed.paramsString;\n            }\n        }\n\n        if (parsed && parsed.oauthParams) {\n            if (kc.flow === 'standard' || kc.flow === 'hybrid') {\n                if ((parsed.oauthParams.code || parsed.oauthParams.error) && parsed.oauthParams.state) {\n                    parsed.oauthParams.newUrl = newUrl;\n                    return parsed.oauthParams;\n                }\n            } else if (kc.flow === 'implicit') {\n                if ((parsed.oauthParams.access_token || parsed.oauthParams.error) && parsed.oauthParams.state) {\n                    parsed.oauthParams.newUrl = newUrl;\n                    return parsed.oauthParams;\n                }\n            }\n        }\n    }\n\n    function parseCallbackParams(paramsString, supportedParams) {\n        var p = paramsString.split('&');\n        var result = {\n            paramsString: '',\n            oauthParams: {}\n        };\n        for (var i = 0; i < p.length; i++) {\n            var split = p[i].indexOf(\"=\");\n            var key = p[i].slice(0, split);\n            if (supportedParams.indexOf(key) !== -1) {\n                result.oauthParams[key] = p[i].slice(split + 1);\n            } else {\n                if (result.paramsString !== '') {\n                    result.paramsString += '&';\n                }\n                result.paramsString += p[i];\n            }\n        }\n        return result;\n    }\n\n    function createPromise() {\n        // Need to create a native Promise which also preserves the\n        // interface of the custom promise type previously used by the API\n        var p = {\n            setSuccess: function(result) {\n                p.resolve(result);\n            },\n\n            setError: function(result) {\n                p.reject(result);\n            }\n        };\n        p.promise = new Promise(function(resolve, reject) {\n            p.resolve = resolve;\n            p.reject = reject;\n        });\n\n        return p;\n    }\n\n    // Function to extend existing native Promise with timeout\n    function applyTimeoutToPromise(promise, timeout, errorMessage) {\n        var timeoutHandle = null;\n        var timeoutPromise = new Promise(function (resolve, reject) {\n            timeoutHandle = setTimeout(function () {\n                reject({ \"error\": errorMessage || \"Promise is not settled within timeout of \" + timeout + \"ms\" });\n            }, timeout);\n        });\n\n        return Promise.race([promise, timeoutPromise]).finally(function () {\n            clearTimeout(timeoutHandle);\n        });\n    }\n\n    function setupCheckLoginIframe() {\n        var promise = createPromise();\n\n        if (!loginIframe.enable) {\n            promise.setSuccess();\n            return promise.promise;\n        }\n\n        if (loginIframe.iframe) {\n            promise.setSuccess();\n            return promise.promise;\n        }\n\n        var iframe = document.createElement('iframe');\n        loginIframe.iframe = iframe;\n\n        iframe.onload = function() {\n            var authUrl = kc.endpoints.authorize();\n            if (authUrl.charAt(0) === '/') {\n                loginIframe.iframeOrigin = getOrigin();\n            } else {\n                loginIframe.iframeOrigin = authUrl.substring(0, authUrl.indexOf('/', 8));\n            }\n            promise.setSuccess();\n        };\n\n        var src = kc.endpoints.checkSessionIframe();\n        iframe.setAttribute('src', src );\n        iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n        iframe.setAttribute('title', 'keycloak-session-iframe' );\n        iframe.style.display = 'none';\n        document.body.appendChild(iframe);\n\n        var messageCallback = function(event) {\n            if ((event.origin !== loginIframe.iframeOrigin) || (loginIframe.iframe.contentWindow !== event.source)) {\n                return;\n            }\n\n            if (!(event.data == 'unchanged' || event.data == 'changed' || event.data == 'error')) {\n                return;\n            }\n\n\n            if (event.data != 'unchanged') {\n                kc.clearToken();\n            }\n\n            var callbacks = loginIframe.callbackList.splice(0, loginIframe.callbackList.length);\n\n            for (var i = callbacks.length - 1; i >= 0; --i) {\n                var promise = callbacks[i];\n                if (event.data == 'error') {\n                    promise.setError();\n                } else {\n                    promise.setSuccess(event.data == 'unchanged');\n                }\n            }\n        };\n\n        window.addEventListener('message', messageCallback, false);\n\n        return promise.promise;\n    }\n\n    function scheduleCheckIframe() {\n        if (loginIframe.enable) {\n            if (kc.token) {\n                setTimeout(function() {\n                    checkLoginIframe().then(function(unchanged) {\n                        if (unchanged) {\n                            scheduleCheckIframe();\n                        }\n                    });\n                }, loginIframe.interval * 1000);\n            }\n        }\n    }\n\n    function checkLoginIframe() {\n        var promise = createPromise();\n\n        if (loginIframe.iframe && loginIframe.iframeOrigin ) {\n            var msg = kc.clientId + ' ' + (kc.sessionId ? kc.sessionId : '');\n            loginIframe.callbackList.push(promise);\n            var origin = loginIframe.iframeOrigin;\n            if (loginIframe.callbackList.length == 1) {\n                loginIframe.iframe.contentWindow.postMessage(msg, origin);\n            }\n        } else {\n            promise.setSuccess();\n        }\n\n        return promise.promise;\n    }\n\n    function check3pCookiesSupported() {\n        var promise = createPromise();\n\n        if (loginIframe.enable || kc.silentCheckSsoRedirectUri) {\n            var iframe = document.createElement('iframe');\n            iframe.setAttribute('src', kc.endpoints.thirdPartyCookiesIframe());\n            iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n            iframe.setAttribute('title', 'keycloak-3p-check-iframe' );\n            iframe.style.display = 'none';\n            document.body.appendChild(iframe);\n\n            var messageCallback = function(event) {\n                if (iframe.contentWindow !== event.source) {\n                    return;\n                }\n\n                if (event.data !== \"supported\" && event.data !== \"unsupported\") {\n                    return;\n                } else if (event.data === \"unsupported\") {\n                    logWarn(\n                        \"[KEYCLOAK] Your browser is blocking access to 3rd-party cookies, this means:\\n\\n\" +\n                        \" - It is not possible to retrieve tokens without redirecting to the Keycloak server (a.k.a. no support for silent authentication).\\n\" +\n                        \" - It is not possible to automatically detect changes to the session status (such as the user logging out in another tab).\\n\\n\" +\n                        \"For more information see: https://www.keycloak.org/docs/latest/securing_apps/#_modern_browsers\"\n                    );\n\n                    loginIframe.enable = false;\n                    if (kc.silentCheckSsoFallback) {\n                        kc.silentCheckSsoRedirectUri = false;\n                    }\n                }\n\n                document.body.removeChild(iframe);\n                window.removeEventListener(\"message\", messageCallback);\n                promise.setSuccess();\n            };\n\n            window.addEventListener('message', messageCallback, false);\n        } else {\n            promise.setSuccess();\n        }\n\n        return applyTimeoutToPromise(promise.promise, kc.messageReceiveTimeout, \"Timeout when waiting for 3rd party check iframe message.\");\n    }\n\n    function loadAdapter(type) {\n        if (!type || type == 'default') {\n            return {\n                login: function(options) {\n                    window.location.assign(kc.createLoginUrl(options));\n                    return createPromise().promise;\n                },\n\n                logout: async function(options) {\n\n                    const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n                    if (logoutMethod === \"GET\") {\n                        window.location.replace(kc.createLogoutUrl(options));\n                        return;\n                    }\n\n                    const logoutUrl = kc.createLogoutUrl(options);\n                    const response = await fetch(logoutUrl, {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/x-www-form-urlencoded\"\n                        },\n                        body: new URLSearchParams({\n                            id_token_hint: kc.idToken,\n                            client_id: kc.clientId,\n                            post_logout_redirect_uri: adapter.redirectUri(options, false)\n                        })\n                    });\n\n                    if (response.redirected) {\n                        window.location.href = response.url;\n                        return;\n                    }\n\n                    if (response.ok) {\n                        window.location.reload();\n                        return;\n                    }\n\n                    throw new Error(\"Logout failed, request returned an error code.\");\n                },\n\n                register: function(options) {\n                    window.location.assign(kc.createRegisterUrl(options));\n                    return createPromise().promise;\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        window.location.href = accountUrl;\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                    return createPromise().promise;\n                },\n\n                redirectUri: function(options, encodeHash) {\n\n                    if (options && options.redirectUri) {\n                        return options.redirectUri;\n                    } else if (kc.redirectUri) {\n                        return kc.redirectUri;\n                    } else {\n                        return location.href;\n                    }\n                }\n            };\n        }\n\n        if (type == 'cordova') {\n            loginIframe.enable = false;\n            var cordovaOpenWindowWrapper = function(loginUrl, target, options) {\n                if (window.cordova && window.cordova.InAppBrowser) {\n                    // Use inappbrowser for IOS and Android if available\n                    return window.cordova.InAppBrowser.open(loginUrl, target, options);\n                } else {\n                    return window.open(loginUrl, target, options);\n                }\n            };\n\n            var shallowCloneCordovaOptions = function (userOptions) {\n                if (userOptions && userOptions.cordovaOptions) {\n                    return Object.keys(userOptions.cordovaOptions).reduce(function (options, optionName) {\n                        options[optionName] = userOptions.cordovaOptions[optionName];\n                        return options;\n                    }, {});\n                } else {\n                    return {};\n                }\n            };\n\n            var formatCordovaOptions = function (cordovaOptions) {\n                return Object.keys(cordovaOptions).reduce(function (options, optionName) {\n                    options.push(optionName+\"=\"+cordovaOptions[optionName]);\n                    return options;\n                }, []).join(\",\");\n            };\n\n            var createCordovaOptions = function (userOptions) {\n                var cordovaOptions = shallowCloneCordovaOptions(userOptions);\n                cordovaOptions.location = 'no';\n                if (userOptions && userOptions.prompt == 'none') {\n                    cordovaOptions.hidden = 'yes';\n                }\n                return formatCordovaOptions(cordovaOptions);\n            };\n\n            var getCordovaRedirectUri = function() {\n                return kc.redirectUri || 'http://localhost';\n            };\n            \n            return {\n                login: function(options) {\n                    var promise = createPromise();\n\n                    var cordovaOptions = createCordovaOptions(options);\n                    var loginUrl = kc.createLoginUrl(options);\n                    var ref = cordovaOpenWindowWrapper(loginUrl, '_blank', cordovaOptions);\n                    var completed = false;\n\n                    var closed = false;\n                    var closeBrowser = function() {\n                        closed = true;\n                        ref.close();\n                    };\n\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            var callback = parseCallback(event.url);\n                            processCallback(callback, promise);\n                            closeBrowser();\n                            completed = true;\n                        }\n                    });\n\n                    ref.addEventListener('loaderror', function(event) {\n                        if (!completed) {\n                            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                                var callback = parseCallback(event.url);\n                                processCallback(callback, promise);\n                                closeBrowser();\n                                completed = true;\n                            } else {\n                                promise.setError();\n                                closeBrowser();\n                            }\n                        }\n                    });\n\n                    ref.addEventListener('exit', function(event) {\n                        if (!closed) {\n                            promise.setError({\n                                reason: \"closed_by_user\"\n                            });\n                        }\n                    });\n\n                    return promise.promise;\n                },\n\n                logout: function(options) {\n                    var promise = createPromise();\n\n                    var logoutUrl = kc.createLogoutUrl(options);\n                    var ref = cordovaOpenWindowWrapper(logoutUrl, '_blank', 'location=no,hidden=yes,clearcache=yes');\n\n                    var error;\n\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                        }\n                    });\n\n                    ref.addEventListener('loaderror', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                        } else {\n                            error = true;\n                            ref.close();\n                        }\n                    });\n\n                    ref.addEventListener('exit', function(event) {\n                        if (error) {\n                            promise.setError();\n                        } else {\n                            kc.clearToken();\n                            promise.setSuccess();\n                        }\n                    });\n\n                    return promise.promise;\n                },\n\n                register : function(options) {\n                    var promise = createPromise();\n                    var registerUrl = kc.createRegisterUrl();\n                    var cordovaOptions = createCordovaOptions(options);\n                    var ref = cordovaOpenWindowWrapper(registerUrl, '_blank', cordovaOptions);\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                            var oauth = parseCallback(event.url);\n                            processCallback(oauth, promise);\n                        }\n                    });\n                    return promise.promise;\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        var ref = cordovaOpenWindowWrapper(accountUrl, '_blank', 'location=no');\n                        ref.addEventListener('loadstart', function(event) {\n                            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                                ref.close();\n                            }\n                        });\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                },\n\n                redirectUri: function(options) {\n                    return getCordovaRedirectUri();\n                }\n            }\n        }\n\n        if (type == 'cordova-native') {\n            loginIframe.enable = false;\n\n            return {\n                login: function(options) {\n                    var promise = createPromise();\n                    var loginUrl = kc.createLoginUrl(options);\n\n                    universalLinks.subscribe('keycloak', function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        var oauth = parseCallback(event.url);\n                        processCallback(oauth, promise);\n                    });\n\n                    window.cordova.plugins.browsertab.openUrl(loginUrl);\n                    return promise.promise;\n                },\n\n                logout: function(options) {\n                    var promise = createPromise();\n                    var logoutUrl = kc.createLogoutUrl(options);\n\n                    universalLinks.subscribe('keycloak', function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        kc.clearToken();\n                        promise.setSuccess();\n                    });\n\n                    window.cordova.plugins.browsertab.openUrl(logoutUrl);\n                    return promise.promise;\n                },\n\n                register : function(options) {\n                    var promise = createPromise();\n                    var registerUrl = kc.createRegisterUrl(options);\n                    universalLinks.subscribe('keycloak' , function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        var oauth = parseCallback(event.url);\n                        processCallback(oauth, promise);\n                    });\n                    window.cordova.plugins.browsertab.openUrl(registerUrl);\n                    return promise.promise;\n\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        window.cordova.plugins.browsertab.openUrl(accountUrl);\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                },\n\n                redirectUri: function(options) {\n                    if (options && options.redirectUri) {\n                        return options.redirectUri;\n                    } else if (kc.redirectUri) {\n                        return kc.redirectUri;\n                    } else {\n                        return \"http://localhost\";\n                    }\n                }\n            }\n        }\n\n        throw 'invalid adapter type: ' + type;\n    }\n\n    var LocalStorage = function() {\n        if (!(this instanceof LocalStorage)) {\n            return new LocalStorage();\n        }\n\n        localStorage.setItem('kc-test', 'test');\n        localStorage.removeItem('kc-test');\n\n        var cs = this;\n\n        function clearExpired() {\n            var time = new Date().getTime();\n            for (var i = 0; i < localStorage.length; i++)  {\n                var key = localStorage.key(i);\n                if (key && key.indexOf('kc-callback-') == 0) {\n                    var value = localStorage.getItem(key);\n                    if (value) {\n                        try {\n                            var expires = JSON.parse(value).expires;\n                            if (!expires || expires < time) {\n                                localStorage.removeItem(key);\n                            }\n                        } catch (err) {\n                            localStorage.removeItem(key);\n                        }\n                    }\n                }\n            }\n        }\n\n        cs.get = function(state) {\n            if (!state) {\n                return;\n            }\n\n            var key = 'kc-callback-' + state;\n            var value = localStorage.getItem(key);\n            if (value) {\n                localStorage.removeItem(key);\n                value = JSON.parse(value);\n            }\n\n            clearExpired();\n            return value;\n        };\n\n        cs.add = function(state) {\n            clearExpired();\n\n            var key = 'kc-callback-' + state.state;\n            state.expires = new Date().getTime() + (60 * 60 * 1000);\n            localStorage.setItem(key, JSON.stringify(state));\n        };\n    };\n\n    var CookieStorage = function() {\n        if (!(this instanceof CookieStorage)) {\n            return new CookieStorage();\n        }\n\n        var cs = this;\n\n        cs.get = function(state) {\n            if (!state) {\n                return;\n            }\n\n            var value = getCookie('kc-callback-' + state);\n            setCookie('kc-callback-' + state, '', cookieExpiration(-100));\n            if (value) {\n                return JSON.parse(value);\n            }\n        };\n\n        cs.add = function(state) {\n            setCookie('kc-callback-' + state.state, JSON.stringify(state), cookieExpiration(60));\n        };\n\n        cs.removeItem = function(key) {\n            setCookie(key, '', cookieExpiration(-100));\n        };\n\n        var cookieExpiration = function (minutes) {\n            var exp = new Date();\n            exp.setTime(exp.getTime() + (minutes*60*1000));\n            return exp;\n        };\n\n        var getCookie = function (key) {\n            var name = key + '=';\n            var ca = document.cookie.split(';');\n            for (var i = 0; i < ca.length; i++) {\n                var c = ca[i];\n                while (c.charAt(0) == ' ') {\n                    c = c.substring(1);\n                }\n                if (c.indexOf(name) == 0) {\n                    return c.substring(name.length, c.length);\n                }\n            }\n            return '';\n        };\n\n        var setCookie = function (key, value, expirationDate) {\n            var cookie = key + '=' + value + '; '\n                + 'expires=' + expirationDate.toUTCString() + '; ';\n            document.cookie = cookie;\n        };\n    };\n\n    function createCallbackStorage() {\n        try {\n            return new LocalStorage();\n        } catch (err) {\n        }\n\n        return new CookieStorage();\n    }\n\n    function createLogger(fn) {\n        return function() {\n            if (kc.enableLogging) {\n                fn.apply(console, Array.prototype.slice.call(arguments));\n            }\n        };\n    }\n}\n\n// See: https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\nfunction bytesToBase64(bytes) {\n    const binString = String.fromCodePoint(...bytes);\n    return btoa(binString);\n}\n\nexport { Keycloak as default };\n"], "mappings": ";AAAA,OAAOA,MAAM,MAAM,WAAW;AAC9B,SAASC,SAAS,QAAQ,YAAY;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;EAChC,MAAMC,KAAK,CAAC,+GAA+G,CAAC;AAChI;AAEA,SAASC,QAAQA,CAAEC,MAAM,EAAE;EACvB,IAAI,EAAE,IAAI,YAAYD,QAAQ,CAAC,EAAE;IAC7B,MAAM,IAAID,KAAK,CAAC,wDAAwD,CAAC;EAC7E;EAEA,IAAIG,EAAE,GAAG,IAAI;EACb,IAAIC,OAAO;EACX,IAAIC,YAAY,GAAG,EAAE;EACrB,IAAIC,eAAe;EAEnB,IAAIC,WAAW,GAAG;IACdC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE;EACd,CAAC;EAED,IAAIC,OAAO,GAAGC,QAAQ,CAACC,oBAAoB,CAAC,QAAQ,CAAC;EACrD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAI,CAACH,OAAO,CAACG,CAAC,CAAC,CAACE,GAAG,CAACC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAIN,OAAO,CAACG,CAAC,CAAC,CAACE,GAAG,CAACC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,KAAKN,OAAO,CAACG,CAAC,CAAC,CAACE,GAAG,CAACC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MACjJd,EAAE,CAACe,aAAa,GAAGP,OAAO,CAACG,CAAC,CAAC,CAACE,GAAG,CAACG,SAAS,CAACR,OAAO,CAACG,CAAC,CAAC,CAACE,GAAG,CAACC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrG;EACJ;EAEA,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAIC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAACC,IAAI,CAAC;EACxC,IAAIC,OAAO,GAAGH,YAAY,CAACC,OAAO,CAACG,IAAI,CAAC;EAExCxB,EAAE,CAACyB,IAAI,GAAG,UAAUC,WAAW,EAAE;IAC7B,IAAI1B,EAAE,CAAC2B,aAAa,EAAE;MAClB,MAAM,IAAI9B,KAAK,CAAC,qDAAqD,CAAC;IAC1E;IAEAG,EAAE,CAAC2B,aAAa,GAAG,IAAI;IAEvB3B,EAAE,CAAC4B,aAAa,GAAG,KAAK;IAExBzB,eAAe,GAAG0B,qBAAqB,CAAC,CAAC;IACzC,IAAIC,QAAQ,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,gBAAgB,CAAC;IAEvD,IAAIJ,WAAW,IAAII,QAAQ,CAAChB,OAAO,CAACY,WAAW,CAACzB,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3DA,OAAO,GAAG8B,WAAW,CAACL,WAAW,CAACzB,OAAO,CAAC;IAC9C,CAAC,MAAM,IAAIyB,WAAW,IAAI,OAAOA,WAAW,CAACzB,OAAO,KAAK,QAAQ,EAAE;MAC/DA,OAAO,GAAGyB,WAAW,CAACzB,OAAO;IACjC,CAAC,MAAM;MACH,IAAI+B,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,OAAO,EAAE;QAClCjC,OAAO,GAAG8B,WAAW,CAAC,SAAS,CAAC;MACpC,CAAC,MAAM;QACH9B,OAAO,GAAG8B,WAAW,CAAC,CAAC;MAC3B;IACJ;IAEA,IAAIL,WAAW,EAAE;MACb,IAAI,OAAOA,WAAW,CAACR,QAAQ,KAAK,WAAW,EAAE;QAC7CA,QAAQ,GAAGQ,WAAW,CAACR,QAAQ;MACnC;MAEA,IAAI,OAAOQ,WAAW,CAACS,gBAAgB,KAAK,WAAW,EAAE;QACrD/B,WAAW,CAACC,MAAM,GAAGqB,WAAW,CAACS,gBAAgB;MACrD;MAEA,IAAIT,WAAW,CAACU,wBAAwB,EAAE;QACtChC,WAAW,CAACG,QAAQ,GAAGmB,WAAW,CAACU,wBAAwB;MAC/D;MAEA,IAAIV,WAAW,CAACW,MAAM,KAAK,gBAAgB,EAAE;QACzCrC,EAAE,CAACsC,aAAa,GAAG,IAAI;MAC3B;MAEA,IAAIZ,WAAW,CAACa,YAAY,EAAE;QAC1B,IAAIb,WAAW,CAACa,YAAY,KAAK,OAAO,IAAIb,WAAW,CAACa,YAAY,KAAK,UAAU,EAAE;UACjFvC,EAAE,CAACuC,YAAY,GAAGb,WAAW,CAACa,YAAY;QAC9C,CAAC,MAAM;UACH,MAAM,gCAAgC;QAC1C;MACJ;MAEA,IAAIb,WAAW,CAACc,IAAI,EAAE;QAClB,QAAQd,WAAW,CAACc,IAAI;UACpB,KAAK,UAAU;YACXxC,EAAE,CAACyC,YAAY,GAAG,MAAM;YACxB;UACJ,KAAK,UAAU;YACXzC,EAAE,CAACyC,YAAY,GAAG,gBAAgB;YAClC;UACJ,KAAK,QAAQ;YACTzC,EAAE,CAACyC,YAAY,GAAG,qBAAqB;YACvC;UACJ;YACI,MAAM,wBAAwB;QACtC;QACAzC,EAAE,CAACwC,IAAI,GAAGd,WAAW,CAACc,IAAI;MAC9B;MAEA,IAAId,WAAW,CAACgB,QAAQ,IAAI,IAAI,EAAE;QAC9B1C,EAAE,CAAC0C,QAAQ,GAAGhB,WAAW,CAACgB,QAAQ;MACtC;MAEA,IAAGhB,WAAW,CAACiB,WAAW,EAAE;QACxB3C,EAAE,CAAC2C,WAAW,GAAGjB,WAAW,CAACiB,WAAW;MAC5C;MAEA,IAAIjB,WAAW,CAACkB,yBAAyB,EAAE;QACvC5C,EAAE,CAAC4C,yBAAyB,GAAGlB,WAAW,CAACkB,yBAAyB;MACxE;MAEA,IAAI,OAAOlB,WAAW,CAACmB,sBAAsB,KAAK,SAAS,EAAE;QACzD7C,EAAE,CAAC6C,sBAAsB,GAAGnB,WAAW,CAACmB,sBAAsB;MAClE,CAAC,MAAM;QACH7C,EAAE,CAAC6C,sBAAsB,GAAG,IAAI;MACpC;MAEA,IAAInB,WAAW,CAACoB,UAAU,EAAE;QACxB,IAAIpB,WAAW,CAACoB,UAAU,KAAK,MAAM,EAAE;UACnC,MAAM,IAAIC,SAAS,CAAC,4DAA4DrB,WAAW,CAACoB,UAAU,IAAI,CAAC;QAC/G;QACA9C,EAAE,CAAC8C,UAAU,GAAGpB,WAAW,CAACoB,UAAU;MAC1C,CAAC,MAAM;QACH9C,EAAE,CAAC8C,UAAU,GAAG,MAAM;MAC1B;MAEA,IAAI,OAAOpB,WAAW,CAACsB,aAAa,KAAK,SAAS,EAAE;QAChDhD,EAAE,CAACgD,aAAa,GAAGtB,WAAW,CAACsB,aAAa;MAChD,CAAC,MAAM;QACHhD,EAAE,CAACgD,aAAa,GAAG,KAAK;MAC5B;MAEA,IAAItB,WAAW,CAACuB,YAAY,KAAK,MAAM,EAAE;QACrCjD,EAAE,CAACiD,YAAY,GAAG,MAAM;MAC5B,CAAC,MAAM;QACHjD,EAAE,CAACiD,YAAY,GAAG,KAAK;MAC3B;MAEA,IAAI,OAAOvB,WAAW,CAACwB,KAAK,KAAK,QAAQ,EAAE;QACvClD,EAAE,CAACkD,KAAK,GAAGxB,WAAW,CAACwB,KAAK;MAChC;MAEA,IAAI,OAAOxB,WAAW,CAACyB,SAAS,KAAK,QAAQ,EAAE;QAC3CnD,EAAE,CAACmD,SAAS,GAAGzB,WAAW,CAACyB,SAAS;MACxC;MAEA,IAAI,OAAOzB,WAAW,CAAC0B,qBAAqB,KAAK,QAAQ,IAAI1B,WAAW,CAAC0B,qBAAqB,GAAG,CAAC,EAAE;QAChGpD,EAAE,CAACoD,qBAAqB,GAAG1B,WAAW,CAAC0B,qBAAqB;MAChE,CAAC,MAAM;QACHpD,EAAE,CAACoD,qBAAqB,GAAG,KAAK;MACpC;IACJ;IAEA,IAAI,CAACpD,EAAE,CAACuC,YAAY,EAAE;MAClBvC,EAAE,CAACuC,YAAY,GAAG,UAAU;IAChC;IACA,IAAI,CAACvC,EAAE,CAACyC,YAAY,EAAE;MAClBzC,EAAE,CAACyC,YAAY,GAAG,MAAM;MACxBzC,EAAE,CAACwC,IAAI,GAAG,UAAU;IACxB;IAEA,IAAIa,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7B,IAAIC,WAAW,GAAGD,aAAa,CAAC,CAAC;IACjCC,WAAW,CAACF,OAAO,CAACG,IAAI,CAAC,YAAW;MAChCxD,EAAE,CAACyD,OAAO,IAAIzD,EAAE,CAACyD,OAAO,CAACzD,EAAE,CAAC4B,aAAa,CAAC;MAC1CyB,OAAO,CAACK,UAAU,CAAC1D,EAAE,CAAC4B,aAAa,CAAC;IACxC,CAAC,CAAC,CAAC+B,KAAK,CAAC,UAASC,KAAK,EAAE;MACrBP,OAAO,CAACQ,QAAQ,CAACD,KAAK,CAAC;IAC3B,CAAC,CAAC;IAEF,IAAIE,aAAa,GAAGC,UAAU,CAAC,CAAC;IAEhC,SAAS1B,MAAMA,CAAA,EAAG;MACd,IAAI2B,OAAO,GAAG,SAAAA,CAASC,MAAM,EAAE;QAC3B,IAAI,CAACA,MAAM,EAAE;UACTC,OAAO,CAACD,MAAM,GAAG,MAAM;QAC3B;QAEA,IAAIvC,WAAW,IAAIA,WAAW,CAACyC,MAAM,EAAE;UACnCD,OAAO,CAACC,MAAM,GAAGzC,WAAW,CAACyC,MAAM;QACvC;QACAnE,EAAE,CAACoE,KAAK,CAACF,OAAO,CAAC,CAACV,IAAI,CAAC,YAAY;UAC/BD,WAAW,CAACG,UAAU,CAAC,CAAC;QAC5B,CAAC,CAAC,CAACC,KAAK,CAAC,UAAUC,KAAK,EAAE;UACtBL,WAAW,CAACM,QAAQ,CAACD,KAAK,CAAC;QAC/B,CAAC,CAAC;MACN,CAAC;MAED,IAAIS,gBAAgB,GAAG,SAAAA,CAAA,EAAW;QAC9B,IAAIC,IAAI,GAAG7D,QAAQ,CAAC8D,aAAa,CAAC,QAAQ,CAAC;QAC3C,IAAI1D,GAAG,GAAGb,EAAE,CAACwE,cAAc,CAAC;UAACP,MAAM,EAAE,MAAM;UAAEtB,WAAW,EAAE3C,EAAE,CAAC4C;QAAyB,CAAC,CAAC;QACxF0B,IAAI,CAACG,YAAY,CAAC,KAAK,EAAE5D,GAAG,CAAC;QAC7ByD,IAAI,CAACG,YAAY,CAAC,SAAS,EAAE,yEAAyE,CAAC;QACvGH,IAAI,CAACG,YAAY,CAAC,OAAO,EAAE,2BAA2B,CAAC;QACvDH,IAAI,CAACI,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3BlE,QAAQ,CAACmE,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;QAE/B,IAAIQ,eAAe,GAAG,SAAAA,CAASC,KAAK,EAAE;UAClC,IAAIA,KAAK,CAACC,MAAM,KAAKhD,MAAM,CAACiD,QAAQ,CAACD,MAAM,IAAIV,IAAI,CAACY,aAAa,KAAKH,KAAK,CAACI,MAAM,EAAE;YAChF;UACJ;UAEA,IAAIC,KAAK,GAAGC,aAAa,CAACN,KAAK,CAACO,IAAI,CAAC;UACrCC,eAAe,CAACH,KAAK,EAAE7B,WAAW,CAAC;UAEnC9C,QAAQ,CAACmE,IAAI,CAACY,WAAW,CAAClB,IAAI,CAAC;UAC/BtC,MAAM,CAACyD,mBAAmB,CAAC,SAAS,EAAEX,eAAe,CAAC;QAC1D,CAAC;QAED9C,MAAM,CAAC0D,gBAAgB,CAAC,SAAS,EAAEZ,eAAe,CAAC;MACvD,CAAC;MAED,IAAIZ,OAAO,GAAG,CAAC,CAAC;MAChB,QAAQxC,WAAW,CAACW,MAAM;QACtB,KAAK,WAAW;UACZ,IAAIjC,WAAW,CAACC,MAAM,EAAE;YACpBsF,qBAAqB,CAAC,CAAC,CAACnC,IAAI,CAAC,YAAW;cACpCrB,gBAAgB,CAAC,CAAC,CAACqB,IAAI,CAAC,UAAUoC,SAAS,EAAE;gBACzC,IAAI,CAACA,SAAS,EAAE;kBACZ5F,EAAE,CAAC4C,yBAAyB,GAAGyB,gBAAgB,CAAC,CAAC,GAAGL,OAAO,CAAC,KAAK,CAAC;gBACtE,CAAC,MAAM;kBACHT,WAAW,CAACG,UAAU,CAAC,CAAC;gBAC5B;cACJ,CAAC,CAAC,CAACC,KAAK,CAAC,UAAUC,KAAK,EAAE;gBACtBL,WAAW,CAACM,QAAQ,CAACD,KAAK,CAAC;cAC/B,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,MAAM;YACH5D,EAAE,CAAC4C,yBAAyB,GAAGyB,gBAAgB,CAAC,CAAC,GAAGL,OAAO,CAAC,KAAK,CAAC;UACtE;UACA;QACJ,KAAK,gBAAgB;UACjBA,OAAO,CAAC,IAAI,CAAC;UACb;QACJ;UACI,MAAM,0BAA0B;MACxC;IACJ;IAEA,SAAS6B,WAAWA,CAAA,EAAG;MACnB,IAAIC,QAAQ,GAAGT,aAAa,CAACrD,MAAM,CAACiD,QAAQ,CAACc,IAAI,CAAC;MAElD,IAAID,QAAQ,EAAE;QACV9D,MAAM,CAACgE,OAAO,CAACC,YAAY,CAACjE,MAAM,CAACgE,OAAO,CAACE,KAAK,EAAE,IAAI,EAAEJ,QAAQ,CAACK,MAAM,CAAC;MAC5E;MAEA,IAAIL,QAAQ,IAAIA,QAAQ,CAACM,KAAK,EAAE;QAC5B,OAAOT,qBAAqB,CAAC,CAAC,CAACnC,IAAI,CAAC,YAAW;UAC3C+B,eAAe,CAACO,QAAQ,EAAEvC,WAAW,CAAC;QAC1C,CAAC,CAAC,CAACI,KAAK,CAAC,UAAUC,KAAK,EAAE;UACtBL,WAAW,CAACM,QAAQ,CAACD,KAAK,CAAC;QAC/B,CAAC,CAAC;MACN,CAAC,MAAM,IAAIlC,WAAW,EAAE;QACpB,IAAIA,WAAW,CAAC2E,KAAK,IAAI3E,WAAW,CAAC4E,YAAY,EAAE;UAC/CC,QAAQ,CAAC7E,WAAW,CAAC2E,KAAK,EAAE3E,WAAW,CAAC4E,YAAY,EAAE5E,WAAW,CAAC8E,OAAO,CAAC;UAE1E,IAAIpG,WAAW,CAACC,MAAM,EAAE;YACpBsF,qBAAqB,CAAC,CAAC,CAACnC,IAAI,CAAC,YAAW;cACpCrB,gBAAgB,CAAC,CAAC,CAACqB,IAAI,CAAC,UAAUoC,SAAS,EAAE;gBACzC,IAAIA,SAAS,EAAE;kBACX5F,EAAE,CAACyG,aAAa,IAAIzG,EAAE,CAACyG,aAAa,CAAC,CAAC;kBACtClD,WAAW,CAACG,UAAU,CAAC,CAAC;kBACxBgD,mBAAmB,CAAC,CAAC;gBACzB,CAAC,MAAM;kBACHnD,WAAW,CAACG,UAAU,CAAC,CAAC;gBAC5B;cACJ,CAAC,CAAC,CAACC,KAAK,CAAC,UAAUC,KAAK,EAAE;gBACtBL,WAAW,CAACM,QAAQ,CAACD,KAAK,CAAC;cAC/B,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,MAAM;YACH5D,EAAE,CAAC2G,WAAW,CAAC,CAAC,CAAC,CAAC,CAACnD,IAAI,CAAC,YAAW;cAC/BxD,EAAE,CAACyG,aAAa,IAAIzG,EAAE,CAACyG,aAAa,CAAC,CAAC;cACtClD,WAAW,CAACG,UAAU,CAAC,CAAC;YAC5B,CAAC,CAAC,CAACC,KAAK,CAAC,UAASC,KAAK,EAAE;cACrB5D,EAAE,CAAC4G,WAAW,IAAI5G,EAAE,CAAC4G,WAAW,CAAC,CAAC;cAClC,IAAIlF,WAAW,CAACW,MAAM,EAAE;gBACpBA,MAAM,CAAC,CAAC;cACZ,CAAC,MAAM;gBACHkB,WAAW,CAACM,QAAQ,CAACD,KAAK,CAAC;cAC/B;YACJ,CAAC,CAAC;UACN;QACJ,CAAC,MAAM,IAAIlC,WAAW,CAACW,MAAM,EAAE;UAC3BA,MAAM,CAAC,CAAC;QACZ,CAAC,MAAM;UACHkB,WAAW,CAACG,UAAU,CAAC,CAAC;QAC5B;MACJ,CAAC,MAAM;QACHH,WAAW,CAACG,UAAU,CAAC,CAAC;MAC5B;IACJ;IAEA,SAASmD,QAAQA,CAAA,EAAG;MAChB,IAAIxD,OAAO,GAAGC,aAAa,CAAC,CAAC;MAE7B,IAAIwD,eAAe,GAAG,SAAAA,CAAA,EAAY;QAC9B,IAAIrG,QAAQ,CAACsG,UAAU,KAAK,aAAa,IAAItG,QAAQ,CAACsG,UAAU,KAAK,UAAU,EAAE;UAC7EtG,QAAQ,CAACgF,mBAAmB,CAAC,kBAAkB,EAAEqB,eAAe,CAAC;UACjEzD,OAAO,CAACK,UAAU,CAAC,CAAC;QACxB;MACJ,CAAC;MACDjD,QAAQ,CAACiF,gBAAgB,CAAC,kBAAkB,EAAEoB,eAAe,CAAC;MAE9DA,eAAe,CAAC,CAAC,CAAC,CAAC;;MAEnB,OAAOzD,OAAO,CAACA,OAAO;IAC1B;IAEAS,aAAa,CAACN,IAAI,CAAC,YAAY;MAC3BqD,QAAQ,CAAC,CAAC,CACLrD,IAAI,CAACwD,uBAAuB,CAAC,CAC7BxD,IAAI,CAACqC,WAAW,CAAC,CACjBlC,KAAK,CAAC,UAAUC,KAAK,EAAE;QACpBP,OAAO,CAACQ,QAAQ,CAACD,KAAK,CAAC;MAC3B,CAAC,CAAC;IACV,CAAC,CAAC;IACFE,aAAa,CAACH,KAAK,CAAC,UAAUC,KAAK,EAAE;MACjCP,OAAO,CAACQ,QAAQ,CAACD,KAAK,CAAC;IAC3B,CAAC,CAAC;IAEF,OAAOP,OAAO,CAACA,OAAO;EAC1B,CAAC;EAEDrD,EAAE,CAACoE,KAAK,GAAG,UAAUF,OAAO,EAAE;IAC1B,OAAOjE,OAAO,CAACmE,KAAK,CAACF,OAAO,CAAC;EACjC,CAAC;EAED,SAAS+C,kBAAkBA,CAACC,GAAG,EAAE;IAC7B;IACA,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,MAAM,GAAGpF,MAAM,CAACoF,MAAM,IAAIpF,MAAM,CAACqF,QAAQ;IAC7C,IAAID,MAAM,IAAIA,MAAM,CAACE,eAAe,IAAItF,MAAM,CAACuF,UAAU,EAAE;MACvDJ,KAAK,GAAG,IAAII,UAAU,CAACL,GAAG,CAAC;MAC3BE,MAAM,CAACE,eAAe,CAACH,KAAK,CAAC;MAC7B,OAAOA,KAAK;IAChB;;IAEA;IACAA,KAAK,GAAG,IAAIK,KAAK,CAACN,GAAG,CAAC;IACtB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,CAACvG,MAAM,EAAE6G,CAAC,EAAE,EAAE;MACnCN,KAAK,CAACM,CAAC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC,GAAG,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC;IAC9C;IACA,OAAOT,KAAK;EAChB;EAEA,SAASU,oBAAoBA,CAACX,GAAG,EAAE;IAC/B,OAAOY,oBAAoB,CAACZ,GAAG,EAAE,gEAAgE,CAAC;EACtG;EAEA,SAASY,oBAAoBA,CAACZ,GAAG,EAAEa,QAAQ,EAAC;IACxC,IAAIC,UAAU,GAAGf,kBAAkB,CAACC,GAAG,CAAC;IACxC,IAAIe,KAAK,GAAG,IAAIT,KAAK,CAACN,GAAG,CAAC;IAC1B,KAAK,IAAIvG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuG,GAAG,EAAEvG,CAAC,EAAE,EAAE;MAC1BsH,KAAK,CAACtH,CAAC,CAAC,GAAGoH,QAAQ,CAACG,UAAU,CAACF,UAAU,CAACrH,CAAC,CAAC,GAAGoH,QAAQ,CAACnH,MAAM,CAAC;IACnE;IACA,OAAOuH,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,IAAI,EAAEJ,KAAK,CAAC;EACjD;EAEA,SAASK,qBAAqBA,CAACxF,UAAU,EAAEyF,YAAY,EAAE;IACrD,IAAIzF,UAAU,KAAK,MAAM,EAAE;MACvB,MAAM,IAAIC,SAAS,CAAC,4DAA4DD,UAAU,IAAI,CAAC;IACnG;;IAEA;IACA,MAAM0F,SAAS,GAAG,IAAIjB,UAAU,CAAC7H,MAAM,CAAC+I,WAAW,CAACF,YAAY,CAAC,CAAC;IAClE,MAAMG,WAAW,GAAGC,aAAa,CAACH,SAAS,CAAC,CACvCI,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAEvB,OAAOF,WAAW;EACtB;EAEA,SAASG,oBAAoBA,CAACC,YAAY,EAAC;IACvC,IAAIC,MAAM,GAAG;MACTC,QAAQ,EAAE;QACNC,GAAG,EAAEH;MACT;IACJ,CAAC;IACD,OAAOI,IAAI,CAACC,SAAS,CAACJ,MAAM,CAAC;EACjC;EAEA/I,EAAE,CAACwE,cAAc,GAAG,UAASN,OAAO,EAAE;IAClC,IAAIgC,KAAK,GAAGkD,UAAU,CAAC,CAAC;IACxB,IAAIC,KAAK,GAAGD,UAAU,CAAC,CAAC;IAExB,IAAIzG,WAAW,GAAG1C,OAAO,CAAC0C,WAAW,CAACuB,OAAO,CAAC;IAE9C,IAAIoF,aAAa,GAAG;MAChBpD,KAAK,EAAEA,KAAK;MACZmD,KAAK,EAAEA,KAAK;MACZ1G,WAAW,EAAE4G,kBAAkB,CAAC5G,WAAW;IAC/C,CAAC;IAED,IAAIuB,OAAO,IAAIA,OAAO,CAACD,MAAM,EAAE;MAC3BqF,aAAa,CAACrF,MAAM,GAAGC,OAAO,CAACD,MAAM;IACzC;IAEA,IAAIuF,OAAO;IACX,IAAItF,OAAO,IAAIA,OAAO,CAACuF,MAAM,IAAI,UAAU,EAAE;MACzCD,OAAO,GAAGxJ,EAAE,CAAC0J,SAAS,CAACC,QAAQ,CAAC,CAAC;IACrC,CAAC,MAAM;MACHH,OAAO,GAAGxJ,EAAE,CAAC0J,SAAS,CAACE,SAAS,CAAC,CAAC;IACtC;IAEA,IAAI1G,KAAK,GAAGgB,OAAO,IAAIA,OAAO,CAAChB,KAAK,IAAIlD,EAAE,CAACkD,KAAK;IAChD,IAAI,CAACA,KAAK,EAAE;MACR;MACAA,KAAK,GAAG,QAAQ;IACpB,CAAC,MAAM,IAAIA,KAAK,CAACpC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACvC;MACAoC,KAAK,GAAG,SAAS,GAAGA,KAAK;IAC7B;IAEA,IAAI2G,GAAG,GAAGL,OAAO,GACX,aAAa,GAAGD,kBAAkB,CAACvJ,EAAE,CAAC8J,QAAQ,CAAC,GAC/C,gBAAgB,GAAGP,kBAAkB,CAAC5G,WAAW,CAAC,GAClD,SAAS,GAAG4G,kBAAkB,CAACrD,KAAK,CAAC,GACrC,iBAAiB,GAAGqD,kBAAkB,CAACvJ,EAAE,CAACuC,YAAY,CAAC,GACvD,iBAAiB,GAAGgH,kBAAkB,CAACvJ,EAAE,CAACyC,YAAY,CAAC,GACvD,SAAS,GAAG8G,kBAAkB,CAACrG,KAAK,CAAC;IAC3C,IAAIhC,QAAQ,EAAE;MACV2I,GAAG,GAAGA,GAAG,GAAG,SAAS,GAAGN,kBAAkB,CAACF,KAAK,CAAC;IACrD;IAEA,IAAInF,OAAO,IAAIA,OAAO,CAACD,MAAM,EAAE;MAC3B4F,GAAG,IAAI,UAAU,GAAGN,kBAAkB,CAACrF,OAAO,CAACD,MAAM,CAAC;IAC1D;IAEA,IAAIC,OAAO,IAAIA,OAAO,CAAC6F,MAAM,EAAE;MAC3BF,GAAG,IAAI,WAAW,GAAGN,kBAAkB,CAACrF,OAAO,CAAC6F,MAAM,CAAC;IAC3D;IAEA,IAAI7F,OAAO,IAAIA,OAAO,CAAC8F,SAAS,EAAE;MAC9BH,GAAG,IAAI,cAAc,GAAGN,kBAAkB,CAACrF,OAAO,CAAC8F,SAAS,CAAC;IACjE;IAEA,IAAI9F,OAAO,IAAIA,OAAO,CAAC+F,OAAO,EAAE;MAC5BJ,GAAG,IAAI,eAAe,GAAGN,kBAAkB,CAACrF,OAAO,CAAC+F,OAAO,CAAC;IAChE;IAEA,IAAI/F,OAAO,IAAIA,OAAO,CAACuF,MAAM,IAAIvF,OAAO,CAACuF,MAAM,IAAI,UAAU,EAAE;MAC3DI,GAAG,IAAI,aAAa,GAAGN,kBAAkB,CAACrF,OAAO,CAACuF,MAAM,CAAC;IAC7D;IAEA,IAAIvF,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;MAC3B0F,GAAG,IAAI,cAAc,GAAGN,kBAAkB,CAACrF,OAAO,CAACC,MAAM,CAAC;IAC9D;IAEA,IAAID,OAAO,IAAIA,OAAO,CAAC+E,GAAG,EAAE;MACxB,IAAIiB,eAAe,GAAGrB,oBAAoB,CAAC3E,OAAO,CAAC+E,GAAG,CAAC;MACvDY,GAAG,IAAI,UAAU,GAAGN,kBAAkB,CAACW,eAAe,CAAC;IAC3D;IAEA,IAAKhG,OAAO,IAAIA,OAAO,CAACf,SAAS,IAAKnD,EAAE,CAACmD,SAAS,EAAE;MAChD0G,GAAG,IAAI,cAAc,GAAGN,kBAAkB,CAACrF,OAAO,CAACf,SAAS,IAAInD,EAAE,CAACmD,SAAS,CAAC;IACjF;IAEA,IAAInD,EAAE,CAAC8C,UAAU,EAAE;MACf,IAAIyF,YAAY,GAAGV,oBAAoB,CAAC,EAAE,CAAC;MAC3CyB,aAAa,CAACa,gBAAgB,GAAG5B,YAAY;MAC7C,IAAI6B,aAAa,GAAG9B,qBAAqB,CAACtI,EAAE,CAAC8C,UAAU,EAAEyF,YAAY,CAAC;MACtEsB,GAAG,IAAI,kBAAkB,GAAGO,aAAa;MACzCP,GAAG,IAAI,yBAAyB,GAAG7J,EAAE,CAAC8C,UAAU;IACpD;IAEA3C,eAAe,CAACkK,GAAG,CAACf,aAAa,CAAC;IAElC,OAAOO,GAAG;EACd,CAAC;EAED7J,EAAE,CAACsK,MAAM,GAAG,UAASpG,OAAO,EAAE;IAC1B,OAAOjE,OAAO,CAACqK,MAAM,CAACpG,OAAO,CAAC;EAClC,CAAC;EAEDlE,EAAE,CAACuK,eAAe,GAAG,UAASrG,OAAO,EAAE;IAEnC,MAAMjB,YAAY,GAAGiB,OAAO,EAAEjB,YAAY,IAAIjD,EAAE,CAACiD,YAAY;IAC7D,IAAIA,YAAY,KAAK,MAAM,EAAE;MACzB,OAAOjD,EAAE,CAAC0J,SAAS,CAACY,MAAM,CAAC,CAAC;IAChC;IAEA,IAAIT,GAAG,GAAG7J,EAAE,CAAC0J,SAAS,CAACY,MAAM,CAAC,CAAC,GACzB,aAAa,GAAGf,kBAAkB,CAACvJ,EAAE,CAAC8J,QAAQ,CAAC,GAC/C,4BAA4B,GAAGP,kBAAkB,CAACtJ,OAAO,CAAC0C,WAAW,CAACuB,OAAO,EAAE,KAAK,CAAC,CAAC;IAE5F,IAAIlE,EAAE,CAACwG,OAAO,EAAE;MACZqD,GAAG,IAAI,iBAAiB,GAAGN,kBAAkB,CAACvJ,EAAE,CAACwG,OAAO,CAAC;IAC7D;IAEA,OAAOqD,GAAG;EACd,CAAC;EAED7J,EAAE,CAAC2J,QAAQ,GAAG,UAAUzF,OAAO,EAAE;IAC7B,OAAOjE,OAAO,CAAC0J,QAAQ,CAACzF,OAAO,CAAC;EACpC,CAAC;EAEDlE,EAAE,CAACwK,iBAAiB,GAAG,UAAStG,OAAO,EAAE;IACrC,IAAI,CAACA,OAAO,EAAE;MACVA,OAAO,GAAG,CAAC,CAAC;IAChB;IACAA,OAAO,CAACuF,MAAM,GAAG,UAAU;IAC3B,OAAOzJ,EAAE,CAACwE,cAAc,CAACN,OAAO,CAAC;EACrC,CAAC;EAEDlE,EAAE,CAACyK,gBAAgB,GAAG,UAASvG,OAAO,EAAE;IACpC,IAAIwG,KAAK,GAAGC,WAAW,CAAC,CAAC;IACzB,IAAId,GAAG,GAAGe,SAAS;IACnB,IAAI,OAAOF,KAAK,KAAK,WAAW,EAAE;MAC9Bb,GAAG,GAAGa,KAAK,GACT,UAAU,GACV,YAAY,GAAGnB,kBAAkB,CAACvJ,EAAE,CAAC8J,QAAQ,CAAC,GAC9C,gBAAgB,GAAGP,kBAAkB,CAACtJ,OAAO,CAAC0C,WAAW,CAACuB,OAAO,CAAC,CAAC;IACzE;IACA,OAAO2F,GAAG;EACd,CAAC;EAED7J,EAAE,CAAC6K,iBAAiB,GAAG,YAAW;IAC9B,OAAO5K,OAAO,CAAC4K,iBAAiB,CAAC,CAAC;EACtC,CAAC;EAED7K,EAAE,CAAC8K,YAAY,GAAG,UAAUC,IAAI,EAAE;IAC9B,IAAIC,MAAM,GAAGhL,EAAE,CAACiL,WAAW;IAC3B,OAAO,CAAC,CAACD,MAAM,IAAIA,MAAM,CAACE,KAAK,CAACpK,OAAO,CAACiK,IAAI,CAAC,IAAI,CAAC;EACtD,CAAC;EAED/K,EAAE,CAACmL,eAAe,GAAG,UAASJ,IAAI,EAAEK,QAAQ,EAAE;IAC1C,IAAI,CAACpL,EAAE,CAACqL,cAAc,EAAE;MACpB,OAAO,KAAK;IAChB;IAEA,IAAIL,MAAM,GAAGhL,EAAE,CAACqL,cAAc,CAACD,QAAQ,IAAIpL,EAAE,CAAC8J,QAAQ,CAAC;IACvD,OAAO,CAAC,CAACkB,MAAM,IAAIA,MAAM,CAACE,KAAK,CAACpK,OAAO,CAACiK,IAAI,CAAC,IAAI,CAAC;EACtD,CAAC;EAED/K,EAAE,CAACsL,eAAe,GAAG,YAAW;IAC5B,IAAIzB,GAAG,GAAGc,WAAW,CAAC,CAAC,GAAG,UAAU;IACpC,IAAIY,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;IAC9BD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAE5B,GAAG,EAAE,IAAI,CAAC;IAC1B0B,GAAG,CAACG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC;IAClDH,GAAG,CAACG,gBAAgB,CAAC,eAAe,EAAE,SAAS,GAAG1L,EAAE,CAACqG,KAAK,CAAC;IAE3D,IAAIhD,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7BiI,GAAG,CAACI,kBAAkB,GAAG,YAAY;MACjC,IAAIJ,GAAG,CAACxE,UAAU,IAAI,CAAC,EAAE;QACrB,IAAIwE,GAAG,CAACK,MAAM,IAAI,GAAG,EAAE;UACnB5L,EAAE,CAAC6L,OAAO,GAAG3C,IAAI,CAAC4C,KAAK,CAACP,GAAG,CAACQ,YAAY,CAAC;UACzC1I,OAAO,CAACK,UAAU,CAAC1D,EAAE,CAAC6L,OAAO,CAAC;QAClC,CAAC,MAAM;UACHxI,OAAO,CAACQ,QAAQ,CAAC,CAAC;QACtB;MACJ;IACJ,CAAC;IAED0H,GAAG,CAACS,IAAI,CAAC,CAAC;IAEV,OAAO3I,OAAO,CAACA,OAAO;EAC1B,CAAC;EAEDrD,EAAE,CAACiM,YAAY,GAAG,YAAW;IACzB,IAAIpC,GAAG,GAAG7J,EAAE,CAAC0J,SAAS,CAACwC,QAAQ,CAAC,CAAC;IACjC,IAAIX,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;IAC9BD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAE5B,GAAG,EAAE,IAAI,CAAC;IAC1B0B,GAAG,CAACG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC;IAClDH,GAAG,CAACG,gBAAgB,CAAC,eAAe,EAAE,SAAS,GAAG1L,EAAE,CAACqG,KAAK,CAAC;IAE3D,IAAIhD,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7BiI,GAAG,CAACI,kBAAkB,GAAG,YAAY;MACjC,IAAIJ,GAAG,CAACxE,UAAU,IAAI,CAAC,EAAE;QACrB,IAAIwE,GAAG,CAACK,MAAM,IAAI,GAAG,EAAE;UACnB5L,EAAE,CAACmM,QAAQ,GAAGjD,IAAI,CAAC4C,KAAK,CAACP,GAAG,CAACQ,YAAY,CAAC;UAC1C1I,OAAO,CAACK,UAAU,CAAC1D,EAAE,CAACmM,QAAQ,CAAC;QACnC,CAAC,MAAM;UACH9I,OAAO,CAACQ,QAAQ,CAAC,CAAC;QACtB;MACJ;IACJ,CAAC;IAED0H,GAAG,CAACS,IAAI,CAAC,CAAC;IAEV,OAAO3I,OAAO,CAACA,OAAO;EAC1B,CAAC;EAEDrD,EAAE,CAACoM,cAAc,GAAG,UAASC,WAAW,EAAE;IACtC,IAAI,CAACrM,EAAE,CAACsM,WAAW,IAAK,CAACtM,EAAE,CAACsG,YAAY,IAAItG,EAAE,CAACwC,IAAI,IAAI,UAAY,EAAE;MACjE,MAAM,mBAAmB;IAC7B;IAEA,IAAIxC,EAAE,CAAC0C,QAAQ,IAAI,IAAI,EAAE;MACrBvB,OAAO,CAAC,2EAA2E,CAAC;MACpF,OAAO,IAAI;IACf;IAEA,IAAIoL,SAAS,GAAGvM,EAAE,CAACsM,WAAW,CAAC,KAAK,CAAC,GAAG5E,IAAI,CAAC8E,IAAI,CAAC,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG1M,EAAE,CAAC0C,QAAQ;IAC5F,IAAI2J,WAAW,EAAE;MACb,IAAIM,KAAK,CAACN,WAAW,CAAC,EAAE;QACpB,MAAM,qBAAqB;MAC/B;MACAE,SAAS,IAAIF,WAAW;IAC5B;IACA,OAAOE,SAAS,GAAG,CAAC;EACxB,CAAC;EAEDvM,EAAE,CAAC2G,WAAW,GAAG,UAAS0F,WAAW,EAAE;IACnC,IAAIhJ,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7B,IAAI,CAACtD,EAAE,CAACsG,YAAY,EAAE;MAClBjD,OAAO,CAACQ,QAAQ,CAAC,CAAC;MAClB,OAAOR,OAAO,CAACA,OAAO;IAC1B;IAEAgJ,WAAW,GAAGA,WAAW,IAAI,CAAC;IAE9B,IAAIO,IAAI,GAAG,SAAAA,CAAA,EAAW;MAClB,IAAItG,YAAY,GAAG,KAAK;MACxB,IAAI+F,WAAW,IAAI,CAAC,CAAC,EAAE;QACnB/F,YAAY,GAAG,IAAI;QACnBnF,OAAO,CAAC,6CAA6C,CAAC;MAC1D,CAAC,MAAM,IAAI,CAACnB,EAAE,CAACsM,WAAW,IAAItM,EAAE,CAACoM,cAAc,CAACC,WAAW,CAAC,EAAE;QAC1D/F,YAAY,GAAG,IAAI;QACnBnF,OAAO,CAAC,4CAA4C,CAAC;MACzD;MAEA,IAAI,CAACmF,YAAY,EAAE;QACfjD,OAAO,CAACK,UAAU,CAAC,KAAK,CAAC;MAC7B,CAAC,MAAM;QACH,IAAImJ,MAAM,GAAG,2BAA2B,GAAG,gBAAgB,GAAG7M,EAAE,CAACsG,YAAY;QAC7E,IAAIuD,GAAG,GAAG7J,EAAE,CAAC0J,SAAS,CAACrD,KAAK,CAAC,CAAC;QAE9BnG,YAAY,CAAC4M,IAAI,CAACzJ,OAAO,CAAC;QAE1B,IAAInD,YAAY,CAACU,MAAM,IAAI,CAAC,EAAE;UAC1B,IAAI2K,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;UAC9BD,GAAG,CAACE,IAAI,CAAC,MAAM,EAAE5B,GAAG,EAAE,IAAI,CAAC;UAC3B0B,GAAG,CAACG,gBAAgB,CAAC,cAAc,EAAE,mCAAmC,CAAC;UACzEH,GAAG,CAACwB,eAAe,GAAG,IAAI;UAE1BF,MAAM,IAAI,aAAa,GAAGtD,kBAAkB,CAACvJ,EAAE,CAAC8J,QAAQ,CAAC;UAEzD,IAAIkD,SAAS,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;UAEpCnB,GAAG,CAACI,kBAAkB,GAAG,YAAY;YACjC,IAAIJ,GAAG,CAACxE,UAAU,IAAI,CAAC,EAAE;cACrB,IAAIwE,GAAG,CAACK,MAAM,IAAI,GAAG,EAAE;gBACnBzK,OAAO,CAAC,4BAA4B,CAAC;gBAErC6L,SAAS,GAAG,CAACA,SAAS,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,IAAI,CAAC;gBAElD,IAAIO,aAAa,GAAG/D,IAAI,CAAC4C,KAAK,CAACP,GAAG,CAACQ,YAAY,CAAC;gBAEhDxF,QAAQ,CAAC0G,aAAa,CAAC,cAAc,CAAC,EAAEA,aAAa,CAAC,eAAe,CAAC,EAAEA,aAAa,CAAC,UAAU,CAAC,EAAED,SAAS,CAAC;gBAE7GhN,EAAE,CAACkN,oBAAoB,IAAIlN,EAAE,CAACkN,oBAAoB,CAAC,CAAC;gBACpD,KAAK,IAAIC,CAAC,GAAGjN,YAAY,CAACkN,GAAG,CAAC,CAAC,EAAED,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAGjN,YAAY,CAACkN,GAAG,CAAC,CAAC,EAAE;kBAChED,CAAC,CAACzJ,UAAU,CAAC,IAAI,CAAC;gBACtB;cACJ,CAAC,MAAM;gBACHnC,OAAO,CAAC,oCAAoC,CAAC;gBAE7C,IAAIgK,GAAG,CAACK,MAAM,IAAI,GAAG,EAAE;kBACnB5L,EAAE,CAACqN,UAAU,CAAC,CAAC;gBACnB;gBAEArN,EAAE,CAACsN,kBAAkB,IAAItN,EAAE,CAACsN,kBAAkB,CAAC,CAAC;gBAChD,KAAK,IAAIH,CAAC,GAAGjN,YAAY,CAACkN,GAAG,CAAC,CAAC,EAAED,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAGjN,YAAY,CAACkN,GAAG,CAAC,CAAC,EAAE;kBAChED,CAAC,CAACtJ,QAAQ,CAAC,IAAI,CAAC;gBACpB;cACJ;YACJ;UACJ,CAAC;UAED0H,GAAG,CAACS,IAAI,CAACa,MAAM,CAAC;QACpB;MACJ;IACJ,CAAC;IAED,IAAIzM,WAAW,CAACC,MAAM,EAAE;MACpB,IAAIkN,aAAa,GAAGpL,gBAAgB,CAAC,CAAC;MACtCoL,aAAa,CAAC/J,IAAI,CAAC,YAAW;QAC1BoJ,IAAI,CAAC,CAAC;MACV,CAAC,CAAC,CAACjJ,KAAK,CAAC,UAASC,KAAK,EAAE;QACrBP,OAAO,CAACQ,QAAQ,CAACD,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,MAAM;MACHgJ,IAAI,CAAC,CAAC;IACV;IAEA,OAAOvJ,OAAO,CAACA,OAAO;EAC1B,CAAC;EAEDrD,EAAE,CAACqN,UAAU,GAAG,YAAW;IACvB,IAAIrN,EAAE,CAACqG,KAAK,EAAE;MACVE,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC1BvG,EAAE,CAACwN,YAAY,IAAIxN,EAAE,CAACwN,YAAY,CAAC,CAAC;MACpC,IAAIxN,EAAE,CAACsC,aAAa,EAAE;QAClBtC,EAAE,CAACoE,KAAK,CAAC,CAAC;MACd;IACJ;EACJ,CAAC;EAED,SAASuG,WAAWA,CAAA,EAAG;IACnB,IAAI,OAAO3K,EAAE,CAACyN,aAAa,KAAK,WAAW,EAAE;MACzC,IAAIzN,EAAE,CAACyN,aAAa,CAACC,MAAM,CAAC1N,EAAE,CAACyN,aAAa,CAAC7M,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;QAC7D,OAAOZ,EAAE,CAACyN,aAAa,GAAG,SAAS,GAAGlE,kBAAkB,CAACvJ,EAAE,CAAC0K,KAAK,CAAC;MACtE,CAAC,MAAM;QACH,OAAO1K,EAAE,CAACyN,aAAa,GAAG,UAAU,GAAGlE,kBAAkB,CAACvJ,EAAE,CAAC0K,KAAK,CAAC;MACvE;IACJ,CAAC,MAAM;MACH,OAAOE,SAAS;IACpB;EACJ;EAEA,SAAS+C,SAASA,CAAA,EAAG;IACjB,IAAI,CAAC3L,MAAM,CAACiD,QAAQ,CAACD,MAAM,EAAE;MACzB,OAAOhD,MAAM,CAACiD,QAAQ,CAAC2I,QAAQ,GAAG,IAAI,GAAG5L,MAAM,CAACiD,QAAQ,CAAC4I,QAAQ,IAAI7L,MAAM,CAACiD,QAAQ,CAAC6I,IAAI,GAAG,GAAG,GAAG9L,MAAM,CAACiD,QAAQ,CAAC6I,IAAI,GAAE,EAAE,CAAC;IAC/H,CAAC,MAAM;MACH,OAAO9L,MAAM,CAACiD,QAAQ,CAACD,MAAM;IACjC;EACJ;EAEA,SAASO,eAAeA,CAACH,KAAK,EAAE/B,OAAO,EAAE;IACrC,IAAI0K,IAAI,GAAG3I,KAAK,CAAC2I,IAAI;IACrB,IAAInK,KAAK,GAAGwB,KAAK,CAACxB,KAAK;IACvB,IAAIK,MAAM,GAAGmB,KAAK,CAACnB,MAAM;IAEzB,IAAI+I,SAAS,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAEpC,IAAItH,KAAK,CAAC,kBAAkB,CAAC,EAAE;MAC3BpF,EAAE,CAACgO,cAAc,IAAIhO,EAAE,CAACgO,cAAc,CAAC5I,KAAK,CAAC,kBAAkB,CAAC,CAAC;IACrE;IAEA,IAAIxB,KAAK,EAAE;MACP,IAAIK,MAAM,IAAI,MAAM,EAAE;QAClB,IAAIgK,SAAS,GAAG;UAAErK,KAAK,EAAEA,KAAK;UAAEsK,iBAAiB,EAAE9I,KAAK,CAAC8I;QAAkB,CAAC;QAC5ElO,EAAE,CAAC4G,WAAW,IAAI5G,EAAE,CAAC4G,WAAW,CAACqH,SAAS,CAAC;QAC3C5K,OAAO,IAAIA,OAAO,CAACQ,QAAQ,CAACoK,SAAS,CAAC;MAC1C,CAAC,MAAM;QACH5K,OAAO,IAAIA,OAAO,CAACK,UAAU,CAAC,CAAC;MACnC;MACA;IACJ,CAAC,MAAM,IAAK1D,EAAE,CAACwC,IAAI,IAAI,UAAU,KAAM4C,KAAK,CAAC+I,YAAY,IAAI/I,KAAK,CAAC4D,QAAQ,CAAC,EAAE;MAC1EoF,WAAW,CAAChJ,KAAK,CAAC+I,YAAY,EAAE,IAAI,EAAE/I,KAAK,CAAC4D,QAAQ,EAAE,IAAI,CAAC;IAC/D;IAEA,IAAKhJ,EAAE,CAACwC,IAAI,IAAI,UAAU,IAAKuL,IAAI,EAAE;MACjC,IAAIlB,MAAM,GAAG,OAAO,GAAGkB,IAAI,GAAG,gCAAgC;MAC9D,IAAIlE,GAAG,GAAG7J,EAAE,CAAC0J,SAAS,CAACrD,KAAK,CAAC,CAAC;MAE9B,IAAIkF,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;MAC9BD,GAAG,CAACE,IAAI,CAAC,MAAM,EAAE5B,GAAG,EAAE,IAAI,CAAC;MAC3B0B,GAAG,CAACG,gBAAgB,CAAC,cAAc,EAAE,mCAAmC,CAAC;MAEzEmB,MAAM,IAAI,aAAa,GAAGtD,kBAAkB,CAACvJ,EAAE,CAAC8J,QAAQ,CAAC;MACzD+C,MAAM,IAAI,gBAAgB,GAAGzH,KAAK,CAACzC,WAAW;MAE9C,IAAIyC,KAAK,CAAC+E,gBAAgB,EAAE;QACxB0C,MAAM,IAAI,iBAAiB,GAAGzH,KAAK,CAAC+E,gBAAgB;MACxD;MAEAoB,GAAG,CAACwB,eAAe,GAAG,IAAI;MAE1BxB,GAAG,CAACI,kBAAkB,GAAG,YAAW;QAChC,IAAIJ,GAAG,CAACxE,UAAU,IAAI,CAAC,EAAE;UACrB,IAAIwE,GAAG,CAACK,MAAM,IAAI,GAAG,EAAE;YAEnB,IAAIqB,aAAa,GAAG/D,IAAI,CAAC4C,KAAK,CAACP,GAAG,CAACQ,YAAY,CAAC;YAChDqC,WAAW,CAACnB,aAAa,CAAC,cAAc,CAAC,EAAEA,aAAa,CAAC,eAAe,CAAC,EAAEA,aAAa,CAAC,UAAU,CAAC,EAAEjN,EAAE,CAACwC,IAAI,KAAK,UAAU,CAAC;YAC7HkE,mBAAmB,CAAC,CAAC;UACzB,CAAC,MAAM;YACH1G,EAAE,CAAC4G,WAAW,IAAI5G,EAAE,CAAC4G,WAAW,CAAC,CAAC;YAClCvD,OAAO,IAAIA,OAAO,CAACQ,QAAQ,CAAC,CAAC;UACjC;QACJ;MACJ,CAAC;MAED0H,GAAG,CAACS,IAAI,CAACa,MAAM,CAAC;IACpB;IAEA,SAASuB,WAAWA,CAACC,WAAW,EAAE/H,YAAY,EAAEE,OAAO,EAAE8H,cAAc,EAAE;MACrEtB,SAAS,GAAG,CAACA,SAAS,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,IAAI,CAAC;MAElDnG,QAAQ,CAAC8H,WAAW,EAAE/H,YAAY,EAAEE,OAAO,EAAEwG,SAAS,CAAC;MAEvD,IAAI9L,QAAQ,IAAKlB,EAAE,CAACuO,aAAa,IAAIvO,EAAE,CAACuO,aAAa,CAAClF,KAAK,IAAIjE,KAAK,CAACoJ,WAAY,EAAE;QAC/ErN,OAAO,CAAC,0CAA0C,CAAC;QACnDnB,EAAE,CAACqN,UAAU,CAAC,CAAC;QACfhK,OAAO,IAAIA,OAAO,CAACQ,QAAQ,CAAC,CAAC;MACjC,CAAC,MAAM;QACH,IAAIyK,cAAc,EAAE;UAChBtO,EAAE,CAACyG,aAAa,IAAIzG,EAAE,CAACyG,aAAa,CAAC,CAAC;UACtCpD,OAAO,IAAIA,OAAO,CAACK,UAAU,CAAC,CAAC;QACnC;MACJ;IACJ;EAEJ;EAEA,SAASK,UAAUA,CAAC8F,GAAG,EAAE;IACrB,IAAIxG,OAAO,GAAGC,aAAa,CAAC,CAAC;IAC7B,IAAImL,SAAS;IAEb,IAAI,CAAC1O,MAAM,EAAE;MACT0O,SAAS,GAAG,eAAe;IAC/B,CAAC,MAAM,IAAI,OAAO1O,MAAM,KAAK,QAAQ,EAAE;MACnC0O,SAAS,GAAG1O,MAAM;IACtB;IAEA,SAAS2O,iBAAiBA,CAACC,iBAAiB,EAAE;MAC1C,IAAI,CAAEA,iBAAiB,EAAE;QACrB3O,EAAE,CAAC0J,SAAS,GAAG;UACXE,SAAS,EAAE,SAAAA,CAAA,EAAW;YAClB,OAAOe,WAAW,CAAC,CAAC,GAAG,+BAA+B;UAC1D,CAAC;UACDtE,KAAK,EAAE,SAAAA,CAAA,EAAW;YACd,OAAOsE,WAAW,CAAC,CAAC,GAAG,gCAAgC;UAC3D,CAAC;UACDL,MAAM,EAAE,SAAAA,CAAA,EAAW;YACf,OAAOK,WAAW,CAAC,CAAC,GAAG,iCAAiC;UAC5D,CAAC;UACDiE,kBAAkB,EAAE,SAAAA,CAAA,EAAW;YAC3B,IAAI/N,GAAG,GAAG8J,WAAW,CAAC,CAAC,GAAG,mDAAmD;YAC7E,IAAI3K,EAAE,CAACe,aAAa,EAAE;cAClBF,GAAG,GAAGA,GAAG,GAAG,WAAW,GAAGb,EAAE,CAACe,aAAa;YAC9C;YACA,OAAOF,GAAG;UACd,CAAC;UACDgO,uBAAuB,EAAE,SAAAA,CAAA,EAAW;YAChC,IAAIhO,GAAG,GAAG8J,WAAW,CAAC,CAAC,GAAG,gDAAgD;YAC1E,IAAI3K,EAAE,CAACe,aAAa,EAAE;cAClBF,GAAG,GAAGA,GAAG,GAAG,WAAW,GAAGb,EAAE,CAACe,aAAa;YAC9C;YACA,OAAOF,GAAG;UACd,CAAC;UACD8I,QAAQ,EAAE,SAAAA,CAAA,EAAW;YACjB,OAAOgB,WAAW,CAAC,CAAC,GAAG,wCAAwC;UACnE,CAAC;UACDuB,QAAQ,EAAE,SAAAA,CAAA,EAAW;YACjB,OAAOvB,WAAW,CAAC,CAAC,GAAG,mCAAmC;UAC9D;QACJ,CAAC;MACL,CAAC,MAAM;QACH3K,EAAE,CAAC0J,SAAS,GAAG;UACXE,SAAS,EAAE,SAAAA,CAAA,EAAW;YAClB,OAAO+E,iBAAiB,CAACG,sBAAsB;UACnD,CAAC;UACDzI,KAAK,EAAE,SAAAA,CAAA,EAAW;YACd,OAAOsI,iBAAiB,CAACI,cAAc;UAC3C,CAAC;UACDzE,MAAM,EAAE,SAAAA,CAAA,EAAW;YACf,IAAI,CAACqE,iBAAiB,CAACK,oBAAoB,EAAE;cACzC,MAAM,kCAAkC;YAC5C;YACA,OAAOL,iBAAiB,CAACK,oBAAoB;UACjD,CAAC;UACDJ,kBAAkB,EAAE,SAAAA,CAAA,EAAW;YAC3B,IAAI,CAACD,iBAAiB,CAACM,oBAAoB,EAAE;cACzC,MAAM,kCAAkC;YAC5C;YACA,OAAON,iBAAiB,CAACM,oBAAoB;UACjD,CAAC;UACDtF,QAAQ,EAAE,SAAAA,CAAA,EAAW;YACjB,MAAM,yEAAyE;UACnF,CAAC;UACDuC,QAAQ,EAAE,SAAAA,CAAA,EAAW;YACjB,IAAI,CAACyC,iBAAiB,CAACO,iBAAiB,EAAE;cACtC,MAAM,kCAAkC;YAC5C;YACA,OAAOP,iBAAiB,CAACO,iBAAiB;UAC9C;QACJ,CAAC;MACL;IACJ;IAEA,IAAIT,SAAS,EAAE;MACX,IAAIlD,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;MAC9BD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAEgD,SAAS,EAAE,IAAI,CAAC;MAChClD,GAAG,CAACG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC;MAElDH,GAAG,CAACI,kBAAkB,GAAG,YAAY;QACjC,IAAIJ,GAAG,CAACxE,UAAU,IAAI,CAAC,EAAE;UACrB,IAAIwE,GAAG,CAACK,MAAM,IAAI,GAAG,IAAIuD,UAAU,CAAC5D,GAAG,CAAC,EAAE;YACtC,IAAIxL,MAAM,GAAGmJ,IAAI,CAAC4C,KAAK,CAACP,GAAG,CAACQ,YAAY,CAAC;YAEzC/L,EAAE,CAACyN,aAAa,GAAG1N,MAAM,CAAC,iBAAiB,CAAC;YAC5CC,EAAE,CAAC0K,KAAK,GAAG3K,MAAM,CAAC,OAAO,CAAC;YAC1BC,EAAE,CAAC8J,QAAQ,GAAG/J,MAAM,CAAC,UAAU,CAAC;YAChC2O,iBAAiB,CAAC,IAAI,CAAC;YACvBrL,OAAO,CAACK,UAAU,CAAC,CAAC;UACxB,CAAC,MAAM;YACHL,OAAO,CAACQ,QAAQ,CAAC,CAAC;UACtB;QACJ;MACJ,CAAC;MAED0H,GAAG,CAACS,IAAI,CAAC,CAAC;IACd,CAAC,MAAM;MACH,IAAI,CAACjM,MAAM,CAAC+J,QAAQ,EAAE;QAClB,MAAM,kBAAkB;MAC5B;MAEA9J,EAAE,CAAC8J,QAAQ,GAAG/J,MAAM,CAAC+J,QAAQ;MAE7B,IAAIsF,YAAY,GAAGrP,MAAM,CAAC,cAAc,CAAC;MACzC,IAAI,CAACqP,YAAY,EAAE;QACf,IAAI,CAACrP,MAAM,CAAC,KAAK,CAAC,EAAE;UAChB,IAAIS,OAAO,GAAGC,QAAQ,CAACC,oBAAoB,CAAC,QAAQ,CAAC;UACrD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;YACrC,IAAIH,OAAO,CAACG,CAAC,CAAC,CAACE,GAAG,CAACwO,KAAK,CAAC,gBAAgB,CAAC,EAAE;cACxCtP,MAAM,CAAC8J,GAAG,GAAGrJ,OAAO,CAACG,CAAC,CAAC,CAACE,GAAG,CAACyO,MAAM,CAAC,CAAC,EAAE9O,OAAO,CAACG,CAAC,CAAC,CAACE,GAAG,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;cAChF;YACJ;UACJ;QACJ;QACA,IAAI,CAACf,MAAM,CAAC2K,KAAK,EAAE;UACf,MAAM,eAAe;QACzB;QAEA1K,EAAE,CAACyN,aAAa,GAAG1N,MAAM,CAAC8J,GAAG;QAC7B7J,EAAE,CAAC0K,KAAK,GAAG3K,MAAM,CAAC2K,KAAK;QACvBgE,iBAAiB,CAAC,IAAI,CAAC;QACvBrL,OAAO,CAACK,UAAU,CAAC,CAAC;MACxB,CAAC,MAAM;QACH,IAAI,OAAO0L,YAAY,KAAK,QAAQ,EAAE;UAClC,IAAIG,qBAAqB;UACzB,IAAIH,YAAY,CAAC1B,MAAM,CAAC0B,YAAY,CAACxO,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;YACrD2O,qBAAqB,GAAGH,YAAY,GAAG,kCAAkC;UAC7E,CAAC,MAAM;YACHG,qBAAqB,GAAGH,YAAY,GAAG,mCAAmC;UAC9E;UACA,IAAI7D,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;UAC9BD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAE8D,qBAAqB,EAAE,IAAI,CAAC;UAC5ChE,GAAG,CAACG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC;UAElDH,GAAG,CAACI,kBAAkB,GAAG,YAAY;YACjC,IAAIJ,GAAG,CAACxE,UAAU,IAAI,CAAC,EAAE;cACrB,IAAIwE,GAAG,CAACK,MAAM,IAAI,GAAG,IAAIuD,UAAU,CAAC5D,GAAG,CAAC,EAAE;gBACtC,IAAIiE,kBAAkB,GAAGtG,IAAI,CAAC4C,KAAK,CAACP,GAAG,CAACQ,YAAY,CAAC;gBACrD2C,iBAAiB,CAACc,kBAAkB,CAAC;gBACrCnM,OAAO,CAACK,UAAU,CAAC,CAAC;cACxB,CAAC,MAAM;gBACHL,OAAO,CAACQ,QAAQ,CAAC,CAAC;cACtB;YACJ;UACJ,CAAC;UAED0H,GAAG,CAACS,IAAI,CAAC,CAAC;QACd,CAAC,MAAM;UACH0C,iBAAiB,CAACU,YAAY,CAAC;UAC/B/L,OAAO,CAACK,UAAU,CAAC,CAAC;QACxB;MACJ;IACJ;IAEA,OAAOL,OAAO,CAACA,OAAO;EAC1B;EAEA,SAAS8L,UAAUA,CAACM,GAAG,EAAE;IACrB,OAAOA,GAAG,CAAC7D,MAAM,IAAI,CAAC,IAAI6D,GAAG,CAAC1D,YAAY,IAAI0D,GAAG,CAACC,WAAW,CAACC,UAAU,CAAC,OAAO,CAAC;EACrF;EAEA,SAASpJ,QAAQA,CAACF,KAAK,EAAEC,YAAY,EAAEE,OAAO,EAAEwG,SAAS,EAAE;IACvD,IAAIhN,EAAE,CAAC4P,kBAAkB,EAAE;MACvBC,YAAY,CAAC7P,EAAE,CAAC4P,kBAAkB,CAAC;MACnC5P,EAAE,CAAC4P,kBAAkB,GAAG,IAAI;IAChC;IAEA,IAAItJ,YAAY,EAAE;MACdtG,EAAE,CAACsG,YAAY,GAAGA,YAAY;MAC9BtG,EAAE,CAAC8P,kBAAkB,GAAGnQ,SAAS,CAAC2G,YAAY,CAAC;IACnD,CAAC,MAAM;MACH,OAAOtG,EAAE,CAACsG,YAAY;MACtB,OAAOtG,EAAE,CAAC8P,kBAAkB;IAChC;IAEA,IAAItJ,OAAO,EAAE;MACTxG,EAAE,CAACwG,OAAO,GAAGA,OAAO;MACpBxG,EAAE,CAACuO,aAAa,GAAG5O,SAAS,CAAC6G,OAAO,CAAC;IACzC,CAAC,MAAM;MACH,OAAOxG,EAAE,CAACwG,OAAO;MACjB,OAAOxG,EAAE,CAACuO,aAAa;IAC3B;IAEA,IAAIlI,KAAK,EAAE;MACPrG,EAAE,CAACqG,KAAK,GAAGA,KAAK;MAChBrG,EAAE,CAACsM,WAAW,GAAG3M,SAAS,CAAC0G,KAAK,CAAC;MACjCrG,EAAE,CAAC+P,SAAS,GAAG/P,EAAE,CAACsM,WAAW,CAAC0D,aAAa;MAC3ChQ,EAAE,CAAC4B,aAAa,GAAG,IAAI;MACvB5B,EAAE,CAACiQ,OAAO,GAAGjQ,EAAE,CAACsM,WAAW,CAAC4D,GAAG;MAC/BlQ,EAAE,CAACiL,WAAW,GAAGjL,EAAE,CAACsM,WAAW,CAAC6D,YAAY;MAC5CnQ,EAAE,CAACqL,cAAc,GAAGrL,EAAE,CAACsM,WAAW,CAAC8D,eAAe;MAElD,IAAIpD,SAAS,EAAE;QACXhN,EAAE,CAAC0C,QAAQ,GAAGgF,IAAI,CAACC,KAAK,CAACqF,SAAS,GAAG,IAAI,CAAC,GAAGhN,EAAE,CAACsM,WAAW,CAAC+D,GAAG;MACnE;MAEA,IAAIrQ,EAAE,CAAC0C,QAAQ,IAAI,IAAI,EAAE;QACrBvB,OAAO,CAAC,qEAAqE,GAAGnB,EAAE,CAAC0C,QAAQ,GAAG,UAAU,CAAC;QAEzG,IAAI1C,EAAE,CAACsQ,cAAc,EAAE;UACnB,IAAI/D,SAAS,GAAG,CAACvM,EAAE,CAACsM,WAAW,CAAC,KAAK,CAAC,GAAI,IAAIG,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAK,GAAG1M,EAAE,CAAC0C,QAAQ,IAAI,IAAI;UAC5FvB,OAAO,CAAC,8BAA8B,GAAGuG,IAAI,CAAC6I,KAAK,CAAChE,SAAS,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;UAC7E,IAAIA,SAAS,IAAI,CAAC,EAAE;YAChBvM,EAAE,CAACsQ,cAAc,CAAC,CAAC;UACvB,CAAC,MAAM;YACHtQ,EAAE,CAAC4P,kBAAkB,GAAGY,UAAU,CAACxQ,EAAE,CAACsQ,cAAc,EAAE/D,SAAS,CAAC;UACpE;QACJ;MACJ;IACJ,CAAC,MAAM;MACH,OAAOvM,EAAE,CAACqG,KAAK;MACf,OAAOrG,EAAE,CAACsM,WAAW;MACrB,OAAOtM,EAAE,CAACiQ,OAAO;MACjB,OAAOjQ,EAAE,CAACiL,WAAW;MACrB,OAAOjL,EAAE,CAACqL,cAAc;MAExBrL,EAAE,CAAC4B,aAAa,GAAG,KAAK;IAC5B;EACJ;EAEA,SAASwH,UAAUA,CAAA,EAAG;IAClB,IAAIqH,SAAS,GAAG,kBAAkB;IAClC,IAAIC,CAAC,GAAG5I,oBAAoB,CAAC,EAAE,EAAE2I,SAAS,CAAC,CAACxP,KAAK,CAAC,EAAE,CAAC;IACrDyP,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG;IACXA,CAAC,CAAC,EAAE,CAAC,GAAGD,SAAS,CAACnB,MAAM,CAAEoB,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,GAAI,GAAG,EAAE,CAAC,CAAC;IAChDA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG;IAClC,IAAIC,IAAI,GAAGD,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;IACrB,OAAOD,IAAI;EACf;EAEA,SAAStL,aAAaA,CAACwE,GAAG,EAAE;IACxB,IAAIzE,KAAK,GAAGyL,gBAAgB,CAAChH,GAAG,CAAC;IACjC,IAAI,CAACzE,KAAK,EAAE;MACR;IACJ;IAEA,IAAI0L,UAAU,GAAG3Q,eAAe,CAAC4Q,GAAG,CAAC3L,KAAK,CAACc,KAAK,CAAC;IAEjD,IAAI4K,UAAU,EAAE;MACZ1L,KAAK,CAACgB,KAAK,GAAG,IAAI;MAClBhB,KAAK,CAACzC,WAAW,GAAGmO,UAAU,CAACnO,WAAW;MAC1CyC,KAAK,CAACoJ,WAAW,GAAGsC,UAAU,CAACzH,KAAK;MACpCjE,KAAK,CAACnB,MAAM,GAAG6M,UAAU,CAAC7M,MAAM;MAChCmB,KAAK,CAAC+E,gBAAgB,GAAG2G,UAAU,CAAC3G,gBAAgB;IACxD;IAEA,OAAO/E,KAAK;EAChB;EAEA,SAASyL,gBAAgBA,CAAChH,GAAG,EAAE;IAC3B,IAAImH,eAAe;IACnB,QAAQhR,EAAE,CAACwC,IAAI;MACX,KAAK,UAAU;QACXwO,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,kBAAkB,EAAE,KAAK,CAAC;QAC/E;MACJ,KAAK,UAAU;QACXA,eAAe,GAAG,CAAC,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,kBAAkB,EAAE,KAAK,CAAC;QAC/H;MACJ,KAAK,QAAQ;QACTA,eAAe,GAAG,CAAC,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,kBAAkB,EAAE,KAAK,CAAC;QACvI;IACR;IAEAA,eAAe,CAAClE,IAAI,CAAC,OAAO,CAAC;IAC7BkE,eAAe,CAAClE,IAAI,CAAC,mBAAmB,CAAC;IACzCkE,eAAe,CAAClE,IAAI,CAAC,WAAW,CAAC;IAEjC,IAAImE,UAAU,GAAGpH,GAAG,CAAC/I,OAAO,CAAC,GAAG,CAAC;IACjC,IAAIoQ,aAAa,GAAGrH,GAAG,CAAC/I,OAAO,CAAC,GAAG,CAAC;IAEpC,IAAIqF,MAAM;IACV,IAAIgL,MAAM;IAEV,IAAInR,EAAE,CAACuC,YAAY,KAAK,OAAO,IAAI0O,UAAU,KAAK,CAAC,CAAC,EAAE;MAClD9K,MAAM,GAAG0D,GAAG,CAAC7I,SAAS,CAAC,CAAC,EAAEiQ,UAAU,CAAC;MACrCE,MAAM,GAAGC,mBAAmB,CAACvH,GAAG,CAAC7I,SAAS,CAACiQ,UAAU,GAAG,CAAC,EAAEC,aAAa,KAAK,CAAC,CAAC,GAAGA,aAAa,GAAGrH,GAAG,CAACjJ,MAAM,CAAC,EAAEoQ,eAAe,CAAC;MAC/H,IAAIG,MAAM,CAACE,YAAY,KAAK,EAAE,EAAE;QAC5BlL,MAAM,IAAI,GAAG,GAAGgL,MAAM,CAACE,YAAY;MACvC;MACA,IAAIH,aAAa,KAAK,CAAC,CAAC,EAAE;QACtB/K,MAAM,IAAI0D,GAAG,CAAC7I,SAAS,CAACkQ,aAAa,CAAC;MAC1C;IACJ,CAAC,MAAM,IAAIlR,EAAE,CAACuC,YAAY,KAAK,UAAU,IAAI2O,aAAa,KAAK,CAAC,CAAC,EAAE;MAC/D/K,MAAM,GAAG0D,GAAG,CAAC7I,SAAS,CAAC,CAAC,EAAEkQ,aAAa,CAAC;MACxCC,MAAM,GAAGC,mBAAmB,CAACvH,GAAG,CAAC7I,SAAS,CAACkQ,aAAa,GAAG,CAAC,CAAC,EAAEF,eAAe,CAAC;MAC/E,IAAIG,MAAM,CAACE,YAAY,KAAK,EAAE,EAAE;QAC5BlL,MAAM,IAAI,GAAG,GAAGgL,MAAM,CAACE,YAAY;MACvC;IACJ;IAEA,IAAIF,MAAM,IAAIA,MAAM,CAACG,WAAW,EAAE;MAC9B,IAAItR,EAAE,CAACwC,IAAI,KAAK,UAAU,IAAIxC,EAAE,CAACwC,IAAI,KAAK,QAAQ,EAAE;QAChD,IAAI,CAAC2O,MAAM,CAACG,WAAW,CAACvD,IAAI,IAAIoD,MAAM,CAACG,WAAW,CAAC1N,KAAK,KAAKuN,MAAM,CAACG,WAAW,CAACpL,KAAK,EAAE;UACnFiL,MAAM,CAACG,WAAW,CAACnL,MAAM,GAAGA,MAAM;UAClC,OAAOgL,MAAM,CAACG,WAAW;QAC7B;MACJ,CAAC,MAAM,IAAItR,EAAE,CAACwC,IAAI,KAAK,UAAU,EAAE;QAC/B,IAAI,CAAC2O,MAAM,CAACG,WAAW,CAACnD,YAAY,IAAIgD,MAAM,CAACG,WAAW,CAAC1N,KAAK,KAAKuN,MAAM,CAACG,WAAW,CAACpL,KAAK,EAAE;UAC3FiL,MAAM,CAACG,WAAW,CAACnL,MAAM,GAAGA,MAAM;UAClC,OAAOgL,MAAM,CAACG,WAAW;QAC7B;MACJ;IACJ;EACJ;EAEA,SAASF,mBAAmBA,CAACC,YAAY,EAAEL,eAAe,EAAE;IACxD,IAAI7D,CAAC,GAAGkE,YAAY,CAACpQ,KAAK,CAAC,GAAG,CAAC;IAC/B,IAAIsQ,MAAM,GAAG;MACTF,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,CAAC;IAClB,CAAC;IACD,KAAK,IAAI3Q,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwM,CAAC,CAACvM,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/B,IAAIM,KAAK,GAAGkM,CAAC,CAACxM,CAAC,CAAC,CAACG,OAAO,CAAC,GAAG,CAAC;MAC7B,IAAI0Q,GAAG,GAAGrE,CAAC,CAACxM,CAAC,CAAC,CAAC8Q,KAAK,CAAC,CAAC,EAAExQ,KAAK,CAAC;MAC9B,IAAI+P,eAAe,CAAClQ,OAAO,CAAC0Q,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACrCD,MAAM,CAACD,WAAW,CAACE,GAAG,CAAC,GAAGrE,CAAC,CAACxM,CAAC,CAAC,CAAC8Q,KAAK,CAACxQ,KAAK,GAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACH,IAAIsQ,MAAM,CAACF,YAAY,KAAK,EAAE,EAAE;UAC5BE,MAAM,CAACF,YAAY,IAAI,GAAG;QAC9B;QACAE,MAAM,CAACF,YAAY,IAAIlE,CAAC,CAACxM,CAAC,CAAC;MAC/B;IACJ;IACA,OAAO4Q,MAAM;EACjB;EAEA,SAASjO,aAAaA,CAAA,EAAG;IACrB;IACA;IACA,IAAI6J,CAAC,GAAG;MACJzJ,UAAU,EAAE,SAAAA,CAAS6N,MAAM,EAAE;QACzBpE,CAAC,CAACuE,OAAO,CAACH,MAAM,CAAC;MACrB,CAAC;MAED1N,QAAQ,EAAE,SAAAA,CAAS0N,MAAM,EAAE;QACvBpE,CAAC,CAACwE,MAAM,CAACJ,MAAM,CAAC;MACpB;IACJ,CAAC;IACDpE,CAAC,CAAC9J,OAAO,GAAG,IAAIzD,OAAO,CAAC,UAAS8R,OAAO,EAAEC,MAAM,EAAE;MAC9CxE,CAAC,CAACuE,OAAO,GAAGA,OAAO;MACnBvE,CAAC,CAACwE,MAAM,GAAGA,MAAM;IACrB,CAAC,CAAC;IAEF,OAAOxE,CAAC;EACZ;;EAEA;EACA,SAASyE,qBAAqBA,CAACvO,OAAO,EAAEwO,OAAO,EAAEC,YAAY,EAAE;IAC3D,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAIC,cAAc,GAAG,IAAIpS,OAAO,CAAC,UAAU8R,OAAO,EAAEC,MAAM,EAAE;MACxDI,aAAa,GAAGvB,UAAU,CAAC,YAAY;QACnCmB,MAAM,CAAC;UAAE,OAAO,EAAEG,YAAY,IAAI,2CAA2C,GAAGD,OAAO,GAAG;QAAK,CAAC,CAAC;MACrG,CAAC,EAAEA,OAAO,CAAC;IACf,CAAC,CAAC;IAEF,OAAOjS,OAAO,CAACqS,IAAI,CAAC,CAAC5O,OAAO,EAAE2O,cAAc,CAAC,CAAC,CAACE,OAAO,CAAC,YAAY;MAC/DrC,YAAY,CAACkC,aAAa,CAAC;IAC/B,CAAC,CAAC;EACN;EAEA,SAASpM,qBAAqBA,CAAA,EAAG;IAC7B,IAAItC,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7B,IAAI,CAAClD,WAAW,CAACC,MAAM,EAAE;MACrBgD,OAAO,CAACK,UAAU,CAAC,CAAC;MACpB,OAAOL,OAAO,CAACA,OAAO;IAC1B;IAEA,IAAIjD,WAAW,CAAC+R,MAAM,EAAE;MACpB9O,OAAO,CAACK,UAAU,CAAC,CAAC;MACpB,OAAOL,OAAO,CAACA,OAAO;IAC1B;IAEA,IAAI8O,MAAM,GAAG1R,QAAQ,CAAC8D,aAAa,CAAC,QAAQ,CAAC;IAC7CnE,WAAW,CAAC+R,MAAM,GAAGA,MAAM;IAE3BA,MAAM,CAACC,MAAM,GAAG,YAAW;MACvB,IAAIC,OAAO,GAAGrS,EAAE,CAAC0J,SAAS,CAACE,SAAS,CAAC,CAAC;MACtC,IAAIyI,OAAO,CAAC3E,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC3BtN,WAAW,CAACkS,YAAY,GAAG3E,SAAS,CAAC,CAAC;MAC1C,CAAC,MAAM;QACHvN,WAAW,CAACkS,YAAY,GAAGD,OAAO,CAACrR,SAAS,CAAC,CAAC,EAAEqR,OAAO,CAACvR,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;MAC5E;MACAuC,OAAO,CAACK,UAAU,CAAC,CAAC;IACxB,CAAC;IAED,IAAI7C,GAAG,GAAGb,EAAE,CAAC0J,SAAS,CAACkF,kBAAkB,CAAC,CAAC;IAC3CuD,MAAM,CAAC1N,YAAY,CAAC,KAAK,EAAE5D,GAAI,CAAC;IAChCsR,MAAM,CAAC1N,YAAY,CAAC,SAAS,EAAE,yEAAyE,CAAC;IACzG0N,MAAM,CAAC1N,YAAY,CAAC,OAAO,EAAE,yBAA0B,CAAC;IACxD0N,MAAM,CAACzN,KAAK,CAACC,OAAO,GAAG,MAAM;IAC7BlE,QAAQ,CAACmE,IAAI,CAACC,WAAW,CAACsN,MAAM,CAAC;IAEjC,IAAIrN,eAAe,GAAG,SAAAA,CAASC,KAAK,EAAE;MAClC,IAAKA,KAAK,CAACC,MAAM,KAAK5E,WAAW,CAACkS,YAAY,IAAMlS,WAAW,CAAC+R,MAAM,CAACjN,aAAa,KAAKH,KAAK,CAACI,MAAO,EAAE;QACpG;MACJ;MAEA,IAAI,EAAEJ,KAAK,CAACO,IAAI,IAAI,WAAW,IAAIP,KAAK,CAACO,IAAI,IAAI,SAAS,IAAIP,KAAK,CAACO,IAAI,IAAI,OAAO,CAAC,EAAE;QAClF;MACJ;MAGA,IAAIP,KAAK,CAACO,IAAI,IAAI,WAAW,EAAE;QAC3BtF,EAAE,CAACqN,UAAU,CAAC,CAAC;MACnB;MAEA,IAAIkF,SAAS,GAAGnS,WAAW,CAACE,YAAY,CAACkS,MAAM,CAAC,CAAC,EAAEpS,WAAW,CAACE,YAAY,CAACM,MAAM,CAAC;MAEnF,KAAK,IAAID,CAAC,GAAG4R,SAAS,CAAC3R,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QAC5C,IAAI0C,OAAO,GAAGkP,SAAS,CAAC5R,CAAC,CAAC;QAC1B,IAAIoE,KAAK,CAACO,IAAI,IAAI,OAAO,EAAE;UACvBjC,OAAO,CAACQ,QAAQ,CAAC,CAAC;QACtB,CAAC,MAAM;UACHR,OAAO,CAACK,UAAU,CAACqB,KAAK,CAACO,IAAI,IAAI,WAAW,CAAC;QACjD;MACJ;IACJ,CAAC;IAEDtD,MAAM,CAAC0D,gBAAgB,CAAC,SAAS,EAAEZ,eAAe,EAAE,KAAK,CAAC;IAE1D,OAAOzB,OAAO,CAACA,OAAO;EAC1B;EAEA,SAASqD,mBAAmBA,CAAA,EAAG;IAC3B,IAAItG,WAAW,CAACC,MAAM,EAAE;MACpB,IAAIL,EAAE,CAACqG,KAAK,EAAE;QACVmK,UAAU,CAAC,YAAW;UAClBrO,gBAAgB,CAAC,CAAC,CAACqB,IAAI,CAAC,UAASoC,SAAS,EAAE;YACxC,IAAIA,SAAS,EAAE;cACXc,mBAAmB,CAAC,CAAC;YACzB;UACJ,CAAC,CAAC;QACN,CAAC,EAAEtG,WAAW,CAACG,QAAQ,GAAG,IAAI,CAAC;MACnC;IACJ;EACJ;EAEA,SAAS4B,gBAAgBA,CAAA,EAAG;IACxB,IAAIkB,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7B,IAAIlD,WAAW,CAAC+R,MAAM,IAAI/R,WAAW,CAACkS,YAAY,EAAG;MACjD,IAAIG,GAAG,GAAGzS,EAAE,CAAC8J,QAAQ,GAAG,GAAG,IAAI9J,EAAE,CAAC+P,SAAS,GAAG/P,EAAE,CAAC+P,SAAS,GAAG,EAAE,CAAC;MAChE3P,WAAW,CAACE,YAAY,CAACwM,IAAI,CAACzJ,OAAO,CAAC;MACtC,IAAI2B,MAAM,GAAG5E,WAAW,CAACkS,YAAY;MACrC,IAAIlS,WAAW,CAACE,YAAY,CAACM,MAAM,IAAI,CAAC,EAAE;QACtCR,WAAW,CAAC+R,MAAM,CAACjN,aAAa,CAACwN,WAAW,CAACD,GAAG,EAAEzN,MAAM,CAAC;MAC7D;IACJ,CAAC,MAAM;MACH3B,OAAO,CAACK,UAAU,CAAC,CAAC;IACxB;IAEA,OAAOL,OAAO,CAACA,OAAO;EAC1B;EAEA,SAAS2D,uBAAuBA,CAAA,EAAG;IAC/B,IAAI3D,OAAO,GAAGC,aAAa,CAAC,CAAC;IAE7B,IAAIlD,WAAW,CAACC,MAAM,IAAIL,EAAE,CAAC4C,yBAAyB,EAAE;MACpD,IAAIuP,MAAM,GAAG1R,QAAQ,CAAC8D,aAAa,CAAC,QAAQ,CAAC;MAC7C4N,MAAM,CAAC1N,YAAY,CAAC,KAAK,EAAEzE,EAAE,CAAC0J,SAAS,CAACmF,uBAAuB,CAAC,CAAC,CAAC;MAClEsD,MAAM,CAAC1N,YAAY,CAAC,SAAS,EAAE,yEAAyE,CAAC;MACzG0N,MAAM,CAAC1N,YAAY,CAAC,OAAO,EAAE,0BAA2B,CAAC;MACzD0N,MAAM,CAACzN,KAAK,CAACC,OAAO,GAAG,MAAM;MAC7BlE,QAAQ,CAACmE,IAAI,CAACC,WAAW,CAACsN,MAAM,CAAC;MAEjC,IAAIrN,eAAe,GAAG,SAAAA,CAASC,KAAK,EAAE;QAClC,IAAIoN,MAAM,CAACjN,aAAa,KAAKH,KAAK,CAACI,MAAM,EAAE;UACvC;QACJ;QAEA,IAAIJ,KAAK,CAACO,IAAI,KAAK,WAAW,IAAIP,KAAK,CAACO,IAAI,KAAK,aAAa,EAAE;UAC5D;QACJ,CAAC,MAAM,IAAIP,KAAK,CAACO,IAAI,KAAK,aAAa,EAAE;UACrC/D,OAAO,CACH,kFAAkF,GAClF,sIAAsI,GACtI,gIAAgI,GAChI,gGACJ,CAAC;UAEDnB,WAAW,CAACC,MAAM,GAAG,KAAK;UAC1B,IAAIL,EAAE,CAAC6C,sBAAsB,EAAE;YAC3B7C,EAAE,CAAC4C,yBAAyB,GAAG,KAAK;UACxC;QACJ;QAEAnC,QAAQ,CAACmE,IAAI,CAACY,WAAW,CAAC2M,MAAM,CAAC;QACjCnQ,MAAM,CAACyD,mBAAmB,CAAC,SAAS,EAAEX,eAAe,CAAC;QACtDzB,OAAO,CAACK,UAAU,CAAC,CAAC;MACxB,CAAC;MAED1B,MAAM,CAAC0D,gBAAgB,CAAC,SAAS,EAAEZ,eAAe,EAAE,KAAK,CAAC;IAC9D,CAAC,MAAM;MACHzB,OAAO,CAACK,UAAU,CAAC,CAAC;IACxB;IAEA,OAAOkO,qBAAqB,CAACvO,OAAO,CAACA,OAAO,EAAErD,EAAE,CAACoD,qBAAqB,EAAE,0DAA0D,CAAC;EACvI;EAEA,SAASrB,WAAWA,CAAC4Q,IAAI,EAAE;IACvB,IAAI,CAACA,IAAI,IAAIA,IAAI,IAAI,SAAS,EAAE;MAC5B,OAAO;QACHvO,KAAK,EAAE,SAAAA,CAASF,OAAO,EAAE;UACrBlC,MAAM,CAACiD,QAAQ,CAAC2N,MAAM,CAAC5S,EAAE,CAACwE,cAAc,CAACN,OAAO,CAAC,CAAC;UAClD,OAAOZ,aAAa,CAAC,CAAC,CAACD,OAAO;QAClC,CAAC;QAEDiH,MAAM;UAAA,IAAAuI,IAAA,GAAAC,iBAAA,CAAE,WAAe5O,OAAO,EAAE;YAE5B,MAAMjB,YAAY,GAAGiB,OAAO,EAAEjB,YAAY,IAAIjD,EAAE,CAACiD,YAAY;YAC7D,IAAIA,YAAY,KAAK,KAAK,EAAE;cACxBjB,MAAM,CAACiD,QAAQ,CAAC2D,OAAO,CAAC5I,EAAE,CAACuK,eAAe,CAACrG,OAAO,CAAC,CAAC;cACpD;YACJ;YAEA,MAAM6O,SAAS,GAAG/S,EAAE,CAACuK,eAAe,CAACrG,OAAO,CAAC;YAC7C,MAAM8O,QAAQ,SAASC,KAAK,CAACF,SAAS,EAAE;cACpCG,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE;gBACL,cAAc,EAAE;cACpB,CAAC;cACDvO,IAAI,EAAE,IAAIwO,eAAe,CAAC;gBACtBC,aAAa,EAAErT,EAAE,CAACwG,OAAO;gBACzB8M,SAAS,EAAEtT,EAAE,CAAC8J,QAAQ;gBACtByJ,wBAAwB,EAAEtT,OAAO,CAAC0C,WAAW,CAACuB,OAAO,EAAE,KAAK;cAChE,CAAC;YACL,CAAC,CAAC;YAEF,IAAI8O,QAAQ,CAACQ,UAAU,EAAE;cACrBxR,MAAM,CAACiD,QAAQ,CAACc,IAAI,GAAGiN,QAAQ,CAACnJ,GAAG;cACnC;YACJ;YAEA,IAAImJ,QAAQ,CAACS,EAAE,EAAE;cACbzR,MAAM,CAACiD,QAAQ,CAACyO,MAAM,CAAC,CAAC;cACxB;YACJ;YAEA,MAAM,IAAI7T,KAAK,CAAC,gDAAgD,CAAC;UACrE,CAAC;UAAA,gBAhCDyK,MAAMA,CAAAqJ,EAAA;YAAA,OAAAd,IAAA,CAAAxK,KAAA,OAAAuL,SAAA;UAAA;QAAA,GAgCL;QAEDjK,QAAQ,EAAE,SAAAA,CAASzF,OAAO,EAAE;UACxBlC,MAAM,CAACiD,QAAQ,CAAC2N,MAAM,CAAC5S,EAAE,CAACwK,iBAAiB,CAACtG,OAAO,CAAC,CAAC;UACrD,OAAOZ,aAAa,CAAC,CAAC,CAACD,OAAO;QAClC,CAAC;QAEDwH,iBAAiB,EAAG,SAAAA,CAAA,EAAW;UAC3B,IAAIgJ,UAAU,GAAG7T,EAAE,CAACyK,gBAAgB,CAAC,CAAC;UACtC,IAAI,OAAOoJ,UAAU,KAAK,WAAW,EAAE;YACnC7R,MAAM,CAACiD,QAAQ,CAACc,IAAI,GAAG8N,UAAU;UACrC,CAAC,MAAM;YACH,MAAM,kCAAkC;UAC5C;UACA,OAAOvQ,aAAa,CAAC,CAAC,CAACD,OAAO;QAClC,CAAC;QAEDV,WAAW,EAAE,SAAAA,CAASuB,OAAO,EAAE4P,UAAU,EAAE;UAEvC,IAAI5P,OAAO,IAAIA,OAAO,CAACvB,WAAW,EAAE;YAChC,OAAOuB,OAAO,CAACvB,WAAW;UAC9B,CAAC,MAAM,IAAI3C,EAAE,CAAC2C,WAAW,EAAE;YACvB,OAAO3C,EAAE,CAAC2C,WAAW;UACzB,CAAC,MAAM;YACH,OAAOsC,QAAQ,CAACc,IAAI;UACxB;QACJ;MACJ,CAAC;IACL;IAEA,IAAI4M,IAAI,IAAI,SAAS,EAAE;MACnBvS,WAAW,CAACC,MAAM,GAAG,KAAK;MAC1B,IAAI0T,wBAAwB,GAAG,SAAAA,CAASC,QAAQ,EAAEC,MAAM,EAAE/P,OAAO,EAAE;QAC/D,IAAIlC,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACE,OAAO,CAACgS,YAAY,EAAE;UAC/C;UACA,OAAOlS,MAAM,CAACE,OAAO,CAACgS,YAAY,CAACzI,IAAI,CAACuI,QAAQ,EAAEC,MAAM,EAAE/P,OAAO,CAAC;QACtE,CAAC,MAAM;UACH,OAAOlC,MAAM,CAACyJ,IAAI,CAACuI,QAAQ,EAAEC,MAAM,EAAE/P,OAAO,CAAC;QACjD;MACJ,CAAC;MAED,IAAIiQ,0BAA0B,GAAG,SAAAA,CAAUC,WAAW,EAAE;QACpD,IAAIA,WAAW,IAAIA,WAAW,CAACC,cAAc,EAAE;UAC3C,OAAOC,MAAM,CAACC,IAAI,CAACH,WAAW,CAACC,cAAc,CAAC,CAACG,MAAM,CAAC,UAAUtQ,OAAO,EAAEuQ,UAAU,EAAE;YACjFvQ,OAAO,CAACuQ,UAAU,CAAC,GAAGL,WAAW,CAACC,cAAc,CAACI,UAAU,CAAC;YAC5D,OAAOvQ,OAAO;UAClB,CAAC,EAAE,CAAC,CAAC,CAAC;QACV,CAAC,MAAM;UACH,OAAO,CAAC,CAAC;QACb;MACJ,CAAC;MAED,IAAIwQ,oBAAoB,GAAG,SAAAA,CAAUL,cAAc,EAAE;QACjD,OAAOC,MAAM,CAACC,IAAI,CAACF,cAAc,CAAC,CAACG,MAAM,CAAC,UAAUtQ,OAAO,EAAEuQ,UAAU,EAAE;UACrEvQ,OAAO,CAAC4I,IAAI,CAAC2H,UAAU,GAAC,GAAG,GAACJ,cAAc,CAACI,UAAU,CAAC,CAAC;UACvD,OAAOvQ,OAAO;QAClB,CAAC,EAAE,EAAE,CAAC,CAAC0M,IAAI,CAAC,GAAG,CAAC;MACpB,CAAC;MAED,IAAI+D,oBAAoB,GAAG,SAAAA,CAAUP,WAAW,EAAE;QAC9C,IAAIC,cAAc,GAAGF,0BAA0B,CAACC,WAAW,CAAC;QAC5DC,cAAc,CAACpP,QAAQ,GAAG,IAAI;QAC9B,IAAImP,WAAW,IAAIA,WAAW,CAACnQ,MAAM,IAAI,MAAM,EAAE;UAC7CoQ,cAAc,CAACO,MAAM,GAAG,KAAK;QACjC;QACA,OAAOF,oBAAoB,CAACL,cAAc,CAAC;MAC/C,CAAC;MAED,IAAIQ,qBAAqB,GAAG,SAAAA,CAAA,EAAW;QACnC,OAAO7U,EAAE,CAAC2C,WAAW,IAAI,kBAAkB;MAC/C,CAAC;MAED,OAAO;QACHyB,KAAK,EAAE,SAAAA,CAASF,OAAO,EAAE;UACrB,IAAIb,OAAO,GAAGC,aAAa,CAAC,CAAC;UAE7B,IAAI+Q,cAAc,GAAGM,oBAAoB,CAACzQ,OAAO,CAAC;UAClD,IAAI8P,QAAQ,GAAGhU,EAAE,CAACwE,cAAc,CAACN,OAAO,CAAC;UACzC,IAAI4Q,GAAG,GAAGf,wBAAwB,CAACC,QAAQ,EAAE,QAAQ,EAAEK,cAAc,CAAC;UACtE,IAAIU,SAAS,GAAG,KAAK;UAErB,IAAIC,MAAM,GAAG,KAAK;UAClB,IAAIC,YAAY,GAAG,SAAAA,CAAA,EAAW;YAC1BD,MAAM,GAAG,IAAI;YACbF,GAAG,CAACI,KAAK,CAAC,CAAC;UACf,CAAC;UAEDJ,GAAG,CAACpP,gBAAgB,CAAC,WAAW,EAAE,UAASX,KAAK,EAAE;YAC9C,IAAIA,KAAK,CAAC8E,GAAG,CAAC/I,OAAO,CAAC+T,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;cACjD,IAAI/O,QAAQ,GAAGT,aAAa,CAACN,KAAK,CAAC8E,GAAG,CAAC;cACvCtE,eAAe,CAACO,QAAQ,EAAEzC,OAAO,CAAC;cAClC4R,YAAY,CAAC,CAAC;cACdF,SAAS,GAAG,IAAI;YACpB;UACJ,CAAC,CAAC;UAEFD,GAAG,CAACpP,gBAAgB,CAAC,WAAW,EAAE,UAASX,KAAK,EAAE;YAC9C,IAAI,CAACgQ,SAAS,EAAE;cACZ,IAAIhQ,KAAK,CAAC8E,GAAG,CAAC/I,OAAO,CAAC+T,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBACjD,IAAI/O,QAAQ,GAAGT,aAAa,CAACN,KAAK,CAAC8E,GAAG,CAAC;gBACvCtE,eAAe,CAACO,QAAQ,EAAEzC,OAAO,CAAC;gBAClC4R,YAAY,CAAC,CAAC;gBACdF,SAAS,GAAG,IAAI;cACpB,CAAC,MAAM;gBACH1R,OAAO,CAACQ,QAAQ,CAAC,CAAC;gBAClBoR,YAAY,CAAC,CAAC;cAClB;YACJ;UACJ,CAAC,CAAC;UAEFH,GAAG,CAACpP,gBAAgB,CAAC,MAAM,EAAE,UAASX,KAAK,EAAE;YACzC,IAAI,CAACiQ,MAAM,EAAE;cACT3R,OAAO,CAACQ,QAAQ,CAAC;gBACbsR,MAAM,EAAE;cACZ,CAAC,CAAC;YACN;UACJ,CAAC,CAAC;UAEF,OAAO9R,OAAO,CAACA,OAAO;QAC1B,CAAC;QAEDiH,MAAM,EAAE,SAAAA,CAASpG,OAAO,EAAE;UACtB,IAAIb,OAAO,GAAGC,aAAa,CAAC,CAAC;UAE7B,IAAIyP,SAAS,GAAG/S,EAAE,CAACuK,eAAe,CAACrG,OAAO,CAAC;UAC3C,IAAI4Q,GAAG,GAAGf,wBAAwB,CAAChB,SAAS,EAAE,QAAQ,EAAE,uCAAuC,CAAC;UAEhG,IAAInP,KAAK;UAETkR,GAAG,CAACpP,gBAAgB,CAAC,WAAW,EAAE,UAASX,KAAK,EAAE;YAC9C,IAAIA,KAAK,CAAC8E,GAAG,CAAC/I,OAAO,CAAC+T,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;cACjDC,GAAG,CAACI,KAAK,CAAC,CAAC;YACf;UACJ,CAAC,CAAC;UAEFJ,GAAG,CAACpP,gBAAgB,CAAC,WAAW,EAAE,UAASX,KAAK,EAAE;YAC9C,IAAIA,KAAK,CAAC8E,GAAG,CAAC/I,OAAO,CAAC+T,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;cACjDC,GAAG,CAACI,KAAK,CAAC,CAAC;YACf,CAAC,MAAM;cACHtR,KAAK,GAAG,IAAI;cACZkR,GAAG,CAACI,KAAK,CAAC,CAAC;YACf;UACJ,CAAC,CAAC;UAEFJ,GAAG,CAACpP,gBAAgB,CAAC,MAAM,EAAE,UAASX,KAAK,EAAE;YACzC,IAAInB,KAAK,EAAE;cACPP,OAAO,CAACQ,QAAQ,CAAC,CAAC;YACtB,CAAC,MAAM;cACH7D,EAAE,CAACqN,UAAU,CAAC,CAAC;cACfhK,OAAO,CAACK,UAAU,CAAC,CAAC;YACxB;UACJ,CAAC,CAAC;UAEF,OAAOL,OAAO,CAACA,OAAO;QAC1B,CAAC;QAEDsG,QAAQ,EAAG,SAAAA,CAASzF,OAAO,EAAE;UACzB,IAAIb,OAAO,GAAGC,aAAa,CAAC,CAAC;UAC7B,IAAI8R,WAAW,GAAGpV,EAAE,CAACwK,iBAAiB,CAAC,CAAC;UACxC,IAAI6J,cAAc,GAAGM,oBAAoB,CAACzQ,OAAO,CAAC;UAClD,IAAI4Q,GAAG,GAAGf,wBAAwB,CAACqB,WAAW,EAAE,QAAQ,EAAEf,cAAc,CAAC;UACzES,GAAG,CAACpP,gBAAgB,CAAC,WAAW,EAAE,UAASX,KAAK,EAAE;YAC9C,IAAIA,KAAK,CAAC8E,GAAG,CAAC/I,OAAO,CAAC+T,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;cACjDC,GAAG,CAACI,KAAK,CAAC,CAAC;cACX,IAAI9P,KAAK,GAAGC,aAAa,CAACN,KAAK,CAAC8E,GAAG,CAAC;cACpCtE,eAAe,CAACH,KAAK,EAAE/B,OAAO,CAAC;YACnC;UACJ,CAAC,CAAC;UACF,OAAOA,OAAO,CAACA,OAAO;QAC1B,CAAC;QAEDwH,iBAAiB,EAAG,SAAAA,CAAA,EAAW;UAC3B,IAAIgJ,UAAU,GAAG7T,EAAE,CAACyK,gBAAgB,CAAC,CAAC;UACtC,IAAI,OAAOoJ,UAAU,KAAK,WAAW,EAAE;YACnC,IAAIiB,GAAG,GAAGf,wBAAwB,CAACF,UAAU,EAAE,QAAQ,EAAE,aAAa,CAAC;YACvEiB,GAAG,CAACpP,gBAAgB,CAAC,WAAW,EAAE,UAASX,KAAK,EAAE;cAC9C,IAAIA,KAAK,CAAC8E,GAAG,CAAC/I,OAAO,CAAC+T,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBACjDC,GAAG,CAACI,KAAK,CAAC,CAAC;cACf;YACJ,CAAC,CAAC;UACN,CAAC,MAAM;YACH,MAAM,kCAAkC;UAC5C;QACJ,CAAC;QAEDvS,WAAW,EAAE,SAAAA,CAASuB,OAAO,EAAE;UAC3B,OAAO2Q,qBAAqB,CAAC,CAAC;QAClC;MACJ,CAAC;IACL;IAEA,IAAIlC,IAAI,IAAI,gBAAgB,EAAE;MAC1BvS,WAAW,CAACC,MAAM,GAAG,KAAK;MAE1B,OAAO;QACH+D,KAAK,EAAE,SAAAA,CAASF,OAAO,EAAE;UACrB,IAAIb,OAAO,GAAGC,aAAa,CAAC,CAAC;UAC7B,IAAI0Q,QAAQ,GAAGhU,EAAE,CAACwE,cAAc,CAACN,OAAO,CAAC;UAEzCmR,cAAc,CAACC,SAAS,CAAC,UAAU,EAAE,UAASvQ,KAAK,EAAE;YACjDsQ,cAAc,CAACE,WAAW,CAAC,UAAU,CAAC;YACtCvT,MAAM,CAACE,OAAO,CAACsT,OAAO,CAACC,UAAU,CAACP,KAAK,CAAC,CAAC;YACzC,IAAI9P,KAAK,GAAGC,aAAa,CAACN,KAAK,CAAC8E,GAAG,CAAC;YACpCtE,eAAe,CAACH,KAAK,EAAE/B,OAAO,CAAC;UACnC,CAAC,CAAC;UAEFrB,MAAM,CAACE,OAAO,CAACsT,OAAO,CAACC,UAAU,CAACC,OAAO,CAAC1B,QAAQ,CAAC;UACnD,OAAO3Q,OAAO,CAACA,OAAO;QAC1B,CAAC;QAEDiH,MAAM,EAAE,SAAAA,CAASpG,OAAO,EAAE;UACtB,IAAIb,OAAO,GAAGC,aAAa,CAAC,CAAC;UAC7B,IAAIyP,SAAS,GAAG/S,EAAE,CAACuK,eAAe,CAACrG,OAAO,CAAC;UAE3CmR,cAAc,CAACC,SAAS,CAAC,UAAU,EAAE,UAASvQ,KAAK,EAAE;YACjDsQ,cAAc,CAACE,WAAW,CAAC,UAAU,CAAC;YACtCvT,MAAM,CAACE,OAAO,CAACsT,OAAO,CAACC,UAAU,CAACP,KAAK,CAAC,CAAC;YACzClV,EAAE,CAACqN,UAAU,CAAC,CAAC;YACfhK,OAAO,CAACK,UAAU,CAAC,CAAC;UACxB,CAAC,CAAC;UAEF1B,MAAM,CAACE,OAAO,CAACsT,OAAO,CAACC,UAAU,CAACC,OAAO,CAAC3C,SAAS,CAAC;UACpD,OAAO1P,OAAO,CAACA,OAAO;QAC1B,CAAC;QAEDsG,QAAQ,EAAG,SAAAA,CAASzF,OAAO,EAAE;UACzB,IAAIb,OAAO,GAAGC,aAAa,CAAC,CAAC;UAC7B,IAAI8R,WAAW,GAAGpV,EAAE,CAACwK,iBAAiB,CAACtG,OAAO,CAAC;UAC/CmR,cAAc,CAACC,SAAS,CAAC,UAAU,EAAG,UAASvQ,KAAK,EAAE;YAClDsQ,cAAc,CAACE,WAAW,CAAC,UAAU,CAAC;YACtCvT,MAAM,CAACE,OAAO,CAACsT,OAAO,CAACC,UAAU,CAACP,KAAK,CAAC,CAAC;YACzC,IAAI9P,KAAK,GAAGC,aAAa,CAACN,KAAK,CAAC8E,GAAG,CAAC;YACpCtE,eAAe,CAACH,KAAK,EAAE/B,OAAO,CAAC;UACnC,CAAC,CAAC;UACFrB,MAAM,CAACE,OAAO,CAACsT,OAAO,CAACC,UAAU,CAACC,OAAO,CAACN,WAAW,CAAC;UACtD,OAAO/R,OAAO,CAACA,OAAO;QAE1B,CAAC;QAEDwH,iBAAiB,EAAG,SAAAA,CAAA,EAAW;UAC3B,IAAIgJ,UAAU,GAAG7T,EAAE,CAACyK,gBAAgB,CAAC,CAAC;UACtC,IAAI,OAAOoJ,UAAU,KAAK,WAAW,EAAE;YACnC7R,MAAM,CAACE,OAAO,CAACsT,OAAO,CAACC,UAAU,CAACC,OAAO,CAAC7B,UAAU,CAAC;UACzD,CAAC,MAAM;YACH,MAAM,kCAAkC;UAC5C;QACJ,CAAC;QAEDlR,WAAW,EAAE,SAAAA,CAASuB,OAAO,EAAE;UAC3B,IAAIA,OAAO,IAAIA,OAAO,CAACvB,WAAW,EAAE;YAChC,OAAOuB,OAAO,CAACvB,WAAW;UAC9B,CAAC,MAAM,IAAI3C,EAAE,CAAC2C,WAAW,EAAE;YACvB,OAAO3C,EAAE,CAAC2C,WAAW;UACzB,CAAC,MAAM;YACH,OAAO,kBAAkB;UAC7B;QACJ;MACJ,CAAC;IACL;IAEA,MAAM,wBAAwB,GAAGgQ,IAAI;EACzC;EAEA,IAAIgD,YAAY,GAAG,SAAAA,CAAA,EAAW;IAC1B,IAAI,EAAE,IAAI,YAAYA,YAAY,CAAC,EAAE;MACjC,OAAO,IAAIA,YAAY,CAAC,CAAC;IAC7B;IAEAC,YAAY,CAACC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;IACvCD,YAAY,CAACE,UAAU,CAAC,SAAS,CAAC;IAElC,IAAIC,EAAE,GAAG,IAAI;IAEb,SAASC,YAAYA,CAAA,EAAG;MACpB,IAAIC,IAAI,GAAG,IAAIxJ,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAC/B,KAAK,IAAI/L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiV,YAAY,CAAChV,MAAM,EAAED,CAAC,EAAE,EAAG;QAC3C,IAAI6Q,GAAG,GAAGoE,YAAY,CAACpE,GAAG,CAAC7Q,CAAC,CAAC;QAC7B,IAAI6Q,GAAG,IAAIA,GAAG,CAAC1Q,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;UACzC,IAAIoV,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC3E,GAAG,CAAC;UACrC,IAAI0E,KAAK,EAAE;YACP,IAAI;cACA,IAAIE,OAAO,GAAGlN,IAAI,CAAC4C,KAAK,CAACoK,KAAK,CAAC,CAACE,OAAO;cACvC,IAAI,CAACA,OAAO,IAAIA,OAAO,GAAGH,IAAI,EAAE;gBAC5BL,YAAY,CAACE,UAAU,CAACtE,GAAG,CAAC;cAChC;YACJ,CAAC,CAAC,OAAO6E,GAAG,EAAE;cACVT,YAAY,CAACE,UAAU,CAACtE,GAAG,CAAC;YAChC;UACJ;QACJ;MACJ;IACJ;IAEAuE,EAAE,CAAChF,GAAG,GAAG,UAAS7K,KAAK,EAAE;MACrB,IAAI,CAACA,KAAK,EAAE;QACR;MACJ;MAEA,IAAIsL,GAAG,GAAG,cAAc,GAAGtL,KAAK;MAChC,IAAIgQ,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC3E,GAAG,CAAC;MACrC,IAAI0E,KAAK,EAAE;QACPN,YAAY,CAACE,UAAU,CAACtE,GAAG,CAAC;QAC5B0E,KAAK,GAAGhN,IAAI,CAAC4C,KAAK,CAACoK,KAAK,CAAC;MAC7B;MAEAF,YAAY,CAAC,CAAC;MACd,OAAOE,KAAK;IAChB,CAAC;IAEDH,EAAE,CAAC1L,GAAG,GAAG,UAASnE,KAAK,EAAE;MACrB8P,YAAY,CAAC,CAAC;MAEd,IAAIxE,GAAG,GAAG,cAAc,GAAGtL,KAAK,CAACA,KAAK;MACtCA,KAAK,CAACkQ,OAAO,GAAG,IAAI3J,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAI,EAAE,GAAG,EAAE,GAAG,IAAK;MACvDkJ,YAAY,CAACC,OAAO,CAACrE,GAAG,EAAEtI,IAAI,CAACC,SAAS,CAACjD,KAAK,CAAC,CAAC;IACpD,CAAC;EACL,CAAC;EAED,IAAIoQ,aAAa,GAAG,SAAAA,CAAA,EAAW;IAC3B,IAAI,EAAE,IAAI,YAAYA,aAAa,CAAC,EAAE;MAClC,OAAO,IAAIA,aAAa,CAAC,CAAC;IAC9B;IAEA,IAAIP,EAAE,GAAG,IAAI;IAEbA,EAAE,CAAChF,GAAG,GAAG,UAAS7K,KAAK,EAAE;MACrB,IAAI,CAACA,KAAK,EAAE;QACR;MACJ;MAEA,IAAIgQ,KAAK,GAAGK,SAAS,CAAC,cAAc,GAAGrQ,KAAK,CAAC;MAC7CsQ,SAAS,CAAC,cAAc,GAAGtQ,KAAK,EAAE,EAAE,EAAEuQ,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;MAC7D,IAAIP,KAAK,EAAE;QACP,OAAOhN,IAAI,CAAC4C,KAAK,CAACoK,KAAK,CAAC;MAC5B;IACJ,CAAC;IAEDH,EAAE,CAAC1L,GAAG,GAAG,UAASnE,KAAK,EAAE;MACrBsQ,SAAS,CAAC,cAAc,GAAGtQ,KAAK,CAACA,KAAK,EAAEgD,IAAI,CAACC,SAAS,CAACjD,KAAK,CAAC,EAAEuQ,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACxF,CAAC;IAEDV,EAAE,CAACD,UAAU,GAAG,UAAStE,GAAG,EAAE;MAC1BgF,SAAS,CAAChF,GAAG,EAAE,EAAE,EAAEiF,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED,IAAIA,gBAAgB,GAAG,SAAAA,CAAUC,OAAO,EAAE;MACtC,IAAIC,GAAG,GAAG,IAAIlK,IAAI,CAAC,CAAC;MACpBkK,GAAG,CAACC,OAAO,CAACD,GAAG,CAACjK,OAAO,CAAC,CAAC,GAAIgK,OAAO,GAAC,EAAE,GAAC,IAAK,CAAC;MAC9C,OAAOC,GAAG;IACd,CAAC;IAED,IAAIJ,SAAS,GAAG,SAAAA,CAAU/E,GAAG,EAAE;MAC3B,IAAIqF,IAAI,GAAGrF,GAAG,GAAG,GAAG;MACpB,IAAIsF,EAAE,GAAGrW,QAAQ,CAACsW,MAAM,CAAC9V,KAAK,CAAC,GAAG,CAAC;MACnC,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmW,EAAE,CAAClW,MAAM,EAAED,CAAC,EAAE,EAAE;QAChC,IAAIqW,CAAC,GAAGF,EAAE,CAACnW,CAAC,CAAC;QACb,OAAOqW,CAAC,CAACtJ,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;UACvBsJ,CAAC,GAAGA,CAAC,CAAChW,SAAS,CAAC,CAAC,CAAC;QACtB;QACA,IAAIgW,CAAC,CAAClW,OAAO,CAAC+V,IAAI,CAAC,IAAI,CAAC,EAAE;UACtB,OAAOG,CAAC,CAAChW,SAAS,CAAC6V,IAAI,CAACjW,MAAM,EAAEoW,CAAC,CAACpW,MAAM,CAAC;QAC7C;MACJ;MACA,OAAO,EAAE;IACb,CAAC;IAED,IAAI4V,SAAS,GAAG,SAAAA,CAAUhF,GAAG,EAAE0E,KAAK,EAAEe,cAAc,EAAE;MAClD,IAAIF,MAAM,GAAGvF,GAAG,GAAG,GAAG,GAAG0E,KAAK,GAAG,IAAI,GAC/B,UAAU,GAAGe,cAAc,CAACC,WAAW,CAAC,CAAC,GAAG,IAAI;MACtDzW,QAAQ,CAACsW,MAAM,GAAGA,MAAM;IAC5B,CAAC;EACL,CAAC;EAED,SAASlV,qBAAqBA,CAAA,EAAG;IAC7B,IAAI;MACA,OAAO,IAAI8T,YAAY,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAOU,GAAG,EAAE,CACd;IAEA,OAAO,IAAIC,aAAa,CAAC,CAAC;EAC9B;EAEA,SAASlV,YAAYA,CAAC+V,EAAE,EAAE;IACtB,OAAO,YAAW;MACd,IAAInX,EAAE,CAACgD,aAAa,EAAE;QAClBmU,EAAE,CAAC9O,KAAK,CAAChH,OAAO,EAAEmG,KAAK,CAAC4P,SAAS,CAAC3F,KAAK,CAAC4F,IAAI,CAACzD,SAAS,CAAC,CAAC;MAC5D;IACJ,CAAC;EACL;AACJ;;AAEA;AACA,SAASjL,aAAaA,CAAC2O,KAAK,EAAE;EAC1B,MAAMC,SAAS,GAAGpP,MAAM,CAACqP,aAAa,CAAC,GAAGF,KAAK,CAAC;EAChD,OAAOG,IAAI,CAACF,SAAS,CAAC;AAC1B;AAEA,SAASzX,QAAQ,IAAI4X,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}