{"ast": null, "code": "import { Validators } from '@angular/forms';\n// Sweet Alert\nimport Swal from 'sweetalert2';\nimport { addApplication, fetchApplicationData, updateApplication } from 'src/app/store/Jobs/jobs_action';\nimport { selectJobsData, selectJobsLoading } from 'src/app/store/Jobs/jobs_selector';\nimport { cloneDeep } from 'lodash';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/pagination.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@ngrx/store\";\nimport * as i4 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"../../../../shared/breadcrumbs/breadcrumbs.component\";\nimport * as i8 from \"angularx-flatpickr\";\nconst _c0 = (a0, a1, a2, a3) => ({\n  \"bg-danger-subtle text-danger\": a0,\n  \"bg-info-subtle text-info\": a1,\n  \"bg-warning-subtle text-warning\": a2,\n  \"bg-success-subtle text-success\": a3\n});\nfunction ApplicationComponent_ng_template_65_For_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 74)(1, \"th\", 75)(2, \"div\", 68)(3, \"input\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_ng_template_65_For_24_Template_input_ngModelChange_3_listener($event) {\n      const data_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(data_r7.state, $event) || (data_r7.state = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function ApplicationComponent_ng_template_65_For_24_Template_input_change_3_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onCheckboxChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"td\", 77)(5, \"a\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 79)(8, \"div\", 80)(9, \"div\", 10);\n    i0.ɵɵelement(10, \"img\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 82);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"td\", 83);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 84);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 85);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 86);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 87)(22, \"span\", 88);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"td\")(25, \"ul\", 89)(26, \"li\", 90)(27, \"a\", 91);\n    i0.ɵɵelement(28, \"i\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"li\", 93);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_65_For_24_Template_li_click_29_listener() {\n      const $index_r8 = i0.ɵɵrestoreView(_r6).$index;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const content_r2 = i0.ɵɵreference(101);\n      return i0.ɵɵresetView(ctx_r4.editorder(content_r2, $index_r8));\n    });\n    i0.ɵɵelementStart(30, \"a\", 94);\n    i0.ɵɵelement(31, \"i\", 95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"li\", 96)(33, \"a\", 97);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_65_For_24_Template_a_click_33_listener() {\n      const data_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const deletemodal_r3 = i0.ɵɵreference(103);\n      return i0.ɵɵresetView(ctx_r4.confirm(deletemodal_r3, data_r7.id));\n    });\n    i0.ɵɵelement(34, \"i\", 98);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const data_r7 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"a_\", data_r7.id, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"value\", data_r7.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", data_r7.state);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"#VZ\", data_r7.id, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"src\", data_r7.img, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r7.designation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r7.date);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r7.contacts);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r7.type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(13, _c0, data_r7.status == \"Rejected\", data_r7.status == \"New\", data_r7.status == \"Pending\", data_r7.status == \"Approved\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", data_r7.status, \"\");\n  }\n}\nfunction ApplicationComponent_ng_template_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"table\", 64)(1, \"thead\", 65)(2, \"tr\", 66)(3, \"th\", 67)(4, \"div\", 68)(5, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_ng_template_65_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.masterSelected, $event) || (ctx_r4.masterSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function ApplicationComponent_ng_template_65_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.checkUncheckAll($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"th\", 70);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_65_Template_th_click_6_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"id\"));\n    });\n    i0.ɵɵtext(7, \"Application ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_65_Template_th_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"name\"));\n    });\n    i0.ɵɵtext(9, \"Company Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_65_Template_th_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"designation\"));\n    });\n    i0.ɵɵtext(11, \" Designation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_65_Template_th_click_12_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"date\"));\n    });\n    i0.ɵɵtext(13, \"Apply Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_65_Template_th_click_14_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"contacts\"));\n    });\n    i0.ɵɵtext(15, \"Contacts \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_65_Template_th_click_16_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"type\"));\n    });\n    i0.ɵɵtext(17, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_65_Template_th_click_18_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"status\"));\n    });\n    i0.ɵɵtext(19, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 72);\n    i0.ɵɵtext(21, \"Action\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"tbody\", 73);\n    i0.ɵɵrepeaterCreate(23, ApplicationComponent_ng_template_65_For_24_Template, 35, 18, \"tr\", 74, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.masterSelected);\n    i0.ɵɵadvance(18);\n    i0.ɵɵrepeater(ctx_r4.applications);\n  }\n}\nfunction ApplicationComponent_ng_template_69_For_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 74)(1, \"th\", 75)(2, \"div\", 68)(3, \"input\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_ng_template_69_For_24_Template_input_ngModelChange_3_listener($event) {\n      const data_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      i0.ɵɵtwoWayBindingSet(data_r11.state, $event) || (data_r11.state = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function ApplicationComponent_ng_template_69_For_24_Template_input_change_3_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onCheckboxChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"td\", 77)(5, \"a\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 79)(8, \"div\", 80)(9, \"div\", 10);\n    i0.ɵɵelement(10, \"img\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 82);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"td\", 83);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 84);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 85);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 86);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 87)(22, \"span\", 88);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"td\")(25, \"ul\", 89)(26, \"li\", 90)(27, \"a\", 91);\n    i0.ɵɵelement(28, \"i\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"li\", 93);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_69_For_24_Template_li_click_29_listener() {\n      const $index_r12 = i0.ɵɵrestoreView(_r10).$index;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const content_r2 = i0.ɵɵreference(101);\n      return i0.ɵɵresetView(ctx_r4.editorder(content_r2, $index_r12));\n    });\n    i0.ɵɵelementStart(30, \"a\", 94);\n    i0.ɵɵelement(31, \"i\", 95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"li\", 96)(33, \"a\", 97);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_69_For_24_Template_a_click_33_listener() {\n      const data_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const deletemodal_r3 = i0.ɵɵreference(103);\n      return i0.ɵɵresetView(ctx_r4.confirm(deletemodal_r3, data_r11.id));\n    });\n    i0.ɵɵelement(34, \"i\", 98);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const data_r11 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"a_\", data_r11.id, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"value\", data_r11.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", data_r11.state);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"#VZ\", data_r11.id, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"src\", data_r11.img, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r11.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r11.designation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r11.date);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r11.contacts);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r11.type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(13, _c0, data_r11.status == \"Rejected\", data_r11.status == \"New\", data_r11.status == \"Pending\", data_r11.status == \"Approved\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", data_r11.status, \"\");\n  }\n}\nfunction ApplicationComponent_ng_template_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"table\", 64)(1, \"thead\", 65)(2, \"tr\", 66)(3, \"th\", 67)(4, \"div\", 68)(5, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_ng_template_69_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.masterSelected, $event) || (ctx_r4.masterSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function ApplicationComponent_ng_template_69_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.checkUncheckAll($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"th\", 70);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_69_Template_th_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"id\"));\n    });\n    i0.ɵɵtext(7, \"Application ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_69_Template_th_click_8_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"name\"));\n    });\n    i0.ɵɵtext(9, \"Company Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_69_Template_th_click_10_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"designation\"));\n    });\n    i0.ɵɵtext(11, \" Designation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_69_Template_th_click_12_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"date\"));\n    });\n    i0.ɵɵtext(13, \"Apply Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_69_Template_th_click_14_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"contacts\"));\n    });\n    i0.ɵɵtext(15, \"Contacts \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_69_Template_th_click_16_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"type\"));\n    });\n    i0.ɵɵtext(17, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_69_Template_th_click_18_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"status\"));\n    });\n    i0.ɵɵtext(19, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 72);\n    i0.ɵɵtext(21, \"Action\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"tbody\", 73);\n    i0.ɵɵrepeaterCreate(23, ApplicationComponent_ng_template_69_For_24_Template, 35, 18, \"tr\", 74, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.masterSelected);\n    i0.ɵɵadvance(18);\n    i0.ɵɵrepeater(ctx_r4.applications);\n  }\n}\nfunction ApplicationComponent_ng_template_75_For_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 74)(1, \"th\", 75)(2, \"div\", 68)(3, \"input\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_ng_template_75_For_24_Template_input_ngModelChange_3_listener($event) {\n      const data_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      i0.ɵɵtwoWayBindingSet(data_r15.state, $event) || (data_r15.state = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function ApplicationComponent_ng_template_75_For_24_Template_input_change_3_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onCheckboxChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"td\", 77)(5, \"a\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 79)(8, \"div\", 80)(9, \"div\", 10);\n    i0.ɵɵelement(10, \"img\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 82);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"td\", 83);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 84);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 85);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 86);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 87)(22, \"span\", 88);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"td\")(25, \"ul\", 89)(26, \"li\", 99)(27, \"a\", 91);\n    i0.ɵɵelement(28, \"i\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"li\", 93);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_75_For_24_Template_li_click_29_listener() {\n      const $index_r16 = i0.ɵɵrestoreView(_r14).$index;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const content_r2 = i0.ɵɵreference(101);\n      return i0.ɵɵresetView(ctx_r4.editorder(content_r2, $index_r16));\n    });\n    i0.ɵɵelementStart(30, \"a\", 94);\n    i0.ɵɵelement(31, \"i\", 95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"li\", 96)(33, \"a\", 97);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_75_For_24_Template_a_click_33_listener() {\n      const data_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const deletemodal_r3 = i0.ɵɵreference(103);\n      return i0.ɵɵresetView(ctx_r4.confirm(deletemodal_r3, data_r15.id));\n    });\n    i0.ɵɵelement(34, \"i\", 98);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const data_r15 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"a_\", data_r15.id, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"value\", data_r15.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", data_r15.state);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"#VZ\", data_r15.id, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"src\", data_r15.img, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r15.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r15.designation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r15.date);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r15.contacts);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r15.type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(13, _c0, data_r15.status == \"Rejected\", data_r15.status == \"New\", data_r15.status == \"Pending\", data_r15.status == \"Approved\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", data_r15.status, \"\");\n  }\n}\nfunction ApplicationComponent_ng_template_75_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"table\", 64)(1, \"thead\", 65)(2, \"tr\", 66)(3, \"th\", 67)(4, \"div\", 68)(5, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_ng_template_75_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.masterSelected, $event) || (ctx_r4.masterSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function ApplicationComponent_ng_template_75_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.checkUncheckAll($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"th\", 70);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_75_Template_th_click_6_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"id\"));\n    });\n    i0.ɵɵtext(7, \"Application ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_75_Template_th_click_8_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"name\"));\n    });\n    i0.ɵɵtext(9, \"Company Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_75_Template_th_click_10_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"designation\"));\n    });\n    i0.ɵɵtext(11, \" Designation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_75_Template_th_click_12_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"date\"));\n    });\n    i0.ɵɵtext(13, \"Apply Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_75_Template_th_click_14_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"contacts\"));\n    });\n    i0.ɵɵtext(15, \"Contacts \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_75_Template_th_click_16_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"type\"));\n    });\n    i0.ɵɵtext(17, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_75_Template_th_click_18_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"status\"));\n    });\n    i0.ɵɵtext(19, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 72);\n    i0.ɵɵtext(21, \"Action\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"tbody\", 73);\n    i0.ɵɵrepeaterCreate(23, ApplicationComponent_ng_template_75_For_24_Template, 35, 18, \"tr\", 74, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.masterSelected);\n    i0.ɵɵadvance(18);\n    i0.ɵɵrepeater(ctx_r4.applications);\n  }\n}\nfunction ApplicationComponent_ng_template_79_For_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 74)(1, \"th\", 75)(2, \"div\", 68)(3, \"input\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_ng_template_79_For_24_Template_input_ngModelChange_3_listener($event) {\n      const data_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      i0.ɵɵtwoWayBindingSet(data_r19.state, $event) || (data_r19.state = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function ApplicationComponent_ng_template_79_For_24_Template_input_change_3_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onCheckboxChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"td\", 77)(5, \"a\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 79)(8, \"div\", 80)(9, \"div\", 10);\n    i0.ɵɵelement(10, \"img\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 82);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"td\", 83);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 84);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 85);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 86);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 87)(22, \"span\", 88);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"td\")(25, \"ul\", 89)(26, \"li\", 99)(27, \"a\", 91);\n    i0.ɵɵelement(28, \"i\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"li\", 93);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_79_For_24_Template_li_click_29_listener() {\n      const $index_r20 = i0.ɵɵrestoreView(_r18).$index;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const content_r2 = i0.ɵɵreference(101);\n      return i0.ɵɵresetView(ctx_r4.editorder(content_r2, $index_r20));\n    });\n    i0.ɵɵelementStart(30, \"a\", 94);\n    i0.ɵɵelement(31, \"i\", 95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"li\", 96)(33, \"a\", 97);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_79_For_24_Template_a_click_33_listener() {\n      const data_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const deletemodal_r3 = i0.ɵɵreference(103);\n      return i0.ɵɵresetView(ctx_r4.confirm(deletemodal_r3, data_r19.id));\n    });\n    i0.ɵɵelement(34, \"i\", 98);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const data_r19 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"a_\", data_r19.id, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"value\", data_r19.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", data_r19.state);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"#VZ\", data_r19.id, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"src\", data_r19.img, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r19.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r19.designation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r19.date);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r19.contacts);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r19.type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(13, _c0, data_r19.status == \"Rejected\", data_r19.status == \"New\", data_r19.status == \"Pending\", data_r19.status == \"Approved\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", data_r19.status, \"\");\n  }\n}\nfunction ApplicationComponent_ng_template_79_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"table\", 64)(1, \"thead\", 65)(2, \"tr\", 66)(3, \"th\", 67)(4, \"div\", 68)(5, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_ng_template_79_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.masterSelected, $event) || (ctx_r4.masterSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function ApplicationComponent_ng_template_79_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.checkUncheckAll($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"th\", 70);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_79_Template_th_click_6_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"id\"));\n    });\n    i0.ɵɵtext(7, \"Application ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_79_Template_th_click_8_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"name\"));\n    });\n    i0.ɵɵtext(9, \"Company Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_79_Template_th_click_10_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"designation\"));\n    });\n    i0.ɵɵtext(11, \" Designation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_79_Template_th_click_12_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"date\"));\n    });\n    i0.ɵɵtext(13, \"Apply Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_79_Template_th_click_14_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"contacts\"));\n    });\n    i0.ɵɵtext(15, \"Contacts \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_79_Template_th_click_16_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"type\"));\n    });\n    i0.ɵɵtext(17, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_79_Template_th_click_18_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"status\"));\n    });\n    i0.ɵɵtext(19, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 72);\n    i0.ɵɵtext(21, \"Action\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"tbody\", 73);\n    i0.ɵɵrepeaterCreate(23, ApplicationComponent_ng_template_79_For_24_Template, 35, 18, \"tr\", 74, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.masterSelected);\n    i0.ɵɵadvance(18);\n    i0.ɵɵrepeater(ctx_r4.applications);\n  }\n}\nfunction ApplicationComponent_ng_template_83_For_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 74)(1, \"th\", 75)(2, \"div\", 68)(3, \"input\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_ng_template_83_For_24_Template_input_ngModelChange_3_listener($event) {\n      const data_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      i0.ɵɵtwoWayBindingSet(data_r23.state, $event) || (data_r23.state = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function ApplicationComponent_ng_template_83_For_24_Template_input_change_3_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onCheckboxChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"td\", 77)(5, \"a\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 79)(8, \"div\", 80)(9, \"div\", 10);\n    i0.ɵɵelement(10, \"img\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 82);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"td\", 83);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 84);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 85);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 86);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 87)(22, \"span\", 88);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"td\")(25, \"ul\", 89)(26, \"li\", 99)(27, \"a\", 91);\n    i0.ɵɵelement(28, \"i\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"li\", 93);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_83_For_24_Template_li_click_29_listener() {\n      const $index_r24 = i0.ɵɵrestoreView(_r22).$index;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const content_r2 = i0.ɵɵreference(101);\n      return i0.ɵɵresetView(ctx_r4.editorder(content_r2, $index_r24));\n    });\n    i0.ɵɵelementStart(30, \"a\", 94);\n    i0.ɵɵelement(31, \"i\", 95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"li\", 96)(33, \"a\", 97);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_83_For_24_Template_a_click_33_listener() {\n      const data_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const deletemodal_r3 = i0.ɵɵreference(103);\n      return i0.ɵɵresetView(ctx_r4.confirm(deletemodal_r3, data_r23.id));\n    });\n    i0.ɵɵelement(34, \"i\", 98);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const data_r23 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"a_\", data_r23.id, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"value\", data_r23.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", data_r23.state);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"#VZ\", data_r23.id, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"src\", data_r23.img, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r23.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r23.designation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r23.date);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r23.contacts);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r23.type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(13, _c0, data_r23.status == \"Rejected\", data_r23.status == \"New\", data_r23.status == \"Pending\", data_r23.status == \"Approved\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", data_r23.status, \"\");\n  }\n}\nfunction ApplicationComponent_ng_template_83_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"table\", 64)(1, \"thead\", 65)(2, \"tr\", 66)(3, \"th\", 67)(4, \"div\", 68)(5, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_ng_template_83_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.masterSelected, $event) || (ctx_r4.masterSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function ApplicationComponent_ng_template_83_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.checkUncheckAll($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"th\", 70);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_83_Template_th_click_6_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"id\"));\n    });\n    i0.ɵɵtext(7, \"Application ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_83_Template_th_click_8_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"name\"));\n    });\n    i0.ɵɵtext(9, \"Company Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_83_Template_th_click_10_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"designation\"));\n    });\n    i0.ɵɵtext(11, \" Designation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_83_Template_th_click_12_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"date\"));\n    });\n    i0.ɵɵtext(13, \"Apply Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_83_Template_th_click_14_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"contacts\"));\n    });\n    i0.ɵɵtext(15, \"Contacts \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_83_Template_th_click_16_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"type\"));\n    });\n    i0.ɵɵtext(17, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 71);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_83_Template_th_click_18_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSort(\"status\"));\n    });\n    i0.ɵɵtext(19, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 72);\n    i0.ɵɵtext(21, \"Action\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"tbody\", 73);\n    i0.ɵɵrepeaterCreate(23, ApplicationComponent_ng_template_83_For_24_Template, 35, 18, \"tr\", 74, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.masterSelected);\n    i0.ɵɵadvance(18);\n    i0.ɵɵrepeater(ctx_r4.applications);\n  }\n}\nfunction ApplicationComponent_ng_template_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 100);\n    i0.ɵɵtext(1, \" Prev \");\n  }\n}\nfunction ApplicationComponent_ng_template_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Next \");\n    i0.ɵɵelement(1, \"i\", 101);\n  }\n}\nfunction ApplicationComponent_ng_template_100_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"h5\", 103);\n    i0.ɵɵtext(2, \"Add Application\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_100_Template_button_click_3_listener() {\n      const modal_r26 = i0.ɵɵrestoreView(_r25).$implicit;\n      return i0.ɵɵresetView(modal_r26.dismiss(\"close click\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"form\", 105);\n    i0.ɵɵlistener(\"ngSubmit\", function ApplicationComponent_ng_template_100_Template_form_ngSubmit_4_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.createapplication());\n    });\n    i0.ɵɵelementStart(5, \"div\", 106)(6, \"div\", 107)(7, \"label\", 108);\n    i0.ɵɵtext(8, \"ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 52)(11, \"div\", 110)(12, \"div\", 111)(13, \"label\", 112)(14, \"div\", 113)(15, \"div\", 114);\n    i0.ɵɵelement(16, \"i\", 115);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"input\", 116);\n    i0.ɵɵlistener(\"change\", function ApplicationComponent_ng_template_100_Template_input_change_17_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.fileChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 117)(19, \"div\", 118);\n    i0.ɵɵelement(20, \"img\", 119);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(21, \"div\", 120)(22, \"label\", 121);\n    i0.ɵɵtext(23, \"Company\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"input\", 122);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 120)(26, \"label\", 123);\n    i0.ɵɵtext(27, \"Designation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"input\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 120)(30, \"label\", 125);\n    i0.ɵɵtext(31, \"Apply Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"input\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 120)(34, \"label\", 127);\n    i0.ɵɵtext(35, \"Contacts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"input\", 128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 129)(38, \"div\", 130)(39, \"div\")(40, \"label\", 131);\n    i0.ɵɵtext(41, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"select\", 132)(43, \"option\", 28);\n    i0.ɵɵtext(44, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"option\", 30);\n    i0.ɵɵtext(46, \"Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"option\", 31);\n    i0.ɵɵtext(48, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"option\", 32);\n    i0.ɵɵtext(50, \"Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"option\", 33);\n    i0.ɵɵtext(52, \"Rejected\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(53, \"div\", 130)(54, \"div\")(55, \"label\", 133);\n    i0.ɵɵtext(56, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"select\", 134)(58, \"option\", 28);\n    i0.ɵɵtext(59, \"Select Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"option\", 35);\n    i0.ɵɵtext(61, \"Full Time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"option\", 36);\n    i0.ɵɵtext(63, \"Part Time\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(64, \"div\", 135)(65, \"div\", 136)(66, \"button\", 137);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_100_Template_button_click_66_listener() {\n      const modal_r26 = i0.ɵɵrestoreView(_r25).$implicit;\n      return i0.ɵɵresetView(modal_r26.dismiss(\"close click\"));\n    });\n    i0.ɵɵtext(67, \"Close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"button\", 138);\n    i0.ɵɵtext(69, \"Add\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.applicationData);\n    i0.ɵɵadvance(28);\n    i0.ɵɵproperty(\"convertModelValue\", true);\n  }\n}\nfunction ApplicationComponent_ng_template_102_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 139);\n    i0.ɵɵelement(1, \"lord-icon\", 140);\n    i0.ɵɵelementStart(2, \"div\", 141)(3, \"h4\");\n    i0.ɵɵtext(4, \"You are about to delete a order ?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 142);\n    i0.ɵɵtext(6, \"Deleting your order will remove all of your information from our database.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 143)(8, \"button\", 144);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_102_Template_button_click_8_listener() {\n      const modal_r28 = i0.ɵɵrestoreView(_r27).$implicit;\n      return i0.ɵɵresetView(modal_r28.dismiss(\"close click\"));\n    });\n    i0.ɵɵelement(9, \"i\", 145);\n    i0.ɵɵtext(10, \" Close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 146);\n    i0.ɵɵlistener(\"click\", function ApplicationComponent_ng_template_102_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.deleteData(ctx_r4.deleteId));\n    });\n    i0.ɵɵtext(12, \"Yes, Delete It\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport class ApplicationComponent {\n  constructor(service, formBuilder, store, modalService) {\n    this.service = service;\n    this.formBuilder = formBuilder;\n    this.store = store;\n    this.modalService = modalService;\n    this.submitted = false;\n    this.status = '';\n    this.type = '';\n    // Check Box Checked Value Get\n    this.checkedValGet = [];\n  }\n  ngOnInit() {\n    /**\n    * BreadCrumb\n    */\n    this.breadCrumbItems = [{\n      label: 'Jobs'\n    }, {\n      label: 'Application',\n      active: true\n    }];\n    // Validation\n    this.applicationData = this.formBuilder.group({\n      id: [''],\n      name: ['', [Validators.required]],\n      date: ['', [Validators.required]],\n      type: ['', [Validators.required]],\n      designation: ['', [Validators.required]],\n      contacts: ['', [Validators.required]],\n      status: ['', [Validators.required]]\n    });\n    this.store.dispatch(fetchApplicationData());\n    this.store.select(selectJobsLoading).subscribe(data => {\n      if (data == false) {\n        document.getElementById('elmLoader')?.classList.add('d-none');\n      }\n    });\n    this.store.select(selectJobsData).subscribe(data => {\n      this.applications = data;\n      this.allapplications = cloneDeep(data);\n      this.applications = this.service.changePage(this.allapplications);\n    });\n  }\n  // Pagination\n  changePage() {\n    this.applications = this.service.changePage(this.allapplications);\n  }\n  // Filter\n  statusFilter() {\n    if (this.status != '') {\n      this.applications = this.allapplications.filter(app => {\n        return app.status === this.status;\n      });\n    } else {\n      this.applications = this.service.changePage(this.allapplications);\n    }\n  }\n  typeFilter() {\n    if (this.type != '') {\n      this.applications = this.allapplications.filter(app => {\n        return app.type === this.type;\n      });\n    } else {\n      this.applications = this.service.changePage(this.allapplications);\n    }\n  }\n  // Search Data\n  performSearch() {\n    this.searchResults = this.allapplications.filter(item => {\n      return item.name.toLowerCase().includes(this.searchTerm.toLowerCase());\n    });\n    this.applications = this.service.changePage(this.searchResults);\n  }\n  onNavChange(changeEvent) {\n    if (changeEvent.nextId === 1) {\n      this.applications = this.service.changePage(this.allapplications);\n    }\n    if (changeEvent.nextId === 2) {\n      this.applications = this.allapplications.filter(app => app.status == 'New');\n    }\n    if (changeEvent.nextId === 3) {\n      this.applications = this.allapplications.filter(app => app.status == 'Pending');\n    }\n    if (changeEvent.nextId === 4) {\n      this.applications = this.allapplications.filter(app => app.status == 'Approved');\n    }\n    if (changeEvent.nextId === 5) {\n      this.applications = this.allapplications.filter(app => app.status == 'Rejected');\n    }\n  }\n  // The master checkbox will check/ uncheck all items\n  checkUncheckAll(ev) {\n    this.applications.forEach(x => x.state = ev.target.checked);\n    var checkedVal = [];\n    var result;\n    for (var i = 0; i < this.applications.length; i++) {\n      if (this.applications[i].state == true) {\n        result = this.applications[i].id;\n        checkedVal.push(result);\n      }\n    }\n    this.checkedValGet = checkedVal;\n    checkedVal.length > 0 ? document.getElementById(\"remove-actions\").style.display = \"block\" : document.getElementById(\"remove-actions\").style.display = \"none\";\n  }\n  isAllChecked() {\n    return this.applications.every(_ => _.state);\n  }\n  // Select Checkbox value Get\n  onCheckboxChange(e) {\n    var checkedVal = [];\n    var result;\n    for (var i = 0; i < this.applications.length; i++) {\n      if (this.applications[i].state == true) {\n        result = this.applications[i].id;\n        checkedVal.push(result);\n      }\n    }\n    this.checkedValGet = checkedVal;\n    checkedVal.length > 0 ? document.getElementById(\"remove-actions\").style.display = \"block\" : document.getElementById(\"remove-actions\").style.display = \"none\";\n  }\n  // Open add Model\n  openModel(content) {\n    this.modalService.open(content, {\n      size: 'md',\n      centered: true\n    });\n  }\n  fileChange(event) {\n    let fileList = event.target;\n    let file = fileList.files[0];\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.imageURL = reader.result;\n      document.querySelectorAll('#companylogo-img').forEach(element => {\n        element.src = this.imageURL;\n      });\n    };\n    reader.readAsDataURL(file);\n  }\n  editorder(content, id) {\n    this.singleData = this.applications[id];\n    this.submitted = false;\n    this.modalService.open(content, {\n      size: ' md',\n      centered: true\n    });\n    var modelTitle = document.querySelector('.modal-title');\n    modelTitle.innerHTML = 'Edit Application';\n    var updatebtn = document.getElementById('add-btn');\n    updatebtn.innerHTML = \"Update\";\n    document.querySelectorAll('#companylogo-img').forEach(element => {\n      element.src = this.singleData.img;\n    });\n    this.applicationData.controls['id'].setValue(this.singleData.id);\n    this.applicationData.controls['name'].setValue(this.singleData.name);\n    this.applicationData.controls['designation'].setValue(this.singleData.designation);\n    this.applicationData.controls['date'].setValue(this.singleData.date);\n    this.applicationData.controls['contacts'].setValue(this.singleData.contacts);\n    this.applicationData.controls['type'].setValue(this.singleData.type);\n    this.applicationData.controls['status'].setValue(this.singleData.status);\n  }\n  /**\n  * Returns form\n  */\n  get form() {\n    return this.applicationData.controls;\n  }\n  createapplication() {\n    if (this.applicationData.valid) {\n      if (this.applicationData.get('id')?.value) {\n        const updatedData = {\n          img: this.singleData.img,\n          ...this.applicationData.value\n        };\n        this.store.dispatch(updateApplication({\n          updatedData\n        }));\n        this.modalService.dismissAll();\n      } else {\n        const name = this.applicationData.get('name')?.value;\n        const designation = this.applicationData.get('designation')?.value;\n        const contacts = this.applicationData.get('contacts')?.value;\n        const img = \"/assets/images/brands/slack.png\";\n        const date = '26 Sep, 2022';\n        const status = this.applicationData.get('status')?.value;\n        const type = this.applicationData.get('type')?.value;\n        const newData = {\n          id: this.applications.length + 1,\n          img,\n          name,\n          designation,\n          date,\n          contacts,\n          type,\n          status\n        };\n        this.store.dispatch(addApplication({\n          newData\n        }));\n        this.modalService.dismissAll();\n      }\n    }\n    this.modalService.dismissAll();\n    setTimeout(() => {\n      this.applicationData.reset();\n    }, 2000);\n    this.submitted = true;\n  }\n  confirm(content, id) {\n    this.deleteId = id;\n    this.modalService.open(content, {\n      centered: true\n    });\n  }\n  // Delete Data\n  deleteData(id) {\n    if (id) {\n      document.getElementById('a_' + id)?.remove();\n    }\n    this.checkedValGet.forEach(item => {\n      document.getElementById('a_' + item)?.remove();\n      this.masterSelected = false;\n    });\n    this.modalService.dismissAll('close click');\n    let timerInterval;\n    Swal.fire({\n      title: 'Deleted!',\n      text: 'Your data has been deleted.',\n      icon: 'success',\n      confirmButtonColor: '#299cdb',\n      timer: 2000,\n      timerProgressBar: true,\n      willClose: () => {\n        clearInterval(timerInterval);\n      }\n    });\n  }\n  // Sort Data\n  onSort(column) {\n    this.applications = this.service.onSort(column, this.allapplications);\n  }\n  static {\n    this.ɵfac = function ApplicationComponent_Factory(t) {\n      return new (t || ApplicationComponent)(i0.ɵɵdirectiveInject(i1.PaginationService), i0.ɵɵdirectiveInject(i2.UntypedFormBuilder), i0.ɵɵdirectiveInject(i3.Store), i0.ɵɵdirectiveInject(i4.NgbModal));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApplicationComponent,\n      selectors: [[\"app-application\"]],\n      decls: 104,\n      vars: 16,\n      consts: [[\"nav\", \"ngbNav\"], [\"content\", \"\"], [\"deletemodal\", \"\"], [\"title\", \"Application\", 3, \"breadcrumbItems\"], [1, \"row\"], [1, \"col-lg-12\"], [\"id\", \"applicationList\", 1, \"card\"], [1, \"card-header\", \"border-0\"], [1, \"d-md-flex\", \"align-items-center\"], [1, \"card-title\", \"mb-3\", \"mb-md-0\", \"flex-grow-1\"], [1, \"flex-shrink-0\"], [1, \"d-flex\", \"gap-1\", \"flex-wrap\"], [\"type\", \"button\", \"data-bs-toggle\", \"modal\", \"id\", \"create-btn\", \"data-bs-target\", \"#showModal\", 1, \"btn\", \"btn-primary\", \"add-btn\", 3, \"click\"], [1, \"ri-add-line\", \"align-bottom\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\"], [1, \"ri-file-download-line\", \"align-bottom\", \"me-1\"], [\"id\", \"remove-actions\", 1, \"btn\", \"btn-soft-danger\", 3, \"click\"], [1, \"ri-delete-bin-2-line\"], [1, \"card-body\", \"border\", \"border-dashed\", \"border-end-0\", \"border-start-0\"], [1, \"row\", \"g-3\"], [1, \"col-xxl-5\", \"col-sm-6\"], [1, \"search-box\"], [\"type\", \"text\", \"placeholder\", \"Search for application ID, company, designation status or something...\", 1, \"form-control\", \"search\", 3, \"ngModelChange\", \"ngModel\"], [1, \"ri-search-line\", \"search-icon\"], [1, \"col-xxl-2\", \"col-sm-6\"], [\"type\", \"text\", \"mwlFlatpickr\", \"\", \"placeholder\", \"Select date\", \"mode\", \"range\", 1, \"form-control\", 3, \"ngModelChange\", \"convertModelValue\", \"ngModel\"], [1, \"col-xxl-2\", \"col-sm-4\"], [\"data-choices\", \"\", \"data-choices-search-false\", \"\", \"name\", \"choices-single-default\", \"id\", \"idStatus\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"\"], [\"value\", \"\", \"selected\", \"\"], [\"value\", \"Approved\"], [\"value\", \"New\"], [\"value\", \"Pending\"], [\"value\", \"Rejected\"], [\"data-choices\", \"\", \"data-choices-search-false\", \"\", \"name\", \"choices-single-default\", \"id\", \"idType\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"Full Time\"], [\"value\", \"Part Time\"], [1, \"col-xxl-1\", \"col-sm-4\"], [\"type\", \"button\", \"onclick\", \"filterData();\", 1, \"btn\", \"btn-warning\", \"w-100\"], [1, \"ri-equalizer-fill\", \"me-1\", \"align-bottom\"], [1, \"card-body\", \"pt-0\"], [\"ngbNav\", \"\", \"role\", \"tablist\", 1, \"nav\", \"nav-tabs\", \"nav-tabs-custom\", \"nav-success\", \"mb-3\", 3, \"navChange\", \"activeId\"], [1, \"nav-item\", 3, \"ngbNavItem\"], [\"ngbNavLink\", \"\", \"data-bs-toggle\", \"tab\", \"id\", \"All\", \"href\", \"javascript:void(0);\", \"role\", \"tab\", \"aria-selected\", \"true\", 1, \"nav-link\", \"All\", \"py-3\"], [\"ngbNavContent\", \"\"], [\"ngbNavLink\", \"\", \"data-bs-toggle\", \"tab\", \"id\", \"New\", \"href\", \"javascript:void(0);\", \"role\", \"tab\", \"aria-selected\", \"false\", 1, \"nav-link\", \"py-3\", \"New\"], [\"ngbNavLink\", \"\", \"data-bs-toggle\", \"tab\", \"id\", \"Pending\", \"href\", \"javascript:void(0);\", \"role\", \"tab\", \"aria-selected\", \"false\", 1, \"nav-link\", \"py-3\", \"Pending\"], [1, \"badge\", \"bg-danger\", \"align-middle\", \"ms-1\"], [\"ngbNavLink\", \"\", \"data-bs-toggle\", \"tab\", \"id\", \"Approved\", \"href\", \"javascript:void(0);\", \"role\", \"tab\", \"aria-selected\", \"false\", 1, \"nav-link\", \"py-3\", \"Approved\"], [\"ngbNavLink\", \"\", \"data-bs-toggle\", \"tab\", \"id\", \"Rejected\", \"href\", \"javascript:void(0);\", \"role\", \"tab\", \"aria-selected\", \"false\", 1, \"nav-link\", \"py-3\", \"Rejected\"], [1, \"table-responsive\", \"table-card\", \"mb-1\"], [1, \"noresult\", 2, \"display\", \"none\"], [1, \"text-center\"], [\"src\", \"https://cdn.lordicon.com/msoeawqm.json\", \"trigger\", \"loop\", \"colors\", \"primary:#405189,secondary:#0ab39c\", 2, \"width\", \"75px\", \"height\", \"75px\"], [1, \"mt-2\"], [1, \"text-muted\"], [3, \"ngbNavOutlet\"], [\"aria-label\", \"Custom pagination\", 1, \"d-flex\", \"justify-content-end\", \"pt-2\", 3, \"pageChange\", \"collectionSize\", \"page\", \"pageSize\"], [\"ngbPaginationPrevious\", \"\"], [\"ngbPaginationNext\", \"\"], [\"id\", \"elmLoader\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"avatar-sm\"], [1, \"visually-hidden\"], [\"role\", \"document\"], [\"id\", \"jobListTable\", 1, \"table\", \"table-nowrap\", \"align-middle\"], [1, \"text-muted\", \"table-light\"], [1, \"text-uppercase\"], [\"scope\", \"col\", 2, \"width\", \"25px\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"checkAll\", \"value\", \"option\", 1, \"form-check-input\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"sort\", 2, \"width\", \"140px\", 3, \"click\"], [1, \"sort\", 3, \"click\"], [1, \"sort\"], [1, \"list\", \"form-check-all\"], [3, \"id\"], [\"scope\", \"row\"], [\"type\", \"checkbox\", \"name\", \"checkAll\", 1, \"form-check-input\", 3, \"ngModelChange\", \"change\", \"value\", \"ngModel\"], [1, \"id\"], [\"href\", \"javascript:void(0);\", 1, \"fw-medium\", \"link-primary\"], [1, \"company\"], [1, \"d-flex\", \"align-items-center\"], [\"alt\", \"\", 1, \"avatar-xxs\", \"rounded-circle\", \"image_src\", \"object-fit-cover\", 3, \"src\"], [1, \"flex-grow-1\", \"ms-2\"], [1, \"designation\"], [1, \"date\"], [1, \"contacts\"], [1, \"type\"], [1, \"status\"], [1, \"badge\", \"text-uppercase\", 3, \"ngClass\"], [1, \"list-inline\", \"hstack\", \"gap-2\", \"mb-0\"], [\"data-bs-toggle\", \"tooltip\", \"data-bs-trigger\", \"hover\", \"data-bs-placement\", \"top\", \"ngbTooltip\", \"View\", 1, \"list-inline-item\"], [\"routerLink\", \"/job-lists/overview\", 1, \"text-primary\", \"d-inline-block\"], [1, \"ri-eye-fill\", \"fs-16\"], [\"data-bs-toggle\", \"tooltip\", \"data-bs-trigger\", \"hover\", \"data-bs-placement\", \"top\", \"ngbTooltip\", \"Edit\", 1, \"list-inline-item\", \"edit\", 3, \"click\"], [\"data-bs-toggle\", \"modal\", 1, \"text-primary\", \"d-inline-block\", \"edit-item-btn\"], [1, \"ri-pencil-fill\", \"fs-16\"], [\"data-bs-toggle\", \"tooltip\", \"data-bs-trigger\", \"hover\", \"data-bs-placement\", \"top\", \"ngbTooltip\", \"Remove\", 1, \"list-inline-item\", \"me-0\"], [\"data-bs-toggle\", \"modal\", 1, \"text-danger\", \"d-inline-block\", \"remove-item-btn\", 3, \"click\"], [1, \"ri-delete-bin-5-fill\", \"fs-16\"], [\"data-bs-toggle\", \"tooltip\", \"data-bs-trigger\", \"hover\", \"data-bs-placement\", \"top\", \"title\", \"View\", 1, \"list-inline-item\"], [1, \"ci-arrow-left\", \"me-2\"], [1, \"ci-arrow-right\", \"ms-2\"], [1, \"modal-header\", \"bg-light\", \"p-3\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"id\", \"close-modal\", 1, \"btn-close\", 3, \"click\"], [\"action\", \"#\", \"autocomplete\", \"off\", 3, \"ngSubmit\", \"formGroup\"], [1, \"modal-body\"], [\"id\", \"modal-id\", 1, \"mb-3\", \"d-none\"], [\"for\", \"applicationId\", 1, \"form-label\"], [\"type\", \"hidden\", \"id\", \"applicationId\", \"placeholder\", \"ID\", \"formControlName\", \"id\", \"readonly\", \"\", 1, \"form-control\"], [1, \"position-relative\", \"d-inline-block\"], [1, \"position-absolute\", \"bottom-0\", \"end-0\"], [\"for\", \"companylogo-image-input\", \"data-bs-toggle\", \"tooltip\", \"data-bs-placement\", \"right\", \"ngbTooltip\", \"Select Image\", 1, \"mb-0\"], [1, \"avatar-xs\", \"cursor-pointer\"], [1, \"avatar-title\", \"bg-light\", \"border\", \"rounded-circle\", \"text-muted\"], [1, \"ri-image-fill\"], [\"value\", \"\", \"id\", \"companylogo-image-input\", \"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"form-control\", \"d-none\", 3, \"change\"], [1, \"avatar-lg\", \"p-1\"], [1, \"avatar-title\", \"bg-light\", \"rounded-circle\"], [\"src\", \"assets/images/users/multi-user.jpg\", \"id\", \"companylogo-img\", 1, \"avatar-md\", \"h-auto\", \"rounded-circle\", \"object-fit-cover\"], [1, \"mb-3\"], [\"for\", \"company-field\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"company-field\", \"formControlName\", \"name\", \"placeholder\", \"Enter company name\", \"required\", \"\", 1, \"form-control\"], [\"for\", \"designation-field\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"designation-field\", \"formControlName\", \"designation\", \"placeholder\", \"Enter designation\", \"required\", \"\", 1, \"form-control\"], [\"for\", \"date-field\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"date-field\", \"mwlFlatpickr\", \"\", \"required\", \"\", \"formControlName\", \"date\", \"placeholder\", \"Select date\", 1, \"form-control\", 3, \"convertModelValue\"], [\"for\", \"contact-field\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"contact-field\", \"placeholder\", \"Enter contact\", \"formControlName\", \"contacts\", \"required\", \"\", 1, \"form-control\"], [1, \"row\", \"gy-4\", \"mb-3\"], [1, \"col-md-6\"], [\"for\", \"status-input\", 1, \"form-label\"], [\"data-trigger\", \"\", \"name\", \"status-input\", \"id\", \"status-input\", \"formControlName\", \"status\", 1, \"form-control\"], [\"for\", \"type-input\", 1, \"form-label\"], [\"data-trigger\", \"\", \"name\", \"type-input\", \"id\", \"type-input\", \"formControlName\", \"type\", 1, \"form-control\"], [1, \"modal-footer\"], [1, \"hstack\", \"gap-2\", \"justify-content-end\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-light\", 3, \"click\"], [\"type\", \"submit\", \"id\", \"add-btn\", 1, \"btn\", \"btn-success\"], [1, \"modal-body\", \"p-5\", \"text-center\"], [\"src\", \"https://cdn.lordicon.com/gsqxdxog.json\", \"trigger\", \"loop\", \"colors\", \"primary:#405189,secondary:#f06548\", 2, \"width\", \"90px\", \"height\", \"90px\"], [1, \"mt-4\", \"text-center\"], [1, \"text-muted\", \"fs-15\", \"mb-4\"], [1, \"hstack\", \"gap-2\", \"justify-content-center\", \"remove\"], [\"id\", \"deleteRecord-close\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-link\", \"link-success\", \"fw-medium\", \"text-decoration-none\", 3, \"click\"], [1, \"ri-close-line\", \"me-1\", \"align-middle\"], [\"id\", \"delete-record\", 1, \"btn\", \"btn-danger\", 3, \"click\"]],\n      template: function ApplicationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelement(0, \"app-breadcrumbs\", 3);\n          i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"div\", 7)(5, \"div\", 8)(6, \"h5\", 9);\n          i0.ɵɵtext(7, \"Job Application\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 10)(9, \"div\", 11)(10, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function ApplicationComponent_Template_button_click_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const content_r2 = i0.ɵɵreference(101);\n            return i0.ɵɵresetView(ctx.openModel(content_r2));\n          });\n          i0.ɵɵelement(11, \"i\", 13);\n          i0.ɵɵtext(12, \" Create Application\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 14);\n          i0.ɵɵelement(14, \"i\", 15);\n          i0.ɵɵtext(15, \" Import\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function ApplicationComponent_Template_button_click_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const deletemodal_r3 = i0.ɵɵreference(103);\n            return i0.ɵɵresetView(ctx.confirm(deletemodal_r3, \"\"));\n          });\n          i0.ɵɵelement(17, \"i\", 17);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(18, \"div\", 18)(19, \"div\", 19)(20, \"div\", 20)(21, \"div\", 21)(22, \"input\", 22);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_Template_input_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function ApplicationComponent_Template_input_ngModelChange_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.performSearch());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(23, \"i\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 24)(25, \"div\")(26, \"input\", 25);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_Template_input_ngModelChange_26_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.date, $event) || (ctx.date = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 26)(28, \"div\")(29, \"select\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_Template_select_ngModelChange_29_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.status, $event) || (ctx.status = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function ApplicationComponent_Template_select_ngModelChange_29_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.statusFilter());\n          });\n          i0.ɵɵelementStart(30, \"option\", 28);\n          i0.ɵɵtext(31, \"Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"option\", 29);\n          i0.ɵɵtext(33, \"All\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"option\", 30);\n          i0.ɵɵtext(35, \"Approved\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"option\", 31);\n          i0.ɵɵtext(37, \"New\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"option\", 32);\n          i0.ɵɵtext(39, \"Pending\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"option\", 33);\n          i0.ɵɵtext(41, \"Rejected\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(42, \"div\", 26)(43, \"div\")(44, \"select\", 34);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ApplicationComponent_Template_select_ngModelChange_44_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(45, \"option\", 28);\n          i0.ɵɵtext(46, \"Select Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"option\", 29);\n          i0.ɵɵtext(48, \"All\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"option\", 35);\n          i0.ɵɵtext(50, \"Full Time\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"option\", 36);\n          i0.ɵɵtext(52, \"Part Time\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(53, \"div\", 37)(54, \"div\")(55, \"button\", 38);\n          i0.ɵɵelement(56, \"i\", 39);\n          i0.ɵɵtext(57, \" Filters \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(58, \"div\", 40)(59, \"div\")(60, \"ul\", 41, 0);\n          i0.ɵɵlistener(\"navChange\", function ApplicationComponent_Template_ul_navChange_60_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNavChange($event));\n          });\n          i0.ɵɵelementStart(62, \"li\", 42)(63, \"a\", 43);\n          i0.ɵɵtext(64, \" All Application \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(65, ApplicationComponent_ng_template_65_Template, 25, 1, \"ng-template\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"li\", 42)(67, \"a\", 45);\n          i0.ɵɵtext(68, \" New \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(69, ApplicationComponent_ng_template_69_Template, 25, 1, \"ng-template\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"li\", 42)(71, \"a\", 46);\n          i0.ɵɵtext(72, \" Pending \");\n          i0.ɵɵelementStart(73, \"span\", 47);\n          i0.ɵɵtext(74, \"2\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(75, ApplicationComponent_ng_template_75_Template, 25, 1, \"ng-template\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"li\", 42)(77, \"a\", 48);\n          i0.ɵɵtext(78, \" Approved \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(79, ApplicationComponent_ng_template_79_Template, 25, 1, \"ng-template\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"li\", 42)(81, \"a\", 49);\n          i0.ɵɵtext(82, \" Rejected \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(83, ApplicationComponent_ng_template_83_Template, 25, 1, \"ng-template\", 44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(84, \"div\", 50)(85, \"div\", 51)(86, \"div\", 52);\n          i0.ɵɵelement(87, \"lord-icon\", 53);\n          i0.ɵɵelementStart(88, \"h5\", 54);\n          i0.ɵɵtext(89, \"Sorry! No Result Found\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"p\", 55);\n          i0.ɵɵtext(91, \"We've searched more than 150+ result We did not find jobs for you search.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(92, \"div\", 56);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"ngb-pagination\", 57);\n          i0.ɵɵtwoWayListener(\"pageChange\", function ApplicationComponent_Template_ngb_pagination_pageChange_93_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.service.page, $event) || (ctx.service.page = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function ApplicationComponent_Template_ngb_pagination_pageChange_93_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.changePage());\n          });\n          i0.ɵɵtemplate(94, ApplicationComponent_ng_template_94_Template, 2, 0, \"ng-template\", 58)(95, ApplicationComponent_ng_template_95_Template, 2, 0, \"ng-template\", 59);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"div\", 60)(97, \"div\", 61)(98, \"span\", 62);\n          i0.ɵɵtext(99, \"Loading...\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(100, ApplicationComponent_ng_template_100_Template, 70, 2, \"ng-template\", 63, 1, i0.ɵɵtemplateRefExtractor)(102, ApplicationComponent_ng_template_102_Template, 13, 0, \"ng-template\", 63, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const nav_r29 = i0.ɵɵreference(61);\n          i0.ɵɵproperty(\"breadcrumbItems\", ctx.breadCrumbItems);\n          i0.ɵɵadvance(22);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"convertModelValue\", true);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.date);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.status);\n          i0.ɵɵadvance(15);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"activeId\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngbNavItem\", 1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavItem\", 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavItem\", 3);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngbNavItem\", 4);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavItem\", 5);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngbNavOutlet\", nav_r29);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"collectionSize\", ctx.allapplications == null ? null : ctx.allapplications.length);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.service.page);\n          i0.ɵɵproperty(\"pageSize\", ctx.service.pageSize);\n        }\n      },\n      dependencies: [i5.NgClass, i6.RouterLink, i7.BreadcrumbsComponent, i4.NgbTooltip, i2.ɵNgNoValidate, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.NgModel, i2.FormGroupDirective, i2.FormControlName, i8.FlatpickrDirective, i4.NgbPagination, i4.NgbPaginationNext, i4.NgbPaginationPrevious, i4.NgbNavContent, i4.NgbNav, i4.NgbNavItem, i4.NgbNavItemRole, i4.NgbNavLink, i4.NgbNavLinkBase, i4.NgbNavOutlet],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "addApplication", "fetchApplicationData", "updateApplication", "selectJobsData", "selectJobsLoading", "cloneDeep", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "ApplicationComponent_ng_template_65_For_24_Template_input_ngModelChange_3_listener", "$event", "data_r7", "ɵɵrestoreView", "_r6", "$implicit", "ɵɵtwoWayBindingSet", "state", "ɵɵresetView", "ɵɵlistener", "ApplicationComponent_ng_template_65_For_24_Template_input_change_3_listener", "ctx_r4", "ɵɵnextContext", "onCheckboxChange", "ɵɵelementEnd", "ɵɵtext", "ɵɵelement", "ApplicationComponent_ng_template_65_For_24_Template_li_click_29_listener", "$index_r8", "$index", "content_r2", "ɵɵreference", "editorder", "ApplicationComponent_ng_template_65_For_24_Template_a_click_33_listener", "deletemodal_r3", "confirm", "id", "ɵɵpropertyInterpolate1", "ɵɵadvance", "ɵɵpropertyInterpolate", "ɵɵtwoWayProperty", "ɵɵtextInterpolate1", "img", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "name", "designation", "date", "contacts", "type", "ɵɵproperty", "ɵɵpureFunction4", "_c0", "status", "ApplicationComponent_ng_template_65_Template_input_ngModelChange_5_listener", "_r4", "masterSelected", "ApplicationComponent_ng_template_65_Template_input_change_5_listener", "checkUncheckAll", "ApplicationComponent_ng_template_65_Template_th_click_6_listener", "onSort", "ApplicationComponent_ng_template_65_Template_th_click_8_listener", "ApplicationComponent_ng_template_65_Template_th_click_10_listener", "ApplicationComponent_ng_template_65_Template_th_click_12_listener", "ApplicationComponent_ng_template_65_Template_th_click_14_listener", "ApplicationComponent_ng_template_65_Template_th_click_16_listener", "ApplicationComponent_ng_template_65_Template_th_click_18_listener", "ɵɵrepeaterCreate", "ApplicationComponent_ng_template_65_For_24_Template", "ɵɵrepeaterTrackByIndex", "ɵɵrepeater", "applications", "ApplicationComponent_ng_template_69_For_24_Template_input_ngModelChange_3_listener", "data_r11", "_r10", "ApplicationComponent_ng_template_69_For_24_Template_input_change_3_listener", "ApplicationComponent_ng_template_69_For_24_Template_li_click_29_listener", "$index_r12", "ApplicationComponent_ng_template_69_For_24_Template_a_click_33_listener", "ApplicationComponent_ng_template_69_Template_input_ngModelChange_5_listener", "_r9", "ApplicationComponent_ng_template_69_Template_input_change_5_listener", "ApplicationComponent_ng_template_69_Template_th_click_6_listener", "ApplicationComponent_ng_template_69_Template_th_click_8_listener", "ApplicationComponent_ng_template_69_Template_th_click_10_listener", "ApplicationComponent_ng_template_69_Template_th_click_12_listener", "ApplicationComponent_ng_template_69_Template_th_click_14_listener", "ApplicationComponent_ng_template_69_Template_th_click_16_listener", "ApplicationComponent_ng_template_69_Template_th_click_18_listener", "ApplicationComponent_ng_template_69_For_24_Template", "ApplicationComponent_ng_template_75_For_24_Template_input_ngModelChange_3_listener", "data_r15", "_r14", "ApplicationComponent_ng_template_75_For_24_Template_input_change_3_listener", "ApplicationComponent_ng_template_75_For_24_Template_li_click_29_listener", "$index_r16", "ApplicationComponent_ng_template_75_For_24_Template_a_click_33_listener", "ApplicationComponent_ng_template_75_Template_input_ngModelChange_5_listener", "_r13", "ApplicationComponent_ng_template_75_Template_input_change_5_listener", "ApplicationComponent_ng_template_75_Template_th_click_6_listener", "ApplicationComponent_ng_template_75_Template_th_click_8_listener", "ApplicationComponent_ng_template_75_Template_th_click_10_listener", "ApplicationComponent_ng_template_75_Template_th_click_12_listener", "ApplicationComponent_ng_template_75_Template_th_click_14_listener", "ApplicationComponent_ng_template_75_Template_th_click_16_listener", "ApplicationComponent_ng_template_75_Template_th_click_18_listener", "ApplicationComponent_ng_template_75_For_24_Template", "ApplicationComponent_ng_template_79_For_24_Template_input_ngModelChange_3_listener", "data_r19", "_r18", "ApplicationComponent_ng_template_79_For_24_Template_input_change_3_listener", "ApplicationComponent_ng_template_79_For_24_Template_li_click_29_listener", "$index_r20", "ApplicationComponent_ng_template_79_For_24_Template_a_click_33_listener", "ApplicationComponent_ng_template_79_Template_input_ngModelChange_5_listener", "_r17", "ApplicationComponent_ng_template_79_Template_input_change_5_listener", "ApplicationComponent_ng_template_79_Template_th_click_6_listener", "ApplicationComponent_ng_template_79_Template_th_click_8_listener", "ApplicationComponent_ng_template_79_Template_th_click_10_listener", "ApplicationComponent_ng_template_79_Template_th_click_12_listener", "ApplicationComponent_ng_template_79_Template_th_click_14_listener", "ApplicationComponent_ng_template_79_Template_th_click_16_listener", "ApplicationComponent_ng_template_79_Template_th_click_18_listener", "ApplicationComponent_ng_template_79_For_24_Template", "ApplicationComponent_ng_template_83_For_24_Template_input_ngModelChange_3_listener", "data_r23", "_r22", "ApplicationComponent_ng_template_83_For_24_Template_input_change_3_listener", "ApplicationComponent_ng_template_83_For_24_Template_li_click_29_listener", "$index_r24", "ApplicationComponent_ng_template_83_For_24_Template_a_click_33_listener", "ApplicationComponent_ng_template_83_Template_input_ngModelChange_5_listener", "_r21", "ApplicationComponent_ng_template_83_Template_input_change_5_listener", "ApplicationComponent_ng_template_83_Template_th_click_6_listener", "ApplicationComponent_ng_template_83_Template_th_click_8_listener", "ApplicationComponent_ng_template_83_Template_th_click_10_listener", "ApplicationComponent_ng_template_83_Template_th_click_12_listener", "ApplicationComponent_ng_template_83_Template_th_click_14_listener", "ApplicationComponent_ng_template_83_Template_th_click_16_listener", "ApplicationComponent_ng_template_83_Template_th_click_18_listener", "ApplicationComponent_ng_template_83_For_24_Template", "ApplicationComponent_ng_template_100_Template_button_click_3_listener", "modal_r26", "_r25", "dismiss", "ApplicationComponent_ng_template_100_Template_form_ngSubmit_4_listener", "createapplication", "ApplicationComponent_ng_template_100_Template_input_change_17_listener", "fileChange", "ApplicationComponent_ng_template_100_Template_button_click_66_listener", "applicationData", "ApplicationComponent_ng_template_102_Template_button_click_8_listener", "modal_r28", "_r27", "ApplicationComponent_ng_template_102_Template_button_click_11_listener", "deleteData", "deleteId", "ApplicationComponent", "constructor", "service", "formBuilder", "store", "modalService", "submitted", "checkedValGet", "ngOnInit", "breadCrumbItems", "label", "active", "group", "required", "dispatch", "select", "subscribe", "data", "document", "getElementById", "classList", "add", "allapplications", "changePage", "statusFilter", "filter", "app", "typeFilter", "performSearch", "searchResults", "item", "toLowerCase", "includes", "searchTerm", "onNavChange", "changeEvent", "nextId", "ev", "for<PERSON>ach", "x", "target", "checked", "checkedVal", "result", "i", "length", "push", "style", "display", "isAllChecked", "every", "_", "e", "openModel", "content", "open", "size", "centered", "event", "fileList", "file", "files", "reader", "FileReader", "onload", "imageURL", "querySelectorAll", "element", "src", "readAsDataURL", "singleData", "modelTitle", "querySelector", "innerHTML", "updatebtn", "controls", "setValue", "form", "valid", "get", "value", "updatedData", "dismissAll", "newData", "setTimeout", "reset", "remove", "timerInterval", "fire", "title", "text", "icon", "confirmButtonColor", "timer", "timerP<PERSON>ressBar", "willClose", "clearInterval", "column", "ɵɵdirectiveInject", "i1", "PaginationService", "i2", "UntypedFormBuilder", "i3", "Store", "i4", "NgbModal", "selectors", "decls", "vars", "consts", "template", "ApplicationComponent_Template", "rf", "ctx", "ApplicationComponent_Template_button_click_10_listener", "_r1", "ApplicationComponent_Template_button_click_16_listener", "ApplicationComponent_Template_input_ngModelChange_22_listener", "ApplicationComponent_Template_input_ngModelChange_26_listener", "ApplicationComponent_Template_select_ngModelChange_29_listener", "ApplicationComponent_Template_select_ngModelChange_44_listener", "ApplicationComponent_Template_ul_navChange_60_listener", "ɵɵtemplate", "ApplicationComponent_ng_template_65_Template", "ApplicationComponent_ng_template_69_Template", "ApplicationComponent_ng_template_75_Template", "ApplicationComponent_ng_template_79_Template", "ApplicationComponent_ng_template_83_Template", "ApplicationComponent_Template_ngb_pagination_pageChange_93_listener", "page", "ApplicationComponent_ng_template_94_Template", "ApplicationComponent_ng_template_95_Template", "ApplicationComponent_ng_template_100_Template", "ɵɵtemplateRefExtractor", "ApplicationComponent_ng_template_102_Template", "nav_r29", "pageSize"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\application\\application.component.ts", "C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\application\\application.component.html"], "sourcesContent": ["import { DecimalPipe } from '@angular/common';\r\nimport { Component, OnInit, QueryList, ViewChildren } from '@angular/core';\r\nimport { NgbModal, NgbNavChangeEvent } from '@ng-bootstrap/ng-bootstrap';\r\nimport { Observable } from 'rxjs';\r\nimport { UntypedFormBuilder, Validators, UntypedFormGroup, UntypedFormArray, AbstractControl } from '@angular/forms';\r\n// Sweet Alert\r\nimport Swal from 'sweetalert2';\r\nimport { RootReducerState } from 'src/app/store';\r\nimport { Store } from '@ngrx/store';\r\nimport { addApplication, deleteApplication, fetchApplicationData, updateApplication } from 'src/app/store/Jobs/jobs_action';\r\nimport { selectJobsData, selectJobsLoading } from 'src/app/store/Jobs/jobs_selector';\r\nimport { cloneDeep } from 'lodash';\r\nimport { PaginationService } from 'src/app/core/services/pagination.service';\r\n\r\n@Component({\r\n  selector: 'app-application',\r\n  templateUrl: './application.component.html',\r\n  styleUrls: ['./application.component.scss']\r\n})\r\nexport class ApplicationComponent implements OnInit {\r\n\r\n\r\n  // bread crumb items\r\n  breadCrumbItems!: Array<{}>;\r\n  applications: any;\r\n  masterSelected!: boolean;\r\n  // Form\r\n  applicationData!: UntypedFormGroup;\r\n  submitted = false;\r\n  allapplications: any;\r\n  searchTerm: any;\r\n  searchResults: any;\r\n  date: any;\r\n  status: any = '';\r\n  type: any = '';\r\n\r\n  constructor(public service: PaginationService,\r\n    public formBuilder: UntypedFormBuilder,\r\n    private store: Store<{ data: RootReducerState }>,\r\n    public modalService: NgbModal) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    /**\r\n* BreadCrumb\r\n*/\r\n    this.breadCrumbItems = [\r\n      { label: 'Jobs' },\r\n      { label: 'Application', active: true }\r\n    ];\r\n\r\n    // Validation\r\n    this.applicationData = this.formBuilder.group({\r\n      id: [''],\r\n      name: ['', [Validators.required]],\r\n      date: ['', [Validators.required]],\r\n      type: ['', [Validators.required]],\r\n      designation: ['', [Validators.required]],\r\n      contacts: ['', [Validators.required]],\r\n      status: ['', [Validators.required]]\r\n    });\r\n\r\n    this.store.dispatch(fetchApplicationData());\r\n    this.store.select(selectJobsLoading).subscribe((data) => {\r\n      if (data == false) {\r\n        document.getElementById('elmLoader')?.classList.add('d-none');\r\n      }\r\n    });\r\n\r\n    this.store.select(selectJobsData).subscribe((data) => {\r\n      this.applications = data;\r\n      this.allapplications = cloneDeep(data);\r\n      this.applications = this.service.changePage(this.allapplications)\r\n    });\r\n  }\r\n\r\n  // Pagination\r\n  changePage() {\r\n    this.applications = this.service.changePage(this.allapplications)\r\n  }\r\n\r\n  // Filter\r\n  statusFilter() {\r\n    if (this.status != '') {\r\n      this.applications = this.allapplications.filter((app: any) => {\r\n        return app.status === this.status;\r\n      });\r\n    } else {\r\n      this.applications = this.service.changePage(this.allapplications)\r\n    }\r\n  }\r\n\r\n  typeFilter() {\r\n    if (this.type != '') {\r\n      this.applications = this.allapplications.filter((app: any) => {\r\n        return app.type === this.type;\r\n      });\r\n    } else {\r\n      this.applications = this.service.changePage(this.allapplications)\r\n    }\r\n  }\r\n\r\n  // Search Data\r\n  performSearch(): void {\r\n    this.searchResults = this.allapplications.filter((item: any) => {\r\n      return (\r\n        item.name.toLowerCase().includes(this.searchTerm.toLowerCase())\r\n      );\r\n    });\r\n    this.applications = this.service.changePage(this.searchResults)\r\n  }\r\n\r\n  onNavChange(changeEvent: NgbNavChangeEvent) {\r\n    if (changeEvent.nextId === 1) {\r\n      this.applications = this.service.changePage(this.allapplications)\r\n    }\r\n    if (changeEvent.nextId === 2) {\r\n      this.applications = this.allapplications.filter((app: any) => app.status == 'New');\r\n    }\r\n    if (changeEvent.nextId === 3) {\r\n      this.applications = this.allapplications.filter((app: any) => app.status == 'Pending');\r\n    }\r\n    if (changeEvent.nextId === 4) {\r\n      this.applications = this.allapplications.filter((app: any) => app.status == 'Approved');\r\n    }\r\n    if (changeEvent.nextId === 5) {\r\n      this.applications = this.allapplications.filter((app: any) => app.status == 'Rejected');\r\n    }\r\n  }\r\n\r\n  // Check Box Checked Value Get\r\n  checkedValGet: any[] = [];\r\n  // The master checkbox will check/ uncheck all items\r\n  checkUncheckAll(ev: any) {\r\n    this.applications.forEach((x: { state: any; }) => x.state = ev.target.checked)\r\n    var checkedVal: any[] = [];\r\n    var result\r\n    for (var i = 0; i < this.applications.length; i++) {\r\n      if (this.applications[i].state == true) {\r\n        result = this.applications[i].id;\r\n        checkedVal.push(result);\r\n      }\r\n    }\r\n    this.checkedValGet = checkedVal\r\n    checkedVal.length > 0 ? (document.getElementById(\"remove-actions\") as HTMLElement).style.display = \"block\" : (document.getElementById(\"remove-actions\") as HTMLElement).style.display = \"none\";\r\n\r\n  }\r\n  isAllChecked() {\r\n    return this.applications.every((_: { state: any; }) => _.state);\r\n  }\r\n\r\n  // Select Checkbox value Get\r\n  onCheckboxChange(e: any) {\r\n    var checkedVal: any[] = [];\r\n    var result\r\n    for (var i = 0; i < this.applications.length; i++) {\r\n      if (this.applications[i].state == true) {\r\n        result = this.applications[i].id;\r\n        checkedVal.push(result);\r\n      }\r\n    }\r\n    this.checkedValGet = checkedVal\r\n    checkedVal.length > 0 ? (document.getElementById(\"remove-actions\") as HTMLElement).style.display = \"block\" : (document.getElementById(\"remove-actions\") as HTMLElement).style.display = \"none\";\r\n  }\r\n\r\n  // Open add Model\r\n  openModel(content: any) {\r\n    this.modalService.open(content, { size: 'md', centered: true });\r\n  }\r\n\r\n  // File Upload\r\n  imageURL: string | undefined;\r\n  fileChange(event: any) {\r\n    let fileList: any = (event.target as HTMLInputElement);\r\n    let file: File = fileList.files[0];\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      this.imageURL = reader.result as string;\r\n      document.querySelectorAll('#companylogo-img').forEach((element: any) => {\r\n        element.src = this.imageURL;\r\n      });\r\n    }\r\n    reader.readAsDataURL(file)\r\n  }\r\n\r\n  /**\r\n  * Open modal\r\n  * @param content modal content\r\n  */\r\n  singleData: any;\r\n  editorder(content: any, id: any) {\r\n    this.singleData = this.applications[id];\r\n    this.submitted = false;\r\n    this.modalService.open(content, { size: ' md', centered: true })\r\n    var modelTitle = document.querySelector('.modal-title') as HTMLAreaElement;\r\n    modelTitle.innerHTML = 'Edit Application';\r\n    var updatebtn = document.getElementById('add-btn') as HTMLAreaElement\r\n    updatebtn.innerHTML = \"Update\";\r\n    document.querySelectorAll('#companylogo-img').forEach((element: any) => {\r\n      element.src = this.singleData.img;\r\n    });\r\n    this.applicationData.controls['id'].setValue(this.singleData.id);\r\n    this.applicationData.controls['name'].setValue(this.singleData.name);\r\n    this.applicationData.controls['designation'].setValue(this.singleData.designation);\r\n    this.applicationData.controls['date'].setValue(this.singleData.date);\r\n    this.applicationData.controls['contacts'].setValue(this.singleData.contacts);\r\n    this.applicationData.controls['type'].setValue(this.singleData.type);\r\n    this.applicationData.controls['status'].setValue(this.singleData.status);\r\n  }\r\n\r\n  /**\r\n* Returns form\r\n*/\r\n  get form() {\r\n    return this.applicationData.controls;\r\n  }\r\n\r\n  createapplication() {\r\n    if (this.applicationData.valid) {\r\n      if (this.applicationData.get('id')?.value) {\r\n        const updatedData = { img: this.singleData.img, ...this.applicationData.value };\r\n        this.store.dispatch(updateApplication({ updatedData }));\r\n        this.modalService.dismissAll();\r\n      } else {\r\n        const name = this.applicationData.get('name')?.value;\r\n        const designation = this.applicationData.get('designation')?.value;\r\n        const contacts = this.applicationData.get('contacts')?.value;\r\n        const img = \"/assets/images/brands/slack.png\";\r\n        const date = '26 Sep, 2022';\r\n        const status = this.applicationData.get('status')?.value;\r\n        const type = this.applicationData.get('type')?.value;\r\n        const newData = {\r\n          id: this.applications.length + 1,\r\n          img,\r\n          name,\r\n          designation,\r\n          date,\r\n          contacts,\r\n          type,\r\n          status\r\n        };\r\n        this.store.dispatch(addApplication({ newData }));\r\n        this.modalService.dismissAll()\r\n      }\r\n    }\r\n    this.modalService.dismissAll();\r\n    setTimeout(() => {\r\n      this.applicationData.reset();\r\n    }, 2000);\r\n    this.submitted = true\r\n  }\r\n\r\n  /**\r\n   * Delete Model Open\r\n   */\r\n  deleteId: any;\r\n  confirm(content: any, id: any) {\r\n    this.deleteId = id;\r\n    this.modalService.open(content, { centered: true });\r\n  }\r\n\r\n  // Delete Data\r\n  deleteData(id: any) {\r\n    if (id) {\r\n      document.getElementById('a_' + id)?.remove();\r\n    }\r\n    this.checkedValGet.forEach((item: any) => {\r\n      document.getElementById('a_' + item)?.remove();\r\n      this.masterSelected = false;\r\n    });\r\n    this.modalService.dismissAll('close click')\r\n    let timerInterval: any;\r\n    Swal.fire({\r\n      title: 'Deleted!',\r\n      text: 'Your data has been deleted.',\r\n      icon: 'success',\r\n      confirmButtonColor: '#299cdb',\r\n      timer: 2000,\r\n      timerProgressBar: true,\r\n      willClose: () => {\r\n        clearInterval(timerInterval);\r\n      },\r\n    });\r\n  }\r\n\r\n  // Sort Data\r\n  onSort(column: any) {\r\n    this.applications = this.service.onSort(column, this.allapplications)\r\n  }\r\n}", "<!-- Start Breadcrumbs -->\r\n<app-breadcrumbs title=\"Application\" [breadcrumbItems]=\"breadCrumbItems\"></app-breadcrumbs>\r\n<!-- End Breadcrumbs -->\r\n<div class=\"row\">\r\n    <div class=\"col-lg-12\">\r\n        <div class=\"card\" id=\"applicationList\">\r\n            <div class=\"card-header  border-0\">\r\n                <div class=\"d-md-flex align-items-center\">\r\n                    <h5 class=\"card-title mb-3 mb-md-0 flex-grow-1\">Job Application</h5>\r\n                    <div class=\"flex-shrink-0\">\r\n                        <div class=\"d-flex gap-1 flex-wrap\">\r\n                            <button type=\"button\" class=\"btn btn-primary add-btn\" data-bs-toggle=\"modal\" id=\"create-btn\" data-bs-target=\"#showModal\" (click)=\"openModel(content)\"><i class=\"ri-add-line align-bottom me-1\"></i> Create\r\n                                Application</button>\r\n                            <button type=\"button\" class=\"btn btn-success\"><i class=\"ri-file-download-line align-bottom me-1\"></i> Import</button>\r\n                            <button class=\"btn btn-soft-danger\" id=\"remove-actions\" (click)=\"confirm(deletemodal,'')\"><i class=\"ri-delete-bin-2-line\"></i></button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"card-body border border-dashed border-end-0 border-start-0\">\r\n                <!-- <form> -->\r\n                <div class=\"row g-3\">\r\n                    <div class=\"col-xxl-5 col-sm-6\">\r\n                        <div class=\"search-box\">\r\n                            <input type=\"text\" class=\"form-control search\" placeholder=\"Search for application ID, company, designation status or something...\" [(ngModel)]=\"searchTerm\" (ngModelChange)=\"performSearch()\">\r\n                            <i class=\"ri-search-line search-icon\"></i>\r\n                        </div>\r\n                    </div>\r\n                    <!--end col-->\r\n                    <div class=\"col-xxl-2 col-sm-6\">\r\n                        <div>\r\n                            <input type=\"text\" class=\"form-control\" mwlFlatpickr [convertModelValue]=\"true\" placeholder=\"Select date\" [(ngModel)]=\"date\" mode=\"range\">\r\n                        </div>\r\n                    </div>\r\n                    <!--end col-->\r\n                    <div class=\"col-xxl-2 col-sm-4\">\r\n                        <div>\r\n                            <select class=\"form-control\" data-choices data-choices-search-false name=\"choices-single-default\" id=\"idStatus\" [(ngModel)]=\"status\" (ngModelChange)=\"statusFilter()\">\r\n                                <option value=\"\">Status</option>\r\n                                <option value=\"\" selected>All</option>\r\n                                <option value=\"Approved\">Approved</option>\r\n                                <option value=\"New\">New</option>\r\n                                <option value=\"Pending\">Pending</option>\r\n                                <option value=\"Rejected\">Rejected</option>\r\n                            </select>\r\n                        </div>\r\n                    </div>\r\n                    <!--end col-->\r\n                    <div class=\"col-xxl-2 col-sm-4\">\r\n                        <div>\r\n                            <select class=\"form-control\" data-choices data-choices-search-false name=\"choices-single-default\" id=\"idType\" [(ngModel)]=\"searchTerm\">\r\n                                <option value=\"\">Select Type</option>\r\n                                <option value=\"\" selected>All</option>\r\n                                <option value=\"Full Time\">Full Time</option>\r\n                                <option value=\"Part Time\">Part Time</option>\r\n                            </select>\r\n                        </div>\r\n                    </div>\r\n                    <!--end col-->\r\n                    <div class=\"col-xxl-1 col-sm-4\">\r\n                        <div>\r\n                            <button type=\"button\" class=\"btn btn-warning w-100\" onclick=\"filterData();\"> <i class=\"ri-equalizer-fill me-1 align-bottom\"></i>\r\n                                Filters\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                    <!--end col-->\r\n                </div>\r\n                <!--end row-->\r\n                <!-- </form> -->\r\n            </div>\r\n            <div class=\"card-body pt-0\">\r\n                <div>\r\n                    <ul ngbNav #nav=\"ngbNav\" [activeId]=\"1\" (navChange)=\"onNavChange($event)\" class=\"nav nav-tabs nav-tabs-custom nav-success mb-3\" role=\"tablist\">\r\n                        <li [ngbNavItem]=\"1\" class=\"nav-item\">\r\n                            <a ngbNavLink class=\"nav-link All py-3\" data-bs-toggle=\"tab\" id=\"All\" href=\"javascript:void(0);\" role=\"tab\" aria-selected=\"true\">\r\n                                All Application\r\n                            </a>\r\n                            <ng-template ngbNavContent>\r\n                                <table class=\"table table-nowrap align-middle\" id=\"jobListTable\">\r\n                                    <thead class=\"text-muted table-light\">\r\n                                        <tr class=\"text-uppercase\">\r\n                                            <th scope=\"col\" style=\"width: 25px;\">\r\n                                                <div class=\"form-check\">\r\n                                                    <input class=\"form-check-input\" type=\"checkbox\" id=\"checkAll\" value=\"option\" [(ngModel)]=\"masterSelected\" (change)=\"checkUncheckAll($event)\">\r\n                                                </div>\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('id')\" style=\"width: 140px;\">Application ID</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('name')\">Company Name\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('designation')\">\r\n                                                Designation</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('date')\">Apply Date\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('contacts')\">Contacts\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('type')\">Type</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('status')\">Status</th>\r\n                                            <th class=\"sort\">Action</th>\r\n                                        </tr>\r\n                                    </thead>\r\n                                    <tbody class=\"list form-check-all\">\r\n                                        @for(data of applications;track $index){\r\n                                        <tr id=\"a_{{data.id}}\">\r\n                                            <th scope=\"row\">\r\n                                                <div class=\"form-check\">\r\n                                                    <input class=\"form-check-input\" type=\"checkbox\" name=\"checkAll\" value=\"{{data.id}}\" [(ngModel)]=\"data.state\" (change)=\"onCheckboxChange($event)\">\r\n                                                </div>\r\n                                            </th>\r\n                                            <td class=\"id\"><a href=\"javascript:void(0);\" class=\"fw-medium link-primary\">#VZ{{data.id}}</a>\r\n                                            </td>\r\n                                            <td class=\"company\">\r\n                                                <div class=\"d-flex align-items-center\">\r\n                                                    <div class=\"flex-shrink-0\">\r\n                                                        <img src=\"{{data.img}}\" alt=\"\" class=\"avatar-xxs rounded-circle image_src object-fit-cover\">\r\n                                                    </div>\r\n                                                    <div class=\"flex-grow-1 ms-2\">{{data.name}}</div>\r\n                                                </div>\r\n                                            </td>\r\n                                            <td class=\"designation\">{{data.designation}}</td>\r\n                                            <td class=\"date\">{{data.date}}</td>\r\n                                            <td class=\"contacts\">{{data.contacts}}</td>\r\n                                            <td class=\"type\">{{data.type}}</td>\r\n                                            <td class=\"status\">\r\n                                                <span class=\"badge text-uppercase\" [ngClass]=\"{'bg-danger-subtle text-danger': data.status == 'Rejected','bg-info-subtle text-info': data.status == 'New',\r\n                                                    'bg-warning-subtle text-warning': data.status == 'Pending','bg-success-subtle text-success': data.status == 'Approved'}\">\r\n                                                    {{data.status}}</span>\r\n\r\n                                            </td>\r\n                                            <td>\r\n                                                <ul class=\"list-inline hstack gap-2 mb-0\">\r\n                                                    <li class=\"list-inline-item\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" ngbTooltip=\"View\">\r\n                                                        <a routerLink=\"/job-lists/overview\" class=\"text-primary d-inline-block\">\r\n                                                            <i class=\"ri-eye-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                    <li class=\"list-inline-item edit\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" ngbTooltip=\"Edit\" (click)=\"editorder(content,$index)\">\r\n                                                        <a data-bs-toggle=\"modal\" class=\"text-primary d-inline-block edit-item-btn\">\r\n                                                            <i class=\"ri-pencil-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                    <li class=\"list-inline-item me-0\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" ngbTooltip=\"Remove\">\r\n                                                        <a class=\"text-danger d-inline-block remove-item-btn\" data-bs-toggle=\"modal\" (click)=\"confirm(deletemodal,data.id)\">\r\n                                                            <i class=\"ri-delete-bin-5-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                </ul>\r\n                                            </td>\r\n                                        </tr>\r\n                                        }\r\n                                    </tbody>\r\n                                </table>\r\n                            </ng-template>\r\n                        </li>\r\n                        <li [ngbNavItem]=\"2\" class=\"nav-item\">\r\n                            <a ngbNavLink class=\"nav-link py-3 New\" data-bs-toggle=\"tab\" id=\"New\" href=\"javascript:void(0);\" role=\"tab\" aria-selected=\"false\">\r\n                                New\r\n                            </a>\r\n                            <ng-template ngbNavContent>\r\n                                <table class=\"table table-nowrap align-middle\" id=\"jobListTable\">\r\n                                    <thead class=\"text-muted table-light\">\r\n                                        <tr class=\"text-uppercase\">\r\n                                            <th scope=\"col\" style=\"width: 25px;\">\r\n                                                <div class=\"form-check\">\r\n                                                    <input class=\"form-check-input\" type=\"checkbox\" id=\"checkAll\" value=\"option\" [(ngModel)]=\"masterSelected\" (change)=\"checkUncheckAll($event)\">\r\n                                                </div>\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('id')\" style=\"width: 140px;\">Application ID</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('name')\">Company Name\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('designation')\">\r\n                                                Designation</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('date')\">Apply Date\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('contacts')\">Contacts\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('type')\">Type</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('status')\">Status</th>\r\n                                            <th class=\"sort\">Action</th>\r\n                                        </tr>\r\n                                    </thead>\r\n                                    <tbody class=\"list form-check-all\">\r\n                                        @for(data of applications;track $index){\r\n                                        <tr id=\"a_{{data.id}}\">\r\n                                            <th scope=\"row\">\r\n                                                <div class=\"form-check\">\r\n                                                    <input class=\"form-check-input\" type=\"checkbox\" name=\"checkAll\" value=\"{{data.id}}\" [(ngModel)]=\"data.state\" (change)=\"onCheckboxChange($event)\">\r\n                                                </div>\r\n                                            </th>\r\n                                            <td class=\"id\"><a href=\"javascript:void(0);\" class=\"fw-medium link-primary\">#VZ{{data.id}}</a>\r\n                                            </td>\r\n                                            <td class=\"company\">\r\n                                                <div class=\"d-flex align-items-center\">\r\n                                                    <div class=\"flex-shrink-0\">\r\n                                                        <img src=\"{{data.img}}\" alt=\"\" class=\"avatar-xxs rounded-circle image_src object-fit-cover\">\r\n                                                    </div>\r\n                                                    <div class=\"flex-grow-1 ms-2\">{{data.name}}</div>\r\n                                                </div>\r\n                                            </td>\r\n                                            <td class=\"designation\">{{data.designation}}</td>\r\n                                            <td class=\"date\">{{data.date}}</td>\r\n                                            <td class=\"contacts\">{{data.contacts}}</td>\r\n                                            <td class=\"type\">{{data.type}}</td>\r\n                                            <td class=\"status\">\r\n                                                <span class=\"badge text-uppercase\" [ngClass]=\"{'bg-danger-subtle text-danger': data.status == 'Rejected','bg-info-subtle text-info': data.status == 'New',\r\n                                                    'bg-warning-subtle text-warning': data.status == 'Pending','bg-success-subtle text-success': data.status == 'Approved'}\">\r\n                                                    {{data.status}}</span>\r\n\r\n                                            </td>\r\n                                            <td>\r\n                                                <ul class=\"list-inline hstack gap-2 mb-0\">\r\n                                                    <li class=\"list-inline-item\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" ngbTooltip=\"View\">\r\n                                                        <a routerLink=\"/job-lists/overview\" class=\"text-primary d-inline-block\">\r\n                                                            <i class=\"ri-eye-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                    <li class=\"list-inline-item edit\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" ngbTooltip=\"Edit\" (click)=\"editorder(content,$index)\">\r\n                                                        <a data-bs-toggle=\"modal\" class=\"text-primary d-inline-block edit-item-btn\">\r\n                                                            <i class=\"ri-pencil-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                    <li class=\"list-inline-item me-0\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" ngbTooltip=\"Remove\">\r\n                                                        <a class=\"text-danger d-inline-block remove-item-btn\" data-bs-toggle=\"modal\" (click)=\"confirm(deletemodal,data.id)\">\r\n                                                            <i class=\"ri-delete-bin-5-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                </ul>\r\n                                            </td>\r\n                                        </tr>\r\n                                        }\r\n                                    </tbody>\r\n                                </table>\r\n                            </ng-template>\r\n                        </li>\r\n                        <li [ngbNavItem]=\"3\" class=\"nav-item\">\r\n                            <a ngbNavLink class=\"nav-link py-3 Pending\" data-bs-toggle=\"tab\" id=\"Pending\" href=\"javascript:void(0);\" role=\"tab\" aria-selected=\"false\">\r\n                                Pending <span class=\"badge bg-danger align-middle ms-1\">2</span>\r\n                            </a>\r\n                            <ng-template ngbNavContent>\r\n                                <table class=\"table table-nowrap align-middle\" id=\"jobListTable\">\r\n                                    <thead class=\"text-muted table-light\">\r\n                                        <tr class=\"text-uppercase\">\r\n                                            <th scope=\"col\" style=\"width: 25px;\">\r\n                                                <div class=\"form-check\">\r\n                                                    <input class=\"form-check-input\" type=\"checkbox\" id=\"checkAll\" value=\"option\" [(ngModel)]=\"masterSelected\" (change)=\"checkUncheckAll($event)\">\r\n                                                </div>\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('id')\" style=\"width: 140px;\">Application ID</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('name')\">Company Name\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('designation')\">\r\n                                                Designation</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('date')\">Apply Date\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('contacts')\">Contacts\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('type')\">Type</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('status')\">Status</th>\r\n                                            <th class=\"sort\">Action</th>\r\n                                        </tr>\r\n                                    </thead>\r\n                                    <tbody class=\"list form-check-all\">\r\n                                        @for(data of applications;track $index){\r\n                                        <tr id=\"a_{{data.id}}\">\r\n                                            <th scope=\"row\">\r\n                                                <div class=\"form-check\">\r\n                                                    <input class=\"form-check-input\" type=\"checkbox\" name=\"checkAll\" value=\"{{data.id}}\" [(ngModel)]=\"data.state\" (change)=\"onCheckboxChange($event)\">\r\n                                                </div>\r\n                                            </th>\r\n                                            <td class=\"id\"><a href=\"javascript:void(0);\" class=\"fw-medium link-primary\">#VZ{{data.id}}</a>\r\n                                            </td>\r\n                                            <td class=\"company\">\r\n                                                <div class=\"d-flex align-items-center\">\r\n                                                    <div class=\"flex-shrink-0\">\r\n                                                        <img src=\"{{data.img}}\" alt=\"\" class=\"avatar-xxs rounded-circle image_src object-fit-cover\">\r\n                                                    </div>\r\n                                                    <div class=\"flex-grow-1 ms-2\">{{data.name}}</div>\r\n                                                </div>\r\n                                            </td>\r\n                                            <td class=\"designation\">{{data.designation}}</td>\r\n                                            <td class=\"date\">{{data.date}}</td>\r\n                                            <td class=\"contacts\">{{data.contacts}}</td>\r\n                                            <td class=\"type\">{{data.type}}</td>\r\n                                            <td class=\"status\">\r\n                                                <span class=\"badge text-uppercase\" [ngClass]=\"{'bg-danger-subtle text-danger': data.status == 'Rejected','bg-info-subtle text-info': data.status == 'New',\r\n                                                    'bg-warning-subtle text-warning': data.status == 'Pending','bg-success-subtle text-success': data.status == 'Approved'}\">\r\n                                                    {{data.status}}</span>\r\n\r\n                                            </td>\r\n                                            <td>\r\n                                                <ul class=\"list-inline hstack gap-2 mb-0\">\r\n                                                    <li class=\"list-inline-item\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" title=\"View\">\r\n                                                        <a routerLink=\"/job-lists/overview\" class=\"text-primary d-inline-block\">\r\n                                                            <i class=\"ri-eye-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                    <li class=\"list-inline-item edit\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" ngbTooltip=\"Edit\" (click)=\"editorder(content,$index)\">\r\n                                                        <a data-bs-toggle=\"modal\" class=\"text-primary d-inline-block edit-item-btn\">\r\n                                                            <i class=\"ri-pencil-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                    <li class=\"list-inline-item me-0\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" ngbTooltip=\"Remove\">\r\n                                                        <a class=\"text-danger d-inline-block remove-item-btn\" data-bs-toggle=\"modal\" (click)=\"confirm(deletemodal,data.id)\">\r\n                                                            <i class=\"ri-delete-bin-5-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                </ul>\r\n                                            </td>\r\n                                        </tr>\r\n                                        }\r\n                                    </tbody>\r\n                                </table>\r\n                            </ng-template>\r\n                        </li>\r\n                        <li [ngbNavItem]=\"4\" class=\"nav-item\">\r\n                            <a ngbNavLink class=\"nav-link py-3 Approved\" data-bs-toggle=\"tab\" id=\"Approved\" href=\"javascript:void(0);\" role=\"tab\" aria-selected=\"false\">\r\n                                Approved\r\n                            </a>\r\n                            <ng-template ngbNavContent>\r\n                                <table class=\"table table-nowrap align-middle\" id=\"jobListTable\">\r\n                                    <thead class=\"text-muted table-light\">\r\n                                        <tr class=\"text-uppercase\">\r\n                                            <th scope=\"col\" style=\"width: 25px;\">\r\n                                                <div class=\"form-check\">\r\n                                                    <input class=\"form-check-input\" type=\"checkbox\" id=\"checkAll\" value=\"option\" [(ngModel)]=\"masterSelected\" (change)=\"checkUncheckAll($event)\">\r\n                                                </div>\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('id')\" style=\"width: 140px;\">Application ID</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('name')\">Company Name\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('designation')\">\r\n                                                Designation</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('date')\">Apply Date\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('contacts')\">Contacts\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('type')\">Type</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('status')\">Status</th>\r\n                                            <th class=\"sort\">Action</th>\r\n                                        </tr>\r\n                                    </thead>\r\n                                    <tbody class=\"list form-check-all\">\r\n                                        @for(data of applications;track $index){\r\n                                        <tr id=\"a_{{data.id}}\">\r\n                                            <th scope=\"row\">\r\n                                                <div class=\"form-check\">\r\n                                                    <input class=\"form-check-input\" type=\"checkbox\" name=\"checkAll\" value=\"{{data.id}}\" [(ngModel)]=\"data.state\" (change)=\"onCheckboxChange($event)\">\r\n                                                </div>\r\n                                            </th>\r\n                                            <td class=\"id\"><a href=\"javascript:void(0);\" class=\"fw-medium link-primary\">#VZ{{data.id}}</a>\r\n                                            </td>\r\n                                            <td class=\"company\">\r\n                                                <div class=\"d-flex align-items-center\">\r\n                                                    <div class=\"flex-shrink-0\">\r\n                                                        <img src=\"{{data.img}}\" alt=\"\" class=\"avatar-xxs rounded-circle image_src object-fit-cover\">\r\n                                                    </div>\r\n                                                    <div class=\"flex-grow-1 ms-2\">{{data.name}}</div>\r\n                                                </div>\r\n                                            </td>\r\n                                            <td class=\"designation\">{{data.designation}}</td>\r\n                                            <td class=\"date\">{{data.date}}</td>\r\n                                            <td class=\"contacts\">{{data.contacts}}</td>\r\n                                            <td class=\"type\">{{data.type}}</td>\r\n                                            <td class=\"status\">\r\n                                                <span class=\"badge text-uppercase\" [ngClass]=\"{'bg-danger-subtle text-danger': data.status == 'Rejected','bg-info-subtle text-info': data.status == 'New',\r\n                                                    'bg-warning-subtle text-warning': data.status == 'Pending','bg-success-subtle text-success': data.status == 'Approved'}\">\r\n                                                    {{data.status}}</span>\r\n\r\n                                            </td>\r\n                                            <td>\r\n                                                <ul class=\"list-inline hstack gap-2 mb-0\">\r\n                                                    <li class=\"list-inline-item\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" title=\"View\">\r\n                                                        <a routerLink=\"/job-lists/overview\" class=\"text-primary d-inline-block\">\r\n                                                            <i class=\"ri-eye-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                    <li class=\"list-inline-item edit\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" ngbTooltip=\"Edit\" (click)=\"editorder(content,$index)\">\r\n                                                        <a data-bs-toggle=\"modal\" class=\"text-primary d-inline-block edit-item-btn\">\r\n                                                            <i class=\"ri-pencil-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                    <li class=\"list-inline-item me-0\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" ngbTooltip=\"Remove\">\r\n                                                        <a class=\"text-danger d-inline-block remove-item-btn\" data-bs-toggle=\"modal\" (click)=\"confirm(deletemodal,data.id)\">\r\n                                                            <i class=\"ri-delete-bin-5-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                </ul>\r\n                                            </td>\r\n                                        </tr>\r\n                                        }\r\n                                    </tbody>\r\n                                </table>\r\n                            </ng-template>\r\n                        </li>\r\n                        <li [ngbNavItem]=\"5\" class=\"nav-item\">\r\n                            <a ngbNavLink class=\"nav-link py-3 Rejected\" data-bs-toggle=\"tab\" id=\"Rejected\" href=\"javascript:void(0);\" role=\"tab\" aria-selected=\"false\">\r\n                                Rejected\r\n                            </a>\r\n                            <ng-template ngbNavContent>\r\n                                <table class=\"table table-nowrap align-middle\" id=\"jobListTable\">\r\n                                    <thead class=\"text-muted table-light\">\r\n                                        <tr class=\"text-uppercase\">\r\n                                            <th scope=\"col\" style=\"width: 25px;\">\r\n                                                <div class=\"form-check\">\r\n                                                    <input class=\"form-check-input\" type=\"checkbox\" id=\"checkAll\" value=\"option\" [(ngModel)]=\"masterSelected\" (change)=\"checkUncheckAll($event)\">\r\n                                                </div>\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('id')\" style=\"width: 140px;\">Application ID</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('name')\">Company Name\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('designation')\">\r\n                                                Designation</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('date')\">Apply Date\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('contacts')\">Contacts\r\n                                            </th>\r\n                                            <th class=\"sort\" (click)=\"onSort('type')\">Type</th>\r\n                                            <th class=\"sort\" (click)=\"onSort('status')\">Status</th>\r\n                                            <th class=\"sort\">Action</th>\r\n                                        </tr>\r\n                                    </thead>\r\n                                    <tbody class=\"list form-check-all\">\r\n                                        @for(data of applications;track $index){\r\n                                        <tr id=\"a_{{data.id}}\">\r\n                                            <th scope=\"row\">\r\n                                                <div class=\"form-check\">\r\n                                                    <input class=\"form-check-input\" type=\"checkbox\" name=\"checkAll\" value=\"{{data.id}}\" [(ngModel)]=\"data.state\" (change)=\"onCheckboxChange($event)\">\r\n                                                </div>\r\n                                            </th>\r\n                                            <td class=\"id\"><a href=\"javascript:void(0);\" class=\"fw-medium link-primary\">#VZ{{data.id}}</a>\r\n                                            </td>\r\n                                            <td class=\"company\">\r\n                                                <div class=\"d-flex align-items-center\">\r\n                                                    <div class=\"flex-shrink-0\">\r\n                                                        <img src=\"{{data.img}}\" alt=\"\" class=\"avatar-xxs rounded-circle image_src object-fit-cover\">\r\n                                                    </div>\r\n                                                    <div class=\"flex-grow-1 ms-2\">{{data.name}}</div>\r\n                                                </div>\r\n                                            </td>\r\n                                            <td class=\"designation\">{{data.designation}}</td>\r\n                                            <td class=\"date\">{{data.date}}</td>\r\n                                            <td class=\"contacts\">{{data.contacts}}</td>\r\n                                            <td class=\"type\">{{data.type}}</td>\r\n                                            <td class=\"status\">\r\n                                                <span class=\"badge text-uppercase\" [ngClass]=\"{'bg-danger-subtle text-danger': data.status == 'Rejected','bg-info-subtle text-info': data.status == 'New',\r\n                                                    'bg-warning-subtle text-warning': data.status == 'Pending','bg-success-subtle text-success': data.status == 'Approved'}\">\r\n                                                    {{data.status}}</span>\r\n\r\n                                            </td>\r\n                                            <td>\r\n                                                <ul class=\"list-inline hstack gap-2 mb-0\">\r\n                                                    <li class=\"list-inline-item\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" title=\"View\">\r\n                                                        <a routerLink=\"/job-lists/overview\" class=\"text-primary d-inline-block\">\r\n                                                            <i class=\"ri-eye-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                    <li class=\"list-inline-item edit\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" ngbTooltip=\"Edit\" (click)=\"editorder(content,$index)\">\r\n                                                        <a data-bs-toggle=\"modal\" class=\"text-primary d-inline-block edit-item-btn\">\r\n                                                            <i class=\"ri-pencil-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                    <li class=\"list-inline-item me-0\" data-bs-toggle=\"tooltip\" data-bs-trigger=\"hover\" data-bs-placement=\"top\" ngbTooltip=\"Remove\">\r\n                                                        <a class=\"text-danger d-inline-block remove-item-btn\" data-bs-toggle=\"modal\" (click)=\"confirm(deletemodal,data.id)\">\r\n                                                            <i class=\"ri-delete-bin-5-fill fs-16\"></i>\r\n                                                        </a>\r\n                                                    </li>\r\n                                                </ul>\r\n                                            </td>\r\n                                        </tr>\r\n                                        }\r\n                                    </tbody>\r\n                                </table>\r\n                            </ng-template>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <!-- Tab Pane -->\r\n                    <div class=\"table-responsive table-card mb-1\">\r\n                        <div class=\"noresult\" style=\"display: none\">\r\n                            <div class=\"text-center\">\r\n                                <lord-icon src=\"https://cdn.lordicon.com/msoeawqm.json\" trigger=\"loop\" colors=\"primary:#405189,secondary:#0ab39c\" style=\"width:75px;height:75px\">\r\n                                </lord-icon>\r\n                                <h5 class=\"mt-2\">Sorry! No Result Found</h5>\r\n                                <p class=\"text-muted\">We've searched more than 150+ result We did not find jobs for you\r\n                                    search.</p>\r\n                            </div>\r\n                        </div>\r\n                        <div [ngbNavOutlet]=\"nav\"></div>\r\n                    </div>\r\n                    <!-- pagination -->\r\n                    <ngb-pagination class=\"d-flex justify-content-end pt-2\" [collectionSize]=\"allapplications?.length\" [(page)]=\"service.page\" [pageSize]=\"service.pageSize\" (pageChange)=\"changePage()\" aria-label=\"Custom pagination\">\r\n                        <ng-template ngbPaginationPrevious let-page let-pages=\"pages\">\r\n                            <i class=\"ci-arrow-left me-2\"></i>\r\n                            Prev\r\n                        </ng-template>\r\n                        <ng-template ngbPaginationNext>\r\n                            Next\r\n                            <i class=\"ci-arrow-right ms-2\"></i>\r\n                        </ng-template>\r\n                    </ngb-pagination>\r\n                    <div id=\"elmLoader\">\r\n                        <div class=\"spinner-border text-primary avatar-sm\" role=\"status\">\r\n                            <span class=\"visually-hidden\">Loading...</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n\r\n                <!-- Modal -->\r\n                <ng-template #content role=\"document\" let-modal>\r\n                    <div class=\"modal-header bg-light p-3\">\r\n                        <h5 class=\"modal-title\" id=\"exampleModalLabel\">Add Application&nbsp;</h5>\r\n                        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" (click)=\"modal.dismiss('close click')\" id=\"close-modal\"></button>\r\n                    </div>\r\n                    <form action=\"#\" autocomplete=\"off\" (ngSubmit)=\"createapplication()\" [formGroup]=\"applicationData\">\r\n                        <div class=\"modal-body\">\r\n\r\n                            <div class=\"mb-3 d-none\" id=\"modal-id\">\r\n                                <label for=\"applicationId\" class=\"form-label\">ID</label>\r\n                                <input type=\"hidden\" id=\"applicationId\" class=\"form-control\" placeholder=\"ID\" formControlName=\"id\" readonly />\r\n                            </div>\r\n\r\n                            <div class=\"text-center\">\r\n                                <div class=\"position-relative d-inline-block\">\r\n                                    <div class=\"position-absolute  bottom-0 end-0\">\r\n                                        <label for=\"companylogo-image-input\" class=\"mb-0\" data-bs-toggle=\"tooltip\" data-bs-placement=\"right\" ngbTooltip=\"Select Image\">\r\n                                            <div class=\"avatar-xs cursor-pointer\">\r\n                                                <div class=\"avatar-title bg-light border rounded-circle text-muted\">\r\n                                                    <i class=\"ri-image-fill\"></i>\r\n                                                </div>\r\n                                            </div>\r\n                                        </label>\r\n                                        <input class=\"form-control d-none\" value=\"\" id=\"companylogo-image-input\" type=\"file\" accept=\"image/png, image/gif, image/jpeg\" (change)=\"fileChange($event)\">\r\n                                    </div>\r\n                                    <div class=\"avatar-lg p-1\">\r\n                                        <div class=\"avatar-title bg-light rounded-circle\">\r\n                                            <img src=\"assets/images/users/multi-user.jpg\" id=\"companylogo-img\" class=\"avatar-md h-auto rounded-circle object-fit-cover\" />\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div class=\"mb-3\">\r\n                                <label for=\"company-field\" class=\"form-label\">Company</label>\r\n                                <input type=\"text\" id=\"company-field\" class=\"form-control\" formControlName=\"name\" placeholder=\"Enter company name\" required />\r\n                            </div>\r\n\r\n                            <div class=\"mb-3\">\r\n                                <label for=\"designation-field\" class=\"form-label\">Designation</label>\r\n                                <input type=\"text\" id=\"designation-field\" class=\"form-control\" formControlName=\"designation\" placeholder=\"Enter designation\" required />\r\n                            </div>\r\n\r\n                            <div class=\"mb-3\">\r\n                                <label for=\"date-field\" class=\"form-label\">Apply Date</label>\r\n                                <input type=\"date\" id=\"date-field\" class=\"form-control\" mwlFlatpickr [convertModelValue]=\"true\" required formControlName=\"date\" placeholder=\"Select date\" />\r\n                            </div>\r\n\r\n                            <div class=\"mb-3\">\r\n                                <label for=\"contact-field\" class=\"form-label\">Contacts</label>\r\n                                <input type=\"text\" id=\"contact-field\" class=\"form-control\" placeholder=\"Enter contact\" formControlName=\"contacts\" required />\r\n                            </div>\r\n\r\n                            <div class=\"row gy-4 mb-3\">\r\n                                <div class=\"col-md-6\">\r\n                                    <div>\r\n                                        <label for=\"status-input\" class=\"form-label\">Status</label>\r\n                                        <select class=\"form-control\" data-trigger name=\"status-input\" id=\"status-input\" formControlName=\"status\">\r\n                                            <option value=\"\">Status</option>\r\n                                            <option value=\"Approved\">Approved</option>\r\n                                            <option value=\"New\">New</option>\r\n                                            <option value=\"Pending\">Pending</option>\r\n                                            <option value=\"Rejected\">Rejected</option>\r\n                                        </select>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"col-md-6\">\r\n                                    <div>\r\n                                        <label for=\"type-input\" class=\"form-label\">Type</label>\r\n                                        <select class=\"form-control\" data-trigger name=\"type-input\" id=\"type-input\" formControlName=\"type\">\r\n                                            <option value=\"\">Select Type</option>\r\n                                            <option value=\"Full Time\">Full Time</option>\r\n                                            <option value=\"Part Time\">Part Time</option>\r\n                                        </select>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n\r\n\r\n                        </div>\r\n                        <div class=\"modal-footer\">\r\n                            <div class=\"hstack gap-2 justify-content-end\">\r\n                                <button type=\"button\" class=\"btn btn-light\" data-bs-dismiss=\"modal\" (click)=\"modal.dismiss('close click')\">Close</button>\r\n                                <button type=\"submit\" class=\"btn btn-success\" id=\"add-btn\">Add</button>\r\n                            </div>\r\n                        </div>\r\n                    </form>\r\n                </ng-template>\r\n\r\n                <!-- Modal -->\r\n\r\n                <ng-template #deletemodal role=\"document\" let-modal>\r\n                    <div class=\"modal-body p-5 text-center\">\r\n                        <lord-icon src=\"https://cdn.lordicon.com/gsqxdxog.json\" trigger=\"loop\" colors=\"primary:#405189,secondary:#f06548\" style=\"width:90px;height:90px\">\r\n                        </lord-icon>\r\n                        <div class=\"mt-4 text-center\">\r\n                            <h4>You are about to delete a order ?</h4>\r\n                            <p class=\"text-muted fs-15 mb-4\">Deleting your order will remove all of your\r\n                                information from our database.</p>\r\n                            <div class=\"hstack gap-2 justify-content-center remove\">\r\n                                <button class=\"btn btn-link link-success fw-medium text-decoration-none\" id=\"deleteRecord-close\" data-bs-dismiss=\"modal\" (click)=\"modal.dismiss('close click')\"><i class=\"ri-close-line me-1 align-middle\"></i>\r\n                                    Close</button>\r\n                                <button class=\"btn btn-danger\" id=\"delete-record\" (click)=\"deleteData(deleteId)\">Yes,\r\n                                    Delete\r\n                                    It</button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </ng-template>\r\n                <!--end modal -->\r\n            </div>\r\n        </div>\r\n\r\n    </div>\r\n    <!--end col-->\r\n</div>"], "mappings": "AAIA,SAA6BA,UAAU,QAA6D,gBAAgB;AACpH;AACA,OAAOC,IAAI,MAAM,aAAa;AAG9B,SAASC,cAAc,EAAqBC,oBAAoB,EAAEC,iBAAiB,QAAQ,gCAAgC;AAC3H,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,kCAAkC;AACpF,SAASC,SAAS,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;IC+FkBC,EAHZ,CAAAC,cAAA,aAAuB,aACH,cACY,gBAC6H;IAA7DD,EAAA,CAAAE,gBAAA,2BAAAC,mFAAAC,MAAA;MAAA,MAAAC,OAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAJ,OAAA,CAAAK,KAAA,EAAAN,MAAA,MAAAC,OAAA,CAAAK,KAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAACJ,EAAA,CAAAY,UAAA,oBAAAC,4EAAAT,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAE,gBAAA,CAAAZ,MAAA,CAAwB;IAAA,EAAC;IAExJJ,EAFQ,CAAAiB,YAAA,EAAiJ,EAC/I,EACL;IACUjB,EAAf,CAAAC,cAAA,aAAe,YAA6D;IAAAD,EAAA,CAAAkB,MAAA,GAAc;IAC1FlB,EAD0F,CAAAiB,YAAA,EAAI,EACzF;IAGGjB,EAFR,CAAAC,cAAA,aAAoB,cACuB,cACR;IACvBD,EAAA,CAAAmB,SAAA,eAA4F;IAChGnB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAEnDlB,EAFmD,CAAAiB,YAAA,EAAM,EAC/C,EACL;IACLjB,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAkB,MAAA,IAAoB;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACjDjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACnCjB,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAkB,MAAA,IAAiB;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IAC3CjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IAE/BjB,EADJ,CAAAC,cAAA,cAAmB,gBAE8G;IACzHD,EAAA,CAAAkB,MAAA,IAAe;IAEvBlB,EAFuB,CAAAiB,YAAA,EAAO,EAEzB;IAIOjB,EAHZ,CAAAC,cAAA,UAAI,cAC0C,cACkF,aAC5C;IACpED,EAAA,CAAAmB,SAAA,aAAiC;IAEzCnB,EADI,CAAAiB,YAAA,EAAI,EACH;IACLjB,EAAA,CAAAC,cAAA,cAAiK;IAApCD,EAAA,CAAAY,UAAA,mBAAAQ,yEAAA;MAAA,MAAAC,SAAA,GAAArB,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAe,MAAA;MAAA,MAAAR,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAQ,UAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAW,SAAA,CAAAF,UAAA,EAAAF,SAAA,CAAyB;IAAA,EAAC;IAC5JrB,EAAA,CAAAC,cAAA,aAA4E;IACxED,EAAA,CAAAmB,SAAA,aAAoC;IAE5CnB,EADI,CAAAiB,YAAA,EAAI,EACH;IAEDjB,EADJ,CAAAC,cAAA,cAA+H,aACP;IAAvCD,EAAA,CAAAY,UAAA,mBAAAc,wEAAA;MAAA,MAAArB,OAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAM,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAY,cAAA,GAAA3B,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAc,OAAA,CAAAD,cAAA,EAAAtB,OAAA,CAAAwB,EAAA,CAA4B;IAAA,EAAC;IAC/G7B,EAAA,CAAAmB,SAAA,aAA0C;IAK9DnB,EAJgB,CAAAiB,YAAA,EAAI,EACH,EACJ,EACJ,EACJ;;;;IA7CDjB,EAAA,CAAA8B,sBAAA,aAAAzB,OAAA,CAAAwB,EAAA,KAAkB;IAGsD7B,EAAA,CAAA+B,SAAA,GAAmB;IAAnB/B,EAAA,CAAAgC,qBAAA,UAAA3B,OAAA,CAAAwB,EAAA,CAAmB;IAAC7B,EAAA,CAAAiC,gBAAA,YAAA5B,OAAA,CAAAK,KAAA,CAAwB;IAGxCV,EAAA,CAAA+B,SAAA,GAAc;IAAd/B,EAAA,CAAAkC,kBAAA,QAAA7B,OAAA,CAAAwB,EAAA,KAAc;IAKzE7B,EAAA,CAAA+B,SAAA,GAAkB;IAAlB/B,EAAA,CAAAgC,qBAAA,QAAA3B,OAAA,CAAA8B,GAAA,EAAAnC,EAAA,CAAAoC,aAAA,CAAkB;IAEGpC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAAhC,OAAA,CAAAiC,IAAA,CAAa;IAG3BtC,EAAA,CAAA+B,SAAA,GAAoB;IAApB/B,EAAA,CAAAqC,iBAAA,CAAAhC,OAAA,CAAAkC,WAAA,CAAoB;IAC3BvC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAAhC,OAAA,CAAAmC,IAAA,CAAa;IACTxC,EAAA,CAAA+B,SAAA,GAAiB;IAAjB/B,EAAA,CAAAqC,iBAAA,CAAAhC,OAAA,CAAAoC,QAAA,CAAiB;IACrBzC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAAhC,OAAA,CAAAqC,IAAA,CAAa;IAES1C,EAAA,CAAA+B,SAAA,GACyF;IADzF/B,EAAA,CAAA2C,UAAA,YAAA3C,EAAA,CAAA4C,eAAA,KAAAC,GAAA,EAAAxC,OAAA,CAAAyC,MAAA,gBAAAzC,OAAA,CAAAyC,MAAA,WAAAzC,OAAA,CAAAyC,MAAA,eAAAzC,OAAA,CAAAyC,MAAA,gBACyF;IACxH9C,EAAA,CAAA+B,SAAA,EAAe;IAAf/B,EAAA,CAAAkC,kBAAA,MAAA7B,OAAA,CAAAyC,MAAA,KAAe;;;;;;IA1Cf9C,EALpB,CAAAC,cAAA,gBAAiE,gBACvB,aACP,aACc,cACT,gBACyH;IAAhED,EAAA,CAAAE,gBAAA,2BAAA6C,4EAAA3C,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAA0C,GAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAS,kBAAA,CAAAK,MAAA,CAAAmC,cAAA,EAAA7C,MAAA,MAAAU,MAAA,CAAAmC,cAAA,GAAA7C,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA4B;IAACJ,EAAA,CAAAY,UAAA,oBAAAsC,qEAAA9C,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAA0C,GAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAqC,eAAA,CAAA/C,MAAA,CAAuB;IAAA,EAAC;IAEpJJ,EAFQ,CAAAiB,YAAA,EAA6I,EAC3I,EACL;IACLjB,EAAA,CAAAC,cAAA,aAA8D;IAA7CD,EAAA,CAAAY,UAAA,mBAAAwC,iEAAA;MAAApD,EAAA,CAAAM,aAAA,CAAA0C,GAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,IAAI,CAAC;IAAA,EAAC;IAAuBrD,EAAA,CAAAkB,MAAA,qBAAc;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACjFjB,EAAA,CAAAC,cAAA,aAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAA0C,iEAAA;MAAAtD,EAAA,CAAAM,aAAA,CAAA0C,GAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,oBAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAAiD;IAAhCD,EAAA,CAAAY,UAAA,mBAAA2C,kEAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAA0C,GAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,aAAa,CAAC;IAAA,EAAC;IAC5CrD,EAAA,CAAAkB,MAAA,oBAAW;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACpBjB,EAAA,CAAAC,cAAA,cAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAA4C,kEAAA;MAAAxD,EAAA,CAAAM,aAAA,CAAA0C,GAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,mBAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAA8C;IAA7BD,EAAA,CAAAY,UAAA,mBAAA6C,kEAAA;MAAAzD,EAAA,CAAAM,aAAA,CAAA0C,GAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,UAAU,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,iBAC9C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAA8C,kEAAA;MAAA1D,EAAA,CAAAM,aAAA,CAAA0C,GAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,YAAI;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACnDjB,EAAA,CAAAC,cAAA,cAA4C;IAA3BD,EAAA,CAAAY,UAAA,mBAAA+C,kEAAA;MAAA3D,EAAA,CAAAM,aAAA,CAAA0C,GAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,QAAQ,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,cAAM;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACvDjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,cAAM;IAE/BlB,EAF+B,CAAAiB,YAAA,EAAK,EAC3B,EACD;IACRjB,EAAA,CAAAC,cAAA,iBAAmC;IAC/BD,EAAA,CAAA4D,gBAAA,KAAAC,mDAAA,oBAAA7D,EAAA,CAAA8D,sBAAA,CA+CC;IAET9D,EADI,CAAAiB,YAAA,EAAQ,EACJ;;;;IAnEyFjB,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAiC,gBAAA,YAAAnB,MAAA,CAAAmC,cAAA,CAA4B;IAkBrHjD,EAAA,CAAA+B,SAAA,IA+CC;IA/CD/B,EAAA,CAAA+D,UAAA,CAAAjD,MAAA,CAAAkD,YAAA,CA+CC;;;;;;IAqCWhE,EAHZ,CAAAC,cAAA,aAAuB,aACH,cACY,gBAC6H;IAA7DD,EAAA,CAAAE,gBAAA,2BAAA+D,mFAAA7D,MAAA;MAAA,MAAA8D,QAAA,GAAAlE,EAAA,CAAAM,aAAA,CAAA6D,IAAA,EAAA3D,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAyD,QAAA,CAAAxD,KAAA,EAAAN,MAAA,MAAA8D,QAAA,CAAAxD,KAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAACJ,EAAA,CAAAY,UAAA,oBAAAwD,4EAAAhE,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAA6D,IAAA;MAAA,MAAArD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAE,gBAAA,CAAAZ,MAAA,CAAwB;IAAA,EAAC;IAExJJ,EAFQ,CAAAiB,YAAA,EAAiJ,EAC/I,EACL;IACUjB,EAAf,CAAAC,cAAA,aAAe,YAA6D;IAAAD,EAAA,CAAAkB,MAAA,GAAc;IAC1FlB,EAD0F,CAAAiB,YAAA,EAAI,EACzF;IAGGjB,EAFR,CAAAC,cAAA,aAAoB,cACuB,cACR;IACvBD,EAAA,CAAAmB,SAAA,eAA4F;IAChGnB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAEnDlB,EAFmD,CAAAiB,YAAA,EAAM,EAC/C,EACL;IACLjB,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAkB,MAAA,IAAoB;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACjDjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACnCjB,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAkB,MAAA,IAAiB;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IAC3CjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IAE/BjB,EADJ,CAAAC,cAAA,cAAmB,gBAE8G;IACzHD,EAAA,CAAAkB,MAAA,IAAe;IAEvBlB,EAFuB,CAAAiB,YAAA,EAAO,EAEzB;IAIOjB,EAHZ,CAAAC,cAAA,UAAI,cAC0C,cACkF,aAC5C;IACpED,EAAA,CAAAmB,SAAA,aAAiC;IAEzCnB,EADI,CAAAiB,YAAA,EAAI,EACH;IACLjB,EAAA,CAAAC,cAAA,cAAiK;IAApCD,EAAA,CAAAY,UAAA,mBAAAyD,yEAAA;MAAA,MAAAC,UAAA,GAAAtE,EAAA,CAAAM,aAAA,CAAA6D,IAAA,EAAA7C,MAAA;MAAA,MAAAR,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAQ,UAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAW,SAAA,CAAAF,UAAA,EAAA+C,UAAA,CAAyB;IAAA,EAAC;IAC5JtE,EAAA,CAAAC,cAAA,aAA4E;IACxED,EAAA,CAAAmB,SAAA,aAAoC;IAE5CnB,EADI,CAAAiB,YAAA,EAAI,EACH;IAEDjB,EADJ,CAAAC,cAAA,cAA+H,aACP;IAAvCD,EAAA,CAAAY,UAAA,mBAAA2D,wEAAA;MAAA,MAAAL,QAAA,GAAAlE,EAAA,CAAAM,aAAA,CAAA6D,IAAA,EAAA3D,SAAA;MAAA,MAAAM,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAY,cAAA,GAAA3B,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAc,OAAA,CAAAD,cAAA,EAAAuC,QAAA,CAAArC,EAAA,CAA4B;IAAA,EAAC;IAC/G7B,EAAA,CAAAmB,SAAA,aAA0C;IAK9DnB,EAJgB,CAAAiB,YAAA,EAAI,EACH,EACJ,EACJ,EACJ;;;;IA7CDjB,EAAA,CAAA8B,sBAAA,aAAAoC,QAAA,CAAArC,EAAA,KAAkB;IAGsD7B,EAAA,CAAA+B,SAAA,GAAmB;IAAnB/B,EAAA,CAAAgC,qBAAA,UAAAkC,QAAA,CAAArC,EAAA,CAAmB;IAAC7B,EAAA,CAAAiC,gBAAA,YAAAiC,QAAA,CAAAxD,KAAA,CAAwB;IAGxCV,EAAA,CAAA+B,SAAA,GAAc;IAAd/B,EAAA,CAAAkC,kBAAA,QAAAgC,QAAA,CAAArC,EAAA,KAAc;IAKzE7B,EAAA,CAAA+B,SAAA,GAAkB;IAAlB/B,EAAA,CAAAgC,qBAAA,QAAAkC,QAAA,CAAA/B,GAAA,EAAAnC,EAAA,CAAAoC,aAAA,CAAkB;IAEGpC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAA6B,QAAA,CAAA5B,IAAA,CAAa;IAG3BtC,EAAA,CAAA+B,SAAA,GAAoB;IAApB/B,EAAA,CAAAqC,iBAAA,CAAA6B,QAAA,CAAA3B,WAAA,CAAoB;IAC3BvC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAA6B,QAAA,CAAA1B,IAAA,CAAa;IACTxC,EAAA,CAAA+B,SAAA,GAAiB;IAAjB/B,EAAA,CAAAqC,iBAAA,CAAA6B,QAAA,CAAAzB,QAAA,CAAiB;IACrBzC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAA6B,QAAA,CAAAxB,IAAA,CAAa;IAES1C,EAAA,CAAA+B,SAAA,GACyF;IADzF/B,EAAA,CAAA2C,UAAA,YAAA3C,EAAA,CAAA4C,eAAA,KAAAC,GAAA,EAAAqB,QAAA,CAAApB,MAAA,gBAAAoB,QAAA,CAAApB,MAAA,WAAAoB,QAAA,CAAApB,MAAA,eAAAoB,QAAA,CAAApB,MAAA,gBACyF;IACxH9C,EAAA,CAAA+B,SAAA,EAAe;IAAf/B,EAAA,CAAAkC,kBAAA,MAAAgC,QAAA,CAAApB,MAAA,KAAe;;;;;;IA1Cf9C,EALpB,CAAAC,cAAA,gBAAiE,gBACvB,aACP,aACc,cACT,gBACyH;IAAhED,EAAA,CAAAE,gBAAA,2BAAAsE,4EAAApE,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAmE,GAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAS,kBAAA,CAAAK,MAAA,CAAAmC,cAAA,EAAA7C,MAAA,MAAAU,MAAA,CAAAmC,cAAA,GAAA7C,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA4B;IAACJ,EAAA,CAAAY,UAAA,oBAAA8D,qEAAAtE,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAmE,GAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAqC,eAAA,CAAA/C,MAAA,CAAuB;IAAA,EAAC;IAEpJJ,EAFQ,CAAAiB,YAAA,EAA6I,EAC3I,EACL;IACLjB,EAAA,CAAAC,cAAA,aAA8D;IAA7CD,EAAA,CAAAY,UAAA,mBAAA+D,iEAAA;MAAA3E,EAAA,CAAAM,aAAA,CAAAmE,GAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,IAAI,CAAC;IAAA,EAAC;IAAuBrD,EAAA,CAAAkB,MAAA,qBAAc;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACjFjB,EAAA,CAAAC,cAAA,aAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAAgE,iEAAA;MAAA5E,EAAA,CAAAM,aAAA,CAAAmE,GAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,oBAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAAiD;IAAhCD,EAAA,CAAAY,UAAA,mBAAAiE,kEAAA;MAAA7E,EAAA,CAAAM,aAAA,CAAAmE,GAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,aAAa,CAAC;IAAA,EAAC;IAC5CrD,EAAA,CAAAkB,MAAA,oBAAW;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACpBjB,EAAA,CAAAC,cAAA,cAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAAkE,kEAAA;MAAA9E,EAAA,CAAAM,aAAA,CAAAmE,GAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,mBAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAA8C;IAA7BD,EAAA,CAAAY,UAAA,mBAAAmE,kEAAA;MAAA/E,EAAA,CAAAM,aAAA,CAAAmE,GAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,UAAU,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,iBAC9C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAAoE,kEAAA;MAAAhF,EAAA,CAAAM,aAAA,CAAAmE,GAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,YAAI;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACnDjB,EAAA,CAAAC,cAAA,cAA4C;IAA3BD,EAAA,CAAAY,UAAA,mBAAAqE,kEAAA;MAAAjF,EAAA,CAAAM,aAAA,CAAAmE,GAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,QAAQ,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,cAAM;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACvDjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,cAAM;IAE/BlB,EAF+B,CAAAiB,YAAA,EAAK,EAC3B,EACD;IACRjB,EAAA,CAAAC,cAAA,iBAAmC;IAC/BD,EAAA,CAAA4D,gBAAA,KAAAsB,mDAAA,oBAAAlF,EAAA,CAAA8D,sBAAA,CA+CC;IAET9D,EADI,CAAAiB,YAAA,EAAQ,EACJ;;;;IAnEyFjB,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAiC,gBAAA,YAAAnB,MAAA,CAAAmC,cAAA,CAA4B;IAkBrHjD,EAAA,CAAA+B,SAAA,IA+CC;IA/CD/B,EAAA,CAAA+D,UAAA,CAAAjD,MAAA,CAAAkD,YAAA,CA+CC;;;;;;IAqCWhE,EAHZ,CAAAC,cAAA,aAAuB,aACH,cACY,gBAC6H;IAA7DD,EAAA,CAAAE,gBAAA,2BAAAiF,mFAAA/E,MAAA;MAAA,MAAAgF,QAAA,GAAApF,EAAA,CAAAM,aAAA,CAAA+E,IAAA,EAAA7E,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAA2E,QAAA,CAAA1E,KAAA,EAAAN,MAAA,MAAAgF,QAAA,CAAA1E,KAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAACJ,EAAA,CAAAY,UAAA,oBAAA0E,4EAAAlF,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAA+E,IAAA;MAAA,MAAAvE,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAE,gBAAA,CAAAZ,MAAA,CAAwB;IAAA,EAAC;IAExJJ,EAFQ,CAAAiB,YAAA,EAAiJ,EAC/I,EACL;IACUjB,EAAf,CAAAC,cAAA,aAAe,YAA6D;IAAAD,EAAA,CAAAkB,MAAA,GAAc;IAC1FlB,EAD0F,CAAAiB,YAAA,EAAI,EACzF;IAGGjB,EAFR,CAAAC,cAAA,aAAoB,cACuB,cACR;IACvBD,EAAA,CAAAmB,SAAA,eAA4F;IAChGnB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAEnDlB,EAFmD,CAAAiB,YAAA,EAAM,EAC/C,EACL;IACLjB,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAkB,MAAA,IAAoB;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACjDjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACnCjB,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAkB,MAAA,IAAiB;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IAC3CjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IAE/BjB,EADJ,CAAAC,cAAA,cAAmB,gBAE8G;IACzHD,EAAA,CAAAkB,MAAA,IAAe;IAEvBlB,EAFuB,CAAAiB,YAAA,EAAO,EAEzB;IAIOjB,EAHZ,CAAAC,cAAA,UAAI,cAC0C,cAC6E,aACvC;IACpED,EAAA,CAAAmB,SAAA,aAAiC;IAEzCnB,EADI,CAAAiB,YAAA,EAAI,EACH;IACLjB,EAAA,CAAAC,cAAA,cAAiK;IAApCD,EAAA,CAAAY,UAAA,mBAAA2E,yEAAA;MAAA,MAAAC,UAAA,GAAAxF,EAAA,CAAAM,aAAA,CAAA+E,IAAA,EAAA/D,MAAA;MAAA,MAAAR,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAQ,UAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAW,SAAA,CAAAF,UAAA,EAAAiE,UAAA,CAAyB;IAAA,EAAC;IAC5JxF,EAAA,CAAAC,cAAA,aAA4E;IACxED,EAAA,CAAAmB,SAAA,aAAoC;IAE5CnB,EADI,CAAAiB,YAAA,EAAI,EACH;IAEDjB,EADJ,CAAAC,cAAA,cAA+H,aACP;IAAvCD,EAAA,CAAAY,UAAA,mBAAA6E,wEAAA;MAAA,MAAAL,QAAA,GAAApF,EAAA,CAAAM,aAAA,CAAA+E,IAAA,EAAA7E,SAAA;MAAA,MAAAM,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAY,cAAA,GAAA3B,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAc,OAAA,CAAAD,cAAA,EAAAyD,QAAA,CAAAvD,EAAA,CAA4B;IAAA,EAAC;IAC/G7B,EAAA,CAAAmB,SAAA,aAA0C;IAK9DnB,EAJgB,CAAAiB,YAAA,EAAI,EACH,EACJ,EACJ,EACJ;;;;IA7CDjB,EAAA,CAAA8B,sBAAA,aAAAsD,QAAA,CAAAvD,EAAA,KAAkB;IAGsD7B,EAAA,CAAA+B,SAAA,GAAmB;IAAnB/B,EAAA,CAAAgC,qBAAA,UAAAoD,QAAA,CAAAvD,EAAA,CAAmB;IAAC7B,EAAA,CAAAiC,gBAAA,YAAAmD,QAAA,CAAA1E,KAAA,CAAwB;IAGxCV,EAAA,CAAA+B,SAAA,GAAc;IAAd/B,EAAA,CAAAkC,kBAAA,QAAAkD,QAAA,CAAAvD,EAAA,KAAc;IAKzE7B,EAAA,CAAA+B,SAAA,GAAkB;IAAlB/B,EAAA,CAAAgC,qBAAA,QAAAoD,QAAA,CAAAjD,GAAA,EAAAnC,EAAA,CAAAoC,aAAA,CAAkB;IAEGpC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAA+C,QAAA,CAAA9C,IAAA,CAAa;IAG3BtC,EAAA,CAAA+B,SAAA,GAAoB;IAApB/B,EAAA,CAAAqC,iBAAA,CAAA+C,QAAA,CAAA7C,WAAA,CAAoB;IAC3BvC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAA+C,QAAA,CAAA5C,IAAA,CAAa;IACTxC,EAAA,CAAA+B,SAAA,GAAiB;IAAjB/B,EAAA,CAAAqC,iBAAA,CAAA+C,QAAA,CAAA3C,QAAA,CAAiB;IACrBzC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAA+C,QAAA,CAAA1C,IAAA,CAAa;IAES1C,EAAA,CAAA+B,SAAA,GACyF;IADzF/B,EAAA,CAAA2C,UAAA,YAAA3C,EAAA,CAAA4C,eAAA,KAAAC,GAAA,EAAAuC,QAAA,CAAAtC,MAAA,gBAAAsC,QAAA,CAAAtC,MAAA,WAAAsC,QAAA,CAAAtC,MAAA,eAAAsC,QAAA,CAAAtC,MAAA,gBACyF;IACxH9C,EAAA,CAAA+B,SAAA,EAAe;IAAf/B,EAAA,CAAAkC,kBAAA,MAAAkD,QAAA,CAAAtC,MAAA,KAAe;;;;;;IA1Cf9C,EALpB,CAAAC,cAAA,gBAAiE,gBACvB,aACP,aACc,cACT,gBACyH;IAAhED,EAAA,CAAAE,gBAAA,2BAAAwF,4EAAAtF,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAS,kBAAA,CAAAK,MAAA,CAAAmC,cAAA,EAAA7C,MAAA,MAAAU,MAAA,CAAAmC,cAAA,GAAA7C,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA4B;IAACJ,EAAA,CAAAY,UAAA,oBAAAgF,qEAAAxF,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAqC,eAAA,CAAA/C,MAAA,CAAuB;IAAA,EAAC;IAEpJJ,EAFQ,CAAAiB,YAAA,EAA6I,EAC3I,EACL;IACLjB,EAAA,CAAAC,cAAA,aAA8D;IAA7CD,EAAA,CAAAY,UAAA,mBAAAiF,iEAAA;MAAA7F,EAAA,CAAAM,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,IAAI,CAAC;IAAA,EAAC;IAAuBrD,EAAA,CAAAkB,MAAA,qBAAc;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACjFjB,EAAA,CAAAC,cAAA,aAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAAkF,iEAAA;MAAA9F,EAAA,CAAAM,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,oBAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAAiD;IAAhCD,EAAA,CAAAY,UAAA,mBAAAmF,kEAAA;MAAA/F,EAAA,CAAAM,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,aAAa,CAAC;IAAA,EAAC;IAC5CrD,EAAA,CAAAkB,MAAA,oBAAW;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACpBjB,EAAA,CAAAC,cAAA,cAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAAoF,kEAAA;MAAAhG,EAAA,CAAAM,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,mBAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAA8C;IAA7BD,EAAA,CAAAY,UAAA,mBAAAqF,kEAAA;MAAAjG,EAAA,CAAAM,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,UAAU,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,iBAC9C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAAsF,kEAAA;MAAAlG,EAAA,CAAAM,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,YAAI;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACnDjB,EAAA,CAAAC,cAAA,cAA4C;IAA3BD,EAAA,CAAAY,UAAA,mBAAAuF,kEAAA;MAAAnG,EAAA,CAAAM,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,QAAQ,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,cAAM;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACvDjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,cAAM;IAE/BlB,EAF+B,CAAAiB,YAAA,EAAK,EAC3B,EACD;IACRjB,EAAA,CAAAC,cAAA,iBAAmC;IAC/BD,EAAA,CAAA4D,gBAAA,KAAAwC,mDAAA,oBAAApG,EAAA,CAAA8D,sBAAA,CA+CC;IAET9D,EADI,CAAAiB,YAAA,EAAQ,EACJ;;;;IAnEyFjB,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAiC,gBAAA,YAAAnB,MAAA,CAAAmC,cAAA,CAA4B;IAkBrHjD,EAAA,CAAA+B,SAAA,IA+CC;IA/CD/B,EAAA,CAAA+D,UAAA,CAAAjD,MAAA,CAAAkD,YAAA,CA+CC;;;;;;IAqCWhE,EAHZ,CAAAC,cAAA,aAAuB,aACH,cACY,gBAC6H;IAA7DD,EAAA,CAAAE,gBAAA,2BAAAmG,mFAAAjG,MAAA;MAAA,MAAAkG,QAAA,GAAAtG,EAAA,CAAAM,aAAA,CAAAiG,IAAA,EAAA/F,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAA6F,QAAA,CAAA5F,KAAA,EAAAN,MAAA,MAAAkG,QAAA,CAAA5F,KAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAACJ,EAAA,CAAAY,UAAA,oBAAA4F,4EAAApG,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAiG,IAAA;MAAA,MAAAzF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAE,gBAAA,CAAAZ,MAAA,CAAwB;IAAA,EAAC;IAExJJ,EAFQ,CAAAiB,YAAA,EAAiJ,EAC/I,EACL;IACUjB,EAAf,CAAAC,cAAA,aAAe,YAA6D;IAAAD,EAAA,CAAAkB,MAAA,GAAc;IAC1FlB,EAD0F,CAAAiB,YAAA,EAAI,EACzF;IAGGjB,EAFR,CAAAC,cAAA,aAAoB,cACuB,cACR;IACvBD,EAAA,CAAAmB,SAAA,eAA4F;IAChGnB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAEnDlB,EAFmD,CAAAiB,YAAA,EAAM,EAC/C,EACL;IACLjB,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAkB,MAAA,IAAoB;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACjDjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACnCjB,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAkB,MAAA,IAAiB;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IAC3CjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IAE/BjB,EADJ,CAAAC,cAAA,cAAmB,gBAE8G;IACzHD,EAAA,CAAAkB,MAAA,IAAe;IAEvBlB,EAFuB,CAAAiB,YAAA,EAAO,EAEzB;IAIOjB,EAHZ,CAAAC,cAAA,UAAI,cAC0C,cAC6E,aACvC;IACpED,EAAA,CAAAmB,SAAA,aAAiC;IAEzCnB,EADI,CAAAiB,YAAA,EAAI,EACH;IACLjB,EAAA,CAAAC,cAAA,cAAiK;IAApCD,EAAA,CAAAY,UAAA,mBAAA6F,yEAAA;MAAA,MAAAC,UAAA,GAAA1G,EAAA,CAAAM,aAAA,CAAAiG,IAAA,EAAAjF,MAAA;MAAA,MAAAR,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAQ,UAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAW,SAAA,CAAAF,UAAA,EAAAmF,UAAA,CAAyB;IAAA,EAAC;IAC5J1G,EAAA,CAAAC,cAAA,aAA4E;IACxED,EAAA,CAAAmB,SAAA,aAAoC;IAE5CnB,EADI,CAAAiB,YAAA,EAAI,EACH;IAEDjB,EADJ,CAAAC,cAAA,cAA+H,aACP;IAAvCD,EAAA,CAAAY,UAAA,mBAAA+F,wEAAA;MAAA,MAAAL,QAAA,GAAAtG,EAAA,CAAAM,aAAA,CAAAiG,IAAA,EAAA/F,SAAA;MAAA,MAAAM,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAY,cAAA,GAAA3B,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAc,OAAA,CAAAD,cAAA,EAAA2E,QAAA,CAAAzE,EAAA,CAA4B;IAAA,EAAC;IAC/G7B,EAAA,CAAAmB,SAAA,aAA0C;IAK9DnB,EAJgB,CAAAiB,YAAA,EAAI,EACH,EACJ,EACJ,EACJ;;;;IA7CDjB,EAAA,CAAA8B,sBAAA,aAAAwE,QAAA,CAAAzE,EAAA,KAAkB;IAGsD7B,EAAA,CAAA+B,SAAA,GAAmB;IAAnB/B,EAAA,CAAAgC,qBAAA,UAAAsE,QAAA,CAAAzE,EAAA,CAAmB;IAAC7B,EAAA,CAAAiC,gBAAA,YAAAqE,QAAA,CAAA5F,KAAA,CAAwB;IAGxCV,EAAA,CAAA+B,SAAA,GAAc;IAAd/B,EAAA,CAAAkC,kBAAA,QAAAoE,QAAA,CAAAzE,EAAA,KAAc;IAKzE7B,EAAA,CAAA+B,SAAA,GAAkB;IAAlB/B,EAAA,CAAAgC,qBAAA,QAAAsE,QAAA,CAAAnE,GAAA,EAAAnC,EAAA,CAAAoC,aAAA,CAAkB;IAEGpC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAAiE,QAAA,CAAAhE,IAAA,CAAa;IAG3BtC,EAAA,CAAA+B,SAAA,GAAoB;IAApB/B,EAAA,CAAAqC,iBAAA,CAAAiE,QAAA,CAAA/D,WAAA,CAAoB;IAC3BvC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAAiE,QAAA,CAAA9D,IAAA,CAAa;IACTxC,EAAA,CAAA+B,SAAA,GAAiB;IAAjB/B,EAAA,CAAAqC,iBAAA,CAAAiE,QAAA,CAAA7D,QAAA,CAAiB;IACrBzC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAAiE,QAAA,CAAA5D,IAAA,CAAa;IAES1C,EAAA,CAAA+B,SAAA,GACyF;IADzF/B,EAAA,CAAA2C,UAAA,YAAA3C,EAAA,CAAA4C,eAAA,KAAAC,GAAA,EAAAyD,QAAA,CAAAxD,MAAA,gBAAAwD,QAAA,CAAAxD,MAAA,WAAAwD,QAAA,CAAAxD,MAAA,eAAAwD,QAAA,CAAAxD,MAAA,gBACyF;IACxH9C,EAAA,CAAA+B,SAAA,EAAe;IAAf/B,EAAA,CAAAkC,kBAAA,MAAAoE,QAAA,CAAAxD,MAAA,KAAe;;;;;;IA1Cf9C,EALpB,CAAAC,cAAA,gBAAiE,gBACvB,aACP,aACc,cACT,gBACyH;IAAhED,EAAA,CAAAE,gBAAA,2BAAA0G,4EAAAxG,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAuG,IAAA;MAAA,MAAA/F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAS,kBAAA,CAAAK,MAAA,CAAAmC,cAAA,EAAA7C,MAAA,MAAAU,MAAA,CAAAmC,cAAA,GAAA7C,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA4B;IAACJ,EAAA,CAAAY,UAAA,oBAAAkG,qEAAA1G,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAuG,IAAA;MAAA,MAAA/F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAqC,eAAA,CAAA/C,MAAA,CAAuB;IAAA,EAAC;IAEpJJ,EAFQ,CAAAiB,YAAA,EAA6I,EAC3I,EACL;IACLjB,EAAA,CAAAC,cAAA,aAA8D;IAA7CD,EAAA,CAAAY,UAAA,mBAAAmG,iEAAA;MAAA/G,EAAA,CAAAM,aAAA,CAAAuG,IAAA;MAAA,MAAA/F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,IAAI,CAAC;IAAA,EAAC;IAAuBrD,EAAA,CAAAkB,MAAA,qBAAc;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACjFjB,EAAA,CAAAC,cAAA,aAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAAoG,iEAAA;MAAAhH,EAAA,CAAAM,aAAA,CAAAuG,IAAA;MAAA,MAAA/F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,oBAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAAiD;IAAhCD,EAAA,CAAAY,UAAA,mBAAAqG,kEAAA;MAAAjH,EAAA,CAAAM,aAAA,CAAAuG,IAAA;MAAA,MAAA/F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,aAAa,CAAC;IAAA,EAAC;IAC5CrD,EAAA,CAAAkB,MAAA,oBAAW;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACpBjB,EAAA,CAAAC,cAAA,cAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAAsG,kEAAA;MAAAlH,EAAA,CAAAM,aAAA,CAAAuG,IAAA;MAAA,MAAA/F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,mBAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAA8C;IAA7BD,EAAA,CAAAY,UAAA,mBAAAuG,kEAAA;MAAAnH,EAAA,CAAAM,aAAA,CAAAuG,IAAA;MAAA,MAAA/F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,UAAU,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,iBAC9C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAAwG,kEAAA;MAAApH,EAAA,CAAAM,aAAA,CAAAuG,IAAA;MAAA,MAAA/F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,YAAI;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACnDjB,EAAA,CAAAC,cAAA,cAA4C;IAA3BD,EAAA,CAAAY,UAAA,mBAAAyG,kEAAA;MAAArH,EAAA,CAAAM,aAAA,CAAAuG,IAAA;MAAA,MAAA/F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,QAAQ,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,cAAM;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACvDjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,cAAM;IAE/BlB,EAF+B,CAAAiB,YAAA,EAAK,EAC3B,EACD;IACRjB,EAAA,CAAAC,cAAA,iBAAmC;IAC/BD,EAAA,CAAA4D,gBAAA,KAAA0D,mDAAA,oBAAAtH,EAAA,CAAA8D,sBAAA,CA+CC;IAET9D,EADI,CAAAiB,YAAA,EAAQ,EACJ;;;;IAnEyFjB,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAiC,gBAAA,YAAAnB,MAAA,CAAAmC,cAAA,CAA4B;IAkBrHjD,EAAA,CAAA+B,SAAA,IA+CC;IA/CD/B,EAAA,CAAA+D,UAAA,CAAAjD,MAAA,CAAAkD,YAAA,CA+CC;;;;;;IAqCWhE,EAHZ,CAAAC,cAAA,aAAuB,aACH,cACY,gBAC6H;IAA7DD,EAAA,CAAAE,gBAAA,2BAAAqH,mFAAAnH,MAAA;MAAA,MAAAoH,QAAA,GAAAxH,EAAA,CAAAM,aAAA,CAAAmH,IAAA,EAAAjH,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAA+G,QAAA,CAAA9G,KAAA,EAAAN,MAAA,MAAAoH,QAAA,CAAA9G,KAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAACJ,EAAA,CAAAY,UAAA,oBAAA8G,4EAAAtH,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAmH,IAAA;MAAA,MAAA3G,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAE,gBAAA,CAAAZ,MAAA,CAAwB;IAAA,EAAC;IAExJJ,EAFQ,CAAAiB,YAAA,EAAiJ,EAC/I,EACL;IACUjB,EAAf,CAAAC,cAAA,aAAe,YAA6D;IAAAD,EAAA,CAAAkB,MAAA,GAAc;IAC1FlB,EAD0F,CAAAiB,YAAA,EAAI,EACzF;IAGGjB,EAFR,CAAAC,cAAA,aAAoB,cACuB,cACR;IACvBD,EAAA,CAAAmB,SAAA,eAA4F;IAChGnB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAEnDlB,EAFmD,CAAAiB,YAAA,EAAM,EAC/C,EACL;IACLjB,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAkB,MAAA,IAAoB;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACjDjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACnCjB,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAkB,MAAA,IAAiB;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IAC3CjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,IAAa;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IAE/BjB,EADJ,CAAAC,cAAA,cAAmB,gBAE8G;IACzHD,EAAA,CAAAkB,MAAA,IAAe;IAEvBlB,EAFuB,CAAAiB,YAAA,EAAO,EAEzB;IAIOjB,EAHZ,CAAAC,cAAA,UAAI,cAC0C,cAC6E,aACvC;IACpED,EAAA,CAAAmB,SAAA,aAAiC;IAEzCnB,EADI,CAAAiB,YAAA,EAAI,EACH;IACLjB,EAAA,CAAAC,cAAA,cAAiK;IAApCD,EAAA,CAAAY,UAAA,mBAAA+G,yEAAA;MAAA,MAAAC,UAAA,GAAA5H,EAAA,CAAAM,aAAA,CAAAmH,IAAA,EAAAnG,MAAA;MAAA,MAAAR,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAQ,UAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAW,SAAA,CAAAF,UAAA,EAAAqG,UAAA,CAAyB;IAAA,EAAC;IAC5J5H,EAAA,CAAAC,cAAA,aAA4E;IACxED,EAAA,CAAAmB,SAAA,aAAoC;IAE5CnB,EADI,CAAAiB,YAAA,EAAI,EACH;IAEDjB,EADJ,CAAAC,cAAA,cAA+H,aACP;IAAvCD,EAAA,CAAAY,UAAA,mBAAAiH,wEAAA;MAAA,MAAAL,QAAA,GAAAxH,EAAA,CAAAM,aAAA,CAAAmH,IAAA,EAAAjH,SAAA;MAAA,MAAAM,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAY,cAAA,GAAA3B,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAc,OAAA,CAAAD,cAAA,EAAA6F,QAAA,CAAA3F,EAAA,CAA4B;IAAA,EAAC;IAC/G7B,EAAA,CAAAmB,SAAA,aAA0C;IAK9DnB,EAJgB,CAAAiB,YAAA,EAAI,EACH,EACJ,EACJ,EACJ;;;;IA7CDjB,EAAA,CAAA8B,sBAAA,aAAA0F,QAAA,CAAA3F,EAAA,KAAkB;IAGsD7B,EAAA,CAAA+B,SAAA,GAAmB;IAAnB/B,EAAA,CAAAgC,qBAAA,UAAAwF,QAAA,CAAA3F,EAAA,CAAmB;IAAC7B,EAAA,CAAAiC,gBAAA,YAAAuF,QAAA,CAAA9G,KAAA,CAAwB;IAGxCV,EAAA,CAAA+B,SAAA,GAAc;IAAd/B,EAAA,CAAAkC,kBAAA,QAAAsF,QAAA,CAAA3F,EAAA,KAAc;IAKzE7B,EAAA,CAAA+B,SAAA,GAAkB;IAAlB/B,EAAA,CAAAgC,qBAAA,QAAAwF,QAAA,CAAArF,GAAA,EAAAnC,EAAA,CAAAoC,aAAA,CAAkB;IAEGpC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAAmF,QAAA,CAAAlF,IAAA,CAAa;IAG3BtC,EAAA,CAAA+B,SAAA,GAAoB;IAApB/B,EAAA,CAAAqC,iBAAA,CAAAmF,QAAA,CAAAjF,WAAA,CAAoB;IAC3BvC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAAmF,QAAA,CAAAhF,IAAA,CAAa;IACTxC,EAAA,CAAA+B,SAAA,GAAiB;IAAjB/B,EAAA,CAAAqC,iBAAA,CAAAmF,QAAA,CAAA/E,QAAA,CAAiB;IACrBzC,EAAA,CAAA+B,SAAA,GAAa;IAAb/B,EAAA,CAAAqC,iBAAA,CAAAmF,QAAA,CAAA9E,IAAA,CAAa;IAES1C,EAAA,CAAA+B,SAAA,GACyF;IADzF/B,EAAA,CAAA2C,UAAA,YAAA3C,EAAA,CAAA4C,eAAA,KAAAC,GAAA,EAAA2E,QAAA,CAAA1E,MAAA,gBAAA0E,QAAA,CAAA1E,MAAA,WAAA0E,QAAA,CAAA1E,MAAA,eAAA0E,QAAA,CAAA1E,MAAA,gBACyF;IACxH9C,EAAA,CAAA+B,SAAA,EAAe;IAAf/B,EAAA,CAAAkC,kBAAA,MAAAsF,QAAA,CAAA1E,MAAA,KAAe;;;;;;IA1Cf9C,EALpB,CAAAC,cAAA,gBAAiE,gBACvB,aACP,aACc,cACT,gBACyH;IAAhED,EAAA,CAAAE,gBAAA,2BAAA4H,4EAAA1H,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAyH,IAAA;MAAA,MAAAjH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAS,kBAAA,CAAAK,MAAA,CAAAmC,cAAA,EAAA7C,MAAA,MAAAU,MAAA,CAAAmC,cAAA,GAAA7C,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA4B;IAACJ,EAAA,CAAAY,UAAA,oBAAAoH,qEAAA5H,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAyH,IAAA;MAAA,MAAAjH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAqC,eAAA,CAAA/C,MAAA,CAAuB;IAAA,EAAC;IAEpJJ,EAFQ,CAAAiB,YAAA,EAA6I,EAC3I,EACL;IACLjB,EAAA,CAAAC,cAAA,aAA8D;IAA7CD,EAAA,CAAAY,UAAA,mBAAAqH,iEAAA;MAAAjI,EAAA,CAAAM,aAAA,CAAAyH,IAAA;MAAA,MAAAjH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,IAAI,CAAC;IAAA,EAAC;IAAuBrD,EAAA,CAAAkB,MAAA,qBAAc;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACjFjB,EAAA,CAAAC,cAAA,aAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAAsH,iEAAA;MAAAlI,EAAA,CAAAM,aAAA,CAAAyH,IAAA;MAAA,MAAAjH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,oBAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAAiD;IAAhCD,EAAA,CAAAY,UAAA,mBAAAuH,kEAAA;MAAAnI,EAAA,CAAAM,aAAA,CAAAyH,IAAA;MAAA,MAAAjH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,aAAa,CAAC;IAAA,EAAC;IAC5CrD,EAAA,CAAAkB,MAAA,oBAAW;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACpBjB,EAAA,CAAAC,cAAA,cAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAAwH,kEAAA;MAAApI,EAAA,CAAAM,aAAA,CAAAyH,IAAA;MAAA,MAAAjH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,mBAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAA8C;IAA7BD,EAAA,CAAAY,UAAA,mBAAAyH,kEAAA;MAAArI,EAAA,CAAAM,aAAA,CAAAyH,IAAA;MAAA,MAAAjH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,UAAU,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,iBAC9C;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACLjB,EAAA,CAAAC,cAAA,cAA0C;IAAzBD,EAAA,CAAAY,UAAA,mBAAA0H,kEAAA;MAAAtI,EAAA,CAAAM,aAAA,CAAAyH,IAAA;MAAA,MAAAjH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,MAAM,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,YAAI;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACnDjB,EAAA,CAAAC,cAAA,cAA4C;IAA3BD,EAAA,CAAAY,UAAA,mBAAA2H,kEAAA;MAAAvI,EAAA,CAAAM,aAAA,CAAAyH,IAAA;MAAA,MAAAjH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAuC,MAAA,CAAO,QAAQ,CAAC;IAAA,EAAC;IAACrD,EAAA,CAAAkB,MAAA,cAAM;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACvDjB,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAkB,MAAA,cAAM;IAE/BlB,EAF+B,CAAAiB,YAAA,EAAK,EAC3B,EACD;IACRjB,EAAA,CAAAC,cAAA,iBAAmC;IAC/BD,EAAA,CAAA4D,gBAAA,KAAA4E,mDAAA,oBAAAxI,EAAA,CAAA8D,sBAAA,CA+CC;IAET9D,EADI,CAAAiB,YAAA,EAAQ,EACJ;;;;IAnEyFjB,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAiC,gBAAA,YAAAnB,MAAA,CAAAmC,cAAA,CAA4B;IAkBrHjD,EAAA,CAAA+B,SAAA,IA+CC;IA/CD/B,EAAA,CAAA+D,UAAA,CAAAjD,MAAA,CAAAkD,YAAA,CA+CC;;;;;IAuBbhE,EAAA,CAAAmB,SAAA,aAAkC;IAClCnB,EAAA,CAAAkB,MAAA,aACJ;;;;;IAEIlB,EAAA,CAAAkB,MAAA,aACA;IAAAlB,EAAA,CAAAmB,SAAA,aAAmC;;;;;;IAcvCnB,EADJ,CAAAC,cAAA,eAAuC,cACY;IAAAD,EAAA,CAAAkB,MAAA,4BAAqB;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IACzEjB,EAAA,CAAAC,cAAA,kBAAwH;IAAxDD,EAAA,CAAAY,UAAA,mBAAA6H,sEAAA;MAAA,MAAAC,SAAA,GAAA1I,EAAA,CAAAM,aAAA,CAAAqI,IAAA,EAAAnI,SAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAS+H,SAAA,CAAAE,OAAA,CAAc,aAAa,CAAC;IAAA,EAAC;IAC1G5I,EAD4H,CAAAiB,YAAA,EAAS,EAC/H;IACNjB,EAAA,CAAAC,cAAA,gBAAmG;IAA/DD,EAAA,CAAAY,UAAA,sBAAAiI,uEAAA;MAAA7I,EAAA,CAAAM,aAAA,CAAAqI,IAAA;MAAA,MAAA7H,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAYG,MAAA,CAAAgI,iBAAA,EAAmB;IAAA,EAAC;IAIxD9I,EAHR,CAAAC,cAAA,eAAwB,eAEmB,iBACW;IAAAD,EAAA,CAAAkB,MAAA,SAAE;IAAAlB,EAAA,CAAAiB,YAAA,EAAQ;IACxDjB,EAAA,CAAAmB,SAAA,iBAA8G;IAClHnB,EAAA,CAAAiB,YAAA,EAAM;IAOcjB,EALpB,CAAAC,cAAA,eAAyB,gBACyB,gBACK,kBACoF,gBACrF,gBACkC;IAChED,EAAA,CAAAmB,SAAA,cAA6B;IAGzCnB,EAFQ,CAAAiB,YAAA,EAAM,EACJ,EACF;IACRjB,EAAA,CAAAC,cAAA,kBAA6J;IAA9BD,EAAA,CAAAY,UAAA,oBAAAmI,uEAAA3I,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAqI,IAAA;MAAA,MAAA7H,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAkI,UAAA,CAAA5I,MAAA,CAAkB;IAAA,EAAC;IAChKJ,EADI,CAAAiB,YAAA,EAA6J,EAC3J;IAEFjB,EADJ,CAAAC,cAAA,gBAA2B,gBAC2B;IAC9CD,EAAA,CAAAmB,SAAA,gBAA8H;IAI9InB,EAHY,CAAAiB,YAAA,EAAM,EACJ,EACJ,EACJ;IAGFjB,EADJ,CAAAC,cAAA,gBAAkB,kBACgC;IAAAD,EAAA,CAAAkB,MAAA,eAAO;IAAAlB,EAAA,CAAAiB,YAAA,EAAQ;IAC7DjB,EAAA,CAAAmB,SAAA,kBAA8H;IAClInB,EAAA,CAAAiB,YAAA,EAAM;IAGFjB,EADJ,CAAAC,cAAA,gBAAkB,kBACoC;IAAAD,EAAA,CAAAkB,MAAA,mBAAW;IAAAlB,EAAA,CAAAiB,YAAA,EAAQ;IACrEjB,EAAA,CAAAmB,SAAA,kBAAwI;IAC5InB,EAAA,CAAAiB,YAAA,EAAM;IAGFjB,EADJ,CAAAC,cAAA,gBAAkB,kBAC6B;IAAAD,EAAA,CAAAkB,MAAA,kBAAU;IAAAlB,EAAA,CAAAiB,YAAA,EAAQ;IAC7DjB,EAAA,CAAAmB,SAAA,kBAA4J;IAChKnB,EAAA,CAAAiB,YAAA,EAAM;IAGFjB,EADJ,CAAAC,cAAA,gBAAkB,kBACgC;IAAAD,EAAA,CAAAkB,MAAA,gBAAQ;IAAAlB,EAAA,CAAAiB,YAAA,EAAQ;IAC9DjB,EAAA,CAAAmB,SAAA,kBAA6H;IACjInB,EAAA,CAAAiB,YAAA,EAAM;IAKMjB,EAHZ,CAAAC,cAAA,gBAA2B,gBACD,WACb,kBAC4C;IAAAD,EAAA,CAAAkB,MAAA,cAAM;IAAAlB,EAAA,CAAAiB,YAAA,EAAQ;IAEvDjB,EADJ,CAAAC,cAAA,mBAAyG,kBACpF;IAAAD,EAAA,CAAAkB,MAAA,cAAM;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IAChCjB,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAkB,MAAA,gBAAQ;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IAC1CjB,EAAA,CAAAC,cAAA,kBAAoB;IAAAD,EAAA,CAAAkB,MAAA,WAAG;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IAChCjB,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAkB,MAAA,eAAO;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IACxCjB,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAkB,MAAA,gBAAQ;IAG7ClB,EAH6C,CAAAiB,YAAA,EAAS,EACrC,EACP,EACJ;IAGEjB,EAFR,CAAAC,cAAA,gBAAsB,WACb,kBAC0C;IAAAD,EAAA,CAAAkB,MAAA,YAAI;IAAAlB,EAAA,CAAAiB,YAAA,EAAQ;IAEnDjB,EADJ,CAAAC,cAAA,mBAAmG,kBAC9E;IAAAD,EAAA,CAAAkB,MAAA,mBAAW;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IACrCjB,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAkB,MAAA,iBAAS;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IAC5CjB,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAkB,MAAA,iBAAS;IAOvDlB,EAPuD,CAAAiB,YAAA,EAAS,EACvC,EACP,EACJ,EACJ,EAGJ;IAGEjB,EAFR,CAAAC,cAAA,gBAA0B,gBACwB,mBACiE;IAAvCD,EAAA,CAAAY,UAAA,mBAAAqI,uEAAA;MAAA,MAAAP,SAAA,GAAA1I,EAAA,CAAAM,aAAA,CAAAqI,IAAA,EAAAnI,SAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAS+H,SAAA,CAAAE,OAAA,CAAc,aAAa,CAAC;IAAA,EAAC;IAAC5I,EAAA,CAAAkB,MAAA,aAAK;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IACzHjB,EAAA,CAAAC,cAAA,mBAA2D;IAAAD,EAAA,CAAAkB,MAAA,WAAG;IAG1ElB,EAH0E,CAAAiB,YAAA,EAAS,EACrE,EACJ,EACH;;;;IAjF8DjB,EAAA,CAAA+B,SAAA,GAA6B;IAA7B/B,EAAA,CAAA2C,UAAA,cAAA7B,MAAA,CAAAoI,eAAA,CAA6B;IAwCjBlJ,EAAA,CAAA+B,SAAA,IAA0B;IAA1B/B,EAAA,CAAA2C,UAAA,2BAA0B;;;;;;IA+C3G3C,EAAA,CAAAC,cAAA,eAAwC;IACpCD,EAAA,CAAAmB,SAAA,qBACY;IAERnB,EADJ,CAAAC,cAAA,eAA8B,SACtB;IAAAD,EAAA,CAAAkB,MAAA,wCAAiC;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IAC1CjB,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAkB,MAAA,iFACC;IAAAlB,EAAA,CAAAiB,YAAA,EAAI;IAElCjB,EADJ,CAAAC,cAAA,eAAwD,kBAC4G;IAAvCD,EAAA,CAAAY,UAAA,mBAAAuI,sEAAA;MAAA,MAAAC,SAAA,GAAApJ,EAAA,CAAAM,aAAA,CAAA+I,IAAA,EAAA7I,SAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASyI,SAAA,CAAAR,OAAA,CAAc,aAAa,CAAC;IAAA,EAAC;IAAC5I,EAAA,CAAAmB,SAAA,aAA+C;IAC3MnB,EAAA,CAAAkB,MAAA,cAAK;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IAClBjB,EAAA,CAAAC,cAAA,mBAAiF;IAA/BD,EAAA,CAAAY,UAAA,mBAAA0I,uEAAA;MAAAtJ,EAAA,CAAAM,aAAA,CAAA+I,IAAA;MAAA,MAAAvI,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAyI,UAAA,CAAAzI,MAAA,CAAA0I,QAAA,CAAoB;IAAA,EAAC;IAACxJ,EAAA,CAAAkB,MAAA,sBAE3E;IAGlBlB,EAHkB,CAAAiB,YAAA,EAAS,EACb,EACJ,EACJ;;;ADrlB1B,OAAM,MAAOwI,oBAAoB;EAiB/BC,YAAmBC,OAA0B,EACpCC,WAA+B,EAC9BC,KAAwC,EACzCC,YAAsB;IAHZ,KAAAH,OAAO,GAAPA,OAAO;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,KAAK,GAALA,KAAK;IACN,KAAAC,YAAY,GAAZA,YAAY;IAXrB,KAAAC,SAAS,GAAG,KAAK;IAKjB,KAAAjH,MAAM,GAAQ,EAAE;IAChB,KAAAJ,IAAI,GAAQ,EAAE;IAgGd;IACA,KAAAsH,aAAa,GAAU,EAAE;EA3FzB;EAEAC,QAAQA,CAAA;IACN;;;IAGA,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE;IAAM,CAAE,EACjB;MAAEA,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAE;IAAI,CAAE,CACvC;IAED;IACA,IAAI,CAAClB,eAAe,GAAG,IAAI,CAACU,WAAW,CAACS,KAAK,CAAC;MAC5CxI,EAAE,EAAE,CAAC,EAAE,CAAC;MACRS,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC9C,UAAU,CAAC8K,QAAQ,CAAC,CAAC;MACjC9H,IAAI,EAAE,CAAC,EAAE,EAAE,CAAChD,UAAU,CAAC8K,QAAQ,CAAC,CAAC;MACjC5H,IAAI,EAAE,CAAC,EAAE,EAAE,CAAClD,UAAU,CAAC8K,QAAQ,CAAC,CAAC;MACjC/H,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC/C,UAAU,CAAC8K,QAAQ,CAAC,CAAC;MACxC7H,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACjD,UAAU,CAAC8K,QAAQ,CAAC,CAAC;MACrCxH,MAAM,EAAE,CAAC,EAAE,EAAE,CAACtD,UAAU,CAAC8K,QAAQ,CAAC;KACnC,CAAC;IAEF,IAAI,CAACT,KAAK,CAACU,QAAQ,CAAC5K,oBAAoB,EAAE,CAAC;IAC3C,IAAI,CAACkK,KAAK,CAACW,MAAM,CAAC1K,iBAAiB,CAAC,CAAC2K,SAAS,CAAEC,IAAI,IAAI;MACtD,IAAIA,IAAI,IAAI,KAAK,EAAE;QACjBC,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC,EAAEC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAC/D;IACF,CAAC,CAAC;IAEF,IAAI,CAACjB,KAAK,CAACW,MAAM,CAAC3K,cAAc,CAAC,CAAC4K,SAAS,CAAEC,IAAI,IAAI;MACnD,IAAI,CAAC1G,YAAY,GAAG0G,IAAI;MACxB,IAAI,CAACK,eAAe,GAAGhL,SAAS,CAAC2K,IAAI,CAAC;MACtC,IAAI,CAAC1G,YAAY,GAAG,IAAI,CAAC2F,OAAO,CAACqB,UAAU,CAAC,IAAI,CAACD,eAAe,CAAC;IACnE,CAAC,CAAC;EACJ;EAEA;EACAC,UAAUA,CAAA;IACR,IAAI,CAAChH,YAAY,GAAG,IAAI,CAAC2F,OAAO,CAACqB,UAAU,CAAC,IAAI,CAACD,eAAe,CAAC;EACnE;EAEA;EACAE,YAAYA,CAAA;IACV,IAAI,IAAI,CAACnI,MAAM,IAAI,EAAE,EAAE;MACrB,IAAI,CAACkB,YAAY,GAAG,IAAI,CAAC+G,eAAe,CAACG,MAAM,CAAEC,GAAQ,IAAI;QAC3D,OAAOA,GAAG,CAACrI,MAAM,KAAK,IAAI,CAACA,MAAM;MACnC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACkB,YAAY,GAAG,IAAI,CAAC2F,OAAO,CAACqB,UAAU,CAAC,IAAI,CAACD,eAAe,CAAC;IACnE;EACF;EAEAK,UAAUA,CAAA;IACR,IAAI,IAAI,CAAC1I,IAAI,IAAI,EAAE,EAAE;MACnB,IAAI,CAACsB,YAAY,GAAG,IAAI,CAAC+G,eAAe,CAACG,MAAM,CAAEC,GAAQ,IAAI;QAC3D,OAAOA,GAAG,CAACzI,IAAI,KAAK,IAAI,CAACA,IAAI;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACsB,YAAY,GAAG,IAAI,CAAC2F,OAAO,CAACqB,UAAU,CAAC,IAAI,CAACD,eAAe,CAAC;IACnE;EACF;EAEA;EACAM,aAAaA,CAAA;IACX,IAAI,CAACC,aAAa,GAAG,IAAI,CAACP,eAAe,CAACG,MAAM,CAAEK,IAAS,IAAI;MAC7D,OACEA,IAAI,CAACjJ,IAAI,CAACkJ,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACC,UAAU,CAACF,WAAW,EAAE,CAAC;IAEnE,CAAC,CAAC;IACF,IAAI,CAACxH,YAAY,GAAG,IAAI,CAAC2F,OAAO,CAACqB,UAAU,CAAC,IAAI,CAACM,aAAa,CAAC;EACjE;EAEAK,WAAWA,CAACC,WAA8B;IACxC,IAAIA,WAAW,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAAC7H,YAAY,GAAG,IAAI,CAAC2F,OAAO,CAACqB,UAAU,CAAC,IAAI,CAACD,eAAe,CAAC;IACnE;IACA,IAAIa,WAAW,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAAC7H,YAAY,GAAG,IAAI,CAAC+G,eAAe,CAACG,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACrI,MAAM,IAAI,KAAK,CAAC;IACpF;IACA,IAAI8I,WAAW,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAAC7H,YAAY,GAAG,IAAI,CAAC+G,eAAe,CAACG,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACrI,MAAM,IAAI,SAAS,CAAC;IACxF;IACA,IAAI8I,WAAW,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAAC7H,YAAY,GAAG,IAAI,CAAC+G,eAAe,CAACG,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACrI,MAAM,IAAI,UAAU,CAAC;IACzF;IACA,IAAI8I,WAAW,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAAC7H,YAAY,GAAG,IAAI,CAAC+G,eAAe,CAACG,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACrI,MAAM,IAAI,UAAU,CAAC;IACzF;EACF;EAIA;EACAK,eAAeA,CAAC2I,EAAO;IACrB,IAAI,CAAC9H,YAAY,CAAC+H,OAAO,CAAEC,CAAkB,IAAKA,CAAC,CAACtL,KAAK,GAAGoL,EAAE,CAACG,MAAM,CAACC,OAAO,CAAC;IAC9E,IAAIC,UAAU,GAAU,EAAE;IAC1B,IAAIC,MAAM;IACV,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrI,YAAY,CAACsI,MAAM,EAAED,CAAC,EAAE,EAAE;MACjD,IAAI,IAAI,CAACrI,YAAY,CAACqI,CAAC,CAAC,CAAC3L,KAAK,IAAI,IAAI,EAAE;QACtC0L,MAAM,GAAG,IAAI,CAACpI,YAAY,CAACqI,CAAC,CAAC,CAACxK,EAAE;QAChCsK,UAAU,CAACI,IAAI,CAACH,MAAM,CAAC;MACzB;IACF;IACA,IAAI,CAACpC,aAAa,GAAGmC,UAAU;IAC/BA,UAAU,CAACG,MAAM,GAAG,CAAC,GAAI3B,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAiB,CAAC4B,KAAK,CAACC,OAAO,GAAG,OAAO,GAAI9B,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAiB,CAAC4B,KAAK,CAACC,OAAO,GAAG,MAAM;EAEhM;EACAC,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC1I,YAAY,CAAC2I,KAAK,CAAEC,CAAkB,IAAKA,CAAC,CAAClM,KAAK,CAAC;EACjE;EAEA;EACAM,gBAAgBA,CAAC6L,CAAM;IACrB,IAAIV,UAAU,GAAU,EAAE;IAC1B,IAAIC,MAAM;IACV,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrI,YAAY,CAACsI,MAAM,EAAED,CAAC,EAAE,EAAE;MACjD,IAAI,IAAI,CAACrI,YAAY,CAACqI,CAAC,CAAC,CAAC3L,KAAK,IAAI,IAAI,EAAE;QACtC0L,MAAM,GAAG,IAAI,CAACpI,YAAY,CAACqI,CAAC,CAAC,CAACxK,EAAE;QAChCsK,UAAU,CAACI,IAAI,CAACH,MAAM,CAAC;MACzB;IACF;IACA,IAAI,CAACpC,aAAa,GAAGmC,UAAU;IAC/BA,UAAU,CAACG,MAAM,GAAG,CAAC,GAAI3B,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAiB,CAAC4B,KAAK,CAACC,OAAO,GAAG,OAAO,GAAI9B,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAiB,CAAC4B,KAAK,CAACC,OAAO,GAAG,MAAM;EAChM;EAEA;EACAK,SAASA,CAACC,OAAY;IACpB,IAAI,CAACjD,YAAY,CAACkD,IAAI,CAACD,OAAO,EAAE;MAAEE,IAAI,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EACjE;EAIAlE,UAAUA,CAACmE,KAAU;IACnB,IAAIC,QAAQ,GAASD,KAAK,CAAClB,MAA2B;IACtD,IAAIoB,IAAI,GAASD,QAAQ,CAACE,KAAK,CAAC,CAAC,CAAC;IAClC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;MACnB,IAAI,CAACC,QAAQ,GAAGH,MAAM,CAACnB,MAAgB;MACvCzB,QAAQ,CAACgD,gBAAgB,CAAC,kBAAkB,CAAC,CAAC5B,OAAO,CAAE6B,OAAY,IAAI;QACrEA,OAAO,CAACC,GAAG,GAAG,IAAI,CAACH,QAAQ;MAC7B,CAAC,CAAC;IACJ,CAAC;IACDH,MAAM,CAACO,aAAa,CAACT,IAAI,CAAC;EAC5B;EAOA5L,SAASA,CAACsL,OAAY,EAAElL,EAAO;IAC7B,IAAI,CAACkM,UAAU,GAAG,IAAI,CAAC/J,YAAY,CAACnC,EAAE,CAAC;IACvC,IAAI,CAACkI,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,YAAY,CAACkD,IAAI,CAACD,OAAO,EAAE;MAAEE,IAAI,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IAChE,IAAIc,UAAU,GAAGrD,QAAQ,CAACsD,aAAa,CAAC,cAAc,CAAoB;IAC1ED,UAAU,CAACE,SAAS,GAAG,kBAAkB;IACzC,IAAIC,SAAS,GAAGxD,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAoB;IACrEuD,SAAS,CAACD,SAAS,GAAG,QAAQ;IAC9BvD,QAAQ,CAACgD,gBAAgB,CAAC,kBAAkB,CAAC,CAAC5B,OAAO,CAAE6B,OAAY,IAAI;MACrEA,OAAO,CAACC,GAAG,GAAG,IAAI,CAACE,UAAU,CAAC5L,GAAG;IACnC,CAAC,CAAC;IACF,IAAI,CAAC+G,eAAe,CAACkF,QAAQ,CAAC,IAAI,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACN,UAAU,CAAClM,EAAE,CAAC;IAChE,IAAI,CAACqH,eAAe,CAACkF,QAAQ,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACN,UAAU,CAACzL,IAAI,CAAC;IACpE,IAAI,CAAC4G,eAAe,CAACkF,QAAQ,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACN,UAAU,CAACxL,WAAW,CAAC;IAClF,IAAI,CAAC2G,eAAe,CAACkF,QAAQ,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACN,UAAU,CAACvL,IAAI,CAAC;IACpE,IAAI,CAAC0G,eAAe,CAACkF,QAAQ,CAAC,UAAU,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACN,UAAU,CAACtL,QAAQ,CAAC;IAC5E,IAAI,CAACyG,eAAe,CAACkF,QAAQ,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACN,UAAU,CAACrL,IAAI,CAAC;IACpE,IAAI,CAACwG,eAAe,CAACkF,QAAQ,CAAC,QAAQ,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACN,UAAU,CAACjL,MAAM,CAAC;EAC1E;EAEA;;;EAGA,IAAIwL,IAAIA,CAAA;IACN,OAAO,IAAI,CAACpF,eAAe,CAACkF,QAAQ;EACtC;EAEAtF,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACI,eAAe,CAACqF,KAAK,EAAE;MAC9B,IAAI,IAAI,CAACrF,eAAe,CAACsF,GAAG,CAAC,IAAI,CAAC,EAAEC,KAAK,EAAE;QACzC,MAAMC,WAAW,GAAG;UAAEvM,GAAG,EAAE,IAAI,CAAC4L,UAAU,CAAC5L,GAAG;UAAE,GAAG,IAAI,CAAC+G,eAAe,CAACuF;QAAK,CAAE;QAC/E,IAAI,CAAC5E,KAAK,CAACU,QAAQ,CAAC3K,iBAAiB,CAAC;UAAE8O;QAAW,CAAE,CAAC,CAAC;QACvD,IAAI,CAAC5E,YAAY,CAAC6E,UAAU,EAAE;MAChC,CAAC,MAAM;QACL,MAAMrM,IAAI,GAAG,IAAI,CAAC4G,eAAe,CAACsF,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK;QACpD,MAAMlM,WAAW,GAAG,IAAI,CAAC2G,eAAe,CAACsF,GAAG,CAAC,aAAa,CAAC,EAAEC,KAAK;QAClE,MAAMhM,QAAQ,GAAG,IAAI,CAACyG,eAAe,CAACsF,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK;QAC5D,MAAMtM,GAAG,GAAG,iCAAiC;QAC7C,MAAMK,IAAI,GAAG,cAAc;QAC3B,MAAMM,MAAM,GAAG,IAAI,CAACoG,eAAe,CAACsF,GAAG,CAAC,QAAQ,CAAC,EAAEC,KAAK;QACxD,MAAM/L,IAAI,GAAG,IAAI,CAACwG,eAAe,CAACsF,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK;QACpD,MAAMG,OAAO,GAAG;UACd/M,EAAE,EAAE,IAAI,CAACmC,YAAY,CAACsI,MAAM,GAAG,CAAC;UAChCnK,GAAG;UACHG,IAAI;UACJC,WAAW;UACXC,IAAI;UACJC,QAAQ;UACRC,IAAI;UACJI;SACD;QACD,IAAI,CAAC+G,KAAK,CAACU,QAAQ,CAAC7K,cAAc,CAAC;UAAEkP;QAAO,CAAE,CAAC,CAAC;QAChD,IAAI,CAAC9E,YAAY,CAAC6E,UAAU,EAAE;MAChC;IACF;IACA,IAAI,CAAC7E,YAAY,CAAC6E,UAAU,EAAE;IAC9BE,UAAU,CAAC,MAAK;MACd,IAAI,CAAC3F,eAAe,CAAC4F,KAAK,EAAE;IAC9B,CAAC,EAAE,IAAI,CAAC;IACR,IAAI,CAAC/E,SAAS,GAAG,IAAI;EACvB;EAMAnI,OAAOA,CAACmL,OAAY,EAAElL,EAAO;IAC3B,IAAI,CAAC2H,QAAQ,GAAG3H,EAAE;IAClB,IAAI,CAACiI,YAAY,CAACkD,IAAI,CAACD,OAAO,EAAE;MAAEG,QAAQ,EAAE;IAAI,CAAE,CAAC;EACrD;EAEA;EACA3D,UAAUA,CAAC1H,EAAO;IAChB,IAAIA,EAAE,EAAE;MACN8I,QAAQ,CAACC,cAAc,CAAC,IAAI,GAAG/I,EAAE,CAAC,EAAEkN,MAAM,EAAE;IAC9C;IACA,IAAI,CAAC/E,aAAa,CAAC+B,OAAO,CAAER,IAAS,IAAI;MACvCZ,QAAQ,CAACC,cAAc,CAAC,IAAI,GAAGW,IAAI,CAAC,EAAEwD,MAAM,EAAE;MAC9C,IAAI,CAAC9L,cAAc,GAAG,KAAK;IAC7B,CAAC,CAAC;IACF,IAAI,CAAC6G,YAAY,CAAC6E,UAAU,CAAC,aAAa,CAAC;IAC3C,IAAIK,aAAkB;IACtBvP,IAAI,CAACwP,IAAI,CAAC;MACRC,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAE,6BAA6B;MACnCC,IAAI,EAAE,SAAS;MACfC,kBAAkB,EAAE,SAAS;MAC7BC,KAAK,EAAE,IAAI;MACXC,gBAAgB,EAAE,IAAI;MACtBC,SAAS,EAAEA,CAAA,KAAK;QACdC,aAAa,CAACT,aAAa,CAAC;MAC9B;KACD,CAAC;EACJ;EAEA;EACA3L,MAAMA,CAACqM,MAAW;IAChB,IAAI,CAAC1L,YAAY,GAAG,IAAI,CAAC2F,OAAO,CAACtG,MAAM,CAACqM,MAAM,EAAE,IAAI,CAAC3E,eAAe,CAAC;EACvE;;;uBA7QWtB,oBAAoB,EAAAzJ,EAAA,CAAA2P,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAA7P,EAAA,CAAA2P,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAA/P,EAAA,CAAA2P,iBAAA,CAAAK,EAAA,CAAAC,KAAA,GAAAjQ,EAAA,CAAA2P,iBAAA,CAAAO,EAAA,CAAAC,QAAA;IAAA;EAAA;;;YAApB1G,oBAAoB;MAAA2G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UClBjC1Q,EAAA,CAAAmB,SAAA,yBAA2F;UAOvEnB,EALpB,CAAAC,cAAA,aAAiB,aACU,aACoB,aACA,aACW,YACU;UAAAD,EAAA,CAAAkB,MAAA,sBAAe;UAAAlB,EAAA,CAAAiB,YAAA,EAAK;UAG5DjB,EAFR,CAAAC,cAAA,cAA2B,cACa,kBACsH;UAA7BD,EAAA,CAAAY,UAAA,mBAAAgQ,uDAAA;YAAA5Q,EAAA,CAAAM,aAAA,CAAAuQ,GAAA;YAAA,MAAAtP,UAAA,GAAAvB,EAAA,CAAAwB,WAAA;YAAA,OAAAxB,EAAA,CAAAW,WAAA,CAASgQ,GAAA,CAAA7D,SAAA,CAAAvL,UAAA,CAAkB;UAAA,EAAC;UAACvB,EAAA,CAAAmB,SAAA,aAA6C;UAACnB,EAAA,CAAAkB,MAAA,2BACrL;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UACxBjB,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAmB,SAAA,aAAuD;UAACnB,EAAA,CAAAkB,MAAA,eAAM;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UACrHjB,EAAA,CAAAC,cAAA,kBAA0F;UAAlCD,EAAA,CAAAY,UAAA,mBAAAkQ,uDAAA;YAAA9Q,EAAA,CAAAM,aAAA,CAAAuQ,GAAA;YAAA,MAAAlP,cAAA,GAAA3B,EAAA,CAAAwB,WAAA;YAAA,OAAAxB,EAAA,CAAAW,WAAA,CAASgQ,GAAA,CAAA/O,OAAA,CAAAD,cAAA,EAAoB,EAAE,CAAC;UAAA,EAAC;UAAC3B,EAAA,CAAAmB,SAAA,aAAoC;UAI9InB,EAJ8I,CAAAiB,YAAA,EAAS,EACrI,EACJ,EACJ,EACJ;UAMUjB,EALhB,CAAAC,cAAA,eAAwE,eAE/C,eACe,eACJ,iBAC2K;UAA3DD,EAAA,CAAAE,gBAAA,2BAAA6Q,8DAAA3Q,MAAA;YAAAJ,EAAA,CAAAM,aAAA,CAAAuQ,GAAA;YAAA7Q,EAAA,CAAAS,kBAAA,CAAAkQ,GAAA,CAAAjF,UAAA,EAAAtL,MAAA,MAAAuQ,GAAA,CAAAjF,UAAA,GAAAtL,MAAA;YAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;UAAA,EAAwB;UAACJ,EAAA,CAAAY,UAAA,2BAAAmQ,8DAAA;YAAA/Q,EAAA,CAAAM,aAAA,CAAAuQ,GAAA;YAAA,OAAA7Q,EAAA,CAAAW,WAAA,CAAiBgQ,GAAA,CAAAtF,aAAA,EAAe;UAAA,EAAC;UAA9LrL,EAAA,CAAAiB,YAAA,EAA+L;UAC/LjB,EAAA,CAAAmB,SAAA,aAA0C;UAElDnB,EADI,CAAAiB,YAAA,EAAM,EACJ;UAIEjB,EAFR,CAAAC,cAAA,eAAgC,WACvB,iBACyI;UAAhCD,EAAA,CAAAE,gBAAA,2BAAA8Q,8DAAA5Q,MAAA;YAAAJ,EAAA,CAAAM,aAAA,CAAAuQ,GAAA;YAAA7Q,EAAA,CAAAS,kBAAA,CAAAkQ,GAAA,CAAAnO,IAAA,EAAApC,MAAA,MAAAuQ,GAAA,CAAAnO,IAAA,GAAApC,MAAA;YAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;UAAA,EAAkB;UAEpIJ,EAFQ,CAAAiB,YAAA,EAA0I,EACxI,EACJ;UAIEjB,EAFR,CAAAC,cAAA,eAAgC,WACvB,kBACqK;UAAtDD,EAAA,CAAAE,gBAAA,2BAAA+Q,+DAAA7Q,MAAA;YAAAJ,EAAA,CAAAM,aAAA,CAAAuQ,GAAA;YAAA7Q,EAAA,CAAAS,kBAAA,CAAAkQ,GAAA,CAAA7N,MAAA,EAAA1C,MAAA,MAAAuQ,GAAA,CAAA7N,MAAA,GAAA1C,MAAA;YAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;UAAA,EAAoB;UAACJ,EAAA,CAAAY,UAAA,2BAAAqQ,+DAAA;YAAAjR,EAAA,CAAAM,aAAA,CAAAuQ,GAAA;YAAA,OAAA7Q,EAAA,CAAAW,WAAA,CAAiBgQ,GAAA,CAAA1F,YAAA,EAAc;UAAA,EAAC;UACjKjL,EAAA,CAAAC,cAAA,kBAAiB;UAAAD,EAAA,CAAAkB,MAAA,cAAM;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UAChCjB,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAkB,MAAA,WAAG;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UACtCjB,EAAA,CAAAC,cAAA,kBAAyB;UAAAD,EAAA,CAAAkB,MAAA,gBAAQ;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UAC1CjB,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAkB,MAAA,WAAG;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UAChCjB,EAAA,CAAAC,cAAA,kBAAwB;UAAAD,EAAA,CAAAkB,MAAA,eAAO;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UACxCjB,EAAA,CAAAC,cAAA,kBAAyB;UAAAD,EAAA,CAAAkB,MAAA,gBAAQ;UAG7ClB,EAH6C,CAAAiB,YAAA,EAAS,EACrC,EACP,EACJ;UAIEjB,EAFR,CAAAC,cAAA,eAAgC,WACvB,kBACsI;UAAzBD,EAAA,CAAAE,gBAAA,2BAAAgR,+DAAA9Q,MAAA;YAAAJ,EAAA,CAAAM,aAAA,CAAAuQ,GAAA;YAAA7Q,EAAA,CAAAS,kBAAA,CAAAkQ,GAAA,CAAAjF,UAAA,EAAAtL,MAAA,MAAAuQ,GAAA,CAAAjF,UAAA,GAAAtL,MAAA;YAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;UAAA,EAAwB;UAClIJ,EAAA,CAAAC,cAAA,kBAAiB;UAAAD,EAAA,CAAAkB,MAAA,mBAAW;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UACrCjB,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAkB,MAAA,WAAG;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UACtCjB,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAkB,MAAA,iBAAS;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UAC5CjB,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAkB,MAAA,iBAAS;UAG/ClB,EAH+C,CAAAiB,YAAA,EAAS,EACvC,EACP,EACJ;UAIEjB,EAFR,CAAAC,cAAA,eAAgC,WACvB,kBAC2E;UAACD,EAAA,CAAAmB,SAAA,aAAmD;UAC5HnB,EAAA,CAAAkB,MAAA,iBACJ;UAOhBlB,EAPgB,CAAAiB,YAAA,EAAS,EACP,EACJ,EAEJ,EAGJ;UAGEjB,EAFR,CAAAC,cAAA,eAA4B,WACnB,iBAC8I;UAAvGD,EAAA,CAAAY,UAAA,uBAAAuQ,uDAAA/Q,MAAA;YAAAJ,EAAA,CAAAM,aAAA,CAAAuQ,GAAA;YAAA,OAAA7Q,EAAA,CAAAW,WAAA,CAAagQ,GAAA,CAAAhF,WAAA,CAAAvL,MAAA,CAAmB;UAAA,EAAC;UAEjEJ,EADJ,CAAAC,cAAA,cAAsC,aAC+F;UAC7HD,EAAA,CAAAkB,MAAA,yBACJ;UAAAlB,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAoR,UAAA,KAAAC,4CAAA,2BAA2B;UA2E/BrR,EAAA,CAAAiB,YAAA,EAAK;UAEDjB,EADJ,CAAAC,cAAA,cAAsC,aACgG;UAC9HD,EAAA,CAAAkB,MAAA,aACJ;UAAAlB,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAoR,UAAA,KAAAE,4CAAA,2BAA2B;UA2E/BtR,EAAA,CAAAiB,YAAA,EAAK;UAEDjB,EADJ,CAAAC,cAAA,cAAsC,aACwG;UACtID,EAAA,CAAAkB,MAAA,iBAAQ;UAAAlB,EAAA,CAAAC,cAAA,gBAAgD;UAAAD,EAAA,CAAAkB,MAAA,SAAC;UAC7DlB,EAD6D,CAAAiB,YAAA,EAAO,EAChE;UACJjB,EAAA,CAAAoR,UAAA,KAAAG,4CAAA,2BAA2B;UA2E/BvR,EAAA,CAAAiB,YAAA,EAAK;UAEDjB,EADJ,CAAAC,cAAA,cAAsC,aAC0G;UACxID,EAAA,CAAAkB,MAAA,kBACJ;UAAAlB,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAoR,UAAA,KAAAI,4CAAA,2BAA2B;UA2E/BxR,EAAA,CAAAiB,YAAA,EAAK;UAEDjB,EADJ,CAAAC,cAAA,cAAsC,aAC0G;UACxID,EAAA,CAAAkB,MAAA,kBACJ;UAAAlB,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAoR,UAAA,KAAAK,4CAAA,2BAA2B;UA4EnCzR,EADI,CAAAiB,YAAA,EAAK,EACJ;UAKGjB,EAFR,CAAAC,cAAA,eAA8C,eACE,eACf;UACrBD,EAAA,CAAAmB,SAAA,qBACY;UACZnB,EAAA,CAAAC,cAAA,cAAiB;UAAAD,EAAA,CAAAkB,MAAA,8BAAsB;UAAAlB,EAAA,CAAAiB,YAAA,EAAK;UAC5CjB,EAAA,CAAAC,cAAA,aAAsB;UAAAD,EAAA,CAAAkB,MAAA,iFACX;UAEnBlB,EAFmB,CAAAiB,YAAA,EAAI,EACb,EACJ;UACNjB,EAAA,CAAAmB,SAAA,eAAgC;UACpCnB,EAAA,CAAAiB,YAAA,EAAM;UAENjB,EAAA,CAAAC,cAAA,0BAAoN;UAAjHD,EAAA,CAAAE,gBAAA,wBAAAwR,oEAAAtR,MAAA;YAAAJ,EAAA,CAAAM,aAAA,CAAAuQ,GAAA;YAAA7Q,EAAA,CAAAS,kBAAA,CAAAkQ,GAAA,CAAAhH,OAAA,CAAAgI,IAAA,EAAAvR,MAAA,MAAAuQ,GAAA,CAAAhH,OAAA,CAAAgI,IAAA,GAAAvR,MAAA;YAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;UAAA,EAAuB;UAA+BJ,EAAA,CAAAY,UAAA,wBAAA8Q,oEAAA;YAAA1R,EAAA,CAAAM,aAAA,CAAAuQ,GAAA;YAAA,OAAA7Q,EAAA,CAAAW,WAAA,CAAcgQ,GAAA,CAAA3F,UAAA,EAAY;UAAA,EAAC;UAKhLhL,EAJA,CAAAoR,UAAA,KAAAQ,4CAAA,0BAA8D,KAAAC,4CAAA,0BAI/B;UAInC7R,EAAA,CAAAiB,YAAA,EAAiB;UAGTjB,EAFR,CAAAC,cAAA,eAAoB,eACiD,gBAC/B;UAAAD,EAAA,CAAAkB,MAAA,kBAAU;UAGpDlB,EAHoD,CAAAiB,YAAA,EAAO,EAC7C,EACJ,EACJ;UA+FNjB,EA3FA,CAAAoR,UAAA,MAAAU,6CAAA,+BAAA9R,EAAA,CAAA+R,sBAAA,CAAgD,MAAAC,6CAAA,+BAAAhS,EAAA,CAAA+R,sBAAA,CA2FI;UAwBpE/R,EALY,CAAAiB,YAAA,EAAM,EACJ,EAEJ,EAEJ;;;;UA/mB+BjB,EAAA,CAAA2C,UAAA,oBAAAgO,GAAA,CAAAzG,eAAA,CAAmC;UAuBwFlK,EAAA,CAAA+B,SAAA,IAAwB;UAAxB/B,EAAA,CAAAiC,gBAAA,YAAA0O,GAAA,CAAAjF,UAAA,CAAwB;UAOvG1L,EAAA,CAAA+B,SAAA,GAA0B;UAA1B/B,EAAA,CAAA2C,UAAA,2BAA0B;UAA2B3C,EAAA,CAAAiC,gBAAA,YAAA0O,GAAA,CAAAnO,IAAA,CAAkB;UAMZxC,EAAA,CAAA+B,SAAA,GAAoB;UAApB/B,EAAA,CAAAiC,gBAAA,YAAA0O,GAAA,CAAA7N,MAAA,CAAoB;UAatB9C,EAAA,CAAA+B,SAAA,IAAwB;UAAxB/B,EAAA,CAAAiC,gBAAA,YAAA0O,GAAA,CAAAjF,UAAA,CAAwB;UAuBrH1L,EAAA,CAAA+B,SAAA,IAAc;UAAd/B,EAAA,CAAA2C,UAAA,eAAc;UAC/B3C,EAAA,CAAA+B,SAAA,GAAgB;UAAhB/B,EAAA,CAAA2C,UAAA,iBAAgB;UAgFhB3C,EAAA,CAAA+B,SAAA,GAAgB;UAAhB/B,EAAA,CAAA2C,UAAA,iBAAgB;UAgFhB3C,EAAA,CAAA+B,SAAA,GAAgB;UAAhB/B,EAAA,CAAA2C,UAAA,iBAAgB;UAgFhB3C,EAAA,CAAA+B,SAAA,GAAgB;UAAhB/B,EAAA,CAAA2C,UAAA,iBAAgB;UAgFhB3C,EAAA,CAAA+B,SAAA,GAAgB;UAAhB/B,EAAA,CAAA2C,UAAA,iBAAgB;UA6Ff3C,EAAA,CAAA+B,SAAA,IAAoB;UAApB/B,EAAA,CAAA2C,UAAA,iBAAAsP,OAAA,CAAoB;UAG2BjS,EAAA,CAAA+B,SAAA,EAA0C;UAA1C/B,EAAA,CAAA2C,UAAA,mBAAAgO,GAAA,CAAA5F,eAAA,kBAAA4F,GAAA,CAAA5F,eAAA,CAAAuB,MAAA,CAA0C;UAACtM,EAAA,CAAAiC,gBAAA,SAAA0O,GAAA,CAAAhH,OAAA,CAAAgI,IAAA,CAAuB;UAAC3R,EAAA,CAAA2C,UAAA,aAAAgO,GAAA,CAAAhH,OAAA,CAAAuI,QAAA,CAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}