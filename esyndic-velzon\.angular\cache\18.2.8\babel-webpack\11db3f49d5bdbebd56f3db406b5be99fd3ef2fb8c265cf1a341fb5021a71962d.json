{"ast": null, "code": "import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar Promise = getNative(root, 'Promise');\nexport default Promise;", "map": {"version": 3, "names": ["getNative", "root", "Promise"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_Promise.js"], "sourcesContent": ["import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar Promise = getNative(root, 'Promise');\n\nexport default Promise;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,IAAI,MAAM,YAAY;;AAE7B;AACA,IAAIC,OAAO,GAAGF,SAAS,CAACC,IAAI,EAAE,SAAS,CAAC;AAExC,eAAeC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}