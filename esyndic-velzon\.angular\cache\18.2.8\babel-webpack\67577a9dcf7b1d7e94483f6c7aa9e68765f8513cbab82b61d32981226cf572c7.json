{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Urdu [ur]\n//! author : <PERSON><PERSON> : https://github.com/ibnesayeed\n//! author : Zack : https://github.com/ZackVision\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var months = ['جنوری', 'فروری', 'مارچ', 'اپریل', 'مئی', 'جون', 'جولائی', 'اگست', 'ستمبر', 'اکتوبر', 'نومبر', 'دسمبر'],\n    days = ['اتوار', 'پیر', 'منگل', 'بدھ', 'جمعرات', 'جمعہ', 'ہفتہ'];\n  var ur = moment.defineLocale('ur', {\n    months: months,\n    monthsShort: months,\n    weekdays: days,\n    weekdaysShort: days,\n    weekdaysMin: days,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd، D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /صبح|شام/,\n    isPM: function (input) {\n      return 'شام' === input;\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'صبح';\n      }\n      return 'شام';\n    },\n    calendar: {\n      sameDay: '[آج بوقت] LT',\n      nextDay: '[کل بوقت] LT',\n      nextWeek: 'dddd [بوقت] LT',\n      lastDay: '[گذشتہ روز بوقت] LT',\n      lastWeek: '[گذشتہ] dddd [بوقت] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s بعد',\n      past: '%s قبل',\n      s: 'چند سیکنڈ',\n      ss: '%d سیکنڈ',\n      m: 'ایک منٹ',\n      mm: '%d منٹ',\n      h: 'ایک گھنٹہ',\n      hh: '%d گھنٹے',\n      d: 'ایک دن',\n      dd: '%d دن',\n      M: 'ایک ماہ',\n      MM: '%d ماہ',\n      y: 'ایک سال',\n      yy: '%d سال'\n    },\n    preparse: function (string) {\n      return string.replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/,/g, '،');\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return ur;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "months", "days", "ur", "defineLocale", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "meridiem", "hour", "minute", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "preparse", "string", "replace", "postformat", "week", "dow", "doy"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/moment/locale/ur.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Urdu [ur]\n//! author : <PERSON><PERSON> : https://github.com/ibnesayeed\n//! author : Zack : https://github.com/ZackVision\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var months = [\n            'جنوری',\n            'فروری',\n            'مارچ',\n            'اپریل',\n            'مئی',\n            'جون',\n            'جولائی',\n            'اگست',\n            'ستمبر',\n            'اکتوبر',\n            'نومبر',\n            'دسمبر',\n        ],\n        days = ['اتوار', 'پیر', 'منگل', 'بدھ', 'جمعرات', 'جمعہ', 'ہفتہ'];\n\n    var ur = moment.defineLocale('ur', {\n        months: months,\n        monthsShort: months,\n        weekdays: days,\n        weekdaysShort: days,\n        weekdaysMin: days,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd، D MMMM YYYY HH:mm',\n        },\n        meridiemParse: /صبح|شام/,\n        isPM: function (input) {\n            return 'شام' === input;\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 12) {\n                return 'صبح';\n            }\n            return 'شام';\n        },\n        calendar: {\n            sameDay: '[آج بوقت] LT',\n            nextDay: '[کل بوقت] LT',\n            nextWeek: 'dddd [بوقت] LT',\n            lastDay: '[گذشتہ روز بوقت] LT',\n            lastWeek: '[گذشتہ] dddd [بوقت] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s بعد',\n            past: '%s قبل',\n            s: 'چند سیکنڈ',\n            ss: '%d سیکنڈ',\n            m: 'ایک منٹ',\n            mm: '%d منٹ',\n            h: 'ایک گھنٹہ',\n            hh: '%d گھنٹے',\n            d: 'ایک دن',\n            dd: '%d دن',\n            M: 'ایک ماہ',\n            MM: '%d ماہ',\n            y: 'ایک سال',\n            yy: '%d سال',\n        },\n        preparse: function (string) {\n            return string.replace(/،/g, ',');\n        },\n        postformat: function (string) {\n            return string.replace(/,/g, '،');\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return ur;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,MAAM,GAAG,CACL,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,EACL,QAAQ,EACR,MAAM,EACN,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,CACV;IACDC,IAAI,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;EAEpE,IAAIC,EAAE,GAAGH,MAAM,CAACI,YAAY,CAAC,IAAI,EAAE;IAC/BH,MAAM,EAAEA,MAAM;IACdI,WAAW,EAAEJ,MAAM;IACnBK,QAAQ,EAAEJ,IAAI;IACdK,aAAa,EAAEL,IAAI;IACnBM,WAAW,EAAEN,IAAI;IACjBO,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,SAAS;IACxBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,KAAK,KAAKA,KAAK;IAC1B,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACX,OAAO,KAAK;MAChB;MACA,OAAO,KAAK;IAChB,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,cAAc;MACvBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,gBAAgB;MAC1BC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,wBAAwB;MAClCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACpC,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUF,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACpC,CAAC;IACDE,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOhD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}