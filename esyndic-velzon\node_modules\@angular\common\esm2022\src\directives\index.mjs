/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { NgClass } from './ng_class';
import { NgComponentOutlet } from './ng_component_outlet';
import { <PERSON><PERSON><PERSON>, NgForOf, NgForOfContext } from './ng_for_of';
import { NgIf, NgIfContext } from './ng_if';
import { NgPlural, NgPluralCase } from './ng_plural';
import { NgStyle } from './ng_style';
import { NgSwitch, NgSwitchCase, NgSwitchDefault } from './ng_switch';
import { NgTemplateOutlet } from './ng_template_outlet';
export { NgClass, NgComponentOutlet, NgFor, NgForOf, NgForOfContext, NgIf, NgIfContext, NgPlural, NgPluralCase, NgStyle, NgSwitch, Ng<PERSON>witchCase, Ng<PERSON><PERSON>Default, NgTemplateOutlet, };
/**
 * A collection of Angular directives that are likely to be used in each and every Angular
 * application.
 */
export const COMMON_DIRECTIVES = [
    NgClass,
    NgComponentOutlet,
    NgForOf,
    NgIf,
    NgTemplateOutlet,
    NgStyle,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    NgPlural,
    NgPluralCase,
];
//# sourceMappingURL=data:application/json;base64,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