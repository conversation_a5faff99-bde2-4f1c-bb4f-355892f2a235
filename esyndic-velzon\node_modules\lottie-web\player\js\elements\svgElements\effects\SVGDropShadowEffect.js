import {
  degToRads,
  rgbToHex,
} from '../../../utils/common';
import createNS from '../../../utils/helpers/svg_elements';
import SVGComposableEffect from './SVGComposableEffect';
import {
  extendPrototype,
} from '../../../utils/functionExtensions';

function SVGDropShadowEffect(filter, filterManager, elem, id, source) {
  var globalFilterSize = filterManager.container.globalData.renderConfig.filterSize;
  var filterSize = filterManager.data.fs || globalFilterSize;
  filter.setAttribute('x', filterSize.x || globalFilterSize.x);
  filter.setAttribute('y', filterSize.y || globalFilterSize.y);
  filter.setAttribute('width', filterSize.width || globalFilterSize.width);
  filter.setAttribute('height', filterSize.height || globalFilterSize.height);
  this.filterManager = filterManager;

  var feGaussianBlur = createNS('feGaussianBlur');
  feGaussianBlur.setAttribute('in', 'SourceAlpha');
  feGaussianBlur.setAttribute('result', id + '_drop_shadow_1');
  feGaussianBlur.setAttribute('stdDeviation', '0');
  this.feGaussianBlur = feGaussianBlur;
  filter.appendChild(feGaussianBlur);

  var feOffset = createNS('feOffset');
  feOffset.setAttribute('dx', '25');
  feOffset.setAttribute('dy', '0');
  feOffset.setAttribute('in', id + '_drop_shadow_1');
  feOffset.setAttribute('result', id + '_drop_shadow_2');
  this.feOffset = feOffset;
  filter.appendChild(feOffset);
  var feFlood = createNS('feFlood');
  feFlood.setAttribute('flood-color', '#00ff00');
  feFlood.setAttribute('flood-opacity', '1');
  feFlood.setAttribute('result', id + '_drop_shadow_3');
  this.feFlood = feFlood;
  filter.appendChild(feFlood);

  var feComposite = createNS('feComposite');
  feComposite.setAttribute('in', id + '_drop_shadow_3');
  feComposite.setAttribute('in2', id + '_drop_shadow_2');
  feComposite.setAttribute('operator', 'in');
  feComposite.setAttribute('result', id + '_drop_shadow_4');
  filter.appendChild(feComposite);

  var feMerge = this.createMergeNode(
    id,
    [
      id + '_drop_shadow_4',
      source,
    ]
  );
  filter.appendChild(feMerge);
  //
}
extendPrototype([SVGComposableEffect], SVGDropShadowEffect);

SVGDropShadowEffect.prototype.renderFrame = function (forceRender) {
  if (forceRender || this.filterManager._mdf) {
    if (forceRender || this.filterManager.effectElements[4].p._mdf) {
      this.feGaussianBlur.setAttribute('stdDeviation', this.filterManager.effectElements[4].p.v / 4);
    }
    if (forceRender || this.filterManager.effectElements[0].p._mdf) {
      var col = this.filterManager.effectElements[0].p.v;
      this.feFlood.setAttribute('flood-color', rgbToHex(Math.round(col[0] * 255), Math.round(col[1] * 255), Math.round(col[2] * 255)));
    }
    if (forceRender || this.filterManager.effectElements[1].p._mdf) {
      this.feFlood.setAttribute('flood-opacity', this.filterManager.effectElements[1].p.v / 255);
    }
    if (forceRender || this.filterManager.effectElements[2].p._mdf || this.filterManager.effectElements[3].p._mdf) {
      var distance = this.filterManager.effectElements[3].p.v;
      var angle = (this.filterManager.effectElements[2].p.v - 90) * degToRads;
      var x = distance * Math.cos(angle);
      var y = distance * Math.sin(angle);
      this.feOffset.setAttribute('dx', x);
      this.feOffset.setAttribute('dy', y);
    }
  }
};

export default SVGDropShadowEffect;
