<!-- Start Breadcrumbs -->
<app-breadcrumbs title="File Upload" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Dropzone</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <p class="text-muted">DropzoneJS is an open source library that provides drag’n’drop file uploads with
                    image previews.</p>

                <dropzone class="dropzone_sec"></dropzone>
                <!-- end dropzon-preview -->
            </div>
            <!-- end card body -->
        </div>
        <!-- end card -->
    </div> <!-- end col -->
</div>
<!-- end row -->
<div class="row mt-2">
    <div class="col-lg-12">
        <div class="justify-content-between d-flex align-items-center mb-3">
            <h5 class="mb-0 pb-1 text-decoration-underline">Filepond</h5>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">Multiple File Upload</h4>
                    </div><!-- end card header -->

                    <div class="card-body">
                        <p class="text-muted">FilePond is a JavaScript library that optimizes multiple images for faster
                            uploads and offers a great, accessible, silky smooth user experience.</p>
                        <dropzone class="dropzone_sec"></dropzone>
                    </div>
                    <!-- end dropzon-preview -->
                    <!-- end card body -->
                </div>
                <!-- end card -->
            </div> <!-- end col -->

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">Profile Picture Selection</h4>
                    </div><!-- end card header -->

                    <div class="card-body">
                        <p class="text-muted">FilePond is a JavaScript library with profile picture-shaped file upload
                            variation.</p>
                       
                        <dropzone class="dropzone_sec"></dropzone>
                        <!-- end dropzon-preview -->

                    </div>
                    <!-- end card body -->
                </div>
                <!-- end card -->
            </div> <!-- end col -->
        </div>
        <!-- end row -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->