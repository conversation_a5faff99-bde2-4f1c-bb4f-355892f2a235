{"name": "keycloak-js", "version": "24.0.2", "description": "A client-side JavaScript OpenID Connect library that can be used to secure web applications", "main": "./dist/keycloak.js", "module": "./dist/keycloak.mjs", "types": "./dist/keycloak.d.ts", "exports": {".": {"import": {"types": "./dist/keycloak.d.mts", "default": "./dist/keycloak.mjs"}, "require": {"types": "./dist/keycloak.d.ts", "default": "./dist/keycloak.js"}}, "./authz": {"import": {"types": "./dist/keycloak-authz.d.mts", "default": "./dist/keycloak-authz.mjs"}, "require": {"types": "./dist/keycloak-authz.d.ts", "default": "./dist/keycloak-authz.js"}}}, "files": ["dist"], "wireit": {"build": {"command": "rollup --config --configPlugin typescript && pnpm run duplicate-types", "files": ["src/**", "package.json", "rollup.config.ts", "tsconfig.json"], "output": ["dist/**", "!dist/*.d.ts"]}}, "repository": {"type": "git", "url": "https://github.com/keycloak/keycloak"}, "author": "Keycloak", "license": "Apache-2.0", "homepage": "https://www.keycloak.org", "keywords": ["keycloak", "sso", "o<PERSON>h", "oauth2", "authentication"], "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "es6-promise": "^4.2.8", "rollup": "^4.12.0", "shx": "^0.3.4"}, "dependencies": {"js-sha256": "^0.11.0", "jwt-decode": "^4.0.0"}, "scripts": {"build": "wireit", "duplicate-types": "shx cp dist/keycloak.d.ts dist/keycloak.d.mts && shx cp dist/keycloak-authz.d.ts dist/keycloak-authz.d.mts"}}