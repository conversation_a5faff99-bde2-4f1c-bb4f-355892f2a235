{"ast": null, "code": "/*!\n * getSize v2.0.3\n * measure size of elements\n * MIT license\n */\n\n/* jshint browser: true, strict: true, undef: true, unused: true */\n/* globals console: false */\n\n(function (window, factory) {\n  /* jshint strict: false */ /* globals define, module */\n  if (typeof define == 'function' && define.amd) {\n    // AMD\n    define(factory);\n  } else if (typeof module == 'object' && module.exports) {\n    // CommonJS\n    module.exports = factory();\n  } else {\n    // browser global\n    window.getSize = factory();\n  }\n})(window, function factory() {\n  'use strict';\n\n  // -------------------------- helpers -------------------------- //\n\n  // get a number from a string, not a percentage\n  function getStyleSize(value) {\n    var num = parseFloat(value);\n    // not a percent like '100%', and a number\n    var isValid = value.indexOf('%') == -1 && !isNaN(num);\n    return isValid && num;\n  }\n  function noop() {}\n  var logError = typeof console == 'undefined' ? noop : function (message) {\n    console.error(message);\n  };\n\n  // -------------------------- measurements -------------------------- //\n\n  var measurements = ['paddingLeft', 'paddingRight', 'paddingTop', 'paddingBottom', 'marginLeft', 'marginRight', 'marginTop', 'marginBottom', 'borderLeftWidth', 'borderRightWidth', 'borderTopWidth', 'borderBottomWidth'];\n  var measurementsLength = measurements.length;\n  function getZeroSize() {\n    var size = {\n      width: 0,\n      height: 0,\n      innerWidth: 0,\n      innerHeight: 0,\n      outerWidth: 0,\n      outerHeight: 0\n    };\n    for (var i = 0; i < measurementsLength; i++) {\n      var measurement = measurements[i];\n      size[measurement] = 0;\n    }\n    return size;\n  }\n\n  // -------------------------- getStyle -------------------------- //\n\n  /**\n   * getStyle, get style of element, check for Firefox bug\n   * https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n   */\n  function getStyle(elem) {\n    var style = getComputedStyle(elem);\n    if (!style) {\n      logError('Style returned ' + style + '. Are you running this code in a hidden iframe on Firefox? ' + 'See https://bit.ly/getsizebug1');\n    }\n    return style;\n  }\n\n  // -------------------------- setup -------------------------- //\n\n  var isSetup = false;\n  var isBoxSizeOuter;\n\n  /**\n   * setup\n   * check isBoxSizerOuter\n   * do on first getSize() rather than on page load for Firefox bug\n   */\n  function setup() {\n    // setup once\n    if (isSetup) {\n      return;\n    }\n    isSetup = true;\n\n    // -------------------------- box sizing -------------------------- //\n\n    /**\n     * Chrome & Safari measure the outer-width on style.width on border-box elems\n     * IE11 & Firefox<29 measures the inner-width\n     */\n    var div = document.createElement('div');\n    div.style.width = '200px';\n    div.style.padding = '1px 2px 3px 4px';\n    div.style.borderStyle = 'solid';\n    div.style.borderWidth = '1px 2px 3px 4px';\n    div.style.boxSizing = 'border-box';\n    var body = document.body || document.documentElement;\n    body.appendChild(div);\n    var style = getStyle(div);\n    // round value for browser zoom. desandro/masonry#928\n    isBoxSizeOuter = Math.round(getStyleSize(style.width)) == 200;\n    getSize.isBoxSizeOuter = isBoxSizeOuter;\n    body.removeChild(div);\n  }\n\n  // -------------------------- getSize -------------------------- //\n\n  function getSize(elem) {\n    setup();\n\n    // use querySeletor if elem is string\n    if (typeof elem == 'string') {\n      elem = document.querySelector(elem);\n    }\n\n    // do not proceed on non-objects\n    if (!elem || typeof elem != 'object' || !elem.nodeType) {\n      return;\n    }\n    var style = getStyle(elem);\n\n    // if hidden, everything is 0\n    if (style.display == 'none') {\n      return getZeroSize();\n    }\n    var size = {};\n    size.width = elem.offsetWidth;\n    size.height = elem.offsetHeight;\n    var isBorderBox = size.isBorderBox = style.boxSizing == 'border-box';\n\n    // get all measurements\n    for (var i = 0; i < measurementsLength; i++) {\n      var measurement = measurements[i];\n      var value = style[measurement];\n      var num = parseFloat(value);\n      // any 'auto', 'medium' value will be 0\n      size[measurement] = !isNaN(num) ? num : 0;\n    }\n    var paddingWidth = size.paddingLeft + size.paddingRight;\n    var paddingHeight = size.paddingTop + size.paddingBottom;\n    var marginWidth = size.marginLeft + size.marginRight;\n    var marginHeight = size.marginTop + size.marginBottom;\n    var borderWidth = size.borderLeftWidth + size.borderRightWidth;\n    var borderHeight = size.borderTopWidth + size.borderBottomWidth;\n    var isBorderBoxSizeOuter = isBorderBox && isBoxSizeOuter;\n\n    // overwrite width and height if we can get it from style\n    var styleWidth = getStyleSize(style.width);\n    if (styleWidth !== false) {\n      size.width = styleWidth + (\n      // add padding and border unless it's already including it\n      isBorderBoxSizeOuter ? 0 : paddingWidth + borderWidth);\n    }\n    var styleHeight = getStyleSize(style.height);\n    if (styleHeight !== false) {\n      size.height = styleHeight + (\n      // add padding and border unless it's already including it\n      isBorderBoxSizeOuter ? 0 : paddingHeight + borderHeight);\n    }\n    size.innerWidth = size.width - (paddingWidth + borderWidth);\n    size.innerHeight = size.height - (paddingHeight + borderHeight);\n    size.outerWidth = size.width + marginWidth;\n    size.outerHeight = size.height + marginHeight;\n    return size;\n  }\n  return getSize;\n});", "map": {"version": 3, "names": ["window", "factory", "define", "amd", "module", "exports", "getSize", "getStyleSize", "value", "num", "parseFloat", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "isNaN", "noop", "logError", "console", "message", "error", "measurements", "measurementsLength", "length", "getZeroSize", "size", "width", "height", "innerWidth", "innerHeight", "outerWidth", "outerHeight", "i", "measurement", "getStyle", "elem", "style", "getComputedStyle", "isSetup", "isBoxSizeOuter", "setup", "div", "document", "createElement", "padding", "borderStyle", "borderWidth", "boxSizing", "body", "documentElement", "append<PERSON><PERSON><PERSON>", "Math", "round", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "nodeType", "display", "offsetWidth", "offsetHeight", "isBorderBox", "paddingWidth", "paddingLeft", "paddingRight", "paddingHeight", "paddingTop", "paddingBottom", "marginWid<PERSON>", "marginLeft", "marginRight", "marginHeight", "marginTop", "marginBottom", "borderLeftWidth", "borderRightWidth", "borderHeight", "borderTopWidth", "borderBottomWidth", "isBorderBoxSizeOuter", "styleWidth", "styleHeight"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/get-size/get-size.js"], "sourcesContent": ["/*!\n * getSize v2.0.3\n * measure size of elements\n * MIT license\n */\n\n/* jshint browser: true, strict: true, undef: true, unused: true */\n/* globals console: false */\n\n( function( window, factory ) {\n  /* jshint strict: false */ /* globals define, module */\n  if ( typeof define == 'function' && define.amd ) {\n    // AMD\n    define( factory );\n  } else if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory();\n  } else {\n    // browser global\n    window.getSize = factory();\n  }\n\n})( window, function factory() {\n'use strict';\n\n// -------------------------- helpers -------------------------- //\n\n// get a number from a string, not a percentage\nfunction getStyleSize( value ) {\n  var num = parseFloat( value );\n  // not a percent like '100%', and a number\n  var isValid = value.indexOf('%') == -1 && !isNaN( num );\n  return isValid && num;\n}\n\nfunction noop() {}\n\nvar logError = typeof console == 'undefined' ? noop :\n  function( message ) {\n    console.error( message );\n  };\n\n// -------------------------- measurements -------------------------- //\n\nvar measurements = [\n  'paddingLeft',\n  'paddingRight',\n  'paddingTop',\n  'paddingBottom',\n  'marginLeft',\n  'marginRight',\n  'marginTop',\n  'marginBottom',\n  'borderLeftWidth',\n  'borderRightWidth',\n  'borderTopWidth',\n  'borderBottomWidth'\n];\n\nvar measurementsLength = measurements.length;\n\nfunction getZeroSize() {\n  var size = {\n    width: 0,\n    height: 0,\n    innerWidth: 0,\n    innerHeight: 0,\n    outerWidth: 0,\n    outerHeight: 0\n  };\n  for ( var i=0; i < measurementsLength; i++ ) {\n    var measurement = measurements[i];\n    size[ measurement ] = 0;\n  }\n  return size;\n}\n\n// -------------------------- getStyle -------------------------- //\n\n/**\n * getStyle, get style of element, check for Firefox bug\n * https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n */\nfunction getStyle( elem ) {\n  var style = getComputedStyle( elem );\n  if ( !style ) {\n    logError( 'Style returned ' + style +\n      '. Are you running this code in a hidden iframe on Firefox? ' +\n      'See https://bit.ly/getsizebug1' );\n  }\n  return style;\n}\n\n// -------------------------- setup -------------------------- //\n\nvar isSetup = false;\n\nvar isBoxSizeOuter;\n\n/**\n * setup\n * check isBoxSizerOuter\n * do on first getSize() rather than on page load for Firefox bug\n */\nfunction setup() {\n  // setup once\n  if ( isSetup ) {\n    return;\n  }\n  isSetup = true;\n\n  // -------------------------- box sizing -------------------------- //\n\n  /**\n   * Chrome & Safari measure the outer-width on style.width on border-box elems\n   * IE11 & Firefox<29 measures the inner-width\n   */\n  var div = document.createElement('div');\n  div.style.width = '200px';\n  div.style.padding = '1px 2px 3px 4px';\n  div.style.borderStyle = 'solid';\n  div.style.borderWidth = '1px 2px 3px 4px';\n  div.style.boxSizing = 'border-box';\n\n  var body = document.body || document.documentElement;\n  body.appendChild( div );\n  var style = getStyle( div );\n  // round value for browser zoom. desandro/masonry#928\n  isBoxSizeOuter = Math.round( getStyleSize( style.width ) ) == 200;\n  getSize.isBoxSizeOuter = isBoxSizeOuter;\n\n  body.removeChild( div );\n}\n\n// -------------------------- getSize -------------------------- //\n\nfunction getSize( elem ) {\n  setup();\n\n  // use querySeletor if elem is string\n  if ( typeof elem == 'string' ) {\n    elem = document.querySelector( elem );\n  }\n\n  // do not proceed on non-objects\n  if ( !elem || typeof elem != 'object' || !elem.nodeType ) {\n    return;\n  }\n\n  var style = getStyle( elem );\n\n  // if hidden, everything is 0\n  if ( style.display == 'none' ) {\n    return getZeroSize();\n  }\n\n  var size = {};\n  size.width = elem.offsetWidth;\n  size.height = elem.offsetHeight;\n\n  var isBorderBox = size.isBorderBox = style.boxSizing == 'border-box';\n\n  // get all measurements\n  for ( var i=0; i < measurementsLength; i++ ) {\n    var measurement = measurements[i];\n    var value = style[ measurement ];\n    var num = parseFloat( value );\n    // any 'auto', 'medium' value will be 0\n    size[ measurement ] = !isNaN( num ) ? num : 0;\n  }\n\n  var paddingWidth = size.paddingLeft + size.paddingRight;\n  var paddingHeight = size.paddingTop + size.paddingBottom;\n  var marginWidth = size.marginLeft + size.marginRight;\n  var marginHeight = size.marginTop + size.marginBottom;\n  var borderWidth = size.borderLeftWidth + size.borderRightWidth;\n  var borderHeight = size.borderTopWidth + size.borderBottomWidth;\n\n  var isBorderBoxSizeOuter = isBorderBox && isBoxSizeOuter;\n\n  // overwrite width and height if we can get it from style\n  var styleWidth = getStyleSize( style.width );\n  if ( styleWidth !== false ) {\n    size.width = styleWidth +\n      // add padding and border unless it's already including it\n      ( isBorderBoxSizeOuter ? 0 : paddingWidth + borderWidth );\n  }\n\n  var styleHeight = getStyleSize( style.height );\n  if ( styleHeight !== false ) {\n    size.height = styleHeight +\n      // add padding and border unless it's already including it\n      ( isBorderBoxSizeOuter ? 0 : paddingHeight + borderHeight );\n  }\n\n  size.innerWidth = size.width - ( paddingWidth + borderWidth );\n  size.innerHeight = size.height - ( paddingHeight + borderHeight );\n\n  size.outerWidth = size.width + marginWidth;\n  size.outerHeight = size.height + marginHeight;\n\n  return size;\n}\n\nreturn getSize;\n\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,CAAE,UAAUA,MAAM,EAAEC,OAAO,EAAG;EAC5B,2BAA2B;EAC3B,IAAK,OAAOC,MAAM,IAAI,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAG;IAC/C;IACAD,MAAM,CAAED,OAAQ,CAAC;EACnB,CAAC,MAAM,IAAK,OAAOG,MAAM,IAAI,QAAQ,IAAIA,MAAM,CAACC,OAAO,EAAG;IACxD;IACAD,MAAM,CAACC,OAAO,GAAGJ,OAAO,CAAC,CAAC;EAC5B,CAAC,MAAM;IACL;IACAD,MAAM,CAACM,OAAO,GAAGL,OAAO,CAAC,CAAC;EAC5B;AAEF,CAAC,EAAGD,MAAM,EAAE,SAASC,OAAOA,CAAA,EAAG;EAC/B,YAAY;;EAEZ;;EAEA;EACA,SAASM,YAAYA,CAAEC,KAAK,EAAG;IAC7B,IAAIC,GAAG,GAAGC,UAAU,CAAEF,KAAM,CAAC;IAC7B;IACA,IAAIG,OAAO,GAAGH,KAAK,CAACI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAACC,KAAK,CAAEJ,GAAI,CAAC;IACvD,OAAOE,OAAO,IAAIF,GAAG;EACvB;EAEA,SAASK,IAAIA,CAAA,EAAG,CAAC;EAEjB,IAAIC,QAAQ,GAAG,OAAOC,OAAO,IAAI,WAAW,GAAGF,IAAI,GACjD,UAAUG,OAAO,EAAG;IAClBD,OAAO,CAACE,KAAK,CAAED,OAAQ,CAAC;EAC1B,CAAC;;EAEH;;EAEA,IAAIE,YAAY,GAAG,CACjB,aAAa,EACb,cAAc,EACd,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,aAAa,EACb,WAAW,EACX,cAAc,EACd,iBAAiB,EACjB,kBAAkB,EAClB,gBAAgB,EAChB,mBAAmB,CACpB;EAED,IAAIC,kBAAkB,GAAGD,YAAY,CAACE,MAAM;EAE5C,SAASC,WAAWA,CAAA,EAAG;IACrB,IAAIC,IAAI,GAAG;MACTC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE;IACf,CAAC;IACD,KAAM,IAAIC,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAGV,kBAAkB,EAAEU,CAAC,EAAE,EAAG;MAC3C,IAAIC,WAAW,GAAGZ,YAAY,CAACW,CAAC,CAAC;MACjCP,IAAI,CAAEQ,WAAW,CAAE,GAAG,CAAC;IACzB;IACA,OAAOR,IAAI;EACb;;EAEA;;EAEA;AACA;AACA;AACA;EACA,SAASS,QAAQA,CAAEC,IAAI,EAAG;IACxB,IAAIC,KAAK,GAAGC,gBAAgB,CAAEF,IAAK,CAAC;IACpC,IAAK,CAACC,KAAK,EAAG;MACZnB,QAAQ,CAAE,iBAAiB,GAAGmB,KAAK,GACjC,6DAA6D,GAC7D,gCAAiC,CAAC;IACtC;IACA,OAAOA,KAAK;EACd;;EAEA;;EAEA,IAAIE,OAAO,GAAG,KAAK;EAEnB,IAAIC,cAAc;;EAElB;AACA;AACA;AACA;AACA;EACA,SAASC,KAAKA,CAAA,EAAG;IACf;IACA,IAAKF,OAAO,EAAG;MACb;IACF;IACAA,OAAO,GAAG,IAAI;;IAEd;;IAEA;AACF;AACA;AACA;IACE,IAAIG,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACvCF,GAAG,CAACL,KAAK,CAACV,KAAK,GAAG,OAAO;IACzBe,GAAG,CAACL,KAAK,CAACQ,OAAO,GAAG,iBAAiB;IACrCH,GAAG,CAACL,KAAK,CAACS,WAAW,GAAG,OAAO;IAC/BJ,GAAG,CAACL,KAAK,CAACU,WAAW,GAAG,iBAAiB;IACzCL,GAAG,CAACL,KAAK,CAACW,SAAS,GAAG,YAAY;IAElC,IAAIC,IAAI,GAAGN,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACO,eAAe;IACpDD,IAAI,CAACE,WAAW,CAAET,GAAI,CAAC;IACvB,IAAIL,KAAK,GAAGF,QAAQ,CAAEO,GAAI,CAAC;IAC3B;IACAF,cAAc,GAAGY,IAAI,CAACC,KAAK,CAAE3C,YAAY,CAAE2B,KAAK,CAACV,KAAM,CAAE,CAAC,IAAI,GAAG;IACjElB,OAAO,CAAC+B,cAAc,GAAGA,cAAc;IAEvCS,IAAI,CAACK,WAAW,CAAEZ,GAAI,CAAC;EACzB;;EAEA;;EAEA,SAASjC,OAAOA,CAAE2B,IAAI,EAAG;IACvBK,KAAK,CAAC,CAAC;;IAEP;IACA,IAAK,OAAOL,IAAI,IAAI,QAAQ,EAAG;MAC7BA,IAAI,GAAGO,QAAQ,CAACY,aAAa,CAAEnB,IAAK,CAAC;IACvC;;IAEA;IACA,IAAK,CAACA,IAAI,IAAI,OAAOA,IAAI,IAAI,QAAQ,IAAI,CAACA,IAAI,CAACoB,QAAQ,EAAG;MACxD;IACF;IAEA,IAAInB,KAAK,GAAGF,QAAQ,CAAEC,IAAK,CAAC;;IAE5B;IACA,IAAKC,KAAK,CAACoB,OAAO,IAAI,MAAM,EAAG;MAC7B,OAAOhC,WAAW,CAAC,CAAC;IACtB;IAEA,IAAIC,IAAI,GAAG,CAAC,CAAC;IACbA,IAAI,CAACC,KAAK,GAAGS,IAAI,CAACsB,WAAW;IAC7BhC,IAAI,CAACE,MAAM,GAAGQ,IAAI,CAACuB,YAAY;IAE/B,IAAIC,WAAW,GAAGlC,IAAI,CAACkC,WAAW,GAAGvB,KAAK,CAACW,SAAS,IAAI,YAAY;;IAEpE;IACA,KAAM,IAAIf,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAGV,kBAAkB,EAAEU,CAAC,EAAE,EAAG;MAC3C,IAAIC,WAAW,GAAGZ,YAAY,CAACW,CAAC,CAAC;MACjC,IAAItB,KAAK,GAAG0B,KAAK,CAAEH,WAAW,CAAE;MAChC,IAAItB,GAAG,GAAGC,UAAU,CAAEF,KAAM,CAAC;MAC7B;MACAe,IAAI,CAAEQ,WAAW,CAAE,GAAG,CAAClB,KAAK,CAAEJ,GAAI,CAAC,GAAGA,GAAG,GAAG,CAAC;IAC/C;IAEA,IAAIiD,YAAY,GAAGnC,IAAI,CAACoC,WAAW,GAAGpC,IAAI,CAACqC,YAAY;IACvD,IAAIC,aAAa,GAAGtC,IAAI,CAACuC,UAAU,GAAGvC,IAAI,CAACwC,aAAa;IACxD,IAAIC,WAAW,GAAGzC,IAAI,CAAC0C,UAAU,GAAG1C,IAAI,CAAC2C,WAAW;IACpD,IAAIC,YAAY,GAAG5C,IAAI,CAAC6C,SAAS,GAAG7C,IAAI,CAAC8C,YAAY;IACrD,IAAIzB,WAAW,GAAGrB,IAAI,CAAC+C,eAAe,GAAG/C,IAAI,CAACgD,gBAAgB;IAC9D,IAAIC,YAAY,GAAGjD,IAAI,CAACkD,cAAc,GAAGlD,IAAI,CAACmD,iBAAiB;IAE/D,IAAIC,oBAAoB,GAAGlB,WAAW,IAAIpB,cAAc;;IAExD;IACA,IAAIuC,UAAU,GAAGrE,YAAY,CAAE2B,KAAK,CAACV,KAAM,CAAC;IAC5C,IAAKoD,UAAU,KAAK,KAAK,EAAG;MAC1BrD,IAAI,CAACC,KAAK,GAAGoD,UAAU;MACrB;MACED,oBAAoB,GAAG,CAAC,GAAGjB,YAAY,GAAGd,WAAW,CAAE;IAC7D;IAEA,IAAIiC,WAAW,GAAGtE,YAAY,CAAE2B,KAAK,CAACT,MAAO,CAAC;IAC9C,IAAKoD,WAAW,KAAK,KAAK,EAAG;MAC3BtD,IAAI,CAACE,MAAM,GAAGoD,WAAW;MACvB;MACEF,oBAAoB,GAAG,CAAC,GAAGd,aAAa,GAAGW,YAAY,CAAE;IAC/D;IAEAjD,IAAI,CAACG,UAAU,GAAGH,IAAI,CAACC,KAAK,IAAKkC,YAAY,GAAGd,WAAW,CAAE;IAC7DrB,IAAI,CAACI,WAAW,GAAGJ,IAAI,CAACE,MAAM,IAAKoC,aAAa,GAAGW,YAAY,CAAE;IAEjEjD,IAAI,CAACK,UAAU,GAAGL,IAAI,CAACC,KAAK,GAAGwC,WAAW;IAC1CzC,IAAI,CAACM,WAAW,GAAGN,IAAI,CAACE,MAAM,GAAG0C,YAAY;IAE7C,OAAO5C,IAAI;EACb;EAEA,OAAOjB,OAAO;AAEd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}