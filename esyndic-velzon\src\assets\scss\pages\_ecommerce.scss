//
// _ecommerce.scss
//

// product list

#selection-element { 
    display: none;
}

.filter-choices-input{
    .choices__inner{
        padding: 0;
        border: none;
        background-color: var(--#{$prefix}secondary-bg);
    }

    .choices__input{
        background-color: var(--#{$prefix}secondary-bg);
    }
}

//  product detail

.sticky-side-div{
    @media (min-width: 992px){
        position: sticky;
        top: calc(#{$header-height} + #{$grid-gutter-width});
    }
}

.product-img-slider{
    .product-nav-slider{
        .nav-slide-item{
            border: 1px solid var(--#{$prefix}border-color);
            border-radius: $border-radius;
            padding: 0.5rem;
            cursor: pointer;
        }

        .swiper-slide-thumb-active{
            .nav-slide-item{
                background-color: var(--#{$prefix}light);
            }
        }
    }
}

.filter-list{
    a {
        &.active {
            .listname {
                color: var(--#{$prefix}success);
            }
        }   
    }
}

#remove-actions{
    display: none;
}