{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nexport var visualMapActionInfo = {\n  type: 'selectDataRange',\n  event: 'dataRangeSelected',\n  // FIXME use updateView appears wrong\n  update: 'update'\n};\nexport var visualMapActionHander = function (payload, ecModel) {\n  ecModel.eachComponent({\n    mainType: 'visualMap',\n    query: payload\n  }, function (model) {\n    model.setSelected(payload.selected);\n  });\n};", "map": {"version": 3, "names": ["visualMapActionInfo", "type", "event", "update", "visualMapActionHander", "payload", "ecModel", "eachComponent", "mainType", "query", "model", "setSelected", "selected"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/component/visualMap/visualMapAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nexport var visualMapActionInfo = {\n  type: 'selectDataRange',\n  event: 'dataRangeSelected',\n  // FIXME use updateView appears wrong\n  update: 'update'\n};\nexport var visualMapActionHander = function (payload, ecModel) {\n  ecModel.eachComponent({\n    mainType: 'visualMap',\n    query: payload\n  }, function (model) {\n    model.setSelected(payload.selected);\n  });\n};"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,mBAAmB,GAAG;EAC/BC,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,mBAAmB;EAC1B;EACAC,MAAM,EAAE;AACV,CAAC;AACD,OAAO,IAAIC,qBAAqB,GAAG,SAAAA,CAAUC,OAAO,EAAEC,OAAO,EAAE;EAC7DA,OAAO,CAACC,aAAa,CAAC;IACpBC,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAEJ;EACT,CAAC,EAAE,UAAUK,KAAK,EAAE;IAClBA,KAAK,CAACC,WAAW,CAACN,OAAO,CAACO,QAAQ,CAAC;EACrC,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}