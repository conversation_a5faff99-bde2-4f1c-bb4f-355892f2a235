<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Orders" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
  <div class="col-lg-12">
    <div class="card" id="orderList">
      <div class="card-header  border-0">
        <div class="row align-items-center gy-3">
          <div class="col-sm">
            <h5 class="card-title mb-0">Order History</h5>
          </div>
          <div class="col-sm-auto hstack gap-2">
            <button type="button" class="btn btn-success add-btn" data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal" (click)="openModal(content)"><i class="ri-add-line align-bottom me-1"></i>
              Create
              Order</button>
            <button type="button" class="btn btn-info" (click)="csvFileExport()"><i class="ri-file-download-line align-bottom me-1"></i> Export</button>
            <button class="btn btn-soft-danger" id="remove-actions" style="display: none" (click)="deleteMultiple(deleteModel)"><i class="ri-delete-bin-2-line"></i></button>
          </div>
        </div>
      </div>
      <div class="card-body border border-dashed border-end-0 border-start-0">

        <div class="row g-3">
          <div class="col-xxl-5 col-sm-6">
            <div class="search-box">
              <input type="text" name="searchTerm" class="form-control" placeholder="Search for order ID, customer, order status or something..." [(ngModel)]="searchTerm" (ngModelChange)="performSearch()">
              <i class="ri-search-line search-icon"></i>
            </div>
          </div>
          <!--end col-->
          <div class="col-xxl-2 col-sm-6">
            <div>
              <input class="form-control flatpickr-input" type="text" mwlFlatpickr [altInput]="true" [convertModelValue]="true" placeholder="Select date" id="isDate" [(ngModel)]="date" mode="range">

            </div>
          </div>
          <!--end col-->
          <div class="col-xxl-2 col-sm-4">
            <div>
              <select class="form-control" data-choices data-choices-search-false name="choices-single-default" id="idStatus" [(ngModel)]="status" (ngModelChange)="filterStatus()">
                <option value="" selected>Status</option>
                <option value="Pending">Pending</option>
                <option value="Inprogress">Inprogress</option>
                <option value="Cancelled">Cancelled</option>
                <option value="Pickups">Pickups</option>
                <option value="Returns">Returns</option>
                <option value="Delivered">Delivered</option>
              </select>
            </div>
          </div>
          <!--end col-->
          <div class="col-xxl-2 col-sm-4">
            <div>
              <select class="form-control" data-choices data-choices-search-false name="choices-single-default" id="idPayment" [(ngModel)]="payment" (ngModelChange)="PaymentFiletr()">
                <option value="" selected>Select Payment</option>
                <option value="Mastercard">Mastercard</option>
                <option value="Paypal">Paypal</option>
                <option value="Visa">Visa</option>
                <option value="COD">COD</option>
              </select>
            </div>
          </div>
          <!--end col-->
          <div class="col-xxl-1 col-sm-4">
            <div>
              <button type="button" class="btn btn-primary w-100"> <i class="ri-equalizer-fill mx-1 align-bottom"></i>
                Filters
              </button>
            </div>
          </div>
          <!--end col-->
        </div>
        <!--end row-->

      </div>
      <div class="card-body pt-0">
        <div>
          <!-- Nav tabs -->
          <ul ngbNav #nav="ngbNav" [activeId]="1" (navChange)="onNavChange($event)" class="nav nav-tabs nav-tabs-custom nav-success mb-3">
            <li [ngbNavItem]="1">
              <a ngbNavLink>
                <i class="ri-store-2-fill me-1 align-bottom"></i> All Orders
              </a>
              <ng-template ngbNavContent>
                <div class="table-responsive table-card mb-0">
                  <table class="table">
                    <thead>
                      <tr class="bg-light text-muted text-uppercase">
                        <th scope="col" style="width: 25px;">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="checkAll" value="option" [(ngModel)]="masterSelected" (change)="checkUncheckAll($event)">
                          </div>
                        </th>
                        <th class="sort" (click)="onSort('orderId')">Order ID</th>
                        <th class="sort" (click)="onSort('customer')">Customer</th>
                        <th class="sort" (click)="onSort('product')">Product</th>
                        <th class="sort" (click)="onSort('orderDate')">Order Date</th>
                        <th class="sort" (click)="onSort('amount')">Amount</th>
                        <th class="sort" (click)="onSort('payment')">Payment Method</th>
                        <th class="sort" (click)="onSort('status')">Delivery Status</th>
                        <th class="sort">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      @for (data of orderes; track $index) {
                      <tr id="o_{{data._id}}">
                        <th scope="row">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="checkAll" value="{{data._id}}" [(ngModel)]="data.state" (change)="onCheckboxChange($event)">
                          </div>
                        </th>
                        <td><a routerLink="/ecommerce/order-details">
                            <ngb-highlight [result]="data.orderId" [term]="searchTerm"></ngb-highlight>
                          </a></td>
                        <td>
                          <ngb-highlight [result]="data.customer" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.product" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.orderDate | date :'longDate'" [term]="searchTerm">
                          </ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.amount" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.payment" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td class="status"><span class="badge text-uppercase" [ngClass]=" { 'bg-warning-subtle text-warning': data.status === 'Pending', 'bg-danger-subtle text-danger': data.status === 'Cancelled', 'bg-secondary-subtle text-secondary': data.status === 'Inprogress', 'bg-info-subtle text-info': data.status === 'Pickups', 'bg-primary-subtle text-primary': data.status === 'Returns', 'bg-success-subtle text-success': data.status === 'Delivered' }">{{data.status}}</span>
                        </td>
                        <td>
                          <ul class="list-inline hstack gap-2 mb-0">
                            <li class="list-inline-item" data-bs-toggle="tooltip" data-bs-trigger="hover" ngbTooltip="View" placement="top">
                              <a routerLink="/ecommerce/order-details" class="text-primary d-inline-block">
                                <i class="ri-eye-fill fs-16"></i>
                              </a>
                            </li>
                            <li class="list-inline-item edit" data-bs-toggle="tooltip" data-bs-trigger="hover" ngbTooltip="Edit" placement="top">
                              <a href="javascript:void(0);" data-bs-toggle="modal" class="text-primary d-inline-block edit-item-btn" data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal" (click)="editDataGet($index,content)">
                                <i class="ri-pencil-fill fs-16"></i>
                              </a>
                            </li>
                            <li class="list-inline-item me-0" ngbTooltip="Remove" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" (click)="confirm(deleteModel,data._id)">
                              <a class="text-danger d-inline-block remove-item-btn" data-bs-toggle="modal" data-bs-target="#deleteOrder">
                                <i class="ri-delete-bin-5-fill fs-16"></i>
                              </a>
                            </li>
                          </ul>
                        </td>
                      </tr>
                      }
                    </tbody>
                  </table>
                </div>
                <div class="row justify-content-md-between align-items-md-center gy-2">
                  <div class="col col-sm-6">
                    <div class="dataTables_info mb-2" id="tickets-table_info" role="status" aria-live="polite">
                      Showing
                      {{service.startIndex}} to
                      {{service.endIndex}} of {{allorderes?.length}}
                      entries
                    </div>
                  </div>
                  <!-- Pagination -->
                  <div class="col col-sm-6">
                    <div class="text-sm-right float-end listjs-pagination">
                      <ngb-pagination [collectionSize]="allorderes?.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                      </ngb-pagination>
                    </div>
                  </div>
                  <!-- End Pagination -->
                </div>
              </ng-template>
            </li>
            <li [ngbNavItem]="2">
              <a ngbNavLink>
                <i class="ri-checkbox-circle-line me-1 align-bottom"></i> Delivered
              </a>
              <ng-template ngbNavContent>
                <div class="table-responsive table-card mb-0">
                  <table class="table">
                    <thead>
                      <tr class="bg-light text-muted text-uppercase">
                        <th scope="col" style="width: 25px;">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="checkAll" value="option" [(ngModel)]="masterSelected" (change)="checkUncheckAll($event)">
                          </div>
                        </th>
                        <th class="sort" (click)="onSort('orderId')">Order ID</th>
                        <th class="sort" (click)="onSort('customer')">Customer</th>
                        <th class="sort" (click)="onSort('product')">Product</th>
                        <th class="sort" (click)="onSort('orderDate')">Order Date</th>
                        <th class="sort" (click)="onSort('amount')">Amount</th>
                        <th class="sort" (click)="onSort('payment')">Payment Method</th>
                        <th class="sort" (click)="onSort('status')">Delivery Status</th>
                        <th class="sort">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      @for (data of orderes; track $index) {
                      <tr>
                        <th scope="row">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="checkAll" value="{{data._id}}" [(ngModel)]="data.state">
                          </div>
                        </th>
                        <td><a routerLink="/ecommerce/order-details">
                            <ngb-highlight [result]="data.orderId" [term]="searchTerm"></ngb-highlight>
                          </a></td>
                        <td>
                          <ngb-highlight [result]="data.customer" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.product" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.orderDate | date :'longDate'" [term]="searchTerm">
                          </ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.amount" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.payment" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td class="status"><span class="badge text-uppercase" [ngClass]=" { 'bg-warning-subtle text-warning': data.status === 'Pending', 'bg-danger-subtle text-danger': data.status === 'Cancelled', 'bg-secondary-subtle text-secondary': data.status === 'Inprogress', 'bg-info-subtle text-info': data.status === 'Pickups', 'bg-primary-subtle text-primary': data.status === 'Returns', 'bg-success-subtle text-success': data.status === 'Delivered' }">{{data.status}}</span>
                        </td>
                        <td>
                          <ul class="list-inline hstack gap-2 mb-0">
                            <li class="list-inline-item" data-bs-toggle="tooltip" data-bs-trigger="hover" ngbTooltip="View" placement="top">
                              <a routerLink="/ecommerce/order-details" class="text-primary d-inline-block">
                                <i class="ri-eye-fill fs-16"></i>
                              </a>
                            </li>
                            <li class="list-inline-item edit" data-bs-toggle="tooltip" data-bs-trigger="hover" ngbTooltip="Edit" placement="top">
                              <a href="javascript:void(0);" data-bs-toggle="modal" class="text-primary d-inline-block edit-item-btn" data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal" (click)="editDataGet($index,content)">
                                <i class="ri-pencil-fill fs-16"></i>
                              </a>
                            </li>
                            <li class="list-inline-item" ngbTooltip="Remove" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" (click)="confirm(deleteModel,data._id)">
                              <a class="text-danger d-inline-block remove-item-btn" data-bs-toggle="modal" data-bs-target="#deleteOrder">
                                <i class="ri-delete-bin-5-fill fs-16"></i>
                              </a>
                            </li>
                          </ul>
                        </td>
                      </tr>
                      }
                    </tbody>
                  </table>
                </div>
                <div class="row justify-content-md-between align-items-md-center gy-2">
                  <div class="col col-sm-6">
                    <div class="dataTables_info mb-2" id="tickets-table_info" role="status" aria-live="polite">
                      Showing
                      {{service.startIndex}} to
                      {{service.endIndex}} of {{allorderes?.length}}
                      entries
                    </div>
                  </div>
                  <!-- Pagination -->
                  <div class="col col-sm-6">
                    <div class="text-sm-right float-end listjs-pagination">
                      <ngb-pagination [collectionSize]="allorderes?.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                      </ngb-pagination>
                    </div>
                  </div>
                  <!-- End Pagination -->
                </div>
              </ng-template>
            </li>
            <li [ngbNavItem]="3">
              <a ngbNavLink>
                <i class="ri-truck-line me-1 align-bottom"></i> Pickups <span class="badge bg-danger align-middle ms-1">{{orderes?.length}}</span>
              </a>
              <ng-template ngbNavContent>
                <div class="table-responsive">
                  <table class="table ">
                    <thead>
                      <tr class="bg-light text-muted text-uppercase">
                        <th scope="col" style="width: 25px;">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="checkAll" value="option" [(ngModel)]="masterSelected" (change)="checkUncheckAll($event)">
                          </div>
                        </th>
                        <th class="sort" (click)="onSort('orderId')">Order ID</th>
                        <th class="sort" (click)="onSort('customer')">Customer</th>
                        <th class="sort" (click)="onSort('product')">Product</th>
                        <th class="sort" (click)="onSort('orderDate')">Order Date</th>
                        <th class="sort" (click)="onSort('amount')">Amount</th>
                        <th class="sort" (click)="onSort('payment')">Payment Method</th>
                        <th class="sort" (click)="onSort('status')">Delivery Status</th>
                        <th class="sort">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      @for (data of orderes; track $index) {
                      <tr>
                        <th scope="row">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="checkAll" value="{{data._id}}" [(ngModel)]="data.state">
                          </div>
                        </th>
                        <td><a routerLink="/ecommerce/order-details">
                            <ngb-highlight [result]="data.orderId" [term]="searchTerm"></ngb-highlight>
                          </a></td>
                        <td>
                          <ngb-highlight [result]="data.customer" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.product" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.orderDate | date :'longDate'" [term]="searchTerm">
                          </ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.amount" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.payment" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td class="status"><span class="badge text-uppercase" [ngClass]=" { 'bg-warning-subtle text-warning': data.status === 'Pending', 'bg-danger-subtle text-danger': data.status === 'Cancelled', 'bg-secondary-subtle text-secondary': data.status === 'Inprogress', 'bg-info-subtle text-info': data.status === 'Pickups', 'bg-primary-subtle text-primary': data.status === 'Returns', 'bg-success-subtle text-success': data.status === 'Delivered' }">{{data.status}}</span>
                        </td>
                        <td>
                          <ul class="list-inline hstack gap-2 mb-0">
                            <li class="list-inline-item" data-bs-toggle="tooltip" data-bs-trigger="hover" ngbTooltip="View" placement="top">
                              <a routerLink="/ecommerce/order-details" class="text-primary d-inline-block">
                                <i class="ri-eye-fill fs-16"></i>
                              </a>
                            </li>
                            <li class="list-inline-item edit" data-bs-toggle="tooltip" data-bs-trigger="hover" ngbTooltip="Edit" placement="top">
                              <a href="javascript:void(0);" data-bs-toggle="modal" class="text-primary d-inline-block edit-item-btn" data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal" (click)="editDataGet($index,content)">
                                <i class="ri-pencil-fill fs-16"></i>
                              </a>
                            </li>
                            <li class="list-inline-item" ngbTooltip="Remove" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" (click)="confirm(deleteModel,data._id)">
                              <a class="text-danger d-inline-block remove-item-btn" data-bs-toggle="modal" data-bs-target="#deleteOrder">
                                <i class="ri-delete-bin-5-fill fs-16"></i>
                              </a>
                            </li>
                          </ul>
                        </td>
                      </tr>
                      }
                    </tbody>
                  </table>
                </div>
                <div class="row justify-content-md-between align-items-md-center gy-2">
                  <div class="col col-sm-6">
                    <div class="dataTables_info mb-2" id="tickets-table_info" role="status" aria-live="polite">
                      Showing
                      {{service.startIndex}} to
                      {{service.endIndex}} of {{allorderes?.length}}
                      entries
                    </div>
                  </div>
                  <!-- Pagination -->
                  <div class="col col-sm-6">
                    <div class="text-sm-right float-end listjs-pagination">
                      <ngb-pagination [collectionSize]="allorderes?.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                      </ngb-pagination>
                    </div>
                  </div>
                  <!-- End Pagination -->
                </div>
              </ng-template>
            </li>
            <li [ngbNavItem]="4">
              <a ngbNavLink>
                <i class="ri-arrow-left-right-fill me-1 align-bottom"></i> Returns
              </a>
              <ng-template ngbNavContent>
                <div class="table-responsive table-card mb-0">
                  <table class="table">
                    <thead>
                      <tr class="bg-light text-muted text-uppercase">
                        <th scope="col" style="width: 25px;">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="checkAll" value="option" [(ngModel)]="masterSelected" (change)="checkUncheckAll($event)">
                          </div>
                        </th>
                        <th class="sort" (click)="onSort('orderId')">Order ID</th>
                        <th class="sort" (click)="onSort('customer')">Customer</th>
                        <th class="sort" (click)="onSort('product')">Product</th>
                        <th class="sort" (click)="onSort('orderDate')">Order Date</th>
                        <th class="sort" (click)="onSort('amount')">Amount</th>
                        <th class="sort" (click)="onSort('payment')">Payment Method</th>
                        <th class="sort" (click)="onSort('status')">Delivery Status</th>
                        <th class="sort">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      @for (data of orderes; track $index) {
                      <tr>
                        <th scope="row">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="checkAll" value="{{data._id}}" [(ngModel)]="data.state">
                          </div>
                        </th>
                        <td><a routerLink="/ecommerce/order-details">
                            <ngb-highlight [result]="data.orderId" [term]="searchTerm"></ngb-highlight>
                          </a></td>
                        <td>
                          <ngb-highlight [result]="data.customer" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.product" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.orderDate | date :'longDate'" [term]="searchTerm">
                          </ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.amount" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.payment" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td class="status"><span class="badge text-uppercase" [ngClass]=" { 'bg-warning-subtle text-warning': data.status === 'Pending', 'bg-danger-subtle text-danger': data.status === 'Cancelled', 'bg-secondary-subtle text-secondary': data.status === 'Inprogress', 'bg-info-subtle text-info': data.status === 'Pickups', 'bg-primary-subtle text-primary': data.status === 'Returns', 'bg-success-subtle text-success': data.status === 'Delivered' }">{{data.status}}</span>
                        </td>
                        <td>
                          <ul class="list-inline hstack gap-2 mb-0">
                            <li class="list-inline-item" data-bs-toggle="tooltip" data-bs-trigger="hover" ngbTooltip="View" placement="top">
                              <a routerLink="/ecommerce/order-details" class="text-primary d-inline-block">
                                <i class="ri-eye-fill fs-16"></i>
                              </a>
                            </li>
                            <li class="list-inline-item edit" data-bs-toggle="tooltip" data-bs-trigger="hover" ngbTooltip="Edit" placement="top">
                              <a href="javascript:void(0);" data-bs-toggle="modal" class="text-primary d-inline-block edit-item-btn" data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal" (click)="editDataGet($index,content)">
                                <i class="ri-pencil-fill fs-16"></i>
                              </a>
                            </li>
                            <li class="list-inline-item" ngbTooltip="Remove" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" (click)="confirm(deleteModel,data._id)">
                              <a class="text-danger d-inline-block remove-item-btn" data-bs-toggle="modal" data-bs-target="#deleteOrder">
                                <i class="ri-delete-bin-5-fill fs-16"></i>
                              </a>
                            </li>
                          </ul>
                        </td>
                      </tr>
                      }
                    </tbody>
                  </table>
                </div>
                <div class="row justify-content-md-between align-items-md-center gy-2">
                  <div class="col col-sm-6">
                    <div class="dataTables_info mb-2" id="tickets-table_info" role="status" aria-live="polite">
                      Showing
                      {{service.startIndex}} to
                      {{service.endIndex}} of {{allorderes?.length}}
                      entries
                    </div>
                  </div>
                  <!-- Pagination -->
                  <div class="col col-sm-6">
                    <div class="text-sm-right float-end listjs-pagination">
                      <ngb-pagination [collectionSize]="allorderes?.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                      </ngb-pagination>
                    </div>
                  </div>
                  <!-- End Pagination -->
                </div>
              </ng-template>
            </li>
            <li [ngbNavItem]="5">
              <a ngbNavLink>
                <i class="ri-close-circle-line me-1 align-bottom"></i> Cancelled
              </a>
              <ng-template ngbNavContent>
                <div class="table-responsive table-card mb-0">
                  <table class="table">
                    <thead>
                      <tr class="bg-light text-muted text-uppercase">
                        <th scope="col" style="width: 25px;">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="checkAll" value="option" [(ngModel)]="masterSelected" (change)="checkUncheckAll($event)">
                          </div>
                        </th>
                        <th class="sort" (click)="onSort('orderId')">Order ID</th>
                        <th class="sort" (click)="onSort('customer')">Customer</th>
                        <th class="sort" (click)="onSort('product')">Product</th>
                        <th class="sort" (click)="onSort('orderDate')">Order Date</th>
                        <th class="sort" (click)="onSort('amount')">Amount</th>
                        <th class="sort" (click)="onSort('payment')">Payment Method</th>
                        <th class="sort" (click)="onSort('status')">Delivery Status</th>
                        <th class="sort">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      @for (data of orderes; track $index) {
                      <tr>
                        <th scope="row">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="checkAll" value="{{data._id}}" [(ngModel)]="data.state">
                          </div>
                        </th>
                        <td><a routerLink="/ecommerce/order-details">
                            <ngb-highlight [result]="data.orderId" [term]="searchTerm"></ngb-highlight>
                          </a></td>
                        <td>
                          <ngb-highlight [result]="data.customer" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.product" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.orderDate | date :'longDate'" [term]="searchTerm">
                          </ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.amount" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td>
                          <ngb-highlight [result]="data.payment" [term]="searchTerm"></ngb-highlight>
                        </td>
                        <td class="status"><span class="badge text-uppercase" [ngClass]=" { 'bg-warning-subtle text-warning': data.status === 'Pending', 'bg-danger-subtle text-danger': data.status === 'Cancelled', 'bg-secondary-subtle text-secondary': data.status === 'Inprogress', 'bg-info-subtle text-info': data.status === 'Pickups', 'bg-primary-subtle text-primary': data.status === 'Returns', 'bg-success-subtle text-success': data.status === 'Delivered' }">{{data.status}}</span>
                        </td>
                        <td>
                          <ul class="list-inline hstack gap-2 mb-0">
                            <li class="list-inline-item" data-bs-toggle="tooltip" data-bs-trigger="hover" ngbTooltip="View" placement="top">
                              <a routerLink="/ecommerce/order-details" class="text-primary d-inline-block">
                                <i class="ri-eye-fill fs-16"></i>
                              </a>
                            </li>
                            <li class="list-inline-item edit" data-bs-toggle="tooltip" data-bs-trigger="hover" ngbTooltip="Edit" placement="top">
                              <a href="javascript:void(0);" data-bs-toggle="modal" class="text-primary d-inline-block edit-item-btn" data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal" (click)="editDataGet($index,content)">
                                <i class="ri-pencil-fill fs-16"></i>
                              </a>
                            </li>
                            <li class="list-inline-item" ngbTooltip="Remove" placement="top" data-bs-toggle="tooltip" data-bs-trigger="hover" (click)="confirm(deleteModel,data._id)">
                              <a class="text-danger d-inline-block remove-item-btn" data-bs-toggle="modal" data-bs-target="#deleteOrder">
                                <i class="ri-delete-bin-5-fill fs-16"></i>
                              </a>
                            </li>
                          </ul>
                        </td>
                      </tr>
                      }
                    </tbody>
                  </table>
                </div>
                <div class="row justify-content-md-between align-items-md-center gy-2">
                  <div class="col col-sm-6">
                    <div class="dataTables_info mb-2" id="tickets-table_info" role="status" aria-live="polite">
                      Showing
                      {{service.startIndex}} to
                      {{service.endIndex}} of {{allorderes?.length}}
                      entries
                    </div>
                  </div>
                  <!-- Pagination -->
                  <div class="col col-sm-6">
                    <div class="text-sm-right float-end listjs-pagination">
                      <ngb-pagination [collectionSize]="allorderes?.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                      </ngb-pagination>
                    </div>
                  </div>
                  <!-- End Pagination -->
                </div>
              </ng-template>
            </li>
          </ul>

          <!-- Tab panes -->
          <div class="tab-content text-muted">
            <div [ngbNavOutlet]="nav"></div>
          </div>

          <!-- Order Create Model -->
          <ng-template #content role="document" let-modal>
            <div class="modal-header bg-light p-3">
              <h5 class="modal-title" id="exampleModalLabel">Add Order</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="close-modal" (click)="modal.dismiss('Cross click')"></button>
            </div>
            <form (ngSubmit)="saveUser()" [formGroup]="ordersForm" class="tablelist-form" autocomplete="off">
              <div class="modal-body">
                <input type="hidden" name="id" value="" formControlName="_id" />
                <div class="mb-3">
                  <label for="customername-field" class="form-label">Customer Name</label>
                  <input type="text" id="customername-field" class="form-control" placeholder="Enter Name" required formControlName="customer" [ngClass]="{ 'is-invalid': submitted && form['customer'].errors }" />
                  @if(submitted && form['customer'].errors){
                  <div class="invalid-feedback" align="left">
                    @if(form['customer'].errors['required']){
                    <div>Customer Name is required</div>
                    }
                  </div>
                  }
                </div>

                <div class="mb-3">
                  <label for="productname-field" class="form-label">Product</label>
                  <select class="form-control" data-trigger name="productname-field" id="productname-field" required formControlName="product" [ngClass]="{ 'is-invalid': submitted && form['product'].errors }">
                    <option value="">Product</option>
                    <option value="Puma Tshirt">Puma Tshirt</option>
                    <option value="Adidas Sneakers">Adidas Sneakers</option>
                    <option value="350 ml Glass Grocery Container">350 ml Glass Grocery Container</option>
                    <option value="American egale outfitters Shirt">American egale outfitters Shirt</option>
                    <option value="Galaxy Watch4">Galaxy Watch4</option>
                    <option value="Apple iPhone 12">Apple iPhone 12</option>
                    <option value="Funky Prints T-shirt">Funky Prints T-shirt</option>
                    <option value="USB Flash Drive Personalized with 3D Print">USB Flash Drive Personalized with 3D
                      Print</option>
                    <option value="Oxford Button-Down Shirt">Oxford Button-Down Shirt</option>
                    <option value="Classic Short Sleeve Shirt">Classic Short Sleeve Shirt</option>
                    <option value="Half Sleeve T-Shirts (Blue)">Half Sleeve T-Shirts (Blue)</option>
                    <option value="Noise Evolve Smartwatch">Noise Evolve Smartwatch</option>
                  </select>
                  @if(submitted && form['product'].errors){
                  <div class="invalid-feedback" align="left">
                    @if(form['product'].errors['required']){
                    <div>product Name is required</div>
                    }
                  </div>
                  }
                </div>

                <div class="mb-3">
                  <label for="date-field" class="form-label">Order Date</label>
                  <input class="form-control flatpickr-input" type="text" mwlFlatpickr [altInput]="true" required [convertModelValue]="true" formControlName="orderDate" [ngClass]="{ 'is-invalid': submitted && form['customer'].errors }">
                  @if(submitted && form['orderDate'].errors){
                  <div class="invalid-feedback" align="left">
                    @if(form['orderDate'].errors['required']){
                    <div>Order Date is required</div>
                    }
                  </div>
                  }
                </div>

                <div class="row gy-4 mb-3">
                  <div class="col-md-6">
                    <div>
                      <label for="amount-field" class="form-label">Amount</label>
                      <input type="text" id="amount-field" class="form-control" placeholder="Total amount" required formControlName="amount" [ngClass]="{ 'is-invalid': submitted && form['amount'].errors }" />
                      @if(submitted && form['amount'].errors){
                      <div class="invalid-feedback" align="left">
                        @if(form['amount'].errors['required']){
                        <div>Amount is required</div>
                        }
                      </div>
                      }
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div>
                      <label for="payment-field" class="form-label">Payment
                        Method</label>
                      <select class="form-control" data-trigger name="payment-method" id="payment-field" required formControlName="payment" [ngClass]="{ 'is-invalid': submitted && form['payment'].errors }">
                        <option value="">Payment Method</option>
                        <option value="Mastercard">Mastercard</option>
                        <option value="Visa">Visa</option>
                        <option value="COD">COD</option>
                        <option value="Paypal">Paypal</option>
                      </select>
                      @if(submitted && form['payment'].errors){
                      <div class="invalid-feedback" align="left">
                        @if(form['payment'].errors['required']){
                        <div>Payment Method is required</div>
                        }
                      </div>
                      }
                    </div>
                  </div>
                </div>

                <div>
                  <label for="delivered-status" class="form-label">Delivery Status</label>
                  <select class="form-control" data-trigger name="delivered-status" id="delivered-status" required formControlName="status" [ngClass]="{ 'is-invalid': submitted && form['status'].errors }">
                    <option value="">Delivery Status</option>
                    <option value="Pending">Pending</option>
                    <option value="Inprogress">Inprogress</option>
                    <option value="Cancelled">Cancelled</option>
                    <option value="Pickups">Pickups</option>
                    <option value="Returns">Returns</option>
                    <option value="Delivered">Delivered</option>
                  </select>
                  @if(submitted && form['status'].errors){
                  <div class="invalid-feedback" align="left">
                    @if(form['status'].errors['required']){
                    <div>Delivered Status is required</div>
                    }
                  </div>
                  }
                </div>
              </div>
              <div class="modal-footer">
                <div class="hstack gap-2 justify-content-end">
                  <button type="button" class="btn btn-light" data-bs-dismiss="modal" (click)="modal.close('Close click')">Close</button>
                  <button type="submit" class="btn btn-success" id="add-btn">Add Order</button>
                </div>
              </div>
            </form>
          </ng-template>
          <!--End Modal -->
          <div id="elmLoader">
            <div class="spinner-border text-primary avatar-sm" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
        </div>
      </div>

    </div>
    <!--end col-->
  </div>
  <!--end row-->

  <!-- removeItemModal -->
  <ng-template #deleteModel let-modal>
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="btn-close" (click)="modal.dismiss('Cross click')"></button>
      </div>
      <div class="modal-body">
        <div class="mt-2 text-center">
          <lord-icon src="https://cdn.lordicon.com/gsqxdxog.json" trigger="loop" colors="primary:#405189,secondary:#f06548" style="width:90px;height:90px"></lord-icon>
          <div class="mt-4 pt-2 fs-15 mx-4 mx-sm-5">
            <h4>You are about to delete a order ?</h4>
            <p class="text-muted mx-4 mb-0">Deleting your order will remove all of your information from our database.
            </p>
          </div>
        </div>
        <div class="d-flex gap-2 justify-content-center mt-4 mb-2">
          <button class="btn btn-link link-success fw-medium text-decoration-none" data-bs-dismiss="modal" id="deleteRecord-close" (click)="modal.close('Close click')"><i class="ri-close-line me-1 align-middle"></i>
            Close</button>
          <button type="button" class="btn w-sm btn-danger " id="delete-product" (click)="deleteData(deleteId)" (click)="modal.close('Close click')">Yes, Delete It!</button>
        </div>
      </div>
    </div><!-- /.modal-content -->
  </ng-template>