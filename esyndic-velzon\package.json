{"name": "velzon", "version": "4.3.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.0.4", "@angular/cdk": "^18.0.4", "@angular/common": "^18.0.4", "@angular/compiler": "^18.0.4", "@angular/core": "^18.0.4", "@angular/forms": "^18.0.4", "@angular/google-maps": "^18.0.4", "@angular/material": "^16.2.14", "@angular/platform-browser": "^18.0.4", "@angular/platform-browser-dynamic": "^18.0.4", "@angular/router": "^18.0.4", "@asymmetrik/ngx-leaflet": "^18.0.1", "@ckeditor/ckeditor5-angular": "^9.0.0", "@ckeditor/ckeditor5-build-classic": "^43.2.0", "@ctrl/ngx-emoji-mart": "^9.2.0", "@fullcalendar/angular": "^6.1.14", "@fullcalendar/core": "^6.1.14", "@fullcalendar/daygrid": "^6.1.14", "@fullcalendar/interaction": "^6.1.14", "@fullcalendar/list": "^6.1.14", "@fullcalendar/multimonth": "^6.1.14", "@fullcalendar/timegrid": "^6.1.14", "@lordicon/element": "^1.6.0", "@ng-bootstrap/ng-bootstrap": "17.0.0", "@ng-select/ng-select": "^13.2.0", "@ngrx/effects": "^18.0.0", "@ngrx/store": "^18.0.0", "@ngrx/store-devtools": "^18.0.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@nicky-lenaers/ngx-scroll-to": "^14.0.0", "@popperjs/core": "^2.11.8", "@types/google.maps": "^3.55.10", "@types/jasmine": "^5.1.4", "@types/prismjs": "^1.26.4", "angular-feather": "^6.5.1", "angular-ng-autocomplete": "^2.0.12", "angular-ng-stepper": "^2.0.0", "angular-shepherd": "^17.0.0", "angularx-flatpickr": "^7.3.0", "apexcharts": "^3.49.1", "axios": "^1.7.2", "bootstrap": "5.3.3", "browserslist": "^4.23.1", "chart.js": "^4.4.3", "echarts": "^5.5.0", "firebase": "^10.12.2", "flatpickr": "^4.6.13", "fuzzy-search": "^3.2.1", "intersection-observer": "^0.12.2", "jasmine-core": "^5.1.2", "jquery": "^3.7.1", "karma": "^6.4.3", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "keycloak-angular": "^15.2.1", "keycloak-js": "^24.0.2", "leaflet": "^1.9.4", "lottie-web": "^5.12.2", "masonry-layout": "^4.2.2", "metismenujs": "^1.4.0", "moment": "^2.30.1", "ng-apexcharts": "^1.11.0", "ng-otp-input": "^1.9.3", "ng2-charts": "^6.0.1", "ngx-color-picker": "^16.0.0", "ngx-cookie-service": "^18.0.0", "ngx-countup": "^13.1.0", "ngx-csv": "0.3.2", "ngx-drag-drop": "^17.0.0", "ngx-dropzone-wrapper": "^16.0.0", "ngx-echarts": "^17.1.0", "ngx-lightbox": "^3.0.0", "ngx-mask": "^17.0.1", "ngx-masonry": "^14.0.1", "ngx-nestable": "^0.9.4", "ngx-pipes": "^3.2.2", "ngx-slick-carousel": "^17.0.0", "ngx-slider-v2": "^17.0.0", "ngx-ui-switch": "^15.0.0", "prismjs": "^1.29.0", "rxjs": "^7.8.1", "simplebar-angular": "^3.2.6", "slick-carousel": "^1.8.1", "sweetalert2": "^11.12.0", "tslib": "^2.6.3", "zone.js": "^0.14.7"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.5", "@angular/cli": "^18.0.5", "@angular/compiler-cli": "^18.0.4", "@angular/localize": "^18.0.4", "@types/echarts": "^4.9.22", "@types/file-saver": "^2.0.7", "@types/jquery": "^3.5.29", "@types/leaflet": "^1.9.8", "@types/lodash": "^4.14.202", "@types/node": "^20.10.0", "@types/quill": "^2.0.14", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "typescript": "~5.4.5"}, "browserslist": ["last 1 version", "> 1%", "IE 11"], "overrides": {"autoprefixer": "10.4.5"}}