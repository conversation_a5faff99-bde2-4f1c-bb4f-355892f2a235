{"ast": null, "code": "export class InvalidTokenError extends Error {}\nInvalidTokenError.prototype.name = \"InvalidTokenError\";\nfunction b64DecodeUnicode(str) {\n  return decodeURIComponent(atob(str).replace(/(.)/g, (m, p) => {\n    let code = p.charCodeAt(0).toString(16).toUpperCase();\n    if (code.length < 2) {\n      code = \"0\" + code;\n    }\n    return \"%\" + code;\n  }));\n}\nfunction base64UrlDecode(str) {\n  let output = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  switch (output.length % 4) {\n    case 0:\n      break;\n    case 2:\n      output += \"==\";\n      break;\n    case 3:\n      output += \"=\";\n      break;\n    default:\n      throw new Error(\"base64 string is not of the correct length\");\n  }\n  try {\n    return b64DecodeUnicode(output);\n  } catch (err) {\n    return atob(output);\n  }\n}\nexport function jwtDecode(token, options) {\n  if (typeof token !== \"string\") {\n    throw new InvalidTokenError(\"Invalid token specified: must be a string\");\n  }\n  options || (options = {});\n  const pos = options.header === true ? 0 : 1;\n  const part = token.split(\".\")[pos];\n  if (typeof part !== \"string\") {\n    throw new InvalidTokenError(`Invalid token specified: missing part #${pos + 1}`);\n  }\n  let decoded;\n  try {\n    decoded = base64UrlDecode(part);\n  } catch (e) {\n    throw new InvalidTokenError(`Invalid token specified: invalid base64 for part #${pos + 1} (${e.message})`);\n  }\n  try {\n    return JSON.parse(decoded);\n  } catch (e) {\n    throw new InvalidTokenError(`Invalid token specified: invalid json for part #${pos + 1} (${e.message})`);\n  }\n}", "map": {"version": 3, "names": ["InvalidTokenError", "Error", "prototype", "name", "b64DecodeUnicode", "str", "decodeURIComponent", "atob", "replace", "m", "p", "code", "charCodeAt", "toString", "toUpperCase", "length", "base64UrlDecode", "output", "err", "jwtDecode", "token", "options", "pos", "header", "part", "split", "decoded", "e", "message", "JSON", "parse"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/jwt-decode/build/esm/index.js"], "sourcesContent": ["export class InvalidTokenError extends Error {\n}\nInvalidTokenError.prototype.name = \"InvalidTokenError\";\nfunction b64DecodeUnicode(str) {\n    return decodeURIComponent(atob(str).replace(/(.)/g, (m, p) => {\n        let code = p.charCodeAt(0).toString(16).toUpperCase();\n        if (code.length < 2) {\n            code = \"0\" + code;\n        }\n        return \"%\" + code;\n    }));\n}\nfunction base64UrlDecode(str) {\n    let output = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n    switch (output.length % 4) {\n        case 0:\n            break;\n        case 2:\n            output += \"==\";\n            break;\n        case 3:\n            output += \"=\";\n            break;\n        default:\n            throw new Error(\"base64 string is not of the correct length\");\n    }\n    try {\n        return b64DecodeUnicode(output);\n    }\n    catch (err) {\n        return atob(output);\n    }\n}\nexport function jwtDecode(token, options) {\n    if (typeof token !== \"string\") {\n        throw new InvalidTokenError(\"Invalid token specified: must be a string\");\n    }\n    options || (options = {});\n    const pos = options.header === true ? 0 : 1;\n    const part = token.split(\".\")[pos];\n    if (typeof part !== \"string\") {\n        throw new InvalidTokenError(`Invalid token specified: missing part #${pos + 1}`);\n    }\n    let decoded;\n    try {\n        decoded = base64UrlDecode(part);\n    }\n    catch (e) {\n        throw new InvalidTokenError(`Invalid token specified: invalid base64 for part #${pos + 1} (${e.message})`);\n    }\n    try {\n        return JSON.parse(decoded);\n    }\n    catch (e) {\n        throw new InvalidTokenError(`Invalid token specified: invalid json for part #${pos + 1} (${e.message})`);\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,iBAAiB,SAASC,KAAK,CAAC;AAE7CD,iBAAiB,CAACE,SAAS,CAACC,IAAI,GAAG,mBAAmB;AACtD,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,OAAOC,kBAAkB,CAACC,IAAI,CAACF,GAAG,CAAC,CAACG,OAAO,CAAC,MAAM,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC1D,IAAIC,IAAI,GAAGD,CAAC,CAACE,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;IACrD,IAAIH,IAAI,CAACI,MAAM,GAAG,CAAC,EAAE;MACjBJ,IAAI,GAAG,GAAG,GAAGA,IAAI;IACrB;IACA,OAAO,GAAG,GAAGA,IAAI;EACrB,CAAC,CAAC,CAAC;AACP;AACA,SAASK,eAAeA,CAACX,GAAG,EAAE;EAC1B,IAAIY,MAAM,GAAGZ,GAAG,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACtD,QAAQS,MAAM,CAACF,MAAM,GAAG,CAAC;IACrB,KAAK,CAAC;MACF;IACJ,KAAK,CAAC;MACFE,MAAM,IAAI,IAAI;MACd;IACJ,KAAK,CAAC;MACFA,MAAM,IAAI,GAAG;MACb;IACJ;MACI,MAAM,IAAIhB,KAAK,CAAC,4CAA4C,CAAC;EACrE;EACA,IAAI;IACA,OAAOG,gBAAgB,CAACa,MAAM,CAAC;EACnC,CAAC,CACD,OAAOC,GAAG,EAAE;IACR,OAAOX,IAAI,CAACU,MAAM,CAAC;EACvB;AACJ;AACA,OAAO,SAASE,SAASA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACtC,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAIpB,iBAAiB,CAAC,2CAA2C,CAAC;EAC5E;EACAqB,OAAO,KAAKA,OAAO,GAAG,CAAC,CAAC,CAAC;EACzB,MAAMC,GAAG,GAAGD,OAAO,CAACE,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;EAC3C,MAAMC,IAAI,GAAGJ,KAAK,CAACK,KAAK,CAAC,GAAG,CAAC,CAACH,GAAG,CAAC;EAClC,IAAI,OAAOE,IAAI,KAAK,QAAQ,EAAE;IAC1B,MAAM,IAAIxB,iBAAiB,CAAC,0CAA0CsB,GAAG,GAAG,CAAC,EAAE,CAAC;EACpF;EACA,IAAII,OAAO;EACX,IAAI;IACAA,OAAO,GAAGV,eAAe,CAACQ,IAAI,CAAC;EACnC,CAAC,CACD,OAAOG,CAAC,EAAE;IACN,MAAM,IAAI3B,iBAAiB,CAAC,qDAAqDsB,GAAG,GAAG,CAAC,KAAKK,CAAC,CAACC,OAAO,GAAG,CAAC;EAC9G;EACA,IAAI;IACA,OAAOC,IAAI,CAACC,KAAK,CAACJ,OAAO,CAAC;EAC9B,CAAC,CACD,OAAOC,CAAC,EAAE;IACN,MAAM,IAAI3B,iBAAiB,CAAC,mDAAmDsB,GAAG,GAAG,CAAC,KAAKK,CAAC,CAACC,OAAO,GAAG,CAAC;EAC5G;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}