{"version": 3, "file": "paginator.mjs", "sources": ["../../../../../../src/material/paginator/paginator-intl.ts", "../../../../../../src/material/paginator/paginator.ts", "../../../../../../src/material/paginator/paginator.html", "../../../../../../src/material/paginator/module.ts", "../../../../../../src/material/paginator/paginator_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Optional, SkipSelf} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\n@Injectable({providedIn: 'root'})\nexport class MatPaginatorIntl {\n  /**\n   * Stream to emit from when labels are changed. Use this to notify components when the labels have\n   * changed after initialization.\n   */\n  readonly changes: Subject<void> = new Subject<void>();\n\n  /** A label for the page size selector. */\n  itemsPerPageLabel: string = 'Items per page:';\n\n  /** A label for the button that increments the current page. */\n  nextPageLabel: string = 'Next page';\n\n  /** A label for the button that decrements the current page. */\n  previousPageLabel: string = 'Previous page';\n\n  /** A label for the button that moves to the first page. */\n  firstPageLabel: string = 'First page';\n\n  /** A label for the button that moves to the last page. */\n  lastPageLabel: string = 'Last page';\n\n  /** A label for the range of items within the current page and the length of the whole list. */\n  getRangeLabel: (page: number, pageSize: number, length: number) => string = (\n    page: number,\n    pageSize: number,\n    length: number,\n  ) => {\n    if (length == 0 || pageSize == 0) {\n      return `0 of ${length}`;\n    }\n\n    length = Math.max(length, 0);\n\n    const startIndex = page * pageSize;\n\n    // If the start index exceeds the list length, do not try and fix the end index to the end.\n    const endIndex =\n      startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n\n    return `${startIndex + 1} – ${endIndex} of ${length}`;\n  };\n}\n\n/** @docs-private */\nexport function MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl: MatPaginatorIntl) {\n  return parentIntl || new MatPaginatorIntl();\n}\n\n/** @docs-private */\nexport const MAT_PAGINATOR_INTL_PROVIDER = {\n  // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n  provide: MatPaginatorIntl,\n  deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n  useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Directive,\n  EventEmitter,\n  Inject,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  OnInit,\n  Optional,\n  Output,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {MatFormFieldAppearance} from '@angular/material/form-field';\nimport {\n  CanDisable,\n  HasInitialized,\n  mixinDisabled,\n  mixinInitialized,\n  ThemePalette,\n} from '@angular/material/core';\nimport {Subscription} from 'rxjs';\nimport {\n  BooleanInput,\n  coerceBooleanProperty,\n  coerceNumberProperty,\n  NumberInput,\n} from '@angular/cdk/coercion';\nimport {MatPaginatorIntl} from './paginator-intl';\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n\n/** Object that can used to configure the underlying `MatSelect` inside a `MatPaginator`. */\nexport interface MatPaginatorSelectConfig {\n  /** Whether to center the active option over the trigger. */\n  disableOptionCentering?: boolean;\n\n  /** Classes to be passed to the select panel. */\n  panelClass?: string | string[] | Set<string> | {[key: string]: any};\n}\n\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nexport class PageEvent {\n  /** The current page index. */\n  pageIndex: number;\n\n  /**\n   * Index of the page that was selected previously.\n   * @breaking-change 8.0.0 To be made into a required property.\n   */\n  previousPageIndex?: number;\n\n  /** The current page size. */\n  pageSize: number;\n\n  /** The current total number of items being paged. */\n  length: number;\n}\n\n// Note that while `MatPaginatorDefaultOptions` and `MAT_PAGINATOR_DEFAULT_OPTIONS` are identical\n// between the MDC and non-MDC versions, we have to duplicate them, because the type of\n// `formFieldAppearance` is narrower in the MDC version.\n\n/** Object that can be used to configure the default options for the paginator module. */\nexport interface MatPaginatorDefaultOptions {\n  /** Number of items to display on a page. By default set to 50. */\n  pageSize?: number;\n\n  /** The set of provided page size options to display to the user. */\n  pageSizeOptions?: number[];\n\n  /** Whether to hide the page size selection UI from the user. */\n  hidePageSize?: boolean;\n\n  /** Whether to show the first/last buttons UI to the user. */\n  showFirstLastButtons?: boolean;\n\n  /** The default form-field appearance to apply to the page size options selector. */\n  formFieldAppearance?: MatFormFieldAppearance;\n}\n\n/** Injection token that can be used to provide the default options for the paginator module. */\nexport const MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken<MatPaginatorDefaultOptions>(\n  'MAT_PAGINATOR_DEFAULT_OPTIONS',\n);\n\n// Boilerplate for applying mixins to _MatPaginatorBase.\n/** @docs-private */\nconst _MatPaginatorMixinBase = mixinDisabled(mixinInitialized(class {}));\n\n/**\n * Base class with all of the `MatPaginator` functionality.\n * @docs-private\n */\n@Directive()\nexport abstract class _MatPaginatorBase<\n    O extends {\n      pageSize?: number;\n      pageSizeOptions?: number[];\n      hidePageSize?: boolean;\n      showFirstLastButtons?: boolean;\n    },\n  >\n  extends _MatPaginatorMixinBase\n  implements OnInit, OnDestroy, CanDisable, HasInitialized\n{\n  private _initialized: boolean;\n  private _intlChanges: Subscription;\n\n  /** Theme color to be used for the underlying form controls. */\n  @Input() color: ThemePalette;\n\n  /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n  @Input()\n  get pageIndex(): number {\n    return this._pageIndex;\n  }\n  set pageIndex(value: NumberInput) {\n    this._pageIndex = Math.max(coerceNumberProperty(value), 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  private _pageIndex = 0;\n\n  /** The length of the total number of items that are being paginated. Defaulted to 0. */\n  @Input()\n  get length(): number {\n    return this._length;\n  }\n  set length(value: NumberInput) {\n    this._length = coerceNumberProperty(value);\n    this._changeDetectorRef.markForCheck();\n  }\n  private _length = 0;\n\n  /** Number of items to display on a page. By default set to 50. */\n  @Input()\n  get pageSize(): number {\n    return this._pageSize;\n  }\n  set pageSize(value: NumberInput) {\n    this._pageSize = Math.max(coerceNumberProperty(value), 0);\n    this._updateDisplayedPageSizeOptions();\n  }\n  private _pageSize: number;\n\n  /** The set of provided page size options to display to the user. */\n  @Input()\n  get pageSizeOptions(): number[] {\n    return this._pageSizeOptions;\n  }\n  set pageSizeOptions(value: number[] | readonly number[]) {\n    this._pageSizeOptions = (value || []).map(p => coerceNumberProperty(p));\n    this._updateDisplayedPageSizeOptions();\n  }\n  private _pageSizeOptions: number[] = [];\n\n  /** Whether to hide the page size selection UI from the user. */\n  @Input()\n  get hidePageSize(): boolean {\n    return this._hidePageSize;\n  }\n  set hidePageSize(value: BooleanInput) {\n    this._hidePageSize = coerceBooleanProperty(value);\n  }\n  private _hidePageSize = false;\n\n  /** Whether to show the first/last buttons UI to the user. */\n  @Input()\n  get showFirstLastButtons(): boolean {\n    return this._showFirstLastButtons;\n  }\n  set showFirstLastButtons(value: BooleanInput) {\n    this._showFirstLastButtons = coerceBooleanProperty(value);\n  }\n  private _showFirstLastButtons = false;\n\n  /** Used to configure the underlying `MatSelect` inside the paginator. */\n  @Input() selectConfig: MatPaginatorSelectConfig = {};\n\n  /** Event emitted when the paginator changes the page size or page index. */\n  @Output() readonly page: EventEmitter<PageEvent> = new EventEmitter<PageEvent>();\n\n  /** Displayed set of page size options. Will be sorted and include current page size. */\n  _displayedPageSizeOptions: number[];\n\n  constructor(\n    public _intl: MatPaginatorIntl,\n    private _changeDetectorRef: ChangeDetectorRef,\n    defaults?: O,\n  ) {\n    super();\n    this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n\n    if (defaults) {\n      const {pageSize, pageSizeOptions, hidePageSize, showFirstLastButtons} = defaults;\n\n      if (pageSize != null) {\n        this._pageSize = pageSize;\n      }\n\n      if (pageSizeOptions != null) {\n        this._pageSizeOptions = pageSizeOptions;\n      }\n\n      if (hidePageSize != null) {\n        this._hidePageSize = hidePageSize;\n      }\n\n      if (showFirstLastButtons != null) {\n        this._showFirstLastButtons = showFirstLastButtons;\n      }\n    }\n  }\n\n  ngOnInit() {\n    this._initialized = true;\n    this._updateDisplayedPageSizeOptions();\n    this._markInitialized();\n  }\n\n  ngOnDestroy() {\n    this._intlChanges.unsubscribe();\n  }\n\n  /** Advances to the next page if it exists. */\n  nextPage(): void {\n    if (!this.hasNextPage()) {\n      return;\n    }\n\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex + 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n\n  /** Move back to the previous page if it exists. */\n  previousPage(): void {\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n\n  /** Move to the first page if not already there. */\n  firstPage(): void {\n    // hasPreviousPage being false implies at the start\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = 0;\n    this._emitPageEvent(previousPageIndex);\n  }\n\n  /** Move to the last page if not already there. */\n  lastPage(): void {\n    // hasNextPage being false implies at the end\n    if (!this.hasNextPage()) {\n      return;\n    }\n\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.getNumberOfPages() - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n\n  /** Whether there is a previous page. */\n  hasPreviousPage(): boolean {\n    return this.pageIndex >= 1 && this.pageSize != 0;\n  }\n\n  /** Whether there is a next page. */\n  hasNextPage(): boolean {\n    const maxPageIndex = this.getNumberOfPages() - 1;\n    return this.pageIndex < maxPageIndex && this.pageSize != 0;\n  }\n\n  /** Calculate the number of pages */\n  getNumberOfPages(): number {\n    if (!this.pageSize) {\n      return 0;\n    }\n\n    return Math.ceil(this.length / this.pageSize);\n  }\n\n  /**\n   * Changes the page size so that the first item displayed on the page will still be\n   * displayed using the new page size.\n   *\n   * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n   * switching so that the page size is 5 will set the third page as the current page so\n   * that the 10th item will still be displayed.\n   */\n  _changePageSize(pageSize: number) {\n    // Current page needs to be updated to reflect the new page size. Navigate to the page\n    // containing the previous page's first item.\n    const startIndex = this.pageIndex * this.pageSize;\n    const previousPageIndex = this.pageIndex;\n\n    this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n    this.pageSize = pageSize;\n    this._emitPageEvent(previousPageIndex);\n  }\n\n  /** Checks whether the buttons for going forwards should be disabled. */\n  _nextButtonsDisabled() {\n    return this.disabled || !this.hasNextPage();\n  }\n\n  /** Checks whether the buttons for going backwards should be disabled. */\n  _previousButtonsDisabled() {\n    return this.disabled || !this.hasPreviousPage();\n  }\n\n  /**\n   * Updates the list of page size options to display to the user. Includes making sure that\n   * the page size is an option and that the list is sorted.\n   */\n  private _updateDisplayedPageSizeOptions() {\n    if (!this._initialized) {\n      return;\n    }\n\n    // If no page size is provided, use the first page size option or the default page size.\n    if (!this.pageSize) {\n      this._pageSize =\n        this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n    }\n\n    this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n\n    if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n      this._displayedPageSizeOptions.push(this.pageSize);\n    }\n\n    // Sort the numbers using a number-specific sort function.\n    this._displayedPageSizeOptions.sort((a, b) => a - b);\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n  private _emitPageEvent(previousPageIndex: number) {\n    this.page.emit({\n      previousPageIndex,\n      pageIndex: this.pageIndex,\n      pageSize: this.pageSize,\n      length: this.length,\n    });\n  }\n}\n\nlet nextUniqueId = 0;\n\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\n@Component({\n  selector: 'mat-paginator',\n  exportAs: 'matPaginator',\n  templateUrl: 'paginator.html',\n  styleUrls: ['paginator.css'],\n  inputs: ['disabled'],\n  host: {\n    'class': 'mat-mdc-paginator',\n    'role': 'group',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n})\nexport class MatPaginator extends _MatPaginatorBase<MatPaginatorDefaultOptions> {\n  /** If set, styles the \"page size\" form field with the designated style. */\n  _formFieldAppearance?: MatFormFieldAppearance;\n\n  /** ID for the DOM node containing the paginator's items per page label. */\n  readonly _pageSizeLabelId = `mat-paginator-page-size-label-${nextUniqueId++}`;\n\n  constructor(\n    intl: MatPaginatorIntl,\n    changeDetectorRef: ChangeDetectorRef,\n    @Optional() @Inject(MAT_PAGINATOR_DEFAULT_OPTIONS) defaults?: MatPaginatorDefaultOptions,\n  ) {\n    super(intl, changeDetectorRef, defaults);\n    this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n  }\n}\n", "<div class=\"mat-mdc-paginator-outer-container\">\n  <div class=\"mat-mdc-paginator-container\">\n    <div class=\"mat-mdc-paginator-page-size\" *ngIf=\"!hidePageSize\">\n      <div class=\"mat-mdc-paginator-page-size-label\" id=\"{{_pageSizeLabelId}}\">\n        {{_intl.itemsPerPageLabel}}\n      </div>\n\n      <mat-form-field\n        *ngIf=\"_displayedPageSizeOptions.length > 1\"\n        [appearance]=\"_formFieldAppearance!\"\n        [color]=\"color\"\n        class=\"mat-mdc-paginator-page-size-select\">\n        <mat-select\n          [value]=\"pageSize\"\n          [disabled]=\"disabled\"\n          [aria-labelledby]=\"_pageSizeLabelId\"\n          [panelClass]=\"selectConfig.panelClass || ''\"\n          [disableOptionCentering]=\"selectConfig.disableOptionCentering\"\n          (selectionChange)=\"_changePageSize($event.value)\"\n          hideSingleSelectionIndicator>\n          <mat-option *ngFor=\"let pageSizeOption of _displayedPageSizeOptions\" [value]=\"pageSizeOption\">\n            {{pageSizeOption}}\n          </mat-option>\n        </mat-select>\n      </mat-form-field>\n\n      <div\n        class=\"mat-mdc-paginator-page-size-value\"\n        *ngIf=\"_displayedPageSizeOptions.length <= 1\">{{pageSize}}</div>\n    </div>\n\n    <div class=\"mat-mdc-paginator-range-actions\">\n      <div class=\"mat-mdc-paginator-range-label\" aria-live=\"polite\">\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\n      </div>\n\n      <button mat-icon-button type=\"button\"\n              class=\"mat-mdc-paginator-navigation-first\"\n              (click)=\"firstPage()\"\n              [attr.aria-label]=\"_intl.firstPageLabel\"\n              [matTooltip]=\"_intl.firstPageLabel\"\n              [matTooltipDisabled]=\"_previousButtonsDisabled()\"\n              [matTooltipPosition]=\"'above'\"\n              [disabled]=\"_previousButtonsDisabled()\"\n              *ngIf=\"showFirstLastButtons\">\n        <svg class=\"mat-mdc-paginator-icon\"\n             viewBox=\"0 0 24 24\"\n             focusable=\"false\"\n             aria-hidden=\"true\">\n          <path d=\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\"/>\n        </svg>\n      </button>\n      <button mat-icon-button type=\"button\"\n              class=\"mat-mdc-paginator-navigation-previous\"\n              (click)=\"previousPage()\"\n              [attr.aria-label]=\"_intl.previousPageLabel\"\n              [matTooltip]=\"_intl.previousPageLabel\"\n              [matTooltipDisabled]=\"_previousButtonsDisabled()\"\n              [matTooltipPosition]=\"'above'\"\n              [disabled]=\"_previousButtonsDisabled()\">\n        <svg class=\"mat-mdc-paginator-icon\"\n             viewBox=\"0 0 24 24\"\n             focusable=\"false\"\n             aria-hidden=\"true\">\n          <path d=\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"/>\n        </svg>\n      </button>\n      <button mat-icon-button type=\"button\"\n              class=\"mat-mdc-paginator-navigation-next\"\n              (click)=\"nextPage()\"\n              [attr.aria-label]=\"_intl.nextPageLabel\"\n              [matTooltip]=\"_intl.nextPageLabel\"\n              [matTooltipDisabled]=\"_nextButtonsDisabled()\"\n              [matTooltipPosition]=\"'above'\"\n              [disabled]=\"_nextButtonsDisabled()\">\n        <svg class=\"mat-mdc-paginator-icon\"\n             viewBox=\"0 0 24 24\"\n             focusable=\"false\"\n             aria-hidden=\"true\">\n          <path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"/>\n        </svg>\n      </button>\n      <button mat-icon-button type=\"button\"\n              class=\"mat-mdc-paginator-navigation-last\"\n              (click)=\"lastPage()\"\n              [attr.aria-label]=\"_intl.lastPageLabel\"\n              [matTooltip]=\"_intl.lastPageLabel\"\n              [matTooltipDisabled]=\"_nextButtonsDisabled()\"\n              [matTooltipPosition]=\"'above'\"\n              [disabled]=\"_nextButtonsDisabled()\"\n              *ngIf=\"showFirstLastButtons\">\n        <svg class=\"mat-mdc-paginator-icon\"\n             viewBox=\"0 0 24 24\"\n             focusable=\"false\"\n             aria-hidden=\"true\">\n          <path d=\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\"/>\n        </svg>\n      </button>\n    </div>\n  </div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {MAT_PAGINATOR_INTL_PROVIDER} from './paginator-intl';\nimport {MatButtonModule} from '@angular/material/button';\nimport {MatSelectModule} from '@angular/material/select';\nimport {MatTooltipModule} from '@angular/material/tooltip';\nimport {MatPaginator} from './paginator';\n\n@NgModule({\n  imports: [CommonModule, MatButtonModule, MatSelectModule, MatTooltipModule],\n  exports: [MatPaginator],\n  declarations: [MatPaginator],\n  providers: [MAT_PAGINATOR_INTL_PROVIDER],\n})\nexport class MatPaginatorModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAWA;;;AAGG;MAEU,gBAAgB,CAAA;AAD7B,IAAA,WAAA,GAAA;AAEE;;;AAGG;AACM,QAAA,IAAA,CAAA,OAAO,GAAkB,IAAI,OAAO,EAAQ,CAAC;;QAGtD,IAAiB,CAAA,iBAAA,GAAW,iBAAiB,CAAC;;QAG9C,IAAa,CAAA,aAAA,GAAW,WAAW,CAAC;;QAGpC,IAAiB,CAAA,iBAAA,GAAW,eAAe,CAAC;;QAG5C,IAAc,CAAA,cAAA,GAAW,YAAY,CAAC;;QAGtC,IAAa,CAAA,aAAA,GAAW,WAAW,CAAC;;QAGpC,IAAa,CAAA,aAAA,GAA+D,CAC1E,IAAY,EACZ,QAAgB,EAChB,MAAc,KACZ;AACF,YAAA,IAAI,MAAM,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,EAAE;gBAChC,OAAO,CAAA,KAAA,EAAQ,MAAM,CAAA,CAAE,CAAC;AACzB,aAAA;YAED,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAE7B,YAAA,MAAM,UAAU,GAAG,IAAI,GAAG,QAAQ,CAAC;;YAGnC,MAAM,QAAQ,GACZ,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,QAAQ,EAAE,MAAM,CAAC,GAAG,UAAU,GAAG,QAAQ,CAAC;YAExF,OAAO,CAAA,EAAG,UAAU,GAAG,CAAC,MAAM,QAAQ,CAAA,IAAA,EAAO,MAAM,CAAA,CAAE,CAAC;AACxD,SAAC,CAAC;AACH,KAAA;8GA1CY,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAhB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AA6ChC;AACM,SAAU,mCAAmC,CAAC,UAA4B,EAAA;AAC9E,IAAA,OAAO,UAAU,IAAI,IAAI,gBAAgB,EAAE,CAAC;AAC9C,CAAC;AAED;AACa,MAAA,2BAA2B,GAAG;;AAEzC,IAAA,OAAO,EAAE,gBAAgB;AACzB,IAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,gBAAgB,CAAC,CAAC;AAC1D,IAAA,UAAU,EAAE,mCAAmC;;;AC9BjD;AACA,MAAM,iBAAiB,GAAG,EAAE,CAAC;AAW7B;;;AAGG;MACU,SAAS,CAAA;AAerB,CAAA;AAwBD;MACa,6BAA6B,GAAG,IAAI,cAAc,CAC7D,+BAA+B,EAC/B;AAEF;AACA;AACA,MAAM,sBAAsB,GAAG,aAAa,CAAC,gBAAgB,CAAC,MAAA;AAAQ,CAAA,CAAC,CAAC,CAAC;AAEzE;;;AAGG;AAEG,MAAgB,iBAQpB,SAAQ,sBAAsB,CAAA;;AAU9B,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IACD,IAAI,SAAS,CAAC,KAAkB,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3D,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;AAID,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;IACD,IAAI,MAAM,CAAC,KAAkB,EAAA;AAC3B,QAAA,IAAI,CAAC,OAAO,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;AAID,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAkB,EAAA;AAC7B,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,+BAA+B,EAAE,CAAC;KACxC;;AAID,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;IACD,IAAI,eAAe,CAAC,KAAmC,EAAA;QACrD,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,+BAA+B,EAAE,CAAC;KACxC;;AAID,IAAA,IACI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;IACD,IAAI,YAAY,CAAC,KAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,aAAa,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KACnD;;AAID,IAAA,IACI,oBAAoB,GAAA;QACtB,OAAO,IAAI,CAAC,qBAAqB,CAAC;KACnC;IACD,IAAI,oBAAoB,CAAC,KAAmB,EAAA;AAC1C,QAAA,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC3D;AAYD,IAAA,WAAA,CACS,KAAuB,EACtB,kBAAqC,EAC7C,QAAY,EAAA;AAEZ,QAAA,KAAK,EAAE,CAAC;QAJD,IAAK,CAAA,KAAA,GAAL,KAAK,CAAkB;QACtB,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QAlEvC,IAAU,CAAA,UAAA,GAAG,CAAC,CAAC;QAWf,IAAO,CAAA,OAAA,GAAG,CAAC,CAAC;QAsBZ,IAAgB,CAAA,gBAAA,GAAa,EAAE,CAAC;QAUhC,IAAa,CAAA,aAAA,GAAG,KAAK,CAAC;QAUtB,IAAqB,CAAA,qBAAA,GAAG,KAAK,CAAC;;QAG7B,IAAY,CAAA,YAAA,GAA6B,EAAE,CAAC;;AAGlC,QAAA,IAAA,CAAA,IAAI,GAA4B,IAAI,YAAY,EAAa,CAAC;AAW/E,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC,CAAC;AAE1F,QAAA,IAAI,QAAQ,EAAE;YACZ,MAAM,EAAC,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,oBAAoB,EAAC,GAAG,QAAQ,CAAC;YAEjF,IAAI,QAAQ,IAAI,IAAI,EAAE;AACpB,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC3B,aAAA;YAED,IAAI,eAAe,IAAI,IAAI,EAAE;AAC3B,gBAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;AACzC,aAAA;YAED,IAAI,YAAY,IAAI,IAAI,EAAE;AACxB,gBAAA,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;AACnC,aAAA;YAED,IAAI,oBAAoB,IAAI,IAAI,EAAE;AAChC,gBAAA,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;AACnD,aAAA;AACF,SAAA;KACF;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,+BAA+B,EAAE,CAAC;QACvC,IAAI,CAAC,gBAAgB,EAAE,CAAC;KACzB;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;KACjC;;IAGD,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB,OAAO;AACR,SAAA;AAED,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACpC,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;KACxC;;IAGD,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;YAC3B,OAAO;AACR,SAAA;AAED,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACpC,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;KACxC;;IAGD,SAAS,GAAA;;AAEP,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;YAC3B,OAAO;AACR,SAAA;AAED,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;AACzC,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACnB,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;KACxC;;IAGD,QAAQ,GAAA;;AAEN,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB,OAAO;AACR,SAAA;AAED,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;KACxC;;IAGD,eAAe,GAAA;QACb,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;KAClD;;IAGD,WAAW,GAAA;QACT,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,SAAS,GAAG,YAAY,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;KAC5D;;IAGD,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,OAAO,CAAC,CAAC;AACV,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC/C;AAED;;;;;;;AAOG;AACH,IAAA,eAAe,CAAC,QAAgB,EAAA;;;QAG9B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClD,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;AAEzC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;KACxC;;IAGD,oBAAoB,GAAA;QAClB,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;KAC7C;;IAGD,wBAAwB,GAAA;QACtB,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;KACjD;AAED;;;AAGG;IACK,+BAA+B,GAAA;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,OAAO;AACR,SAAA;;AAGD,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS;AACZ,gBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC;AAClF,SAAA;QAED,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;AAE9D,QAAA,IAAI,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;YAChE,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACpD,SAAA;;AAGD,QAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;AAGO,IAAA,cAAc,CAAC,iBAAyB,EAAA;AAC9C,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACb,iBAAiB;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;AACpB,SAAA,CAAC,CAAC;KACJ;8GAlQmB,iBAAiB,EAAA,IAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAjB,iBAAiB,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,UAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBADtC,SAAS;yJAgBC,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAIF,SAAS,EAAA,CAAA;sBADZ,KAAK;gBAYF,MAAM,EAAA,CAAA;sBADT,KAAK;gBAYF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAYF,eAAe,EAAA,CAAA;sBADlB,KAAK;gBAYF,YAAY,EAAA,CAAA;sBADf,KAAK;gBAWF,oBAAoB,EAAA,CAAA;sBADvB,KAAK;gBAUG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAGa,IAAI,EAAA,CAAA;sBAAtB,MAAM;;AAgLT,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB;;;;AAIG;AAcG,MAAO,YAAa,SAAQ,iBAA6C,CAAA;AAO7E,IAAA,WAAA,CACE,IAAsB,EACtB,iBAAoC,EACe,QAAqC,EAAA;AAExF,QAAA,KAAK,CAAC,IAAI,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC;;AAPlC,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAA,8BAAA,EAAiC,YAAY,EAAE,EAAE,CAAC;QAQ5E,IAAI,CAAC,oBAAoB,GAAG,QAAQ,EAAE,mBAAmB,IAAI,SAAS,CAAC;KACxE;AAdU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,gFAUD,6BAA6B,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAVxC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,0NCtYzB,swIAqGA,EAAA,MAAA,EAAA,CAAA,kzDAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,aAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,eAAA,EAAA,OAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,YAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,oBAAA,EAAA,OAAA,EAAA,YAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,eAAA,EAAA,UAAA,EAAA,YAAA,EAAA,8BAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,YAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,EAAA,QAAA,EAAA,cAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDiSa,YAAY,EAAA,UAAA,EAAA,CAAA;kBAbxB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,YACf,cAAc,EAAA,MAAA,EAGhB,CAAC,UAAU,CAAC,EACd,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,mBAAmB;AAC5B,wBAAA,MAAM,EAAE,OAAO;AAChB,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,swIAAA,EAAA,MAAA,EAAA,CAAA,kzDAAA,CAAA,EAAA,CAAA;;0BAYlC,QAAQ;;0BAAI,MAAM;2BAAC,6BAA6B,CAAA;;;ME1XxC,kBAAkB,CAAA;8GAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAlB,kBAAkB,EAAA,YAAA,EAAA,CAHd,YAAY,CAAA,EAAA,OAAA,EAAA,CAFjB,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,CAAA,EAAA,OAAA,EAAA,CAChE,YAAY,CAAA,EAAA,CAAA,CAAA,EAAA;+GAIX,kBAAkB,EAAA,SAAA,EAFlB,CAAC,2BAA2B,CAAC,EAAA,OAAA,EAAA,CAH9B,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAK/D,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAN9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,CAAC;oBAC3E,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,YAAY,EAAE,CAAC,YAAY,CAAC;oBAC5B,SAAS,EAAE,CAAC,2BAA2B,CAAC;AACzC,iBAAA,CAAA;;;ACrBD;;AAEG;;;;"}