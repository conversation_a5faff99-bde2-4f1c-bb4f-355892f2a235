{"ast": null, "code": "/**\n * This method is like `_.tap` except that it returns the result of `interceptor`.\n * The purpose of this method is to \"pass thru\" values replacing intermediate\n * results in a method chain sequence.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Seq\n * @param {*} value The value to provide to `interceptor`.\n * @param {Function} interceptor The function to invoke.\n * @returns {*} Returns the result of `interceptor`.\n * @example\n *\n * _('  abc  ')\n *  .chain()\n *  .trim()\n *  .thru(function(value) {\n *    return [value];\n *  })\n *  .value();\n * // => ['abc']\n */\nfunction thru(value, interceptor) {\n  return interceptor(value);\n}\nexport default thru;", "map": {"version": 3, "names": ["thru", "value", "interceptor"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/thru.js"], "sourcesContent": ["/**\n * This method is like `_.tap` except that it returns the result of `interceptor`.\n * The purpose of this method is to \"pass thru\" values replacing intermediate\n * results in a method chain sequence.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Seq\n * @param {*} value The value to provide to `interceptor`.\n * @param {Function} interceptor The function to invoke.\n * @returns {*} Returns the result of `interceptor`.\n * @example\n *\n * _('  abc  ')\n *  .chain()\n *  .trim()\n *  .thru(function(value) {\n *    return [value];\n *  })\n *  .value();\n * // => ['abc']\n */\nfunction thru(value, interceptor) {\n  return interceptor(value);\n}\n\nexport default thru;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAIA,CAACC,KAAK,EAAEC,WAAW,EAAE;EAChC,OAAOA,WAAW,CAACD,KAAK,CAAC;AAC3B;AAEA,eAAeD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}