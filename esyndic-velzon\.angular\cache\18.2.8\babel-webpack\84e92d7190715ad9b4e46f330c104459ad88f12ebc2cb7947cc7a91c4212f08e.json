{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// These APIs are for more advanced usages\n// For example extend charts and components, creating graphic elements, formatting.\nimport ComponentModel from '../model/Component.js';\nimport ComponentView from '../view/Component.js';\nimport SeriesModel from '../model/Series.js';\nimport ChartView from '../view/Chart.js';\nimport SeriesData from '../data/SeriesData.js';\nimport * as zrender_1 from 'zrender/lib/zrender.js';\nexport { zrender_1 as zrender };\nimport * as matrix_1 from 'zrender/lib/core/matrix.js';\nexport { matrix_1 as matrix };\nimport * as vector_1 from 'zrender/lib/core/vector.js';\nexport { vector_1 as vector };\nimport * as zrUtil_1 from 'zrender/lib/core/util.js';\nexport { zrUtil_1 as zrUtil };\nimport * as color_1 from 'zrender/lib/tool/color.js';\nexport { color_1 as color };\nexport { throttle } from '../util/throttle.js';\nimport * as helper_1 from './api/helper.js';\nexport { helper_1 as helper };\nexport { use } from '../extension.js';\nexport { setPlatformAPI } from 'zrender/lib/core/platform.js';\n// --------------------- Helper Methods ---------------------\nexport { default as parseGeoJSON } from '../coord/geo/parseGeoJson.js';\nexport { default as parseGeoJson } from '../coord/geo/parseGeoJson.js';\nimport * as number_1 from './api/number.js';\nexport { number_1 as number };\nimport * as time_1 from './api/time.js';\nexport { time_1 as time };\nimport * as graphic_1 from './api/graphic.js';\nexport { graphic_1 as graphic };\nimport * as format_1 from './api/format.js';\nexport { format_1 as format };\nimport * as util_1 from './api/util.js';\nexport { util_1 as util };\nexport { default as env } from 'zrender/lib/core/env.js';\n// --------------------- Export for Extension Usage ---------------------\n// export {SeriesData};\nexport { SeriesData as List }; // TODO: Compatitable with exists echarts-gl code\nexport { default as Model } from '../model/Model.js';\nexport { default as Axis } from '../coord/Axis.js';\nexport { ComponentModel, ComponentView, SeriesModel, ChartView };\n// Only for GL\nexport { brushSingle as innerDrawElementOnCanvas } from 'zrender/lib/canvas/graphic.js';\n// --------------------- Deprecated Extension Methods ---------------------\n// Should use `ComponentModel.extend` or `class XXXX extend ComponentModel` to create class.\n// Then use `registerComponentModel` in `install` parameter when `use` this extension. For example:\n// class Bar3DModel extends ComponentModel {}\n// export function install(registers) { registers.registerComponentModel(Bar3DModel); }\n// echarts.use(install);\nexport function extendComponentModel(proto) {\n  var Model = ComponentModel.extend(proto);\n  ComponentModel.registerClass(Model);\n  return Model;\n}\nexport function extendComponentView(proto) {\n  var View = ComponentView.extend(proto);\n  ComponentView.registerClass(View);\n  return View;\n}\nexport function extendSeriesModel(proto) {\n  var Model = SeriesModel.extend(proto);\n  SeriesModel.registerClass(Model);\n  return Model;\n}\nexport function extendChartView(proto) {\n  var View = ChartView.extend(proto);\n  ChartView.registerClass(View);\n  return View;\n}", "map": {"version": 3, "names": ["ComponentModel", "ComponentView", "SeriesModel", "ChartView", "SeriesData", "zrender_1", "zrender", "matrix_1", "matrix", "vector_1", "vector", "zrUtil_1", "zrUtil", "color_1", "color", "throttle", "helper_1", "helper", "use", "setPlatformAPI", "default", "parseGeoJSON", "parseGeoJson", "number_1", "number", "time_1", "time", "graphic_1", "graphic", "format_1", "format", "util_1", "util", "env", "List", "Model", "Axis", "brushSingle", "innerDrawElementOnCanvas", "extendComponentModel", "proto", "extend", "registerClass", "extendComponentView", "View", "extendSeriesModel", "extendChartView"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/export/api.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// These APIs are for more advanced usages\n// For example extend charts and components, creating graphic elements, formatting.\nimport ComponentModel from '../model/Component.js';\nimport ComponentView from '../view/Component.js';\nimport SeriesModel from '../model/Series.js';\nimport ChartView from '../view/Chart.js';\nimport SeriesData from '../data/SeriesData.js';\nimport * as zrender_1 from 'zrender/lib/zrender.js';\nexport { zrender_1 as zrender };\nimport * as matrix_1 from 'zrender/lib/core/matrix.js';\nexport { matrix_1 as matrix };\nimport * as vector_1 from 'zrender/lib/core/vector.js';\nexport { vector_1 as vector };\nimport * as zrUtil_1 from 'zrender/lib/core/util.js';\nexport { zrUtil_1 as zrUtil };\nimport * as color_1 from 'zrender/lib/tool/color.js';\nexport { color_1 as color };\nexport { throttle } from '../util/throttle.js';\nimport * as helper_1 from './api/helper.js';\nexport { helper_1 as helper };\nexport { use } from '../extension.js';\nexport { setPlatformAPI } from 'zrender/lib/core/platform.js';\n// --------------------- Helper Methods ---------------------\nexport { default as parseGeoJSON } from '../coord/geo/parseGeoJson.js';\nexport { default as parseGeoJson } from '../coord/geo/parseGeoJson.js';\nimport * as number_1 from './api/number.js';\nexport { number_1 as number };\nimport * as time_1 from './api/time.js';\nexport { time_1 as time };\nimport * as graphic_1 from './api/graphic.js';\nexport { graphic_1 as graphic };\nimport * as format_1 from './api/format.js';\nexport { format_1 as format };\nimport * as util_1 from './api/util.js';\nexport { util_1 as util };\nexport { default as env } from 'zrender/lib/core/env.js';\n// --------------------- Export for Extension Usage ---------------------\n// export {SeriesData};\nexport { SeriesData as List }; // TODO: Compatitable with exists echarts-gl code\nexport { default as Model } from '../model/Model.js';\nexport { default as Axis } from '../coord/Axis.js';\nexport { ComponentModel, ComponentView, SeriesModel, ChartView };\n// Only for GL\nexport { brushSingle as innerDrawElementOnCanvas } from 'zrender/lib/canvas/graphic.js';\n// --------------------- Deprecated Extension Methods ---------------------\n// Should use `ComponentModel.extend` or `class XXXX extend ComponentModel` to create class.\n// Then use `registerComponentModel` in `install` parameter when `use` this extension. For example:\n// class Bar3DModel extends ComponentModel {}\n// export function install(registers) { registers.registerComponentModel(Bar3DModel); }\n// echarts.use(install);\nexport function extendComponentModel(proto) {\n  var Model = ComponentModel.extend(proto);\n  ComponentModel.registerClass(Model);\n  return Model;\n}\nexport function extendComponentView(proto) {\n  var View = ComponentView.extend(proto);\n  ComponentView.registerClass(View);\n  return View;\n}\nexport function extendSeriesModel(proto) {\n  var Model = SeriesModel.extend(proto);\n  SeriesModel.registerClass(Model);\n  return Model;\n}\nexport function extendChartView(proto) {\n  var View = ChartView.extend(proto);\n  ChartView.registerClass(View);\n  return View;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,cAAc,MAAM,uBAAuB;AAClD,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAO,KAAKC,SAAS,MAAM,wBAAwB;AACnD,SAASA,SAAS,IAAIC,OAAO;AAC7B,OAAO,KAAKC,QAAQ,MAAM,4BAA4B;AACtD,SAASA,QAAQ,IAAIC,MAAM;AAC3B,OAAO,KAAKC,QAAQ,MAAM,4BAA4B;AACtD,SAASA,QAAQ,IAAIC,MAAM;AAC3B,OAAO,KAAKC,QAAQ,MAAM,0BAA0B;AACpD,SAASA,QAAQ,IAAIC,MAAM;AAC3B,OAAO,KAAKC,OAAO,MAAM,2BAA2B;AACpD,SAASA,OAAO,IAAIC,KAAK;AACzB,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,OAAO,KAAKC,QAAQ,MAAM,iBAAiB;AAC3C,SAASA,QAAQ,IAAIC,MAAM;AAC3B,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SAASC,cAAc,QAAQ,8BAA8B;AAC7D;AACA,SAASC,OAAO,IAAIC,YAAY,QAAQ,8BAA8B;AACtE,SAASD,OAAO,IAAIE,YAAY,QAAQ,8BAA8B;AACtE,OAAO,KAAKC,QAAQ,MAAM,iBAAiB;AAC3C,SAASA,QAAQ,IAAIC,MAAM;AAC3B,OAAO,KAAKC,MAAM,MAAM,eAAe;AACvC,SAASA,MAAM,IAAIC,IAAI;AACvB,OAAO,KAAKC,SAAS,MAAM,kBAAkB;AAC7C,SAASA,SAAS,IAAIC,OAAO;AAC7B,OAAO,KAAKC,QAAQ,MAAM,iBAAiB;AAC3C,SAASA,QAAQ,IAAIC,MAAM;AAC3B,OAAO,KAAKC,MAAM,MAAM,eAAe;AACvC,SAASA,MAAM,IAAIC,IAAI;AACvB,SAASZ,OAAO,IAAIa,GAAG,QAAQ,yBAAyB;AACxD;AACA;AACA,SAAS7B,UAAU,IAAI8B,IAAI,GAAG,CAAC;AAC/B,SAASd,OAAO,IAAIe,KAAK,QAAQ,mBAAmB;AACpD,SAASf,OAAO,IAAIgB,IAAI,QAAQ,kBAAkB;AAClD,SAASpC,cAAc,EAAEC,aAAa,EAAEC,WAAW,EAAEC,SAAS;AAC9D;AACA,SAASkC,WAAW,IAAIC,wBAAwB,QAAQ,+BAA+B;AACvF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EAC1C,IAAIL,KAAK,GAAGnC,cAAc,CAACyC,MAAM,CAACD,KAAK,CAAC;EACxCxC,cAAc,CAAC0C,aAAa,CAACP,KAAK,CAAC;EACnC,OAAOA,KAAK;AACd;AACA,OAAO,SAASQ,mBAAmBA,CAACH,KAAK,EAAE;EACzC,IAAII,IAAI,GAAG3C,aAAa,CAACwC,MAAM,CAACD,KAAK,CAAC;EACtCvC,aAAa,CAACyC,aAAa,CAACE,IAAI,CAAC;EACjC,OAAOA,IAAI;AACb;AACA,OAAO,SAASC,iBAAiBA,CAACL,KAAK,EAAE;EACvC,IAAIL,KAAK,GAAGjC,WAAW,CAACuC,MAAM,CAACD,KAAK,CAAC;EACrCtC,WAAW,CAACwC,aAAa,CAACP,KAAK,CAAC;EAChC,OAAOA,KAAK;AACd;AACA,OAAO,SAASW,eAAeA,CAACN,KAAK,EAAE;EACrC,IAAII,IAAI,GAAGzC,SAAS,CAACsC,MAAM,CAACD,KAAK,CAAC;EAClCrC,SAAS,CAACuC,aAAa,CAACE,IAAI,CAAC;EAC7B,OAAOA,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}