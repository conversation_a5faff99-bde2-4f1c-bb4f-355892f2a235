package com.esyndic.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "assembly_attendance", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"assembly_id", "user_id"})
})
public class AssemblyAttendance {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(nullable = false)
    private Boolean attended = false;

    @Column(name = "attendance_time")
    private LocalDateTime attendanceTime;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assembly_id", nullable = false)
    @NotNull(message = "Assembly is required")
    private Assembly assembly;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @NotNull(message = "User is required")
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "apartment_id")
    private Apartment apartment;

    // Constructors
    public AssemblyAttendance() {}

    public AssemblyAttendance(Assembly assembly, User user, Apartment apartment) {
        this.assembly = assembly;
        this.user = user;
        this.apartment = apartment;
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public Boolean getAttended() {
        return attended;
    }

    public void setAttended(Boolean attended) {
        this.attended = attended;
        if (attended && attendanceTime == null) {
            this.attendanceTime = LocalDateTime.now();
        }
    }

    public LocalDateTime getAttendanceTime() {
        return attendanceTime;
    }

    public void setAttendanceTime(LocalDateTime attendanceTime) {
        this.attendanceTime = attendanceTime;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public Assembly getAssembly() {
        return assembly;
    }

    public void setAssembly(Assembly assembly) {
        this.assembly = assembly;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Apartment getApartment() {
        return apartment;
    }

    public void setApartment(Apartment apartment) {
        this.apartment = apartment;
    }

    // Helper methods
    public void markAttended() {
        this.attended = true;
        this.attendanceTime = LocalDateTime.now();
    }

    public void markAbsent() {
        this.attended = false;
        this.attendanceTime = null;
    }

    @Override
    public String toString() {
        return "AssemblyAttendance{" +
                "id=" + id +
                ", attended=" + attended +
                ", attendanceTime=" + attendanceTime +
                ", user=" + (user != null ? user.getUsername() : null) +
                ", apartment=" + (apartment != null ? apartment.getApartmentNumber() : null) +
                '}';
    }
}
