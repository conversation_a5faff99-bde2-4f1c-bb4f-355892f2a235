{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport ComponentView from '../../view/Component.js';\nvar DataZoomView = /** @class */function (_super) {\n  __extends(DataZoomView, _super);\n  function DataZoomView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = DataZoomView.type;\n    return _this;\n  }\n  DataZoomView.prototype.render = function (dataZoomModel, ecModel, api, payload) {\n    this.dataZoomModel = dataZoomModel;\n    this.ecModel = ecModel;\n    this.api = api;\n  };\n  DataZoomView.type = 'dataZoom';\n  return DataZoomView;\n}(ComponentView);\nexport default DataZoomView;", "map": {"version": 3, "names": ["__extends", "ComponentView", "DataZoomView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "dataZoomModel", "ecModel", "api", "payload"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/component/dataZoom/DataZoomView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport ComponentView from '../../view/Component.js';\nvar DataZoomView = /** @class */function (_super) {\n  __extends(DataZoomView, _super);\n  function DataZoomView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = DataZoomView.type;\n    return _this;\n  }\n  DataZoomView.prototype.render = function (dataZoomModel, ecModel, api, payload) {\n    this.dataZoomModel = dataZoomModel;\n    this.ecModel = ecModel;\n    this.api = api;\n  };\n  DataZoomView.type = 'dataZoom';\n  return DataZoomView;\n}(ComponentView);\nexport default DataZoomView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,IAAIC,YAAY,GAAG,aAAa,UAAUC,MAAM,EAAE;EAChDH,SAAS,CAACE,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,YAAY,CAACK,IAAI;IAC9B,OAAOH,KAAK;EACd;EACAF,YAAY,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,aAAa,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IAC9E,IAAI,CAACH,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,GAAG,GAAGA,GAAG;EAChB,CAAC;EACDV,YAAY,CAACK,IAAI,GAAG,UAAU;EAC9B,OAAOL,YAAY;AACrB,CAAC,CAACD,aAAa,CAAC;AAChB,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}