<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Area Charts" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Basic Column Charts</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="basicChart.series" [chart]="basicChart.chart" [dataLabels]="basicChart.dataLabels"
                    [plotOptions]="basicChart.plotOptions" [yaxis]="basicChart.yaxis" [legend]="basicChart.legend"
                    [fill]="basicChart.fill" [stroke]="basicChart.stroke" [tooltip]="basicChart.tooltip"
                    [xaxis]="basicChart.xaxis" [colors]="basicChart.colors" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->

    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Column with Data Labels</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="columnWithDataChart.series" [chart]="columnWithDataChart.chart"
                    [dataLabels]="columnWithDataChart.dataLabels" [plotOptions]="columnWithDataChart.plotOptions"
                    [yaxis]="columnWithDataChart.yaxis" [xaxis]="columnWithDataChart.xaxis"
                    [fill]="columnWithDataChart.fill" [title]="columnWithDataChart.title"
                    [colors]="columnWithDataChart.colors" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div>

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Stacked Column Charts</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="StackedColumnChart.series" [chart]="StackedColumnChart.chart"
                    [dataLabels]="StackedColumnChart.dataLabels" [plotOptions]="StackedColumnChart.plotOptions"
                    [responsive]="StackedColumnChart.responsive" [xaxis]="StackedColumnChart.xaxis"
                    [legend]="StackedColumnChart.legend" [fill]="StackedColumnChart.fill"
                    [colors]="StackedColumnChart.colors" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->

    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Stacked Column 100</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="StackedColumn100Chart.series" [chart]="StackedColumn100Chart.chart"
                    [dataLabels]="StackedColumn100Chart.dataLabels" [plotOptions]="StackedColumn100Chart.plotOptions"
                    [responsive]="StackedColumn100Chart.responsive" [xaxis]="StackedColumn100Chart.xaxis"
                    [legend]="StackedColumn100Chart.legend" [fill]="StackedColumn100Chart.fill"
                    [colors]="StackedColumn100Chart.colors" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Grouped Stacked Columns</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="groupedstackedcolumns.series" [chart]="groupedstackedcolumns.chart"
                    [dataLabels]="groupedstackedcolumns.dataLabels" [plotOptions]="groupedstackedcolumns.plotOptions"
                    [responsive]="groupedstackedcolumns.responsive" [xaxis]="groupedstackedcolumns.xaxis"
                    [legend]="groupedstackedcolumns.legend" [fill]="groupedstackedcolumns.fill"
                    [colors]="groupedstackedcolumns.colors" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Dumbbell Chart</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="dumbbellchart.series" [chart]="dumbbellchart.chart"
                    [dataLabels]="dumbbellchart.dataLabels" [plotOptions]="dumbbellchart.plotOptions"
                    [responsive]="dumbbellchart.responsive" [xaxis]="dumbbellchart.xaxis"
                    [legend]="dumbbellchart.legend" [fill]="dumbbellchart.fill" [colors]="dumbbellchart.colors"
                    [yaxis]="dumbbellchart.yaxis" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div><!--end row-->

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Column with Markers</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="ColumnWithMarkerChart.series" [chart]="ColumnWithMarkerChart.chart"
                    [legend]="ColumnWithMarkerChart.legend" [dataLabels]="ColumnWithMarkerChart.dataLabels"
                    [colors]="ColumnWithMarkerChart.colors" [plotOptions]="ColumnWithMarkerChart.plotOptions" dir="ltr">
                </apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->

    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Column with Rotated Labels</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="ColumnWithRotatedChart.series" [chart]="ColumnWithRotatedChart.chart"
                    [dataLabels]="ColumnWithRotatedChart.dataLabels" [plotOptions]="ColumnWithRotatedChart.plotOptions"
                    [yaxis]="ColumnWithRotatedChart.yaxis" [xaxis]="ColumnWithRotatedChart.xaxis"
                    [stroke]="ColumnWithRotatedChart.stroke" [grid]="ColumnWithRotatedChart.grid"
                    [fill]="ColumnWithRotatedChart.fill" [annotations]="ColumnWithRotatedChart.annotations"
                    [colors]="ColumnWithRotatedChart.colors" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Column with Nagetive Values</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="ColumnWithNagetiveChart.series" [chart]="ColumnWithNagetiveChart.chart"
                    [dataLabels]="ColumnWithNagetiveChart.dataLabels" [colors]="ColumnWithNagetiveChart.colors"
                    [plotOptions]="ColumnWithNagetiveChart.plotOptions" [yaxis]="ColumnWithNagetiveChart.yaxis"
                    [xaxis]="ColumnWithNagetiveChart.xaxis" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->

    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Range Column Chart</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="rangeColumnChart.series" [chart]="rangeColumnChart.chart"
                    [dataLabels]="rangeColumnChart.dataLabels" [plotOptions]="rangeColumnChart.plotOptions"
                    [colors]="rangeColumnChart.colors" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Dynamic Loaded Chart</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <div id="dynamicloadedchart-wrap" dir="ltr">
                    <div id="chart-year" class="apex-charts">
                        <apx-chart [series]="dynamicLoadedChart.series" [chart]="dynamicLoadedChart.chart"
                            [dataLabels]="dynamicLoadedChart.dataLabels" [plotOptions]="dynamicLoadedChart.plotOptions"
                            [yaxis]="dynamicLoadedChart.yaxis" [xaxis]="dynamicLoadedChart.xaxis"
                            [subtitle]="dynamicLoadedChart.subtitle" [colors]="dynamicLoadedChart.colors"
                            [states]="dynamicLoadedChart.states" [title]="dynamicLoadedChart.title"
                            [tooltip]="dynamicLoadedChart.tooltip" dir="ltr"></apx-chart>
                    </div>
                    <div id="chart-quarter" class="apex-charts">
                        <apx-chart [series]="dynamicQuarterLoadedChart.series" [chart]="dynamicQuarterLoadedChart.chart"
                            [legend]="dynamicQuarterLoadedChart.legend"
                            [plotOptions]="dynamicQuarterLoadedChart.plotOptions"
                            [yaxis]="dynamicQuarterLoadedChart.yaxis" [xaxis]="dynamicQuarterLoadedChart.xaxis"
                            [grid]="dynamicQuarterLoadedChart.grid" [title]="dynamicQuarterLoadedChart.title"
                            [colors]="dynamicQuarterLoadedChart.colors" dir="ltr">
                        </apx-chart>
                    </div>
                </div>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->

    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Distributed Columns Chart</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="distributedColumnChart.series" [chart]="distributedColumnChart.chart"
                    [dataLabels]="distributedColumnChart.dataLabels" [plotOptions]="distributedColumnChart.plotOptions"
                    [yaxis]="distributedColumnChart.yaxis" [xaxis]="distributedColumnChart.xaxis"
                    [legend]="distributedColumnChart.legend" [colors]="distributedColumnChart.colors"
                    [grid]="distributedColumnChart.grid" dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Column with Group Label</h4>
            </div><!-- end card header -->

            <div class="card-body">
                <apx-chart [series]="groupLabelChart.series" [chart]="groupLabelChart.chart"
                    [plotOptions]="groupLabelChart.plotOptions" [colors]="groupLabelChart.colors"
                    [xaxis]="groupLabelChart.xaxis" [title]="groupLabelChart.title" [tooltip]="groupLabelChart.tooltip"
                    dir="ltr"></apx-chart>
            </div><!-- end card-body -->
        </div><!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->