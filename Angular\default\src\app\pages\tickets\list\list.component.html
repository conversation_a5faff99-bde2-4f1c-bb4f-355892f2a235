<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Invoice List" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->


<div class="row">
    <div class="col-xxl-3 col-sm-6">
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="fw-medium text-muted mb-0">Total Tickets</p>
                        <h2 class="mt-4 ff-secondary fw-semibold"><span [countUp]="547" class="counter-value" [options]="option"></span>k</h2>
                        <p class="mb-0 text-muted"><span class="badge bg-light text-success mb-0">
                                <i class="ri-arrow-up-line align-middle"></i> 17.32 %
                            </span> vs. previous month</p>
                    </div>
                    <div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-info-subtle text-info rounded-circle fs-4">
                                <i class="ri-ticket-2-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div><!-- end card body -->
        </div> <!-- end card-->
    </div>
    <!--end col-->
    <div class="col-xxl-3 col-sm-6">
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="fw-medium text-muted mb-0">Pending Tickets</p>
                        <h2 class="mt-4 ff-secondary fw-semibold"><span [countUp]="124" class="counter-value" [options]="option"></span>k</h2>
                        <p class="mb-0 text-muted"><span class="badge bg-light text-danger mb-0">
                                <i class="ri-arrow-down-line align-middle"></i> 0.96 %
                            </span> vs. previous month</p>
                    </div>
                    <div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-info-subtle text-info rounded-circle fs-4">
                                <i class="mdi mdi-timer-sand"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div><!-- end card body -->
        </div>
    </div>
    <!--end col-->
    <div class="col-xxl-3 col-sm-6">
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="fw-medium text-muted mb-0">Closed Tickets</p>
                        <h2 class="mt-4 ff-secondary fw-semibold"><span [countUp]="107" class="counter-value" [options]="option"></span>K</h2>
                        <p class="mb-0 text-muted"><span class="badge bg-light text-danger mb-0">
                                <i class="ri-arrow-down-line align-middle"></i> 3.87 %
                            </span> vs. previous month</p>
                    </div>
                    <div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-info-subtle text-info rounded-circle fs-4">
                                <i class="ri-shopping-bag-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div><!-- end card body -->
        </div>
    </div>
    <!--end col-->
    <div class="col-xxl-3 col-sm-6">
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="fw-medium text-muted mb-0">Deleted Tickets</p>
                        <h2 class="mt-4 ff-secondary fw-semibold"><span [countUp]="15.95" class="counter-value" [options]="option"></span>%</h2>
                        <p class="mb-0 text-muted"><span class="badge bg-light text-success mb-0">
                                <i class="ri-arrow-up-line align-middle"></i> 1.09 %
                            </span> vs. previous month</p>
                    </div>
                    <div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-info-subtle text-info rounded-circle fs-4">
                                <i class="ri-delete-bin-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div><!-- end card body -->
        </div>
    </div>
    <!--end col-->
</div>
<!--end row-->

<div class="row">
    <div class="col-lg-12">
        <div class="card" id="ticketsList">
            <div class="card-header border-0">
                <div class="d-flex align-items-center">
                    <h5 class="card-title mb-0 flex-grow-1">Tickets</h5>
                    <div class="flex-shrink-0  hstack gap-2">
                        <button class="btn btn-danger add-btn" data-bs-toggle="modal" data-bs-target="#showModal" (click)="openModal(content)"><i class="ri-add-line align-bottom me-1"></i> Create
                            Tickets</button>
                        <button class="btn btn-soft-danger" id="remove-actions" style="display: none" (click)="deleteMultiple(deleteModel)"><i class="ri-delete-bin-2-line"></i></button>
                    </div>
                </div>
            </div>
            <div class="card-body border border-dashed border-end-0 border-start-0">

                <div class="row g-3">
                    <div class="col-xxl-5 col-sm-12">
                        <div class="search-box">
                            <input type="text" name="searchTerm" class="form-control search bg-light border-light" placeholder="Search for ticket details or something..." [(ngModel)]="searchTerm" (ngModelChange)="performSearch()">
                            <i class="ri-search-line search-icon"></i>
                        </div>
                    </div>
                    <!--end col-->

                    <div class="col-xxl-3 col-sm-4">
                        <input class="form-control bg-light border-light" type="text" mwlFlatpickr [altInput]="true" [convertModelValue]="true" placeholder="Select date range" id="isDate" [(ngModel)]="date" mode="range">
                    </div>
                    <!--end col-->

                    <div class="col-xxl-3 col-sm-4">
                        <div class="input-light">
                            <select class="form-control" data-choices data-choices-search-false name="choices-single-default" id="idStatus" [(ngModel)]="status" (ngModelChange)="statusFilter()">
                                <option value="" selected>Select Status</option>
                                <option value="Open">Open</option>
                                <option value="Inprogress">Inprogress</option>
                                <option value="Closed">Closed</option>
                                <option value="New">New</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-xxl-1 col-sm-4">
                        <button type="button" class="btn btn-primary w-100" (click)="SearchData();"> <i class="ri-equalizer-fill mx-1 align-bottom"></i>
                            Filters
                        </button>
                    </div>
                    <!--end col-->
                </div>
                <!--end row-->

            </div>
            <!--end card-body-->
            <div class="card-body">
                <div class="table-responsive table-card mb-3">
                    <table class="table align-middle table-nowrap mb-0" id="ticketTable">
                        <thead>
                            <tr>
                                <th scope="col" style="width: 40px;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="checkAll" value="option" [(ngModel)]="masterSelected" (change)="checkUncheckAll($event)">
                                    </div>
                                </th>
                                <th class="sort" (click)="onSort('ticketId')">ID</th>
                                <th class="sort" (click)="onSort('title')">Title</th>
                                <th class="sort" (click)="onSort('client')">Client</th>
                                <th class="sort" (click)="onSort('assigned')">Assigned To</th>
                                <th class="sort" (click)="onSort('create')">Create Date</th>
                                <th class="sort" (click)="onSort('dueDate')">Due Date</th>
                                <th class="sort" (click)="onSort('status')">Status</th>
                                <th class="sort" (click)="onSort('priority')">Priority</th>
                                <th class="sort">Action</th>
                            </tr>
                        </thead>
                        <tbody class="list form-check-all" id="ticket-list-data">
                            @for ( data of lists; track $index) {
                            <tr id="t_{{data._id}}">
                                <th scope="row">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="checkAll" value="{{data._id}}" [(ngModel)]="data.state" (change)="onCheckboxChange($event)">
                                    </div>
                                </th>
                                <td class="id"><a routerLink="/tickets/details" class="fw-medium link-primary">{{data.id}}</a></td>
                                <td>
                                    <ngb-highlight [result]="data.title" [term]="searchTerm"></ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="data.client" [term]="searchTerm"></ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="data.assigned" [term]="searchTerm"></ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="data.create | date :'longDate'" [term]="searchTerm"></ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="data.due | date :'longDate'" [term]="searchTerm">
                                    </ngb-highlight>
                                </td>
                                <td class="status"><span class="badge text-uppercase" [ngClass]=" { 'bg-danger-subtle text-danger': data.status === 'Closed', 'bg-warning-subtle text-warning': data.status === 'Inprogress', 'bg-success-subtle text-success': data.status === 'Open', 'bg-info-subtle text-info': data.status === 'New'}">{{data.status}}</span>
                                </td>
                                <td class="priority"><span class="badge text-uppercase" [ngClass]=" { 'bg-success ': data.priority === 'Low', 'bg-danger': data.priority === 'High', 'bg-warning': data.priority === 'Medium'}">{{data.priority}}</span>
                                </td>
                                <td>
                                    <div class="dropdown" ngbDropdown>
                                        <button class="btn btn-soft-secondary btn-sm dropdown arrow-none" type="button" data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle>
                                            <i class="ri-more-fill align-middle"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                                            <li><a class="dropdown-item" routerLink="/tickets/details"><i class="ri-eye-fill align-bottom me-2 text- float-start"></i>
                                                    View</a>
                                            </li>
                                            <li><a class="dropdown-item edit-item-btn" data-bs-toggle="modal" (click)="editDataGet($index,content)"><i class="ri-pencil-fill align-bottom me-2 text-muted float-start"></i>
                                                    Edit</a></li>
                                            <li>
                                                <a class="dropdown-item remove-item-btn" data-bs-toggle="modal" (click)="confirm(deleteModel,data._id)">
                                                    <i class="ri-delete-bin-fill align-bottom me-2 text-muted float-start"></i>
                                                    Delete
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-end mt-2">
                    <!-- Pagination -->
                    <ngb-pagination [collectionSize]="alllists?.length" [(page)]="service.page" [pageSize]="service.pageSize" (pageChange)="changePage()">
                    </ngb-pagination>
                    <!-- End Pagination -->
                </div>
            </div>
            <!--end card-body-->
        </div>
        <!--end card-->
        <div id="elmLoader">
            <div class="spinner-border text-primary avatar-sm" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    </div>
    <!--end col-->
</div>
<!--end row-->

<ng-template #content role="document" let-modal>
    <div class="modal-header p-3 bg-info-subtle">
        <h5 class="modal-title" id="exampleModalLabel">Add Ticket</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <form (ngSubmit)="saveUser()" [formGroup]="ordersForm" class="tablelist-form" autocomplete="off">
        <div class="modal-body">
            <div class="row g-3">
                <div class="col-lg-12">
                    <div id="modal-id">
                        <label for="orderId" class="form-label">ID</label>
                        <input type="text" id="orderId" class="form-control" placeholder="ID" value="#VLZ462" readonly formControlName="id" />
                    </div>
                </div>
                <div class="col-lg-12">
                    <div>
                        <label for="tasksTitle-field" class="form-label">Title</label>
                        <input type="text" id="tasksTitle-field" class="form-control" placeholder="Title" required formControlName="title" [ngClass]="{ 'is-invalid': submitted && form['title'].errors }" />
                        @if(submitted && form['title'].errors){
                        <div class="invalid-feedback" align="left">
                            @if(form['title'].errors['required']){
                            <div>Title is required</div>
                            }
                        </div>
                        }
                    </div>
                </div>
                <div class="col-lg-6">
                    <div>
                        <label for="client_nameName-field" class="form-label">Client</label>
                        <input type="text" id="client_nameName-field" class="form-control" placeholder="Client Name" required formControlName="client" [ngClass]="{ 'is-invalid': submitted && form['client'].errors }" />
                        @if(submitted && form['client'].errors){
                        <div class="invalid-feedback" align="left">
                            @if(form['client'].errors['required']){
                            <div>Client is required</div>
                            }
                        </div>
                        }
                    </div>
                </div>
                <div class="col-lg-6">
                    <div>
                        <label for="assignedtoName-field" class="form-label">Assigned To</label>
                        <input type="text" id="assignedtoName-field" class="form-control" placeholder="Assigned to" required formControlName="assigned" [ngClass]="{ 'is-invalid': submitted && form['assigned'].errors }" />
                        @if(submitted && form['assigned'].errors){
                        <div class="invalid-feedback" align="left">
                            @if(form['assigned'].errors['required']){
                            <div>Assigned To is required</div>
                            }
                        </div>
                        }
                    </div>
                </div>
                <div class="col-lg-6">
                    <label for="date-field" class="form-label">Create Date</label>
                    <input class="form-control flatpickr-input" type="text" placeholder="Create Date" mwlFlatpickr [altInput]="true" [enableTime]="true" [convertModelValue]="true" [dateFormat]="'Y-m-d H:i'" formControlName="create" [ngClass]="{ 'is-invalid': submitted && form['create'].errors }">
                    @if(submitted && form['create'].errors){
                    <div class="invalid-feedback" align="left">
                        @if(form['create'].errors['required']){
                        <div>Create Date To is required</div>
                        }
                    </div>
                    }
                </div>
                <div class="col-lg-6">
                    <label for="duedate-field" class="form-label">Due Date</label>
                    <input class="form-control flatpickr-input" type="text" placeholder="Due Date" mwlFlatpickr [altInput]="true" [enableTime]="true" [convertModelValue]="true" [dateFormat]="'Y-m-d H:i'" formControlName="due" [ngClass]="{ 'is-invalid': submitted && form['due'].errors }">
                    @if(submitted && form['due'].errors){
                    <div class="invalid-feedback" align="left">
                        @if(form['due'].errors['required']){
                        <div>Due Date To is required</div>
                        }
                    </div>
                    }
                </div>
                <div class="col-lg-6">
                    <label for="ticket-status" class="form-label">Status</label>
                    <select class="form-control" data-plugin="choices" name="ticket-status" id="ticket-status" formControlName="status" [ngClass]="{ 'is-invalid': submitted && form['status'].errors }">
                        <option value="">Status</option>
                        <option value="New">New</option>
                        <option value="Inprogress">Inprogress</option>
                        <option value="Closed">Closed</option>
                        <option value="Open">Open</option>
                    </select>
                    @if(submitted && form['status'].errors){
                    <div class="invalid-feedback" align="left">
                        @if(form['status'].errors['required']){
                        <div>Status To is required</div>
                        }
                    </div>
                    }
                </div>
                <div class="col-lg-6">
                    <label for="priority-field" class="form-label">Priority</label>
                    <select class="form-control" data-plugin="choices" name="priority-field" id="priority-field" formControlName="priority" [ngClass]="{ 'is-invalid': submitted && form['priority'].errors }">
                        <option value="">Priority</option>
                        <option value="High">High</option>
                        <option value="Medium">Medium</option>
                        <option value="Low">Low</option>
                    </select>
                    @if(submitted && form['priority'].errors){
                    <div class="invalid-feedback" align="left">
                        @if(form['priority'].errors['required']){
                        <div>Priority To is required</div>
                        }
                    </div>
                    }
                </div>
            </div>

        </div>
        <div class="modal-footer">
            <div class="hstack gap-2 justify-content-end">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal" (click)="modal.close('Close click')">Close</button>
                <button type="submit" class="btn btn-success" id="add-btn">Add Ticket</button>
            </div>
        </div>
    </form>
</ng-template>

<!-- removeItemModal -->
<ng-template #deleteModel let-modal>
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="btn-close" (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body">
            <div class="mt-2 text-center">
                <lord-icon src="https://cdn.lordicon.com/gsqxdxog.json" trigger="loop" colors="primary:#405189,secondary:#f06548" style="width:90px;height:90px"></lord-icon>
                <div class="mt-4 pt-2 fs-15 mx-4 mx-sm-5">
                    <h4>You are about to delete a contact ?</h4>
                    <p class="text-muted mx-4 mb-0">Deleting your contact will remove all of your information from our
                        database.</p>
                </div>
            </div>
            <div class="d-flex gap-2 justify-content-center mt-4 mb-2">
                <button class="btn btn-link link-success fw-medium text-decoration-none" id="deleteRecord-close" data-bs-dismiss="modal" (click)="modal.close('Close click')"><i class="ri-close-line me-1 align-middle"></i> Close</button>
                <button type="button" class="btn w-sm btn-danger " id="delete-product" (click)="deleteData(deleteId)" (click)="modal.close('Close click')">Yes, Delete It!</button>
            </div>
        </div>
    </div><!-- /.modal-content -->
</ng-template>