{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapModel from './VisualMapModel.js';\nimport * as numberUtil from '../../util/number.js';\nimport { inheritDefaultOption } from '../../util/component.js';\n// Constant\nvar DEFAULT_BAR_BOUND = [20, 140];\nvar ContinuousModel = /** @class */function (_super) {\n  __extends(ContinuousModel, _super);\n  function ContinuousModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ContinuousModel.type;\n    return _this;\n  }\n  /**\n   * @override\n   */\n  ContinuousModel.prototype.optionUpdated = function (newOption, isInit) {\n    _super.prototype.optionUpdated.apply(this, arguments);\n    this.resetExtent();\n    this.resetVisual(function (mappingOption) {\n      mappingOption.mappingMethod = 'linear';\n      mappingOption.dataExtent = this.getExtent();\n    });\n    this._resetRange();\n  };\n  /**\n   * @protected\n   * @override\n   */\n  ContinuousModel.prototype.resetItemSize = function () {\n    _super.prototype.resetItemSize.apply(this, arguments);\n    var itemSize = this.itemSize;\n    (itemSize[0] == null || isNaN(itemSize[0])) && (itemSize[0] = DEFAULT_BAR_BOUND[0]);\n    (itemSize[1] == null || isNaN(itemSize[1])) && (itemSize[1] = DEFAULT_BAR_BOUND[1]);\n  };\n  /**\n   * @private\n   */\n  ContinuousModel.prototype._resetRange = function () {\n    var dataExtent = this.getExtent();\n    var range = this.option.range;\n    if (!range || range.auto) {\n      // `range` should always be array (so we don't use other\n      // value like 'auto') for user-friend. (consider getOption).\n      dataExtent.auto = 1;\n      this.option.range = dataExtent;\n    } else if (zrUtil.isArray(range)) {\n      if (range[0] > range[1]) {\n        range.reverse();\n      }\n      range[0] = Math.max(range[0], dataExtent[0]);\n      range[1] = Math.min(range[1], dataExtent[1]);\n    }\n  };\n  /**\n   * @protected\n   * @override\n   */\n  ContinuousModel.prototype.completeVisualOption = function () {\n    _super.prototype.completeVisualOption.apply(this, arguments);\n    zrUtil.each(this.stateList, function (state) {\n      var symbolSize = this.option.controller[state].symbolSize;\n      if (symbolSize && symbolSize[0] !== symbolSize[1]) {\n        symbolSize[0] = symbolSize[1] / 3; // For good looking.\n      }\n    }, this);\n  };\n  /**\n   * @override\n   */\n  ContinuousModel.prototype.setSelected = function (selected) {\n    this.option.range = selected.slice();\n    this._resetRange();\n  };\n  /**\n   * @public\n   */\n  ContinuousModel.prototype.getSelected = function () {\n    var dataExtent = this.getExtent();\n    var dataInterval = numberUtil.asc((this.get('range') || []).slice());\n    // Clamp\n    dataInterval[0] > dataExtent[1] && (dataInterval[0] = dataExtent[1]);\n    dataInterval[1] > dataExtent[1] && (dataInterval[1] = dataExtent[1]);\n    dataInterval[0] < dataExtent[0] && (dataInterval[0] = dataExtent[0]);\n    dataInterval[1] < dataExtent[0] && (dataInterval[1] = dataExtent[0]);\n    return dataInterval;\n  };\n  /**\n   * @override\n   */\n  ContinuousModel.prototype.getValueState = function (value) {\n    var range = this.option.range;\n    var dataExtent = this.getExtent();\n    // When range[0] === dataExtent[0], any value larger than dataExtent[0] maps to 'inRange'.\n    // range[1] is processed likewise.\n    return (range[0] <= dataExtent[0] || range[0] <= value) && (range[1] >= dataExtent[1] || value <= range[1]) ? 'inRange' : 'outOfRange';\n  };\n  ContinuousModel.prototype.findTargetDataIndices = function (range) {\n    var result = [];\n    this.eachTargetSeries(function (seriesModel) {\n      var dataIndices = [];\n      var data = seriesModel.getData();\n      data.each(this.getDataDimensionIndex(data), function (value, dataIndex) {\n        range[0] <= value && value <= range[1] && dataIndices.push(dataIndex);\n      }, this);\n      result.push({\n        seriesId: seriesModel.id,\n        dataIndex: dataIndices\n      });\n    }, this);\n    return result;\n  };\n  /**\n   * @implement\n   */\n  ContinuousModel.prototype.getVisualMeta = function (getColorVisual) {\n    var oVals = getColorStopValues(this, 'outOfRange', this.getExtent());\n    var iVals = getColorStopValues(this, 'inRange', this.option.range.slice());\n    var stops = [];\n    function setStop(value, valueState) {\n      stops.push({\n        value: value,\n        color: getColorVisual(value, valueState)\n      });\n    }\n    // Format to: outOfRange -- inRange -- outOfRange.\n    var iIdx = 0;\n    var oIdx = 0;\n    var iLen = iVals.length;\n    var oLen = oVals.length;\n    for (; oIdx < oLen && (!iVals.length || oVals[oIdx] <= iVals[0]); oIdx++) {\n      // If oVal[oIdx] === iVals[iIdx], oVal[oIdx] should be ignored.\n      if (oVals[oIdx] < iVals[iIdx]) {\n        setStop(oVals[oIdx], 'outOfRange');\n      }\n    }\n    for (var first = 1; iIdx < iLen; iIdx++, first = 0) {\n      // If range is full, value beyond min, max will be clamped.\n      // make a singularity\n      first && stops.length && setStop(iVals[iIdx], 'outOfRange');\n      setStop(iVals[iIdx], 'inRange');\n    }\n    for (var first = 1; oIdx < oLen; oIdx++) {\n      if (!iVals.length || iVals[iVals.length - 1] < oVals[oIdx]) {\n        // make a singularity\n        if (first) {\n          stops.length && setStop(stops[stops.length - 1].value, 'outOfRange');\n          first = 0;\n        }\n        setStop(oVals[oIdx], 'outOfRange');\n      }\n    }\n    var stopsLen = stops.length;\n    return {\n      stops: stops,\n      outerColors: [stopsLen ? stops[0].color : 'transparent', stopsLen ? stops[stopsLen - 1].color : 'transparent']\n    };\n  };\n  ContinuousModel.type = 'visualMap.continuous';\n  ContinuousModel.defaultOption = inheritDefaultOption(VisualMapModel.defaultOption, {\n    align: 'auto',\n    calculable: false,\n    hoverLink: true,\n    realtime: true,\n    handleIcon: 'path://M-11.39,9.77h0a3.5,3.5,0,0,1-3.5,3.5h-22a3.5,3.5,0,0,1-3.5-3.5h0a3.5,3.5,0,0,1,3.5-3.5h22A3.5,3.5,0,0,1-11.39,9.77Z',\n    handleSize: '120%',\n    handleStyle: {\n      borderColor: '#fff',\n      borderWidth: 1\n    },\n    indicatorIcon: 'circle',\n    indicatorSize: '50%',\n    indicatorStyle: {\n      borderColor: '#fff',\n      borderWidth: 2,\n      shadowBlur: 2,\n      shadowOffsetX: 1,\n      shadowOffsetY: 1,\n      shadowColor: 'rgba(0,0,0,0.2)'\n    }\n    // emphasis: {\n    //     handleStyle: {\n    //         shadowBlur: 3,\n    //         shadowOffsetX: 1,\n    //         shadowOffsetY: 1,\n    //         shadowColor: 'rgba(0,0,0,0.2)'\n    //     }\n    // }\n  });\n  return ContinuousModel;\n}(VisualMapModel);\nfunction getColorStopValues(visualMapModel, valueState, dataExtent) {\n  if (dataExtent[0] === dataExtent[1]) {\n    return dataExtent.slice();\n  }\n  // When using colorHue mapping, it is not linear color any more.\n  // Moreover, canvas gradient seems not to be accurate linear.\n  // FIXME\n  // Should be arbitrary value 100? or based on pixel size?\n  var count = 200;\n  var step = (dataExtent[1] - dataExtent[0]) / count;\n  var value = dataExtent[0];\n  var stopValues = [];\n  for (var i = 0; i <= count && value < dataExtent[1]; i++) {\n    stopValues.push(value);\n    value += step;\n  }\n  stopValues.push(dataExtent[1]);\n  return stopValues;\n}\nexport default ContinuousModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "VisualMapModel", "numberUtil", "inheritDefaultOption", "DEFAULT_BAR_BOUND", "ContinuousModel", "_super", "_this", "apply", "arguments", "type", "prototype", "optionUpdated", "newOption", "isInit", "resetExtent", "resetVisual", "mappingOption", "mappingMethod", "dataExtent", "getExtent", "_resetRange", "resetItemSize", "itemSize", "isNaN", "range", "option", "auto", "isArray", "reverse", "Math", "max", "min", "completeVisualOption", "each", "stateList", "state", "symbolSize", "controller", "setSelected", "selected", "slice", "getSelected", "dataInterval", "asc", "get", "getValueState", "value", "findTargetDataIndices", "result", "eachTargetSeries", "seriesModel", "dataIndices", "data", "getData", "getDataDimensionIndex", "dataIndex", "push", "seriesId", "id", "getVisualMeta", "getColorVisual", "oVals", "getColorStopValues", "iVals", "stops", "setStop", "valueState", "color", "iIdx", "oIdx", "iLen", "length", "oLen", "first", "stopsLen", "outerColors", "defaultOption", "align", "calculable", "hoverLink", "realtime", "handleIcon", "handleSize", "handleStyle", "borderColor", "borderWidth", "indicatorIcon", "indicatorSize", "indicatorStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowOffsetY", "shadowColor", "visualMapModel", "count", "step", "stopValues", "i"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/component/visualMap/ContinuousModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapModel from './VisualMapModel.js';\nimport * as numberUtil from '../../util/number.js';\nimport { inheritDefaultOption } from '../../util/component.js';\n// Constant\nvar DEFAULT_BAR_BOUND = [20, 140];\nvar ContinuousModel = /** @class */function (_super) {\n  __extends(ContinuousModel, _super);\n  function ContinuousModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ContinuousModel.type;\n    return _this;\n  }\n  /**\n   * @override\n   */\n  ContinuousModel.prototype.optionUpdated = function (newOption, isInit) {\n    _super.prototype.optionUpdated.apply(this, arguments);\n    this.resetExtent();\n    this.resetVisual(function (mappingOption) {\n      mappingOption.mappingMethod = 'linear';\n      mappingOption.dataExtent = this.getExtent();\n    });\n    this._resetRange();\n  };\n  /**\n   * @protected\n   * @override\n   */\n  ContinuousModel.prototype.resetItemSize = function () {\n    _super.prototype.resetItemSize.apply(this, arguments);\n    var itemSize = this.itemSize;\n    (itemSize[0] == null || isNaN(itemSize[0])) && (itemSize[0] = DEFAULT_BAR_BOUND[0]);\n    (itemSize[1] == null || isNaN(itemSize[1])) && (itemSize[1] = DEFAULT_BAR_BOUND[1]);\n  };\n  /**\n   * @private\n   */\n  ContinuousModel.prototype._resetRange = function () {\n    var dataExtent = this.getExtent();\n    var range = this.option.range;\n    if (!range || range.auto) {\n      // `range` should always be array (so we don't use other\n      // value like 'auto') for user-friend. (consider getOption).\n      dataExtent.auto = 1;\n      this.option.range = dataExtent;\n    } else if (zrUtil.isArray(range)) {\n      if (range[0] > range[1]) {\n        range.reverse();\n      }\n      range[0] = Math.max(range[0], dataExtent[0]);\n      range[1] = Math.min(range[1], dataExtent[1]);\n    }\n  };\n  /**\n   * @protected\n   * @override\n   */\n  ContinuousModel.prototype.completeVisualOption = function () {\n    _super.prototype.completeVisualOption.apply(this, arguments);\n    zrUtil.each(this.stateList, function (state) {\n      var symbolSize = this.option.controller[state].symbolSize;\n      if (symbolSize && symbolSize[0] !== symbolSize[1]) {\n        symbolSize[0] = symbolSize[1] / 3; // For good looking.\n      }\n    }, this);\n  };\n  /**\n   * @override\n   */\n  ContinuousModel.prototype.setSelected = function (selected) {\n    this.option.range = selected.slice();\n    this._resetRange();\n  };\n  /**\n   * @public\n   */\n  ContinuousModel.prototype.getSelected = function () {\n    var dataExtent = this.getExtent();\n    var dataInterval = numberUtil.asc((this.get('range') || []).slice());\n    // Clamp\n    dataInterval[0] > dataExtent[1] && (dataInterval[0] = dataExtent[1]);\n    dataInterval[1] > dataExtent[1] && (dataInterval[1] = dataExtent[1]);\n    dataInterval[0] < dataExtent[0] && (dataInterval[0] = dataExtent[0]);\n    dataInterval[1] < dataExtent[0] && (dataInterval[1] = dataExtent[0]);\n    return dataInterval;\n  };\n  /**\n   * @override\n   */\n  ContinuousModel.prototype.getValueState = function (value) {\n    var range = this.option.range;\n    var dataExtent = this.getExtent();\n    // When range[0] === dataExtent[0], any value larger than dataExtent[0] maps to 'inRange'.\n    // range[1] is processed likewise.\n    return (range[0] <= dataExtent[0] || range[0] <= value) && (range[1] >= dataExtent[1] || value <= range[1]) ? 'inRange' : 'outOfRange';\n  };\n  ContinuousModel.prototype.findTargetDataIndices = function (range) {\n    var result = [];\n    this.eachTargetSeries(function (seriesModel) {\n      var dataIndices = [];\n      var data = seriesModel.getData();\n      data.each(this.getDataDimensionIndex(data), function (value, dataIndex) {\n        range[0] <= value && value <= range[1] && dataIndices.push(dataIndex);\n      }, this);\n      result.push({\n        seriesId: seriesModel.id,\n        dataIndex: dataIndices\n      });\n    }, this);\n    return result;\n  };\n  /**\n   * @implement\n   */\n  ContinuousModel.prototype.getVisualMeta = function (getColorVisual) {\n    var oVals = getColorStopValues(this, 'outOfRange', this.getExtent());\n    var iVals = getColorStopValues(this, 'inRange', this.option.range.slice());\n    var stops = [];\n    function setStop(value, valueState) {\n      stops.push({\n        value: value,\n        color: getColorVisual(value, valueState)\n      });\n    }\n    // Format to: outOfRange -- inRange -- outOfRange.\n    var iIdx = 0;\n    var oIdx = 0;\n    var iLen = iVals.length;\n    var oLen = oVals.length;\n    for (; oIdx < oLen && (!iVals.length || oVals[oIdx] <= iVals[0]); oIdx++) {\n      // If oVal[oIdx] === iVals[iIdx], oVal[oIdx] should be ignored.\n      if (oVals[oIdx] < iVals[iIdx]) {\n        setStop(oVals[oIdx], 'outOfRange');\n      }\n    }\n    for (var first = 1; iIdx < iLen; iIdx++, first = 0) {\n      // If range is full, value beyond min, max will be clamped.\n      // make a singularity\n      first && stops.length && setStop(iVals[iIdx], 'outOfRange');\n      setStop(iVals[iIdx], 'inRange');\n    }\n    for (var first = 1; oIdx < oLen; oIdx++) {\n      if (!iVals.length || iVals[iVals.length - 1] < oVals[oIdx]) {\n        // make a singularity\n        if (first) {\n          stops.length && setStop(stops[stops.length - 1].value, 'outOfRange');\n          first = 0;\n        }\n        setStop(oVals[oIdx], 'outOfRange');\n      }\n    }\n    var stopsLen = stops.length;\n    return {\n      stops: stops,\n      outerColors: [stopsLen ? stops[0].color : 'transparent', stopsLen ? stops[stopsLen - 1].color : 'transparent']\n    };\n  };\n  ContinuousModel.type = 'visualMap.continuous';\n  ContinuousModel.defaultOption = inheritDefaultOption(VisualMapModel.defaultOption, {\n    align: 'auto',\n    calculable: false,\n    hoverLink: true,\n    realtime: true,\n    handleIcon: 'path://M-11.39,9.77h0a3.5,3.5,0,0,1-3.5,3.5h-22a3.5,3.5,0,0,1-3.5-3.5h0a3.5,3.5,0,0,1,3.5-3.5h22A3.5,3.5,0,0,1-11.39,9.77Z',\n    handleSize: '120%',\n    handleStyle: {\n      borderColor: '#fff',\n      borderWidth: 1\n    },\n    indicatorIcon: 'circle',\n    indicatorSize: '50%',\n    indicatorStyle: {\n      borderColor: '#fff',\n      borderWidth: 2,\n      shadowBlur: 2,\n      shadowOffsetX: 1,\n      shadowOffsetY: 1,\n      shadowColor: 'rgba(0,0,0,0.2)'\n    }\n    // emphasis: {\n    //     handleStyle: {\n    //         shadowBlur: 3,\n    //         shadowOffsetX: 1,\n    //         shadowOffsetY: 1,\n    //         shadowColor: 'rgba(0,0,0,0.2)'\n    //     }\n    // }\n  });\n\n  return ContinuousModel;\n}(VisualMapModel);\nfunction getColorStopValues(visualMapModel, valueState, dataExtent) {\n  if (dataExtent[0] === dataExtent[1]) {\n    return dataExtent.slice();\n  }\n  // When using colorHue mapping, it is not linear color any more.\n  // Moreover, canvas gradient seems not to be accurate linear.\n  // FIXME\n  // Should be arbitrary value 100? or based on pixel size?\n  var count = 200;\n  var step = (dataExtent[1] - dataExtent[0]) / count;\n  var value = dataExtent[0];\n  var stopValues = [];\n  for (var i = 0; i <= count && value < dataExtent[1]; i++) {\n    stopValues.push(value);\n    value += step;\n  }\n  stopValues.push(dataExtent[1]);\n  return stopValues;\n}\nexport default ContinuousModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D;AACA,IAAIC,iBAAiB,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;AACjC,IAAIC,eAAe,GAAG,aAAa,UAAUC,MAAM,EAAE;EACnDP,SAAS,CAACM,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAAA,EAAG;IACzB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,eAAe,CAACK,IAAI;IACjC,OAAOH,KAAK;EACd;EACA;AACF;AACA;EACEF,eAAe,CAACM,SAAS,CAACC,aAAa,GAAG,UAAUC,SAAS,EAAEC,MAAM,EAAE;IACrER,MAAM,CAACK,SAAS,CAACC,aAAa,CAACJ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACrD,IAAI,CAACM,WAAW,CAAC,CAAC;IAClB,IAAI,CAACC,WAAW,CAAC,UAAUC,aAAa,EAAE;MACxCA,aAAa,CAACC,aAAa,GAAG,QAAQ;MACtCD,aAAa,CAACE,UAAU,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC7C,CAAC,CAAC;IACF,IAAI,CAACC,WAAW,CAAC,CAAC;EACpB,CAAC;EACD;AACF;AACA;AACA;EACEhB,eAAe,CAACM,SAAS,CAACW,aAAa,GAAG,YAAY;IACpDhB,MAAM,CAACK,SAAS,CAACW,aAAa,CAACd,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACrD,IAAIc,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,CAACA,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIC,KAAK,CAACD,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAMA,QAAQ,CAAC,CAAC,CAAC,GAAGnB,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACnF,CAACmB,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIC,KAAK,CAACD,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAMA,QAAQ,CAAC,CAAC,CAAC,GAAGnB,iBAAiB,CAAC,CAAC,CAAC,CAAC;EACrF,CAAC;EACD;AACF;AACA;EACEC,eAAe,CAACM,SAAS,CAACU,WAAW,GAAG,YAAY;IAClD,IAAIF,UAAU,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IACjC,IAAIK,KAAK,GAAG,IAAI,CAACC,MAAM,CAACD,KAAK;IAC7B,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACE,IAAI,EAAE;MACxB;MACA;MACAR,UAAU,CAACQ,IAAI,GAAG,CAAC;MACnB,IAAI,CAACD,MAAM,CAACD,KAAK,GAAGN,UAAU;IAChC,CAAC,MAAM,IAAInB,MAAM,CAAC4B,OAAO,CAACH,KAAK,CAAC,EAAE;MAChC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,EAAE;QACvBA,KAAK,CAACI,OAAO,CAAC,CAAC;MACjB;MACAJ,KAAK,CAAC,CAAC,CAAC,GAAGK,IAAI,CAACC,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,EAAEN,UAAU,CAAC,CAAC,CAAC,CAAC;MAC5CM,KAAK,CAAC,CAAC,CAAC,GAAGK,IAAI,CAACE,GAAG,CAACP,KAAK,CAAC,CAAC,CAAC,EAAEN,UAAU,CAAC,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;EACD;AACF;AACA;AACA;EACEd,eAAe,CAACM,SAAS,CAACsB,oBAAoB,GAAG,YAAY;IAC3D3B,MAAM,CAACK,SAAS,CAACsB,oBAAoB,CAACzB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC5DT,MAAM,CAACkC,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE,UAAUC,KAAK,EAAE;MAC3C,IAAIC,UAAU,GAAG,IAAI,CAACX,MAAM,CAACY,UAAU,CAACF,KAAK,CAAC,CAACC,UAAU;MACzD,IAAIA,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,KAAKA,UAAU,CAAC,CAAC,CAAC,EAAE;QACjDA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;MACrC;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD;AACF;AACA;EACEhC,eAAe,CAACM,SAAS,CAAC4B,WAAW,GAAG,UAAUC,QAAQ,EAAE;IAC1D,IAAI,CAACd,MAAM,CAACD,KAAK,GAAGe,QAAQ,CAACC,KAAK,CAAC,CAAC;IACpC,IAAI,CAACpB,WAAW,CAAC,CAAC;EACpB,CAAC;EACD;AACF;AACA;EACEhB,eAAe,CAACM,SAAS,CAAC+B,WAAW,GAAG,YAAY;IAClD,IAAIvB,UAAU,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IACjC,IAAIuB,YAAY,GAAGzC,UAAU,CAAC0C,GAAG,CAAC,CAAC,IAAI,CAACC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAEJ,KAAK,CAAC,CAAC,CAAC;IACpE;IACAE,YAAY,CAAC,CAAC,CAAC,GAAGxB,UAAU,CAAC,CAAC,CAAC,KAAKwB,YAAY,CAAC,CAAC,CAAC,GAAGxB,UAAU,CAAC,CAAC,CAAC,CAAC;IACpEwB,YAAY,CAAC,CAAC,CAAC,GAAGxB,UAAU,CAAC,CAAC,CAAC,KAAKwB,YAAY,CAAC,CAAC,CAAC,GAAGxB,UAAU,CAAC,CAAC,CAAC,CAAC;IACpEwB,YAAY,CAAC,CAAC,CAAC,GAAGxB,UAAU,CAAC,CAAC,CAAC,KAAKwB,YAAY,CAAC,CAAC,CAAC,GAAGxB,UAAU,CAAC,CAAC,CAAC,CAAC;IACpEwB,YAAY,CAAC,CAAC,CAAC,GAAGxB,UAAU,CAAC,CAAC,CAAC,KAAKwB,YAAY,CAAC,CAAC,CAAC,GAAGxB,UAAU,CAAC,CAAC,CAAC,CAAC;IACpE,OAAOwB,YAAY;EACrB,CAAC;EACD;AACF;AACA;EACEtC,eAAe,CAACM,SAAS,CAACmC,aAAa,GAAG,UAAUC,KAAK,EAAE;IACzD,IAAItB,KAAK,GAAG,IAAI,CAACC,MAAM,CAACD,KAAK;IAC7B,IAAIN,UAAU,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IACjC;IACA;IACA,OAAO,CAACK,KAAK,CAAC,CAAC,CAAC,IAAIN,UAAU,CAAC,CAAC,CAAC,IAAIM,KAAK,CAAC,CAAC,CAAC,IAAIsB,KAAK,MAAMtB,KAAK,CAAC,CAAC,CAAC,IAAIN,UAAU,CAAC,CAAC,CAAC,IAAI4B,KAAK,IAAItB,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,YAAY;EACxI,CAAC;EACDpB,eAAe,CAACM,SAAS,CAACqC,qBAAqB,GAAG,UAAUvB,KAAK,EAAE;IACjE,IAAIwB,MAAM,GAAG,EAAE;IACf,IAAI,CAACC,gBAAgB,CAAC,UAAUC,WAAW,EAAE;MAC3C,IAAIC,WAAW,GAAG,EAAE;MACpB,IAAIC,IAAI,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC;MAChCD,IAAI,CAACnB,IAAI,CAAC,IAAI,CAACqB,qBAAqB,CAACF,IAAI,CAAC,EAAE,UAAUN,KAAK,EAAES,SAAS,EAAE;QACtE/B,KAAK,CAAC,CAAC,CAAC,IAAIsB,KAAK,IAAIA,KAAK,IAAItB,KAAK,CAAC,CAAC,CAAC,IAAI2B,WAAW,CAACK,IAAI,CAACD,SAAS,CAAC;MACvE,CAAC,EAAE,IAAI,CAAC;MACRP,MAAM,CAACQ,IAAI,CAAC;QACVC,QAAQ,EAAEP,WAAW,CAACQ,EAAE;QACxBH,SAAS,EAAEJ;MACb,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACR,OAAOH,MAAM;EACf,CAAC;EACD;AACF;AACA;EACE5C,eAAe,CAACM,SAAS,CAACiD,aAAa,GAAG,UAAUC,cAAc,EAAE;IAClE,IAAIC,KAAK,GAAGC,kBAAkB,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC3C,SAAS,CAAC,CAAC,CAAC;IACpE,IAAI4C,KAAK,GAAGD,kBAAkB,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAACrC,MAAM,CAACD,KAAK,CAACgB,KAAK,CAAC,CAAC,CAAC;IAC1E,IAAIwB,KAAK,GAAG,EAAE;IACd,SAASC,OAAOA,CAACnB,KAAK,EAAEoB,UAAU,EAAE;MAClCF,KAAK,CAACR,IAAI,CAAC;QACTV,KAAK,EAAEA,KAAK;QACZqB,KAAK,EAAEP,cAAc,CAACd,KAAK,EAAEoB,UAAU;MACzC,CAAC,CAAC;IACJ;IACA;IACA,IAAIE,IAAI,GAAG,CAAC;IACZ,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAIC,IAAI,GAAGP,KAAK,CAACQ,MAAM;IACvB,IAAIC,IAAI,GAAGX,KAAK,CAACU,MAAM;IACvB,OAAOF,IAAI,GAAGG,IAAI,KAAK,CAACT,KAAK,CAACQ,MAAM,IAAIV,KAAK,CAACQ,IAAI,CAAC,IAAIN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEM,IAAI,EAAE,EAAE;MACxE;MACA,IAAIR,KAAK,CAACQ,IAAI,CAAC,GAAGN,KAAK,CAACK,IAAI,CAAC,EAAE;QAC7BH,OAAO,CAACJ,KAAK,CAACQ,IAAI,CAAC,EAAE,YAAY,CAAC;MACpC;IACF;IACA,KAAK,IAAII,KAAK,GAAG,CAAC,EAAEL,IAAI,GAAGE,IAAI,EAAEF,IAAI,EAAE,EAAEK,KAAK,GAAG,CAAC,EAAE;MAClD;MACA;MACAA,KAAK,IAAIT,KAAK,CAACO,MAAM,IAAIN,OAAO,CAACF,KAAK,CAACK,IAAI,CAAC,EAAE,YAAY,CAAC;MAC3DH,OAAO,CAACF,KAAK,CAACK,IAAI,CAAC,EAAE,SAAS,CAAC;IACjC;IACA,KAAK,IAAIK,KAAK,GAAG,CAAC,EAAEJ,IAAI,GAAGG,IAAI,EAAEH,IAAI,EAAE,EAAE;MACvC,IAAI,CAACN,KAAK,CAACQ,MAAM,IAAIR,KAAK,CAACA,KAAK,CAACQ,MAAM,GAAG,CAAC,CAAC,GAAGV,KAAK,CAACQ,IAAI,CAAC,EAAE;QAC1D;QACA,IAAII,KAAK,EAAE;UACTT,KAAK,CAACO,MAAM,IAAIN,OAAO,CAACD,KAAK,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC,CAACzB,KAAK,EAAE,YAAY,CAAC;UACpE2B,KAAK,GAAG,CAAC;QACX;QACAR,OAAO,CAACJ,KAAK,CAACQ,IAAI,CAAC,EAAE,YAAY,CAAC;MACpC;IACF;IACA,IAAIK,QAAQ,GAAGV,KAAK,CAACO,MAAM;IAC3B,OAAO;MACLP,KAAK,EAAEA,KAAK;MACZW,WAAW,EAAE,CAACD,QAAQ,GAAGV,KAAK,CAAC,CAAC,CAAC,CAACG,KAAK,GAAG,aAAa,EAAEO,QAAQ,GAAGV,KAAK,CAACU,QAAQ,GAAG,CAAC,CAAC,CAACP,KAAK,GAAG,aAAa;IAC/G,CAAC;EACH,CAAC;EACD/D,eAAe,CAACK,IAAI,GAAG,sBAAsB;EAC7CL,eAAe,CAACwE,aAAa,GAAG1E,oBAAoB,CAACF,cAAc,CAAC4E,aAAa,EAAE;IACjFC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,4HAA4H;IACxIC,UAAU,EAAE,MAAM;IAClBC,WAAW,EAAE;MACXC,WAAW,EAAE,MAAM;MACnBC,WAAW,EAAE;IACf,CAAC;IACDC,aAAa,EAAE,QAAQ;IACvBC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE;MACdJ,WAAW,EAAE,MAAM;MACnBC,WAAW,EAAE,CAAC;MACdI,UAAU,EAAE,CAAC;MACbC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChBC,WAAW,EAAE;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,CAAC,CAAC;EAEF,OAAOxF,eAAe;AACxB,CAAC,CAACJ,cAAc,CAAC;AACjB,SAAS8D,kBAAkBA,CAAC+B,cAAc,EAAE3B,UAAU,EAAEhD,UAAU,EAAE;EAClE,IAAIA,UAAU,CAAC,CAAC,CAAC,KAAKA,UAAU,CAAC,CAAC,CAAC,EAAE;IACnC,OAAOA,UAAU,CAACsB,KAAK,CAAC,CAAC;EAC3B;EACA;EACA;EACA;EACA;EACA,IAAIsD,KAAK,GAAG,GAAG;EACf,IAAIC,IAAI,GAAG,CAAC7E,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,IAAI4E,KAAK;EAClD,IAAIhD,KAAK,GAAG5B,UAAU,CAAC,CAAC,CAAC;EACzB,IAAI8E,UAAU,GAAG,EAAE;EACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIH,KAAK,IAAIhD,KAAK,GAAG5B,UAAU,CAAC,CAAC,CAAC,EAAE+E,CAAC,EAAE,EAAE;IACxDD,UAAU,CAACxC,IAAI,CAACV,KAAK,CAAC;IACtBA,KAAK,IAAIiD,IAAI;EACf;EACAC,UAAU,CAACxC,IAAI,CAACtC,UAAU,CAAC,CAAC,CAAC,CAAC;EAC9B,OAAO8E,UAAU;AACnB;AACA,eAAe5F,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}