{"ast": null, "code": "//! moment.js locale configuration\n//! locale : <PERSON><PERSON> [mi]\n//! author : <PERSON> <<EMAIL>> : https://github.com/johnideal\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var mi = moment.defineLocale('mi', {\n    months: 'Kohi-tāte_Hui-tanguru_Poutū-te-rangi_Paenga-whāwhā_Haratua_Pipiri_Hōngoingoi_Here-turi-kōkā_Mahuru_Whiringa-ā-nuku_Whiringa-ā-rangi_Hakihea'.split('_'),\n    monthsShort: '<PERSON><PERSON>_Hui_Pou_Pae_Hara_Pipi_Hōngoi_Here_Mahu_Whi-nu_Whi-ra_Haki'.split('_'),\n    monthsRegex: /(?:['a-z\\u0101\\u014D\\u016B]+\\-?){1,3}/i,\n    monthsStrictRegex: /(?:['a-z\\u0101\\u014D\\u016B]+\\-?){1,3}/i,\n    monthsShortRegex: /(?:['a-z\\u0101\\u014D\\u016B]+\\-?){1,3}/i,\n    monthsShortStrictRegex: /(?:['a-z\\u0101\\u014D\\u016B]+\\-?){1,2}/i,\n    weekdays: 'Rātapu_Mane_Tūrei_Wenerei_Tāite_Paraire_Hātarei'.split('_'),\n    weekdaysShort: 'Ta_Ma_Tū_We_Tāi_Pa_Hā'.split('_'),\n    weekdaysMin: 'Ta_Ma_Tū_We_Tāi_Pa_Hā'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY [i] HH:mm',\n      LLLL: 'dddd, D MMMM YYYY [i] HH:mm'\n    },\n    calendar: {\n      sameDay: '[i teie mahana, i] LT',\n      nextDay: '[apopo i] LT',\n      nextWeek: 'dddd [i] LT',\n      lastDay: '[inanahi i] LT',\n      lastWeek: 'dddd [whakamutunga i] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'i roto i %s',\n      past: '%s i mua',\n      s: 'te hēkona ruarua',\n      ss: '%d hēkona',\n      m: 'he meneti',\n      mm: '%d meneti',\n      h: 'te haora',\n      hh: '%d haora',\n      d: 'he ra',\n      dd: '%d ra',\n      M: 'he marama',\n      MM: '%d marama',\n      y: 'he tau',\n      yy: '%d tau'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}º/,\n    ordinal: '%dº',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return mi;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "mi", "defineLocale", "months", "split", "monthsShort", "monthsRegex", "monthsStrictRegex", "monthsShortRegex", "monthsShortStrictRegex", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/moment/locale/mi.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : <PERSON><PERSON> [mi]\n//! author : <PERSON> <<EMAIL>> : https://github.com/johnideal\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var mi = moment.defineLocale('mi', {\n        months: 'Kohi-tāte_Hui-tanguru_Poutū-te-rangi_Paenga-whāwhā_Haratua_<PERSON>ri_H<PERSON>ngoingoi_Here-turi-kōkā_Mahuru_Whiringa-ā-nuku_Whiringa-ā-rangi_Hakihea'.split(\n            '_'\n        ),\n        monthsShort:\n            'Ko<PERSON>_Hui_Pou_Pae_Hara_Pipi_Hōngoi_Here_Mahu_Whi-nu_Whi-ra_Haki'.split(\n                '_'\n            ),\n        monthsRegex: /(?:['a-z\\u0101\\u014D\\u016B]+\\-?){1,3}/i,\n        monthsStrictRegex: /(?:['a-z\\u0101\\u014D\\u016B]+\\-?){1,3}/i,\n        monthsShortRegex: /(?:['a-z\\u0101\\u014D\\u016B]+\\-?){1,3}/i,\n        monthsShortStrictRegex: /(?:['a-z\\u0101\\u014D\\u016B]+\\-?){1,2}/i,\n        weekdays: 'Rātapu_Mane_Tūrei_Wenerei_Tāite_Paraire_Hātarei'.split('_'),\n        weekdaysShort: 'Ta_Ma_Tū_We_Tāi_Pa_Hā'.split('_'),\n        weekdaysMin: 'Ta_Ma_Tū_We_Tāi_Pa_Hā'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY [i] HH:mm',\n            LLLL: 'dddd, D MMMM YYYY [i] HH:mm',\n        },\n        calendar: {\n            sameDay: '[i teie mahana, i] LT',\n            nextDay: '[apopo i] LT',\n            nextWeek: 'dddd [i] LT',\n            lastDay: '[inanahi i] LT',\n            lastWeek: 'dddd [whakamutunga i] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'i roto i %s',\n            past: '%s i mua',\n            s: 'te hēkona ruarua',\n            ss: '%d hēkona',\n            m: 'he meneti',\n            mm: '%d meneti',\n            h: 'te haora',\n            hh: '%d haora',\n            d: 'he ra',\n            dd: '%d ra',\n            M: 'he marama',\n            MM: '%d marama',\n            y: 'he tau',\n            yy: '%d tau',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}º/,\n        ordinal: '%dº',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return mi;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,6IAA6I,CAACC,KAAK,CACvJ,GACJ,CAAC;IACDC,WAAW,EACP,gEAAgE,CAACD,KAAK,CAClE,GACJ,CAAC;IACLE,WAAW,EAAE,wCAAwC;IACrDC,iBAAiB,EAAE,wCAAwC;IAC3DC,gBAAgB,EAAE,wCAAwC;IAC1DC,sBAAsB,EAAE,wCAAwC;IAChEC,QAAQ,EAAE,iDAAiD,CAACN,KAAK,CAAC,GAAG,CAAC;IACtEO,aAAa,EAAE,uBAAuB,CAACP,KAAK,CAAC,GAAG,CAAC;IACjDQ,WAAW,EAAE,uBAAuB,CAACR,KAAK,CAAC,GAAG,CAAC;IAC/CS,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,uBAAuB;MAC5BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,uBAAuB;MAChCC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,aAAa;MACvBC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,0BAA0B;MACpCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,aAAa;MACrBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,kBAAkB;MACrBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,UAAU;IAClCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO7C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}