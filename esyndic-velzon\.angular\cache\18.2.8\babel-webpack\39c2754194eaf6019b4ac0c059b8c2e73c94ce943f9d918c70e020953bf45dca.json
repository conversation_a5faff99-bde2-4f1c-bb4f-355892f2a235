{"ast": null, "code": "import get from './get.js';\n\n/**\n * The base implementation of `_.at` without support for individual paths.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {string[]} paths The property paths to pick.\n * @returns {Array} Returns the picked elements.\n */\nfunction baseAt(object, paths) {\n  var index = -1,\n    length = paths.length,\n    result = Array(length),\n    skip = object == null;\n  while (++index < length) {\n    result[index] = skip ? undefined : get(object, paths[index]);\n  }\n  return result;\n}\nexport default baseAt;", "map": {"version": 3, "names": ["get", "baseAt", "object", "paths", "index", "length", "result", "Array", "skip", "undefined"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_baseAt.js"], "sourcesContent": ["import get from './get.js';\n\n/**\n * The base implementation of `_.at` without support for individual paths.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {string[]} paths The property paths to pick.\n * @returns {Array} Returns the picked elements.\n */\nfunction baseAt(object, paths) {\n  var index = -1,\n      length = paths.length,\n      result = Array(length),\n      skip = object == null;\n\n  while (++index < length) {\n    result[index] = skip ? undefined : get(object, paths[index]);\n  }\n  return result;\n}\n\nexport default baseAt;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC7B,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACrBC,MAAM,GAAGC,KAAK,CAACF,MAAM,CAAC;IACtBG,IAAI,GAAGN,MAAM,IAAI,IAAI;EAEzB,OAAO,EAAEE,KAAK,GAAGC,MAAM,EAAE;IACvBC,MAAM,CAACF,KAAK,CAAC,GAAGI,IAAI,GAAGC,SAAS,GAAGT,GAAG,CAACE,MAAM,EAAEC,KAAK,CAACC,KAAK,CAAC,CAAC;EAC9D;EACA,OAAOE,MAAM;AACf;AAEA,eAAeL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}