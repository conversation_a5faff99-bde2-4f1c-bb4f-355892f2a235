package com.esyndic.repository;

import com.esyndic.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserRepository extends JpaRepository<User, UUID> {

    Optional<User> findByKeycloakId(String keycloakId);

    Optional<User> findByUsername(String username);

    Optional<User> findByEmail(String email);

    List<User> findByIsActiveTrue();

    List<User> findByIsActiveFalse();

    @Query("SELECT u FROM User u WHERE u.firstName LIKE %:name% OR u.lastName LIKE %:name% OR u.username LIKE %:name%")
    List<User> findByNameContaining(@Param("name") String name);

    @Query("SELECT u FROM User u WHERE u.email LIKE %:email%")
    List<User> findByEmailContaining(@Param("email") String email);

    boolean existsByUsername(String username);

    boolean existsByEmail(String email);

    boolean existsByKeycloakId(String keycloakId);

    @Query("SELECT COUNT(u) FROM User u WHERE u.isActive = true")
    long countActiveUsers();

    @Query("SELECT u FROM User u JOIN u.ownedApartments a WHERE a.building.id = :buildingId")
    List<User> findOwnersByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT u FROM User u JOIN u.residedApartments a WHERE a.building.id = :buildingId")
    List<User> findResidentsByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT DISTINCT u FROM User u LEFT JOIN u.ownedApartments oa LEFT JOIN u.residedApartments ra " +
           "WHERE oa.building.id = :buildingId OR ra.building.id = :buildingId")
    List<User> findUsersByBuildingId(@Param("buildingId") UUID buildingId);
}
