{"ast": null, "code": "import { createFeatureSelector, createSelector } from '@ngrx/store';\nexport const selectDataState = createFeatureSelector('Jobs');\nexport const selectJobsData = createSelector(selectDataState, state => state.Application);\nexport const selectJobsLoading = createSelector(selectDataState, state => state.loading);\nexport const selectJobsError = createSelector(selectDataState, state => state.error);", "map": {"version": 3, "names": ["createFeatureSelector", "createSelector", "selectDataState", "selectJobsData", "state", "Application", "selectJobsLoading", "loading", "selectJobsError", "error"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\store\\Jobs\\jobs_selector.ts"], "sourcesContent": ["import { createFeatureSelector, createSelector } from '@ngrx/store';\r\nimport { ApplicationState } from './jobs_reducer';\r\n\r\nexport const selectDataState = createFeatureSelector<ApplicationState>('Jobs');\r\n\r\nexport const selectJobsData = createSelector(\r\n    selectDataState,\r\n    (state: ApplicationState) => state.Application\r\n);\r\n\r\nexport const selectJobsLoading = createSelector(\r\n    selectDataState,\r\n    (state: ApplicationState) => state.loading\r\n);\r\n\r\nexport const selectJobsError = createSelector(\r\n    selectDataState,\r\n    (state: ApplicationState) => state.error\r\n);\r\n\r\n"], "mappings": "AAAA,SAASA,qBAAqB,EAAEC,cAAc,QAAQ,aAAa;AAGnE,OAAO,MAAMC,eAAe,GAAGF,qBAAqB,CAAmB,MAAM,CAAC;AAE9E,OAAO,MAAMG,cAAc,GAAGF,cAAc,CACxCC,eAAe,EACdE,KAAuB,IAAKA,KAAK,CAACC,WAAW,CACjD;AAED,OAAO,MAAMC,iBAAiB,GAAGL,cAAc,CAC3CC,eAAe,EACdE,KAAuB,IAAKA,KAAK,CAACG,OAAO,CAC7C;AAED,OAAO,MAAMC,eAAe,GAAGP,cAAc,CACzCC,eAAe,EACdE,KAAuB,IAAKA,KAAK,CAACK,KAAK,CAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}