package com.esyndic.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "claims")
public class Claim {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(nullable = false)
    @NotBlank(message = "Title is required")
    @Size(max = 255, message = "Title must not exceed 255 characters")
    private String title;

    @Column(nullable = false, columnDefinition = "TEXT")
    @NotBlank(message = "Description is required")
    private String description;

    @Column(length = 100)
    private String category;

    @Enumerated(EnumType.STRING)
    private ClaimPriority priority = ClaimPriority.MEDIUM;

    @Enumerated(EnumType.STRING)
    private ClaimStatus status = ClaimStatus.PENDING;

    @Column(name = "image_file_path", length = 500)
    private String imageFilePath;

    @Column(name = "resolution_notes", columnDefinition = "TEXT")
    private String resolutionNotes;

    @Column(name = "resolved_at")
    private LocalDateTime resolvedAt;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "building_id", nullable = false)
    @NotNull(message = "Building is required")
    private Building building;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "apartment_id")
    private Apartment apartment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "submitted_by", nullable = false)
    @NotNull(message = "Submitted by user is required")
    private User submittedBy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assigned_to")
    private User assignedTo;

    // Enums
    public enum ClaimPriority {
        LOW, MEDIUM, HIGH, URGENT
    }

    public enum ClaimStatus {
        PENDING, IN_PROGRESS, RESOLVED, CLOSED
    }

    // Constructors
    public Claim() {}

    public Claim(Building building, User submittedBy, String title, String description) {
        this.building = building;
        this.submittedBy = submittedBy;
        this.title = title;
        this.description = description;
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public ClaimPriority getPriority() {
        return priority;
    }

    public void setPriority(ClaimPriority priority) {
        this.priority = priority;
    }

    public ClaimStatus getStatus() {
        return status;
    }

    public void setStatus(ClaimStatus status) {
        this.status = status;
    }

    public String getImageFilePath() {
        return imageFilePath;
    }

    public void setImageFilePath(String imageFilePath) {
        this.imageFilePath = imageFilePath;
    }

    public String getResolutionNotes() {
        return resolutionNotes;
    }

    public void setResolutionNotes(String resolutionNotes) {
        this.resolutionNotes = resolutionNotes;
    }

    public LocalDateTime getResolvedAt() {
        return resolvedAt;
    }

    public void setResolvedAt(LocalDateTime resolvedAt) {
        this.resolvedAt = resolvedAt;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Building getBuilding() {
        return building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }

    public Apartment getApartment() {
        return apartment;
    }

    public void setApartment(Apartment apartment) {
        this.apartment = apartment;
    }

    public User getSubmittedBy() {
        return submittedBy;
    }

    public void setSubmittedBy(User submittedBy) {
        this.submittedBy = submittedBy;
    }

    public User getAssignedTo() {
        return assignedTo;
    }

    public void setAssignedTo(User assignedTo) {
        this.assignedTo = assignedTo;
    }

    // Helper methods
    public boolean isPending() {
        return status == ClaimStatus.PENDING;
    }

    public boolean isInProgress() {
        return status == ClaimStatus.IN_PROGRESS;
    }

    public boolean isResolved() {
        return status == ClaimStatus.RESOLVED;
    }

    public boolean isClosed() {
        return status == ClaimStatus.CLOSED;
    }

    public boolean isUrgent() {
        return priority == ClaimPriority.URGENT;
    }

    public boolean isHighPriority() {
        return priority == ClaimPriority.HIGH || priority == ClaimPriority.URGENT;
    }

    public void resolve(String resolutionNotes, User resolver) {
        this.status = ClaimStatus.RESOLVED;
        this.resolutionNotes = resolutionNotes;
        this.resolvedAt = LocalDateTime.now();
        this.assignedTo = resolver;
    }

    public void close() {
        this.status = ClaimStatus.CLOSED;
    }

    public void assignTo(User user) {
        this.assignedTo = user;
        if (this.status == ClaimStatus.PENDING) {
            this.status = ClaimStatus.IN_PROGRESS;
        }
    }

    public boolean hasImage() {
        return imageFilePath != null && !imageFilePath.trim().isEmpty();
    }

    @Override
    public String toString() {
        return "Claim{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", category='" + category + '\'' +
                ", priority=" + priority +
                ", status=" + status +
                ", createdAt=" + createdAt +
                '}';
    }
}
