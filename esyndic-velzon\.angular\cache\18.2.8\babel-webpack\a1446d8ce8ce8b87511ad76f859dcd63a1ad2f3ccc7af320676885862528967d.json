{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../../../shared/breadcrumbs/breadcrumbs.component\";\nimport * as i4 from \"angularx-flatpickr\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nexport class NewjobComponent {\n  constructor(formBuilder) {\n    this.formBuilder = formBuilder;\n    this.submitted = false;\n  }\n  ngOnInit() {\n    /**\n    * BreadCrumb\n    */\n    this.breadCrumbItems = [{\n      label: 'Jobs'\n    }, {\n      label: 'New Job',\n      active: true\n    }];\n    // Validation\n    this.itemData = this.formBuilder.group({\n      title: ['', [Validators.required]],\n      position: ['', [Validators.required]],\n      category: ['', [Validators.required]],\n      job_type: ['', [Validators.required]],\n      description: ['', [Validators.required]],\n      vacancy: ['', [Validators.required]],\n      experience: ['', [Validators.required]],\n      last_date: ['', [Validators.required]],\n      close_date: ['', [Validators.required]],\n      start_salary: ['', [Validators.required]],\n      last_salary: ['', [Validators.required]],\n      country: ['', [Validators.required]],\n      state: ['', [Validators.required]],\n      tags: ['', [Validators.required]]\n    });\n    /**\n    * Default Select2\n    */\n    // multiDefaultOption1 = 'Watches';\n    // Default1 = [\n    //   { name: 'Watches' },\n    //   { name: 'Headset' },\n    //   { name: 'Sweatshirt' },\n    // ];\n  }\n  /**\n  * Returns form\n  */\n  get form() {\n    return this.itemData.controls;\n  }\n  createJob() {\n    if (this.itemData.valid) {}\n    this.submitted = true;\n  }\n  static {\n    this.ɵfac = function NewjobComponent_Factory(t) {\n      return new (t || NewjobComponent)(i0.ɵɵdirectiveInject(i1.UntypedFormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NewjobComponent,\n      selectors: [[\"app-newjob\"]],\n      decls: 171,\n      vars: 37,\n      consts: [[\"title\", \"New Job\", 3, \"breadcrumbItems\"], [1, \"row\"], [1, \"col-lg-12\"], [1, \"card\"], [\"action\", \"#\", 3, \"ngSubmit\", \"formGroup\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"card-body\"], [1, \"row\", \"g-4\"], [1, \"col-lg-6\"], [\"for\", \"job-title-Input\", 1, \"form-label\"], [1, \"text-danger\"], [\"type\", \"text\", \"id\", \"job-title-Input\", \"placeholder\", \"Enter job title\", \"required\", \"\", \"formControlName\", \"title\", 1, \"form-control\", 3, \"ngClass\"], [1, \"invalid-feedback\"], [\"for\", \"job-position-Input\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"job-position-Input\", \"placeholder\", \"Enter job position\", \"required\", \"\", \"formControlName\", \"position\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"job-category-Input\", 1, \"form-label\"], [\"data-choices\", \"\", \"name\", \"job-category-Input\", \"required\", \"\", \"formControlName\", \"category\", 1, \"form-control\", 3, \"ngClass\"], [\"value\", \"\"], [\"value\", \"Accounting & Finance\"], [\"value\", \"Purchasing Manager\"], [\"value\", \"Education & training\"], [\"value\", \"Marketing & Advertising\"], [\"value\", \"It / Software Jobs\"], [\"value\", \"Digital Marketing\"], [\"value\", \"Administrative Officer\"], [\"value\", \"Government Jobs\"], [\"for\", \"job-type-Input\", 1, \"form-label\"], [\"data-choices\", \"\", \"name\", \"job-type-Input\", \"required\", \"\", \"formControlName\", \"job_type\", 1, \"form-control\", 3, \"ngClass\"], [\"value\", \"Full Time\"], [\"value\", \"Part Time\"], [\"value\", \"Freelance\"], [\"value\", \"Internship\"], [\"for\", \"description-field\", 1, \"form-label\"], [\"id\", \"description-field\", \"rows\", \"3\", \"placeholder\", \"Enter description\", \"required\", \"\", \"formControlName\", \"description\", 1, \"form-control\", 3, \"ngClass\"], [1, \"col-md-6\"], [\"for\", \"vancancy-Input\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"vancancy-Input\", \"placeholder\", \"No. of Vacancy\", \"required\", \"\", \"formControlName\", \"vacancy\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"experience-Input\", 1, \"form-label\"], [\"data-choices\", \"\", \"name\", \"experience-Input\", \"formControlName\", \"experience\", 1, \"form-control\", 3, \"ngClass\"], [\"value\", \"0 Year\"], [\"value\", \"2 Years\"], [\"value\", \"3 Years\"], [\"value\", \"4 Years\"], [\"value\", \"5 Years\"], [\"for\", \"last-apply-date-Input\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"last-apply-date-Input\", \"mwlFlatpickr\", \"\", \"data-date-format\", \"d M, Y\", \"placeholder\", \"Select date\", \"required\", \"\", \"formControlName\", \"last_date\", 1, \"form-control\", 3, \"convertModelValue\", \"ngClass\"], [\"for\", \"close-date-Input\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"close-date-Input\", \"mwlFlatpickr\", \"\", \"data-date-format\", \"d M, Y\", \"placeholder\", \"Select date\", \"required\", \"\", \"formControlName\", \"close_date\", 1, \"form-control\", 3, \"convertModelValue\", \"ngClass\"], [\"for\", \"start-salary-Input\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"start-salary-Input\", \"placeholder\", \"Enter start salary\", \"required\", \"\", \"formControlName\", \"start_salary\", 1, \"form-control\"], [\"for\", \"last-salary-Input\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"last-salary-Input\", \"placeholder\", \"Enter end salary\", \"required\", \"\", \"formControlName\", \"last_salary\", 1, \"form-control\"], [\"for\", \"country-Input\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"country-Input\", \"placeholder\", \"Enter country\", \"required\", \"\", \"formControlName\", \"country\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"city-Input\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"city-Input\", \"placeholder\", \"Enter city\", \"required\", \"\", \"formControlName\", \"state\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"website-field\", 1, \"form-label\"], [\"id\", \"choices-text-unique-values\", \"data-choices\", \"\", \"data-choices-text-unique-true\", \"\", \"type\", \"text\", \"value\", \"Design, Remote\", \"required\", \"\", \"formControlName\", \"tags\", 1, \"form-control\", \"choices__item\", \"choices__item--selectable\"], [1, \"hstack\", \"justify-content-end\", \"gap-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-ghost-danger\"], [1, \"ri-close-line\", \"align-bottom\"], [\"type\", \"submit\", 1, \"btn\", \"btn-secondary\"]],\n      template: function NewjobComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-breadcrumbs\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function NewjobComponent_Template_form_ngSubmit_4_listener() {\n            return ctx.createJob();\n          });\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"h5\", 6);\n          i0.ɵɵtext(7, \"Create Job\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"div\")(12, \"label\", 10);\n          i0.ɵɵtext(13, \"Job Title \");\n          i0.ɵɵelementStart(14, \"span\", 11);\n          i0.ɵɵtext(15, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(16, \"input\", 12);\n          i0.ɵɵelementStart(17, \"div\", 13);\n          i0.ɵɵtext(18, \"Please, enter the title.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"div\")(21, \"label\", 14);\n          i0.ɵɵtext(22, \"Job Position \");\n          i0.ɵɵelementStart(23, \"span\", 11);\n          i0.ɵɵtext(24, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(25, \"input\", 15);\n          i0.ɵɵelementStart(26, \"div\", 13);\n          i0.ɵɵtext(27, \"Please, enter the Job Position.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 9)(29, \"div\")(30, \"label\", 16);\n          i0.ɵɵtext(31, \"Job Category \");\n          i0.ɵɵelementStart(32, \"span\", 11);\n          i0.ɵɵtext(33, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"select\", 17)(35, \"option\", 18);\n          i0.ɵɵtext(36, \"Select Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"option\", 19);\n          i0.ɵɵtext(38, \"Accounting & Finance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"option\", 20);\n          i0.ɵɵtext(40, \"Purchasing Manager\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"option\", 21);\n          i0.ɵɵtext(42, \"Education & training\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"option\", 22);\n          i0.ɵɵtext(44, \"Marketing & Advertising\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"option\", 23);\n          i0.ɵɵtext(46, \"It / Software Jobs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"option\", 24);\n          i0.ɵɵtext(48, \"Digital Marketing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"option\", 25);\n          i0.ɵɵtext(50, \"Administrative Officer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"option\", 26);\n          i0.ɵɵtext(52, \"Government Jobs\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 13);\n          i0.ɵɵtext(54, \"Please, select the Job Category.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"div\", 9)(56, \"div\")(57, \"label\", 27);\n          i0.ɵɵtext(58, \"Job Type \");\n          i0.ɵɵelementStart(59, \"span\", 11);\n          i0.ɵɵtext(60, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"select\", 28)(62, \"option\", 18);\n          i0.ɵɵtext(63, \"Select job type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"option\", 29);\n          i0.ɵɵtext(65, \"Full Time\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"option\", 30);\n          i0.ɵɵtext(67, \"Part Time\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"option\", 31);\n          i0.ɵɵtext(69, \"Freelance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"option\", 32);\n          i0.ɵɵtext(71, \"Internship\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"div\", 13);\n          i0.ɵɵtext(73, \"Please, select the Job Type.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(74, \"div\", 2)(75, \"div\")(76, \"label\", 33);\n          i0.ɵɵtext(77, \"Description \");\n          i0.ɵɵelementStart(78, \"span\", 11);\n          i0.ɵɵtext(79, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(80, \"textarea\", 34);\n          i0.ɵɵelementStart(81, \"div\", 13);\n          i0.ɵɵtext(82, \"Please, enter the Description.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(83, \"div\", 35)(84, \"div\")(85, \"label\", 36);\n          i0.ɵɵtext(86, \"No. of Vacancy \");\n          i0.ɵɵelementStart(87, \"span\", 11);\n          i0.ɵɵtext(88, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(89, \"input\", 37);\n          i0.ɵɵelementStart(90, \"div\", 13);\n          i0.ɵɵtext(91, \"Please, enter the No. of Vacancy.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(92, \"div\", 35)(93, \"div\")(94, \"label\", 38);\n          i0.ɵɵtext(95, \"Experience \");\n          i0.ɵɵelementStart(96, \"span\", 11);\n          i0.ɵɵtext(97, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(98, \"select\", 39)(99, \"option\", 18);\n          i0.ɵɵtext(100, \"Select Experience\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"option\", 40);\n          i0.ɵɵtext(102, \"0 Year\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"option\", 41);\n          i0.ɵɵtext(104, \"2 Years\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"option\", 42);\n          i0.ɵɵtext(106, \"3 Years\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"option\", 43);\n          i0.ɵɵtext(108, \"4 Years\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"option\", 44);\n          i0.ɵɵtext(110, \"5 Years\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(111, \"div\", 13);\n          i0.ɵɵtext(112, \"Please, enter the Experience.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(113, \"div\", 9)(114, \"div\")(115, \"label\", 45);\n          i0.ɵɵtext(116, \"Last Date of Apply \");\n          i0.ɵɵelementStart(117, \"span\", 11);\n          i0.ɵɵtext(118, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(119, \"input\", 46);\n          i0.ɵɵelementStart(120, \"div\", 13);\n          i0.ɵɵtext(121, \"Please, enter the Last Date.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(122, \"div\", 9)(123, \"div\")(124, \"label\", 47);\n          i0.ɵɵtext(125, \"Close Date \");\n          i0.ɵɵelementStart(126, \"span\", 11);\n          i0.ɵɵtext(127, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(128, \"input\", 48);\n          i0.ɵɵelementStart(129, \"div\", 13);\n          i0.ɵɵtext(130, \"Please, enter the Close Date.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(131, \"div\", 35)(132, \"div\")(133, \"label\", 49);\n          i0.ɵɵtext(134, \"Start Salary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(135, \"input\", 50);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(136, \"div\", 35)(137, \"div\")(138, \"label\", 51);\n          i0.ɵɵtext(139, \"Last Salary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(140, \"input\", 52);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(141, \"div\", 35)(142, \"div\")(143, \"label\", 53);\n          i0.ɵɵtext(144, \"Country \");\n          i0.ɵɵelementStart(145, \"span\", 11);\n          i0.ɵɵtext(146, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(147, \"input\", 54);\n          i0.ɵɵelementStart(148, \"div\", 13);\n          i0.ɵɵtext(149, \"Please, enter the Country.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(150, \"div\", 35)(151, \"div\")(152, \"label\", 55);\n          i0.ɵɵtext(153, \"State \");\n          i0.ɵɵelementStart(154, \"span\", 11);\n          i0.ɵɵtext(155, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(156, \"input\", 56);\n          i0.ɵɵelementStart(157, \"div\", 13);\n          i0.ɵɵtext(158, \"Please, enter the State.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(159, \"div\", 2)(160, \"div\")(161, \"label\", 57);\n          i0.ɵɵtext(162, \"Tags\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(163, \"input\", 58);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(164, \"div\", 2)(165, \"div\", 59)(166, \"button\", 60);\n          i0.ɵɵelement(167, \"i\", 61);\n          i0.ɵɵtext(168, \" Cancel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(169, \"button\", 62);\n          i0.ɵɵtext(170, \"Add Job\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"breadcrumbItems\", ctx.breadCrumbItems);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formGroup\", ctx.itemData);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c0, ctx.submitted && ctx.form[\"title\"].errors));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c0, ctx.submitted && ctx.form[\"position\"].errors));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c0, ctx.submitted && ctx.form[\"category\"].errors));\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ctx.submitted && ctx.form[\"job_type\"].errors));\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c0, ctx.submitted && ctx.form[\"description\"].errors));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(25, _c0, ctx.submitted && ctx.form[\"vacancy\"].errors));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(27, _c0, ctx.submitted && ctx.form[\"experience\"].errors));\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"convertModelValue\", true)(\"ngClass\", i0.ɵɵpureFunction1(29, _c0, ctx.submitted && ctx.form[\"last_date\"].errors));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"convertModelValue\", true)(\"ngClass\", i0.ɵɵpureFunction1(31, _c0, ctx.submitted && ctx.form[\"close_date\"].errors));\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(33, _c0, ctx.submitted && ctx.form[\"country\"].errors));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(35, _c0, ctx.submitted && ctx.form[\"state\"].errors));\n        }\n      },\n      dependencies: [i2.NgClass, i3.BreadcrumbsComponent, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i4.FlatpickrDirective],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "NewjobComponent", "constructor", "formBuilder", "submitted", "ngOnInit", "breadCrumbItems", "label", "active", "itemData", "group", "title", "required", "position", "category", "job_type", "description", "vacancy", "experience", "last_date", "close_date", "start_salary", "last_salary", "country", "state", "tags", "form", "controls", "createJob", "valid", "i0", "ɵɵdirectiveInject", "i1", "UntypedFormBuilder", "selectors", "decls", "vars", "consts", "template", "NewjobComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "NewjobComponent_Template_form_ngSubmit_4_listener", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "ɵɵpureFunction1", "_c0", "errors"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\newjob\\newjob.component.ts", "C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\newjob\\newjob.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { UntypedFormBuilder, Validators, UntypedFormGroup, UntypedFormArray, AbstractControl } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-newjob',\r\n  templateUrl: './newjob.component.html',\r\n  styleUrls: ['./newjob.component.scss']\r\n})\r\nexport class NewjobComponent implements OnInit {\r\n  // bread crumb items\r\n  breadCrumbItems!: Array<{}>;\r\n  tags: any;\r\n  // Form\r\n  itemData!: UntypedFormGroup;\r\n  submitted = false;\r\n\r\n  constructor(public formBuilder: UntypedFormBuilder) { }\r\n\r\n  ngOnInit(): void {\r\n    /**\r\n  * BreadCrumb\r\n  */\r\n    this.breadCrumbItems = [\r\n      { label: 'Jobs' },\r\n      { label: 'New Job', active: true }\r\n    ];\r\n\r\n    // Validation\r\n    this.itemData = this.formBuilder.group({\r\n      title: ['', [Validators.required]],\r\n      position: ['', [Validators.required]],\r\n      category: ['', [Validators.required]],\r\n      job_type: ['', [Validators.required]],\r\n      description: ['', [Validators.required]],\r\n      vacancy: ['', [Validators.required]],\r\n      experience: ['', [Validators.required]],\r\n      last_date: ['', [Validators.required]],\r\n      close_date: ['', [Validators.required]],\r\n      start_salary: ['', [Validators.required]],\r\n      last_salary: ['', [Validators.required]],\r\n      country: ['', [Validators.required]],\r\n      state: ['', [Validators.required]],\r\n      tags: ['', [Validators.required]],\r\n    });\r\n\r\n    /**\r\n   * Default Select2\r\n   */\r\n    // multiDefaultOption1 = 'Watches';\r\n    // Default1 = [\r\n    //   { name: 'Watches' },\r\n    //   { name: 'Headset' },\r\n    //   { name: 'Sweatshirt' },\r\n    // ];\r\n\r\n\r\n  }\r\n\r\n  /**\r\n  * Returns form\r\n  */\r\n  get form() {\r\n    return this.itemData.controls;\r\n  }\r\n\r\n  createJob() {\r\n    if (this.itemData.valid) {\r\n    }\r\n    this.submitted = true;\r\n  }\r\n}\r\n", "<!-- Start Breadcrumbs -->\r\n<app-breadcrumbs title=\"New Job\" [breadcrumbItems]=\"breadCrumbItems\"></app-breadcrumbs>\r\n<!-- End Breadcrumbs -->\r\n\r\n<div class=\"row\">\r\n    <div class=\"col-lg-12\">\r\n        <div class=\"card\">\r\n            <form action=\"#\" (ngSubmit)=\"createJob()\" [formGroup]=\"itemData\">\r\n                <div class=\"card-header\">\r\n                    <h5 class=\"card-title mb-0\">Create Job</h5>\r\n                </div>\r\n                <div class=\"card-body\">\r\n                    <div class=\"row g-4\">\r\n                        <div class=\"col-lg-6\">\r\n                            <div>\r\n                                <label for=\"job-title-Input\" class=\"form-label\">Job Title <span\r\n                                        class=\"text-danger\">*</span></label>\r\n                                <input type=\"text\" class=\"form-control\" id=\"job-title-Input\"\r\n                                    placeholder=\"Enter job title\" required formControlName=\"title\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && form['title'].errors }\" />\r\n                                <div class=\"invalid-feedback\">Please, enter the title.</div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-lg-6\">\r\n                            <div>\r\n                                <label for=\"job-position-Input\" class=\"form-label\">Job Position <span\r\n                                        class=\"text-danger\">*</span></label>\r\n                                <input type=\"text\" class=\"form-control\" id=\"job-position-Input\"\r\n                                    placeholder=\"Enter job position\" required formControlName=\"position\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && form['position'].errors }\" />\r\n                                <div class=\"invalid-feedback\">Please, enter the Job Position.</div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-lg-6\">\r\n                            <div>\r\n                                <label for=\"job-category-Input\" class=\"form-label\">Job Category <span\r\n                                        class=\"text-danger\">*</span></label>\r\n                                <select class=\"form-control\" data-choices name=\"job-category-Input\" required\r\n                                    formControlName=\"category\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && form['category'].errors }\">\r\n                                    <option value=\"\">Select Category</option>\r\n                                    <option value=\"Accounting & Finance\">Accounting & Finance</option>\r\n                                    <option value=\"Purchasing Manager\">Purchasing Manager</option>\r\n                                    <option value=\"Education & training\">Education & training</option>\r\n                                    <option value=\"Marketing & Advertising\">Marketing & Advertising</option>\r\n                                    <option value=\"It / Software Jobs\">It / Software Jobs</option>\r\n                                    <option value=\"Digital Marketing\">Digital Marketing</option>\r\n                                    <option value=\"Administrative Officer\">Administrative Officer</option>\r\n                                    <option value=\"Government Jobs\">Government Jobs</option>\r\n                                </select>\r\n                                <div class=\"invalid-feedback\">Please, select the Job Category.</div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-lg-6\">\r\n                            <div>\r\n                                <label for=\"job-type-Input\" class=\"form-label\">Job Type <span\r\n                                        class=\"text-danger\">*</span></label>\r\n                                <select class=\"form-control\" data-choices name=\"job-type-Input\" required\r\n                                    formControlName=\"job_type\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && form['job_type'].errors }\">\r\n                                    <option value=\"\">Select job type</option>\r\n                                    <option value=\"Full Time\">Full Time</option>\r\n                                    <option value=\"Part Time\">Part Time</option>\r\n                                    <option value=\"Freelance\">Freelance</option>\r\n                                    <option value=\"Internship\">Internship</option>\r\n                                </select>\r\n                                <div class=\"invalid-feedback\">Please, select the Job Type.</div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"col-lg-12\">\r\n                            <div>\r\n                                <label for=\"description-field\" class=\"form-label\">Description <span\r\n                                        class=\"text-danger\">*</span></label>\r\n                                <textarea class=\"form-control\" id=\"description-field\" rows=\"3\"\r\n                                    placeholder=\"Enter description\" required formControlName=\"description\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && form['description'].errors }\"></textarea>\r\n                                <div class=\"invalid-feedback\">Please, enter the Description.</div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"col-md-6\">\r\n                            <div>\r\n                                <label for=\"vancancy-Input\" class=\"form-label\">No. of Vacancy <span\r\n                                        class=\"text-danger\">*</span></label>\r\n                                <input type=\"number\" class=\"form-control\" id=\"vancancy-Input\"\r\n                                    placeholder=\"No. of Vacancy\" required formControlName=\"vacancy\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && form['vacancy'].errors }\" />\r\n                                <div class=\"invalid-feedback\">Please, enter the No. of Vacancy.</div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-md-6\">\r\n                            <div>\r\n                                <label for=\"experience-Input\" class=\"form-label\">Experience <span\r\n                                        class=\"text-danger\">*</span></label>\r\n                                <select class=\"form-control\" data-choices name=\"experience-Input\"\r\n                                    formControlName=\"experience\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && form['experience'].errors }\">\r\n                                    <option value=\"\">Select Experience</option>\r\n                                    <option value=\"0 Year\">0 Year</option>\r\n                                    <option value=\"2 Years\">2 Years</option>\r\n                                    <option value=\"3 Years\">3 Years</option>\r\n                                    <option value=\"4 Years\">4 Years</option>\r\n                                    <option value=\"5 Years\">5 Years</option>\r\n                                </select>\r\n                                <div class=\"invalid-feedback\">Please, enter the Experience.</div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"col-lg-6\">\r\n                            <div>\r\n                                <label for=\"last-apply-date-Input\" class=\"form-label\">Last Date of Apply <span\r\n                                        class=\"text-danger\">*</span></label>\r\n                                <input type=\"text\" class=\"form-control\" id=\"last-apply-date-Input\" mwlFlatpickr\r\n                                    data-date-format=\"d M, Y\" placeholder=\"Select date\" [convertModelValue]=\"true\"\r\n                                    required formControlName=\"last_date\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && form['last_date'].errors }\" />\r\n                                <div class=\"invalid-feedback\">Please, enter the Last Date.</div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"col-lg-6\">\r\n                            <div>\r\n                                <label for=\"close-date-Input\" class=\"form-label\">Close Date <span\r\n                                        class=\"text-danger\">*</span></label>\r\n                                <input type=\"text\" class=\"form-control\" id=\"close-date-Input\" mwlFlatpickr\r\n                                    data-date-format=\"d M, Y\" placeholder=\"Select date\" [convertModelValue]=\"true\"\r\n                                    required formControlName=\"close_date\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && form['close_date'].errors }\" />\r\n                                <div class=\"invalid-feedback\">Please, enter the Close Date.</div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"col-md-6\">\r\n                            <div>\r\n                                <label for=\"start-salary-Input\" class=\"form-label\">Start Salary</label>\r\n                                <input type=\"number\" class=\"form-control\" id=\"start-salary-Input\"\r\n                                    placeholder=\"Enter start salary\" required formControlName=\"start_salary\" />\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"col-md-6\">\r\n                            <div>\r\n                                <label for=\"last-salary-Input\" class=\"form-label\">Last Salary</label>\r\n                                <input type=\"number\" class=\"form-control\" id=\"last-salary-Input\"\r\n                                    placeholder=\"Enter end salary\" required formControlName=\"last_salary\" />\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"col-md-6\">\r\n                            <div>\r\n                                <label for=\"country-Input\" class=\"form-label\">Country <span\r\n                                        class=\"text-danger\">*</span></label>\r\n                                <input type=\"text\" class=\"form-control\" id=\"country-Input\" placeholder=\"Enter country\"\r\n                                    required formControlName=\"country\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && form['country'].errors }\" />\r\n                                <div class=\"invalid-feedback\">Please, enter the Country.</div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"col-md-6\">\r\n                            <div>\r\n                                <label for=\"city-Input\" class=\"form-label\">State <span\r\n                                        class=\"text-danger\">*</span></label>\r\n                                <input type=\"text\" class=\"form-control\" id=\"city-Input\" placeholder=\"Enter city\"\r\n                                    required formControlName=\"state\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && form['state'].errors }\" />\r\n                                <div class=\"invalid-feedback\">Please, enter the State.</div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"col-lg-12\">\r\n                            <div>\r\n                                <label for=\"website-field\" class=\"form-label\">Tags</label>\r\n                                <input class=\"form-control choices__item choices__item--selectable\"\r\n                                    id=\"choices-text-unique-values\" data-choices data-choices-text-unique-true\r\n                                    type=\"text\" value=\"Design, Remote\" required formControlName=\"tags\" />\r\n                            </div>\r\n\r\n                            <!-- <div class=\"filter-choices-input\">\r\n                                <ng-select [items]=\"Default1\" [multiple]=\"true\" bindLabel=\"name\" [(ngModel)]=\"multiDefaultOption1\">\r\n                                    <ng-template ng-optgroup-tmp let-item=\"item\">\r\n                                        {{item.country || 'Unnamed group'}}\r\n                                    </ng-template>\r\n                                </ng-select>\r\n                            </div> -->\r\n\r\n                        </div>\r\n\r\n                        <div class=\"col-lg-12\">\r\n                            <div class=\"hstack justify-content-end gap-2\">\r\n                                <button type=\"button\" class=\"btn btn-ghost-danger\"><i\r\n                                        class=\"ri-close-line align-bottom\"></i> Cancel</button>\r\n                                <button type=\"submit\" class=\"btn btn-secondary\">Add Job</button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </form>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAA6BA,UAAU,QAA6D,gBAAgB;;;;;;;;;AAOpH,OAAM,MAAOC,eAAe;EAQ1BC,YAAmBC,WAA+B;IAA/B,KAAAA,WAAW,GAAXA,WAAW;IAF9B,KAAAC,SAAS,GAAG,KAAK;EAEqC;EAEtDC,QAAQA,CAAA;IACN;;;IAGA,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE;IAAM,CAAE,EACjB;MAAEA,KAAK,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAI,CAAE,CACnC;IAED;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACN,WAAW,CAACO,KAAK,CAAC;MACrCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACX,UAAU,CAACY,QAAQ,CAAC,CAAC;MAClCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACb,UAAU,CAACY,QAAQ,CAAC,CAAC;MACrCE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACd,UAAU,CAACY,QAAQ,CAAC,CAAC;MACrCG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACf,UAAU,CAACY,QAAQ,CAAC,CAAC;MACrCI,WAAW,EAAE,CAAC,EAAE,EAAE,CAAChB,UAAU,CAACY,QAAQ,CAAC,CAAC;MACxCK,OAAO,EAAE,CAAC,EAAE,EAAE,CAACjB,UAAU,CAACY,QAAQ,CAAC,CAAC;MACpCM,UAAU,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACY,QAAQ,CAAC,CAAC;MACvCO,SAAS,EAAE,CAAC,EAAE,EAAE,CAACnB,UAAU,CAACY,QAAQ,CAAC,CAAC;MACtCQ,UAAU,EAAE,CAAC,EAAE,EAAE,CAACpB,UAAU,CAACY,QAAQ,CAAC,CAAC;MACvCS,YAAY,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACY,QAAQ,CAAC,CAAC;MACzCU,WAAW,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACY,QAAQ,CAAC,CAAC;MACxCW,OAAO,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACY,QAAQ,CAAC,CAAC;MACpCY,KAAK,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACY,QAAQ,CAAC,CAAC;MAClCa,IAAI,EAAE,CAAC,EAAE,EAAE,CAACzB,UAAU,CAACY,QAAQ,CAAC;KACjC,CAAC;IAEF;;;IAGA;IACA;IACA;IACA;IACA;IACA;EAGF;EAEA;;;EAGA,IAAIc,IAAIA,CAAA;IACN,OAAO,IAAI,CAACjB,QAAQ,CAACkB,QAAQ;EAC/B;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACnB,QAAQ,CAACoB,KAAK,EAAE,CACzB;IACA,IAAI,CAACzB,SAAS,GAAG,IAAI;EACvB;;;uBA7DWH,eAAe,EAAA6B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAfhC,eAAe;MAAAiC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BV,EAAA,CAAAY,SAAA,yBAAuF;UAM3EZ,EAHZ,CAAAa,cAAA,aAAiB,aACU,aACD,cACmD;UAAhDb,EAAA,CAAAc,UAAA,sBAAAC,kDAAA;YAAA,OAAYJ,GAAA,CAAAb,SAAA,EAAW;UAAA,EAAC;UAEjCE,EADJ,CAAAa,cAAA,aAAyB,YACO;UAAAb,EAAA,CAAAgB,MAAA,iBAAU;UAC1ChB,EAD0C,CAAAiB,YAAA,EAAK,EACzC;UAKUjB,EAJhB,CAAAa,cAAA,aAAuB,aACE,cACK,WACb,iBAC+C;UAAAb,EAAA,CAAAgB,MAAA,kBAAU;UAAAhB,EAAA,CAAAa,cAAA,gBAC9B;UAAAb,EAAA,CAAAgB,MAAA,SAAC;UAAOhB,EAAP,CAAAiB,YAAA,EAAO,EAAQ;UAC5CjB,EAAA,CAAAY,SAAA,iBAEsE;UACtEZ,EAAA,CAAAa,cAAA,eAA8B;UAAAb,EAAA,CAAAgB,MAAA,gCAAwB;UAE9DhB,EAF8D,CAAAiB,YAAA,EAAM,EAC1D,EACJ;UAGEjB,EAFR,CAAAa,cAAA,cAAsB,WACb,iBACkD;UAAAb,EAAA,CAAAgB,MAAA,qBAAa;UAAAhB,EAAA,CAAAa,cAAA,gBACpC;UAAAb,EAAA,CAAAgB,MAAA,SAAC;UAAOhB,EAAP,CAAAiB,YAAA,EAAO,EAAQ;UAC5CjB,EAAA,CAAAY,SAAA,iBAEyE;UACzEZ,EAAA,CAAAa,cAAA,eAA8B;UAAAb,EAAA,CAAAgB,MAAA,uCAA+B;UAErEhB,EAFqE,CAAAiB,YAAA,EAAM,EACjE,EACJ;UAGEjB,EAFR,CAAAa,cAAA,cAAsB,WACb,iBACkD;UAAAb,EAAA,CAAAgB,MAAA,qBAAa;UAAAhB,EAAA,CAAAa,cAAA,gBACpC;UAAAb,EAAA,CAAAgB,MAAA,SAAC;UAAOhB,EAAP,CAAAiB,YAAA,EAAO,EAAQ;UAIxCjB,EAHJ,CAAAa,cAAA,kBAEuE,kBAClD;UAAAb,EAAA,CAAAgB,MAAA,uBAAe;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UACzCjB,EAAA,CAAAa,cAAA,kBAAqC;UAAAb,EAAA,CAAAgB,MAAA,4BAAoB;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAClEjB,EAAA,CAAAa,cAAA,kBAAmC;UAAAb,EAAA,CAAAgB,MAAA,0BAAkB;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAC9DjB,EAAA,CAAAa,cAAA,kBAAqC;UAAAb,EAAA,CAAAgB,MAAA,4BAAoB;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAClEjB,EAAA,CAAAa,cAAA,kBAAwC;UAAAb,EAAA,CAAAgB,MAAA,+BAAuB;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UACxEjB,EAAA,CAAAa,cAAA,kBAAmC;UAAAb,EAAA,CAAAgB,MAAA,0BAAkB;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAC9DjB,EAAA,CAAAa,cAAA,kBAAkC;UAAAb,EAAA,CAAAgB,MAAA,yBAAiB;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAC5DjB,EAAA,CAAAa,cAAA,kBAAuC;UAAAb,EAAA,CAAAgB,MAAA,8BAAsB;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UACtEjB,EAAA,CAAAa,cAAA,kBAAgC;UAAAb,EAAA,CAAAgB,MAAA,uBAAe;UACnDhB,EADmD,CAAAiB,YAAA,EAAS,EACnD;UACTjB,EAAA,CAAAa,cAAA,eAA8B;UAAAb,EAAA,CAAAgB,MAAA,wCAAgC;UAEtEhB,EAFsE,CAAAiB,YAAA,EAAM,EAClE,EACJ;UAGEjB,EAFR,CAAAa,cAAA,cAAsB,WACb,iBAC8C;UAAAb,EAAA,CAAAgB,MAAA,iBAAS;UAAAhB,EAAA,CAAAa,cAAA,gBAC5B;UAAAb,EAAA,CAAAgB,MAAA,SAAC;UAAOhB,EAAP,CAAAiB,YAAA,EAAO,EAAQ;UAIxCjB,EAHJ,CAAAa,cAAA,kBAEuE,kBAClD;UAAAb,EAAA,CAAAgB,MAAA,uBAAe;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UACzCjB,EAAA,CAAAa,cAAA,kBAA0B;UAAAb,EAAA,CAAAgB,MAAA,iBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAC5CjB,EAAA,CAAAa,cAAA,kBAA0B;UAAAb,EAAA,CAAAgB,MAAA,iBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAC5CjB,EAAA,CAAAa,cAAA,kBAA0B;UAAAb,EAAA,CAAAgB,MAAA,iBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAC5CjB,EAAA,CAAAa,cAAA,kBAA2B;UAAAb,EAAA,CAAAgB,MAAA,kBAAU;UACzChB,EADyC,CAAAiB,YAAA,EAAS,EACzC;UACTjB,EAAA,CAAAa,cAAA,eAA8B;UAAAb,EAAA,CAAAgB,MAAA,oCAA4B;UAElEhB,EAFkE,CAAAiB,YAAA,EAAM,EAC9D,EACJ;UAIEjB,EAFR,CAAAa,cAAA,cAAuB,WACd,iBACiD;UAAAb,EAAA,CAAAgB,MAAA,oBAAY;UAAAhB,EAAA,CAAAa,cAAA,gBAClC;UAAAb,EAAA,CAAAgB,MAAA,SAAC;UAAOhB,EAAP,CAAAiB,YAAA,EAAO,EAAQ;UAC5CjB,EAAA,CAAAY,SAAA,oBAEqF;UACrFZ,EAAA,CAAAa,cAAA,eAA8B;UAAAb,EAAA,CAAAgB,MAAA,sCAA8B;UAEpEhB,EAFoE,CAAAiB,YAAA,EAAM,EAChE,EACJ;UAIEjB,EAFR,CAAAa,cAAA,eAAsB,WACb,iBAC8C;UAAAb,EAAA,CAAAgB,MAAA,uBAAe;UAAAhB,EAAA,CAAAa,cAAA,gBAClC;UAAAb,EAAA,CAAAgB,MAAA,SAAC;UAAOhB,EAAP,CAAAiB,YAAA,EAAO,EAAQ;UAC5CjB,EAAA,CAAAY,SAAA,iBAEwE;UACxEZ,EAAA,CAAAa,cAAA,eAA8B;UAAAb,EAAA,CAAAgB,MAAA,yCAAiC;UAEvEhB,EAFuE,CAAAiB,YAAA,EAAM,EACnE,EACJ;UAGEjB,EAFR,CAAAa,cAAA,eAAsB,WACb,iBACgD;UAAAb,EAAA,CAAAgB,MAAA,mBAAW;UAAAhB,EAAA,CAAAa,cAAA,gBAChC;UAAAb,EAAA,CAAAgB,MAAA,SAAC;UAAOhB,EAAP,CAAAiB,YAAA,EAAO,EAAQ;UAIxCjB,EAHJ,CAAAa,cAAA,kBAEyE,kBACpD;UAAAb,EAAA,CAAAgB,MAAA,0BAAiB;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAC3CjB,EAAA,CAAAa,cAAA,mBAAuB;UAAAb,EAAA,CAAAgB,MAAA,eAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UACtCjB,EAAA,CAAAa,cAAA,mBAAwB;UAAAb,EAAA,CAAAgB,MAAA,gBAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UACxCjB,EAAA,CAAAa,cAAA,mBAAwB;UAAAb,EAAA,CAAAgB,MAAA,gBAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UACxCjB,EAAA,CAAAa,cAAA,mBAAwB;UAAAb,EAAA,CAAAgB,MAAA,gBAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UACxCjB,EAAA,CAAAa,cAAA,mBAAwB;UAAAb,EAAA,CAAAgB,MAAA,gBAAO;UACnChB,EADmC,CAAAiB,YAAA,EAAS,EACnC;UACTjB,EAAA,CAAAa,cAAA,gBAA8B;UAAAb,EAAA,CAAAgB,MAAA,sCAA6B;UAEnEhB,EAFmE,CAAAiB,YAAA,EAAM,EAC/D,EACJ;UAIEjB,EAFR,CAAAa,cAAA,eAAsB,YACb,kBACqD;UAAAb,EAAA,CAAAgB,MAAA,4BAAmB;UAAAhB,EAAA,CAAAa,cAAA,iBAC7C;UAAAb,EAAA,CAAAgB,MAAA,UAAC;UAAOhB,EAAP,CAAAiB,YAAA,EAAO,EAAQ;UAC5CjB,EAAA,CAAAY,SAAA,kBAG0E;UAC1EZ,EAAA,CAAAa,cAAA,gBAA8B;UAAAb,EAAA,CAAAgB,MAAA,qCAA4B;UAElEhB,EAFkE,CAAAiB,YAAA,EAAM,EAC9D,EACJ;UAIEjB,EAFR,CAAAa,cAAA,eAAsB,YACb,kBACgD;UAAAb,EAAA,CAAAgB,MAAA,oBAAW;UAAAhB,EAAA,CAAAa,cAAA,iBAChC;UAAAb,EAAA,CAAAgB,MAAA,UAAC;UAAOhB,EAAP,CAAAiB,YAAA,EAAO,EAAQ;UAC5CjB,EAAA,CAAAY,SAAA,kBAG2E;UAC3EZ,EAAA,CAAAa,cAAA,gBAA8B;UAAAb,EAAA,CAAAgB,MAAA,sCAA6B;UAEnEhB,EAFmE,CAAAiB,YAAA,EAAM,EAC/D,EACJ;UAIEjB,EAFR,CAAAa,cAAA,gBAAsB,YACb,kBACkD;UAAAb,EAAA,CAAAgB,MAAA,qBAAY;UAAAhB,EAAA,CAAAiB,YAAA,EAAQ;UACvEjB,EAAA,CAAAY,SAAA,kBAC+E;UAEvFZ,EADI,CAAAiB,YAAA,EAAM,EACJ;UAIEjB,EAFR,CAAAa,cAAA,gBAAsB,YACb,kBACiD;UAAAb,EAAA,CAAAgB,MAAA,oBAAW;UAAAhB,EAAA,CAAAiB,YAAA,EAAQ;UACrEjB,EAAA,CAAAY,SAAA,kBAC4E;UAEpFZ,EADI,CAAAiB,YAAA,EAAM,EACJ;UAIEjB,EAFR,CAAAa,cAAA,gBAAsB,YACb,kBAC6C;UAAAb,EAAA,CAAAgB,MAAA,iBAAQ;UAAAhB,EAAA,CAAAa,cAAA,iBAC1B;UAAAb,EAAA,CAAAgB,MAAA,UAAC;UAAOhB,EAAP,CAAAiB,YAAA,EAAO,EAAQ;UAC5CjB,EAAA,CAAAY,SAAA,kBAEwE;UACxEZ,EAAA,CAAAa,cAAA,gBAA8B;UAAAb,EAAA,CAAAgB,MAAA,mCAA0B;UAEhEhB,EAFgE,CAAAiB,YAAA,EAAM,EAC5D,EACJ;UAIEjB,EAFR,CAAAa,cAAA,gBAAsB,YACb,kBAC0C;UAAAb,EAAA,CAAAgB,MAAA,eAAM;UAAAhB,EAAA,CAAAa,cAAA,iBACrB;UAAAb,EAAA,CAAAgB,MAAA,UAAC;UAAOhB,EAAP,CAAAiB,YAAA,EAAO,EAAQ;UAC5CjB,EAAA,CAAAY,SAAA,kBAEsE;UACtEZ,EAAA,CAAAa,cAAA,gBAA8B;UAAAb,EAAA,CAAAgB,MAAA,iCAAwB;UAE9DhB,EAF8D,CAAAiB,YAAA,EAAM,EAC1D,EACJ;UAIEjB,EAFR,CAAAa,cAAA,eAAuB,YACd,kBAC6C;UAAAb,EAAA,CAAAgB,MAAA,aAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAQ;UAC1DjB,EAAA,CAAAY,SAAA,kBAEyE;UAWjFZ,EAVI,CAAAiB,YAAA,EAAM,EAUJ;UAIEjB,EAFR,CAAAa,cAAA,eAAuB,gBAC2B,mBACS;UAAAb,EAAA,CAAAY,SAAA,cACJ;UAACZ,EAAA,CAAAgB,MAAA,gBAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAC/DjB,EAAA,CAAAa,cAAA,mBAAgD;UAAAb,EAAA,CAAAgB,MAAA,gBAAO;UAQvFhB,EARuF,CAAAiB,YAAA,EAAS,EAC9D,EACJ,EACJ,EACJ,EACH,EACL,EACJ,EACJ;;;UAxM2BjB,EAAA,CAAAkB,UAAA,oBAAAP,GAAA,CAAAnC,eAAA,CAAmC;UAMdwB,EAAA,CAAAmB,SAAA,GAAsB;UAAtBnB,EAAA,CAAAkB,UAAA,cAAAP,GAAA,CAAAhC,QAAA,CAAsB;UAYxCqB,EAAA,CAAAmB,SAAA,IAA+D;UAA/DnB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAArC,SAAA,IAAAqC,GAAA,CAAAf,IAAA,UAAA0B,MAAA,EAA+D;UAU/DtB,EAAA,CAAAmB,SAAA,GAAkE;UAAlEnB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAArC,SAAA,IAAAqC,GAAA,CAAAf,IAAA,aAAA0B,MAAA,EAAkE;UAUlEtB,EAAA,CAAAmB,SAAA,GAAkE;UAAlEnB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAArC,SAAA,IAAAqC,GAAA,CAAAf,IAAA,aAAA0B,MAAA,EAAkE;UAoBlEtB,EAAA,CAAAmB,SAAA,IAAkE;UAAlEnB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAArC,SAAA,IAAAqC,GAAA,CAAAf,IAAA,aAAA0B,MAAA,EAAkE;UAiBlEtB,EAAA,CAAAmB,SAAA,IAAqE;UAArEnB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAArC,SAAA,IAAAqC,GAAA,CAAAf,IAAA,gBAAA0B,MAAA,EAAqE;UAWrEtB,EAAA,CAAAmB,SAAA,GAAiE;UAAjEnB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAArC,SAAA,IAAAqC,GAAA,CAAAf,IAAA,YAAA0B,MAAA,EAAiE;UAUjEtB,EAAA,CAAAmB,SAAA,GAAoE;UAApEnB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAArC,SAAA,IAAAqC,GAAA,CAAAf,IAAA,eAAA0B,MAAA,EAAoE;UAiBhBtB,EAAA,CAAAmB,SAAA,IAA0B;UAE9EnB,EAFoD,CAAAkB,UAAA,2BAA0B,YAAAlB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAArC,SAAA,IAAAqC,GAAA,CAAAf,IAAA,cAAA0B,MAAA,EAEX;UAUftB,EAAA,CAAAmB,SAAA,GAA0B;UAE9EnB,EAFoD,CAAAkB,UAAA,2BAA0B,YAAAlB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAArC,SAAA,IAAAqC,GAAA,CAAAf,IAAA,eAAA0B,MAAA,EAEV;UA2BpEtB,EAAA,CAAAmB,SAAA,IAAiE;UAAjEnB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAArC,SAAA,IAAAqC,GAAA,CAAAf,IAAA,YAAA0B,MAAA,EAAiE;UAWjEtB,EAAA,CAAAmB,SAAA,GAA+D;UAA/DnB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAArC,SAAA,IAAAqC,GAAA,CAAAf,IAAA,UAAA0B,MAAA,EAA+D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}