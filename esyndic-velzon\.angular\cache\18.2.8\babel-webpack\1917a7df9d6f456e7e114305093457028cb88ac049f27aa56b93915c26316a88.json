{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { ECPolygon } from '../line/poly.js';\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { bind } from 'zrender/lib/core/util.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport ChartView from '../../view/Chart.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar ThemeRiverView = /** @class */function (_super) {\n  __extends(ThemeRiverView, _super);\n  function ThemeRiverView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ThemeRiverView.type;\n    _this._layers = [];\n    return _this;\n  }\n  ThemeRiverView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var self = this;\n    var group = this.group;\n    var layersSeries = seriesModel.getLayerSeries();\n    var layoutInfo = data.getLayout('layoutInfo');\n    var rect = layoutInfo.rect;\n    var boundaryGap = layoutInfo.boundaryGap;\n    group.x = 0;\n    group.y = rect.y + boundaryGap[0];\n    function keyGetter(item) {\n      return item.name;\n    }\n    var dataDiffer = new DataDiffer(this._layersSeries || [], layersSeries, keyGetter, keyGetter);\n    var newLayersGroups = [];\n    dataDiffer.add(bind(process, this, 'add')).update(bind(process, this, 'update')).remove(bind(process, this, 'remove')).execute();\n    function process(status, idx, oldIdx) {\n      var oldLayersGroups = self._layers;\n      if (status === 'remove') {\n        group.remove(oldLayersGroups[idx]);\n        return;\n      }\n      var points0 = [];\n      var points1 = [];\n      var style;\n      var indices = layersSeries[idx].indices;\n      var j = 0;\n      for (; j < indices.length; j++) {\n        var layout = data.getItemLayout(indices[j]);\n        var x = layout.x;\n        var y0 = layout.y0;\n        var y = layout.y;\n        points0.push(x, y0);\n        points1.push(x, y0 + y);\n        style = data.getItemVisual(indices[j], 'style');\n      }\n      var polygon;\n      var textLayout = data.getItemLayout(indices[0]);\n      var labelModel = seriesModel.getModel('label');\n      var margin = labelModel.get('margin');\n      var emphasisModel = seriesModel.getModel('emphasis');\n      if (status === 'add') {\n        var layerGroup = newLayersGroups[idx] = new graphic.Group();\n        polygon = new ECPolygon({\n          shape: {\n            points: points0,\n            stackedOnPoints: points1,\n            smooth: 0.4,\n            stackedOnSmooth: 0.4,\n            smoothConstraint: false\n          },\n          z2: 0\n        });\n        layerGroup.add(polygon);\n        group.add(layerGroup);\n        if (seriesModel.isAnimationEnabled()) {\n          polygon.setClipPath(createGridClipShape(polygon.getBoundingRect(), seriesModel, function () {\n            polygon.removeClipPath();\n          }));\n        }\n      } else {\n        var layerGroup = oldLayersGroups[oldIdx];\n        polygon = layerGroup.childAt(0);\n        group.add(layerGroup);\n        newLayersGroups[idx] = layerGroup;\n        graphic.updateProps(polygon, {\n          shape: {\n            points: points0,\n            stackedOnPoints: points1\n          }\n        }, seriesModel);\n        saveOldStyle(polygon);\n      }\n      setLabelStyle(polygon, getLabelStatesModels(seriesModel), {\n        labelDataIndex: indices[j - 1],\n        defaultText: data.getName(indices[j - 1]),\n        inheritColor: style.fill\n      }, {\n        normal: {\n          verticalAlign: 'middle'\n          // align: 'right'\n        }\n      });\n      polygon.setTextConfig({\n        position: null,\n        local: true\n      });\n      var labelEl = polygon.getTextContent();\n      // TODO More label position options.\n      if (labelEl) {\n        labelEl.x = textLayout.x - margin;\n        labelEl.y = textLayout.y0 + textLayout.y / 2;\n      }\n      polygon.useStyle(style);\n      data.setItemGraphicEl(idx, polygon);\n      setStatesStylesFromModel(polygon, seriesModel);\n      toggleHoverEmphasis(polygon, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n    }\n    this._layersSeries = layersSeries;\n    this._layers = newLayersGroups;\n  };\n  ThemeRiverView.type = 'themeRiver';\n  return ThemeRiverView;\n}(ChartView);\n;\n// add animation to the view\nfunction createGridClipShape(rect, seriesModel, cb) {\n  var rectEl = new graphic.Rect({\n    shape: {\n      x: rect.x - 10,\n      y: rect.y - 10,\n      width: 0,\n      height: rect.height + 20\n    }\n  });\n  graphic.initProps(rectEl, {\n    shape: {\n      x: rect.x - 50,\n      width: rect.width + 100,\n      height: rect.height + 20\n    }\n  }, seriesModel, cb);\n  return rectEl;\n}\nexport default ThemeRiverView;", "map": {"version": 3, "names": ["__extends", "ECPolygon", "graphic", "setStatesStylesFromModel", "toggleHoverEmphasis", "setLabelStyle", "getLabelStatesModels", "bind", "<PERSON><PERSON><PERSON><PERSON>", "ChartView", "saveOldStyle", "ThemeRiverView", "_super", "_this", "apply", "arguments", "type", "_layers", "prototype", "render", "seriesModel", "ecModel", "api", "data", "getData", "self", "group", "layersSeries", "getLayerSeries", "layoutInfo", "getLayout", "rect", "boundaryGap", "x", "y", "keyGetter", "item", "name", "<PERSON><PERSON><PERSON><PERSON>", "_layersSeries", "newLayersGroups", "add", "process", "update", "remove", "execute", "status", "idx", "oldIdx", "oldLayersGroups", "points0", "points1", "style", "indices", "j", "length", "layout", "getItemLayout", "y0", "push", "getItemVisual", "polygon", "textLayout", "labelModel", "getModel", "margin", "get", "emphasisModel", "layerGroup", "Group", "shape", "points", "stackedOnPoints", "smooth", "stackedOnSmooth", "smoothConstraint", "z2", "isAnimationEnabled", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createGridClipShape", "getBoundingRect", "removeClip<PERSON>ath", "childAt", "updateProps", "labelDataIndex", "defaultText", "getName", "inheritColor", "fill", "normal", "verticalAlign", "setTextConfig", "position", "local", "labelEl", "getTextContent", "useStyle", "setItemGraphicEl", "cb", "rectEl", "Rect", "width", "height", "initProps"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/echarts/lib/chart/themeRiver/ThemeRiverView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { ECPolygon } from '../line/poly.js';\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { bind } from 'zrender/lib/core/util.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport ChartView from '../../view/Chart.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar ThemeRiverView = /** @class */function (_super) {\n  __extends(ThemeRiverView, _super);\n  function ThemeRiverView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ThemeRiverView.type;\n    _this._layers = [];\n    return _this;\n  }\n  ThemeRiverView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var self = this;\n    var group = this.group;\n    var layersSeries = seriesModel.getLayerSeries();\n    var layoutInfo = data.getLayout('layoutInfo');\n    var rect = layoutInfo.rect;\n    var boundaryGap = layoutInfo.boundaryGap;\n    group.x = 0;\n    group.y = rect.y + boundaryGap[0];\n    function keyGetter(item) {\n      return item.name;\n    }\n    var dataDiffer = new DataDiffer(this._layersSeries || [], layersSeries, keyGetter, keyGetter);\n    var newLayersGroups = [];\n    dataDiffer.add(bind(process, this, 'add')).update(bind(process, this, 'update')).remove(bind(process, this, 'remove')).execute();\n    function process(status, idx, oldIdx) {\n      var oldLayersGroups = self._layers;\n      if (status === 'remove') {\n        group.remove(oldLayersGroups[idx]);\n        return;\n      }\n      var points0 = [];\n      var points1 = [];\n      var style;\n      var indices = layersSeries[idx].indices;\n      var j = 0;\n      for (; j < indices.length; j++) {\n        var layout = data.getItemLayout(indices[j]);\n        var x = layout.x;\n        var y0 = layout.y0;\n        var y = layout.y;\n        points0.push(x, y0);\n        points1.push(x, y0 + y);\n        style = data.getItemVisual(indices[j], 'style');\n      }\n      var polygon;\n      var textLayout = data.getItemLayout(indices[0]);\n      var labelModel = seriesModel.getModel('label');\n      var margin = labelModel.get('margin');\n      var emphasisModel = seriesModel.getModel('emphasis');\n      if (status === 'add') {\n        var layerGroup = newLayersGroups[idx] = new graphic.Group();\n        polygon = new ECPolygon({\n          shape: {\n            points: points0,\n            stackedOnPoints: points1,\n            smooth: 0.4,\n            stackedOnSmooth: 0.4,\n            smoothConstraint: false\n          },\n          z2: 0\n        });\n        layerGroup.add(polygon);\n        group.add(layerGroup);\n        if (seriesModel.isAnimationEnabled()) {\n          polygon.setClipPath(createGridClipShape(polygon.getBoundingRect(), seriesModel, function () {\n            polygon.removeClipPath();\n          }));\n        }\n      } else {\n        var layerGroup = oldLayersGroups[oldIdx];\n        polygon = layerGroup.childAt(0);\n        group.add(layerGroup);\n        newLayersGroups[idx] = layerGroup;\n        graphic.updateProps(polygon, {\n          shape: {\n            points: points0,\n            stackedOnPoints: points1\n          }\n        }, seriesModel);\n        saveOldStyle(polygon);\n      }\n      setLabelStyle(polygon, getLabelStatesModels(seriesModel), {\n        labelDataIndex: indices[j - 1],\n        defaultText: data.getName(indices[j - 1]),\n        inheritColor: style.fill\n      }, {\n        normal: {\n          verticalAlign: 'middle'\n          // align: 'right'\n        }\n      });\n\n      polygon.setTextConfig({\n        position: null,\n        local: true\n      });\n      var labelEl = polygon.getTextContent();\n      // TODO More label position options.\n      if (labelEl) {\n        labelEl.x = textLayout.x - margin;\n        labelEl.y = textLayout.y0 + textLayout.y / 2;\n      }\n      polygon.useStyle(style);\n      data.setItemGraphicEl(idx, polygon);\n      setStatesStylesFromModel(polygon, seriesModel);\n      toggleHoverEmphasis(polygon, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n    }\n    this._layersSeries = layersSeries;\n    this._layers = newLayersGroups;\n  };\n  ThemeRiverView.type = 'themeRiver';\n  return ThemeRiverView;\n}(ChartView);\n;\n// add animation to the view\nfunction createGridClipShape(rect, seriesModel, cb) {\n  var rectEl = new graphic.Rect({\n    shape: {\n      x: rect.x - 10,\n      y: rect.y - 10,\n      width: 0,\n      height: rect.height + 20\n    }\n  });\n  graphic.initProps(rectEl, {\n    shape: {\n      x: rect.x - 50,\n      width: rect.width + 100,\n      height: rect.height + 20\n    }\n  }, seriesModel, cb);\n  return rectEl;\n}\nexport default ThemeRiverView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,wBAAwB,EAAEC,mBAAmB,QAAQ,sBAAsB;AACpF,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,YAAY,QAAQ,oCAAoC;AACjE,IAAIC,cAAc,GAAG,aAAa,UAAUC,MAAM,EAAE;EAClDZ,SAAS,CAACW,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAAA,EAAG;IACxB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,cAAc,CAACK,IAAI;IAChCH,KAAK,CAACI,OAAO,GAAG,EAAE;IAClB,OAAOJ,KAAK;EACd;EACAF,cAAc,CAACO,SAAS,CAACC,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACrE,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;IAChC,IAAIC,IAAI,GAAG,IAAI;IACf,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,YAAY,GAAGP,WAAW,CAACQ,cAAc,CAAC,CAAC;IAC/C,IAAIC,UAAU,GAAGN,IAAI,CAACO,SAAS,CAAC,YAAY,CAAC;IAC7C,IAAIC,IAAI,GAAGF,UAAU,CAACE,IAAI;IAC1B,IAAIC,WAAW,GAAGH,UAAU,CAACG,WAAW;IACxCN,KAAK,CAACO,CAAC,GAAG,CAAC;IACXP,KAAK,CAACQ,CAAC,GAAGH,IAAI,CAACG,CAAC,GAAGF,WAAW,CAAC,CAAC,CAAC;IACjC,SAASG,SAASA,CAACC,IAAI,EAAE;MACvB,OAAOA,IAAI,CAACC,IAAI;IAClB;IACA,IAAIC,UAAU,GAAG,IAAI9B,UAAU,CAAC,IAAI,CAAC+B,aAAa,IAAI,EAAE,EAAEZ,YAAY,EAAEQ,SAAS,EAAEA,SAAS,CAAC;IAC7F,IAAIK,eAAe,GAAG,EAAE;IACxBF,UAAU,CAACG,GAAG,CAAClC,IAAI,CAACmC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAACC,MAAM,CAACpC,IAAI,CAACmC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAACE,MAAM,CAACrC,IAAI,CAACmC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC;IAChI,SAASH,OAAOA,CAACI,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAE;MACpC,IAAIC,eAAe,GAAGxB,IAAI,CAACR,OAAO;MAClC,IAAI6B,MAAM,KAAK,QAAQ,EAAE;QACvBpB,KAAK,CAACkB,MAAM,CAACK,eAAe,CAACF,GAAG,CAAC,CAAC;QAClC;MACF;MACA,IAAIG,OAAO,GAAG,EAAE;MAChB,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIC,KAAK;MACT,IAAIC,OAAO,GAAG1B,YAAY,CAACoB,GAAG,CAAC,CAACM,OAAO;MACvC,IAAIC,CAAC,GAAG,CAAC;MACT,OAAOA,CAAC,GAAGD,OAAO,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QAC9B,IAAIE,MAAM,GAAGjC,IAAI,CAACkC,aAAa,CAACJ,OAAO,CAACC,CAAC,CAAC,CAAC;QAC3C,IAAIrB,CAAC,GAAGuB,MAAM,CAACvB,CAAC;QAChB,IAAIyB,EAAE,GAAGF,MAAM,CAACE,EAAE;QAClB,IAAIxB,CAAC,GAAGsB,MAAM,CAACtB,CAAC;QAChBgB,OAAO,CAACS,IAAI,CAAC1B,CAAC,EAAEyB,EAAE,CAAC;QACnBP,OAAO,CAACQ,IAAI,CAAC1B,CAAC,EAAEyB,EAAE,GAAGxB,CAAC,CAAC;QACvBkB,KAAK,GAAG7B,IAAI,CAACqC,aAAa,CAACP,OAAO,CAACC,CAAC,CAAC,EAAE,OAAO,CAAC;MACjD;MACA,IAAIO,OAAO;MACX,IAAIC,UAAU,GAAGvC,IAAI,CAACkC,aAAa,CAACJ,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/C,IAAIU,UAAU,GAAG3C,WAAW,CAAC4C,QAAQ,CAAC,OAAO,CAAC;MAC9C,IAAIC,MAAM,GAAGF,UAAU,CAACG,GAAG,CAAC,QAAQ,CAAC;MACrC,IAAIC,aAAa,GAAG/C,WAAW,CAAC4C,QAAQ,CAAC,UAAU,CAAC;MACpD,IAAIlB,MAAM,KAAK,KAAK,EAAE;QACpB,IAAIsB,UAAU,GAAG5B,eAAe,CAACO,GAAG,CAAC,GAAG,IAAI7C,OAAO,CAACmE,KAAK,CAAC,CAAC;QAC3DR,OAAO,GAAG,IAAI5D,SAAS,CAAC;UACtBqE,KAAK,EAAE;YACLC,MAAM,EAAErB,OAAO;YACfsB,eAAe,EAAErB,OAAO;YACxBsB,MAAM,EAAE,GAAG;YACXC,eAAe,EAAE,GAAG;YACpBC,gBAAgB,EAAE;UACpB,CAAC;UACDC,EAAE,EAAE;QACN,CAAC,CAAC;QACFR,UAAU,CAAC3B,GAAG,CAACoB,OAAO,CAAC;QACvBnC,KAAK,CAACe,GAAG,CAAC2B,UAAU,CAAC;QACrB,IAAIhD,WAAW,CAACyD,kBAAkB,CAAC,CAAC,EAAE;UACpChB,OAAO,CAACiB,WAAW,CAACC,mBAAmB,CAAClB,OAAO,CAACmB,eAAe,CAAC,CAAC,EAAE5D,WAAW,EAAE,YAAY;YAC1FyC,OAAO,CAACoB,cAAc,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC;QACL;MACF,CAAC,MAAM;QACL,IAAIb,UAAU,GAAGnB,eAAe,CAACD,MAAM,CAAC;QACxCa,OAAO,GAAGO,UAAU,CAACc,OAAO,CAAC,CAAC,CAAC;QAC/BxD,KAAK,CAACe,GAAG,CAAC2B,UAAU,CAAC;QACrB5B,eAAe,CAACO,GAAG,CAAC,GAAGqB,UAAU;QACjClE,OAAO,CAACiF,WAAW,CAACtB,OAAO,EAAE;UAC3BS,KAAK,EAAE;YACLC,MAAM,EAAErB,OAAO;YACfsB,eAAe,EAAErB;UACnB;QACF,CAAC,EAAE/B,WAAW,CAAC;QACfV,YAAY,CAACmD,OAAO,CAAC;MACvB;MACAxD,aAAa,CAACwD,OAAO,EAAEvD,oBAAoB,CAACc,WAAW,CAAC,EAAE;QACxDgE,cAAc,EAAE/B,OAAO,CAACC,CAAC,GAAG,CAAC,CAAC;QAC9B+B,WAAW,EAAE9D,IAAI,CAAC+D,OAAO,CAACjC,OAAO,CAACC,CAAC,GAAG,CAAC,CAAC,CAAC;QACzCiC,YAAY,EAAEnC,KAAK,CAACoC;MACtB,CAAC,EAAE;QACDC,MAAM,EAAE;UACNC,aAAa,EAAE;UACf;QACF;MACF,CAAC,CAAC;MAEF7B,OAAO,CAAC8B,aAAa,CAAC;QACpBC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,IAAIC,OAAO,GAAGjC,OAAO,CAACkC,cAAc,CAAC,CAAC;MACtC;MACA,IAAID,OAAO,EAAE;QACXA,OAAO,CAAC7D,CAAC,GAAG6B,UAAU,CAAC7B,CAAC,GAAGgC,MAAM;QACjC6B,OAAO,CAAC5D,CAAC,GAAG4B,UAAU,CAACJ,EAAE,GAAGI,UAAU,CAAC5B,CAAC,GAAG,CAAC;MAC9C;MACA2B,OAAO,CAACmC,QAAQ,CAAC5C,KAAK,CAAC;MACvB7B,IAAI,CAAC0E,gBAAgB,CAAClD,GAAG,EAAEc,OAAO,CAAC;MACnC1D,wBAAwB,CAAC0D,OAAO,EAAEzC,WAAW,CAAC;MAC9ChB,mBAAmB,CAACyD,OAAO,EAAEM,aAAa,CAACD,GAAG,CAAC,OAAO,CAAC,EAAEC,aAAa,CAACD,GAAG,CAAC,WAAW,CAAC,EAAEC,aAAa,CAACD,GAAG,CAAC,UAAU,CAAC,CAAC;IACzH;IACA,IAAI,CAAC3B,aAAa,GAAGZ,YAAY;IACjC,IAAI,CAACV,OAAO,GAAGuB,eAAe;EAChC,CAAC;EACD7B,cAAc,CAACK,IAAI,GAAG,YAAY;EAClC,OAAOL,cAAc;AACvB,CAAC,CAACF,SAAS,CAAC;AACZ;AACA;AACA,SAASsE,mBAAmBA,CAAChD,IAAI,EAAEX,WAAW,EAAE8E,EAAE,EAAE;EAClD,IAAIC,MAAM,GAAG,IAAIjG,OAAO,CAACkG,IAAI,CAAC;IAC5B9B,KAAK,EAAE;MACLrC,CAAC,EAAEF,IAAI,CAACE,CAAC,GAAG,EAAE;MACdC,CAAC,EAAEH,IAAI,CAACG,CAAC,GAAG,EAAE;MACdmE,KAAK,EAAE,CAAC;MACRC,MAAM,EAAEvE,IAAI,CAACuE,MAAM,GAAG;IACxB;EACF,CAAC,CAAC;EACFpG,OAAO,CAACqG,SAAS,CAACJ,MAAM,EAAE;IACxB7B,KAAK,EAAE;MACLrC,CAAC,EAAEF,IAAI,CAACE,CAAC,GAAG,EAAE;MACdoE,KAAK,EAAEtE,IAAI,CAACsE,KAAK,GAAG,GAAG;MACvBC,MAAM,EAAEvE,IAAI,CAACuE,MAAM,GAAG;IACxB;EACF,CAAC,EAAElF,WAAW,EAAE8E,EAAE,CAAC;EACnB,OAAOC,MAAM;AACf;AACA,eAAexF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}