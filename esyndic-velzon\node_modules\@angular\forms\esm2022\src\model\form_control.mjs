/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { removeListItem } from '../util';
import { AbstractControl, isOptionsObj, pickAsyncValidators, pickValidators, } from './abstract_model';
function isFormControlState(formState) {
    return (typeof formState === 'object' &&
        formState !== null &&
        Object.keys(formState).length === 2 &&
        'value' in formState &&
        'disabled' in formState);
}
export const FormControl = class FormControl extends AbstractControl {
    constructor(
    // formState and defaultValue will only be null if T is nullable
    formState = null, validatorOrOpts, asyncValidator) {
        super(pickValidators(validatorOrOpts), pickAsyncValidators(asyncValidator, validatorOrOpts));
        /** @publicApi */
        this.defaultValue = null;
        /** @internal */
        this._onChange = [];
        /** @internal */
        this._pendingChange = false;
        this._applyFormState(formState);
        this._setUpdateStrategy(validatorOrOpts);
        this._initObservables();
        this.updateValueAndValidity({
            onlySelf: true,
            // If `asyncValidator` is present, it will trigger control status change from `PENDING` to
            // `VALID` or `INVALID`.
            // The status should be broadcasted via the `statusChanges` observable, so we set
            // `emitEvent` to `true` to allow that during the control creation process.
            emitEvent: !!this.asyncValidator,
        });
        if (isOptionsObj(validatorOrOpts) &&
            (validatorOrOpts.nonNullable || validatorOrOpts.initialValueIsDefault)) {
            if (isFormControlState(formState)) {
                this.defaultValue = formState.value;
            }
            else {
                this.defaultValue = formState;
            }
        }
    }
    setValue(value, options = {}) {
        this.value = this._pendingValue = value;
        if (this._onChange.length && options.emitModelToViewChange !== false) {
            this._onChange.forEach((changeFn) => changeFn(this.value, options.emitViewToModelChange !== false));
        }
        this.updateValueAndValidity(options);
    }
    patchValue(value, options = {}) {
        this.setValue(value, options);
    }
    reset(formState = this.defaultValue, options = {}) {
        this._applyFormState(formState);
        this.markAsPristine(options);
        this.markAsUntouched(options);
        this.setValue(this.value, options);
        this._pendingChange = false;
    }
    /**  @internal */
    _updateValue() { }
    /**  @internal */
    _anyControls(condition) {
        return false;
    }
    /**  @internal */
    _allControlsDisabled() {
        return this.disabled;
    }
    registerOnChange(fn) {
        this._onChange.push(fn);
    }
    /** @internal */
    _unregisterOnChange(fn) {
        removeListItem(this._onChange, fn);
    }
    registerOnDisabledChange(fn) {
        this._onDisabledChange.push(fn);
    }
    /** @internal */
    _unregisterOnDisabledChange(fn) {
        removeListItem(this._onDisabledChange, fn);
    }
    /** @internal */
    _forEachChild(cb) { }
    /** @internal */
    _syncPendingControls() {
        if (this.updateOn === 'submit') {
            if (this._pendingDirty)
                this.markAsDirty();
            if (this._pendingTouched)
                this.markAsTouched();
            if (this._pendingChange) {
                this.setValue(this._pendingValue, { onlySelf: true, emitModelToViewChange: false });
                return true;
            }
        }
        return false;
    }
    _applyFormState(formState) {
        if (isFormControlState(formState)) {
            this.value = this._pendingValue = formState.value;
            formState.disabled
                ? this.disable({ onlySelf: true, emitEvent: false })
                : this.enable({ onlySelf: true, emitEvent: false });
        }
        else {
            this.value = this._pendingValue = formState;
        }
    }
};
export const UntypedFormControl = FormControl;
/**
 * @description
 * Asserts that the given control is an instance of `FormControl`
 *
 * @publicApi
 */
export const isFormControl = (control) => control instanceof FormControl;
//# sourceMappingURL=data:application/json;base64,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