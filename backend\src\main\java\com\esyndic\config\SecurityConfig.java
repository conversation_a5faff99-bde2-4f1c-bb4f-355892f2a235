package com.esyndic.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Value("${spring.security.oauth2.resourceserver.jwt.jwk-set-uri}")
    private String jwkSetUri;

    @Value("${cors.allowed-origins}")
    private String allowedOrigins;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // Public endpoints
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/api/health/**").permitAll()
                .requestMatchers("/actuator/**").permitAll()
                
                // Paymee webhook endpoints (should be secured with API key in production)
                .requestMatchers("/api/payments/webhook/**").permitAll()
                
                // SUPERADMIN only endpoints
                .requestMatchers("/api/admin/users/**").hasRole("SUPERADMIN")
                .requestMatchers("/api/admin/buildings/**").hasRole("SUPERADMIN")
                .requestMatchers("/api/admin/system/**").hasRole("SUPERADMIN")
                
                // ADMIN and SUPERADMIN endpoints
                .requestMatchers("/api/buildings/*/manage/**").hasAnyRole("ADMIN", "SUPERADMIN")
                .requestMatchers("/api/expenses/*/approve/**").hasAnyRole("ADMIN", "SUPERADMIN")
                .requestMatchers("/api/claims/*/assign/**").hasAnyRole("ADMIN", "SUPERADMIN")
                
                // PRESIDENT, ADMIN and SUPERADMIN endpoints
                .requestMatchers("/api/assemblies/*/create").hasAnyRole("PRESIDENT", "ADMIN", "SUPERADMIN")
                .requestMatchers("/api/assemblies/*/manage/**").hasAnyRole("PRESIDENT", "ADMIN", "SUPERADMIN")
                .requestMatchers("/api/expenses/create").hasAnyRole("PRESIDENT", "ADMIN", "SUPERADMIN")
                
                // OWNER, PRESIDENT, ADMIN and SUPERADMIN endpoints
                .requestMatchers("/api/payments/create").hasAnyRole("OWNER", "PRESIDENT", "ADMIN", "SUPERADMIN")
                .requestMatchers("/api/claims/create").hasAnyRole("OWNER", "RESIDENT", "PRESIDENT", "ADMIN", "SUPERADMIN")
                
                // All authenticated users endpoints
                .requestMatchers("/api/profile/**").authenticated()
                .requestMatchers("/api/buildings/*/view/**").authenticated()
                .requestMatchers("/api/apartments/*/view/**").authenticated()
                .requestMatchers("/api/assemblies/*/view/**").authenticated()
                .requestMatchers("/api/payments/view/**").authenticated()
                .requestMatchers("/api/expenses/view/**").authenticated()
                .requestMatchers("/api/claims/view/**").authenticated()
                
                // All other requests require authentication
                .anyRequest().authenticated()
            )
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(jwt -> jwt
                    .decoder(jwtDecoder())
                    .jwtAuthenticationConverter(jwtAuthenticationConverter())
                )
            );

        return http.build();
    }

    @Bean
    public JwtDecoder jwtDecoder() {
        return NimbusJwtDecoder.withJwkSetUri(jwkSetUri).build();
    }

    @Bean
    public JwtAuthenticationConverter jwtAuthenticationConverter() {
        JwtGrantedAuthoritiesConverter authoritiesConverter = new JwtGrantedAuthoritiesConverter();
        authoritiesConverter.setAuthorityPrefix("ROLE_");
        authoritiesConverter.setAuthoritiesClaimName("realm_access.roles");

        JwtAuthenticationConverter authenticationConverter = new JwtAuthenticationConverter();
        authenticationConverter.setJwtGrantedAuthoritiesConverter(authoritiesConverter);
        authenticationConverter.setPrincipalClaimName("preferred_username");

        return authenticationConverter;
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList(allowedOrigins.split(",")));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(List.of("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
