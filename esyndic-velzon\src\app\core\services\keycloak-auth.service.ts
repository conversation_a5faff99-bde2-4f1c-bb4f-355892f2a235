import { Injectable } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import { KeycloakProfile } from 'keycloak-js';
import { Observable, BehaviorSubject, from } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

export interface UserProfile {
  id?: string;
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  roles?: string[];
  realmRoles?: string[];
  resourceRoles?: { [key: string]: string[] };
}

@Injectable({
  providedIn: 'root'
})
export class KeycloakAuthService {
  private userProfileSubject = new BehaviorSubject<UserProfile | null>(null);
  public userProfile$ = this.userProfileSubject.asObservable();

  constructor(private keycloakService: KeycloakService) {
    this.loadUserProfile();
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.keycloakService.isLoggedIn();
  }

  /**
   * Get current user profile
   */
  getCurrentUser(): UserProfile | null {
    return this.userProfileSubject.value;
  }

  /**
   * Load user profile from Keycloak
   */
  private async loadUserProfile(): Promise<void> {
    if (this.isAuthenticated()) {
      try {
        const keycloakProfile = await this.keycloakService.loadUserProfile();
        const userRoles = this.keycloakService.getUserRoles();
        const realmRoles = this.keycloakService.getUserRoles(false);
        const resourceRoles: string[] = []; // getResourceRoles not available in this version

        const userProfile: UserProfile = {
          id: keycloakProfile.id,
          username: keycloakProfile.username,
          email: keycloakProfile.email,
          firstName: keycloakProfile.firstName,
          lastName: keycloakProfile.lastName,
          fullName: `${keycloakProfile.firstName || ''} ${keycloakProfile.lastName || ''}`.trim(),
          roles: userRoles,
          realmRoles: realmRoles,
          resourceRoles: {} // Empty object for compatibility
        };

        this.userProfileSubject.next(userProfile);
      } catch (error) {
        console.error('Error loading user profile:', error);
        this.userProfileSubject.next(null);
      }
    }
  }

  /**
   * Login user
   */
  login(): void {
    this.keycloakService.login({
      redirectUri: window.location.origin + '/dashboard'
    });
  }

  /**
   * Logout user
   */
  logout(): void {
    this.keycloakService.logout(window.location.origin);
  }

  /**
   * Get access token
   */
  async getToken(): Promise<string | undefined> {
    return await this.keycloakService.getToken();
  }

  /**
   * Check if user has specific realm role
   */
  hasRealmRole(role: string): boolean {
    return this.keycloakService.isUserInRole(role);
  }

  /**
   * Check if user has specific resource role
   */
  hasResourceRole(role: string, resource?: string): boolean {
    // Resource roles not supported in this version, check realm roles instead
    return this.keycloakService.isUserInRole(role);
  }

  /**
   * Check if user has any of the specified roles
   */
  hasAnyRole(roles: string[], isResourceRole: boolean = false, resource?: string): boolean {
    return roles.some(role => 
      isResourceRole ? this.hasResourceRole(role, resource) : this.hasRealmRole(role)
    );
  }

  /**
   * Check if user has all of the specified roles
   */
  hasAllRoles(roles: string[], isResourceRole: boolean = false, resource?: string): boolean {
    return roles.every(role => 
      isResourceRole ? this.hasResourceRole(role, resource) : this.hasRealmRole(role)
    );
  }

  /**
   * Check if user is Super Admin
   */
  isSuperAdmin(): boolean {
    return this.hasRealmRole('SUPERADMIN');
  }

  /**
   * Check if user is Admin
   */
  isAdmin(): boolean {
    return this.hasRealmRole('ADMIN') || this.isSuperAdmin();
  }

  /**
   * Check if user is President
   */
  isPresident(): boolean {
    return this.hasRealmRole('PRESIDENT') || this.isAdmin();
  }

  /**
   * Check if user is Owner
   */
  isOwner(): boolean {
    return this.hasRealmRole('OWNER') || this.isPresident();
  }

  /**
   * Check if user is Resident
   */
  isResident(): boolean {
    return this.hasRealmRole('RESIDENT') || this.isOwner();
  }

  /**
   * Get user's highest role level
   */
  getUserRoleLevel(): string {
    if (this.isSuperAdmin()) return 'SUPERADMIN';
    if (this.isAdmin()) return 'ADMIN';
    if (this.isPresident()) return 'PRESIDENT';
    if (this.isOwner()) return 'OWNER';
    if (this.isResident()) return 'RESIDENT';
    return 'GUEST';
  }

  /**
   * Get navigation route based on user role
   */
  getDefaultRoute(): string {
    if (this.isSuperAdmin()) return '/admin/dashboard';
    if (this.isAdmin()) return '/admin/dashboard';
    if (this.isPresident()) return '/president/dashboard';
    if (this.isOwner()) return '/owner/dashboard';
    if (this.isResident()) return '/resident/dashboard';
    return '/dashboard';
  }

  /**
   * Refresh token
   */
  refreshToken(): Observable<boolean> {
    return from(this.keycloakService.updateToken(30)).pipe(
      map(() => true),
      catchError(() => {
        this.logout();
        return [false];
      })
    );
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(): boolean {
    return this.keycloakService.isTokenExpired();
  }
}
