{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Maldivian [dv]\n//! author : Jawish Hameed : https://github.com/jawish\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var months = ['ޖެނުއަރީ', 'ފެބްރުއަރީ', 'މާރިޗު', 'އޭޕްރީލު', 'މޭ', 'ޖޫން', 'ޖުލައި', 'އޯގަސްޓު', 'ސެޕްޓެމްބަރު', 'އޮކްޓޯބަރު', 'ނޮވެމްބަރު', 'ޑިސެމްބަރު'],\n    weekdays = ['އާދިއްތަ', 'ހޯމަ', 'އަންގާރަ', 'ބުދަ', 'ބުރާސްފަތި', 'ހުކުރު', 'ހޮނިހިރު'];\n  var dv = moment.defineLocale('dv', {\n    months: months,\n    monthsShort: months,\n    weekdays: weekdays,\n    weekdaysShort: weekdays,\n    weekdaysMin: 'އާދި_ހޯމަ_އަން_ބުދަ_ބުރާ_ހުކު_ހޮނި'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'D/M/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /މކ|މފ/,\n    isPM: function (input) {\n      return 'މފ' === input;\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'މކ';\n      } else {\n        return 'މފ';\n      }\n    },\n    calendar: {\n      sameDay: '[މިއަދު] LT',\n      nextDay: '[މާދަމާ] LT',\n      nextWeek: 'dddd LT',\n      lastDay: '[އިއްޔެ] LT',\n      lastWeek: '[ފާއިތުވި] dddd LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'ތެރޭގައި %s',\n      past: 'ކުރިން %s',\n      s: 'ސިކުންތުކޮޅެއް',\n      ss: 'd% ސިކުންތު',\n      m: 'މިނިޓެއް',\n      mm: 'މިނިޓު %d',\n      h: 'ގަޑިއިރެއް',\n      hh: 'ގަޑިއިރު %d',\n      d: 'ދުވަހެއް',\n      dd: 'ދުވަސް %d',\n      M: 'މަހެއް',\n      MM: 'މަސް %d',\n      y: 'އަހަރެއް',\n      yy: 'އަހަރު %d'\n    },\n    preparse: function (string) {\n      return string.replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/,/g, '،');\n    },\n    week: {\n      dow: 7,\n      // Sunday is the first day of the week.\n      doy: 12 // The week that contains Jan 12th is the first week of the year.\n    }\n  });\n  return dv;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "months", "weekdays", "dv", "defineLocale", "monthsShort", "weekdaysShort", "weekdaysMin", "split", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "meridiem", "hour", "minute", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "preparse", "string", "replace", "postformat", "week", "dow", "doy"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/moment/locale/dv.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Maldivian [dv]\n//! author : Jawish Hameed : https://github.com/jawish\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var months = [\n            'ޖެނުއަރީ',\n            'ފެބްރުއަރީ',\n            'މާރިޗު',\n            'އޭޕްރީލު',\n            'މޭ',\n            'ޖޫން',\n            'ޖުލައި',\n            'އޯގަސްޓު',\n            'ސެޕްޓެމްބަރު',\n            'އޮކްޓޯބަރު',\n            'ނޮވެމްބަރު',\n            'ޑިސެމްބަރު',\n        ],\n        weekdays = [\n            'އާދިއްތަ',\n            'ހޯމަ',\n            'އަންގާރަ',\n            'ބުދަ',\n            'ބުރާސްފަތި',\n            'ހުކުރު',\n            'ހޮނިހިރު',\n        ];\n\n    var dv = moment.defineLocale('dv', {\n        months: months,\n        monthsShort: months,\n        weekdays: weekdays,\n        weekdaysShort: weekdays,\n        weekdaysMin: 'އާދި_ހޯމަ_އަން_ބުދަ_ބުރާ_ހުކު_ހޮނި'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'D/M/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        meridiemParse: /މކ|މފ/,\n        isPM: function (input) {\n            return 'މފ' === input;\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 12) {\n                return 'މކ';\n            } else {\n                return 'މފ';\n            }\n        },\n        calendar: {\n            sameDay: '[މިއަދު] LT',\n            nextDay: '[މާދަމާ] LT',\n            nextWeek: 'dddd LT',\n            lastDay: '[އިއްޔެ] LT',\n            lastWeek: '[ފާއިތުވި] dddd LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'ތެރޭގައި %s',\n            past: 'ކުރިން %s',\n            s: 'ސިކުންތުކޮޅެއް',\n            ss: 'd% ސިކުންތު',\n            m: 'މިނިޓެއް',\n            mm: 'މިނިޓު %d',\n            h: 'ގަޑިއިރެއް',\n            hh: 'ގަޑިއިރު %d',\n            d: 'ދުވަހެއް',\n            dd: 'ދުވަސް %d',\n            M: 'މަހެއް',\n            MM: 'މަސް %d',\n            y: 'އަހަރެއް',\n            yy: 'އަހަރު %d',\n        },\n        preparse: function (string) {\n            return string.replace(/،/g, ',');\n        },\n        postformat: function (string) {\n            return string.replace(/,/g, '،');\n        },\n        week: {\n            dow: 7, // Sunday is the first day of the week.\n            doy: 12, // The week that contains Jan 12th is the first week of the year.\n        },\n    });\n\n    return dv;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,MAAM,GAAG,CACL,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,UAAU,EACV,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,YAAY,CACf;IACDC,QAAQ,GAAG,CACP,UAAU,EACV,MAAM,EACN,UAAU,EACV,MAAM,EACN,YAAY,EACZ,QAAQ,EACR,UAAU,CACb;EAEL,IAAIC,EAAE,GAAGH,MAAM,CAACI,YAAY,CAAC,IAAI,EAAE;IAC/BH,MAAM,EAAEA,MAAM;IACdI,WAAW,EAAEJ,MAAM;IACnBC,QAAQ,EAAEA,QAAQ;IAClBI,aAAa,EAAEJ,QAAQ;IACvBK,WAAW,EAAE,oCAAoC,CAACC,KAAK,CAAC,GAAG,CAAC;IAC5DC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,OAAO;IACtBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,IAAI,KAAKA,KAAK;IACzB,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACX,OAAO,IAAI;MACf,CAAC,MAAM;QACH,OAAO,IAAI;MACf;IACJ,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,oBAAoB;MAC9BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,aAAa;MACrBC,IAAI,EAAE,WAAW;MACjBC,CAAC,EAAE,gBAAgB;MACnBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACpC,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUF,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACpC,CAAC;IACDE,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,EAAE,CAAE;IACb;EACJ,CAAC,CAAC;EAEF,OAAOhD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}