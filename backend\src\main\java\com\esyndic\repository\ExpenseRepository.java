package com.esyndic.repository;

import com.esyndic.entity.Expense;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Repository
public interface ExpenseRepository extends JpaRepository<Expense, UUID> {

    List<Expense> findByBuildingId(UUID buildingId);

    List<Expense> findByStatus(Expense.ExpenseStatus status);

    List<Expense> findByBuildingIdAndStatus(UUID buildingId, Expense.ExpenseStatus status);

    List<Expense> findByCategory(String category);

    List<Expense> findByBuildingIdAndCategory(UUID buildingId, String category);

    List<Expense> findBySupplier(String supplier);

    List<Expense> findByCreatedById(UUID createdById);

    List<Expense> findByApprovedById(UUID approvedById);

    @Query("SELECT e FROM Expense e WHERE e.expenseDate >= :startDate AND e.expenseDate <= :endDate")
    List<Expense> findByExpenseDateBetween(@Param("startDate") LocalDate startDate, 
                                         @Param("endDate") LocalDate endDate);

    @Query("SELECT e FROM Expense e WHERE e.building.id = :buildingId AND " +
           "e.expenseDate >= :startDate AND e.expenseDate <= :endDate")
    List<Expense> findByBuildingIdAndExpenseDateBetween(@Param("buildingId") UUID buildingId,
                                                      @Param("startDate") LocalDate startDate,
                                                      @Param("endDate") LocalDate endDate);

    @Query("SELECT e FROM Expense e WHERE e.amount >= :minAmount AND e.amount <= :maxAmount")
    List<Expense> findByAmountBetween(@Param("minAmount") BigDecimal minAmount, 
                                    @Param("maxAmount") BigDecimal maxAmount);

    @Query("SELECT SUM(e.amount) FROM Expense e WHERE e.building.id = :buildingId AND e.status = 'APPROVED'")
    BigDecimal getTotalApprovedExpensesByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT SUM(e.amount) FROM Expense e WHERE e.building.id = :buildingId AND " +
           "e.status = 'APPROVED' AND e.expenseDate >= :startDate AND e.expenseDate <= :endDate")
    BigDecimal getTotalApprovedExpensesByBuildingIdAndDateRange(@Param("buildingId") UUID buildingId,
                                                              @Param("startDate") LocalDate startDate,
                                                              @Param("endDate") LocalDate endDate);

    @Query("SELECT SUM(e.amount) FROM Expense e WHERE e.building.id = :buildingId AND " +
           "e.category = :category AND e.status = 'APPROVED'")
    BigDecimal getTotalApprovedExpensesByBuildingIdAndCategory(@Param("buildingId") UUID buildingId,
                                                             @Param("category") String category);

    @Query("SELECT COUNT(e) FROM Expense e WHERE e.building.id = :buildingId")
    long countExpensesByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT COUNT(e) FROM Expense e WHERE e.building.id = :buildingId AND e.status = :status")
    long countExpensesByBuildingIdAndStatus(@Param("buildingId") UUID buildingId, 
                                          @Param("status") Expense.ExpenseStatus status);

    @Query("SELECT DISTINCT e.category FROM Expense e WHERE e.building.id = :buildingId")
    List<String> findDistinctCategoriesByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT DISTINCT e.supplier FROM Expense e WHERE e.building.id = :buildingId AND e.supplier IS NOT NULL")
    List<String> findDistinctSuppliersByBuildingId(@Param("buildingId") UUID buildingId);

    @Query("SELECT e FROM Expense e WHERE e.building.id = :buildingId ORDER BY e.expenseDate DESC")
    List<Expense> findByBuildingIdOrderByExpenseDateDesc(@Param("buildingId") UUID buildingId);

    @Query("SELECT e FROM Expense e WHERE e.description LIKE %:description%")
    List<Expense> findByDescriptionContaining(@Param("description") String description);

    @Query("SELECT e FROM Expense e WHERE e.building.id = :buildingId AND e.description LIKE %:description%")
    List<Expense> findByBuildingIdAndDescriptionContaining(@Param("buildingId") UUID buildingId, 
                                                         @Param("description") String description);

    @Query("SELECT e FROM Expense e WHERE e.invoiceNumber LIKE %:invoiceNumber%")
    List<Expense> findByInvoiceNumberContaining(@Param("invoiceNumber") String invoiceNumber);

    @Query("SELECT e FROM Expense e WHERE e.invoiceFilePath IS NOT NULL AND e.invoiceFilePath != ''")
    List<Expense> findExpensesWithInvoices();

    @Query("SELECT e FROM Expense e WHERE e.building.id = :buildingId AND " +
           "e.invoiceFilePath IS NOT NULL AND e.invoiceFilePath != ''")
    List<Expense> findExpensesWithInvoicesByBuildingId(@Param("buildingId") UUID buildingId);
}
