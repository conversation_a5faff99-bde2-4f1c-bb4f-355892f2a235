package com.esyndic.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "apartments", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"building_id", "apartment_number"})
})
public class Apartment {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "apartment_number", nullable = false, length = 20)
    @NotBlank(message = "Apartment number is required")
    @Size(max = 20, message = "Apartment number must not exceed 20 characters")
    private String apartmentNumber;

    @Column(name = "floor_number")
    private Integer floorNumber;

    @Column(name = "area_sqm", precision = 8, scale = 2)
    @DecimalMin(value = "0.0", inclusive = false, message = "Area must be greater than 0")
    private BigDecimal areaSqm;

    @Column(name = "monthly_dues", nullable = false, precision = 10, scale = 2)
    @NotNull(message = "Monthly dues is required")
    @DecimalMin(value = "0.0", message = "Monthly dues must be non-negative")
    private BigDecimal monthlyDues = BigDecimal.ZERO;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "building_id", nullable = false)
    @NotNull(message = "Building is required")
    private Building building;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "owner_id")
    private User owner;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "resident_id")
    private User resident;

    @OneToMany(mappedBy = "apartment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Payment> payments;

    @OneToMany(mappedBy = "apartment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Claim> claims;

    @OneToMany(mappedBy = "apartment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<AssemblyAttendance> assemblyAttendances;

    @OneToMany(mappedBy = "apartment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<AssemblyVote> assemblyVotes;

    // Constructors
    public Apartment() {}

    public Apartment(String apartmentNumber, Building building, BigDecimal monthlyDues) {
        this.apartmentNumber = apartmentNumber;
        this.building = building;
        this.monthlyDues = monthlyDues;
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getApartmentNumber() {
        return apartmentNumber;
    }

    public void setApartmentNumber(String apartmentNumber) {
        this.apartmentNumber = apartmentNumber;
    }

    public Integer getFloorNumber() {
        return floorNumber;
    }

    public void setFloorNumber(Integer floorNumber) {
        this.floorNumber = floorNumber;
    }

    public BigDecimal getAreaSqm() {
        return areaSqm;
    }

    public void setAreaSqm(BigDecimal areaSqm) {
        this.areaSqm = areaSqm;
    }

    public BigDecimal getMonthlyDues() {
        return monthlyDues;
    }

    public void setMonthlyDues(BigDecimal monthlyDues) {
        this.monthlyDues = monthlyDues;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Building getBuilding() {
        return building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }

    public User getOwner() {
        return owner;
    }

    public void setOwner(User owner) {
        this.owner = owner;
    }

    public User getResident() {
        return resident;
    }

    public void setResident(User resident) {
        this.resident = resident;
    }

    public List<Payment> getPayments() {
        return payments;
    }

    public void setPayments(List<Payment> payments) {
        this.payments = payments;
    }

    public List<Claim> getClaims() {
        return claims;
    }

    public void setClaims(List<Claim> claims) {
        this.claims = claims;
    }

    public List<AssemblyAttendance> getAssemblyAttendances() {
        return assemblyAttendances;
    }

    public void setAssemblyAttendances(List<AssemblyAttendance> assemblyAttendances) {
        this.assemblyAttendances = assemblyAttendances;
    }

    public List<AssemblyVote> getAssemblyVotes() {
        return assemblyVotes;
    }

    public void setAssemblyVotes(List<AssemblyVote> assemblyVotes) {
        this.assemblyVotes = assemblyVotes;
    }

    // Helper methods
    public String getDisplayName() {
        return "Apt " + apartmentNumber + (floorNumber != null ? " (Floor " + floorNumber + ")" : "");
    }

    public String getFullAddress() {
        return getDisplayName() + ", " + (building != null ? building.getFullAddress() : "");
    }

    @Override
    public String toString() {
        return "Apartment{" +
                "id=" + id +
                ", apartmentNumber='" + apartmentNumber + '\'' +
                ", floorNumber=" + floorNumber +
                ", monthlyDues=" + monthlyDues +
                ", isActive=" + isActive +
                '}';
    }
}
