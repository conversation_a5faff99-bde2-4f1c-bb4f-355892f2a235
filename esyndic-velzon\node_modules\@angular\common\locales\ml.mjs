/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ml", [["AM", "PM"], u, u], u, [["ഞ", "തി", "ചൊ", "ബു", "വ്യാ", "വെ", "ശ"], ["ഞായർ", "തിങ്കൾ", "ചൊവ്വ", "ബുധൻ", "വ്യാഴം", "വെള്ളി", "ശനി"], ["ഞായറാഴ്‌ച", "തിങ്കളാഴ്‌ച", "ചൊവ്വാഴ്ച", "ബുധനാഴ്‌ച", "വ്യാഴാഴ്‌ച", "വെള്ളിയാഴ്‌ച", "ശനിയാഴ്‌ച"], ["ഞാ", "തി", "ചൊ", "ബു", "വ്യാ", "വെ", "ശ"]], [["ഞാ", "തി", "ചൊ", "ബു", "വ്യാ", "വെ", "ശ"], ["ഞായർ", "തിങ്കൾ", "ചൊവ്വ", "ബുധൻ", "വ്യാഴം", "വെള്ളി", "ശനി"], ["ഞായറാഴ്‌ച", "തിങ്കളാഴ്‌ച", "ചൊവ്വാഴ്‌ച", "ബുധനാഴ്‌ച", "വ്യാഴാഴ്‌ച", "വെള്ളിയാഴ്‌ച", "ശനിയാഴ്‌ച"], ["ഞാ", "തി", "ചൊ", "ബു", "വ്യാ", "വെ", "ശ"]], [["ജ", "ഫെ", "മാ", "ഏ", "മെ", "ജൂൺ", "ജൂ", "ഓ", "സെ", "ഒ", "ന", "ഡി"], ["ജനു", "ഫെബ്രു", "മാർ", "ഏപ്രി", "മേയ്", "ജൂൺ", "ജൂലൈ", "ഓഗ", "സെപ്റ്റം", "ഒക്ടോ", "നവം", "ഡിസം"], ["ജനുവരി", "ഫെബ്രുവരി", "മാർച്ച്", "ഏപ്രിൽ", "മേയ്", "ജൂൺ", "ജൂലൈ", "ഓഗസ്റ്റ്", "സെപ്റ്റംബർ", "ഒക്‌ടോബർ", "നവംബർ", "ഡിസംബർ"]], u, [["ക്രി.മു.", "എഡി"], u, ["ക്രിസ്‌തുവിന് മുമ്പ്", "ആന്നോ ഡൊമിനി"]], 0, [0, 0], ["d/M/yy", "y, MMM d", "y, MMMM d", "y, MMMM d, EEEE"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "INR", "₹", "ഇന്ത്യൻ രൂപ", { "BYN": [u, "р."], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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