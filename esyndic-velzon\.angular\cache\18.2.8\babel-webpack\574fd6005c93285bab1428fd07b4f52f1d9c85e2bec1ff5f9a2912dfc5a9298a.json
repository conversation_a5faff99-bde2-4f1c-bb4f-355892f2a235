{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../core/services/authfake.service\";\nimport * as i5 from \"./toast-service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"./toasts-container.component\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c1 = (a0, a1) => ({\n  \"mdi-eye-off-outline\": a0,\n  \"mdi-eye-outline\": a1\n});\nfunction LoginComponent_div_31_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_31_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Email must be a valid email address\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵtemplate(1, LoginComponent_div_31_div_1_Template, 2, 0, \"div\", 53)(2, LoginComponent_div_31_div_2_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"email\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"email\"].errors[\"email\"]);\n  }\n}\nfunction LoginComponent_div_42_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵtemplate(1, LoginComponent_div_42_span_1_Template, 2, 0, \"span\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"required\"]);\n  }\n}\nfunction LoginComponent_span_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 54);\n  }\n}\n/**\n * Login Component\n */\nexport class LoginComponent {\n  constructor(formBuilder, authenticationService, router, authFackservice, route, toastService) {\n    this.formBuilder = formBuilder;\n    this.authenticationService = authenticationService;\n    this.router = router;\n    this.authFackservice = authFackservice;\n    this.route = route;\n    this.toastService = toastService;\n    this.submitted = false;\n    this.error = '';\n    // set the current year\n    this.year = new Date().getFullYear();\n    // redirect to home if already logged in\n    if (this.authenticationService.currentUserValue) {\n      this.router.navigate(['/']);\n    }\n  }\n  ngOnInit() {\n    if (sessionStorage.getItem('currentUser')) {\n      this.router.navigate(['/']);\n    }\n    /**\n     * Form Validation\n     */\n    this.loginForm = this.formBuilder.group({\n      email: ['<EMAIL>', [Validators.required, Validators.email]],\n      password: ['admin123', [Validators.required]],\n      apartmentCode: [''] // Optional field for owners/residents\n    });\n    // get return url from route parameters or default to '/'\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\n  }\n  // convenience getter for easy access to form fields\n  get f() {\n    return this.loginForm.controls;\n  }\n  /**\n   * Form submit\n   */\n  onSubmit() {\n    this.submitted = true;\n    // stop here if form is invalid\n    if (this.loginForm.invalid) {\n      return;\n    }\n    // Login Api with optional apartment code\n    const email = this.f['email'].value;\n    const password = this.f['password'].value;\n    const apartmentCode = this.f['apartmentCode'].value;\n    this.authenticationService.login(email, password, apartmentCode).subscribe(data => {\n      if (data.status == 'success') {\n        sessionStorage.setItem('toast', 'true');\n        this.toastService.show('Login successful! Welcome to e-Syndic.', {\n          classname: 'bg-success text-white',\n          delay: 5000\n        });\n        this.router.navigate([this.returnUrl]);\n      } else {\n        this.toastService.show(data.data, {\n          classname: 'bg-danger text-white',\n          delay: 15000\n        });\n      }\n    });\n    // stop here if form is invalid\n    // if (this.loginForm.invalid) {\n    //   return;\n    // } else {\n    //   if (environment.defaultauth === 'firebase') {\n    //     this.authenticationService.login(this.f['email'].value, this.f['password'].value).then((res: any) => {\n    //       this.router.navigate(['/']);\n    //     })\n    //       .catch(error => {\n    //         this.error = error ? error : '';\n    //       });\n    //   } else {\n    //     this.authFackservice.login(this.f['email'].value, this.f['password'].value).pipe(first()).subscribe(data => {\n    //           this.router.navigate(['/']);\n    //         },\n    //         error => {\n    //           this.error = error ? error : '';\n    //         });\n    //   }\n    // }\n  }\n  /**\n   * Password Hide/Show\n   */\n  toggleFieldTextType() {\n    this.fieldTextType = !this.fieldTextType;\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.UntypedFormBuilder), i0.ɵɵdirectiveInject(i2.AuthenticationService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.AuthfakeauthenticationService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i5.ToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 85,\n      vars: 17,\n      consts: [[1, \"auth-page-wrapper\", \"pt-5\"], [\"id\", \"auth-particles\", 1, \"auth-one-bg-position\", \"auth-one-bg\"], [1, \"bg-overlay\"], [1, \"shape\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"version\", \"1.1\", 0, \"xmlns\", \"xlink\", \"http://www.w3.org/1999/xlink\", \"viewBox\", \"0 0 1440 120\"], [\"d\", \"M 0,36 C 144,53.6 432,123.2 720,124 C 1008,124.8 1296,56.8 1440,40L1440 140L0 140z\"], [1, \"auth-page-content\"], [1, \"container\"], [1, \"row\"], [1, \"col-lg-12\"], [1, \"text-center\", \"mt-sm-5\", \"mb-4\", \"text-white-50\"], [\"routerLink\", \"/\", 1, \"d-inline-block\", \"auth-logo\"], [\"src\", \"assets/images/logo-light.png\", \"alt\", \"\", \"height\", \"20\"], [1, \"mt-3\", \"fs-15\", \"fw-medium\"], [1, \"row\", \"justify-content-center\"], [1, \"col-md-8\", \"col-lg-6\", \"col-xl-5\"], [1, \"card\", \"mt-4\"], [1, \"card-body\", \"p-4\"], [1, \"text-center\", \"mt-2\"], [1, \"text-primary\"], [1, \"text-muted\"], [1, \"p-2\", \"mt-4\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"mb-3\"], [\"for\", \"username\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter email\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"float-end\"], [\"routerLink\", \"/pass-reset/basic\", 1, \"text-muted\"], [\"for\", \"password-input\", 1, \"form-label\"], [1, \"position-relative\", \"auth-pass-inputgroup\", \"mb-3\"], [\"placeholder\", \"Enter password\", \"id\", \"password-input\", \"formControlName\", \"password\", 1, \"form-control\", \"pe-5\", 3, \"type\", \"ngClass\"], [\"type\", \"button\", \"id\", \"password-addon\", 1, \"btn\", \"btn-link\", \"position-absolute\", \"end-0\", \"top-0\", \"text-decoration-none\", \"text-muted\"], [1, \"mdi\", \"align-middle\", 3, \"click\", \"ngClass\"], [\"for\", \"apartment-code\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"apartment-code\", \"formControlName\", \"apartmentCode\", \"placeholder\", \"Ex: APT001\", 1, \"form-control\"], [1, \"form-text\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"value\", \"\", \"id\", \"auth-remember-check\", 1, \"form-check-input\"], [\"for\", \"auth-remember-check\", 1, \"form-check-label\"], [1, \"mt-4\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\", \"w-100\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"mt-4\", \"text-center\"], [1, \"alert\", \"alert-info\"], [1, \"mb-2\"], [1, \"mb-0\"], [\"href\", \"mailto:<EMAIL>\", 1, \"fw-semibold\", \"text-primary\", \"text-decoration-underline\"], [1, \"footer\"], [1, \"text-center\"], [1, \"mb-0\", \"text-muted\"], [\"aria-live\", \"polite\", \"aria-atomic\", \"true\"], [1, \"invalid-feedback\"], [4, \"ngIf\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(4, \"svg\", 4);\n          i0.ɵɵelement(5, \"path\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9)(10, \"div\", 10)(11, \"div\")(12, \"a\", 11);\n          i0.ɵɵelement(13, \"img\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"p\", 13);\n          i0.ɵɵtext(15, \"e-Syndic - Gestion de Copropri\\u00E9t\\u00E9\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(16, \"div\", 14)(17, \"div\", 15)(18, \"div\", 16)(19, \"div\", 17)(20, \"div\", 18)(21, \"h5\", 19);\n          i0.ɵɵtext(22, \"Bienvenue sur e-Syndic !\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 20);\n          i0.ɵɵtext(24, \"Connectez-vous pour acc\\u00E9der \\u00E0 votre espace de gestion.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 21)(26, \"form\", 22);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_26_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(27, \"div\", 23)(28, \"label\", 24);\n          i0.ɵɵtext(29, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"input\", 25);\n          i0.ɵɵtemplate(31, LoginComponent_div_31_Template, 3, 2, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 23)(33, \"div\", 27)(34, \"a\", 28);\n          i0.ɵɵtext(35, \"Forgot password?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"label\", 29);\n          i0.ɵɵtext(37, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 30);\n          i0.ɵɵelement(39, \"input\", 31);\n          i0.ɵɵelementStart(40, \"button\", 32)(41, \"i\", 33);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_i_click_41_listener() {\n            return ctx.toggleFieldTextType();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(42, LoginComponent_div_42_Template, 2, 1, \"div\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 23)(44, \"label\", 34);\n          i0.ɵɵtext(45, \"Code Appartement \");\n          i0.ɵɵelementStart(46, \"small\", 20);\n          i0.ɵɵtext(47, \"(Optionnel - pour propri\\u00E9taires/r\\u00E9sidents)\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(48, \"input\", 35);\n          i0.ɵɵelementStart(49, \"div\", 36)(50, \"small\", 20);\n          i0.ɵɵtext(51, \"Laissez vide si vous \\u00EAtes administrateur ou super-administrateur\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(52, \"div\", 37);\n          i0.ɵɵelement(53, \"input\", 38);\n          i0.ɵɵelementStart(54, \"label\", 39);\n          i0.ɵɵtext(55, \"Se souvenir de moi\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"div\", 40)(57, \"button\", 41);\n          i0.ɵɵtemplate(58, LoginComponent_span_58_Template, 1, 0, \"span\", 42);\n          i0.ɵɵtext(59, \" Se connecter \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 43)(61, \"div\", 44)(62, \"h6\", 45);\n          i0.ɵɵtext(63, \"Comptes de test :\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"small\")(65, \"strong\");\n          i0.ɵɵtext(66, \"Admin:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(67, \" <EMAIL> / admin123\");\n          i0.ɵɵelement(68, \"br\");\n          i0.ɵɵelementStart(69, \"strong\");\n          i0.ɵɵtext(70, \"Propri\\u00E9taire:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(71, \" <EMAIL> / password123 + APT001 \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(72, \"div\", 43)(73, \"p\", 46);\n          i0.ɵɵtext(74, \"Besoin d'aide ? \");\n          i0.ɵɵelementStart(75, \"a\", 47);\n          i0.ɵɵtext(76, \"Contactez le support\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(77, \"footer\", 48)(78, \"div\", 7)(79, \"div\", 8)(80, \"div\", 9)(81, \"div\", 49)(82, \"p\", 50);\n          i0.ɵɵtext(83);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelement(84, \"app-toasts\", 51);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx.submitted && ctx.f[\"email\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"email\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"type\", ctx.fieldTextType ? \"text\" : \"password\")(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx.submitted && ctx.f[\"password\"].errors));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(14, _c1, !ctx.fieldTextType, ctx.fieldTextType));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"password\"].errors);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"disabled\", ctx.submitted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted);\n          i0.ɵɵadvance(25);\n          i0.ɵɵtextInterpolate1(\"\\u00A9 \", ctx.year, \" e-Syndic. Syst\\u00E8me de gestion de copropri\\u00E9t\\u00E9\");\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i7.ToastsContainer],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "LoginComponent_div_31_div_1_Template", "LoginComponent_div_31_div_2_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "f", "errors", "LoginComponent_div_42_span_1_Template", "ɵɵelement", "LoginComponent", "constructor", "formBuilder", "authenticationService", "router", "authFackservice", "route", "toastService", "submitted", "error", "year", "Date", "getFullYear", "currentUserValue", "navigate", "ngOnInit", "sessionStorage", "getItem", "loginForm", "group", "email", "required", "password", "apartmentCode", "returnUrl", "snapshot", "queryParams", "controls", "onSubmit", "invalid", "value", "login", "subscribe", "data", "status", "setItem", "show", "classname", "delay", "toggleFieldTextType", "fieldTextType", "ɵɵdirectiveInject", "i1", "UntypedFormBuilder", "i2", "AuthenticationService", "i3", "Router", "i4", "AuthfakeauthenticationService", "ActivatedRoute", "i5", "ToastService", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_26_listener", "LoginComponent_div_31_Template", "LoginComponent_Template_i_click_41_listener", "LoginComponent_div_42_Template", "LoginComponent_span_58_Template", "ɵɵpureFunction1", "_c0", "ɵɵpureFunction2", "_c1", "ɵɵtextInterpolate1"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\account\\login\\login.component.ts", "C:\\e-syndic\\esyndic-velzon\\src\\app\\account\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\n\r\n// Login Auth\r\nimport { environment } from '../../../environments/environment';\r\nimport { AuthenticationService } from '../../core/services/auth.service';\r\nimport { AuthfakeauthenticationService } from '../../core/services/authfake.service';\r\nimport { first } from 'rxjs/operators';\r\nimport { ToastService } from './toast-service';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.scss']\r\n})\r\n\r\n/**\r\n * Login Component\r\n */\r\nexport class LoginComponent implements OnInit {\r\n\r\n  // Login Form\r\n  loginForm!: UntypedFormGroup;\r\n  submitted = false;\r\n  fieldTextType!: boolean;\r\n  error = '';\r\n  returnUrl!: string;\r\n  // set the current year\r\n  year: number = new Date().getFullYear();\r\n\r\n  constructor(private formBuilder: UntypedFormBuilder, private authenticationService: AuthenticationService, private router: Router,\r\n    private authFackservice: AuthfakeauthenticationService, private route: ActivatedRoute, public toastService: ToastService) {\r\n    // redirect to home if already logged in\r\n    if (this.authenticationService.currentUserValue) {\r\n      this.router.navigate(['/']);\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    if (sessionStorage.getItem('currentUser')) {\r\n      this.router.navigate(['/']);\r\n    }\r\n    /**\r\n     * Form Validation\r\n     */\r\n    this.loginForm = this.formBuilder.group({\r\n      email: ['<EMAIL>', [Validators.required, Validators.email]],\r\n      password: ['admin123', [Validators.required]],\r\n      apartmentCode: [''] // Optional field for owners/residents\r\n    });\r\n    // get return url from route parameters or default to '/'\r\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\r\n  }\r\n\r\n  // convenience getter for easy access to form fields\r\n  get f() { return this.loginForm.controls; }\r\n\r\n  /**\r\n   * Form submit\r\n   */\r\n  onSubmit() {\r\n    this.submitted = true;\r\n\r\n    // stop here if form is invalid\r\n    if (this.loginForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    // Login Api with optional apartment code\r\n    const email = this.f['email'].value;\r\n    const password = this.f['password'].value;\r\n    const apartmentCode = this.f['apartmentCode'].value;\r\n\r\n    this.authenticationService.login(email, password, apartmentCode).subscribe((data: any) => {\r\n      if (data.status == 'success') {\r\n        sessionStorage.setItem('toast', 'true');\r\n        this.toastService.show('Login successful! Welcome to e-Syndic.', { classname: 'bg-success text-white', delay: 5000 });\r\n        this.router.navigate([this.returnUrl]);\r\n      } else {\r\n        this.toastService.show(data.data, { classname: 'bg-danger text-white', delay: 15000 });\r\n      }\r\n    });\r\n\r\n    // stop here if form is invalid\r\n    // if (this.loginForm.invalid) {\r\n    //   return;\r\n    // } else {\r\n    //   if (environment.defaultauth === 'firebase') {\r\n    //     this.authenticationService.login(this.f['email'].value, this.f['password'].value).then((res: any) => {\r\n    //       this.router.navigate(['/']);\r\n    //     })\r\n    //       .catch(error => {\r\n    //         this.error = error ? error : '';\r\n    //       });\r\n    //   } else {\r\n    //     this.authFackservice.login(this.f['email'].value, this.f['password'].value).pipe(first()).subscribe(data => {\r\n    //           this.router.navigate(['/']);\r\n    //         },\r\n    //         error => {\r\n    //           this.error = error ? error : '';\r\n    //         });\r\n    //   }\r\n    // }\r\n  }\r\n\r\n  /**\r\n   * Password Hide/Show\r\n   */\r\n  toggleFieldTextType() {\r\n    this.fieldTextType = !this.fieldTextType;\r\n  }\r\n\r\n}\r\n", "<div class=\"auth-page-wrapper pt-5\">\r\n    <!-- auth page bg -->\r\n    <div class=\"auth-one-bg-position auth-one-bg\" id=\"auth-particles\">\r\n        <div class=\"bg-overlay\"></div>\r\n\r\n        <div class=\"shape\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" viewBox=\"0 0 1440 120\">\r\n                <path d=\"M 0,36 C 144,53.6 432,123.2 720,124 C 1008,124.8 1296,56.8 1440,40L1440 140L0 140z\"></path>\r\n            </svg>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- auth page content -->\r\n    <div class=\"auth-page-content\">\r\n        <div class=\"container\">\r\n            <div class=\"row\">\r\n                <div class=\"col-lg-12\">\r\n                    <div class=\"text-center mt-sm-5 mb-4 text-white-50\">\r\n                        <div>\r\n                            <a routerLink=\"/\" class=\"d-inline-block auth-logo\">\r\n                                <img src=\"assets/images/logo-light.png\" alt=\"\" height=\"20\">\r\n                            </a>\r\n                        </div>\r\n                        <p class=\"mt-3 fs-15 fw-medium\">e-Syndic - Gestion de Copropriété</p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <!-- end row -->\r\n\r\n            <div class=\"row justify-content-center\">\r\n                <div class=\"col-md-8 col-lg-6 col-xl-5\">\r\n                    <div class=\"card mt-4\">\r\n\r\n                        <div class=\"card-body p-4\">\r\n                            <div class=\"text-center mt-2\">\r\n                                <h5 class=\"text-primary\">Bienvenue sur e-Syndic !</h5>\r\n                                <p class=\"text-muted\">Connectez-vous pour accéder à votre espace de gestion.</p>\r\n                            </div>\r\n                            <div class=\"p-2 mt-4\">\r\n                                <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\r\n\r\n                                    <div class=\"mb-3\">\r\n                                        <label class=\"form-label\" for=\"username\">Email</label>\r\n                                        <input type=\"email\" class=\"form-control\" id=\"email\" formControlName=\"email\" [ngClass]=\"{ 'is-invalid': submitted && f['email'].errors }\" placeholder=\"Enter email\">\r\n                                        <div *ngIf=\"submitted && f['email'].errors\" class=\"invalid-feedback\">\r\n                                            <div *ngIf=\"f['email'].errors['required']\">Email is required</div>\r\n                                            <div *ngIf=\"f['email'].errors['email']\">Email must be a valid email address</div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div class=\"mb-3\">\r\n                                        <div class=\"float-end\">\r\n                                            <a routerLink=\"/pass-reset/basic\" class=\"text-muted\">Forgot password?</a>\r\n                                        </div>\r\n                                        <label class=\"form-label\" for=\"password-input\">Password</label>\r\n                                        <div class=\"position-relative auth-pass-inputgroup mb-3\">\r\n                                            <input [type]=\"fieldTextType ? 'text' : 'password'\" class=\"form-control pe-5\" placeholder=\"Enter password\" id=\"password-input\" formControlName=\"password\" [ngClass]=\"{ 'is-invalid': submitted && f['password'].errors }\">\r\n                                            <button class=\"btn btn-link position-absolute end-0 top-0 text-decoration-none text-muted\" type=\"button\" id=\"password-addon\"><i class=\"mdi align-middle\" [ngClass]=\"{'mdi-eye-off-outline': !fieldTextType, 'mdi-eye-outline': fieldTextType\r\n                                          }\" (click)=\"toggleFieldTextType()\"></i></button>\r\n                                            <div *ngIf=\"submitted && f['password'].errors\" class=\"invalid-feedback\">\r\n                                                <span *ngIf=\"f['password'].errors['required']\">Password is required</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div class=\"mb-3\">\r\n                                        <label class=\"form-label\" for=\"apartment-code\">Code Appartement <small class=\"text-muted\">(Optionnel - pour propriétaires/résidents)</small></label>\r\n                                        <input type=\"text\" class=\"form-control\" id=\"apartment-code\" formControlName=\"apartmentCode\" placeholder=\"Ex: APT001\">\r\n                                        <div class=\"form-text\">\r\n                                            <small class=\"text-muted\">Laissez vide si vous êtes administrateur ou super-administrateur</small>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div class=\"form-check\">\r\n                                        <input class=\"form-check-input\" type=\"checkbox\" value=\"\" id=\"auth-remember-check\">\r\n                                        <label class=\"form-check-label\" for=\"auth-remember-check\">Se souvenir de moi</label>\r\n                                    </div>\r\n\r\n                                    <div class=\"mt-4\">\r\n                                        <button class=\"btn btn-success w-100\" type=\"submit\" [disabled]=\"submitted\">\r\n                                            <span *ngIf=\"submitted\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                                            Se connecter\r\n                                        </button>\r\n                                    </div>\r\n\r\n                                    <div class=\"mt-4 text-center\">\r\n                                        <div class=\"alert alert-info\">\r\n                                            <h6 class=\"mb-2\">Comptes de test :</h6>\r\n                                            <small>\r\n                                                <strong>Admin:</strong> admin&#64;esyndic.com / admin123<br>\r\n                                                <strong>Propriétaire:</strong> jean.dupont&#64;email.com / password123 + APT001\r\n                                            </small>\r\n                                        </div>\r\n                                    </div>\r\n                                </form>\r\n                            </div>\r\n                        </div>\r\n                        <!-- end card body -->\r\n                    </div>\r\n                    <!-- end card -->\r\n\r\n                    <div class=\"mt-4 text-center\">\r\n                        <p class=\"mb-0\">Besoin d'aide ? <a href=\"mailto:support&#64;esyndic.com\" class=\"fw-semibold text-primary text-decoration-underline\">Contactez le support</a></p>\r\n                    </div>\r\n\r\n                </div>\r\n            </div>\r\n            <!-- end row -->\r\n        </div>\r\n        <!-- end container -->\r\n    </div>\r\n    <!-- end auth page content -->\r\n\r\n    <!-- footer -->\r\n    <footer class=\"footer\">\r\n        <div class=\"container\">\r\n            <div class=\"row\">\r\n                <div class=\"col-lg-12\">\r\n                    <div class=\"text-center\">\r\n                        <p class=\"mb-0 text-muted\">&copy; {{year}} e-Syndic. Système de gestion de copropriété</p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </footer>\r\n    <!-- end Footer -->\r\n</div>\r\n<!-- end auth-page-wrapper -->\r\n<app-toasts aria-live=\"polite\" aria-atomic=\"true\"></app-toasts>"], "mappings": "AACA,SAA+CA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;IC4CrCC,EAAA,CAAAC,cAAA,UAA2C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAClEH,EAAA,CAAAC,cAAA,UAAwC;IAAAD,EAAA,CAAAE,MAAA,0CAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFrFH,EAAA,CAAAC,cAAA,cAAqE;IAEjED,EADA,CAAAI,UAAA,IAAAC,oCAAA,kBAA2C,IAAAC,oCAAA,kBACH;IAC5CN,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,CAAA,UAAAC,MAAA,aAAmC;IACnCX,EAAA,CAAAO,SAAA,EAAgC;IAAhCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,CAAA,UAAAC,MAAA,UAAgC;;;;;IAclCX,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAD9EH,EAAA,CAAAC,cAAA,cAAwE;IACpED,EAAA,CAAAI,UAAA,IAAAQ,qCAAA,mBAA+C;IACnDZ,EAAA,CAAAG,YAAA,EAAM;;;;IADKH,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,aAAsC;;;;;IAoBjDX,EAAA,CAAAa,SAAA,eAA8G;;;AD/D1J;;;AAGA,OAAM,MAAOC,cAAc;EAWzBC,YAAoBC,WAA+B,EAAUC,qBAA4C,EAAUC,MAAc,EACvHC,eAA8C,EAAUC,KAAqB,EAASC,YAA0B;IADtG,KAAAL,WAAW,GAAXA,WAAW;IAA8B,KAAAC,qBAAqB,GAArBA,qBAAqB;IAAiC,KAAAC,MAAM,GAANA,MAAM;IAC/G,KAAAC,eAAe,GAAfA,eAAe;IAAyC,KAAAC,KAAK,GAALA,KAAK;IAAyB,KAAAC,YAAY,GAAZA,YAAY;IAR5G,KAAAC,SAAS,GAAG,KAAK;IAEjB,KAAAC,KAAK,GAAG,EAAE;IAEV;IACA,KAAAC,IAAI,GAAW,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IAIrC;IACA,IAAI,IAAI,CAACT,qBAAqB,CAACU,gBAAgB,EAAE;MAC/C,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7B;EACF;EAEAC,QAAQA,CAAA;IACN,IAAIC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC,EAAE;MACzC,IAAI,CAACb,MAAM,CAACU,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7B;IACA;;;IAGA,IAAI,CAACI,SAAS,GAAG,IAAI,CAAChB,WAAW,CAACiB,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,mBAAmB,EAAE,CAACnC,UAAU,CAACoC,QAAQ,EAAEpC,UAAU,CAACmC,KAAK,CAAC,CAAC;MACrEE,QAAQ,EAAE,CAAC,UAAU,EAAE,CAACrC,UAAU,CAACoC,QAAQ,CAAC,CAAC;MAC7CE,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC;KACrB,CAAC;IACF;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAAClB,KAAK,CAACmB,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,GAAG;EACtE;EAEA;EACA,IAAI9B,CAACA,CAAA;IAAK,OAAO,IAAI,CAACsB,SAAS,CAACS,QAAQ;EAAE;EAE1C;;;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACpB,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,IAAI,CAACU,SAAS,CAACW,OAAO,EAAE;MAC1B;IACF;IAEA;IACA,MAAMT,KAAK,GAAG,IAAI,CAACxB,CAAC,CAAC,OAAO,CAAC,CAACkC,KAAK;IACnC,MAAMR,QAAQ,GAAG,IAAI,CAAC1B,CAAC,CAAC,UAAU,CAAC,CAACkC,KAAK;IACzC,MAAMP,aAAa,GAAG,IAAI,CAAC3B,CAAC,CAAC,eAAe,CAAC,CAACkC,KAAK;IAEnD,IAAI,CAAC3B,qBAAqB,CAAC4B,KAAK,CAACX,KAAK,EAAEE,QAAQ,EAAEC,aAAa,CAAC,CAACS,SAAS,CAAEC,IAAS,IAAI;MACvF,IAAIA,IAAI,CAACC,MAAM,IAAI,SAAS,EAAE;QAC5BlB,cAAc,CAACmB,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;QACvC,IAAI,CAAC5B,YAAY,CAAC6B,IAAI,CAAC,wCAAwC,EAAE;UAAEC,SAAS,EAAE,uBAAuB;UAAEC,KAAK,EAAE;QAAI,CAAE,CAAC;QACrH,IAAI,CAAClC,MAAM,CAACU,QAAQ,CAAC,CAAC,IAAI,CAACU,SAAS,CAAC,CAAC;MACxC,CAAC,MAAM;QACL,IAAI,CAACjB,YAAY,CAAC6B,IAAI,CAACH,IAAI,CAACA,IAAI,EAAE;UAAEI,SAAS,EAAE,sBAAsB;UAAEC,KAAK,EAAE;QAAK,CAAE,CAAC;MACxF;IACF,CAAC,CAAC;IAEF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEA;;;EAGAC,mBAAmBA,CAAA;IACjB,IAAI,CAACC,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;;;uBA3FWxC,cAAc,EAAAd,EAAA,CAAAuD,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAzD,EAAA,CAAAuD,iBAAA,CAAAG,EAAA,CAAAC,qBAAA,GAAA3D,EAAA,CAAAuD,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA7D,EAAA,CAAAuD,iBAAA,CAAAO,EAAA,CAAAC,6BAAA,GAAA/D,EAAA,CAAAuD,iBAAA,CAAAK,EAAA,CAAAI,cAAA,GAAAhE,EAAA,CAAAuD,iBAAA,CAAAU,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAdpD,cAAc;MAAAqD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBvBzE,EAFJ,CAAAC,cAAA,aAAoC,aAEkC;UAC9DD,EAAA,CAAAa,SAAA,aAA8B;UAE9Bb,EAAA,CAAAC,cAAA,aAAmB;;UACfD,EAAA,CAAAC,cAAA,aAAwH;UACpHD,EAAA,CAAAa,SAAA,cAAoG;UAGhHb,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;UASkBH,EANxB,CAAAC,cAAA,aAA+B,aACJ,aACF,aACU,eACiC,WAC3C,aACkD;UAC/CD,EAAA,CAAAa,SAAA,eAA2D;UAEnEb,EADI,CAAAG,YAAA,EAAI,EACF;UACNH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAE,MAAA,mDAAiC;UAG7EF,EAH6E,CAAAG,YAAA,EAAI,EACnE,EACJ,EACJ;UAScH,EANpB,CAAAC,cAAA,eAAwC,eACI,eACb,eAEQ,eACO,cACD;UAAAD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtDH,EAAA,CAAAC,cAAA,aAAsB;UAAAD,EAAA,CAAAE,MAAA,wEAAsD;UAChFF,EADgF,CAAAG,YAAA,EAAI,EAC9E;UAEFH,EADJ,CAAAC,cAAA,eAAsB,gBACoC;UAAxBD,EAAA,CAAA2E,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAAhC,QAAA,EAAU;UAAA,EAAC;UAG7C1C,EADJ,CAAAC,cAAA,eAAkB,iBAC2B;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAa,SAAA,iBAAmK;UACnKb,EAAA,CAAAI,UAAA,KAAAyE,8BAAA,kBAAqE;UAIzE7E,EAAA,CAAAG,YAAA,EAAM;UAIEH,EAFR,CAAAC,cAAA,eAAkB,eACS,aACkC;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UACzEF,EADyE,CAAAG,YAAA,EAAI,EACvE;UACNH,EAAA,CAAAC,cAAA,iBAA+C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/DH,EAAA,CAAAC,cAAA,eAAyD;UACrDD,EAAA,CAAAa,SAAA,iBAA0N;UAC7Fb,EAA7H,CAAAC,cAAA,kBAA6H,aAC5F;UAAhCD,EAAA,CAAA2E,UAAA,mBAAAG,4CAAA;YAAA,OAASJ,GAAA,CAAArB,mBAAA,EAAqB;UAAA,EAAC;UAAKrD,EAAJ,CAAAG,YAAA,EAAI,EAAS;UAC9CH,EAAA,CAAAI,UAAA,KAAA2E,8BAAA,kBAAwE;UAIhF/E,EADI,CAAAG,YAAA,EAAM,EACJ;UAGFH,EADJ,CAAAC,cAAA,eAAkB,iBACiC;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAC,cAAA,iBAA0B;UAAAD,EAAA,CAAAE,MAAA,4DAA0C;UAAQF,EAAR,CAAAG,YAAA,EAAQ,EAAQ;UACpJH,EAAA,CAAAa,SAAA,iBAAqH;UAEjHb,EADJ,CAAAC,cAAA,eAAuB,iBACO;UAAAD,EAAA,CAAAE,MAAA,6EAAgE;UAElGF,EAFkG,CAAAG,YAAA,EAAQ,EAChG,EACJ;UAENH,EAAA,CAAAC,cAAA,eAAwB;UACpBD,EAAA,CAAAa,SAAA,iBAAkF;UAClFb,EAAA,CAAAC,cAAA,iBAA0D;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAChFF,EADgF,CAAAG,YAAA,EAAQ,EAClF;UAGFH,EADJ,CAAAC,cAAA,eAAkB,kBAC6D;UACvED,EAAA,CAAAI,UAAA,KAAA4E,+BAAA,mBAAuG;UACvGhF,EAAA,CAAAE,MAAA,sBACJ;UACJF,EADI,CAAAG,YAAA,EAAS,EACP;UAIEH,EAFR,CAAAC,cAAA,eAA8B,eACI,cACT;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEnCH,EADJ,CAAAC,cAAA,aAAO,cACK;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,qCAAgC;UAAAF,EAAA,CAAAa,SAAA,UAAI;UAC5Db,EAAA,CAAAC,cAAA,cAAQ;UAAAD,EAAA,CAAAE,MAAA,0BAAa;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,sDACnC;UAOxBF,EAPwB,CAAAG,YAAA,EAAQ,EACN,EACJ,EACH,EACL,EACJ,EAEJ;UAIFH,EADJ,CAAAC,cAAA,eAA8B,aACV;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAC,cAAA,aAAoG;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAQ5KF,EAR4K,CAAAG,YAAA,EAAI,EAAI,EAC9J,EAEJ,EACJ,EAEJ,EAEJ;UAScH,EALpB,CAAAC,cAAA,kBAAuB,cACI,cACF,cACU,eACM,aACM;UAAAD,EAAA,CAAAE,MAAA,IAA2D;UAO9GF,EAP8G,CAAAG,YAAA,EAAI,EACxF,EACJ,EACJ,EACJ,EACD,EAEP;UAENH,EAAA,CAAAa,SAAA,sBAA+D;;;UAzFzBb,EAAA,CAAAO,SAAA,IAAuB;UAAvBP,EAAA,CAAAQ,UAAA,cAAAkE,GAAA,CAAA1C,SAAA,CAAuB;UAIuDhC,EAAA,CAAAO,SAAA,GAA4D;UAA5DP,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAiF,eAAA,KAAAC,GAAA,EAAAR,GAAA,CAAApD,SAAA,IAAAoD,GAAA,CAAAhE,CAAA,UAAAC,MAAA,EAA4D;UAClIX,EAAA,CAAAO,SAAA,EAAoC;UAApCP,EAAA,CAAAQ,UAAA,SAAAkE,GAAA,CAAApD,SAAA,IAAAoD,GAAA,CAAAhE,CAAA,UAAAC,MAAA,CAAoC;UAY/BX,EAAA,CAAAO,SAAA,GAA4C;UAAuGP,EAAnJ,CAAAQ,UAAA,SAAAkE,GAAA,CAAApB,aAAA,uBAA4C,YAAAtD,EAAA,CAAAiF,eAAA,KAAAC,GAAA,EAAAR,GAAA,CAAApD,SAAA,IAAAoD,GAAA,CAAAhE,CAAA,aAAAC,MAAA,EAAsK;UAChEX,EAAA,CAAAO,SAAA,GACzJ;UADyJP,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAmF,eAAA,KAAAC,GAAA,GAAAV,GAAA,CAAApB,aAAA,EAAAoB,GAAA,CAAApB,aAAA,EACzJ;UACMtD,EAAA,CAAAO,SAAA,EAAuC;UAAvCP,EAAA,CAAAQ,UAAA,SAAAkE,GAAA,CAAApD,SAAA,IAAAoD,GAAA,CAAAhE,CAAA,aAAAC,MAAA,CAAuC;UAoBGX,EAAA,CAAAO,SAAA,IAAsB;UAAtBP,EAAA,CAAAQ,UAAA,aAAAkE,GAAA,CAAApD,SAAA,CAAsB;UAC/DtB,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAQ,UAAA,SAAAkE,GAAA,CAAApD,SAAA,CAAe;UAuCftB,EAAA,CAAAO,SAAA,IAA2D;UAA3DP,EAAA,CAAAqF,kBAAA,YAAAX,GAAA,CAAAlD,IAAA,gEAA2D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}