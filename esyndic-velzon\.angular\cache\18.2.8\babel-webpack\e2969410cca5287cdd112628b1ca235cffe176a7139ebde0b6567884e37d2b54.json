{"ast": null, "code": "import baseEach from './_baseEach.js';\n\n/**\n * The base implementation of `_.some` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */\nfunction baseSome(collection, predicate) {\n  var result;\n  baseEach(collection, function (value, index, collection) {\n    result = predicate(value, index, collection);\n    return !result;\n  });\n  return !!result;\n}\nexport default baseSome;", "map": {"version": 3, "names": ["baseEach", "baseSome", "collection", "predicate", "result", "value", "index"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_baseSome.js"], "sourcesContent": ["import baseEach from './_baseEach.js';\n\n/**\n * The base implementation of `_.some` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */\nfunction baseSome(collection, predicate) {\n  var result;\n\n  baseEach(collection, function(value, index, collection) {\n    result = predicate(value, index, collection);\n    return !result;\n  });\n  return !!result;\n}\n\nexport default baseSome;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,UAAU,EAAEC,SAAS,EAAE;EACvC,IAAIC,MAAM;EAEVJ,QAAQ,CAACE,UAAU,EAAE,UAASG,KAAK,EAAEC,KAAK,EAAEJ,UAAU,EAAE;IACtDE,MAAM,GAAGD,SAAS,CAACE,KAAK,EAAEC,KAAK,EAAEJ,UAAU,CAAC;IAC5C,OAAO,CAACE,MAAM;EAChB,CAAC,CAAC;EACF,OAAO,CAAC,CAACA,MAAM;AACjB;AAEA,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}