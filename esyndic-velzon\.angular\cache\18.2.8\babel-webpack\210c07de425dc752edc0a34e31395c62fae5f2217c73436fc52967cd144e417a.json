{"ast": null, "code": "import { jobcategories } from 'src/app/core/data';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../shared/breadcrumbs/breadcrumbs.component\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@angular/forms\";\nfunction JobcategoriesComponent_For_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 3)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"lord-icon\", 30);\n    i0.ɵɵelementStart(4, \"a\", 31)(5, \"h5\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 33);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"src\", category_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r1.position, \" Position\");\n  }\n}\nexport class JobcategoriesComponent {\n  constructor() {}\n  ngOnInit() {\n    /**\n    * BreadCrumb\n    */\n    this.breadCrumbItems = [{\n      label: 'Jobs'\n    }, {\n      label: 'Job Categories',\n      active: true\n    }];\n    // Fetch Data\n    setTimeout(() => {\n      this.categories = jobcategories;\n      this.allcategory = jobcategories;\n      document.getElementById('elmLoader')?.classList.add('d-none');\n      document.getElementById('loadmore')?.classList.remove('d-none');\n    }, 1200);\n  }\n  // Search Data\n  performSearch() {\n    this.searchResults = this.allcategory.filter(item => {\n      return item.name.toLowerCase().includes(this.searchTerm.toLowerCase()) || item.position.toLowerCase().includes(this.searchTerm.toLowerCase());\n    });\n    this.categories = this.searchResults;\n  }\n  static {\n    this.ɵfac = function JobcategoriesComponent_Factory(t) {\n      return new (t || JobcategoriesComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: JobcategoriesComponent,\n      selectors: [[\"app-jobcategories\"]],\n      decls: 46,\n      vars: 2,\n      consts: [[\"title\", \"Job Categories\", 3, \"breadcrumbItems\"], [1, \"row\"], [1, \"col-lg-12\"], [1, \"card\"], [1, \"card-header\"], [1, \"row\", \"justify-content-between\", \"gy-3\"], [1, \"col-lg-3\"], [1, \"search-box\"], [\"type\", \"text\", \"placeholder\", \"Search for job categories...\", 1, \"form-control\", \"search\", 3, \"ngModelChange\", \"ngModel\"], [1, \"ri-search-line\", \"search-icon\"], [1, \"col-lg-auto\"], [\"ngbDropdown\", \"\", 1, \"d-md-flex\", \"gap-2\", \"flex-wrap\", \"dropdown\"], [1, \"btn\", \"btn-info\", \"add-btn\"], [1, \"ri-add-fill\", \"me-1\", \"align-bottom\"], [1, \"btn\", \"btn-danger\"], [1, \"ri-filter-2-line\", \"me-1\", \"align-bottom\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", \"id\", \"dropdownMenuLink1\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-soft-info\", \"arrow-none\"], [1, \"ri-more-2-fill\"], [\"ngbDropdownMenu\", \"\", \"aria-labelledby\", \"dropdownMenuLink1\", 1, \"dropdown-menu\"], [\"href\", \"javascript:void(0);\", 1, \"dropdown-item\"], [1, \"row\", \"row-cols-xxl-5\", \"row-cols-lg-3\", \"row-cols-md-2\", \"row-cols-1\"], [1, \"col\"], [\"id\", \"elmLoader\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"avatar-sm\"], [1, \"visually-hidden\"], [\"id\", \"loadmore\", 1, \"row\", \"d-none\"], [1, \"text-center\", \"mb-3\"], [\"id\", \"loadmore\", 1, \"btn\", \"btn-link\", \"text-success\", \"mt-2\"], [1, \"mdi\", \"mdi-loading\", \"mdi-spin\", \"fs-20\", \"align-middle\", \"me-2\"], [1, \"card-body\", \"text-center\", \"py-4\"], [\"trigger\", \"hover\", \"colors\", \"primary:#405189\", \"target\", \"div\", 2, \"width\", \"50px\", \"height\", \"50px\", 3, \"src\"], [\"href\", \"javascript:void(0);\", 1, \"stretched-link\"], [1, \"mt-4\"], [1, \"text-muted\", \"mb-0\"]],\n      template: function JobcategoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-breadcrumbs\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function JobcategoriesComponent_Template_input_ngModelChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function JobcategoriesComponent_Template_input_ngModelChange_8_listener() {\n            return ctx.performSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"button\", 12);\n          i0.ɵɵelement(13, \"i\", 13);\n          i0.ɵɵtext(14, \" Add Categories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"button\", 14);\n          i0.ɵɵelement(16, \"i\", 15);\n          i0.ɵɵtext(17, \" Filters\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"button\", 16);\n          i0.ɵɵelement(19, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"ul\", 18)(21, \"li\")(22, \"a\", 19);\n          i0.ɵɵtext(23, \"All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"li\")(25, \"a\", 19);\n          i0.ɵɵtext(26, \"Last Week\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"li\")(28, \"a\", 19);\n          i0.ɵɵtext(29, \"Last Month\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"li\")(31, \"a\", 19);\n          i0.ɵɵtext(32, \"Last Year\");\n          i0.ɵɵelementEnd()()()()()()()()()();\n          i0.ɵɵelementStart(33, \"div\", 20);\n          i0.ɵɵrepeaterCreate(34, JobcategoriesComponent_For_35_Template, 9, 3, \"div\", 21, i0.ɵɵrepeaterTrackByIndex);\n          i0.ɵɵelementStart(36, \"div\", 22)(37, \"div\", 23)(38, \"span\", 24);\n          i0.ɵɵtext(39, \"Loading...\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(40, \"div\", 25)(41, \"div\", 2)(42, \"div\", 26)(43, \"button\", 27);\n          i0.ɵɵelement(44, \"i\", 28);\n          i0.ɵɵtext(45, \" Load More \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"breadcrumbItems\", ctx.breadCrumbItems);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(26);\n          i0.ɵɵrepeater(ctx.categories);\n        }\n      },\n      dependencies: [i1.BreadcrumbsComponent, i2.NgbDropdown, i2.NgbDropdownToggle, i2.NgbDropdownMenu, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["jobcategories", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate", "category_r1", "icon", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "position", "JobcategoriesComponent", "constructor", "ngOnInit", "breadCrumbItems", "label", "active", "setTimeout", "categories", "allcategory", "document", "getElementById", "classList", "add", "remove", "performSearch", "searchResults", "filter", "item", "toLowerCase", "includes", "searchTerm", "selectors", "decls", "vars", "consts", "template", "JobcategoriesComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "JobcategoriesComponent_Template_input_ngModelChange_8_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵlistener", "ɵɵrepeaterCreate", "JobcategoriesComponent_For_35_Template", "ɵɵrepeaterTrackByIndex", "ɵɵproperty", "ɵɵtwoWayProperty", "ɵɵrepeater"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\jobcategories\\jobcategories.component.ts", "C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\jobcategories\\jobcategories.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { jobcategories } from 'src/app/core/data';\r\n\r\n@Component({\r\n  selector: 'app-jobcategories',\r\n  templateUrl: './jobcategories.component.html',\r\n  styleUrls: ['./jobcategories.component.scss']\r\n})\r\nexport class JobcategoriesComponent implements OnInit {\r\n\r\n  // bread crumb items\r\n  breadCrumbItems!: Array<{}>;\r\n  categories: any;\r\n  allcategory: any;\r\n  searchTerm: any;\r\n  searchResults: any;\r\n\r\n  constructor() {\r\n  }\r\n\r\n\r\n  ngOnInit(): void {\r\n    /**\r\n  * BreadCrumb\r\n  */\r\n    this.breadCrumbItems = [\r\n      { label: 'Jobs' },\r\n      { label: 'Job Categories', active: true }\r\n    ];\r\n\r\n    // Fetch Data\r\n    setTimeout(() => {\r\n      this.categories = jobcategories;\r\n      this.allcategory = jobcategories;\r\n      document.getElementById('elmLoader')?.classList.add('d-none')\r\n      document.getElementById('loadmore')?.classList.remove('d-none')\r\n    }, 1200)\r\n  }\r\n\r\n  // Search Data\r\n  performSearch(): void {\r\n    this.searchResults = this.allcategory.filter((item: any) => {\r\n      return (\r\n        item.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\r\n        item.position.toLowerCase().includes(this.searchTerm.toLowerCase())\r\n      );\r\n    });\r\n    this.categories = this.searchResults\r\n  }\r\n}\r\n", "<!-- Start Breadcrumbs -->\r\n<app-breadcrumbs title=\"Job Categories\" [breadcrumbItems]=\"breadCrumbItems\"></app-breadcrumbs>\r\n<!-- End Breadcrumbs -->\r\n\r\n<div class=\"row\">\r\n    <div class=\"col-lg-12\">\r\n        <div class=\"card\">\r\n            <div class=\"card-header\">\r\n                <div class=\"row justify-content-between gy-3\">\r\n                    <div class=\"col-lg-3\">\r\n                        <div class=\"search-box\">\r\n                            <input type=\"text\" class=\"form-control search\" placeholder=\"Search for job categories...\" [(ngModel)]=\"searchTerm\" (ngModelChange)=\"performSearch()\">\r\n                            <i class=\"ri-search-line search-icon\"></i>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-lg-auto\">\r\n                        <div ngbDropdown class=\"d-md-flex gap-2 flex-wrap dropdown\">\r\n                            <button class=\"btn btn-info add-btn\"><i class=\"ri-add-fill me-1 align-bottom\"></i> Add\r\n                                Categories</button>\r\n                            <button class=\"btn btn-danger\"><i class=\"ri-filter-2-line me-1 align-bottom\"></i>\r\n                                Filters</button>\r\n                            <button ngbDropdownToggle type=\"button\" id=\"dropdownMenuLink1\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\" class=\"btn btn-soft-info arrow-none\"><i class=\"ri-more-2-fill\"></i></button>\r\n                            <ul ngbDropdownMenu class=\"dropdown-menu\" aria-labelledby=\"dropdownMenuLink1\">\r\n                                <li><a class=\"dropdown-item\" href=\"javascript:void(0);\">All</a></li>\r\n                                <li><a class=\"dropdown-item\" href=\"javascript:void(0);\">Last Week</a></li>\r\n                                <li><a class=\"dropdown-item\" href=\"javascript:void(0);\">Last Month</a></li>\r\n                                <li><a class=\"dropdown-item\" href=\"javascript:void(0);\">Last Year</a></li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <!--end col-->\r\n</div>\r\n<!--end row-->\r\n\r\n<div class=\"row row-cols-xxl-5 row-cols-lg-3 row-cols-md-2 row-cols-1\">\r\n    @for(category of categories;track $index){\r\n    <div class=\"col\">\r\n        <div class=\"card\">\r\n            <div class=\"card-body text-center py-4\">\r\n                <lord-icon src=\"{{category.icon}}\" trigger=\"hover\" colors=\"primary:#405189\" target=\"div\" style=\"width:50px;height:50px\"></lord-icon>\r\n                <a href=\"javascript:void(0);\" class=\"stretched-link\">\r\n                    <h5 class=\"mt-4\">{{category.name}}</h5>\r\n                </a>\r\n                <p class=\"text-muted mb-0\">{{category.position}} Position</p>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    }\r\n    <div id=\"elmLoader\">\r\n        <div class=\"spinner-border text-primary avatar-sm\" role=\"status\">\r\n            <span class=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<div class=\"row d-none\" id=\"loadmore\">\r\n    <div class=\"col-lg-12\">\r\n        <div class=\"text-center mb-3\">\r\n            <button class=\"btn btn-link text-success mt-2\" id=\"loadmore\"><i class=\"mdi mdi-loading mdi-spin fs-20 align-middle me-2\"></i> Load More </button>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,aAAa,QAAQ,mBAAmB;;;;;;;ICyCrCC,EAFR,CAAAC,cAAA,cAAiB,aACK,cAC0B;IACpCD,EAAA,CAAAE,SAAA,oBAAoI;IAEhIF,EADJ,CAAAC,cAAA,YAAqD,aAChC;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IACtCH,EADsC,CAAAI,YAAA,EAAK,EACvC;IACJJ,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IAGrEH,EAHqE,CAAAI,YAAA,EAAI,EAC3D,EACJ,EACJ;;;;IAPiBJ,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,qBAAA,QAAAC,WAAA,CAAAC,IAAA,CAAuB;IAEbR,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAS,iBAAA,CAAAF,WAAA,CAAAG,IAAA,CAAiB;IAEXV,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAW,kBAAA,KAAAJ,WAAA,CAAAK,QAAA,cAA8B;;;ADvCzE,OAAM,MAAOC,sBAAsB;EASjCC,YAAA,GACA;EAGAC,QAAQA,CAAA;IACN;;;IAGA,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE;IAAM,CAAE,EACjB;MAAEA,KAAK,EAAE,gBAAgB;MAAEC,MAAM,EAAE;IAAI,CAAE,CAC1C;IAED;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,UAAU,GAAGrB,aAAa;MAC/B,IAAI,CAACsB,WAAW,GAAGtB,aAAa;MAChCuB,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC,EAAEC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAC7DH,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,EAAEC,SAAS,CAACE,MAAM,CAAC,QAAQ,CAAC;IACjE,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACC,aAAa,GAAG,IAAI,CAACP,WAAW,CAACQ,MAAM,CAAEC,IAAS,IAAI;MACzD,OACEA,IAAI,CAACpB,IAAI,CAACqB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACC,UAAU,CAACF,WAAW,EAAE,CAAC,IAC/DD,IAAI,CAAClB,QAAQ,CAACmB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACC,UAAU,CAACF,WAAW,EAAE,CAAC;IAEvE,CAAC,CAAC;IACF,IAAI,CAACX,UAAU,GAAG,IAAI,CAACQ,aAAa;EACtC;;;uBAxCWf,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPnCxC,EAAA,CAAAE,SAAA,yBAA8F;UAUlEF,EAP5B,CAAAC,cAAA,aAAiB,aACU,aACD,aACW,aACyB,aACpB,aACM,eACiI;UAA3DD,EAAA,CAAA0C,gBAAA,2BAAAC,+DAAAC,MAAA;YAAA5C,EAAA,CAAA6C,kBAAA,CAAAJ,GAAA,CAAAR,UAAA,EAAAW,MAAA,MAAAH,GAAA,CAAAR,UAAA,GAAAW,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAAC5C,EAAA,CAAA8C,UAAA,2BAAAH,+DAAA;YAAA,OAAiBF,GAAA,CAAAd,aAAA,EAAe;UAAA,EAAC;UAApJ3B,EAAA,CAAAI,YAAA,EAAqJ;UACrJJ,EAAA,CAAAE,SAAA,WAA0C;UAElDF,EADI,CAAAI,YAAA,EAAM,EACJ;UAGEJ,EAFR,CAAAC,cAAA,eAAyB,eACuC,kBACnB;UAAAD,EAAA,CAAAE,SAAA,aAA6C;UAACF,EAAA,CAAAG,MAAA,uBACrE;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACvBJ,EAAA,CAAAC,cAAA,kBAA+B;UAAAD,EAAA,CAAAE,SAAA,aAAkD;UAC7EF,EAAA,CAAAG,MAAA,gBAAO;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACpBJ,EAAA,CAAAC,cAAA,kBAAoJ;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAS;UAEnLJ,EADR,CAAAC,cAAA,cAA8E,UACtE,aAAoD;UAAAD,EAAA,CAAAG,MAAA,WAAG;UAAIH,EAAJ,CAAAI,YAAA,EAAI,EAAK;UAChEJ,EAAJ,CAAAC,cAAA,UAAI,aAAoD;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAIH,EAAJ,CAAAI,YAAA,EAAI,EAAK;UACtEJ,EAAJ,CAAAC,cAAA,UAAI,aAAoD;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAIH,EAAJ,CAAAI,YAAA,EAAI,EAAK;UACvEJ,EAAJ,CAAAC,cAAA,UAAI,aAAoD;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UASjGH,EATiG,CAAAI,YAAA,EAAI,EAAK,EACzE,EACH,EACJ,EACJ,EACJ,EACJ,EACJ,EAEJ;UAGNJ,EAAA,CAAAC,cAAA,eAAuE;UACnED,EAAA,CAAA+C,gBAAA,KAAAC,sCAAA,mBAAAhD,EAAA,CAAAiD,sBAAA,CAYC;UAGOjD,EAFR,CAAAC,cAAA,eAAoB,eACiD,gBAC/B;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAGpDH,EAHoD,CAAAI,YAAA,EAAO,EAC7C,EACJ,EACJ;UAKMJ,EAHZ,CAAAC,cAAA,eAAsC,cACX,eACW,kBACmC;UAAAD,EAAA,CAAAE,SAAA,aAAgE;UAACF,EAAA,CAAAG,MAAA,mBAAU;UAGpJH,EAHoJ,CAAAI,YAAA,EAAS,EAC/I,EACJ,EACJ;;;UAhEkCJ,EAAA,CAAAkD,UAAA,oBAAAT,GAAA,CAAAzB,eAAA,CAAmC;UAU2ChB,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAmD,gBAAA,YAAAV,GAAA,CAAAR,UAAA,CAAwB;UA4B1IjC,EAAA,CAAAK,SAAA,IAYC;UAZDL,EAAA,CAAAoD,UAAA,CAAAX,GAAA,CAAArB,UAAA,CAYC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}