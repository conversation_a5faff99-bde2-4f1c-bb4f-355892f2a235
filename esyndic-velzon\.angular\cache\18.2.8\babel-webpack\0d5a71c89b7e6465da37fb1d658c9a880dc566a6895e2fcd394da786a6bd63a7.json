{"ast": null, "code": "import basePropertyOf from './_basePropertyOf.js';\n\n/** Used to map characters to HTML entities. */\nvar htmlEscapes = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;'\n};\n\n/**\n * Used by `_.escape` to convert characters to HTML entities.\n *\n * @private\n * @param {string} chr The matched character to escape.\n * @returns {string} Returns the escaped character.\n */\nvar escapeHtmlChar = basePropertyOf(htmlEscapes);\nexport default escapeHtmlChar;", "map": {"version": 3, "names": ["basePropertyOf", "htmlEscapes", "escapeHtmlChar"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/lodash-es/_escapeHtmlChar.js"], "sourcesContent": ["import basePropertyOf from './_basePropertyOf.js';\n\n/** Used to map characters to HTML entities. */\nvar htmlEscapes = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;'\n};\n\n/**\n * Used by `_.escape` to convert characters to HTML entities.\n *\n * @private\n * @param {string} chr The matched character to escape.\n * @returns {string} Returns the escaped character.\n */\nvar escapeHtmlChar = basePropertyOf(htmlEscapes);\n\nexport default escapeHtmlChar;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;;AAEjD;AACA,IAAIC,WAAW,GAAG;EAChB,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE;AACP,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc,GAAGF,cAAc,CAACC,WAAW,CAAC;AAEhD,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}