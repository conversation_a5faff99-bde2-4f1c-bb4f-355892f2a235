{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgbPaginationModule, NgbDropdownModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n// routing\nimport { JobListsRoutingModule } from './job-lists-routing.module';\nimport { SharedModule } from 'src/app/shared/shared.module';\n// Flat Picker\nimport { FlatpickrModule } from 'angularx-flatpickr';\n// Component\nimport { ListComponent } from './list/list.component';\nimport { GridComponent } from './grid/grid.component';\nimport { OverviewComponent } from './overview/overview.component';\n// Apex Chart Package\nimport { NgApexchartsModule } from 'ng-apexcharts';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"angularx-flatpickr\";\nexport class JobListsModule {\n  static {\n    this.ɵfac = function JobListsModule_Factory(t) {\n      return new (t || JobListsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: JobListsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, JobListsRoutingModule, SharedModule, NgbPaginationModule, NgApexchartsModule, FormsModule, ReactiveFormsModule, NgbDropdownModule, NgbTooltipModule, FlatpickrModule.forRoot()]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(JobListsModule, {\n    declarations: [ListComponent, GridComponent, OverviewComponent],\n    imports: [CommonModule, JobListsRoutingModule, SharedModule, NgbPaginationModule, NgApexchartsModule, FormsModule, ReactiveFormsModule, NgbDropdownModule, NgbTooltipModule, i1.FlatpickrModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "NgbPaginationModule", "NgbDropdownModule", "NgbTooltipModule", "FormsModule", "ReactiveFormsModule", "JobListsRoutingModule", "SharedModule", "FlatpickrModule", "ListComponent", "GridComponent", "OverviewComponent", "NgApexchartsModule", "JobListsModule", "forRoot", "declarations", "imports", "i1"], "sources": ["C:\\e-syndic\\esyndic-velzon\\src\\app\\pages\\apps\\jobs\\job-lists\\job-lists.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NgbPaginationModule, NgbDropdownModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\n// routing\r\nimport { JobListsRoutingModule } from './job-lists-routing.module';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\n\r\n// Flat Picker\r\nimport { FlatpickrModule } from 'angularx-flatpickr';\r\n\r\n// Component\r\nimport { ListComponent } from './list/list.component';\r\nimport { GridComponent } from './grid/grid.component';\r\nimport { OverviewComponent } from './overview/overview.component';\r\n\r\n// Apex Chart Package\r\nimport { NgApexchartsModule } from 'ng-apexcharts';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ListComponent,\r\n    GridComponent,\r\n    OverviewComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    JobListsRoutingModule,\r\n    SharedModule,\r\n    NgbPaginationModule,\r\n    NgApexchartsModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    NgbDropdownModule,\r\n    NgbTooltipModule,\r\n    FlatpickrModule.forRoot(),\r\n  ]\r\n})\r\nexport class JobListsModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,iBAAiB,EAAEC,gBAAgB,QAAQ,4BAA4B;AACrG,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE;AACA,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,YAAY,QAAQ,8BAA8B;AAE3D;AACA,SAASC,eAAe,QAAQ,oBAAoB;AAEpD;AACA,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,iBAAiB,QAAQ,+BAA+B;AAEjE;AACA,SAASC,kBAAkB,QAAQ,eAAe;;;AAqBlD,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBAZvBb,YAAY,EACZM,qBAAqB,EACrBC,YAAY,EACZN,mBAAmB,EACnBW,kBAAkB,EAClBR,WAAW,EACXC,mBAAmB,EACnBH,iBAAiB,EACjBC,gBAAgB,EAChBK,eAAe,CAACM,OAAO,EAAE;IAAA;EAAA;;;2EAGhBD,cAAc;IAAAE,YAAA,GAjBvBN,aAAa,EACbC,aAAa,EACbC,iBAAiB;IAAAK,OAAA,GAGjBhB,YAAY,EACZM,qBAAqB,EACrBC,YAAY,EACZN,mBAAmB,EACnBW,kBAAkB,EAClBR,WAAW,EACXC,mBAAmB,EACnBH,iBAAiB,EACjBC,gBAAgB,EAAAc,EAAA,CAAAT,eAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}