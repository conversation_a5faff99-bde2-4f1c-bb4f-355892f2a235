{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Italian [it]\n//! author : <PERSON> : https://github.com/aliem\n//! author: <PERSON><PERSON>: https://github.com/nostalgiaz\n//! author: <PERSON> : https://github.com/Manfre98\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var it = moment.defineLocale('it', {\n    months: 'gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre'.split('_'),\n    monthsShort: 'gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic'.split('_'),\n    weekdays: 'domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato'.split('_'),\n    weekdaysShort: 'dom_lun_mar_mer_gio_ven_sab'.split('_'),\n    weekdaysMin: 'do_lu_ma_me_gi_ve_sa'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: function () {\n        return '[Oggi a' + (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") + ']LT';\n      },\n      nextDay: function () {\n        return '[Domani a' + (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") + ']LT';\n      },\n      nextWeek: function () {\n        return 'dddd [a' + (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") + ']LT';\n      },\n      lastDay: function () {\n        return '[Ieri a' + (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") + ']LT';\n      },\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[La scorsa] dddd [a' + (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") + ']LT';\n          default:\n            return '[Lo scorso] dddd [a' + (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") + ']LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'tra %s',\n      past: '%s fa',\n      s: 'alcuni secondi',\n      ss: '%d secondi',\n      m: 'un minuto',\n      mm: '%d minuti',\n      h: \"un'ora\",\n      hh: '%d ore',\n      d: 'un giorno',\n      dd: '%d giorni',\n      w: 'una settimana',\n      ww: '%d settimane',\n      M: 'un mese',\n      MM: '%d mesi',\n      y: 'un anno',\n      yy: '%d anni'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}º/,\n    ordinal: '%dº',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return it;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "it", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "hours", "nextDay", "nextWeek", "lastDay", "lastWeek", "day", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/moment/locale/it.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Italian [it]\n//! author : <PERSON> : https://github.com/aliem\n//! author: <PERSON><PERSON>: https://github.com/nostalgiaz\n//! author: <PERSON> : https://github.com/Manfre98\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var it = moment.defineLocale('it', {\n        months: 'gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre'.split(\n            '_'\n        ),\n        monthsShort: 'gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic'.split('_'),\n        weekdays: 'domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato'.split(\n            '_'\n        ),\n        weekdaysShort: 'dom_lun_mar_mer_gio_ven_sab'.split('_'),\n        weekdaysMin: 'do_lu_ma_me_gi_ve_sa'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: function () {\n                return (\n                    '[Oggi a' +\n                    (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") +\n                    ']LT'\n                );\n            },\n            nextDay: function () {\n                return (\n                    '[Domani a' +\n                    (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") +\n                    ']LT'\n                );\n            },\n            nextWeek: function () {\n                return (\n                    'dddd [a' +\n                    (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") +\n                    ']LT'\n                );\n            },\n            lastDay: function () {\n                return (\n                    '[Ieri a' +\n                    (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") +\n                    ']LT'\n                );\n            },\n            lastWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                        return (\n                            '[La scorsa] dddd [a' +\n                            (this.hours() > 1\n                                ? 'lle '\n                                : this.hours() === 0\n                                  ? ' '\n                                  : \"ll'\") +\n                            ']LT'\n                        );\n                    default:\n                        return (\n                            '[Lo scorso] dddd [a' +\n                            (this.hours() > 1\n                                ? 'lle '\n                                : this.hours() === 0\n                                  ? ' '\n                                  : \"ll'\") +\n                            ']LT'\n                        );\n                }\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'tra %s',\n            past: '%s fa',\n            s: 'alcuni secondi',\n            ss: '%d secondi',\n            m: 'un minuto',\n            mm: '%d minuti',\n            h: \"un'ora\",\n            hh: '%d ore',\n            d: 'un giorno',\n            dd: '%d giorni',\n            w: 'una settimana',\n            ww: '%d settimane',\n            M: 'un mese',\n            MM: '%d mesi',\n            y: 'un anno',\n            yy: '%d anni',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}º/,\n        ordinal: '%dº',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return it;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,+FAA+F,CAAC<PERSON>,KAAK,CACzG,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,0DAA0D,CAACF,KAAK,CACtE,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,SAAAA,CAAA,EAAY;QACjB,OACI,SAAS,IACR,IAAI,CAACC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,GAC9D,KAAK;MAEb,CAAC;MACDC,OAAO,EAAE,SAAAA,CAAA,EAAY;QACjB,OACI,WAAW,IACV,IAAI,CAACD,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,GAC9D,KAAK;MAEb,CAAC;MACDE,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OACI,SAAS,IACR,IAAI,CAACF,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,GAC9D,KAAK;MAEb,CAAC;MACDG,OAAO,EAAE,SAAAA,CAAA,EAAY;QACjB,OACI,SAAS,IACR,IAAI,CAACH,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,GAC9D,KAAK;MAEb,CAAC;MACDI,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;YACF,OACI,qBAAqB,IACpB,IAAI,CAACL,KAAK,CAAC,CAAC,GAAG,CAAC,GACX,MAAM,GACN,IAAI,CAACA,KAAK,CAAC,CAAC,KAAK,CAAC,GAChB,GAAG,GACH,KAAK,CAAC,GACd,KAAK;UAEb;YACI,OACI,qBAAqB,IACpB,IAAI,CAACA,KAAK,CAAC,CAAC,GAAG,CAAC,GACX,MAAM,GACN,IAAI,CAACA,KAAK,CAAC,CAAC,KAAK,CAAC,GAChB,GAAG,GACH,KAAK,CAAC,GACd,KAAK;QAEjB;MACJ,CAAC;MACDM,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,OAAO;MACbC,CAAC,EAAE,gBAAgB;MACnBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,cAAc;MAClBC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,UAAU;IAClCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO7C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}