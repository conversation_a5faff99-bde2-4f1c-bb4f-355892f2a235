{"version": 3, "file": "legacy-form-field.mjs", "sources": ["../../../../../../src/material/legacy-form-field/error.ts", "../../../../../../src/material/legacy-form-field/hint.ts", "../../../../../../src/material/legacy-form-field/label.ts", "../../../../../../src/material/legacy-form-field/placeholder.ts", "../../../../../../src/material/legacy-form-field/form-field.ts", "../../../../../../src/material/legacy-form-field/form-field.html", "../../../../../../src/material/legacy-form-field/prefix.ts", "../../../../../../src/material/legacy-form-field/suffix.ts", "../../../../../../src/material/legacy-form-field/form-field-module.ts", "../../../../../../src/material/legacy-form-field/legacy-form-field_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Attribute, Directive, ElementRef, Input} from '@angular/core';\nimport {MAT_ERROR} from '@angular/material/form-field';\n\nlet nextUniqueId = 0;\n\n/**\n * Single error message to be shown underneath the form field.\n * @deprecated Use `MatError` from `@angular/material/form-field` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: 'mat-error',\n  host: {\n    'class': 'mat-error',\n    '[attr.id]': 'id',\n    'aria-atomic': 'true',\n  },\n  providers: [{provide: MAT_ERROR, useExisting: MatLegacyError}],\n})\nexport class MatLegacyError {\n  @Input() id: string = `mat-error-${nextUniqueId++}`;\n\n  constructor(@Attribute('aria-live') ariaLive: string, elementRef: ElementRef) {\n    // If no aria-live value is set add 'polite' as a default. This is preferred over setting\n    // role='alert' so that screen readers do not interrupt the current task to read this aloud.\n    if (!ariaLive) {\n      elementRef.nativeElement.setAttribute('aria-live', 'polite');\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, InjectionToken, Input} from '@angular/core';\n\nlet nextUniqueId = 0;\n\n/**\n * Injection token that can be used to reference instances of `MatHint`. It serves as\n * alternative token to the actual `MatHint` class which could cause unnecessary\n * retention of the class and its directive metadata.\n *\n * *Note*: This is not part of the public API as the MDC-based form-field will not\n * need a lightweight token for `MatHint` and we want to reduce breaking changes.\n *\n * @deprecated Use `_MAT_HINT` from `@angular/material/form-field` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport const _MAT_LEGACY_HINT = new InjectionToken<MatLegacyHint>('MatHint');\n\n/**\n * Hint text to be shown underneath the form field control.\n * @deprecated Use `MatHint` from `@angular/material/form-field` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: 'mat-hint',\n  host: {\n    'class': 'mat-hint',\n    '[class.mat-form-field-hint-end]': 'align === \"end\"',\n    '[attr.id]': 'id',\n    // Remove align attribute to prevent it from interfering with layout.\n    '[attr.align]': 'null',\n  },\n  providers: [{provide: _MAT_LEGACY_HINT, useExisting: MatLegacyHint}],\n})\nexport class MatLegacyHint {\n  /** Whether to align the hint label at the start or end of the line. */\n  @Input() align: 'start' | 'end' = 'start';\n\n  /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n  @Input() id: string = `mat-hint-${nextUniqueId++}`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive} from '@angular/core';\n\n/**\n * The floating label for a `mat-form-field`.\n * @deprecated Use `MatLabel` from `@angular/material/form-field` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: 'mat-label',\n})\nexport class MatLegacyLabel {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive} from '@angular/core';\n\n/**\n * The placeholder text for an `MatFormField`.\n * @deprecated Use `<mat-label>` to specify the label and the `placeholder` attribute to specify the\n *     placeholder.\n * @breaking-change 8.0.0\n */\n@Directive({\n  selector: 'mat-placeholder',\n})\nexport class MatLegacyPlaceholder {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directionality} from '@angular/cdk/bidi';\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {\n  AfterContentChecked,\n  AfterContentInit,\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  ElementRef,\n  Inject,\n  Input,\n  NgZone,\n  Optional,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  OnDestroy,\n  InjectionToken,\n} from '@angular/core';\nimport {CanColor, mixinColor, ThemePalette} from '@angular/material/core';\nimport {fromEvent, merge, Subject} from 'rxjs';\nimport {startWith, take, takeUntil} from 'rxjs/operators';\nimport {MatLegacyError} from './error';\nimport {_MAT_LEGACY_HINT, MatLegacyHint} from './hint';\nimport {MatLegacyLabel} from './label';\nimport {MatLegacyPlaceholder} from './placeholder';\nimport {MatLegacyPrefix} from './prefix';\nimport {MatLegacySuffix} from './suffix';\nimport {Platform} from '@angular/cdk/platform';\nimport {AbstractControlDirective} from '@angular/forms';\nimport {ANIMATION_MODULE_TYPE} from '@angular/platform-browser/animations';\nimport {\n  getMatFormFieldDuplicatedHintError,\n  getMatFormFieldMissingControlError,\n  getMatFormFieldPlaceholderConflictError,\n  matFormFieldAnimations,\n  MatFormFieldControl,\n  MAT_ERROR,\n  MAT_FORM_FIELD,\n  MAT_PREFIX,\n  MAT_SUFFIX,\n} from '@angular/material/form-field';\n\nlet nextUniqueId = 0;\nconst floatingLabelScale = 0.75;\nconst outlineGapPadding = 5;\n\n/**\n * Boilerplate for applying mixins to MatFormField.\n * @docs-private\n */\nconst _MatFormFieldBase = mixinColor(\n  class {\n    constructor(public _elementRef: ElementRef) {}\n  },\n  'primary',\n);\n\n/**\n * Possible appearance styles for the form field.\n * @deprecated Use `MatFormFieldAppearance` from `@angular/material/form-field` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport type MatLegacyFormFieldAppearance = 'legacy' | 'standard' | 'fill' | 'outline';\n\n/**\n * Possible values for the \"floatLabel\" form field input.\n * @deprecated Use `FloatLabelType` from `@angular/material/form-field` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport type LegacyFloatLabelType = 'always' | 'never' | 'auto';\n\n/**\n * Represents the default options for the form field that can be configured\n * using the `MAT_FORM_FIELD_DEFAULT_OPTIONS` injection token.\n * @deprecated Use `MatFormFieldDefaultOptions` from `@angular/material/form-field` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport interface MatLegacyFormFieldDefaultOptions {\n  /** Default form field appearance style. */\n  appearance?: MatLegacyFormFieldAppearance;\n  /** Default color of the form field. */\n  color?: ThemePalette;\n  /** Whether the required marker should be hidden by default. */\n  hideRequiredMarker?: boolean;\n  /**\n   * Whether the label for form fields should by default float `always`,\n   * `never`, or `auto` (only when necessary).\n   */\n  floatLabel?: LegacyFloatLabelType;\n}\n\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n * @deprecated Use `MAT_FORM_FIELD_DEFAULT_OPTIONS` from `@angular/material/form-field` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport const MAT_LEGACY_FORM_FIELD_DEFAULT_OPTIONS =\n  new InjectionToken<MatLegacyFormFieldDefaultOptions>('MAT_FORM_FIELD_DEFAULT_OPTIONS');\n\n/**\n * Container for form controls that applies Material Design styling and behavior.\n * @deprecated Use `MatFormField` from `@angular/material/form-field` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Component({\n  selector: 'mat-form-field',\n  exportAs: 'matFormField',\n  templateUrl: 'form-field.html',\n  // MatInput is a directive and can't have styles, so we need to include its styles here\n  // in form-field-input.css. The MatInput styles are fairly minimal so it shouldn't be a\n  // big deal for people who aren't using MatInput.\n  styleUrls: [\n    'form-field.css',\n    'form-field-fill.css',\n    'form-field-input.css',\n    'form-field-legacy.css',\n    'form-field-outline.css',\n    'form-field-standard.css',\n  ],\n  animations: [matFormFieldAnimations.transitionMessages],\n  host: {\n    'class': 'mat-form-field',\n    '[class.mat-form-field-appearance-standard]': 'appearance == \"standard\"',\n    '[class.mat-form-field-appearance-fill]': 'appearance == \"fill\"',\n    '[class.mat-form-field-appearance-outline]': 'appearance == \"outline\"',\n    '[class.mat-form-field-appearance-legacy]': 'appearance == \"legacy\"',\n    '[class.mat-form-field-invalid]': '_control.errorState',\n    '[class.mat-form-field-can-float]': '_canLabelFloat()',\n    '[class.mat-form-field-should-float]': '_shouldLabelFloat()',\n    '[class.mat-form-field-has-label]': '_hasFloatingLabel()',\n    '[class.mat-form-field-hide-placeholder]': '_hideControlPlaceholder()',\n    '[class.mat-form-field-disabled]': '_control.disabled',\n    '[class.mat-form-field-autofilled]': '_control.autofilled',\n    '[class.mat-focused]': '_control.focused',\n    '[class.ng-untouched]': '_shouldForward(\"untouched\")',\n    '[class.ng-touched]': '_shouldForward(\"touched\")',\n    '[class.ng-pristine]': '_shouldForward(\"pristine\")',\n    '[class.ng-dirty]': '_shouldForward(\"dirty\")',\n    '[class.ng-valid]': '_shouldForward(\"valid\")',\n    '[class.ng-invalid]': '_shouldForward(\"invalid\")',\n    '[class.ng-pending]': '_shouldForward(\"pending\")',\n    '[class._mat-animation-noopable]': '!_animationsEnabled',\n    'ngSkipHydration': '',\n  },\n  inputs: ['color'],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [{provide: MAT_FORM_FIELD, useExisting: MatLegacyFormField}],\n})\nexport class MatLegacyFormField\n  extends _MatFormFieldBase\n  implements AfterContentInit, AfterContentChecked, AfterViewInit, OnDestroy, CanColor\n{\n  /**\n   * Whether the outline gap needs to be calculated\n   * immediately on the next change detection run.\n   */\n  private _outlineGapCalculationNeededImmediately = false;\n\n  /** Whether the outline gap needs to be calculated next time the zone has stabilized. */\n  private _outlineGapCalculationNeededOnStable = false;\n\n  private readonly _destroyed = new Subject<void>();\n\n  /** The form field appearance style. */\n  @Input()\n  get appearance(): MatLegacyFormFieldAppearance {\n    return this._appearance;\n  }\n  set appearance(value: MatLegacyFormFieldAppearance) {\n    const oldValue = this._appearance;\n\n    this._appearance = value || this._defaults?.appearance || 'legacy';\n\n    if (this._appearance === 'outline' && oldValue !== value) {\n      this._outlineGapCalculationNeededOnStable = true;\n    }\n  }\n  _appearance: MatLegacyFormFieldAppearance;\n\n  /** Whether the required marker should be hidden. */\n  @Input()\n  get hideRequiredMarker(): boolean {\n    return this._hideRequiredMarker;\n  }\n  set hideRequiredMarker(value: BooleanInput) {\n    this._hideRequiredMarker = coerceBooleanProperty(value);\n  }\n  private _hideRequiredMarker = false;\n\n  /** Override for the logic that disables the label animation in certain cases. */\n  private _showAlwaysAnimate = false;\n\n  /** Whether the floating label should always float or not. */\n  _shouldAlwaysFloat(): boolean {\n    return this.floatLabel === 'always' && !this._showAlwaysAnimate;\n  }\n\n  /** Whether the label can float or not. */\n  _canLabelFloat(): boolean {\n    return this.floatLabel !== 'never';\n  }\n\n  /** State of the mat-hint and mat-error animations. */\n  _subscriptAnimationState: string = '';\n\n  /** Text for the form field hint. */\n  @Input()\n  get hintLabel(): string {\n    return this._hintLabel;\n  }\n  set hintLabel(value: string) {\n    this._hintLabel = value;\n    this._processHints();\n  }\n  private _hintLabel = '';\n\n  // Unique id for the hint label.\n  readonly _hintLabelId: string = `mat-hint-${nextUniqueId++}`;\n\n  // Unique id for the label element.\n  readonly _labelId = `mat-form-field-label-${nextUniqueId++}`;\n\n  /**\n   * Whether the label should always float, never float or float as the user types.\n   *\n   * Note: only the legacy appearance supports the `never` option. `never` was originally added as a\n   * way to make the floating label emulate the behavior of a standard input placeholder. However\n   * the form field now supports both floating labels and placeholders. Therefore in the non-legacy\n   * appearances the `never` option has been disabled in favor of just using the placeholder.\n   */\n  @Input()\n  get floatLabel(): LegacyFloatLabelType {\n    return this.appearance !== 'legacy' && this._floatLabel === 'never' ? 'auto' : this._floatLabel;\n  }\n  set floatLabel(value: LegacyFloatLabelType) {\n    if (value !== this._floatLabel) {\n      this._floatLabel = value || this._getDefaultFloatLabelState();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  private _floatLabel: LegacyFloatLabelType;\n\n  /** Whether the Angular animations are enabled. */\n  _animationsEnabled: boolean;\n\n  @ViewChild('connectionContainer', {static: true}) _connectionContainerRef: ElementRef;\n  @ViewChild('inputContainer') _inputContainerRef: ElementRef;\n  @ViewChild('label') private _label: ElementRef<HTMLElement>;\n\n  @ContentChild(MatFormFieldControl) _controlNonStatic: MatFormFieldControl<any>;\n  @ContentChild(MatFormFieldControl, {static: true}) _controlStatic: MatFormFieldControl<any>;\n  get _control() {\n    // TODO(crisbeto): we need this workaround in order to support both Ivy and ViewEngine.\n    //  We should clean this up once Ivy is the default renderer.\n    return this._explicitFormFieldControl || this._controlNonStatic || this._controlStatic;\n  }\n  set _control(value) {\n    this._explicitFormFieldControl = value;\n  }\n  private _explicitFormFieldControl: MatFormFieldControl<any>;\n\n  @ContentChild(MatLegacyLabel) _labelChildNonStatic: MatLegacyLabel;\n  @ContentChild(MatLegacyLabel, {static: true}) _labelChildStatic: MatLegacyLabel;\n  @ContentChild(MatLegacyPlaceholder) _placeholderChild: MatLegacyPlaceholder;\n\n  @ContentChildren(MAT_ERROR, {descendants: true}) _errorChildren: QueryList<MatLegacyError>;\n  @ContentChildren(_MAT_LEGACY_HINT, {descendants: true}) _hintChildren: QueryList<MatLegacyHint>;\n  @ContentChildren(MAT_PREFIX, {descendants: true})\n  _prefixChildren: QueryList<MatLegacyPrefix>;\n  @ContentChildren(MAT_SUFFIX, {descendants: true})\n  _suffixChildren: QueryList<MatLegacySuffix>;\n\n  constructor(\n    elementRef: ElementRef,\n    private _changeDetectorRef: ChangeDetectorRef,\n    @Optional() private _dir: Directionality,\n    @Optional()\n    @Inject(MAT_LEGACY_FORM_FIELD_DEFAULT_OPTIONS)\n    private _defaults: MatLegacyFormFieldDefaultOptions,\n    private _platform: Platform,\n    private _ngZone: NgZone,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) _animationMode: string,\n  ) {\n    super(elementRef);\n\n    this.floatLabel = this._getDefaultFloatLabelState();\n    this._animationsEnabled = _animationMode !== 'NoopAnimations';\n\n    // Set the default through here so we invoke the setter on the first run.\n    this.appearance = _defaults?.appearance || 'legacy';\n    if (_defaults) {\n      this._hideRequiredMarker = Boolean(_defaults.hideRequiredMarker);\n      if (_defaults.color) {\n        this.color = this.defaultColor = _defaults.color;\n      }\n    }\n  }\n\n  /**\n   * Gets the id of the label element. If no label is present, returns `null`.\n   */\n  getLabelId(): string | null {\n    return this._hasFloatingLabel() ? this._labelId : null;\n  }\n\n  /**\n   * Gets an ElementRef for the element that a overlay attached to the form field should be\n   * positioned relative to.\n   */\n  getConnectedOverlayOrigin(): ElementRef {\n    return this._connectionContainerRef || this._elementRef;\n  }\n\n  ngAfterContentInit() {\n    this._validateControlChild();\n\n    const control = this._control;\n\n    if (control.controlType) {\n      this._elementRef.nativeElement.classList.add(`mat-form-field-type-${control.controlType}`);\n    }\n\n    // Subscribe to changes in the child control state in order to update the form field UI.\n    control.stateChanges.pipe(startWith(null)).subscribe(() => {\n      this._validatePlaceholders();\n      this._syncDescribedByIds();\n      this._changeDetectorRef.markForCheck();\n    });\n\n    // Run change detection if the value changes.\n    if (control.ngControl && control.ngControl.valueChanges) {\n      control.ngControl.valueChanges\n        .pipe(takeUntil(this._destroyed))\n        .subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n\n    // Note that we have to run outside of the `NgZone` explicitly,\n    // in order to avoid throwing users into an infinite loop\n    // if `zone-patch-rxjs` is included.\n    this._ngZone.runOutsideAngular(() => {\n      this._ngZone.onStable.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        if (this._outlineGapCalculationNeededOnStable) {\n          this.updateOutlineGap();\n        }\n      });\n    });\n\n    // Run change detection and update the outline if the suffix or prefix changes.\n    merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n      this._outlineGapCalculationNeededOnStable = true;\n      this._changeDetectorRef.markForCheck();\n    });\n\n    // Re-validate when the number of hints changes.\n    this._hintChildren.changes.pipe(startWith(null)).subscribe(() => {\n      this._processHints();\n      this._changeDetectorRef.markForCheck();\n    });\n\n    // Update the aria-described by when the number of errors changes.\n    this._errorChildren.changes.pipe(startWith(null)).subscribe(() => {\n      this._syncDescribedByIds();\n      this._changeDetectorRef.markForCheck();\n    });\n\n    if (this._dir) {\n      this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        if (typeof requestAnimationFrame === 'function') {\n          this._ngZone.runOutsideAngular(() => {\n            requestAnimationFrame(() => this.updateOutlineGap());\n          });\n        } else {\n          this.updateOutlineGap();\n        }\n      });\n    }\n  }\n\n  ngAfterContentChecked() {\n    this._validateControlChild();\n    if (this._outlineGapCalculationNeededImmediately) {\n      this.updateOutlineGap();\n    }\n  }\n\n  ngAfterViewInit() {\n    // Avoid animations on load.\n    this._subscriptAnimationState = 'enter';\n    this._changeDetectorRef.detectChanges();\n  }\n\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /**\n   * Determines whether a class from the AbstractControlDirective\n   * should be forwarded to the host element.\n   */\n  _shouldForward(prop: keyof AbstractControlDirective): boolean {\n    const control = this._control ? this._control.ngControl : null;\n    return control && control[prop];\n  }\n\n  _hasPlaceholder() {\n    return !!((this._control && this._control.placeholder) || this._placeholderChild);\n  }\n\n  _hasLabel() {\n    return !!(this._labelChildNonStatic || this._labelChildStatic);\n  }\n\n  _shouldLabelFloat() {\n    return (\n      this._canLabelFloat() &&\n      ((this._control && this._control.shouldLabelFloat) || this._shouldAlwaysFloat())\n    );\n  }\n\n  _hideControlPlaceholder() {\n    // In the legacy appearance the placeholder is promoted to a label if no label is given.\n    return (\n      (this.appearance === 'legacy' && !this._hasLabel()) ||\n      (this._hasLabel() && !this._shouldLabelFloat())\n    );\n  }\n\n  _hasFloatingLabel() {\n    // In the legacy appearance the placeholder is promoted to a label if no label is given.\n    return this._hasLabel() || (this.appearance === 'legacy' && this._hasPlaceholder());\n  }\n\n  /** Determines whether to display hints or errors. */\n  _getDisplayedMessages(): 'error' | 'hint' {\n    return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState\n      ? 'error'\n      : 'hint';\n  }\n\n  /** Animates the placeholder up and locks it in position. */\n  _animateAndLockLabel(): void {\n    if (this._hasFloatingLabel() && this._canLabelFloat()) {\n      // If animations are disabled, we shouldn't go in here,\n      // because the `transitionend` will never fire.\n      if (this._animationsEnabled && this._label) {\n        this._showAlwaysAnimate = true;\n\n        fromEvent(this._label.nativeElement, 'transitionend')\n          .pipe(take(1))\n          .subscribe(() => {\n            this._showAlwaysAnimate = false;\n          });\n      }\n\n      this.floatLabel = 'always';\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /**\n   * Ensure that there is only one placeholder (either `placeholder` attribute on the child control\n   * or child element with the `mat-placeholder` directive).\n   */\n  private _validatePlaceholders() {\n    if (\n      this._control.placeholder &&\n      this._placeholderChild &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw getMatFormFieldPlaceholderConflictError();\n    }\n  }\n\n  /** Does any extra processing that is required when handling the hints. */\n  private _processHints() {\n    this._validateHints();\n    this._syncDescribedByIds();\n  }\n\n  /**\n   * Ensure that there is a maximum of one of each `<mat-hint>` alignment specified, with the\n   * attribute being considered as `align=\"start\"`.\n   */\n  private _validateHints() {\n    if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      let startHint: MatLegacyHint;\n      let endHint: MatLegacyHint;\n      this._hintChildren.forEach((hint: MatLegacyHint) => {\n        if (hint.align === 'start') {\n          if (startHint || this.hintLabel) {\n            throw getMatFormFieldDuplicatedHintError('start');\n          }\n          startHint = hint;\n        } else if (hint.align === 'end') {\n          if (endHint) {\n            throw getMatFormFieldDuplicatedHintError('end');\n          }\n          endHint = hint;\n        }\n      });\n    }\n  }\n\n  /** Gets the default float label state. */\n  private _getDefaultFloatLabelState(): LegacyFloatLabelType {\n    return (this._defaults && this._defaults.floatLabel) || 'auto';\n  }\n\n  /**\n   * Sets the list of element IDs that describe the child control. This allows the control to update\n   * its `aria-describedby` attribute accordingly.\n   */\n  private _syncDescribedByIds() {\n    if (this._control) {\n      let ids: string[] = [];\n\n      // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n      if (\n        this._control.userAriaDescribedBy &&\n        typeof this._control.userAriaDescribedBy === 'string'\n      ) {\n        ids.push(...this._control.userAriaDescribedBy.split(' '));\n      }\n\n      if (this._getDisplayedMessages() === 'hint') {\n        const startHint = this._hintChildren\n          ? this._hintChildren.find(hint => hint.align === 'start')\n          : null;\n        const endHint = this._hintChildren\n          ? this._hintChildren.find(hint => hint.align === 'end')\n          : null;\n\n        if (startHint) {\n          ids.push(startHint.id);\n        } else if (this._hintLabel) {\n          ids.push(this._hintLabelId);\n        }\n\n        if (endHint) {\n          ids.push(endHint.id);\n        }\n      } else if (this._errorChildren) {\n        ids.push(...this._errorChildren.map(error => error.id));\n      }\n\n      this._control.setDescribedByIds(ids);\n    }\n  }\n\n  /** Throws an error if the form field's control is missing. */\n  protected _validateControlChild() {\n    if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatFormFieldMissingControlError();\n    }\n  }\n\n  /**\n   * Updates the width and position of the gap in the outline. Only relevant for the outline\n   * appearance.\n   */\n  updateOutlineGap() {\n    const labelEl = this._label ? this._label.nativeElement : null;\n    const container = this._connectionContainerRef.nativeElement;\n    const outlineStartSelector = '.mat-form-field-outline-start';\n    const outlineGapSelector = '.mat-form-field-outline-gap';\n\n    // getBoundingClientRect isn't available on the server.\n    if (this.appearance !== 'outline' || !this._platform.isBrowser) {\n      return;\n    }\n\n    // If there is no content, set the gap elements to zero.\n    if (!labelEl || !labelEl.children.length || !labelEl.textContent!.trim()) {\n      const gapElements = container.querySelectorAll(\n        `${outlineStartSelector}, ${outlineGapSelector}`,\n      );\n      for (let i = 0; i < gapElements.length; i++) {\n        gapElements[i].style.width = '0';\n      }\n      return;\n    }\n\n    // If the element is not present in the DOM, the outline gap will need to be calculated\n    // the next time it is checked and in the DOM.\n    if (!this._isAttachedToDOM()) {\n      this._outlineGapCalculationNeededImmediately = true;\n      return;\n    }\n\n    let startWidth = 0;\n    let gapWidth = 0;\n\n    const startEls = container.querySelectorAll(outlineStartSelector);\n    const gapEls = container.querySelectorAll(outlineGapSelector);\n\n    if (this._label && this._label.nativeElement.children.length) {\n      const containerRect = container.getBoundingClientRect();\n\n      // If the container's width and height are zero, it means that the element is\n      // invisible and we can't calculate the outline gap. Mark the element as needing\n      // to be checked the next time the zone stabilizes. We can't do this immediately\n      // on the next change detection, because even if the element becomes visible,\n      // the `ClientRect` won't be recalculated immediately. We reset the\n      // `_outlineGapCalculationNeededImmediately` flag some we don't run the checks twice.\n      if (containerRect.width === 0 && containerRect.height === 0) {\n        this._outlineGapCalculationNeededOnStable = true;\n        this._outlineGapCalculationNeededImmediately = false;\n        return;\n      }\n\n      const containerStart = this._getStartEnd(containerRect);\n      const labelChildren = labelEl.children;\n      const labelStart = this._getStartEnd(labelChildren[0].getBoundingClientRect());\n      let labelWidth = 0;\n\n      for (let i = 0; i < labelChildren.length; i++) {\n        labelWidth += (labelChildren[i] as HTMLElement).offsetWidth;\n      }\n      startWidth = Math.abs(labelStart - containerStart) - outlineGapPadding;\n      gapWidth = labelWidth > 0 ? labelWidth * floatingLabelScale + outlineGapPadding * 2 : 0;\n    }\n\n    for (let i = 0; i < startEls.length; i++) {\n      startEls[i].style.width = `${startWidth}px`;\n    }\n    for (let i = 0; i < gapEls.length; i++) {\n      gapEls[i].style.width = `${gapWidth}px`;\n    }\n\n    this._outlineGapCalculationNeededOnStable = this._outlineGapCalculationNeededImmediately =\n      false;\n  }\n\n  /** Gets the start end of the rect considering the current directionality. */\n  private _getStartEnd(rect: ClientRect): number {\n    return this._dir && this._dir.value === 'rtl' ? rect.right : rect.left;\n  }\n\n  /** Checks whether the form field is attached to the DOM. */\n  private _isAttachedToDOM(): boolean {\n    const element: HTMLElement = this._elementRef.nativeElement;\n\n    if (element.getRootNode) {\n      const rootNode = element.getRootNode();\n      // If the element is inside the DOM the root node will be either the document\n      // or the closest shadow root, otherwise it'll be the element itself.\n      return rootNode && rootNode !== element;\n    }\n\n    // Otherwise fall back to checking if it's in the document. This doesn't account for\n    // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n    return document.documentElement!.contains(element);\n  }\n}\n", "<div class=\"mat-form-field-wrapper\">\n  <div class=\"mat-form-field-flex\" #connectionContainer\n       (click)=\"_control.onContainerClick($event)\">\n\n    <!-- Outline used for outline appearance. -->\n    <ng-container *ngIf=\"appearance == 'outline'\">\n      <div class=\"mat-form-field-outline\">\n        <div class=\"mat-form-field-outline-start\"></div>\n        <div class=\"mat-form-field-outline-gap\"></div>\n        <div class=\"mat-form-field-outline-end\"></div>\n      </div>\n      <div class=\"mat-form-field-outline mat-form-field-outline-thick\">\n        <div class=\"mat-form-field-outline-start\"></div>\n        <div class=\"mat-form-field-outline-gap\"></div>\n        <div class=\"mat-form-field-outline-end\"></div>\n      </div>\n    </ng-container>\n\n    <div\n      class=\"mat-form-field-prefix\"\n      *ngIf=\"_prefixChildren.length\"\n      (cdkObserveContent)=\"updateOutlineGap()\"\n      [cdkObserveContentDisabled]=\"appearance != 'outline'\">\n      <ng-content select=\"[matPrefix]\"></ng-content>\n    </div>\n\n    <div class=\"mat-form-field-infix\" #inputContainer>\n      <ng-content></ng-content>\n\n      <span class=\"mat-form-field-label-wrapper\">\n        <!-- We add aria-owns as a workaround for an issue in JAWS & NVDA where the label isn't\n             read if it comes before the control in the DOM. -->\n        <label class=\"mat-form-field-label\"\n               (cdkObserveContent)=\"updateOutlineGap()\"\n               [cdkObserveContentDisabled]=\"appearance != 'outline'\"\n               [id]=\"_labelId\"\n               [attr.for]=\"_control.id\"\n               [attr.aria-owns]=\"_control.id\"\n               [class.mat-empty]=\"_control.empty && !_shouldAlwaysFloat()\"\n               [class.mat-form-field-empty]=\"_control.empty && !_shouldAlwaysFloat()\"\n               [class.mat-accent]=\"color == 'accent'\"\n               [class.mat-warn]=\"color == 'warn'\"\n               #label\n               *ngIf=\"_hasFloatingLabel()\"\n               [ngSwitch]=\"_hasLabel()\">\n\n          <!-- @breaking-change 8.0.0 remove in favor of mat-label element an placeholder attr. -->\n          <ng-container *ngSwitchCase=\"false\">\n            <ng-content select=\"mat-placeholder\"></ng-content>\n            <span>{{_control.placeholder}}</span>\n          </ng-container>\n\n          <ng-content select=\"mat-label\" *ngSwitchCase=\"true\"></ng-content>\n\n          <!-- @breaking-change 8.0.0 remove `mat-placeholder-required` class -->\n          <span\n            class=\"mat-placeholder-required mat-form-field-required-marker\"\n            aria-hidden=\"true\"\n            *ngIf=\"!hideRequiredMarker && _control.required && !_control.disabled\">&#32;*</span>\n        </label>\n      </span>\n    </div>\n\n    <div class=\"mat-form-field-suffix\" *ngIf=\"_suffixChildren.length\">\n      <ng-content select=\"[matSuffix]\"></ng-content>\n    </div>\n  </div>\n\n  <!-- Underline used for legacy, standard, and box appearances. -->\n  <div class=\"mat-form-field-underline\"\n       *ngIf=\"appearance != 'outline'\">\n    <span class=\"mat-form-field-ripple\"\n          [class.mat-accent]=\"color == 'accent'\"\n          [class.mat-warn]=\"color == 'warn'\"></span>\n  </div>\n\n  <div class=\"mat-form-field-subscript-wrapper\"\n       [ngSwitch]=\"_getDisplayedMessages()\">\n    <div *ngSwitchCase=\"'error'\" [@transitionMessages]=\"_subscriptAnimationState\">\n      <ng-content select=\"mat-error\"></ng-content>\n    </div>\n\n    <div class=\"mat-form-field-hint-wrapper\" *ngSwitchCase=\"'hint'\"\n      [@transitionMessages]=\"_subscriptAnimationState\">\n      <!-- TODO(mmalerba): use an actual <mat-hint> once all selectors are switched to mat-* -->\n      <div *ngIf=\"hintLabel\" [id]=\"_hintLabelId\" class=\"mat-hint\">{{hintLabel}}</div>\n      <ng-content select=\"mat-hint:not([align='end'])\"></ng-content>\n      <div class=\"mat-form-field-hint-spacer\"></div>\n      <ng-content select=\"mat-hint[align='end']\"></ng-content>\n    </div>\n  </div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive} from '@angular/core';\nimport {MAT_PREFIX} from '@angular/material/form-field';\n\n/**\n * Prefix to be placed in front of the form field.\n * @deprecated Use `MatPrefix` from `@angular/material/form-field` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: '[matPrefix]',\n  providers: [{provide: MAT_PREFIX, useExisting: MatLegacyPrefix}],\n})\nexport class MatLegacyPrefix {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive} from '@angular/core';\nimport {MAT_SUFFIX} from '@angular/material/form-field';\n\n/**\n * Suffix to be placed at the end of the form field.\n * @deprecated Use `MatSuffix` from `@angular/material/form-field` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@Directive({\n  selector: '[matSuffix]',\n  providers: [{provide: MAT_SUFFIX, useExisting: MatLegacySuffix}],\n})\nexport class MatLegacySuffix {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ObserversModule} from '@angular/cdk/observers';\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {MatLegacyError} from './error';\nimport {MatLegacyFormField} from './form-field';\nimport {MatLegacyHint} from './hint';\nimport {MatLegacyLabel} from './label';\nimport {MatLegacyPlaceholder} from './placeholder';\nimport {MatLegacyPrefix} from './prefix';\nimport {MatLegacySuffix} from './suffix';\n\n/**\n * @deprecated Use `MatFormFieldModule` from `@angular/material/form-field` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\n@NgModule({\n  declarations: [\n    MatLegacyError,\n    MatLegacyFormField,\n    MatLegacyHint,\n    MatLegacyLabel,\n    MatLegacyPlaceholder,\n    MatLegacyPrefix,\n    MatLegacySuffix,\n  ],\n  imports: [CommonModule, MatCommonModule, ObserversModule],\n  exports: [\n    MatCommonModule,\n    MatLegacyError,\n    MatLegacyFormField,\n    MatLegacyHint,\n    MatLegacyLabel,\n    MatLegacyPlaceholder,\n    MatLegacyPrefix,\n    MatLegacySuffix,\n  ],\n})\nexport class MatLegacyFormFieldModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["nextUniqueId"], "mappings": ";;;;;;;;;;;;;;;;AAWA,IAAIA,cAAY,GAAG,CAAC,CAAC;AAErB;;;;AAIG;MAUU,cAAc,CAAA;IAGzB,WAAoC,CAAA,QAAgB,EAAE,UAAsB,EAAA;AAFnE,QAAA,IAAA,CAAA,EAAE,GAAW,CAAA,UAAA,EAAaA,cAAY,EAAE,EAAE,CAAC;;;QAKlD,IAAI,CAAC,QAAQ,EAAE;YACb,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC9D,SAAA;KACF;AATU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,kBAGF,WAAW,EAAA,SAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAHvB,cAAc,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EAFd,CAAC,EAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAEnD,cAAc,EAAA,UAAA,EAAA,CAAA;kBAT1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,WAAW;AACpB,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,aAAa,EAAE,MAAM;AACtB,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAgB,cAAA,EAAC,CAAC;AAC/D,iBAAA,CAAA;;0BAIc,SAAS;2BAAC,WAAW,CAAA;qEAFzB,EAAE,EAAA,CAAA;sBAAV,KAAK;;;AClBR,IAAIA,cAAY,GAAG,CAAC,CAAC;AAErB;;;;;;;;;;AAUG;MACU,gBAAgB,GAAG,IAAI,cAAc,CAAgB,SAAS,EAAE;AAE7E;;;;AAIG;MAYU,aAAa,CAAA;AAX1B,IAAA,WAAA,GAAA;;QAaW,IAAK,CAAA,KAAA,GAAoB,OAAO,CAAC;;AAGjC,QAAA,IAAA,CAAA,EAAE,GAAW,CAAA,SAAA,EAAYA,cAAY,EAAE,EAAE,CAAC;AACpD,KAAA;8GANY,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAb,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,IAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAFb,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAEzD,aAAa,EAAA,UAAA,EAAA,CAAA;kBAXzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,UAAU;AACnB,wBAAA,iCAAiC,EAAE,iBAAiB;AACpD,wBAAA,WAAW,EAAE,IAAI;;AAEjB,wBAAA,cAAc,EAAE,MAAM;AACvB,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAe,aAAA,EAAC,CAAC;AACrE,iBAAA,CAAA;8BAGU,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGG,EAAE,EAAA,CAAA;sBAAV,KAAK;;;ACpCR;;;;AAIG;MAIU,cAAc,CAAA;8GAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,QAAA,EAAA,WAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAH1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACtB,iBAAA,CAAA;;;ACPD;;;;;AAKG;MAIU,oBAAoB,CAAA;8GAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAApB,oBAAoB,EAAA,QAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAHhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC5B,iBAAA,CAAA;;;ACoCD,IAAI,YAAY,GAAG,CAAC,CAAC;AACrB,MAAM,kBAAkB,GAAG,IAAI,CAAC;AAChC,MAAM,iBAAiB,GAAG,CAAC,CAAC;AAE5B;;;AAGG;AACH,MAAM,iBAAiB,GAAG,UAAU,CAClC,MAAA;AACE,IAAA,WAAA,CAAmB,WAAuB,EAAA;QAAvB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;KAAI;CAC/C,EACD,SAAS,CACV,CAAC;AAoCF;;;;;AAKG;MACU,qCAAqC,GAChD,IAAI,cAAc,CAAmC,gCAAgC,EAAE;AAEzF;;;;AAIG;AA8CG,MAAO,kBACX,SAAQ,iBAAiB,CAAA;;AAezB,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IACD,IAAI,UAAU,CAAC,KAAmC,EAAA;AAChD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;AAElC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,UAAU,IAAI,QAAQ,CAAC;QAEnE,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,QAAQ,KAAK,KAAK,EAAE;AACxD,YAAA,IAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC;AAClD,SAAA;KACF;;AAID,IAAA,IACI,kBAAkB,GAAA;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAC;KACjC;IACD,IAAI,kBAAkB,CAAC,KAAmB,EAAA;AACxC,QAAA,IAAI,CAAC,mBAAmB,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KACzD;;IAOD,kBAAkB,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC;KACjE;;IAGD,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,UAAU,KAAK,OAAO,CAAC;KACpC;;AAMD,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IACD,IAAI,SAAS,CAAC,KAAa,EAAA;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;AASD;;;;;;;AAOG;AACH,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,GAAG,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;KACjG;IACD,IAAI,UAAU,CAAC,KAA2B,EAAA;AACxC,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,WAAW,EAAE;YAC9B,IAAI,CAAC,WAAW,GAAG,KAAK,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAC9D,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACxC,SAAA;KACF;AAYD,IAAA,IAAI,QAAQ,GAAA;;;QAGV,OAAO,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC;KACxF;IACD,IAAI,QAAQ,CAAC,KAAK,EAAA;AAChB,QAAA,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;KACxC;AAcD,IAAA,WAAA,CACE,UAAsB,EACd,kBAAqC,EACzB,IAAoB,EAGhC,SAA2C,EAC3C,SAAmB,EACnB,OAAe,EACoB,cAAsB,EAAA;QAEjE,KAAK,CAAC,UAAU,CAAC,CAAC;QATV,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACzB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;QAGhC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAkC;QAC3C,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;AAhIzB;;;AAGG;QACK,IAAuC,CAAA,uCAAA,GAAG,KAAK,CAAC;;QAGhD,IAAoC,CAAA,oCAAA,GAAG,KAAK,CAAC;AAEpC,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;QA0B1C,IAAmB,CAAA,mBAAA,GAAG,KAAK,CAAC;;QAG5B,IAAkB,CAAA,kBAAA,GAAG,KAAK,CAAC;;QAanC,IAAwB,CAAA,wBAAA,GAAW,EAAE,CAAC;QAW9B,IAAU,CAAA,UAAA,GAAG,EAAE,CAAC;;AAGf,QAAA,IAAA,CAAA,YAAY,GAAW,CAAA,SAAA,EAAY,YAAY,EAAE,EAAE,CAAC;;AAGpD,QAAA,IAAA,CAAA,QAAQ,GAAG,CAAA,qBAAA,EAAwB,YAAY,EAAE,EAAE,CAAC;AAiE3D,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;AACpD,QAAA,IAAI,CAAC,kBAAkB,GAAG,cAAc,KAAK,gBAAgB,CAAC;;QAG9D,IAAI,CAAC,UAAU,GAAG,SAAS,EAAE,UAAU,IAAI,QAAQ,CAAC;AACpD,QAAA,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YACjE,IAAI,SAAS,CAAC,KAAK,EAAE;gBACnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC;AAClD,aAAA;AACF,SAAA;KACF;AAED;;AAEG;IACH,UAAU,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;KACxD;AAED;;;AAGG;IACH,yBAAyB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,WAAW,CAAC;KACzD;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAE7B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE9B,IAAI,OAAO,CAAC,WAAW,EAAE;AACvB,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,OAAO,CAAC,WAAW,CAAA,CAAE,CAAC,CAAC;AAC5F,SAAA;;AAGD,QAAA,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YACxD,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;;QAGH,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE;YACvD,OAAO,CAAC,SAAS,CAAC,YAAY;AAC3B,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAChC,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC,CAAC;AAC5D,SAAA;;;;AAKD,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;gBACpE,IAAI,IAAI,CAAC,oCAAoC,EAAE;oBAC7C,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACzB,iBAAA;AACH,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;;AAGH,QAAA,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,MAAK;AAC/E,YAAA,IAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC;AACjD,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;;AAGH,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YAC9D,IAAI,CAAC,aAAa,EAAE,CAAC;AACrB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;;AAGH,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YAC/D,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AAC/D,gBAAA,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;AAC/C,oBAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;wBAClC,qBAAqB,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;AACvD,qBAAC,CAAC,CAAC;AACJ,iBAAA;AAAM,qBAAA;oBACL,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACzB,iBAAA;AACH,aAAC,CAAC,CAAC;AACJ,SAAA;KACF;IAED,qBAAqB,GAAA;QACnB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,uCAAuC,EAAE;YAChD,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACzB,SAAA;KACF;IAED,eAAe,GAAA;;AAEb,QAAA,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC;AACxC,QAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;KACzC;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;AAED;;;AAGG;AACH,IAAA,cAAc,CAAC,IAAoC,EAAA;AACjD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;AAC/D,QAAA,OAAO,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;KACjC;IAED,eAAe,GAAA;AACb,QAAA,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,KAAK,IAAI,CAAC,iBAAiB,CAAC,CAAC;KACnF;IAED,SAAS,GAAA;QACP,OAAO,CAAC,EAAE,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAChE;IAED,iBAAiB,GAAA;AACf,QAAA,QACE,IAAI,CAAC,cAAc,EAAE;AACrB,aAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,KAAK,IAAI,CAAC,kBAAkB,EAAE,CAAC,EAChF;KACH;IAED,uBAAuB,GAAA;;AAErB,QAAA,QACE,CAAC,IAAI,CAAC,UAAU,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AAClD,aAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAC/C;KACH;IAED,iBAAiB,GAAA;;AAEf,QAAA,OAAO,IAAI,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,UAAU,KAAK,QAAQ,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;KACrF;;IAGD,qBAAqB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU;AACtF,cAAE,OAAO;cACP,MAAM,CAAC;KACZ;;IAGD,oBAAoB,GAAA;QAClB,IAAI,IAAI,CAAC,iBAAiB,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;;;AAGrD,YAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,EAAE;AAC1C,gBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAE/B,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,eAAe,CAAC;AAClD,qBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;qBACb,SAAS,CAAC,MAAK;AACd,oBAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;AAClC,iBAAC,CAAC,CAAC;AACN,aAAA;AAED,YAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;AAC3B,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACxC,SAAA;KACF;AAED;;;AAGG;IACK,qBAAqB,GAAA;AAC3B,QAAA,IACE,IAAI,CAAC,QAAQ,CAAC,WAAW;AACzB,YAAA,IAAI,CAAC,iBAAiB;AACtB,aAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;YACA,MAAM,uCAAuC,EAAE,CAAC;AACjD,SAAA;KACF;;IAGO,aAAa,GAAA;QACnB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;AAED;;;AAGG;IACK,cAAc,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACzE,YAAA,IAAI,SAAwB,CAAC;AAC7B,YAAA,IAAI,OAAsB,CAAC;YAC3B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAmB,KAAI;AACjD,gBAAA,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,EAAE;AAC1B,oBAAA,IAAI,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;AAC/B,wBAAA,MAAM,kCAAkC,CAAC,OAAO,CAAC,CAAC;AACnD,qBAAA;oBACD,SAAS,GAAG,IAAI,CAAC;AAClB,iBAAA;AAAM,qBAAA,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;AAC/B,oBAAA,IAAI,OAAO,EAAE;AACX,wBAAA,MAAM,kCAAkC,CAAC,KAAK,CAAC,CAAC;AACjD,qBAAA;oBACD,OAAO,GAAG,IAAI,CAAC;AAChB,iBAAA;AACH,aAAC,CAAC,CAAC;AACJ,SAAA;KACF;;IAGO,0BAA0B,GAAA;AAChC,QAAA,OAAO,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,MAAM,CAAC;KAChE;AAED;;;AAGG;IACK,mBAAmB,GAAA;QACzB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,GAAG,GAAa,EAAE,CAAC;;AAGvB,YAAA,IACE,IAAI,CAAC,QAAQ,CAAC,mBAAmB;AACjC,gBAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,KAAK,QAAQ,EACrD;AACA,gBAAA,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3D,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE,KAAK,MAAM,EAAE;AAC3C,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa;AAClC,sBAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,CAAC;sBACvD,IAAI,CAAC;AACT,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa;AAChC,sBAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC;sBACrD,IAAI,CAAC;AAET,gBAAA,IAAI,SAAS,EAAE;AACb,oBAAA,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AACxB,iBAAA;qBAAM,IAAI,IAAI,CAAC,UAAU,EAAE;AAC1B,oBAAA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC7B,iBAAA;AAED,gBAAA,IAAI,OAAO,EAAE;AACX,oBAAA,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACtB,iBAAA;AACF,aAAA;iBAAM,IAAI,IAAI,CAAC,cAAc,EAAE;AAC9B,gBAAA,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AACzD,aAAA;AAED,YAAA,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;AACtC,SAAA;KACF;;IAGS,qBAAqB,GAAA;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACrE,MAAM,kCAAkC,EAAE,CAAC;AAC5C,SAAA;KACF;AAED;;;AAGG;IACH,gBAAgB,GAAA;AACd,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;AAC/D,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC;QAC7D,MAAM,oBAAoB,GAAG,+BAA+B,CAAC;QAC7D,MAAM,kBAAkB,GAAG,6BAA6B,CAAC;;AAGzD,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC9D,OAAO;AACR,SAAA;;AAGD,QAAA,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,WAAY,CAAC,IAAI,EAAE,EAAE;AACxE,YAAA,MAAM,WAAW,GAAG,SAAS,CAAC,gBAAgB,CAC5C,CAAG,EAAA,oBAAoB,CAAK,EAAA,EAAA,kBAAkB,CAAE,CAAA,CACjD,CAAC;AACF,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3C,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;AAClC,aAAA;YACD,OAAO;AACR,SAAA;;;AAID,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE;AAC5B,YAAA,IAAI,CAAC,uCAAuC,GAAG,IAAI,CAAC;YACpD,OAAO;AACR,SAAA;QAED,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,MAAM,QAAQ,GAAG,SAAS,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;QAClE,MAAM,MAAM,GAAG,SAAS,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;AAE9D,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,EAAE;AAC5D,YAAA,MAAM,aAAa,GAAG,SAAS,CAAC,qBAAqB,EAAE,CAAC;;;;;;;YAQxD,IAAI,aAAa,CAAC,KAAK,KAAK,CAAC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3D,gBAAA,IAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC;AACjD,gBAAA,IAAI,CAAC,uCAAuC,GAAG,KAAK,CAAC;gBACrD,OAAO;AACR,aAAA;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AACxD,YAAA,MAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC;AACvC,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC;YAC/E,IAAI,UAAU,GAAG,CAAC,CAAC;AAEnB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,gBAAA,UAAU,IAAK,aAAa,CAAC,CAAC,CAAiB,CAAC,WAAW,CAAC;AAC7D,aAAA;YACD,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,cAAc,CAAC,GAAG,iBAAiB,CAAC;AACvE,YAAA,QAAQ,GAAG,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,kBAAkB,GAAG,iBAAiB,GAAG,CAAC,GAAG,CAAC,CAAC;AACzF,SAAA;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,UAAU,CAAA,EAAA,CAAI,CAAC;AAC7C,SAAA;AACD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,QAAQ,CAAA,EAAA,CAAI,CAAC;AACzC,SAAA;AAED,QAAA,IAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC,uCAAuC;AACtF,YAAA,KAAK,CAAC;KACT;;AAGO,IAAA,YAAY,CAAC,IAAgB,EAAA;QACnC,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;KACxE;;IAGO,gBAAgB,GAAA;AACtB,QAAA,MAAM,OAAO,GAAgB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;QAE5D,IAAI,OAAO,CAAC,WAAW,EAAE;AACvB,YAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;;;AAGvC,YAAA,OAAO,QAAQ,IAAI,QAAQ,KAAK,OAAO,CAAC;AACzC,SAAA;;;QAID,OAAO,QAAQ,CAAC,eAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;KACpD;8GAzfU,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAiInB,qCAAqC,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAIzB,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AArIhC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,o5CAFlB,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,kBAAkB,EAAC,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAuGzD,mBAAmB,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EACnB,mBAAmB,EAWnB,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,sBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,cAAc,EACd,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,cAAc,kGACd,oBAAoB,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,SAAA,EAEjB,SAAS,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,SAAA,EACT,gBAAgB,EAChB,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,UAAU,EAEV,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,UAAU,0aC3R7B,k/HA4FA,EAAA,MAAA,EAAA,CAAA,ygJAAA,EAAA,4sCAAA,EAAA,ylJAAA,EAAA,24CAAA,EAAA,o0GAAA,EAAA,6oCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,YAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EDwCc,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FA8B5C,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBA7C9B,SAAS;+BACE,gBAAgB,EAAA,QAAA,EAChB,cAAc,EAaZ,UAAA,EAAA,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,EACjD,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,gBAAgB;AACzB,wBAAA,4CAA4C,EAAE,0BAA0B;AACxE,wBAAA,wCAAwC,EAAE,sBAAsB;AAChE,wBAAA,2CAA2C,EAAE,yBAAyB;AACtE,wBAAA,0CAA0C,EAAE,wBAAwB;AACpE,wBAAA,gCAAgC,EAAE,qBAAqB;AACvD,wBAAA,kCAAkC,EAAE,kBAAkB;AACtD,wBAAA,qCAAqC,EAAE,qBAAqB;AAC5D,wBAAA,kCAAkC,EAAE,qBAAqB;AACzD,wBAAA,yCAAyC,EAAE,2BAA2B;AACtE,wBAAA,iCAAiC,EAAE,mBAAmB;AACtD,wBAAA,mCAAmC,EAAE,qBAAqB;AAC1D,wBAAA,qBAAqB,EAAE,kBAAkB;AACzC,wBAAA,sBAAsB,EAAE,6BAA6B;AACrD,wBAAA,oBAAoB,EAAE,2BAA2B;AACjD,wBAAA,qBAAqB,EAAE,4BAA4B;AACnD,wBAAA,kBAAkB,EAAE,yBAAyB;AAC7C,wBAAA,kBAAkB,EAAE,yBAAyB;AAC7C,wBAAA,oBAAoB,EAAE,2BAA2B;AACjD,wBAAA,oBAAoB,EAAE,2BAA2B;AACjD,wBAAA,iCAAiC,EAAE,qBAAqB;AACxD,wBAAA,iBAAiB,EAAE,EAAE;qBACtB,EACO,MAAA,EAAA,CAAC,OAAO,CAAC,EAAA,aAAA,EACF,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAoB,kBAAA,EAAC,CAAC,EAAA,QAAA,EAAA,k/HAAA,EAAA,MAAA,EAAA,CAAA,ygJAAA,EAAA,4sCAAA,EAAA,ylJAAA,EAAA,24CAAA,EAAA,o0GAAA,EAAA,6oCAAA,CAAA,EAAA,CAAA;;0BAiIpE,QAAQ;;0BACR,QAAQ;;0BACR,MAAM;2BAAC,qCAAqC,CAAA;;0BAI5C,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;4CApHvC,UAAU,EAAA,CAAA;sBADb,KAAK;gBAiBF,kBAAkB,EAAA,CAAA;sBADrB,KAAK;gBA2BF,SAAS,EAAA,CAAA;sBADZ,KAAK;gBAyBF,UAAU,EAAA,CAAA;sBADb,KAAK;gBAe4C,uBAAuB,EAAA,CAAA;sBAAxE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,qBAAqB,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBACnB,kBAAkB,EAAA,CAAA;sBAA9C,SAAS;uBAAC,gBAAgB,CAAA;gBACC,MAAM,EAAA,CAAA;sBAAjC,SAAS;uBAAC,OAAO,CAAA;gBAEiB,iBAAiB,EAAA,CAAA;sBAAnD,YAAY;uBAAC,mBAAmB,CAAA;gBACkB,cAAc,EAAA,CAAA;sBAAhE,YAAY;AAAC,gBAAA,IAAA,EAAA,CAAA,mBAAmB,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBAWnB,oBAAoB,EAAA,CAAA;sBAAjD,YAAY;uBAAC,cAAc,CAAA;gBACkB,iBAAiB,EAAA,CAAA;sBAA9D,YAAY;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBACR,iBAAiB,EAAA,CAAA;sBAApD,YAAY;uBAAC,oBAAoB,CAAA;gBAEe,cAAc,EAAA,CAAA;sBAA9D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBACS,aAAa,EAAA,CAAA;sBAApE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,gBAAgB,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBAEtD,eAAe,EAAA,CAAA;sBADd,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBAGhD,eAAe,EAAA,CAAA;sBADd,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;;;AEhRlD;;;;AAIG;MAKU,eAAe,CAAA;8GAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAf,eAAe,EAAA,QAAA,EAAA,aAAA,EAAA,SAAA,EAFf,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,eAAe,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAErD,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;oBACvB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAiB,eAAA,EAAC,CAAC;AACjE,iBAAA,CAAA;;;ACRD;;;;AAIG;MAKU,eAAe,CAAA;8GAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAf,eAAe,EAAA,QAAA,EAAA,aAAA,EAAA,SAAA,EAFf,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,eAAe,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAErD,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;oBACvB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAiB,eAAA,EAAC,CAAC;AACjE,iBAAA,CAAA;;;ACCD;;;AAGG;MAuBU,wBAAwB,CAAA;8GAAxB,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAxB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,wBAAwB,iBApBjC,cAAc;YACd,kBAAkB;YAClB,aAAa;YACb,cAAc;YACd,oBAAoB;YACpB,eAAe;AACf,YAAA,eAAe,aAEP,YAAY,EAAE,eAAe,EAAE,eAAe,aAEtD,eAAe;YACf,cAAc;YACd,kBAAkB;YAClB,aAAa;YACb,cAAc;YACd,oBAAoB;YACpB,eAAe;YACf,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;AAGN,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,wBAAwB,YAZzB,YAAY,EAAE,eAAe,EAAE,eAAe,EAEtD,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAUN,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBAtBpC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,YAAY,EAAE;wBACZ,cAAc;wBACd,kBAAkB;wBAClB,aAAa;wBACb,cAAc;wBACd,oBAAoB;wBACpB,eAAe;wBACf,eAAe;AAChB,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,eAAe,CAAC;AACzD,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,cAAc;wBACd,kBAAkB;wBAClB,aAAa;wBACb,cAAc;wBACd,oBAAoB;wBACpB,eAAe;wBACf,eAAe;AAChB,qBAAA;AACF,iBAAA,CAAA;;;AC7CD;;AAEG;;;;"}