{"ast": null, "code": "import _asyncToGenerator from \"C:/e-syndic/esyndic-velzon/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*! shepherd.js 11.2.0 */\n\nvar isMergeableObject = function isMergeableObject(value) {\n  return isNonNullObject(value) && !isSpecial(value);\n};\nfunction isNonNullObject(value) {\n  return !!value && typeof value === 'object';\n}\nfunction isSpecial(value) {\n  var stringValue = Object.prototype.toString.call(value);\n  return stringValue === '[object RegExp]' || stringValue === '[object Date]' || isReactElement(value);\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\nfunction isReactElement(value) {\n  return value.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction emptyTarget(val) {\n  return Array.isArray(val) ? [] : {};\n}\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n  return options.clone !== false && options.isMergeableObject(value) ? deepmerge(emptyTarget(value), value, options) : value;\n}\nfunction defaultArrayMerge(target, source, options) {\n  return target.concat(source).map(function (element) {\n    return cloneUnlessOtherwiseSpecified(element, options);\n  });\n}\nfunction getMergeFunction(key, options) {\n  if (!options.customMerge) {\n    return deepmerge;\n  }\n  var customMerge = options.customMerge(key);\n  return typeof customMerge === 'function' ? customMerge : deepmerge;\n}\nfunction getEnumerableOwnPropertySymbols(target) {\n  return Object.getOwnPropertySymbols ? Object.getOwnPropertySymbols(target).filter(function (symbol) {\n    return Object.propertyIsEnumerable.call(target, symbol);\n  }) : [];\n}\nfunction getKeys(target) {\n  return Object.keys(target).concat(getEnumerableOwnPropertySymbols(target));\n}\nfunction propertyIsOnObject(object, property) {\n  try {\n    return property in object;\n  } catch (_) {\n    return false;\n  }\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n  return propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n  && !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n  && Object.propertyIsEnumerable.call(target, key)); // and also unsafe if they're nonenumerable.\n}\nfunction mergeObject(target, source, options) {\n  var destination = {};\n  if (options.isMergeableObject(target)) {\n    getKeys(target).forEach(function (key) {\n      destination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n    });\n  }\n  getKeys(source).forEach(function (key) {\n    if (propertyIsUnsafe(target, key)) {\n      return;\n    }\n    if (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n      destination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n    } else {\n      destination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n    }\n  });\n  return destination;\n}\nfunction deepmerge(target, source, options) {\n  options = options || {};\n  options.arrayMerge = options.arrayMerge || defaultArrayMerge;\n  options.isMergeableObject = options.isMergeableObject || isMergeableObject;\n  // cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n  // implementations can use it. The caller may not replace it.\n  options.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n  var sourceIsArray = Array.isArray(source);\n  var targetIsArray = Array.isArray(target);\n  var sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n  if (!sourceAndTargetTypesMatch) {\n    return cloneUnlessOtherwiseSpecified(source, options);\n  } else if (sourceIsArray) {\n    return options.arrayMerge(target, source, options);\n  } else {\n    return mergeObject(target, source, options);\n  }\n}\ndeepmerge.all = function deepmergeAll(array, options) {\n  if (!Array.isArray(array)) {\n    throw new Error('first argument should be an array');\n  }\n  return array.reduce(function (prev, next) {\n    return deepmerge(prev, next, options);\n  }, {});\n};\nvar deepmerge_1 = deepmerge;\nvar cjs = deepmerge_1;\n\n/**\n * Checks if `value` is classified as an `Element`.\n * @param {*} value The param to check if it is an Element\n */\nfunction isElement$1(value) {\n  return value instanceof Element;\n}\n\n/**\n * Checks if `value` is classified as an `HTMLElement`.\n * @param {*} value The param to check if it is an HTMLElement\n */\nfunction isHTMLElement$1(value) {\n  return value instanceof HTMLElement;\n}\n\n/**\n * Checks if `value` is classified as a `Function` object.\n * @param {*} value The param to check if it is a function\n */\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\n/**\n * Checks if `value` is classified as a `String` object.\n * @param {*} value The param to check if it is a string\n */\nfunction isString(value) {\n  return typeof value === 'string';\n}\n\n/**\n * Checks if `value` is undefined.\n * @param {*} value The param to check if it is undefined\n */\nfunction isUndefined(value) {\n  return value === undefined;\n}\nclass Evented {\n  on(event, handler, ctx, once = false) {\n    if (isUndefined(this.bindings)) {\n      this.bindings = {};\n    }\n    if (isUndefined(this.bindings[event])) {\n      this.bindings[event] = [];\n    }\n    this.bindings[event].push({\n      handler,\n      ctx,\n      once\n    });\n    return this;\n  }\n  once(event, handler, ctx) {\n    return this.on(event, handler, ctx, true);\n  }\n  off(event, handler) {\n    if (isUndefined(this.bindings) || isUndefined(this.bindings[event])) {\n      return this;\n    }\n    if (isUndefined(handler)) {\n      delete this.bindings[event];\n    } else {\n      this.bindings[event].forEach((binding, index) => {\n        if (binding.handler === handler) {\n          this.bindings[event].splice(index, 1);\n        }\n      });\n    }\n    return this;\n  }\n  trigger(event, ...args) {\n    if (!isUndefined(this.bindings) && this.bindings[event]) {\n      this.bindings[event].forEach((binding, index) => {\n        const {\n          ctx,\n          handler,\n          once\n        } = binding;\n        const context = ctx || this;\n        handler.apply(context, args);\n        if (once) {\n          this.bindings[event].splice(index, 1);\n        }\n      });\n    }\n    return this;\n  }\n}\n\n/**\n * Binds all the methods on a JS Class to the `this` context of the class.\n * Adapted from https://github.com/sindresorhus/auto-bind\n * @param {object} self The `this` context of the class\n * @return {object} The `this` context of the class\n */\nfunction autoBind(self) {\n  const keys = Object.getOwnPropertyNames(self.constructor.prototype);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    const val = self[key];\n    if (key !== 'constructor' && typeof val === 'function') {\n      self[key] = val.bind(self);\n    }\n  }\n  return self;\n}\n\n/**\n * Sets up the handler to determine if we should advance the tour\n * @param {string} selector\n * @param {Step} step The step instance\n * @return {Function}\n * @private\n */\nfunction _setupAdvanceOnHandler(selector, step) {\n  return event => {\n    if (step.isOpen()) {\n      const targetIsEl = step.el && event.currentTarget === step.el;\n      const targetIsSelector = !isUndefined(selector) && event.currentTarget.matches(selector);\n      if (targetIsSelector || targetIsEl) {\n        step.tour.next();\n      }\n    }\n  };\n}\n\n/**\n * Bind the event handler for advanceOn\n * @param {Step} step The step instance\n */\nfunction bindAdvance(step) {\n  // An empty selector matches the step element\n  const {\n    event,\n    selector\n  } = step.options.advanceOn || {};\n  if (event) {\n    const handler = _setupAdvanceOnHandler(selector, step);\n\n    // TODO: this should also bind/unbind on show/hide\n    let el;\n    try {\n      el = document.querySelector(selector);\n    } catch (e) {\n      // TODO\n    }\n    if (!isUndefined(selector) && !el) {\n      return console.error(`No element was found for the selector supplied to advanceOn: ${selector}`);\n    } else if (el) {\n      el.addEventListener(event, handler);\n      step.on('destroy', () => {\n        return el.removeEventListener(event, handler);\n      });\n    } else {\n      document.body.addEventListener(event, handler, true);\n      step.on('destroy', () => {\n        return document.body.removeEventListener(event, handler, true);\n      });\n    }\n  } else {\n    return console.error('advanceOn was defined, but no event name was passed.');\n  }\n}\n\n/**\n * Ensure class prefix ends in `-`\n * @param {string} prefix The prefix to prepend to the class names generated by nano-css\n * @return {string} The prefix ending in `-`\n */\nfunction normalizePrefix(prefix) {\n  if (!isString(prefix) || prefix === '') {\n    return '';\n  }\n  return prefix.charAt(prefix.length - 1) !== '-' ? `${prefix}-` : prefix;\n}\n\n/**\n * Resolves attachTo options, converting element option value to a qualified HTMLElement.\n * @param {Step} step The step instance\n * @returns {{}|{element, on}}\n * `element` is a qualified HTML Element\n * `on` is a string position value\n */\nfunction parseAttachTo(step) {\n  const options = step.options.attachTo || {};\n  const returnOpts = Object.assign({}, options);\n  if (isFunction(returnOpts.element)) {\n    // Bind the callback to step so that it has access to the object, to enable running additional logic\n    returnOpts.element = returnOpts.element.call(step);\n  }\n  if (isString(returnOpts.element)) {\n    // Can't override the element in user opts reference because we can't\n    // guarantee that the element will exist in the future.\n    try {\n      returnOpts.element = document.querySelector(returnOpts.element);\n    } catch (e) {\n      // TODO\n    }\n    if (!returnOpts.element) {\n      console.error(`The element for this Shepherd step was not found ${options.element}`);\n    }\n  }\n  return returnOpts;\n}\n\n/**\n * Checks if the step should be centered or not. Does not trigger attachTo.element evaluation, making it a pure\n * alternative for the deprecated step.isCentered() method.\n * @param resolvedAttachToOptions\n * @returns {boolean}\n */\nfunction shouldCenterStep(resolvedAttachToOptions) {\n  if (resolvedAttachToOptions === undefined || resolvedAttachToOptions === null) {\n    return true;\n  }\n  return !resolvedAttachToOptions.element || !resolvedAttachToOptions.on;\n}\n\n/**\n * Create a unique id for steps, tours, modals, etc\n * @return {string}\n */\nfunction uuid() {\n  let d = Date.now();\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = (d + Math.random() * 16) % 16 | 0;\n    d = Math.floor(d / 16);\n    return (c == 'x' ? r : r & 0x3 | 0x8).toString(16);\n  });\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return _extends({\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  }, padding);\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  return _extends({}, rect, {\n    top: rect.y,\n    left: rect.x,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}\nconst _excluded2 = [\"mainAxis\", \"crossAxis\", \"fallbackPlacements\", \"fallbackStrategy\", \"fallbackAxisSideDirection\", \"flipAlignment\"],\n  _excluded4 = [\"mainAxis\", \"crossAxis\", \"limiter\"];\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a reference element when it is given a certain positioning strategy.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition$1 = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (reference, floating, config) {\n    const {\n      placement = 'bottom',\n      strategy = 'absolute',\n      middleware = [],\n      platform\n    } = config;\n    const validMiddleware = middleware.filter(Boolean);\n    const rtl = yield platform.isRTL == null ? void 0 : platform.isRTL(floating);\n    let rects = yield platform.getElementRects({\n      reference,\n      floating,\n      strategy\n    });\n    let {\n      x,\n      y\n    } = computeCoordsFromPlacement(rects, placement, rtl);\n    let statefulPlacement = placement;\n    let middlewareData = {};\n    let resetCount = 0;\n    for (let i = 0; i < validMiddleware.length; i++) {\n      const {\n        name,\n        fn\n      } = validMiddleware[i];\n      const {\n        x: nextX,\n        y: nextY,\n        data,\n        reset\n      } = yield fn({\n        x,\n        y,\n        initialPlacement: placement,\n        placement: statefulPlacement,\n        strategy,\n        middlewareData,\n        rects,\n        platform,\n        elements: {\n          reference,\n          floating\n        }\n      });\n      x = nextX != null ? nextX : x;\n      y = nextY != null ? nextY : y;\n      middlewareData = _extends({}, middlewareData, {\n        [name]: _extends({}, middlewareData[name], data)\n      });\n      if (reset && resetCount <= 50) {\n        resetCount++;\n        if (typeof reset === 'object') {\n          if (reset.placement) {\n            statefulPlacement = reset.placement;\n          }\n          if (reset.rects) {\n            rects = reset.rects === true ? yield platform.getElementRects({\n              reference,\n              floating,\n              strategy\n            }) : reset.rects;\n          }\n          ({\n            x,\n            y\n          } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n        }\n        i = -1;\n        continue;\n      }\n    }\n    return {\n      x,\n      y,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData\n    };\n  });\n  return function computePosition$1(_x, _x2, _x3) {\n    return _ref2.apply(this, arguments);\n  };\n}();\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nfunction detectOverflow(_x4, _x5) {\n  return _detectOverflow.apply(this, arguments);\n}\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nfunction _detectOverflow() {\n  _detectOverflow = _asyncToGenerator(function* (state, options) {\n    var _await$platform$isEle;\n    if (options === void 0) {\n      options = {};\n    }\n    const {\n      x,\n      y,\n      platform,\n      rects,\n      elements,\n      strategy\n    } = state;\n    const {\n      boundary = 'clippingAncestors',\n      rootBoundary = 'viewport',\n      elementContext = 'floating',\n      altBoundary = false,\n      padding = 0\n    } = evaluate(options, state);\n    const paddingObject = getPaddingObject(padding);\n    const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n    const element = elements[altBoundary ? altContext : elementContext];\n    const clippingClientRect = rectToClientRect(yield platform.getClippingRect({\n      element: ((_await$platform$isEle = yield platform.isElement == null ? void 0 : platform.isElement(element)) != null ? _await$platform$isEle : true) ? element : element.contextElement || (yield platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating)),\n      boundary,\n      rootBoundary,\n      strategy\n    }));\n    const rect = elementContext === 'floating' ? _extends({}, rects.floating, {\n      x,\n      y\n    }) : rects.reference;\n    const offsetParent = yield platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating);\n    const offsetScale = (yield platform.isElement == null ? void 0 : platform.isElement(offsetParent)) ? (yield platform.getScale == null ? void 0 : platform.getScale(offsetParent)) || {\n      x: 1,\n      y: 1\n    } : {\n      x: 1,\n      y: 1\n    };\n    const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? yield platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n      rect,\n      offsetParent,\n      strategy\n    }) : rect);\n    return {\n      top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n      bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n      left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n      right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n    };\n  });\n  return _detectOverflow.apply(this, arguments);\n}\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  fn(state) {\n    return _asyncToGenerator(function* () {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      // Since `element` is required, we don't Partial<> the type.\n      const {\n        element,\n        padding = 0\n      } = evaluate(options, state) || {};\n      if (element == null) {\n        return {};\n      }\n      const paddingObject = getPaddingObject(padding);\n      const coords = {\n        x,\n        y\n      };\n      const axis = getAlignmentAxis(placement);\n      const length = getAxisLength(axis);\n      const arrowDimensions = yield platform.getDimensions(element);\n      const isYAxis = axis === 'y';\n      const minProp = isYAxis ? 'top' : 'left';\n      const maxProp = isYAxis ? 'bottom' : 'right';\n      const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n      const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n      const startDiff = coords[axis] - rects.reference[axis];\n      const arrowOffsetParent = yield platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element);\n      let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n      // DOM platform can return `window` as the `offsetParent`.\n      if (!clientSize || !(yield platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent))) {\n        clientSize = elements.floating[clientProp] || rects.floating[length];\n      }\n      const centerToReference = endDiff / 2 - startDiff / 2;\n\n      // If the padding is large enough that it causes the arrow to no longer be\n      // centered, modify the padding so that it is centered.\n      const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n      const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n      const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n      // Make sure the arrow doesn't overflow the floating element if the center\n      // point is outside the floating element's bounds.\n      const min$1 = minPadding;\n      const max = clientSize - arrowDimensions[length] - maxPadding;\n      const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n      const offset = clamp(min$1, center, max);\n\n      // If the reference is small enough that the arrow's padding causes it to\n      // to point to nothing for an aligned placement, adjust the offset of the\n      // floating element itself. This stops `shift()` from taking action, but can\n      // be worked around by calling it again after the `arrow()` if desired.\n      const shouldAddOffset = getAlignment(placement) != null && center != offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n      const alignmentOffset = shouldAddOffset ? center < min$1 ? min$1 - center : max - center : 0;\n      return {\n        [axis]: coords[axis] - alignmentOffset,\n        data: {\n          [axis]: offset,\n          centerOffset: center - offset + alignmentOffset\n        }\n      };\n    })();\n  }\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function flip(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    fn(state) {\n      return _asyncToGenerator(function* () {\n        var _middlewareData$flip;\n        const {\n          placement,\n          middlewareData,\n          rects,\n          initialPlacement,\n          platform,\n          elements\n        } = state;\n        const _evaluate2 = evaluate(options, state),\n          {\n            mainAxis: checkMainAxis = true,\n            crossAxis: checkCrossAxis = true,\n            fallbackPlacements: specifiedFallbackPlacements,\n            fallbackStrategy = 'bestFit',\n            fallbackAxisSideDirection = 'none',\n            flipAlignment = true\n          } = _evaluate2,\n          detectOverflowOptions = _objectWithoutPropertiesLoose(_evaluate2, _excluded2);\n        const side = getSide(placement);\n        const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n        const rtl = yield platform.isRTL == null ? void 0 : platform.isRTL(elements.floating);\n        const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n        if (!specifiedFallbackPlacements && fallbackAxisSideDirection !== 'none') {\n          fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n        }\n        const placements = [initialPlacement, ...fallbackPlacements];\n        const overflow = yield detectOverflow(state, detectOverflowOptions);\n        const overflows = [];\n        let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n        if (checkMainAxis) {\n          overflows.push(overflow[side]);\n        }\n        if (checkCrossAxis) {\n          const sides = getAlignmentSides(placement, rects, rtl);\n          overflows.push(overflow[sides[0]], overflow[sides[1]]);\n        }\n        overflowsData = [...overflowsData, {\n          placement,\n          overflows\n        }];\n\n        // One or more sides is overflowing.\n        if (!overflows.every(side => side <= 0)) {\n          var _middlewareData$flip2, _overflowsData$filter;\n          const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n          const nextPlacement = placements[nextIndex];\n          if (nextPlacement) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n\n          // First, find the candidates that fit on the mainAxis side of overflow,\n          // then find the placement that fits the best on the main crossAxis side.\n          let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n          // Otherwise fallback.\n          if (!resetPlacement) {\n            switch (fallbackStrategy) {\n              case 'bestFit':\n                {\n                  var _overflowsData$map$so;\n                  const placement = (_overflowsData$map$so = overflowsData.map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$map$so[0];\n                  if (placement) {\n                    resetPlacement = placement;\n                  }\n                  break;\n                }\n              case 'initialPlacement':\n                resetPlacement = initialPlacement;\n                break;\n            }\n          }\n          if (placement !== resetPlacement) {\n            return {\n              reset: {\n                placement: resetPlacement\n              }\n            };\n          }\n        }\n        return {};\n      })();\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function shift(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    fn(state) {\n      return _asyncToGenerator(function* () {\n        const {\n          x,\n          y,\n          placement\n        } = state;\n        const _evaluate4 = evaluate(options, state),\n          {\n            mainAxis: checkMainAxis = true,\n            crossAxis: checkCrossAxis = false,\n            limiter = {\n              fn: _ref => {\n                let {\n                  x,\n                  y\n                } = _ref;\n                return {\n                  x,\n                  y\n                };\n              }\n            }\n          } = _evaluate4,\n          detectOverflowOptions = _objectWithoutPropertiesLoose(_evaluate4, _excluded4);\n        const coords = {\n          x,\n          y\n        };\n        const overflow = yield detectOverflow(state, detectOverflowOptions);\n        const crossAxis = getSideAxis(getSide(placement));\n        const mainAxis = getOppositeAxis(crossAxis);\n        let mainAxisCoord = coords[mainAxis];\n        let crossAxisCoord = coords[crossAxis];\n        if (checkMainAxis) {\n          const minSide = mainAxis === 'y' ? 'top' : 'left';\n          const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n          const min = mainAxisCoord + overflow[minSide];\n          const max = mainAxisCoord - overflow[maxSide];\n          mainAxisCoord = clamp(min, mainAxisCoord, max);\n        }\n        if (checkCrossAxis) {\n          const minSide = crossAxis === 'y' ? 'top' : 'left';\n          const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n          const min = crossAxisCoord + overflow[minSide];\n          const max = crossAxisCoord - overflow[maxSide];\n          crossAxisCoord = clamp(min, crossAxisCoord, max);\n        }\n        const limitedCoords = limiter.fn(_extends({}, state, {\n          [mainAxis]: mainAxisCoord,\n          [crossAxis]: crossAxisCoord\n        }));\n        return _extends({}, limitedCoords, {\n          data: {\n            x: limitedCoords.x - x,\n            y: limitedCoords.y - y\n          }\n        });\n      })();\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function limitShift(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : _extends({\n        mainAxis: 0,\n        crossAxis: 0\n      }, rawOffset);\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null ? void 0 : (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  // Browsers without `ShadowRoot` support.\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isContainingBlock(element) {\n  const webkit = isWebKit();\n  const css = getComputedStyle(element);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  return css.transform !== 'none' || css.perspective !== 'none' || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else {\n      currentNode = getParentNode(currentNode);\n    }\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.pageXOffset,\n    scrollTop: element.pageYOffset\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor));\n}\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentIFrame = win.frameElement;\n    while (currentIFrame && offsetParent && offsetWin !== win) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentIFrame = getWindow(currentIFrame).frameElement;\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  if (offsetParent === documentElement) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && strategy !== 'fixed') {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y\n  };\n}\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\nfunction getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  return getBoundingClientRect(getDocumentElement(element)).left + getNodeScroll(element).scrollLeft;\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = _extends({}, clippingAncestor, {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y\n    });\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\nfunction getDimensions(element) {\n  return getCssDimensions(element);\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  return element.offsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const window = getWindow(element);\n  if (!isHTMLElement(element)) {\n    return window;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static' && !isContainingBlock(offsetParent))) {\n    return window;\n  }\n  return offsetParent || getContainingBlock(element) || window;\n}\nconst getElementRects = /*#__PURE__*/function () {\n  var _getElementRects = _asyncToGenerator(function* (_ref) {\n    let {\n      reference,\n      floating,\n      strategy\n    } = _ref;\n    const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n    const getDimensionsFn = this.getDimensions;\n    return {\n      reference: getRectRelativeToOffsetParent(reference, yield getOffsetParentFn(floating), strategy),\n      floating: _extends({\n        x: 0,\n        y: 0\n      }, yield getDimensionsFn(floating))\n    };\n  });\n  function getElementRects(_x6) {\n    return _getElementRects.apply(this, arguments);\n  }\n  return getElementRects;\n}();\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    clearTimeout(timeoutId);\n    io && io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = element.getBoundingClientRect();\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 100);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, _extends({}, options, {\n        // Handle <iframe>s\n        root: root.ownerDocument\n      }));\n    } catch (e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          resizeObserver && resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && (nextRefRect.x !== prevRefRect.x || nextRefRect.y !== prevRefRect.y || nextRefRect.width !== prevRefRect.width || nextRefRect.height !== prevRefRect.height)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo && cleanupIo();\n    resizeObserver && resizeObserver.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a reference element when it is given a certain CSS positioning\n * strategy.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = _extends({\n    platform\n  }, options);\n  const platformWithCache = _extends({}, mergedOptions.platform, {\n    _c: cache\n  });\n  return computePosition$1(reference, floating, _extends({}, mergedOptions, {\n    platform: platformWithCache\n  }));\n};\n\n/**\n * Floating UI Options\n *\n * @typedef {object} FloatingUIOptions\n */\n\n/**\n * Determines options for the tooltip and initializes event listeners.\n *\n * @param {Step} step The step instance\n *\n * @return {FloatingUIOptions}\n */\nfunction setupTooltip(step) {\n  if (step.cleanup) {\n    step.cleanup();\n  }\n  const attachToOptions = step._getResolvedAttachToOptions();\n  let target = attachToOptions.element;\n  const floatingUIOptions = getFloatingUIOptions(attachToOptions, step);\n  const shouldCenter = shouldCenterStep(attachToOptions);\n  if (shouldCenter) {\n    target = document.body;\n    const content = step.shepherdElementComponent.getElement();\n    content.classList.add('shepherd-centered');\n  }\n  step.cleanup = autoUpdate(target, step.el, () => {\n    // The element might have already been removed by the end of the tour.\n    if (!step.el) {\n      step.cleanup();\n      return;\n    }\n    setPosition(target, step, floatingUIOptions, shouldCenter);\n  });\n  step.target = attachToOptions.element;\n  return floatingUIOptions;\n}\n\n/**\n * Merge tooltip options handling nested keys.\n *\n * @param tourOptions - The default tour options.\n * @param options - Step specific options.\n *\n * @return {floatingUIOptions: FloatingUIOptions}\n */\nfunction mergeTooltipConfig(tourOptions, options) {\n  return {\n    floatingUIOptions: cjs(tourOptions.floatingUIOptions || {}, options.floatingUIOptions || {})\n  };\n}\n\n/**\n * Cleanup function called when the step is closed/destroyed.\n *\n * @param {Step} step\n */\nfunction destroyTooltip(step) {\n  if (step.cleanup) {\n    step.cleanup();\n  }\n  step.cleanup = null;\n}\n\n/**\n *\n * @return {Promise<*>}\n */\nfunction setPosition(target, step, floatingUIOptions, shouldCenter) {\n  return computePosition(target, step.el, floatingUIOptions).then(floatingUIposition(step, shouldCenter))\n  // Wait before forcing focus.\n  .then(step => new Promise(resolve => {\n    setTimeout(() => resolve(step), 300);\n  }))\n  // Replaces focusAfterRender modifier.\n  .then(step => {\n    if (step && step.el) {\n      step.el.focus({\n        preventScroll: true\n      });\n    }\n  });\n}\n\n/**\n *\n * @param step\n * @param shouldCenter\n * @return {function({x: *, y: *, placement: *, middlewareData: *}): Promise<unknown>}\n */\nfunction floatingUIposition(step, shouldCenter) {\n  return ({\n    x,\n    y,\n    placement,\n    middlewareData\n  }) => {\n    if (!step.el) {\n      return step;\n    }\n    if (shouldCenter) {\n      Object.assign(step.el.style, {\n        position: 'fixed',\n        left: '50%',\n        top: '50%',\n        transform: 'translate(-50%, -50%)'\n      });\n    } else {\n      Object.assign(step.el.style, {\n        position: 'absolute',\n        left: `${x}px`,\n        top: `${y}px`\n      });\n    }\n    step.el.dataset.popperPlacement = placement;\n    placeArrow(step.el, middlewareData);\n    return step;\n  };\n}\n\n/**\n *\n * @param el\n * @param middlewareData\n */\nfunction placeArrow(el, middlewareData) {\n  const arrowEl = el.querySelector('.shepherd-arrow');\n  if (arrowEl && middlewareData.arrow) {\n    const {\n      x: arrowX,\n      y: arrowY\n    } = middlewareData.arrow;\n    Object.assign(arrowEl.style, {\n      left: arrowX != null ? `${arrowX}px` : '',\n      top: arrowY != null ? `${arrowY}px` : ''\n    });\n  }\n}\n\n/**\n * Gets the `Floating UI` options from a set of base `attachTo` options\n * @param attachToOptions\n * @param {Step} step The step instance\n * @return {Object}\n * @private\n */\nfunction getFloatingUIOptions(attachToOptions, step) {\n  const options = {\n    strategy: 'absolute',\n    middleware: []\n  };\n  const arrowEl = addArrow(step);\n  const shouldCenter = shouldCenterStep(attachToOptions);\n  if (!shouldCenter) {\n    options.middleware.push(flip(),\n    // Replicate PopperJS default behavior.\n    shift({\n      limiter: limitShift(),\n      crossAxis: true\n    }));\n    if (arrowEl) {\n      options.middleware.push(arrow({\n        element: arrowEl\n      }));\n    }\n    options.placement = attachToOptions.on;\n  }\n  return cjs(step.options.floatingUIOptions || {}, options);\n}\n\n/**\n * @param {Step} step\n * @return {HTMLElement|false|null}\n */\nfunction addArrow(step) {\n  if (step.options.arrow && step.el) {\n    return step.el.querySelector('.shepherd-arrow');\n  }\n  return false;\n}\nfunction noop() {}\nfunction assign(tar, src) {\n  // @ts-ignore\n  for (const k in src) tar[k] = src[k];\n  return tar;\n}\nfunction run(fn) {\n  return fn();\n}\nfunction blank_object() {\n  return Object.create(null);\n}\nfunction run_all(fns) {\n  fns.forEach(run);\n}\nfunction is_function(thing) {\n  return typeof thing === 'function';\n}\nfunction safe_not_equal(a, b) {\n  return a != a ? b == b : a !== b || a && typeof a === 'object' || typeof a === 'function';\n}\nfunction is_empty(obj) {\n  return Object.keys(obj).length === 0;\n}\nfunction append(target, node) {\n  target.appendChild(node);\n}\nfunction insert(target, node, anchor) {\n  target.insertBefore(node, anchor || null);\n}\nfunction detach(node) {\n  if (node.parentNode) {\n    node.parentNode.removeChild(node);\n  }\n}\nfunction destroy_each(iterations, detaching) {\n  for (let i = 0; i < iterations.length; i += 1) {\n    if (iterations[i]) iterations[i].d(detaching);\n  }\n}\nfunction element(name) {\n  return document.createElement(name);\n}\nfunction svg_element(name) {\n  return document.createElementNS('http://www.w3.org/2000/svg', name);\n}\nfunction text(data) {\n  return document.createTextNode(data);\n}\nfunction space() {\n  return text(' ');\n}\nfunction empty() {\n  return text('');\n}\nfunction listen(node, event, handler, options) {\n  node.addEventListener(event, handler, options);\n  return () => node.removeEventListener(event, handler, options);\n}\nfunction attr(node, attribute, value) {\n  if (value == null) node.removeAttribute(attribute);else if (node.getAttribute(attribute) !== value) node.setAttribute(attribute, value);\n}\n/**\n * List of attributes that should always be set through the attr method,\n * because updating them through the property setter doesn't work reliably.\n * In the example of `width`/`height`, the problem is that the setter only\n * accepts numeric values, but the attribute can also be set to a string like `50%`.\n * If this list becomes too big, rethink this approach.\n */\nconst always_set_through_set_attribute = ['width', 'height'];\nfunction set_attributes(node, attributes) {\n  // @ts-ignore\n  const descriptors = Object.getOwnPropertyDescriptors(node.__proto__);\n  for (const key in attributes) {\n    if (attributes[key] == null) {\n      node.removeAttribute(key);\n    } else if (key === 'style') {\n      node.style.cssText = attributes[key];\n    } else if (key === '__value') {\n      node.value = node[key] = attributes[key];\n    } else if (descriptors[key] && descriptors[key].set && always_set_through_set_attribute.indexOf(key) === -1) {\n      node[key] = attributes[key];\n    } else {\n      attr(node, key, attributes[key]);\n    }\n  }\n}\nfunction children(element) {\n  return Array.from(element.childNodes);\n}\nfunction toggle_class(element, name, toggle) {\n  element.classList[toggle ? 'add' : 'remove'](name);\n}\nlet current_component;\nfunction set_current_component(component) {\n  current_component = component;\n}\nfunction get_current_component() {\n  if (!current_component) throw new Error('Function called outside component initialization');\n  return current_component;\n}\n/**\n * The `onMount` function schedules a callback to run as soon as the component has been mounted to the DOM.\n * It must be called during the component's initialisation (but doesn't need to live *inside* the component;\n * it can be called from an external module).\n *\n * `onMount` does not run inside a [server-side component](/docs#run-time-server-side-component-api).\n *\n * https://svelte.dev/docs#run-time-svelte-onmount\n */\nfunction onMount(fn) {\n  get_current_component().$$.on_mount.push(fn);\n}\n/**\n * Schedules a callback to run immediately after the component has been updated.\n *\n * The first time the callback runs will be after the initial `onMount`\n */\nfunction afterUpdate(fn) {\n  get_current_component().$$.after_update.push(fn);\n}\nconst dirty_components = [];\nconst binding_callbacks = [];\nlet render_callbacks = [];\nconst flush_callbacks = [];\nconst resolved_promise = /* @__PURE__ */Promise.resolve();\nlet update_scheduled = false;\nfunction schedule_update() {\n  if (!update_scheduled) {\n    update_scheduled = true;\n    resolved_promise.then(flush);\n  }\n}\nfunction add_render_callback(fn) {\n  render_callbacks.push(fn);\n}\n// flush() calls callbacks in this order:\n// 1. All beforeUpdate callbacks, in order: parents before children\n// 2. All bind:this callbacks, in reverse order: children before parents.\n// 3. All afterUpdate callbacks, in order: parents before children. EXCEPT\n//    for afterUpdates called during the initial onMount, which are called in\n//    reverse order: children before parents.\n// Since callbacks might update component values, which could trigger another\n// call to flush(), the following steps guard against this:\n// 1. During beforeUpdate, any updated components will be added to the\n//    dirty_components array and will cause a reentrant call to flush(). Because\n//    the flush index is kept outside the function, the reentrant call will pick\n//    up where the earlier call left off and go through all dirty components. The\n//    current_component value is saved and restored so that the reentrant call will\n//    not interfere with the \"parent\" flush() call.\n// 2. bind:this callbacks cannot trigger new flush() calls.\n// 3. During afterUpdate, any updated components will NOT have their afterUpdate\n//    callback called a second time; the seen_callbacks set, outside the flush()\n//    function, guarantees this behavior.\nconst seen_callbacks = new Set();\nlet flushidx = 0; // Do *not* move this inside the flush() function\nfunction flush() {\n  // Do not reenter flush while dirty components are updated, as this can\n  // result in an infinite loop. Instead, let the inner flush handle it.\n  // Reentrancy is ok afterwards for bindings etc.\n  if (flushidx !== 0) {\n    return;\n  }\n  const saved_component = current_component;\n  do {\n    // first, call beforeUpdate functions\n    // and update components\n    try {\n      while (flushidx < dirty_components.length) {\n        const component = dirty_components[flushidx];\n        flushidx++;\n        set_current_component(component);\n        update(component.$$);\n      }\n    } catch (e) {\n      // reset dirty state to not end up in a deadlocked state and then rethrow\n      dirty_components.length = 0;\n      flushidx = 0;\n      throw e;\n    }\n    set_current_component(null);\n    dirty_components.length = 0;\n    flushidx = 0;\n    while (binding_callbacks.length) binding_callbacks.pop()();\n    // then, once components are updated, call\n    // afterUpdate functions. This may cause\n    // subsequent updates...\n    for (let i = 0; i < render_callbacks.length; i += 1) {\n      const callback = render_callbacks[i];\n      if (!seen_callbacks.has(callback)) {\n        // ...so guard against infinite loops\n        seen_callbacks.add(callback);\n        callback();\n      }\n    }\n    render_callbacks.length = 0;\n  } while (dirty_components.length);\n  while (flush_callbacks.length) {\n    flush_callbacks.pop()();\n  }\n  update_scheduled = false;\n  seen_callbacks.clear();\n  set_current_component(saved_component);\n}\nfunction update($$) {\n  if ($$.fragment !== null) {\n    $$.update();\n    run_all($$.before_update);\n    const dirty = $$.dirty;\n    $$.dirty = [-1];\n    $$.fragment && $$.fragment.p($$.ctx, dirty);\n    $$.after_update.forEach(add_render_callback);\n  }\n}\n/**\n * Useful for example to execute remaining `afterUpdate` callbacks before executing `destroy`.\n */\nfunction flush_render_callbacks(fns) {\n  const filtered = [];\n  const targets = [];\n  render_callbacks.forEach(c => fns.indexOf(c) === -1 ? filtered.push(c) : targets.push(c));\n  targets.forEach(c => c());\n  render_callbacks = filtered;\n}\nconst outroing = new Set();\nlet outros;\nfunction group_outros() {\n  outros = {\n    r: 0,\n    c: [],\n    p: outros // parent group\n  };\n}\nfunction check_outros() {\n  if (!outros.r) {\n    run_all(outros.c);\n  }\n  outros = outros.p;\n}\nfunction transition_in(block, local) {\n  if (block && block.i) {\n    outroing.delete(block);\n    block.i(local);\n  }\n}\nfunction transition_out(block, local, detach, callback) {\n  if (block && block.o) {\n    if (outroing.has(block)) return;\n    outroing.add(block);\n    outros.c.push(() => {\n      outroing.delete(block);\n      if (callback) {\n        if (detach) block.d(1);\n        callback();\n      }\n    });\n    block.o(local);\n  } else if (callback) {\n    callback();\n  }\n}\nfunction get_spread_update(levels, updates) {\n  const update = {};\n  const to_null_out = {};\n  const accounted_for = {\n    $$scope: 1\n  };\n  let i = levels.length;\n  while (i--) {\n    const o = levels[i];\n    const n = updates[i];\n    if (n) {\n      for (const key in o) {\n        if (!(key in n)) to_null_out[key] = 1;\n      }\n      for (const key in n) {\n        if (!accounted_for[key]) {\n          update[key] = n[key];\n          accounted_for[key] = 1;\n        }\n      }\n      levels[i] = n;\n    } else {\n      for (const key in o) {\n        accounted_for[key] = 1;\n      }\n    }\n  }\n  for (const key in to_null_out) {\n    if (!(key in update)) update[key] = undefined;\n  }\n  return update;\n}\nfunction create_component(block) {\n  block && block.c();\n}\nfunction mount_component(component, target, anchor, customElement) {\n  const {\n    fragment,\n    after_update\n  } = component.$$;\n  fragment && fragment.m(target, anchor);\n  if (!customElement) {\n    // onMount happens before the initial afterUpdate\n    add_render_callback(() => {\n      const new_on_destroy = component.$$.on_mount.map(run).filter(is_function);\n      // if the component was destroyed immediately\n      // it will update the `$$.on_destroy` reference to `null`.\n      // the destructured on_destroy may still reference to the old array\n      if (component.$$.on_destroy) {\n        component.$$.on_destroy.push(...new_on_destroy);\n      } else {\n        // Edge case - component was destroyed immediately,\n        // most likely as a result of a binding initialising\n        run_all(new_on_destroy);\n      }\n      component.$$.on_mount = [];\n    });\n  }\n  after_update.forEach(add_render_callback);\n}\nfunction destroy_component(component, detaching) {\n  const $$ = component.$$;\n  if ($$.fragment !== null) {\n    flush_render_callbacks($$.after_update);\n    run_all($$.on_destroy);\n    $$.fragment && $$.fragment.d(detaching);\n    // TODO null out other refs, including component.$$ (but need to\n    // preserve final state?)\n    $$.on_destroy = $$.fragment = null;\n    $$.ctx = [];\n  }\n}\nfunction make_dirty(component, i) {\n  if (component.$$.dirty[0] === -1) {\n    dirty_components.push(component);\n    schedule_update();\n    component.$$.dirty.fill(0);\n  }\n  component.$$.dirty[i / 31 | 0] |= 1 << i % 31;\n}\nfunction init(component, options, instance, create_fragment, not_equal, props, append_styles, dirty = [-1]) {\n  const parent_component = current_component;\n  set_current_component(component);\n  const $$ = component.$$ = {\n    fragment: null,\n    ctx: [],\n    // state\n    props,\n    update: noop,\n    not_equal,\n    bound: blank_object(),\n    // lifecycle\n    on_mount: [],\n    on_destroy: [],\n    on_disconnect: [],\n    before_update: [],\n    after_update: [],\n    context: new Map(options.context || (parent_component ? parent_component.$$.context : [])),\n    // everything else\n    callbacks: blank_object(),\n    dirty,\n    skip_bound: false,\n    root: options.target || parent_component.$$.root\n  };\n  append_styles && append_styles($$.root);\n  let ready = false;\n  $$.ctx = instance ? instance(component, options.props || {}, (i, ret, ...rest) => {\n    const value = rest.length ? rest[0] : ret;\n    if ($$.ctx && not_equal($$.ctx[i], $$.ctx[i] = value)) {\n      if (!$$.skip_bound && $$.bound[i]) $$.bound[i](value);\n      if (ready) make_dirty(component, i);\n    }\n    return ret;\n  }) : [];\n  $$.update();\n  ready = true;\n  run_all($$.before_update);\n  // `false` as a special case of no DOM component\n  $$.fragment = create_fragment ? create_fragment($$.ctx) : false;\n  if (options.target) {\n    if (options.hydrate) {\n      const nodes = children(options.target);\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      $$.fragment && $$.fragment.l(nodes);\n      nodes.forEach(detach);\n    } else {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      $$.fragment && $$.fragment.c();\n    }\n    if (options.intro) transition_in(component.$$.fragment);\n    mount_component(component, options.target, options.anchor, options.customElement);\n    flush();\n  }\n  set_current_component(parent_component);\n}\n/**\n * Base class for Svelte components. Used when dev=false.\n */\nclass SvelteComponent {\n  $destroy() {\n    destroy_component(this, 1);\n    this.$destroy = noop;\n  }\n  $on(type, callback) {\n    if (!is_function(callback)) {\n      return noop;\n    }\n    const callbacks = this.$$.callbacks[type] || (this.$$.callbacks[type] = []);\n    callbacks.push(callback);\n    return () => {\n      const index = callbacks.indexOf(callback);\n      if (index !== -1) callbacks.splice(index, 1);\n    };\n  }\n  $set($$props) {\n    if (this.$$set && !is_empty($$props)) {\n      this.$$.skip_bound = true;\n      this.$$set($$props);\n      this.$$.skip_bound = false;\n    }\n  }\n}\n\n/* src/js/components/shepherd-button.svelte generated by Svelte v3.59.2 */\nfunction create_fragment$8(ctx) {\n  let button;\n  let button_aria_label_value;\n  let button_class_value;\n  let mounted;\n  let dispose;\n  return {\n    c() {\n      button = element(\"button\");\n      attr(button, \"aria-label\", button_aria_label_value = /*label*/ctx[3] ? /*label*/ctx[3] : null);\n      attr(button, \"class\", button_class_value = `${/*classes*/ctx[1] || ''} shepherd-button ${/*secondary*/ctx[4] ? 'shepherd-button-secondary' : ''}`);\n      button.disabled = /*disabled*/ctx[2];\n      attr(button, \"tabindex\", \"0\");\n    },\n    m(target, anchor) {\n      insert(target, button, anchor);\n      button.innerHTML = /*text*/ctx[5];\n      if (!mounted) {\n        dispose = listen(button, \"click\", function () {\n          if (is_function( /*action*/ctx[0])) /*action*/ctx[0].apply(this, arguments);\n        });\n        mounted = true;\n      }\n    },\n    p(new_ctx, [dirty]) {\n      ctx = new_ctx;\n      if (dirty & /*text*/32) button.innerHTML = /*text*/ctx[5];\n      if (dirty & /*label*/8 && button_aria_label_value !== (button_aria_label_value = /*label*/ctx[3] ? /*label*/ctx[3] : null)) {\n        attr(button, \"aria-label\", button_aria_label_value);\n      }\n      if (dirty & /*classes, secondary*/18 && button_class_value !== (button_class_value = `${/*classes*/ctx[1] || ''} shepherd-button ${/*secondary*/ctx[4] ? 'shepherd-button-secondary' : ''}`)) {\n        attr(button, \"class\", button_class_value);\n      }\n      if (dirty & /*disabled*/4) {\n        button.disabled = /*disabled*/ctx[2];\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(button);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nfunction instance$8($$self, $$props, $$invalidate) {\n  let {\n    config,\n    step\n  } = $$props;\n  let action, classes, disabled, label, secondary, text;\n  function getConfigOption(option) {\n    if (isFunction(option)) {\n      return option = option.call(step);\n    }\n    return option;\n  }\n  $$self.$$set = $$props => {\n    if ('config' in $$props) $$invalidate(6, config = $$props.config);\n    if ('step' in $$props) $$invalidate(7, step = $$props.step);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*config, step*/192) {\n      {\n        $$invalidate(0, action = config.action ? config.action.bind(step.tour) : null);\n        $$invalidate(1, classes = config.classes);\n        $$invalidate(2, disabled = config.disabled ? getConfigOption(config.disabled) : false);\n        $$invalidate(3, label = config.label ? getConfigOption(config.label) : null);\n        $$invalidate(4, secondary = config.secondary);\n        $$invalidate(5, text = config.text ? getConfigOption(config.text) : null);\n      }\n    }\n  };\n  return [action, classes, disabled, label, secondary, text, config, step];\n}\nclass Shepherd_button extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$8, create_fragment$8, safe_not_equal, {\n      config: 6,\n      step: 7\n    });\n  }\n}\n\n/* src/js/components/shepherd-footer.svelte generated by Svelte v3.59.2 */\nfunction get_each_context(ctx, list, i) {\n  const child_ctx = ctx.slice();\n  child_ctx[2] = list[i];\n  return child_ctx;\n}\n\n// (24:4) {#if buttons}\nfunction create_if_block$3(ctx) {\n  let each_1_anchor;\n  let current;\n  let each_value = /*buttons*/ctx[1];\n  let each_blocks = [];\n  for (let i = 0; i < each_value.length; i += 1) {\n    each_blocks[i] = create_each_block(get_each_context(ctx, each_value, i));\n  }\n  const out = i => transition_out(each_blocks[i], 1, 1, () => {\n    each_blocks[i] = null;\n  });\n  return {\n    c() {\n      for (let i = 0; i < each_blocks.length; i += 1) {\n        each_blocks[i].c();\n      }\n      each_1_anchor = empty();\n    },\n    m(target, anchor) {\n      for (let i = 0; i < each_blocks.length; i += 1) {\n        if (each_blocks[i]) {\n          each_blocks[i].m(target, anchor);\n        }\n      }\n      insert(target, each_1_anchor, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      if (dirty & /*buttons, step*/3) {\n        each_value = /*buttons*/ctx[1];\n        let i;\n        for (i = 0; i < each_value.length; i += 1) {\n          const child_ctx = get_each_context(ctx, each_value, i);\n          if (each_blocks[i]) {\n            each_blocks[i].p(child_ctx, dirty);\n            transition_in(each_blocks[i], 1);\n          } else {\n            each_blocks[i] = create_each_block(child_ctx);\n            each_blocks[i].c();\n            transition_in(each_blocks[i], 1);\n            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);\n          }\n        }\n        group_outros();\n        for (i = each_value.length; i < each_blocks.length; i += 1) {\n          out(i);\n        }\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      for (let i = 0; i < each_value.length; i += 1) {\n        transition_in(each_blocks[i]);\n      }\n      current = true;\n    },\n    o(local) {\n      each_blocks = each_blocks.filter(Boolean);\n      for (let i = 0; i < each_blocks.length; i += 1) {\n        transition_out(each_blocks[i]);\n      }\n      current = false;\n    },\n    d(detaching) {\n      destroy_each(each_blocks, detaching);\n      if (detaching) detach(each_1_anchor);\n    }\n  };\n}\n\n// (25:8) {#each buttons as config}\nfunction create_each_block(ctx) {\n  let shepherdbutton;\n  let current;\n  shepherdbutton = new Shepherd_button({\n    props: {\n      config: /*config*/ctx[2],\n      step: /*step*/ctx[0]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdbutton.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdbutton, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdbutton_changes = {};\n      if (dirty & /*buttons*/2) shepherdbutton_changes.config = /*config*/ctx[2];\n      if (dirty & /*step*/1) shepherdbutton_changes.step = /*step*/ctx[0];\n      shepherdbutton.$set(shepherdbutton_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdbutton.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdbutton.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdbutton, detaching);\n    }\n  };\n}\nfunction create_fragment$7(ctx) {\n  let footer;\n  let current;\n  let if_block = /*buttons*/ctx[1] && create_if_block$3(ctx);\n  return {\n    c() {\n      footer = element(\"footer\");\n      if (if_block) if_block.c();\n      attr(footer, \"class\", \"shepherd-footer\");\n    },\n    m(target, anchor) {\n      insert(target, footer, anchor);\n      if (if_block) if_block.m(footer, null);\n      current = true;\n    },\n    p(ctx, [dirty]) {\n      if ( /*buttons*/ctx[1]) {\n        if (if_block) {\n          if_block.p(ctx, dirty);\n          if (dirty & /*buttons*/2) {\n            transition_in(if_block, 1);\n          }\n        } else {\n          if_block = create_if_block$3(ctx);\n          if_block.c();\n          transition_in(if_block, 1);\n          if_block.m(footer, null);\n        }\n      } else if (if_block) {\n        group_outros();\n        transition_out(if_block, 1, 1, () => {\n          if_block = null;\n        });\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      transition_in(if_block);\n      current = true;\n    },\n    o(local) {\n      transition_out(if_block);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) detach(footer);\n      if (if_block) if_block.d();\n    }\n  };\n}\nfunction instance$7($$self, $$props, $$invalidate) {\n  let buttons;\n  let {\n    step\n  } = $$props;\n  $$self.$$set = $$props => {\n    if ('step' in $$props) $$invalidate(0, step = $$props.step);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*step*/1) {\n      $$invalidate(1, buttons = step.options.buttons);\n    }\n  };\n  return [step, buttons];\n}\nclass Shepherd_footer extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$7, create_fragment$7, safe_not_equal, {\n      step: 0\n    });\n  }\n}\n\n/* src/js/components/shepherd-cancel-icon.svelte generated by Svelte v3.59.2 */\nfunction create_fragment$6(ctx) {\n  let button;\n  let span;\n  let button_aria_label_value;\n  let mounted;\n  let dispose;\n  return {\n    c() {\n      button = element(\"button\");\n      span = element(\"span\");\n      span.textContent = \"×\";\n      attr(span, \"aria-hidden\", \"true\");\n      attr(button, \"aria-label\", button_aria_label_value = /*cancelIcon*/ctx[0].label ? /*cancelIcon*/ctx[0].label : 'Close Tour');\n      attr(button, \"class\", \"shepherd-cancel-icon\");\n      attr(button, \"type\", \"button\");\n    },\n    m(target, anchor) {\n      insert(target, button, anchor);\n      append(button, span);\n      if (!mounted) {\n        dispose = listen(button, \"click\", /*handleCancelClick*/ctx[1]);\n        mounted = true;\n      }\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*cancelIcon*/1 && button_aria_label_value !== (button_aria_label_value = /*cancelIcon*/ctx[0].label ? /*cancelIcon*/ctx[0].label : 'Close Tour')) {\n        attr(button, \"aria-label\", button_aria_label_value);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(button);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nfunction instance$6($$self, $$props, $$invalidate) {\n  let {\n    cancelIcon,\n    step\n  } = $$props;\n\n  /**\n  * Add a click listener to the cancel link that cancels the tour\n  */\n  const handleCancelClick = e => {\n    e.preventDefault();\n    step.cancel();\n  };\n  $$self.$$set = $$props => {\n    if ('cancelIcon' in $$props) $$invalidate(0, cancelIcon = $$props.cancelIcon);\n    if ('step' in $$props) $$invalidate(2, step = $$props.step);\n  };\n  return [cancelIcon, handleCancelClick, step];\n}\nclass Shepherd_cancel_icon extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$6, create_fragment$6, safe_not_equal, {\n      cancelIcon: 0,\n      step: 2\n    });\n  }\n}\n\n/* src/js/components/shepherd-title.svelte generated by Svelte v3.59.2 */\nfunction create_fragment$5(ctx) {\n  let h3;\n  return {\n    c() {\n      h3 = element(\"h3\");\n      attr(h3, \"id\", /*labelId*/ctx[1]);\n      attr(h3, \"class\", \"shepherd-title\");\n    },\n    m(target, anchor) {\n      insert(target, h3, anchor);\n      /*h3_binding*/\n      ctx[3](h3);\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*labelId*/2) {\n        attr(h3, \"id\", /*labelId*/ctx[1]);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(h3);\n      /*h3_binding*/\n      ctx[3](null);\n    }\n  };\n}\nfunction instance$5($$self, $$props, $$invalidate) {\n  let {\n    labelId,\n    element,\n    title\n  } = $$props;\n  afterUpdate(() => {\n    if (isFunction(title)) {\n      $$invalidate(2, title = title());\n    }\n    $$invalidate(0, element.innerHTML = title, element);\n  });\n  function h3_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('labelId' in $$props) $$invalidate(1, labelId = $$props.labelId);\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('title' in $$props) $$invalidate(2, title = $$props.title);\n  };\n  return [element, labelId, title, h3_binding];\n}\nclass Shepherd_title extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$5, create_fragment$5, safe_not_equal, {\n      labelId: 1,\n      element: 0,\n      title: 2\n    });\n  }\n}\n\n/* src/js/components/shepherd-header.svelte generated by Svelte v3.59.2 */\nfunction create_if_block_1$1(ctx) {\n  let shepherdtitle;\n  let current;\n  shepherdtitle = new Shepherd_title({\n    props: {\n      labelId: /*labelId*/ctx[0],\n      title: /*title*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdtitle.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdtitle, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdtitle_changes = {};\n      if (dirty & /*labelId*/1) shepherdtitle_changes.labelId = /*labelId*/ctx[0];\n      if (dirty & /*title*/4) shepherdtitle_changes.title = /*title*/ctx[2];\n      shepherdtitle.$set(shepherdtitle_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdtitle.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdtitle.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdtitle, detaching);\n    }\n  };\n}\n\n// (39:4) {#if cancelIcon && cancelIcon.enabled}\nfunction create_if_block$2(ctx) {\n  let shepherdcancelicon;\n  let current;\n  shepherdcancelicon = new Shepherd_cancel_icon({\n    props: {\n      cancelIcon: /*cancelIcon*/ctx[3],\n      step: /*step*/ctx[1]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdcancelicon.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdcancelicon, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdcancelicon_changes = {};\n      if (dirty & /*cancelIcon*/8) shepherdcancelicon_changes.cancelIcon = /*cancelIcon*/ctx[3];\n      if (dirty & /*step*/2) shepherdcancelicon_changes.step = /*step*/ctx[1];\n      shepherdcancelicon.$set(shepherdcancelicon_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdcancelicon.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdcancelicon.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdcancelicon, detaching);\n    }\n  };\n}\nfunction create_fragment$4(ctx) {\n  let header;\n  let t;\n  let current;\n  let if_block0 = /*title*/ctx[2] && create_if_block_1$1(ctx);\n  let if_block1 = /*cancelIcon*/ctx[3] && /*cancelIcon*/ctx[3].enabled && create_if_block$2(ctx);\n  return {\n    c() {\n      header = element(\"header\");\n      if (if_block0) if_block0.c();\n      t = space();\n      if (if_block1) if_block1.c();\n      attr(header, \"class\", \"shepherd-header\");\n    },\n    m(target, anchor) {\n      insert(target, header, anchor);\n      if (if_block0) if_block0.m(header, null);\n      append(header, t);\n      if (if_block1) if_block1.m(header, null);\n      current = true;\n    },\n    p(ctx, [dirty]) {\n      if ( /*title*/ctx[2]) {\n        if (if_block0) {\n          if_block0.p(ctx, dirty);\n          if (dirty & /*title*/4) {\n            transition_in(if_block0, 1);\n          }\n        } else {\n          if_block0 = create_if_block_1$1(ctx);\n          if_block0.c();\n          transition_in(if_block0, 1);\n          if_block0.m(header, t);\n        }\n      } else if (if_block0) {\n        group_outros();\n        transition_out(if_block0, 1, 1, () => {\n          if_block0 = null;\n        });\n        check_outros();\n      }\n      if ( /*cancelIcon*/ctx[3] && /*cancelIcon*/ctx[3].enabled) {\n        if (if_block1) {\n          if_block1.p(ctx, dirty);\n          if (dirty & /*cancelIcon*/8) {\n            transition_in(if_block1, 1);\n          }\n        } else {\n          if_block1 = create_if_block$2(ctx);\n          if_block1.c();\n          transition_in(if_block1, 1);\n          if_block1.m(header, null);\n        }\n      } else if (if_block1) {\n        group_outros();\n        transition_out(if_block1, 1, 1, () => {\n          if_block1 = null;\n        });\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      transition_in(if_block0);\n      transition_in(if_block1);\n      current = true;\n    },\n    o(local) {\n      transition_out(if_block0);\n      transition_out(if_block1);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) detach(header);\n      if (if_block0) if_block0.d();\n      if (if_block1) if_block1.d();\n    }\n  };\n}\nfunction instance$4($$self, $$props, $$invalidate) {\n  let {\n    labelId,\n    step\n  } = $$props;\n  let title, cancelIcon;\n  $$self.$$set = $$props => {\n    if ('labelId' in $$props) $$invalidate(0, labelId = $$props.labelId);\n    if ('step' in $$props) $$invalidate(1, step = $$props.step);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*step*/2) {\n      {\n        $$invalidate(2, title = step.options.title);\n        $$invalidate(3, cancelIcon = step.options.cancelIcon);\n      }\n    }\n  };\n  return [labelId, step, title, cancelIcon];\n}\nclass Shepherd_header extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$4, create_fragment$4, safe_not_equal, {\n      labelId: 0,\n      step: 1\n    });\n  }\n}\n\n/* src/js/components/shepherd-text.svelte generated by Svelte v3.59.2 */\nfunction create_fragment$3(ctx) {\n  let div;\n  return {\n    c() {\n      div = element(\"div\");\n      attr(div, \"class\", \"shepherd-text\");\n      attr(div, \"id\", /*descriptionId*/ctx[1]);\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n      /*div_binding*/\n      ctx[3](div);\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*descriptionId*/2) {\n        attr(div, \"id\", /*descriptionId*/ctx[1]);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(div);\n      /*div_binding*/\n      ctx[3](null);\n    }\n  };\n}\nfunction instance$3($$self, $$props, $$invalidate) {\n  let {\n    descriptionId,\n    element,\n    step\n  } = $$props;\n  afterUpdate(() => {\n    let {\n      text\n    } = step.options;\n    if (isFunction(text)) {\n      text = text.call(step);\n    }\n    if (isHTMLElement$1(text)) {\n      element.appendChild(text);\n    } else {\n      $$invalidate(0, element.innerHTML = text, element);\n    }\n  });\n  function div_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('descriptionId' in $$props) $$invalidate(1, descriptionId = $$props.descriptionId);\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('step' in $$props) $$invalidate(2, step = $$props.step);\n  };\n  return [element, descriptionId, step, div_binding];\n}\nclass Shepherd_text extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$3, create_fragment$3, safe_not_equal, {\n      descriptionId: 1,\n      element: 0,\n      step: 2\n    });\n  }\n}\n\n/* src/js/components/shepherd-content.svelte generated by Svelte v3.59.2 */\nfunction create_if_block_2(ctx) {\n  let shepherdheader;\n  let current;\n  shepherdheader = new Shepherd_header({\n    props: {\n      labelId: /*labelId*/ctx[1],\n      step: /*step*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdheader.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdheader, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdheader_changes = {};\n      if (dirty & /*labelId*/2) shepherdheader_changes.labelId = /*labelId*/ctx[1];\n      if (dirty & /*step*/4) shepherdheader_changes.step = /*step*/ctx[2];\n      shepherdheader.$set(shepherdheader_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdheader.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdheader.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdheader, detaching);\n    }\n  };\n}\n\n// (28:2) {#if !isUndefined(step.options.text)}\nfunction create_if_block_1(ctx) {\n  let shepherdtext;\n  let current;\n  shepherdtext = new Shepherd_text({\n    props: {\n      descriptionId: /*descriptionId*/ctx[0],\n      step: /*step*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdtext.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdtext, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdtext_changes = {};\n      if (dirty & /*descriptionId*/1) shepherdtext_changes.descriptionId = /*descriptionId*/ctx[0];\n      if (dirty & /*step*/4) shepherdtext_changes.step = /*step*/ctx[2];\n      shepherdtext.$set(shepherdtext_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdtext.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdtext.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdtext, detaching);\n    }\n  };\n}\n\n// (35:2) {#if Array.isArray(step.options.buttons) && step.options.buttons.length}\nfunction create_if_block$1(ctx) {\n  let shepherdfooter;\n  let current;\n  shepherdfooter = new Shepherd_footer({\n    props: {\n      step: /*step*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdfooter.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdfooter, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdfooter_changes = {};\n      if (dirty & /*step*/4) shepherdfooter_changes.step = /*step*/ctx[2];\n      shepherdfooter.$set(shepherdfooter_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdfooter.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdfooter.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdfooter, detaching);\n    }\n  };\n}\nfunction create_fragment$2(ctx) {\n  let div;\n  let show_if_2 = !isUndefined( /*step*/ctx[2].options.title) || /*step*/ctx[2].options.cancelIcon && /*step*/ctx[2].options.cancelIcon.enabled;\n  let t0;\n  let show_if_1 = !isUndefined( /*step*/ctx[2].options.text);\n  let t1;\n  let show_if = Array.isArray( /*step*/ctx[2].options.buttons) && /*step*/ctx[2].options.buttons.length;\n  let current;\n  let if_block0 = show_if_2 && create_if_block_2(ctx);\n  let if_block1 = show_if_1 && create_if_block_1(ctx);\n  let if_block2 = show_if && create_if_block$1(ctx);\n  return {\n    c() {\n      div = element(\"div\");\n      if (if_block0) if_block0.c();\n      t0 = space();\n      if (if_block1) if_block1.c();\n      t1 = space();\n      if (if_block2) if_block2.c();\n      attr(div, \"class\", \"shepherd-content\");\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n      if (if_block0) if_block0.m(div, null);\n      append(div, t0);\n      if (if_block1) if_block1.m(div, null);\n      append(div, t1);\n      if (if_block2) if_block2.m(div, null);\n      current = true;\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*step*/4) show_if_2 = !isUndefined( /*step*/ctx[2].options.title) || /*step*/ctx[2].options.cancelIcon && /*step*/ctx[2].options.cancelIcon.enabled;\n      if (show_if_2) {\n        if (if_block0) {\n          if_block0.p(ctx, dirty);\n          if (dirty & /*step*/4) {\n            transition_in(if_block0, 1);\n          }\n        } else {\n          if_block0 = create_if_block_2(ctx);\n          if_block0.c();\n          transition_in(if_block0, 1);\n          if_block0.m(div, t0);\n        }\n      } else if (if_block0) {\n        group_outros();\n        transition_out(if_block0, 1, 1, () => {\n          if_block0 = null;\n        });\n        check_outros();\n      }\n      if (dirty & /*step*/4) show_if_1 = !isUndefined( /*step*/ctx[2].options.text);\n      if (show_if_1) {\n        if (if_block1) {\n          if_block1.p(ctx, dirty);\n          if (dirty & /*step*/4) {\n            transition_in(if_block1, 1);\n          }\n        } else {\n          if_block1 = create_if_block_1(ctx);\n          if_block1.c();\n          transition_in(if_block1, 1);\n          if_block1.m(div, t1);\n        }\n      } else if (if_block1) {\n        group_outros();\n        transition_out(if_block1, 1, 1, () => {\n          if_block1 = null;\n        });\n        check_outros();\n      }\n      if (dirty & /*step*/4) show_if = Array.isArray( /*step*/ctx[2].options.buttons) && /*step*/ctx[2].options.buttons.length;\n      if (show_if) {\n        if (if_block2) {\n          if_block2.p(ctx, dirty);\n          if (dirty & /*step*/4) {\n            transition_in(if_block2, 1);\n          }\n        } else {\n          if_block2 = create_if_block$1(ctx);\n          if_block2.c();\n          transition_in(if_block2, 1);\n          if_block2.m(div, null);\n        }\n      } else if (if_block2) {\n        group_outros();\n        transition_out(if_block2, 1, 1, () => {\n          if_block2 = null;\n        });\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      transition_in(if_block0);\n      transition_in(if_block1);\n      transition_in(if_block2);\n      current = true;\n    },\n    o(local) {\n      transition_out(if_block0);\n      transition_out(if_block1);\n      transition_out(if_block2);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) detach(div);\n      if (if_block0) if_block0.d();\n      if (if_block1) if_block1.d();\n      if (if_block2) if_block2.d();\n    }\n  };\n}\nfunction instance$2($$self, $$props, $$invalidate) {\n  let {\n    descriptionId,\n    labelId,\n    step\n  } = $$props;\n  $$self.$$set = $$props => {\n    if ('descriptionId' in $$props) $$invalidate(0, descriptionId = $$props.descriptionId);\n    if ('labelId' in $$props) $$invalidate(1, labelId = $$props.labelId);\n    if ('step' in $$props) $$invalidate(2, step = $$props.step);\n  };\n  return [descriptionId, labelId, step];\n}\nclass Shepherd_content extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$2, create_fragment$2, safe_not_equal, {\n      descriptionId: 0,\n      labelId: 1,\n      step: 2\n    });\n  }\n}\n\n/* src/js/components/shepherd-element.svelte generated by Svelte v3.59.2 */\nfunction create_if_block(ctx) {\n  let div;\n  return {\n    c() {\n      div = element(\"div\");\n      attr(div, \"class\", \"shepherd-arrow\");\n      attr(div, \"data-popper-arrow\", \"\");\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n    },\n    d(detaching) {\n      if (detaching) detach(div);\n    }\n  };\n}\nfunction create_fragment$1(ctx) {\n  let div;\n  let t;\n  let shepherdcontent;\n  let div_aria_describedby_value;\n  let div_aria_labelledby_value;\n  let current;\n  let mounted;\n  let dispose;\n  let if_block = /*step*/ctx[4].options.arrow && /*step*/ctx[4].options.attachTo && /*step*/ctx[4].options.attachTo.element && /*step*/ctx[4].options.attachTo.on && create_if_block();\n  shepherdcontent = new Shepherd_content({\n    props: {\n      descriptionId: /*descriptionId*/ctx[2],\n      labelId: /*labelId*/ctx[3],\n      step: /*step*/ctx[4]\n    }\n  });\n  let div_levels = [{\n    \"aria-describedby\": div_aria_describedby_value = !isUndefined( /*step*/ctx[4].options.text) ? /*descriptionId*/ctx[2] : null\n  }, {\n    \"aria-labelledby\": div_aria_labelledby_value = /*step*/ctx[4].options.title ? /*labelId*/ctx[3] : null\n  }, /*dataStepId*/ctx[1], {\n    role: \"dialog\"\n  }, {\n    tabindex: \"0\"\n  }];\n  let div_data = {};\n  for (let i = 0; i < div_levels.length; i += 1) {\n    div_data = assign(div_data, div_levels[i]);\n  }\n  return {\n    c() {\n      div = element(\"div\");\n      if (if_block) if_block.c();\n      t = space();\n      create_component(shepherdcontent.$$.fragment);\n      set_attributes(div, div_data);\n      toggle_class(div, \"shepherd-has-cancel-icon\", /*hasCancelIcon*/ctx[5]);\n      toggle_class(div, \"shepherd-has-title\", /*hasTitle*/ctx[6]);\n      toggle_class(div, \"shepherd-element\", true);\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n      if (if_block) if_block.m(div, null);\n      append(div, t);\n      mount_component(shepherdcontent, div, null);\n      /*div_binding*/\n      ctx[13](div);\n      current = true;\n      if (!mounted) {\n        dispose = listen(div, \"keydown\", /*handleKeyDown*/ctx[7]);\n        mounted = true;\n      }\n    },\n    p(ctx, [dirty]) {\n      if ( /*step*/ctx[4].options.arrow && /*step*/ctx[4].options.attachTo && /*step*/ctx[4].options.attachTo.element && /*step*/ctx[4].options.attachTo.on) {\n        if (if_block) ;else {\n          if_block = create_if_block();\n          if_block.c();\n          if_block.m(div, t);\n        }\n      } else if (if_block) {\n        if_block.d(1);\n        if_block = null;\n      }\n      const shepherdcontent_changes = {};\n      if (dirty & /*descriptionId*/4) shepherdcontent_changes.descriptionId = /*descriptionId*/ctx[2];\n      if (dirty & /*labelId*/8) shepherdcontent_changes.labelId = /*labelId*/ctx[3];\n      if (dirty & /*step*/16) shepherdcontent_changes.step = /*step*/ctx[4];\n      shepherdcontent.$set(shepherdcontent_changes);\n      set_attributes(div, div_data = get_spread_update(div_levels, [(!current || dirty & /*step, descriptionId*/20 && div_aria_describedby_value !== (div_aria_describedby_value = !isUndefined( /*step*/ctx[4].options.text) ? /*descriptionId*/ctx[2] : null)) && {\n        \"aria-describedby\": div_aria_describedby_value\n      }, (!current || dirty & /*step, labelId*/24 && div_aria_labelledby_value !== (div_aria_labelledby_value = /*step*/ctx[4].options.title ? /*labelId*/ctx[3] : null)) && {\n        \"aria-labelledby\": div_aria_labelledby_value\n      }, dirty & /*dataStepId*/2 && /*dataStepId*/ctx[1], {\n        role: \"dialog\"\n      }, {\n        tabindex: \"0\"\n      }]));\n      toggle_class(div, \"shepherd-has-cancel-icon\", /*hasCancelIcon*/ctx[5]);\n      toggle_class(div, \"shepherd-has-title\", /*hasTitle*/ctx[6]);\n      toggle_class(div, \"shepherd-element\", true);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdcontent.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdcontent.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) detach(div);\n      if (if_block) if_block.d();\n      destroy_component(shepherdcontent);\n      /*div_binding*/\n      ctx[13](null);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nconst KEY_TAB = 9;\nconst KEY_ESC = 27;\nconst LEFT_ARROW = 37;\nconst RIGHT_ARROW = 39;\nfunction getClassesArray(classes) {\n  return classes.split(' ').filter(className => !!className.length);\n}\nfunction instance$1($$self, $$props, $$invalidate) {\n  let {\n    classPrefix,\n    element,\n    descriptionId,\n    firstFocusableElement,\n    focusableElements,\n    labelId,\n    lastFocusableElement,\n    step,\n    dataStepId\n  } = $$props;\n  let hasCancelIcon, hasTitle, classes;\n  const getElement = () => element;\n  onMount(() => {\n    // Get all elements that are focusable\n    $$invalidate(1, dataStepId = {\n      [`data-${classPrefix}shepherd-step-id`]: step.id\n    });\n    $$invalidate(9, focusableElements = element.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex=\"0\"]'));\n    $$invalidate(8, firstFocusableElement = focusableElements[0]);\n    $$invalidate(10, lastFocusableElement = focusableElements[focusableElements.length - 1]);\n  });\n  afterUpdate(() => {\n    if (classes !== step.options.classes) {\n      updateDynamicClasses();\n    }\n  });\n  function updateDynamicClasses() {\n    removeClasses(classes);\n    classes = step.options.classes;\n    addClasses(classes);\n  }\n  function removeClasses(classes) {\n    if (isString(classes)) {\n      const oldClasses = getClassesArray(classes);\n      if (oldClasses.length) {\n        element.classList.remove(...oldClasses);\n      }\n    }\n  }\n  function addClasses(classes) {\n    if (isString(classes)) {\n      const newClasses = getClassesArray(classes);\n      if (newClasses.length) {\n        element.classList.add(...newClasses);\n      }\n    }\n  }\n\n  /**\n  * Setup keydown events to allow closing the modal with ESC\n  *\n  * Borrowed from this great post! https://bitsofco.de/accessible-modal-dialog/\n  *\n  * @private\n  */\n  const handleKeyDown = e => {\n    const {\n      tour\n    } = step;\n    switch (e.keyCode) {\n      case KEY_TAB:\n        if (focusableElements.length === 0) {\n          e.preventDefault();\n          break;\n        }\n        // Backward tab\n        if (e.shiftKey) {\n          if (document.activeElement === firstFocusableElement || document.activeElement.classList.contains('shepherd-element')) {\n            e.preventDefault();\n            lastFocusableElement.focus();\n          }\n        } else {\n          if (document.activeElement === lastFocusableElement) {\n            e.preventDefault();\n            firstFocusableElement.focus();\n          }\n        }\n        break;\n      case KEY_ESC:\n        if (tour.options.exitOnEsc) {\n          e.stopPropagation();\n          step.cancel();\n        }\n        break;\n      case LEFT_ARROW:\n        if (tour.options.keyboardNavigation) {\n          e.stopPropagation();\n          tour.back();\n        }\n        break;\n      case RIGHT_ARROW:\n        if (tour.options.keyboardNavigation) {\n          e.stopPropagation();\n          tour.next();\n        }\n        break;\n    }\n  };\n  function div_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('classPrefix' in $$props) $$invalidate(11, classPrefix = $$props.classPrefix);\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('descriptionId' in $$props) $$invalidate(2, descriptionId = $$props.descriptionId);\n    if ('firstFocusableElement' in $$props) $$invalidate(8, firstFocusableElement = $$props.firstFocusableElement);\n    if ('focusableElements' in $$props) $$invalidate(9, focusableElements = $$props.focusableElements);\n    if ('labelId' in $$props) $$invalidate(3, labelId = $$props.labelId);\n    if ('lastFocusableElement' in $$props) $$invalidate(10, lastFocusableElement = $$props.lastFocusableElement);\n    if ('step' in $$props) $$invalidate(4, step = $$props.step);\n    if ('dataStepId' in $$props) $$invalidate(1, dataStepId = $$props.dataStepId);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*step*/16) {\n      {\n        $$invalidate(5, hasCancelIcon = step.options && step.options.cancelIcon && step.options.cancelIcon.enabled);\n        $$invalidate(6, hasTitle = step.options && step.options.title);\n      }\n    }\n  };\n  return [element, dataStepId, descriptionId, labelId, step, hasCancelIcon, hasTitle, handleKeyDown, firstFocusableElement, focusableElements, lastFocusableElement, classPrefix, getElement, div_binding];\n}\nclass Shepherd_element extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$1, create_fragment$1, safe_not_equal, {\n      classPrefix: 11,\n      element: 0,\n      descriptionId: 2,\n      firstFocusableElement: 8,\n      focusableElements: 9,\n      labelId: 3,\n      lastFocusableElement: 10,\n      step: 4,\n      dataStepId: 1,\n      getElement: 12\n    });\n  }\n  get getElement() {\n    return this.$$.ctx[12];\n  }\n}\n\n/**\n * A class representing steps to be added to a tour.\n * @extends {Evented}\n */\nclass Step extends Evented {\n  /**\n   * Create a step\n   * @param {Tour} tour The tour for the step\n   * @param {object} options The options for the step\n   * @param {boolean} options.arrow Whether to display the arrow for the tooltip or not. Defaults to `true`.\n   * @param {object} options.attachTo The element the step should be attached to on the page.\n   * An object with properties `element` and `on`.\n   *\n   * ```js\n   * const step = new Step(tour, {\n   *   attachTo: { element: '.some .selector-path', on: 'left' },\n   *   ...moreOptions\n   * });\n   * ```\n   *\n   * If you don’t specify an `attachTo` the element will appear in the middle of the screen. The same will happen if your `attachTo.element` callback returns `null`, `undefined`, or a selector that does not exist in the DOM.\n   * If you omit the `on` portion of `attachTo`, the element will still be highlighted, but the tooltip will appear\n   * in the middle of the screen, without an arrow pointing to the target.\n   * If the element to highlight does not yet exist while instantiating tour steps, you may use lazy evaluation by supplying a function to `attachTo.element`. The function will be called in the `before-show` phase.\n   * @param {string|HTMLElement|function} options.attachTo.element An element selector string, DOM element, or a function (returning a selector, a DOM element, `null` or `undefined`).\n   * @param {string} options.attachTo.on The optional direction to place the FloatingUI tooltip relative to the element.\n   *   - Possible string values: 'top', 'top-start', 'top-end', 'bottom', 'bottom-start', 'bottom-end', 'right', 'right-start', 'right-end', 'left', 'left-start', 'left-end'\n   * @param {Object} options.advanceOn An action on the page which should advance shepherd to the next step.\n   * It should be an object with a string `selector` and an `event` name\n   * ```js\n   * const step = new Step(tour, {\n   *   advanceOn: { selector: '.some .selector-path', event: 'click' },\n   *   ...moreOptions\n   * });\n   * ```\n   * `event` doesn’t have to be an event inside the tour, it can be any event fired on any element on the page.\n   * You can also always manually advance the Tour by calling `myTour.next()`.\n   * @param {function} options.beforeShowPromise A function that returns a promise.\n   * When the promise resolves, the rest of the `show` code for the step will execute.\n   * @param {Object[]} options.buttons An array of buttons to add to the step. These will be rendered in a\n   * footer below the main body text.\n   * @param {function} options.buttons.button.action A function executed when the button is clicked on.\n   * It is automatically bound to the `tour` the step is associated with, so things like `this.next` will\n   * work inside the action.\n   * You can use action to skip steps or navigate to specific steps, with something like:\n   * ```js\n   * action() {\n   *   return this.show('some_step_name');\n   * }\n   * ```\n   * @param {string} options.buttons.button.classes Extra classes to apply to the `<a>`\n   * @param {boolean} options.buttons.button.disabled Should the button be disabled?\n   * @param {string} options.buttons.button.label The aria-label text of the button\n   * @param {boolean} options.buttons.button.secondary If true, a shepherd-button-secondary class is applied to the button\n   * @param {string} options.buttons.button.text The HTML text of the button\n   * @param {boolean} options.canClickTarget A boolean, that when set to false, will set `pointer-events: none` on the target\n   * @param {object} options.cancelIcon Options for the cancel icon\n   * @param {boolean} options.cancelIcon.enabled Should a cancel “✕” be shown in the header of the step?\n   * @param {string} options.cancelIcon.label The label to add for `aria-label`\n   * @param {string} options.classes A string of extra classes to add to the step's content element.\n   * @param {string} options.highlightClass An extra class to apply to the `attachTo` element when it is\n   * highlighted (that is, when its step is active). You can then target that selector in your CSS.\n   * @param {string} options.id The string to use as the `id` for the step.\n   * @param {number} options.modalOverlayOpeningPadding An amount of padding to add around the modal overlay opening\n   * @param {number | { topLeft: number, bottomLeft: number, bottomRight: number, topRight: number }} options.modalOverlayOpeningRadius An amount of border radius to add around the modal overlay opening\n   * @param {object} options.floatingUIOptions Extra options to pass to FloatingUI\n   * @param {boolean|Object} options.scrollTo Should the element be scrolled to when this step is shown? If true, uses the default `scrollIntoView`,\n   * if an object, passes that object as the params to `scrollIntoView` i.e. `{behavior: 'smooth', block: 'center'}`\n   * @param {function} options.scrollToHandler A function that lets you override the default scrollTo behavior and\n   * define a custom action to do the scrolling, and possibly other logic.\n   * @param {function} options.showOn A function that, when it returns `true`, will show the step.\n   * If it returns false, the step will be skipped.\n   * @param {string} options.text The text in the body of the step. It can be one of three types:\n   * ```\n   * - HTML string\n   * - `HTMLElement` object\n   * - `Function` to be executed when the step is built. It must return one the two options above.\n   * ```\n   * @param {string} options.title The step's title. It becomes an `h3` at the top of the step. It can be one of two types:\n   * ```\n   * - HTML string\n   * - `Function` to be executed when the step is built. It must return HTML string.\n   * ```\n   * @param {object} options.when You can define `show`, `hide`, etc events inside `when`. For example:\n   * ```js\n   * when: {\n   *   show: function() {\n   *     window.scrollTo(0, 0);\n   *   }\n   * }\n   * ```\n   * @return {Step} The newly created Step instance\n   */\n  constructor(tour, options = {}) {\n    super(tour, options);\n    this.tour = tour;\n    this.classPrefix = this.tour.options ? normalizePrefix(this.tour.options.classPrefix) : '';\n    this.styles = tour.styles;\n\n    /**\n     * Resolved attachTo options. Due to lazy evaluation, we only resolve the options during `before-show` phase.\n     * Do not use this directly, use the _getResolvedAttachToOptions method instead.\n     * @type {null|{}|{element, to}}\n     * @private\n     */\n    this._resolvedAttachTo = null;\n    autoBind(this);\n    this._setOptions(options);\n    return this;\n  }\n\n  /**\n   * Cancel the tour\n   * Triggers the `cancel` event\n   */\n  cancel() {\n    this.tour.cancel();\n    this.trigger('cancel');\n  }\n\n  /**\n   * Complete the tour\n   * Triggers the `complete` event\n   */\n  complete() {\n    this.tour.complete();\n    this.trigger('complete');\n  }\n\n  /**\n   * Remove the step, delete the step's element, and destroy the FloatingUI instance for the step.\n   * Triggers `destroy` event\n   */\n  destroy() {\n    destroyTooltip(this);\n    if (isHTMLElement$1(this.el)) {\n      this.el.remove();\n      this.el = null;\n    }\n    this._updateStepTargetOnHide();\n    this.trigger('destroy');\n  }\n\n  /**\n   * Returns the tour for the step\n   * @return {Tour} The tour instance\n   */\n  getTour() {\n    return this.tour;\n  }\n\n  /**\n   * Hide the step\n   */\n  hide() {\n    this.tour.modal.hide();\n    this.trigger('before-hide');\n    if (this.el) {\n      this.el.hidden = true;\n    }\n    this._updateStepTargetOnHide();\n    this.trigger('hide');\n  }\n\n  /**\n   * Resolves attachTo options.\n   * @returns {{}|{element, on}}\n   * @private\n   */\n  _resolveAttachToOptions() {\n    this._resolvedAttachTo = parseAttachTo(this);\n    return this._resolvedAttachTo;\n  }\n\n  /**\n   * A selector for resolved attachTo options.\n   * @returns {{}|{element, on}}\n   * @private\n   */\n  _getResolvedAttachToOptions() {\n    if (this._resolvedAttachTo === null) {\n      return this._resolveAttachToOptions();\n    }\n    return this._resolvedAttachTo;\n  }\n\n  /**\n   * Check if the step is open and visible\n   * @return {boolean} True if the step is open and visible\n   */\n  isOpen() {\n    return Boolean(this.el && !this.el.hidden);\n  }\n\n  /**\n   * Wraps `_show` and ensures `beforeShowPromise` resolves before calling show\n   * @return {*|Promise}\n   */\n  show() {\n    if (isFunction(this.options.beforeShowPromise)) {\n      return Promise.resolve(this.options.beforeShowPromise()).then(() => this._show());\n    }\n    return Promise.resolve(this._show());\n  }\n\n  /**\n   * Updates the options of the step.\n   *\n   * @param {Object} options The options for the step\n   */\n  updateStepOptions(options) {\n    Object.assign(this.options, options);\n    if (this.shepherdElementComponent) {\n      this.shepherdElementComponent.$set({\n        step: this\n      });\n    }\n  }\n\n  /**\n   * Returns the element for the step\n   * @return {HTMLElement|null|undefined} The element instance. undefined if it has never been shown, null if it has been destroyed\n   */\n  getElement() {\n    return this.el;\n  }\n\n  /**\n   * Returns the target for the step\n   * @return {HTMLElement|null|undefined} The element instance. undefined if it has never been shown, null if query string has not been found\n   */\n  getTarget() {\n    return this.target;\n  }\n\n  /**\n   * Creates Shepherd element for step based on options\n   *\n   * @return {Element} The DOM element for the step tooltip\n   * @private\n   */\n  _createTooltipContent() {\n    const descriptionId = `${this.id}-description`;\n    const labelId = `${this.id}-label`;\n    this.shepherdElementComponent = new Shepherd_element({\n      target: this.tour.options.stepsContainer || document.body,\n      props: {\n        classPrefix: this.classPrefix,\n        descriptionId,\n        labelId,\n        step: this,\n        styles: this.styles\n      }\n    });\n    return this.shepherdElementComponent.getElement();\n  }\n\n  /**\n   * If a custom scrollToHandler is defined, call that, otherwise do the generic\n   * scrollIntoView call.\n   *\n   * @param {boolean|Object} scrollToOptions If true, uses the default `scrollIntoView`,\n   * if an object, passes that object as the params to `scrollIntoView` i.e. `{ behavior: 'smooth', block: 'center' }`\n   * @private\n   */\n  _scrollTo(scrollToOptions) {\n    const {\n      element\n    } = this._getResolvedAttachToOptions();\n    if (isFunction(this.options.scrollToHandler)) {\n      this.options.scrollToHandler(element);\n    } else if (isElement$1(element) && typeof element.scrollIntoView === 'function') {\n      element.scrollIntoView(scrollToOptions);\n    }\n  }\n\n  /**\n   * _getClassOptions gets all possible classes for the step\n   * @param {Object} stepOptions The step specific options\n   * @returns {String} unique string from array of classes\n   * @private\n   */\n  _getClassOptions(stepOptions) {\n    const defaultStepOptions = this.tour && this.tour.options && this.tour.options.defaultStepOptions;\n    const stepClasses = stepOptions.classes ? stepOptions.classes : '';\n    const defaultStepOptionsClasses = defaultStepOptions && defaultStepOptions.classes ? defaultStepOptions.classes : '';\n    const allClasses = [...stepClasses.split(' '), ...defaultStepOptionsClasses.split(' ')];\n    const uniqClasses = new Set(allClasses);\n    return Array.from(uniqClasses).join(' ').trim();\n  }\n\n  /**\n   * Sets the options for the step, maps `when` to events, sets up buttons\n   * @param {Object} options The options for the step\n   * @private\n   */\n  _setOptions(options = {}) {\n    let tourOptions = this.tour && this.tour.options && this.tour.options.defaultStepOptions;\n    tourOptions = cjs({}, tourOptions || {});\n    this.options = Object.assign({\n      arrow: true\n    }, tourOptions, options, mergeTooltipConfig(tourOptions, options));\n    const {\n      when\n    } = this.options;\n    this.options.classes = this._getClassOptions(options);\n    this.destroy();\n    this.id = this.options.id || `step-${uuid()}`;\n    if (when) {\n      Object.keys(when).forEach(event => {\n        this.on(event, when[event], this);\n      });\n    }\n  }\n\n  /**\n   * Create the element and set up the FloatingUI instance\n   * @private\n   */\n  _setupElements() {\n    if (!isUndefined(this.el)) {\n      this.destroy();\n    }\n    this.el = this._createTooltipContent();\n    if (this.options.advanceOn) {\n      bindAdvance(this);\n    }\n\n    // The tooltip implementation details are handled outside of the Step\n    // object.\n    setupTooltip(this);\n  }\n\n  /**\n   * Triggers `before-show`, generates the tooltip DOM content,\n   * sets up a FloatingUI instance for the tooltip, then triggers `show`.\n   * @private\n   */\n  _show() {\n    this.trigger('before-show');\n\n    // Force resolve to make sure the options are updated on subsequent shows.\n    this._resolveAttachToOptions();\n    this._setupElements();\n    if (!this.tour.modal) {\n      this.tour._setupModal();\n    }\n    this.tour.modal.setupForStep(this);\n    this._styleTargetElementForStep(this);\n    this.el.hidden = false;\n\n    // start scrolling to target before showing the step\n    if (this.options.scrollTo) {\n      setTimeout(() => {\n        this._scrollTo(this.options.scrollTo);\n      });\n    }\n    this.el.hidden = false;\n    const content = this.shepherdElementComponent.getElement();\n    const target = this.target || document.body;\n    target.classList.add(`${this.classPrefix}shepherd-enabled`);\n    target.classList.add(`${this.classPrefix}shepherd-target`);\n    content.classList.add('shepherd-enabled');\n    this.trigger('show');\n  }\n\n  /**\n   * Modulates the styles of the passed step's target element, based on the step's options and\n   * the tour's `modal` option, to visually emphasize the element\n   *\n   * @param step The step object that attaches to the element\n   * @private\n   */\n  _styleTargetElementForStep(step) {\n    const targetElement = step.target;\n    if (!targetElement) {\n      return;\n    }\n    if (step.options.highlightClass) {\n      targetElement.classList.add(step.options.highlightClass);\n    }\n    targetElement.classList.remove('shepherd-target-click-disabled');\n    if (step.options.canClickTarget === false) {\n      targetElement.classList.add('shepherd-target-click-disabled');\n    }\n  }\n\n  /**\n   * When a step is hidden, remove the highlightClass and 'shepherd-enabled'\n   * and 'shepherd-target' classes\n   * @private\n   */\n  _updateStepTargetOnHide() {\n    const target = this.target || document.body;\n    if (this.options.highlightClass) {\n      target.classList.remove(this.options.highlightClass);\n    }\n    target.classList.remove('shepherd-target-click-disabled', `${this.classPrefix}shepherd-enabled`, `${this.classPrefix}shepherd-target`);\n  }\n}\n\n/**\n * Cleanup the steps and set pointerEvents back to 'auto'\n * @param tour The tour object\n */\nfunction cleanupSteps(tour) {\n  if (tour) {\n    const {\n      steps\n    } = tour;\n    steps.forEach(step => {\n      if (step.options && step.options.canClickTarget === false && step.options.attachTo) {\n        if (step.target instanceof HTMLElement) {\n          step.target.classList.remove('shepherd-target-click-disabled');\n        }\n      }\n    });\n  }\n}\n\n/**\n * Generates the svg path data for a rounded rectangle overlay\n * @param {Object} dimension - Dimensions of rectangle.\n * @param {number} width - Width.\n * @param {number} height - Height.\n * @param {number} [x=0] - Offset from top left corner in x axis. default 0.\n * @param {number} [y=0] - Offset from top left corner in y axis. default 0.\n * @param {number | { topLeft: number, topRight: number, bottomRight: number, bottomLeft: number }} [r=0] - Corner Radius. Keep this smaller than half of width or height.\n * @returns {string} - Rounded rectangle overlay path data.\n */\nfunction makeOverlayPath({\n  width,\n  height,\n  x = 0,\n  y = 0,\n  r = 0\n}) {\n  const {\n    innerWidth: w,\n    innerHeight: h\n  } = window;\n  const {\n    topLeft = 0,\n    topRight = 0,\n    bottomRight = 0,\n    bottomLeft = 0\n  } = typeof r === 'number' ? {\n    topLeft: r,\n    topRight: r,\n    bottomRight: r,\n    bottomLeft: r\n  } : r;\n  return `M${w},${h}\\\nH0\\\nV0\\\nH${w}\\\nV${h}\\\nZ\\\nM${x + topLeft},${y}\\\na${topLeft},${topLeft},0,0,0-${topLeft},${topLeft}\\\nV${height + y - bottomLeft}\\\na${bottomLeft},${bottomLeft},0,0,0,${bottomLeft},${bottomLeft}\\\nH${width + x - bottomRight}\\\na${bottomRight},${bottomRight},0,0,0,${bottomRight}-${bottomRight}\\\nV${y + topRight}\\\na${topRight},${topRight},0,0,0-${topRight}-${topRight}\\\nZ`;\n}\n\n/* src/js/components/shepherd-modal.svelte generated by Svelte v3.59.2 */\nfunction create_fragment(ctx) {\n  let svg;\n  let path;\n  let svg_class_value;\n  let mounted;\n  let dispose;\n  return {\n    c() {\n      svg = svg_element(\"svg\");\n      path = svg_element(\"path\");\n      attr(path, \"d\", /*pathDefinition*/ctx[2]);\n      attr(svg, \"class\", svg_class_value = `${/*modalIsVisible*/ctx[1] ? 'shepherd-modal-is-visible' : ''} shepherd-modal-overlay-container`);\n    },\n    m(target, anchor) {\n      insert(target, svg, anchor);\n      append(svg, path);\n      /*svg_binding*/\n      ctx[11](svg);\n      if (!mounted) {\n        dispose = listen(svg, \"touchmove\", /*_preventModalOverlayTouch*/ctx[3]);\n        mounted = true;\n      }\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*pathDefinition*/4) {\n        attr(path, \"d\", /*pathDefinition*/ctx[2]);\n      }\n      if (dirty & /*modalIsVisible*/2 && svg_class_value !== (svg_class_value = `${/*modalIsVisible*/ctx[1] ? 'shepherd-modal-is-visible' : ''} shepherd-modal-overlay-container`)) {\n        attr(svg, \"class\", svg_class_value);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(svg);\n      /*svg_binding*/\n      ctx[11](null);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nfunction _getScrollParent(element) {\n  if (!element) {\n    return null;\n  }\n  const isHtmlElement = element instanceof HTMLElement;\n  const overflowY = isHtmlElement && window.getComputedStyle(element).overflowY;\n  const isScrollable = overflowY !== 'hidden' && overflowY !== 'visible';\n  if (isScrollable && element.scrollHeight >= element.clientHeight) {\n    return element;\n  }\n  return _getScrollParent(element.parentElement);\n}\n\n/**\n * Get the visible height of the target element relative to its scrollParent.\n * If there is no scroll parent, the height of the element is returned.\n *\n * @param {HTMLElement} element The target element\n * @param {HTMLElement} [scrollParent] The scrollable parent element\n * @returns {{y: number, height: number}}\n * @private\n */\nfunction _getVisibleHeight(element, scrollParent) {\n  const elementRect = element.getBoundingClientRect();\n  let top = elementRect.y || elementRect.top;\n  let bottom = elementRect.bottom || top + elementRect.height;\n  if (scrollParent) {\n    const scrollRect = scrollParent.getBoundingClientRect();\n    const scrollTop = scrollRect.y || scrollRect.top;\n    const scrollBottom = scrollRect.bottom || scrollTop + scrollRect.height;\n    top = Math.max(top, scrollTop);\n    bottom = Math.min(bottom, scrollBottom);\n  }\n  const height = Math.max(bottom - top, 0); // Default to 0 if height is negative\n  return {\n    y: top,\n    height\n  };\n}\nfunction instance($$self, $$props, $$invalidate) {\n  let {\n    element,\n    openingProperties\n  } = $$props;\n  uuid();\n  let modalIsVisible = false;\n  let rafId = undefined;\n  let pathDefinition;\n  closeModalOpening();\n  const getElement = () => element;\n  function closeModalOpening() {\n    $$invalidate(4, openingProperties = {\n      width: 0,\n      height: 0,\n      x: 0,\n      y: 0,\n      r: 0\n    });\n  }\n  function hide() {\n    $$invalidate(1, modalIsVisible = false);\n\n    // Ensure we cleanup all event listeners when we hide the modal\n    _cleanupStepEventListeners();\n  }\n  function positionModal(modalOverlayOpeningPadding = 0, modalOverlayOpeningRadius = 0, scrollParent, targetElement) {\n    if (targetElement) {\n      const {\n        y,\n        height\n      } = _getVisibleHeight(targetElement, scrollParent);\n      const {\n        x,\n        width,\n        left\n      } = targetElement.getBoundingClientRect();\n\n      // getBoundingClientRect is not consistent. Some browsers use x and y, while others use left and top\n      $$invalidate(4, openingProperties = {\n        width: width + modalOverlayOpeningPadding * 2,\n        height: height + modalOverlayOpeningPadding * 2,\n        x: (x || left) - modalOverlayOpeningPadding,\n        y: y - modalOverlayOpeningPadding,\n        r: modalOverlayOpeningRadius\n      });\n    } else {\n      closeModalOpening();\n    }\n  }\n  function setupForStep(step) {\n    // Ensure we move listeners from the previous step, before we setup new ones\n    _cleanupStepEventListeners();\n    if (step.tour.options.useModalOverlay) {\n      _styleForStep(step);\n      show();\n    } else {\n      hide();\n    }\n  }\n  function show() {\n    $$invalidate(1, modalIsVisible = true);\n  }\n  const _preventModalBodyTouch = e => {\n    e.preventDefault();\n  };\n  const _preventModalOverlayTouch = e => {\n    e.stopPropagation();\n  };\n\n  /**\n  * Add touchmove event listener\n  * @private\n  */\n  function _addStepEventListeners() {\n    // Prevents window from moving on touch.\n    window.addEventListener('touchmove', _preventModalBodyTouch, {\n      passive: false\n    });\n  }\n\n  /**\n  * Cancel the requestAnimationFrame loop and remove touchmove event listeners\n  * @private\n  */\n  function _cleanupStepEventListeners() {\n    if (rafId) {\n      cancelAnimationFrame(rafId);\n      rafId = undefined;\n    }\n    window.removeEventListener('touchmove', _preventModalBodyTouch, {\n      passive: false\n    });\n  }\n\n  /**\n  * Style the modal for the step\n  * @param {Step} step The step to style the opening for\n  * @private\n  */\n  function _styleForStep(step) {\n    const {\n      modalOverlayOpeningPadding,\n      modalOverlayOpeningRadius\n    } = step.options;\n    const scrollParent = _getScrollParent(step.target);\n\n    // Setup recursive function to call requestAnimationFrame to update the modal opening position\n    const rafLoop = () => {\n      rafId = undefined;\n      positionModal(modalOverlayOpeningPadding, modalOverlayOpeningRadius, scrollParent, step.target);\n      rafId = requestAnimationFrame(rafLoop);\n    };\n    rafLoop();\n    _addStepEventListeners();\n  }\n  function svg_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('openingProperties' in $$props) $$invalidate(4, openingProperties = $$props.openingProperties);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*openingProperties*/16) {\n      $$invalidate(2, pathDefinition = makeOverlayPath(openingProperties));\n    }\n  };\n  return [element, modalIsVisible, pathDefinition, _preventModalOverlayTouch, openingProperties, getElement, closeModalOpening, hide, positionModal, setupForStep, show, svg_binding];\n}\nclass Shepherd_modal extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance, create_fragment, safe_not_equal, {\n      element: 0,\n      openingProperties: 4,\n      getElement: 5,\n      closeModalOpening: 6,\n      hide: 7,\n      positionModal: 8,\n      setupForStep: 9,\n      show: 10\n    });\n  }\n  get getElement() {\n    return this.$$.ctx[5];\n  }\n  get closeModalOpening() {\n    return this.$$.ctx[6];\n  }\n  get hide() {\n    return this.$$.ctx[7];\n  }\n  get positionModal() {\n    return this.$$.ctx[8];\n  }\n  get setupForStep() {\n    return this.$$.ctx[9];\n  }\n  get show() {\n    return this.$$.ctx[10];\n  }\n}\nconst Shepherd = new Evented();\n\n/**\n * Class representing the site tour\n * @extends {Evented}\n */\nclass Tour extends Evented {\n  /**\n   * @param {Object} options The options for the tour\n   * @param {boolean | function(): boolean | Promise<boolean> | function(): Promise<boolean>} options.confirmCancel If true, will issue a `window.confirm` before cancelling.\n   * If it is a function(support Async Function), it will be called and wait for the return value, and will only be cancelled if the value returned is true\n   * @param {string} options.confirmCancelMessage The message to display in the `window.confirm` dialog\n   * @param {string} options.classPrefix The prefix to add to the `shepherd-enabled` and `shepherd-target` class names as well as the `data-shepherd-step-id`.\n   * @param {Object} options.defaultStepOptions Default options for Steps ({@link Step#constructor}), created through `addStep`\n   * @param {boolean} options.exitOnEsc Exiting the tour with the escape key will be enabled unless this is explicitly\n   * set to false.\n   * @param {boolean} options.keyboardNavigation Navigating the tour via left and right arrow keys will be enabled\n   * unless this is explicitly set to false.\n   * @param {HTMLElement} options.stepsContainer An optional container element for the steps.\n   * If not set, the steps will be appended to `document.body`.\n   * @param {HTMLElement} options.modalContainer An optional container element for the modal.\n   * If not set, the modal will be appended to `document.body`.\n   * @param {object[] | Step[]} options.steps An array of step options objects or Step instances to initialize the tour with\n   * @param {string} options.tourName An optional \"name\" for the tour. This will be appended to the the tour's\n   * dynamically generated `id` property.\n   * @param {boolean} options.useModalOverlay Whether or not steps should be placed above a darkened\n   * modal overlay. If true, the overlay will create an opening around the target element so that it\n   * can remain interactive\n   * @returns {Tour}\n   */\n  constructor(options = {}) {\n    super(options);\n    autoBind(this);\n    const defaultTourOptions = {\n      exitOnEsc: true,\n      keyboardNavigation: true\n    };\n    this.options = Object.assign({}, defaultTourOptions, options);\n    this.classPrefix = normalizePrefix(this.options.classPrefix);\n    this.steps = [];\n    this.addSteps(this.options.steps);\n\n    // Pass these events onto the global Shepherd object\n    const events = ['active', 'cancel', 'complete', 'inactive', 'show', 'start'];\n    events.map(event => {\n      (e => {\n        this.on(e, opts => {\n          opts = opts || {};\n          opts.tour = this;\n          Shepherd.trigger(e, opts);\n        });\n      })(event);\n    });\n    this._setTourID();\n    return this;\n  }\n\n  /**\n   * Adds a new step to the tour\n   * @param {Object|Step} options An object containing step options or a Step instance\n   * @param {number} index The optional index to insert the step at. If undefined, the step\n   * is added to the end of the array.\n   * @return {Step} The newly added step\n   */\n  addStep(options, index) {\n    let step = options;\n    if (!(step instanceof Step)) {\n      step = new Step(this, step);\n    } else {\n      step.tour = this;\n    }\n    if (!isUndefined(index)) {\n      this.steps.splice(index, 0, step);\n    } else {\n      this.steps.push(step);\n    }\n    return step;\n  }\n\n  /**\n   * Add multiple steps to the tour\n   * @param {Array<object> | Array<Step>} steps The steps to add to the tour\n   */\n  addSteps(steps) {\n    if (Array.isArray(steps)) {\n      steps.forEach(step => {\n        this.addStep(step);\n      });\n    }\n    return this;\n  }\n\n  /**\n   * Go to the previous step in the tour\n   */\n  back() {\n    const index = this.steps.indexOf(this.currentStep);\n    this.show(index - 1, false);\n  }\n\n  /**\n   * Calls _done() triggering the 'cancel' event\n   * If `confirmCancel` is true, will show a window.confirm before cancelling\n   * If `confirmCancel` is a function, will call it and wait for the return value,\n   * and only cancel when the value returned is true\n   */\n  cancel() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.options.confirmCancel) {\n        const confirmCancelIsFunction = typeof _this.options.confirmCancel === 'function';\n        const cancelMessage = _this.options.confirmCancelMessage || 'Are you sure you want to stop the tour?';\n        const stopTour = confirmCancelIsFunction ? yield _this.options.confirmCancel() : window.confirm(cancelMessage);\n        if (stopTour) {\n          _this._done('cancel');\n        }\n      } else {\n        _this._done('cancel');\n      }\n    })();\n  }\n\n  /**\n   * Calls _done() triggering the `complete` event\n   */\n  complete() {\n    this._done('complete');\n  }\n\n  /**\n   * Gets the step from a given id\n   * @param {Number|String} id The id of the step to retrieve\n   * @return {Step} The step corresponding to the `id`\n   */\n  getById(id) {\n    return this.steps.find(step => {\n      return step.id === id;\n    });\n  }\n\n  /**\n   * Gets the current step\n   * @returns {Step|null}\n   */\n  getCurrentStep() {\n    return this.currentStep;\n  }\n\n  /**\n   * Hide the current step\n   */\n  hide() {\n    const currentStep = this.getCurrentStep();\n    if (currentStep) {\n      return currentStep.hide();\n    }\n  }\n\n  /**\n   * Check if the tour is active\n   * @return {boolean}\n   */\n  isActive() {\n    return Shepherd.activeTour === this;\n  }\n\n  /**\n   * Go to the next step in the tour\n   * If we are at the end, call `complete`\n   */\n  next() {\n    const index = this.steps.indexOf(this.currentStep);\n    if (index === this.steps.length - 1) {\n      this.complete();\n    } else {\n      this.show(index + 1, true);\n    }\n  }\n\n  /**\n   * Removes the step from the tour\n   * @param {String} name The id for the step to remove\n   */\n  removeStep(name) {\n    const current = this.getCurrentStep();\n\n    // Find the step, destroy it and remove it from this.steps\n    this.steps.some((step, i) => {\n      if (step.id === name) {\n        if (step.isOpen()) {\n          step.hide();\n        }\n        step.destroy();\n        this.steps.splice(i, 1);\n        return true;\n      }\n    });\n    if (current && current.id === name) {\n      this.currentStep = undefined;\n\n      // If we have steps left, show the first one, otherwise just cancel the tour\n      this.steps.length ? this.show(0) : this.cancel();\n    }\n  }\n\n  /**\n   * Show a specific step in the tour\n   * @param {Number|String} key The key to look up the step by\n   * @param {Boolean} forward True if we are going forward, false if backward\n   */\n  show(key = 0, forward = true) {\n    const step = isString(key) ? this.getById(key) : this.steps[key];\n    if (step) {\n      this._updateStateBeforeShow();\n      const shouldSkipStep = isFunction(step.options.showOn) && !step.options.showOn();\n\n      // If `showOn` returns false, we want to skip the step, otherwise, show the step like normal\n      if (shouldSkipStep) {\n        this._skipStep(step, forward);\n      } else {\n        this.trigger('show', {\n          step,\n          previous: this.currentStep\n        });\n        this.currentStep = step;\n        step.show();\n      }\n    }\n  }\n\n  /**\n   * Start the tour\n   */\n  start() {\n    this.trigger('start');\n\n    // Save the focused element before the tour opens\n    this.focusedElBeforeOpen = document.activeElement;\n    this.currentStep = null;\n    this._setupModal();\n    this._setupActiveTour();\n    this.next();\n  }\n\n  /**\n   * Called whenever the tour is cancelled or completed, basically anytime we exit the tour\n   * @param {String} event The event name to trigger\n   * @private\n   */\n  _done(event) {\n    const index = this.steps.indexOf(this.currentStep);\n    if (Array.isArray(this.steps)) {\n      this.steps.forEach(step => step.destroy());\n    }\n    cleanupSteps(this);\n    this.trigger(event, {\n      index\n    });\n    Shepherd.activeTour = null;\n    this.trigger('inactive', {\n      tour: this\n    });\n    if (this.modal) {\n      this.modal.hide();\n    }\n    if (event === 'cancel' || event === 'complete') {\n      if (this.modal) {\n        const modalContainer = document.querySelector('.shepherd-modal-overlay-container');\n        if (modalContainer) {\n          modalContainer.remove();\n        }\n      }\n    }\n\n    // Focus the element that was focused before the tour started\n    if (isHTMLElement$1(this.focusedElBeforeOpen)) {\n      this.focusedElBeforeOpen.focus();\n    }\n  }\n\n  /**\n   * Make this tour \"active\"\n   * @private\n   */\n  _setupActiveTour() {\n    this.trigger('active', {\n      tour: this\n    });\n    Shepherd.activeTour = this;\n  }\n\n  /**\n   * _setupModal create the modal container and instance\n   * @private\n   */\n  _setupModal() {\n    this.modal = new Shepherd_modal({\n      target: this.options.modalContainer || document.body,\n      props: {\n        classPrefix: this.classPrefix,\n        styles: this.styles\n      }\n    });\n  }\n\n  /**\n   * Called when `showOn` evaluates to false, to skip the step or complete the tour if it's the last step\n   * @param {Step} step The step to skip\n   * @param {Boolean} forward True if we are going forward, false if backward\n   * @private\n   */\n  _skipStep(step, forward) {\n    const index = this.steps.indexOf(step);\n    if (index === this.steps.length - 1) {\n      this.complete();\n    } else {\n      const nextIndex = forward ? index + 1 : index - 1;\n      this.show(nextIndex, forward);\n    }\n  }\n\n  /**\n   * Before showing, hide the current step and if the tour is not\n   * already active, call `this._setupActiveTour`.\n   * @private\n   */\n  _updateStateBeforeShow() {\n    if (this.currentStep) {\n      this.currentStep.hide();\n    }\n    if (!this.isActive()) {\n      this._setupActiveTour();\n    }\n  }\n\n  /**\n   * Sets this.id to `${tourName}--${uuid}`\n   * @private\n   */\n  _setTourID() {\n    const tourName = this.options.tourName || 'tour';\n    this.id = `${tourName}--${uuid()}`;\n  }\n}\nconst isServerSide = typeof window === 'undefined';\nclass NoOp {\n  constructor() {}\n}\nif (isServerSide) {\n  Object.assign(Shepherd, {\n    Tour: NoOp,\n    Step: NoOp\n  });\n} else {\n  Object.assign(Shepherd, {\n    Tour,\n    Step\n  });\n}\nexport { Shepherd as default };", "map": {"version": 3, "names": ["isMergeableObject", "value", "isNonNullObject", "isSpecial", "stringValue", "Object", "prototype", "toString", "call", "isReactElement", "canUseSymbol", "Symbol", "for", "REACT_ELEMENT_TYPE", "$$typeof", "emptyTarget", "val", "Array", "isArray", "cloneUnlessOtherwiseSpecified", "options", "clone", "deepmerge", "defaultArrayMerge", "target", "source", "concat", "map", "element", "getMergeFunction", "key", "customMerge", "getEnumerableOwnPropertySymbols", "getOwnPropertySymbols", "filter", "symbol", "propertyIsEnumerable", "get<PERSON><PERSON><PERSON>", "keys", "propertyIsOnObject", "object", "property", "_", "propertyIsUnsafe", "hasOwnProperty", "mergeObject", "destination", "for<PERSON>ach", "arrayMerge", "sourceIsArray", "targetIsArray", "sourceAndTargetTypesMatch", "all", "deepmergeAll", "array", "Error", "reduce", "prev", "next", "deepmerge_1", "cjs", "isElement$1", "Element", "isHTMLElement$1", "HTMLElement", "isFunction", "isString", "isUndefined", "undefined", "Evented", "on", "event", "handler", "ctx", "once", "bindings", "push", "off", "binding", "index", "splice", "trigger", "args", "context", "apply", "autoBind", "self", "getOwnPropertyNames", "constructor", "i", "length", "bind", "_setupAdvanceOnHandler", "selector", "step", "isOpen", "targetIsEl", "el", "currentTarget", "targetIsSelector", "matches", "tour", "bindAdvance", "advanceOn", "document", "querySelector", "e", "console", "error", "addEventListener", "removeEventListener", "body", "normalizePrefix", "prefix", "char<PERSON>t", "parseAttachTo", "attachTo", "returnOpts", "assign", "shouldCenterStep", "resolvedAttachToOptions", "uuid", "d", "Date", "now", "replace", "c", "r", "Math", "random", "floor", "_extends", "arguments", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "indexOf", "min", "max", "round", "createCoords", "v", "x", "y", "oppositeSideMap", "left", "right", "bottom", "top", "oppositeAlignmentMap", "start", "end", "clamp", "evaluate", "param", "getSide", "placement", "split", "getAlignment", "getOppositeAxis", "axis", "getAxisLength", "getSideAxis", "includes", "getAlignmentAxis", "getAlignmentSides", "rects", "rtl", "alignment", "alignmentAxis", "mainAlignmentSide", "reference", "floating", "getOppositePlacement", "getExpandedPlacements", "oppositePlacement", "getOppositeAlignmentPlacement", "getSideList", "side", "isStart", "lr", "rl", "tb", "bt", "getOppositeAxisPlacements", "flipAlignment", "direction", "list", "expandPaddingObject", "padding", "getPaddingObject", "rectToClientRect", "rect", "width", "height", "_excluded2", "_excluded4", "computeCoordsFromPlacement", "_ref", "sideAxis", "align<PERSON><PERSON><PERSON>", "isVertical", "commonX", "commonY", "commonAlign", "coords", "computePosition$1", "_ref2", "_asyncToGenerator", "config", "strategy", "middleware", "platform", "validMiddleware", "Boolean", "isRTL", "getElementRects", "statefulPlacement", "middlewareData", "resetCount", "name", "fn", "nextX", "nextY", "data", "reset", "initialPlacement", "elements", "_x", "_x2", "_x3", "detectOverflow", "_x4", "_x5", "_detectOverflow", "state", "_await$platform$isEle", "boundary", "rootBoundary", "elementContext", "altBoundary", "paddingObject", "altContext", "clippingClientRect", "getClippingRect", "isElement", "contextElement", "getDocumentElement", "offsetParent", "getOffsetParent", "offsetScale", "getScale", "elementClientRect", "convertOffsetParentRelativeRectToViewportRelativeRect", "arrow", "arrowDimensions", "getDimensions", "isYAxis", "minProp", "maxProp", "clientProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "centerToReference", "largestPossiblePadding", "minPadding", "maxPadding", "min$1", "center", "offset", "shouldAddOffset", "alignmentOffset", "centerOffset", "flip", "_middlewareData$flip", "_evaluate2", "mainAxis", "checkMainAxis", "crossAxis", "checkCrossAxis", "fallbackPlacements", "specifiedFallbackPlacements", "fallbackStrategy", "fallbackAxisSideDirection", "detectOverflowOptions", "isBasePlacement", "placements", "overflow", "overflows", "overflowsData", "sides", "every", "_middlewareData$flip2", "_overflowsData$filter", "nextIndex", "nextPlacement", "resetPlacement", "sort", "a", "b", "_overflowsData$map$so", "acc", "shift", "_evaluate4", "limiter", "mainAxisCoord", "crossAxisCoord", "minSide", "maxSide", "limitedCoords", "limitShift", "rawOffset", "computedOffset", "len", "limitMin", "limitMax", "_middlewareData$offse", "_middlewareData$offse2", "isOriginSide", "getNodeName", "node", "isNode", "nodeName", "toLowerCase", "getWindow", "_node$ownerDocument", "ownerDocument", "defaultView", "window", "documentElement", "Node", "isHTMLElement", "isShadowRoot", "ShadowRoot", "isOverflowElement", "overflowX", "overflowY", "display", "getComputedStyle", "test", "isTableElement", "isContainingBlock", "webkit", "isWebKit", "css", "transform", "perspective", "containerType", "<PERSON><PERSON>ilter", "some", "<PERSON><PERSON><PERSON><PERSON>", "contain", "getContainingBlock", "currentNode", "getParentNode", "isLastTraversableNode", "CSS", "supports", "getNodeScroll", "scrollLeft", "scrollTop", "pageXOffset", "pageYOffset", "result", "assignedSlot", "parentNode", "host", "getNearestOverflowAncestor", "getOverflowAncestors", "_node$ownerDocument2", "scrollableAncestor", "isBody", "win", "visualViewport", "getCssDimensions", "parseFloat", "hasOffset", "offsetWidth", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON>", "$", "unwrapElement", "dom<PERSON>lement", "getBoundingClientRect", "Number", "isFinite", "noOffsets", "getVisualOffsets", "offsetLeft", "offsetTop", "shouldAddVisualOffsets", "isFixed", "floatingOffsetParent", "includeScale", "isFixedStrategy", "clientRect", "scale", "visualOffsets", "offsetWin", "currentIFrame", "frameElement", "iframeScale", "iframeRect", "clientLeft", "paddingLeft", "clientTop", "paddingTop", "isOffsetParentAnElement", "scroll", "offsets", "offsetRect", "getClientRects", "from", "getWindowScrollBarX", "getDocumentRect", "html", "scrollWidth", "clientWidth", "scrollHeight", "clientHeight", "getViewportRect", "visualViewportBased", "getInnerBoundingClientRect", "getClientRectFromClippingAncestor", "clippingAncestor", "hasFixedPositionAncestor", "stopNode", "position", "getClippingElementAncestors", "cache", "cachedResult", "get", "currentContainingBlockComputedStyle", "elementIsFixed", "computedStyle", "currentNodeIsContaining", "shouldDropCurrentNode", "ancestor", "set", "elementClippingAncestors", "_c", "clippingAncestors", "firstClippingAncestor", "clippingRect", "accRect", "getRectRelativeToOffsetParent", "getTrueOffsetParent", "polyfill", "_getElementRects", "getOffsetParentFn", "getDimensionsFn", "_x6", "<PERSON><PERSON><PERSON>", "onMove", "io", "timeoutId", "root", "cleanup", "clearTimeout", "disconnect", "refresh", "skip", "threshold", "insetTop", "insetRight", "insetBottom", "insetLeft", "rootMargin", "isFirstUpdate", "handleObserve", "entries", "ratio", "intersectionRatio", "setTimeout", "IntersectionObserver", "observe", "autoUpdate", "update", "ancestorScroll", "ancestorResize", "elementResize", "ResizeObserver", "layoutShift", "animationFrame", "referenceEl", "ancestors", "passive", "cleanupIo", "reobserveFrame", "resizeObserver", "firstEntry", "unobserve", "cancelAnimationFrame", "requestAnimationFrame", "frameId", "prevRefRect", "frameLoop", "nextRefRect", "computePosition", "Map", "mergedOptions", "platformWithCache", "setupTooltip", "attachToOptions", "_getResolvedAttachToOptions", "floatingUIOptions", "getFloatingUIOptions", "shouldCenter", "content", "shepherdElementComponent", "getElement", "classList", "add", "setPosition", "mergeTooltipConfig", "tourOptions", "destroyTooltip", "then", "floatingUIposition", "Promise", "resolve", "focus", "preventScroll", "style", "dataset", "popperPlacement", "placeArrow", "arrowEl", "arrowX", "arrowY", "addArrow", "noop", "tar", "src", "k", "run", "blank_object", "create", "run_all", "fns", "is_function", "thing", "safe_not_equal", "is_empty", "obj", "append", "append<PERSON><PERSON><PERSON>", "insert", "anchor", "insertBefore", "detach", "<PERSON><PERSON><PERSON><PERSON>", "destroy_each", "iterations", "detaching", "createElement", "svg_element", "createElementNS", "text", "createTextNode", "space", "empty", "listen", "attr", "attribute", "removeAttribute", "getAttribute", "setAttribute", "always_set_through_set_attribute", "set_attributes", "attributes", "descriptors", "getOwnPropertyDescriptors", "__proto__", "cssText", "children", "childNodes", "toggle_class", "toggle", "current_component", "set_current_component", "component", "get_current_component", "onMount", "$$", "on_mount", "afterUpdate", "after_update", "dirty_components", "binding_callbacks", "render_callbacks", "flush_callbacks", "resolved_promise", "update_scheduled", "schedule_update", "flush", "add_render_callback", "seen_callbacks", "Set", "flushidx", "saved_component", "pop", "callback", "has", "clear", "fragment", "before_update", "dirty", "p", "flush_render_callbacks", "filtered", "targets", "outroing", "outros", "group_outros", "check_outros", "transition_in", "block", "local", "delete", "transition_out", "o", "get_spread_update", "levels", "updates", "to_null_out", "accounted_for", "$$scope", "n", "create_component", "mount_component", "customElement", "m", "new_on_destroy", "on_destroy", "destroy_component", "make_dirty", "fill", "init", "instance", "create_fragment", "not_equal", "props", "append_styles", "parent_component", "bound", "on_disconnect", "callbacks", "skip_bound", "ready", "ret", "rest", "hydrate", "nodes", "l", "intro", "SvelteComponent", "$destroy", "$on", "type", "$set", "$$props", "$$set", "create_fragment$8", "button", "button_aria_label_value", "button_class_value", "mounted", "dispose", "disabled", "innerHTML", "new_ctx", "instance$8", "$$self", "$$invalidate", "action", "classes", "label", "secondary", "getConfigOption", "option", "Shepherd_button", "get_each_context", "child_ctx", "slice", "create_if_block$3", "each_1_anchor", "current", "each_value", "each_blocks", "create_each_block", "out", "shepherd<PERSON><PERSON>", "shepherdbutton_changes", "create_fragment$7", "footer", "if_block", "instance$7", "buttons", "<PERSON>_<PERSON>er", "create_fragment$6", "span", "textContent", "instance$6", "cancelIcon", "handleCancelClick", "preventDefault", "cancel", "Shepherd_cancel_icon", "create_fragment$5", "h3", "instance$5", "labelId", "title", "h3_binding", "$$value", "Shepherd_title", "create_if_block_1$1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shepherdtitle_changes", "create_if_block$2", "shepherdcancelicon", "shepherdcancelicon_changes", "create_fragment$4", "header", "t", "if_block0", "if_block1", "enabled", "instance$4", "Shepherd_header", "create_fragment$3", "div", "instance$3", "descriptionId", "div_binding", "Shepherd_<PERSON>", "create_if_block_2", "shepherdheader", "shepherdheader_changes", "create_if_block_1", "shepherdtext", "shepherdtext_changes", "create_if_block$1", "shepherdfooter", "shepherdfooter_changes", "create_fragment$2", "show_if_2", "t0", "show_if_1", "t1", "show_if", "if_block2", "instance$2", "Shepherd_content", "create_if_block", "create_fragment$1", "shepherdcontent", "div_aria_describedby_value", "div_aria_labelledby_value", "div_levels", "role", "tabindex", "div_data", "shepherdcontent_changes", "KEY_TAB", "KEY_ESC", "LEFT_ARROW", "RIGHT_ARROW", "getClassesArray", "className", "instance$1", "classPrefix", "firstFocusableElement", "focusableElements", "lastFocusableElement", "dataStepId", "hasCancelIcon", "hasTitle", "id", "querySelectorAll", "updateDynamicClasses", "removeClasses", "addClasses", "oldClasses", "remove", "newClasses", "handleKeyDown", "keyCode", "shift<PERSON>ey", "activeElement", "contains", "exitOnEsc", "stopPropagation", "keyboardNavigation", "back", "Shepherd_element", "Step", "styles", "_resolvedAttachTo", "_setOptions", "complete", "destroy", "_updateStepTargetOnHide", "getTour", "hide", "modal", "hidden", "_resolveAttachToOptions", "show", "beforeShowPromise", "_show", "updateStepOptions", "get<PERSON><PERSON><PERSON>", "_createTooltipContent", "<PERSON><PERSON><PERSON><PERSON>", "_scrollTo", "scrollToOptions", "scrollToHandler", "scrollIntoView", "_getClassOptions", "stepOptions", "defaultStepOptions", "stepClasses", "defaultStepOptionsClasses", "allClasses", "uniqClasses", "join", "trim", "when", "_setupElements", "_setupModal", "setupForStep", "_styleTargetElementForStep", "scrollTo", "targetElement", "highlightClass", "canClickTarget", "cleanupSteps", "steps", "makeOverlayPath", "innerWidth", "w", "innerHeight", "h", "topLeft", "topRight", "bottomRight", "bottomLeft", "svg", "path", "svg_class_value", "_getScrollParent", "isHtmlElement", "isScrollable", "parentElement", "_getVisibleHeight", "scrollParent", "elementRect", "scrollRect", "scrollBottom", "openingProperties", "modalIsVisible", "rafId", "pathDefinition", "closeModalOpening", "_cleanupStepEventListeners", "positionModal", "modalOverlayOpeningPadding", "modalOverlayOpeningRadius", "useModalOverlay", "_styleForStep", "_preventModalBodyTouch", "_preventModalOverlayTouch", "_addStepEventListeners", "rafL<PERSON>", "svg_binding", "Shepherd_modal", "<PERSON>", "Tour", "defaultTourOptions", "addSteps", "events", "opts", "_setTourID", "addStep", "currentStep", "_this", "confirmCancel", "confirmCancelIsFunction", "cancelMessage", "confirmCancelMessage", "stopTour", "confirm", "_done", "getById", "find", "getCurrentStep", "isActive", "activeTour", "removeStep", "forward", "_updateStateBeforeShow", "shouldSkipStep", "showOn", "_skipStep", "previous", "focusedElBeforeOpen", "_setupActiveTour", "modalContainer", "tourName", "isServerSide", "NoOp", "default"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/shepherd.js/dist/js/shepherd.esm.js"], "sourcesContent": ["/*! shepherd.js 11.2.0 */\n\nvar isMergeableObject = function isMergeableObject(value) {\n  return isNonNullObject(value) && !isSpecial(value);\n};\nfunction isNonNullObject(value) {\n  return !!value && typeof value === 'object';\n}\nfunction isSpecial(value) {\n  var stringValue = Object.prototype.toString.call(value);\n  return stringValue === '[object RegExp]' || stringValue === '[object Date]' || isReactElement(value);\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\nfunction isReactElement(value) {\n  return value.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction emptyTarget(val) {\n  return Array.isArray(val) ? [] : {};\n}\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n  return options.clone !== false && options.isMergeableObject(value) ? deepmerge(emptyTarget(value), value, options) : value;\n}\nfunction defaultArrayMerge(target, source, options) {\n  return target.concat(source).map(function (element) {\n    return cloneUnlessOtherwiseSpecified(element, options);\n  });\n}\nfunction getMergeFunction(key, options) {\n  if (!options.customMerge) {\n    return deepmerge;\n  }\n  var customMerge = options.customMerge(key);\n  return typeof customMerge === 'function' ? customMerge : deepmerge;\n}\nfunction getEnumerableOwnPropertySymbols(target) {\n  return Object.getOwnPropertySymbols ? Object.getOwnPropertySymbols(target).filter(function (symbol) {\n    return Object.propertyIsEnumerable.call(target, symbol);\n  }) : [];\n}\nfunction getKeys(target) {\n  return Object.keys(target).concat(getEnumerableOwnPropertySymbols(target));\n}\nfunction propertyIsOnObject(object, property) {\n  try {\n    return property in object;\n  } catch (_) {\n    return false;\n  }\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n  return propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n  && !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n  && Object.propertyIsEnumerable.call(target, key)); // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n  var destination = {};\n  if (options.isMergeableObject(target)) {\n    getKeys(target).forEach(function (key) {\n      destination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n    });\n  }\n  getKeys(source).forEach(function (key) {\n    if (propertyIsUnsafe(target, key)) {\n      return;\n    }\n    if (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n      destination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n    } else {\n      destination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n    }\n  });\n  return destination;\n}\nfunction deepmerge(target, source, options) {\n  options = options || {};\n  options.arrayMerge = options.arrayMerge || defaultArrayMerge;\n  options.isMergeableObject = options.isMergeableObject || isMergeableObject;\n  // cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n  // implementations can use it. The caller may not replace it.\n  options.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n  var sourceIsArray = Array.isArray(source);\n  var targetIsArray = Array.isArray(target);\n  var sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n  if (!sourceAndTargetTypesMatch) {\n    return cloneUnlessOtherwiseSpecified(source, options);\n  } else if (sourceIsArray) {\n    return options.arrayMerge(target, source, options);\n  } else {\n    return mergeObject(target, source, options);\n  }\n}\ndeepmerge.all = function deepmergeAll(array, options) {\n  if (!Array.isArray(array)) {\n    throw new Error('first argument should be an array');\n  }\n  return array.reduce(function (prev, next) {\n    return deepmerge(prev, next, options);\n  }, {});\n};\nvar deepmerge_1 = deepmerge;\nvar cjs = deepmerge_1;\n\n/**\n * Checks if `value` is classified as an `Element`.\n * @param {*} value The param to check if it is an Element\n */\nfunction isElement$1(value) {\n  return value instanceof Element;\n}\n\n/**\n * Checks if `value` is classified as an `HTMLElement`.\n * @param {*} value The param to check if it is an HTMLElement\n */\nfunction isHTMLElement$1(value) {\n  return value instanceof HTMLElement;\n}\n\n/**\n * Checks if `value` is classified as a `Function` object.\n * @param {*} value The param to check if it is a function\n */\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\n/**\n * Checks if `value` is classified as a `String` object.\n * @param {*} value The param to check if it is a string\n */\nfunction isString(value) {\n  return typeof value === 'string';\n}\n\n/**\n * Checks if `value` is undefined.\n * @param {*} value The param to check if it is undefined\n */\nfunction isUndefined(value) {\n  return value === undefined;\n}\n\nclass Evented {\n  on(event, handler, ctx, once = false) {\n    if (isUndefined(this.bindings)) {\n      this.bindings = {};\n    }\n    if (isUndefined(this.bindings[event])) {\n      this.bindings[event] = [];\n    }\n    this.bindings[event].push({\n      handler,\n      ctx,\n      once\n    });\n    return this;\n  }\n  once(event, handler, ctx) {\n    return this.on(event, handler, ctx, true);\n  }\n  off(event, handler) {\n    if (isUndefined(this.bindings) || isUndefined(this.bindings[event])) {\n      return this;\n    }\n    if (isUndefined(handler)) {\n      delete this.bindings[event];\n    } else {\n      this.bindings[event].forEach((binding, index) => {\n        if (binding.handler === handler) {\n          this.bindings[event].splice(index, 1);\n        }\n      });\n    }\n    return this;\n  }\n  trigger(event, ...args) {\n    if (!isUndefined(this.bindings) && this.bindings[event]) {\n      this.bindings[event].forEach((binding, index) => {\n        const {\n          ctx,\n          handler,\n          once\n        } = binding;\n        const context = ctx || this;\n        handler.apply(context, args);\n        if (once) {\n          this.bindings[event].splice(index, 1);\n        }\n      });\n    }\n    return this;\n  }\n}\n\n/**\n * Binds all the methods on a JS Class to the `this` context of the class.\n * Adapted from https://github.com/sindresorhus/auto-bind\n * @param {object} self The `this` context of the class\n * @return {object} The `this` context of the class\n */\nfunction autoBind(self) {\n  const keys = Object.getOwnPropertyNames(self.constructor.prototype);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    const val = self[key];\n    if (key !== 'constructor' && typeof val === 'function') {\n      self[key] = val.bind(self);\n    }\n  }\n  return self;\n}\n\n/**\n * Sets up the handler to determine if we should advance the tour\n * @param {string} selector\n * @param {Step} step The step instance\n * @return {Function}\n * @private\n */\nfunction _setupAdvanceOnHandler(selector, step) {\n  return event => {\n    if (step.isOpen()) {\n      const targetIsEl = step.el && event.currentTarget === step.el;\n      const targetIsSelector = !isUndefined(selector) && event.currentTarget.matches(selector);\n      if (targetIsSelector || targetIsEl) {\n        step.tour.next();\n      }\n    }\n  };\n}\n\n/**\n * Bind the event handler for advanceOn\n * @param {Step} step The step instance\n */\nfunction bindAdvance(step) {\n  // An empty selector matches the step element\n  const {\n    event,\n    selector\n  } = step.options.advanceOn || {};\n  if (event) {\n    const handler = _setupAdvanceOnHandler(selector, step);\n\n    // TODO: this should also bind/unbind on show/hide\n    let el;\n    try {\n      el = document.querySelector(selector);\n    } catch (e) {\n      // TODO\n    }\n    if (!isUndefined(selector) && !el) {\n      return console.error(`No element was found for the selector supplied to advanceOn: ${selector}`);\n    } else if (el) {\n      el.addEventListener(event, handler);\n      step.on('destroy', () => {\n        return el.removeEventListener(event, handler);\n      });\n    } else {\n      document.body.addEventListener(event, handler, true);\n      step.on('destroy', () => {\n        return document.body.removeEventListener(event, handler, true);\n      });\n    }\n  } else {\n    return console.error('advanceOn was defined, but no event name was passed.');\n  }\n}\n\n/**\n * Ensure class prefix ends in `-`\n * @param {string} prefix The prefix to prepend to the class names generated by nano-css\n * @return {string} The prefix ending in `-`\n */\nfunction normalizePrefix(prefix) {\n  if (!isString(prefix) || prefix === '') {\n    return '';\n  }\n  return prefix.charAt(prefix.length - 1) !== '-' ? `${prefix}-` : prefix;\n}\n\n/**\n * Resolves attachTo options, converting element option value to a qualified HTMLElement.\n * @param {Step} step The step instance\n * @returns {{}|{element, on}}\n * `element` is a qualified HTML Element\n * `on` is a string position value\n */\nfunction parseAttachTo(step) {\n  const options = step.options.attachTo || {};\n  const returnOpts = Object.assign({}, options);\n  if (isFunction(returnOpts.element)) {\n    // Bind the callback to step so that it has access to the object, to enable running additional logic\n    returnOpts.element = returnOpts.element.call(step);\n  }\n  if (isString(returnOpts.element)) {\n    // Can't override the element in user opts reference because we can't\n    // guarantee that the element will exist in the future.\n    try {\n      returnOpts.element = document.querySelector(returnOpts.element);\n    } catch (e) {\n      // TODO\n    }\n    if (!returnOpts.element) {\n      console.error(`The element for this Shepherd step was not found ${options.element}`);\n    }\n  }\n  return returnOpts;\n}\n\n/**\n * Checks if the step should be centered or not. Does not trigger attachTo.element evaluation, making it a pure\n * alternative for the deprecated step.isCentered() method.\n * @param resolvedAttachToOptions\n * @returns {boolean}\n */\nfunction shouldCenterStep(resolvedAttachToOptions) {\n  if (resolvedAttachToOptions === undefined || resolvedAttachToOptions === null) {\n    return true;\n  }\n  return !resolvedAttachToOptions.element || !resolvedAttachToOptions.on;\n}\n\n/**\n * Create a unique id for steps, tours, modals, etc\n * @return {string}\n */\nfunction uuid() {\n  let d = Date.now();\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = (d + Math.random() * 16) % 16 | 0;\n    d = Math.floor(d / 16);\n    return (c == 'x' ? r : r & 0x3 | 0x8).toString(16);\n  });\n}\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\n\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return _extends({\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  }, padding);\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  return _extends({}, rect, {\n    top: rect.y,\n    left: rect.x,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}\n\nconst _excluded2 = [\"mainAxis\", \"crossAxis\", \"fallbackPlacements\", \"fallbackStrategy\", \"fallbackAxisSideDirection\", \"flipAlignment\"],\n  _excluded4 = [\"mainAxis\", \"crossAxis\", \"limiter\"];\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a reference element when it is given a certain positioning strategy.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition$1 = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = _extends({}, middlewareData, {\n      [name]: _extends({}, middlewareData[name], data)\n    });\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n      continue;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? _extends({}, rects.floating, {\n    x,\n    y\n  }) : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. This stops `shift()` from taking action, but can\n    // be worked around by calling it again after the `arrow()` if desired.\n    const shouldAddOffset = getAlignment(placement) != null && center != offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? min$1 - center : max - center : 0;\n    return {\n      [axis]: coords[axis] - alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset + alignmentOffset\n      }\n    };\n  }\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function flip(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const _evaluate2 = evaluate(options, state),\n        {\n          mainAxis: checkMainAxis = true,\n          crossAxis: checkCrossAxis = true,\n          fallbackPlacements: specifiedFallbackPlacements,\n          fallbackStrategy = 'bestFit',\n          fallbackAxisSideDirection = 'none',\n          flipAlignment = true\n        } = _evaluate2,\n        detectOverflowOptions = _objectWithoutPropertiesLoose(_evaluate2, _excluded2);\n      const side = getSide(placement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      if (!specifiedFallbackPlacements && fallbackAxisSideDirection !== 'none') {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          // Try next placement and re-run the lifecycle.\n          return {\n            data: {\n              index: nextIndex,\n              overflows: overflowsData\n            },\n            reset: {\n              placement: nextPlacement\n            }\n          };\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$map$so;\n                const placement = (_overflowsData$map$so = overflowsData.map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$map$so[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function shift(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const _evaluate4 = evaluate(options, state),\n        {\n          mainAxis: checkMainAxis = true,\n          crossAxis: checkCrossAxis = false,\n          limiter = {\n            fn: _ref => {\n              let {\n                x,\n                y\n              } = _ref;\n              return {\n                x,\n                y\n              };\n            }\n          }\n        } = _evaluate4,\n        detectOverflowOptions = _objectWithoutPropertiesLoose(_evaluate4, _excluded4);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn(_extends({}, state, {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      }));\n      return _extends({}, limitedCoords, {\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y\n        }\n      });\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function limitShift(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : _extends({\n        mainAxis: 0,\n        crossAxis: 0\n      }, rawOffset);\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null ? void 0 : (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  // Browsers without `ShadowRoot` support.\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isContainingBlock(element) {\n  const webkit = isWebKit();\n  const css = getComputedStyle(element);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  return css.transform !== 'none' || css.perspective !== 'none' || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else {\n      currentNode = getParentNode(currentNode);\n    }\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.pageXOffset,\n    scrollTop: element.pageYOffset\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor));\n}\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentIFrame = win.frameElement;\n    while (currentIFrame && offsetParent && offsetWin !== win) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentIFrame = getWindow(currentIFrame).frameElement;\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  if (offsetParent === documentElement) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && strategy !== 'fixed') {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y\n  };\n}\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\nfunction getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  return getBoundingClientRect(getDocumentElement(element)).left + getNodeScroll(element).scrollLeft;\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = _extends({}, clippingAncestor, {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y\n    });\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\nfunction getDimensions(element) {\n  return getCssDimensions(element);\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  return element.offsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const window = getWindow(element);\n  if (!isHTMLElement(element)) {\n    return window;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static' && !isContainingBlock(offsetParent))) {\n    return window;\n  }\n  return offsetParent || getContainingBlock(element) || window;\n}\nconst getElementRects = async function getElementRects(_ref) {\n  let {\n    reference,\n    floating,\n    strategy\n  } = _ref;\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  return {\n    reference: getRectRelativeToOffsetParent(reference, await getOffsetParentFn(floating), strategy),\n    floating: _extends({\n      x: 0,\n      y: 0\n    }, await getDimensionsFn(floating))\n  };\n};\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    clearTimeout(timeoutId);\n    io && io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = element.getBoundingClientRect();\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 100);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, _extends({}, options, {\n        // Handle <iframe>s\n        root: root.ownerDocument\n      }));\n    } catch (e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          resizeObserver && resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && (nextRefRect.x !== prevRefRect.x || nextRefRect.y !== prevRefRect.y || nextRefRect.width !== prevRefRect.width || nextRefRect.height !== prevRefRect.height)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo && cleanupIo();\n    resizeObserver && resizeObserver.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a reference element when it is given a certain CSS positioning\n * strategy.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = _extends({\n    platform\n  }, options);\n  const platformWithCache = _extends({}, mergedOptions.platform, {\n    _c: cache\n  });\n  return computePosition$1(reference, floating, _extends({}, mergedOptions, {\n    platform: platformWithCache\n  }));\n};\n\n/**\n * Floating UI Options\n *\n * @typedef {object} FloatingUIOptions\n */\n\n/**\n * Determines options for the tooltip and initializes event listeners.\n *\n * @param {Step} step The step instance\n *\n * @return {FloatingUIOptions}\n */\nfunction setupTooltip(step) {\n  if (step.cleanup) {\n    step.cleanup();\n  }\n  const attachToOptions = step._getResolvedAttachToOptions();\n  let target = attachToOptions.element;\n  const floatingUIOptions = getFloatingUIOptions(attachToOptions, step);\n  const shouldCenter = shouldCenterStep(attachToOptions);\n  if (shouldCenter) {\n    target = document.body;\n    const content = step.shepherdElementComponent.getElement();\n    content.classList.add('shepherd-centered');\n  }\n  step.cleanup = autoUpdate(target, step.el, () => {\n    // The element might have already been removed by the end of the tour.\n    if (!step.el) {\n      step.cleanup();\n      return;\n    }\n    setPosition(target, step, floatingUIOptions, shouldCenter);\n  });\n  step.target = attachToOptions.element;\n  return floatingUIOptions;\n}\n\n/**\n * Merge tooltip options handling nested keys.\n *\n * @param tourOptions - The default tour options.\n * @param options - Step specific options.\n *\n * @return {floatingUIOptions: FloatingUIOptions}\n */\nfunction mergeTooltipConfig(tourOptions, options) {\n  return {\n    floatingUIOptions: cjs(tourOptions.floatingUIOptions || {}, options.floatingUIOptions || {})\n  };\n}\n\n/**\n * Cleanup function called when the step is closed/destroyed.\n *\n * @param {Step} step\n */\nfunction destroyTooltip(step) {\n  if (step.cleanup) {\n    step.cleanup();\n  }\n  step.cleanup = null;\n}\n\n/**\n *\n * @return {Promise<*>}\n */\nfunction setPosition(target, step, floatingUIOptions, shouldCenter) {\n  return computePosition(target, step.el, floatingUIOptions).then(floatingUIposition(step, shouldCenter))\n  // Wait before forcing focus.\n  .then(step => new Promise(resolve => {\n    setTimeout(() => resolve(step), 300);\n  }))\n  // Replaces focusAfterRender modifier.\n  .then(step => {\n    if (step && step.el) {\n      step.el.focus({\n        preventScroll: true\n      });\n    }\n  });\n}\n\n/**\n *\n * @param step\n * @param shouldCenter\n * @return {function({x: *, y: *, placement: *, middlewareData: *}): Promise<unknown>}\n */\nfunction floatingUIposition(step, shouldCenter) {\n  return ({\n    x,\n    y,\n    placement,\n    middlewareData\n  }) => {\n    if (!step.el) {\n      return step;\n    }\n    if (shouldCenter) {\n      Object.assign(step.el.style, {\n        position: 'fixed',\n        left: '50%',\n        top: '50%',\n        transform: 'translate(-50%, -50%)'\n      });\n    } else {\n      Object.assign(step.el.style, {\n        position: 'absolute',\n        left: `${x}px`,\n        top: `${y}px`\n      });\n    }\n    step.el.dataset.popperPlacement = placement;\n    placeArrow(step.el, middlewareData);\n    return step;\n  };\n}\n\n/**\n *\n * @param el\n * @param middlewareData\n */\nfunction placeArrow(el, middlewareData) {\n  const arrowEl = el.querySelector('.shepherd-arrow');\n  if (arrowEl && middlewareData.arrow) {\n    const {\n      x: arrowX,\n      y: arrowY\n    } = middlewareData.arrow;\n    Object.assign(arrowEl.style, {\n      left: arrowX != null ? `${arrowX}px` : '',\n      top: arrowY != null ? `${arrowY}px` : ''\n    });\n  }\n}\n\n/**\n * Gets the `Floating UI` options from a set of base `attachTo` options\n * @param attachToOptions\n * @param {Step} step The step instance\n * @return {Object}\n * @private\n */\nfunction getFloatingUIOptions(attachToOptions, step) {\n  const options = {\n    strategy: 'absolute',\n    middleware: []\n  };\n  const arrowEl = addArrow(step);\n  const shouldCenter = shouldCenterStep(attachToOptions);\n  if (!shouldCenter) {\n    options.middleware.push(flip(),\n    // Replicate PopperJS default behavior.\n    shift({\n      limiter: limitShift(),\n      crossAxis: true\n    }));\n    if (arrowEl) {\n      options.middleware.push(arrow({\n        element: arrowEl\n      }));\n    }\n    options.placement = attachToOptions.on;\n  }\n  return cjs(step.options.floatingUIOptions || {}, options);\n}\n\n/**\n * @param {Step} step\n * @return {HTMLElement|false|null}\n */\nfunction addArrow(step) {\n  if (step.options.arrow && step.el) {\n    return step.el.querySelector('.shepherd-arrow');\n  }\n  return false;\n}\n\nfunction noop() {}\nfunction assign(tar, src) {\n  // @ts-ignore\n  for (const k in src) tar[k] = src[k];\n  return tar;\n}\nfunction run(fn) {\n  return fn();\n}\nfunction blank_object() {\n  return Object.create(null);\n}\nfunction run_all(fns) {\n  fns.forEach(run);\n}\nfunction is_function(thing) {\n  return typeof thing === 'function';\n}\nfunction safe_not_equal(a, b) {\n  return a != a ? b == b : a !== b || a && typeof a === 'object' || typeof a === 'function';\n}\nfunction is_empty(obj) {\n  return Object.keys(obj).length === 0;\n}\nfunction append(target, node) {\n  target.appendChild(node);\n}\nfunction insert(target, node, anchor) {\n  target.insertBefore(node, anchor || null);\n}\nfunction detach(node) {\n  if (node.parentNode) {\n    node.parentNode.removeChild(node);\n  }\n}\nfunction destroy_each(iterations, detaching) {\n  for (let i = 0; i < iterations.length; i += 1) {\n    if (iterations[i]) iterations[i].d(detaching);\n  }\n}\nfunction element(name) {\n  return document.createElement(name);\n}\nfunction svg_element(name) {\n  return document.createElementNS('http://www.w3.org/2000/svg', name);\n}\nfunction text(data) {\n  return document.createTextNode(data);\n}\nfunction space() {\n  return text(' ');\n}\nfunction empty() {\n  return text('');\n}\nfunction listen(node, event, handler, options) {\n  node.addEventListener(event, handler, options);\n  return () => node.removeEventListener(event, handler, options);\n}\nfunction attr(node, attribute, value) {\n  if (value == null) node.removeAttribute(attribute);else if (node.getAttribute(attribute) !== value) node.setAttribute(attribute, value);\n}\n/**\n * List of attributes that should always be set through the attr method,\n * because updating them through the property setter doesn't work reliably.\n * In the example of `width`/`height`, the problem is that the setter only\n * accepts numeric values, but the attribute can also be set to a string like `50%`.\n * If this list becomes too big, rethink this approach.\n */\nconst always_set_through_set_attribute = ['width', 'height'];\nfunction set_attributes(node, attributes) {\n  // @ts-ignore\n  const descriptors = Object.getOwnPropertyDescriptors(node.__proto__);\n  for (const key in attributes) {\n    if (attributes[key] == null) {\n      node.removeAttribute(key);\n    } else if (key === 'style') {\n      node.style.cssText = attributes[key];\n    } else if (key === '__value') {\n      node.value = node[key] = attributes[key];\n    } else if (descriptors[key] && descriptors[key].set && always_set_through_set_attribute.indexOf(key) === -1) {\n      node[key] = attributes[key];\n    } else {\n      attr(node, key, attributes[key]);\n    }\n  }\n}\nfunction children(element) {\n  return Array.from(element.childNodes);\n}\nfunction toggle_class(element, name, toggle) {\n  element.classList[toggle ? 'add' : 'remove'](name);\n}\nlet current_component;\nfunction set_current_component(component) {\n  current_component = component;\n}\nfunction get_current_component() {\n  if (!current_component) throw new Error('Function called outside component initialization');\n  return current_component;\n}\n/**\n * The `onMount` function schedules a callback to run as soon as the component has been mounted to the DOM.\n * It must be called during the component's initialisation (but doesn't need to live *inside* the component;\n * it can be called from an external module).\n *\n * `onMount` does not run inside a [server-side component](/docs#run-time-server-side-component-api).\n *\n * https://svelte.dev/docs#run-time-svelte-onmount\n */\nfunction onMount(fn) {\n  get_current_component().$$.on_mount.push(fn);\n}\n/**\n * Schedules a callback to run immediately after the component has been updated.\n *\n * The first time the callback runs will be after the initial `onMount`\n */\nfunction afterUpdate(fn) {\n  get_current_component().$$.after_update.push(fn);\n}\nconst dirty_components = [];\nconst binding_callbacks = [];\nlet render_callbacks = [];\nconst flush_callbacks = [];\nconst resolved_promise = /* @__PURE__ */Promise.resolve();\nlet update_scheduled = false;\nfunction schedule_update() {\n  if (!update_scheduled) {\n    update_scheduled = true;\n    resolved_promise.then(flush);\n  }\n}\nfunction add_render_callback(fn) {\n  render_callbacks.push(fn);\n}\n// flush() calls callbacks in this order:\n// 1. All beforeUpdate callbacks, in order: parents before children\n// 2. All bind:this callbacks, in reverse order: children before parents.\n// 3. All afterUpdate callbacks, in order: parents before children. EXCEPT\n//    for afterUpdates called during the initial onMount, which are called in\n//    reverse order: children before parents.\n// Since callbacks might update component values, which could trigger another\n// call to flush(), the following steps guard against this:\n// 1. During beforeUpdate, any updated components will be added to the\n//    dirty_components array and will cause a reentrant call to flush(). Because\n//    the flush index is kept outside the function, the reentrant call will pick\n//    up where the earlier call left off and go through all dirty components. The\n//    current_component value is saved and restored so that the reentrant call will\n//    not interfere with the \"parent\" flush() call.\n// 2. bind:this callbacks cannot trigger new flush() calls.\n// 3. During afterUpdate, any updated components will NOT have their afterUpdate\n//    callback called a second time; the seen_callbacks set, outside the flush()\n//    function, guarantees this behavior.\nconst seen_callbacks = new Set();\nlet flushidx = 0; // Do *not* move this inside the flush() function\nfunction flush() {\n  // Do not reenter flush while dirty components are updated, as this can\n  // result in an infinite loop. Instead, let the inner flush handle it.\n  // Reentrancy is ok afterwards for bindings etc.\n  if (flushidx !== 0) {\n    return;\n  }\n  const saved_component = current_component;\n  do {\n    // first, call beforeUpdate functions\n    // and update components\n    try {\n      while (flushidx < dirty_components.length) {\n        const component = dirty_components[flushidx];\n        flushidx++;\n        set_current_component(component);\n        update(component.$$);\n      }\n    } catch (e) {\n      // reset dirty state to not end up in a deadlocked state and then rethrow\n      dirty_components.length = 0;\n      flushidx = 0;\n      throw e;\n    }\n    set_current_component(null);\n    dirty_components.length = 0;\n    flushidx = 0;\n    while (binding_callbacks.length) binding_callbacks.pop()();\n    // then, once components are updated, call\n    // afterUpdate functions. This may cause\n    // subsequent updates...\n    for (let i = 0; i < render_callbacks.length; i += 1) {\n      const callback = render_callbacks[i];\n      if (!seen_callbacks.has(callback)) {\n        // ...so guard against infinite loops\n        seen_callbacks.add(callback);\n        callback();\n      }\n    }\n    render_callbacks.length = 0;\n  } while (dirty_components.length);\n  while (flush_callbacks.length) {\n    flush_callbacks.pop()();\n  }\n  update_scheduled = false;\n  seen_callbacks.clear();\n  set_current_component(saved_component);\n}\nfunction update($$) {\n  if ($$.fragment !== null) {\n    $$.update();\n    run_all($$.before_update);\n    const dirty = $$.dirty;\n    $$.dirty = [-1];\n    $$.fragment && $$.fragment.p($$.ctx, dirty);\n    $$.after_update.forEach(add_render_callback);\n  }\n}\n/**\n * Useful for example to execute remaining `afterUpdate` callbacks before executing `destroy`.\n */\nfunction flush_render_callbacks(fns) {\n  const filtered = [];\n  const targets = [];\n  render_callbacks.forEach(c => fns.indexOf(c) === -1 ? filtered.push(c) : targets.push(c));\n  targets.forEach(c => c());\n  render_callbacks = filtered;\n}\nconst outroing = new Set();\nlet outros;\nfunction group_outros() {\n  outros = {\n    r: 0,\n    c: [],\n    p: outros // parent group\n  };\n}\n\nfunction check_outros() {\n  if (!outros.r) {\n    run_all(outros.c);\n  }\n  outros = outros.p;\n}\nfunction transition_in(block, local) {\n  if (block && block.i) {\n    outroing.delete(block);\n    block.i(local);\n  }\n}\nfunction transition_out(block, local, detach, callback) {\n  if (block && block.o) {\n    if (outroing.has(block)) return;\n    outroing.add(block);\n    outros.c.push(() => {\n      outroing.delete(block);\n      if (callback) {\n        if (detach) block.d(1);\n        callback();\n      }\n    });\n    block.o(local);\n  } else if (callback) {\n    callback();\n  }\n}\nfunction get_spread_update(levels, updates) {\n  const update = {};\n  const to_null_out = {};\n  const accounted_for = {\n    $$scope: 1\n  };\n  let i = levels.length;\n  while (i--) {\n    const o = levels[i];\n    const n = updates[i];\n    if (n) {\n      for (const key in o) {\n        if (!(key in n)) to_null_out[key] = 1;\n      }\n      for (const key in n) {\n        if (!accounted_for[key]) {\n          update[key] = n[key];\n          accounted_for[key] = 1;\n        }\n      }\n      levels[i] = n;\n    } else {\n      for (const key in o) {\n        accounted_for[key] = 1;\n      }\n    }\n  }\n  for (const key in to_null_out) {\n    if (!(key in update)) update[key] = undefined;\n  }\n  return update;\n}\nfunction create_component(block) {\n  block && block.c();\n}\nfunction mount_component(component, target, anchor, customElement) {\n  const {\n    fragment,\n    after_update\n  } = component.$$;\n  fragment && fragment.m(target, anchor);\n  if (!customElement) {\n    // onMount happens before the initial afterUpdate\n    add_render_callback(() => {\n      const new_on_destroy = component.$$.on_mount.map(run).filter(is_function);\n      // if the component was destroyed immediately\n      // it will update the `$$.on_destroy` reference to `null`.\n      // the destructured on_destroy may still reference to the old array\n      if (component.$$.on_destroy) {\n        component.$$.on_destroy.push(...new_on_destroy);\n      } else {\n        // Edge case - component was destroyed immediately,\n        // most likely as a result of a binding initialising\n        run_all(new_on_destroy);\n      }\n      component.$$.on_mount = [];\n    });\n  }\n  after_update.forEach(add_render_callback);\n}\nfunction destroy_component(component, detaching) {\n  const $$ = component.$$;\n  if ($$.fragment !== null) {\n    flush_render_callbacks($$.after_update);\n    run_all($$.on_destroy);\n    $$.fragment && $$.fragment.d(detaching);\n    // TODO null out other refs, including component.$$ (but need to\n    // preserve final state?)\n    $$.on_destroy = $$.fragment = null;\n    $$.ctx = [];\n  }\n}\nfunction make_dirty(component, i) {\n  if (component.$$.dirty[0] === -1) {\n    dirty_components.push(component);\n    schedule_update();\n    component.$$.dirty.fill(0);\n  }\n  component.$$.dirty[i / 31 | 0] |= 1 << i % 31;\n}\nfunction init(component, options, instance, create_fragment, not_equal, props, append_styles, dirty = [-1]) {\n  const parent_component = current_component;\n  set_current_component(component);\n  const $$ = component.$$ = {\n    fragment: null,\n    ctx: [],\n    // state\n    props,\n    update: noop,\n    not_equal,\n    bound: blank_object(),\n    // lifecycle\n    on_mount: [],\n    on_destroy: [],\n    on_disconnect: [],\n    before_update: [],\n    after_update: [],\n    context: new Map(options.context || (parent_component ? parent_component.$$.context : [])),\n    // everything else\n    callbacks: blank_object(),\n    dirty,\n    skip_bound: false,\n    root: options.target || parent_component.$$.root\n  };\n  append_styles && append_styles($$.root);\n  let ready = false;\n  $$.ctx = instance ? instance(component, options.props || {}, (i, ret, ...rest) => {\n    const value = rest.length ? rest[0] : ret;\n    if ($$.ctx && not_equal($$.ctx[i], $$.ctx[i] = value)) {\n      if (!$$.skip_bound && $$.bound[i]) $$.bound[i](value);\n      if (ready) make_dirty(component, i);\n    }\n    return ret;\n  }) : [];\n  $$.update();\n  ready = true;\n  run_all($$.before_update);\n  // `false` as a special case of no DOM component\n  $$.fragment = create_fragment ? create_fragment($$.ctx) : false;\n  if (options.target) {\n    if (options.hydrate) {\n      const nodes = children(options.target);\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      $$.fragment && $$.fragment.l(nodes);\n      nodes.forEach(detach);\n    } else {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      $$.fragment && $$.fragment.c();\n    }\n    if (options.intro) transition_in(component.$$.fragment);\n    mount_component(component, options.target, options.anchor, options.customElement);\n    flush();\n  }\n  set_current_component(parent_component);\n}\n/**\n * Base class for Svelte components. Used when dev=false.\n */\nclass SvelteComponent {\n  $destroy() {\n    destroy_component(this, 1);\n    this.$destroy = noop;\n  }\n  $on(type, callback) {\n    if (!is_function(callback)) {\n      return noop;\n    }\n    const callbacks = this.$$.callbacks[type] || (this.$$.callbacks[type] = []);\n    callbacks.push(callback);\n    return () => {\n      const index = callbacks.indexOf(callback);\n      if (index !== -1) callbacks.splice(index, 1);\n    };\n  }\n  $set($$props) {\n    if (this.$$set && !is_empty($$props)) {\n      this.$$.skip_bound = true;\n      this.$$set($$props);\n      this.$$.skip_bound = false;\n    }\n  }\n}\n\n/* src/js/components/shepherd-button.svelte generated by Svelte v3.59.2 */\nfunction create_fragment$8(ctx) {\n  let button;\n  let button_aria_label_value;\n  let button_class_value;\n  let mounted;\n  let dispose;\n  return {\n    c() {\n      button = element(\"button\");\n      attr(button, \"aria-label\", button_aria_label_value = /*label*/ctx[3] ? /*label*/ctx[3] : null);\n      attr(button, \"class\", button_class_value = `${/*classes*/ctx[1] || ''} shepherd-button ${/*secondary*/ctx[4] ? 'shepherd-button-secondary' : ''}`);\n      button.disabled = /*disabled*/ctx[2];\n      attr(button, \"tabindex\", \"0\");\n    },\n    m(target, anchor) {\n      insert(target, button, anchor);\n      button.innerHTML = /*text*/ctx[5];\n      if (!mounted) {\n        dispose = listen(button, \"click\", function () {\n          if (is_function( /*action*/ctx[0])) /*action*/ctx[0].apply(this, arguments);\n        });\n        mounted = true;\n      }\n    },\n    p(new_ctx, [dirty]) {\n      ctx = new_ctx;\n      if (dirty & /*text*/32) button.innerHTML = /*text*/ctx[5];\n      if (dirty & /*label*/8 && button_aria_label_value !== (button_aria_label_value = /*label*/ctx[3] ? /*label*/ctx[3] : null)) {\n        attr(button, \"aria-label\", button_aria_label_value);\n      }\n      if (dirty & /*classes, secondary*/18 && button_class_value !== (button_class_value = `${/*classes*/ctx[1] || ''} shepherd-button ${/*secondary*/ctx[4] ? 'shepherd-button-secondary' : ''}`)) {\n        attr(button, \"class\", button_class_value);\n      }\n      if (dirty & /*disabled*/4) {\n        button.disabled = /*disabled*/ctx[2];\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(button);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nfunction instance$8($$self, $$props, $$invalidate) {\n  let {\n    config,\n    step\n  } = $$props;\n  let action, classes, disabled, label, secondary, text;\n  function getConfigOption(option) {\n    if (isFunction(option)) {\n      return option = option.call(step);\n    }\n    return option;\n  }\n  $$self.$$set = $$props => {\n    if ('config' in $$props) $$invalidate(6, config = $$props.config);\n    if ('step' in $$props) $$invalidate(7, step = $$props.step);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*config, step*/192) {\n      {\n        $$invalidate(0, action = config.action ? config.action.bind(step.tour) : null);\n        $$invalidate(1, classes = config.classes);\n        $$invalidate(2, disabled = config.disabled ? getConfigOption(config.disabled) : false);\n        $$invalidate(3, label = config.label ? getConfigOption(config.label) : null);\n        $$invalidate(4, secondary = config.secondary);\n        $$invalidate(5, text = config.text ? getConfigOption(config.text) : null);\n      }\n    }\n  };\n  return [action, classes, disabled, label, secondary, text, config, step];\n}\nclass Shepherd_button extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$8, create_fragment$8, safe_not_equal, {\n      config: 6,\n      step: 7\n    });\n  }\n}\n\n/* src/js/components/shepherd-footer.svelte generated by Svelte v3.59.2 */\nfunction get_each_context(ctx, list, i) {\n  const child_ctx = ctx.slice();\n  child_ctx[2] = list[i];\n  return child_ctx;\n}\n\n// (24:4) {#if buttons}\nfunction create_if_block$3(ctx) {\n  let each_1_anchor;\n  let current;\n  let each_value = /*buttons*/ctx[1];\n  let each_blocks = [];\n  for (let i = 0; i < each_value.length; i += 1) {\n    each_blocks[i] = create_each_block(get_each_context(ctx, each_value, i));\n  }\n  const out = i => transition_out(each_blocks[i], 1, 1, () => {\n    each_blocks[i] = null;\n  });\n  return {\n    c() {\n      for (let i = 0; i < each_blocks.length; i += 1) {\n        each_blocks[i].c();\n      }\n      each_1_anchor = empty();\n    },\n    m(target, anchor) {\n      for (let i = 0; i < each_blocks.length; i += 1) {\n        if (each_blocks[i]) {\n          each_blocks[i].m(target, anchor);\n        }\n      }\n      insert(target, each_1_anchor, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      if (dirty & /*buttons, step*/3) {\n        each_value = /*buttons*/ctx[1];\n        let i;\n        for (i = 0; i < each_value.length; i += 1) {\n          const child_ctx = get_each_context(ctx, each_value, i);\n          if (each_blocks[i]) {\n            each_blocks[i].p(child_ctx, dirty);\n            transition_in(each_blocks[i], 1);\n          } else {\n            each_blocks[i] = create_each_block(child_ctx);\n            each_blocks[i].c();\n            transition_in(each_blocks[i], 1);\n            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);\n          }\n        }\n        group_outros();\n        for (i = each_value.length; i < each_blocks.length; i += 1) {\n          out(i);\n        }\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      for (let i = 0; i < each_value.length; i += 1) {\n        transition_in(each_blocks[i]);\n      }\n      current = true;\n    },\n    o(local) {\n      each_blocks = each_blocks.filter(Boolean);\n      for (let i = 0; i < each_blocks.length; i += 1) {\n        transition_out(each_blocks[i]);\n      }\n      current = false;\n    },\n    d(detaching) {\n      destroy_each(each_blocks, detaching);\n      if (detaching) detach(each_1_anchor);\n    }\n  };\n}\n\n// (25:8) {#each buttons as config}\nfunction create_each_block(ctx) {\n  let shepherdbutton;\n  let current;\n  shepherdbutton = new Shepherd_button({\n    props: {\n      config: /*config*/ctx[2],\n      step: /*step*/ctx[0]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdbutton.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdbutton, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdbutton_changes = {};\n      if (dirty & /*buttons*/2) shepherdbutton_changes.config = /*config*/ctx[2];\n      if (dirty & /*step*/1) shepherdbutton_changes.step = /*step*/ctx[0];\n      shepherdbutton.$set(shepherdbutton_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdbutton.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdbutton.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdbutton, detaching);\n    }\n  };\n}\nfunction create_fragment$7(ctx) {\n  let footer;\n  let current;\n  let if_block = /*buttons*/ctx[1] && create_if_block$3(ctx);\n  return {\n    c() {\n      footer = element(\"footer\");\n      if (if_block) if_block.c();\n      attr(footer, \"class\", \"shepherd-footer\");\n    },\n    m(target, anchor) {\n      insert(target, footer, anchor);\n      if (if_block) if_block.m(footer, null);\n      current = true;\n    },\n    p(ctx, [dirty]) {\n      if ( /*buttons*/ctx[1]) {\n        if (if_block) {\n          if_block.p(ctx, dirty);\n          if (dirty & /*buttons*/2) {\n            transition_in(if_block, 1);\n          }\n        } else {\n          if_block = create_if_block$3(ctx);\n          if_block.c();\n          transition_in(if_block, 1);\n          if_block.m(footer, null);\n        }\n      } else if (if_block) {\n        group_outros();\n        transition_out(if_block, 1, 1, () => {\n          if_block = null;\n        });\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      transition_in(if_block);\n      current = true;\n    },\n    o(local) {\n      transition_out(if_block);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) detach(footer);\n      if (if_block) if_block.d();\n    }\n  };\n}\nfunction instance$7($$self, $$props, $$invalidate) {\n  let buttons;\n  let {\n    step\n  } = $$props;\n  $$self.$$set = $$props => {\n    if ('step' in $$props) $$invalidate(0, step = $$props.step);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*step*/1) {\n      $$invalidate(1, buttons = step.options.buttons);\n    }\n  };\n  return [step, buttons];\n}\nclass Shepherd_footer extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$7, create_fragment$7, safe_not_equal, {\n      step: 0\n    });\n  }\n}\n\n/* src/js/components/shepherd-cancel-icon.svelte generated by Svelte v3.59.2 */\nfunction create_fragment$6(ctx) {\n  let button;\n  let span;\n  let button_aria_label_value;\n  let mounted;\n  let dispose;\n  return {\n    c() {\n      button = element(\"button\");\n      span = element(\"span\");\n      span.textContent = \"×\";\n      attr(span, \"aria-hidden\", \"true\");\n      attr(button, \"aria-label\", button_aria_label_value = /*cancelIcon*/ctx[0].label ? /*cancelIcon*/ctx[0].label : 'Close Tour');\n      attr(button, \"class\", \"shepherd-cancel-icon\");\n      attr(button, \"type\", \"button\");\n    },\n    m(target, anchor) {\n      insert(target, button, anchor);\n      append(button, span);\n      if (!mounted) {\n        dispose = listen(button, \"click\", /*handleCancelClick*/ctx[1]);\n        mounted = true;\n      }\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*cancelIcon*/1 && button_aria_label_value !== (button_aria_label_value = /*cancelIcon*/ctx[0].label ? /*cancelIcon*/ctx[0].label : 'Close Tour')) {\n        attr(button, \"aria-label\", button_aria_label_value);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(button);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nfunction instance$6($$self, $$props, $$invalidate) {\n  let {\n    cancelIcon,\n    step\n  } = $$props;\n\n  /**\n  * Add a click listener to the cancel link that cancels the tour\n  */\n  const handleCancelClick = e => {\n    e.preventDefault();\n    step.cancel();\n  };\n  $$self.$$set = $$props => {\n    if ('cancelIcon' in $$props) $$invalidate(0, cancelIcon = $$props.cancelIcon);\n    if ('step' in $$props) $$invalidate(2, step = $$props.step);\n  };\n  return [cancelIcon, handleCancelClick, step];\n}\nclass Shepherd_cancel_icon extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$6, create_fragment$6, safe_not_equal, {\n      cancelIcon: 0,\n      step: 2\n    });\n  }\n}\n\n/* src/js/components/shepherd-title.svelte generated by Svelte v3.59.2 */\nfunction create_fragment$5(ctx) {\n  let h3;\n  return {\n    c() {\n      h3 = element(\"h3\");\n      attr(h3, \"id\", /*labelId*/ctx[1]);\n      attr(h3, \"class\", \"shepherd-title\");\n    },\n    m(target, anchor) {\n      insert(target, h3, anchor);\n      /*h3_binding*/\n      ctx[3](h3);\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*labelId*/2) {\n        attr(h3, \"id\", /*labelId*/ctx[1]);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(h3);\n      /*h3_binding*/\n      ctx[3](null);\n    }\n  };\n}\nfunction instance$5($$self, $$props, $$invalidate) {\n  let {\n    labelId,\n    element,\n    title\n  } = $$props;\n  afterUpdate(() => {\n    if (isFunction(title)) {\n      $$invalidate(2, title = title());\n    }\n    $$invalidate(0, element.innerHTML = title, element);\n  });\n  function h3_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('labelId' in $$props) $$invalidate(1, labelId = $$props.labelId);\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('title' in $$props) $$invalidate(2, title = $$props.title);\n  };\n  return [element, labelId, title, h3_binding];\n}\nclass Shepherd_title extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$5, create_fragment$5, safe_not_equal, {\n      labelId: 1,\n      element: 0,\n      title: 2\n    });\n  }\n}\n\n/* src/js/components/shepherd-header.svelte generated by Svelte v3.59.2 */\nfunction create_if_block_1$1(ctx) {\n  let shepherdtitle;\n  let current;\n  shepherdtitle = new Shepherd_title({\n    props: {\n      labelId: /*labelId*/ctx[0],\n      title: /*title*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdtitle.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdtitle, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdtitle_changes = {};\n      if (dirty & /*labelId*/1) shepherdtitle_changes.labelId = /*labelId*/ctx[0];\n      if (dirty & /*title*/4) shepherdtitle_changes.title = /*title*/ctx[2];\n      shepherdtitle.$set(shepherdtitle_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdtitle.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdtitle.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdtitle, detaching);\n    }\n  };\n}\n\n// (39:4) {#if cancelIcon && cancelIcon.enabled}\nfunction create_if_block$2(ctx) {\n  let shepherdcancelicon;\n  let current;\n  shepherdcancelicon = new Shepherd_cancel_icon({\n    props: {\n      cancelIcon: /*cancelIcon*/ctx[3],\n      step: /*step*/ctx[1]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdcancelicon.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdcancelicon, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdcancelicon_changes = {};\n      if (dirty & /*cancelIcon*/8) shepherdcancelicon_changes.cancelIcon = /*cancelIcon*/ctx[3];\n      if (dirty & /*step*/2) shepherdcancelicon_changes.step = /*step*/ctx[1];\n      shepherdcancelicon.$set(shepherdcancelicon_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdcancelicon.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdcancelicon.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdcancelicon, detaching);\n    }\n  };\n}\nfunction create_fragment$4(ctx) {\n  let header;\n  let t;\n  let current;\n  let if_block0 = /*title*/ctx[2] && create_if_block_1$1(ctx);\n  let if_block1 = /*cancelIcon*/ctx[3] && /*cancelIcon*/ctx[3].enabled && create_if_block$2(ctx);\n  return {\n    c() {\n      header = element(\"header\");\n      if (if_block0) if_block0.c();\n      t = space();\n      if (if_block1) if_block1.c();\n      attr(header, \"class\", \"shepherd-header\");\n    },\n    m(target, anchor) {\n      insert(target, header, anchor);\n      if (if_block0) if_block0.m(header, null);\n      append(header, t);\n      if (if_block1) if_block1.m(header, null);\n      current = true;\n    },\n    p(ctx, [dirty]) {\n      if ( /*title*/ctx[2]) {\n        if (if_block0) {\n          if_block0.p(ctx, dirty);\n          if (dirty & /*title*/4) {\n            transition_in(if_block0, 1);\n          }\n        } else {\n          if_block0 = create_if_block_1$1(ctx);\n          if_block0.c();\n          transition_in(if_block0, 1);\n          if_block0.m(header, t);\n        }\n      } else if (if_block0) {\n        group_outros();\n        transition_out(if_block0, 1, 1, () => {\n          if_block0 = null;\n        });\n        check_outros();\n      }\n      if ( /*cancelIcon*/ctx[3] && /*cancelIcon*/ctx[3].enabled) {\n        if (if_block1) {\n          if_block1.p(ctx, dirty);\n          if (dirty & /*cancelIcon*/8) {\n            transition_in(if_block1, 1);\n          }\n        } else {\n          if_block1 = create_if_block$2(ctx);\n          if_block1.c();\n          transition_in(if_block1, 1);\n          if_block1.m(header, null);\n        }\n      } else if (if_block1) {\n        group_outros();\n        transition_out(if_block1, 1, 1, () => {\n          if_block1 = null;\n        });\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      transition_in(if_block0);\n      transition_in(if_block1);\n      current = true;\n    },\n    o(local) {\n      transition_out(if_block0);\n      transition_out(if_block1);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) detach(header);\n      if (if_block0) if_block0.d();\n      if (if_block1) if_block1.d();\n    }\n  };\n}\nfunction instance$4($$self, $$props, $$invalidate) {\n  let {\n    labelId,\n    step\n  } = $$props;\n  let title, cancelIcon;\n  $$self.$$set = $$props => {\n    if ('labelId' in $$props) $$invalidate(0, labelId = $$props.labelId);\n    if ('step' in $$props) $$invalidate(1, step = $$props.step);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*step*/2) {\n      {\n        $$invalidate(2, title = step.options.title);\n        $$invalidate(3, cancelIcon = step.options.cancelIcon);\n      }\n    }\n  };\n  return [labelId, step, title, cancelIcon];\n}\nclass Shepherd_header extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$4, create_fragment$4, safe_not_equal, {\n      labelId: 0,\n      step: 1\n    });\n  }\n}\n\n/* src/js/components/shepherd-text.svelte generated by Svelte v3.59.2 */\nfunction create_fragment$3(ctx) {\n  let div;\n  return {\n    c() {\n      div = element(\"div\");\n      attr(div, \"class\", \"shepherd-text\");\n      attr(div, \"id\", /*descriptionId*/ctx[1]);\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n      /*div_binding*/\n      ctx[3](div);\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*descriptionId*/2) {\n        attr(div, \"id\", /*descriptionId*/ctx[1]);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(div);\n      /*div_binding*/\n      ctx[3](null);\n    }\n  };\n}\nfunction instance$3($$self, $$props, $$invalidate) {\n  let {\n    descriptionId,\n    element,\n    step\n  } = $$props;\n  afterUpdate(() => {\n    let {\n      text\n    } = step.options;\n    if (isFunction(text)) {\n      text = text.call(step);\n    }\n    if (isHTMLElement$1(text)) {\n      element.appendChild(text);\n    } else {\n      $$invalidate(0, element.innerHTML = text, element);\n    }\n  });\n  function div_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('descriptionId' in $$props) $$invalidate(1, descriptionId = $$props.descriptionId);\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('step' in $$props) $$invalidate(2, step = $$props.step);\n  };\n  return [element, descriptionId, step, div_binding];\n}\nclass Shepherd_text extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$3, create_fragment$3, safe_not_equal, {\n      descriptionId: 1,\n      element: 0,\n      step: 2\n    });\n  }\n}\n\n/* src/js/components/shepherd-content.svelte generated by Svelte v3.59.2 */\nfunction create_if_block_2(ctx) {\n  let shepherdheader;\n  let current;\n  shepherdheader = new Shepherd_header({\n    props: {\n      labelId: /*labelId*/ctx[1],\n      step: /*step*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdheader.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdheader, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdheader_changes = {};\n      if (dirty & /*labelId*/2) shepherdheader_changes.labelId = /*labelId*/ctx[1];\n      if (dirty & /*step*/4) shepherdheader_changes.step = /*step*/ctx[2];\n      shepherdheader.$set(shepherdheader_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdheader.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdheader.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdheader, detaching);\n    }\n  };\n}\n\n// (28:2) {#if !isUndefined(step.options.text)}\nfunction create_if_block_1(ctx) {\n  let shepherdtext;\n  let current;\n  shepherdtext = new Shepherd_text({\n    props: {\n      descriptionId: /*descriptionId*/ctx[0],\n      step: /*step*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdtext.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdtext, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdtext_changes = {};\n      if (dirty & /*descriptionId*/1) shepherdtext_changes.descriptionId = /*descriptionId*/ctx[0];\n      if (dirty & /*step*/4) shepherdtext_changes.step = /*step*/ctx[2];\n      shepherdtext.$set(shepherdtext_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdtext.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdtext.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdtext, detaching);\n    }\n  };\n}\n\n// (35:2) {#if Array.isArray(step.options.buttons) && step.options.buttons.length}\nfunction create_if_block$1(ctx) {\n  let shepherdfooter;\n  let current;\n  shepherdfooter = new Shepherd_footer({\n    props: {\n      step: /*step*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdfooter.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdfooter, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdfooter_changes = {};\n      if (dirty & /*step*/4) shepherdfooter_changes.step = /*step*/ctx[2];\n      shepherdfooter.$set(shepherdfooter_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdfooter.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdfooter.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdfooter, detaching);\n    }\n  };\n}\nfunction create_fragment$2(ctx) {\n  let div;\n  let show_if_2 = !isUndefined( /*step*/ctx[2].options.title) || /*step*/ctx[2].options.cancelIcon && /*step*/ctx[2].options.cancelIcon.enabled;\n  let t0;\n  let show_if_1 = !isUndefined( /*step*/ctx[2].options.text);\n  let t1;\n  let show_if = Array.isArray( /*step*/ctx[2].options.buttons) && /*step*/ctx[2].options.buttons.length;\n  let current;\n  let if_block0 = show_if_2 && create_if_block_2(ctx);\n  let if_block1 = show_if_1 && create_if_block_1(ctx);\n  let if_block2 = show_if && create_if_block$1(ctx);\n  return {\n    c() {\n      div = element(\"div\");\n      if (if_block0) if_block0.c();\n      t0 = space();\n      if (if_block1) if_block1.c();\n      t1 = space();\n      if (if_block2) if_block2.c();\n      attr(div, \"class\", \"shepherd-content\");\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n      if (if_block0) if_block0.m(div, null);\n      append(div, t0);\n      if (if_block1) if_block1.m(div, null);\n      append(div, t1);\n      if (if_block2) if_block2.m(div, null);\n      current = true;\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*step*/4) show_if_2 = !isUndefined( /*step*/ctx[2].options.title) || /*step*/ctx[2].options.cancelIcon && /*step*/ctx[2].options.cancelIcon.enabled;\n      if (show_if_2) {\n        if (if_block0) {\n          if_block0.p(ctx, dirty);\n          if (dirty & /*step*/4) {\n            transition_in(if_block0, 1);\n          }\n        } else {\n          if_block0 = create_if_block_2(ctx);\n          if_block0.c();\n          transition_in(if_block0, 1);\n          if_block0.m(div, t0);\n        }\n      } else if (if_block0) {\n        group_outros();\n        transition_out(if_block0, 1, 1, () => {\n          if_block0 = null;\n        });\n        check_outros();\n      }\n      if (dirty & /*step*/4) show_if_1 = !isUndefined( /*step*/ctx[2].options.text);\n      if (show_if_1) {\n        if (if_block1) {\n          if_block1.p(ctx, dirty);\n          if (dirty & /*step*/4) {\n            transition_in(if_block1, 1);\n          }\n        } else {\n          if_block1 = create_if_block_1(ctx);\n          if_block1.c();\n          transition_in(if_block1, 1);\n          if_block1.m(div, t1);\n        }\n      } else if (if_block1) {\n        group_outros();\n        transition_out(if_block1, 1, 1, () => {\n          if_block1 = null;\n        });\n        check_outros();\n      }\n      if (dirty & /*step*/4) show_if = Array.isArray( /*step*/ctx[2].options.buttons) && /*step*/ctx[2].options.buttons.length;\n      if (show_if) {\n        if (if_block2) {\n          if_block2.p(ctx, dirty);\n          if (dirty & /*step*/4) {\n            transition_in(if_block2, 1);\n          }\n        } else {\n          if_block2 = create_if_block$1(ctx);\n          if_block2.c();\n          transition_in(if_block2, 1);\n          if_block2.m(div, null);\n        }\n      } else if (if_block2) {\n        group_outros();\n        transition_out(if_block2, 1, 1, () => {\n          if_block2 = null;\n        });\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      transition_in(if_block0);\n      transition_in(if_block1);\n      transition_in(if_block2);\n      current = true;\n    },\n    o(local) {\n      transition_out(if_block0);\n      transition_out(if_block1);\n      transition_out(if_block2);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) detach(div);\n      if (if_block0) if_block0.d();\n      if (if_block1) if_block1.d();\n      if (if_block2) if_block2.d();\n    }\n  };\n}\nfunction instance$2($$self, $$props, $$invalidate) {\n  let {\n    descriptionId,\n    labelId,\n    step\n  } = $$props;\n  $$self.$$set = $$props => {\n    if ('descriptionId' in $$props) $$invalidate(0, descriptionId = $$props.descriptionId);\n    if ('labelId' in $$props) $$invalidate(1, labelId = $$props.labelId);\n    if ('step' in $$props) $$invalidate(2, step = $$props.step);\n  };\n  return [descriptionId, labelId, step];\n}\nclass Shepherd_content extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$2, create_fragment$2, safe_not_equal, {\n      descriptionId: 0,\n      labelId: 1,\n      step: 2\n    });\n  }\n}\n\n/* src/js/components/shepherd-element.svelte generated by Svelte v3.59.2 */\nfunction create_if_block(ctx) {\n  let div;\n  return {\n    c() {\n      div = element(\"div\");\n      attr(div, \"class\", \"shepherd-arrow\");\n      attr(div, \"data-popper-arrow\", \"\");\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n    },\n    d(detaching) {\n      if (detaching) detach(div);\n    }\n  };\n}\nfunction create_fragment$1(ctx) {\n  let div;\n  let t;\n  let shepherdcontent;\n  let div_aria_describedby_value;\n  let div_aria_labelledby_value;\n  let current;\n  let mounted;\n  let dispose;\n  let if_block = /*step*/ctx[4].options.arrow && /*step*/ctx[4].options.attachTo && /*step*/ctx[4].options.attachTo.element && /*step*/ctx[4].options.attachTo.on && create_if_block();\n  shepherdcontent = new Shepherd_content({\n    props: {\n      descriptionId: /*descriptionId*/ctx[2],\n      labelId: /*labelId*/ctx[3],\n      step: /*step*/ctx[4]\n    }\n  });\n  let div_levels = [{\n    \"aria-describedby\": div_aria_describedby_value = !isUndefined( /*step*/ctx[4].options.text) ? /*descriptionId*/ctx[2] : null\n  }, {\n    \"aria-labelledby\": div_aria_labelledby_value = /*step*/ctx[4].options.title ? /*labelId*/ctx[3] : null\n  }, /*dataStepId*/ctx[1], {\n    role: \"dialog\"\n  }, {\n    tabindex: \"0\"\n  }];\n  let div_data = {};\n  for (let i = 0; i < div_levels.length; i += 1) {\n    div_data = assign(div_data, div_levels[i]);\n  }\n  return {\n    c() {\n      div = element(\"div\");\n      if (if_block) if_block.c();\n      t = space();\n      create_component(shepherdcontent.$$.fragment);\n      set_attributes(div, div_data);\n      toggle_class(div, \"shepherd-has-cancel-icon\", /*hasCancelIcon*/ctx[5]);\n      toggle_class(div, \"shepherd-has-title\", /*hasTitle*/ctx[6]);\n      toggle_class(div, \"shepherd-element\", true);\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n      if (if_block) if_block.m(div, null);\n      append(div, t);\n      mount_component(shepherdcontent, div, null);\n      /*div_binding*/\n      ctx[13](div);\n      current = true;\n      if (!mounted) {\n        dispose = listen(div, \"keydown\", /*handleKeyDown*/ctx[7]);\n        mounted = true;\n      }\n    },\n    p(ctx, [dirty]) {\n      if ( /*step*/ctx[4].options.arrow && /*step*/ctx[4].options.attachTo && /*step*/ctx[4].options.attachTo.element && /*step*/ctx[4].options.attachTo.on) {\n        if (if_block) ; else {\n          if_block = create_if_block();\n          if_block.c();\n          if_block.m(div, t);\n        }\n      } else if (if_block) {\n        if_block.d(1);\n        if_block = null;\n      }\n      const shepherdcontent_changes = {};\n      if (dirty & /*descriptionId*/4) shepherdcontent_changes.descriptionId = /*descriptionId*/ctx[2];\n      if (dirty & /*labelId*/8) shepherdcontent_changes.labelId = /*labelId*/ctx[3];\n      if (dirty & /*step*/16) shepherdcontent_changes.step = /*step*/ctx[4];\n      shepherdcontent.$set(shepherdcontent_changes);\n      set_attributes(div, div_data = get_spread_update(div_levels, [(!current || dirty & /*step, descriptionId*/20 && div_aria_describedby_value !== (div_aria_describedby_value = !isUndefined( /*step*/ctx[4].options.text) ? /*descriptionId*/ctx[2] : null)) && {\n        \"aria-describedby\": div_aria_describedby_value\n      }, (!current || dirty & /*step, labelId*/24 && div_aria_labelledby_value !== (div_aria_labelledby_value = /*step*/ctx[4].options.title ? /*labelId*/ctx[3] : null)) && {\n        \"aria-labelledby\": div_aria_labelledby_value\n      }, dirty & /*dataStepId*/2 && /*dataStepId*/ctx[1], {\n        role: \"dialog\"\n      }, {\n        tabindex: \"0\"\n      }]));\n      toggle_class(div, \"shepherd-has-cancel-icon\", /*hasCancelIcon*/ctx[5]);\n      toggle_class(div, \"shepherd-has-title\", /*hasTitle*/ctx[6]);\n      toggle_class(div, \"shepherd-element\", true);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdcontent.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdcontent.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) detach(div);\n      if (if_block) if_block.d();\n      destroy_component(shepherdcontent);\n      /*div_binding*/\n      ctx[13](null);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nconst KEY_TAB = 9;\nconst KEY_ESC = 27;\nconst LEFT_ARROW = 37;\nconst RIGHT_ARROW = 39;\nfunction getClassesArray(classes) {\n  return classes.split(' ').filter(className => !!className.length);\n}\nfunction instance$1($$self, $$props, $$invalidate) {\n  let {\n    classPrefix,\n    element,\n    descriptionId,\n    firstFocusableElement,\n    focusableElements,\n    labelId,\n    lastFocusableElement,\n    step,\n    dataStepId\n  } = $$props;\n  let hasCancelIcon, hasTitle, classes;\n  const getElement = () => element;\n  onMount(() => {\n    // Get all elements that are focusable\n    $$invalidate(1, dataStepId = {\n      [`data-${classPrefix}shepherd-step-id`]: step.id\n    });\n    $$invalidate(9, focusableElements = element.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex=\"0\"]'));\n    $$invalidate(8, firstFocusableElement = focusableElements[0]);\n    $$invalidate(10, lastFocusableElement = focusableElements[focusableElements.length - 1]);\n  });\n  afterUpdate(() => {\n    if (classes !== step.options.classes) {\n      updateDynamicClasses();\n    }\n  });\n  function updateDynamicClasses() {\n    removeClasses(classes);\n    classes = step.options.classes;\n    addClasses(classes);\n  }\n  function removeClasses(classes) {\n    if (isString(classes)) {\n      const oldClasses = getClassesArray(classes);\n      if (oldClasses.length) {\n        element.classList.remove(...oldClasses);\n      }\n    }\n  }\n  function addClasses(classes) {\n    if (isString(classes)) {\n      const newClasses = getClassesArray(classes);\n      if (newClasses.length) {\n        element.classList.add(...newClasses);\n      }\n    }\n  }\n\n  /**\n  * Setup keydown events to allow closing the modal with ESC\n  *\n  * Borrowed from this great post! https://bitsofco.de/accessible-modal-dialog/\n  *\n  * @private\n  */\n  const handleKeyDown = e => {\n    const {\n      tour\n    } = step;\n    switch (e.keyCode) {\n      case KEY_TAB:\n        if (focusableElements.length === 0) {\n          e.preventDefault();\n          break;\n        }\n        // Backward tab\n        if (e.shiftKey) {\n          if (document.activeElement === firstFocusableElement || document.activeElement.classList.contains('shepherd-element')) {\n            e.preventDefault();\n            lastFocusableElement.focus();\n          }\n        } else {\n          if (document.activeElement === lastFocusableElement) {\n            e.preventDefault();\n            firstFocusableElement.focus();\n          }\n        }\n        break;\n      case KEY_ESC:\n        if (tour.options.exitOnEsc) {\n          e.stopPropagation();\n          step.cancel();\n        }\n        break;\n      case LEFT_ARROW:\n        if (tour.options.keyboardNavigation) {\n          e.stopPropagation();\n          tour.back();\n        }\n        break;\n      case RIGHT_ARROW:\n        if (tour.options.keyboardNavigation) {\n          e.stopPropagation();\n          tour.next();\n        }\n        break;\n    }\n  };\n  function div_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('classPrefix' in $$props) $$invalidate(11, classPrefix = $$props.classPrefix);\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('descriptionId' in $$props) $$invalidate(2, descriptionId = $$props.descriptionId);\n    if ('firstFocusableElement' in $$props) $$invalidate(8, firstFocusableElement = $$props.firstFocusableElement);\n    if ('focusableElements' in $$props) $$invalidate(9, focusableElements = $$props.focusableElements);\n    if ('labelId' in $$props) $$invalidate(3, labelId = $$props.labelId);\n    if ('lastFocusableElement' in $$props) $$invalidate(10, lastFocusableElement = $$props.lastFocusableElement);\n    if ('step' in $$props) $$invalidate(4, step = $$props.step);\n    if ('dataStepId' in $$props) $$invalidate(1, dataStepId = $$props.dataStepId);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*step*/16) {\n      {\n        $$invalidate(5, hasCancelIcon = step.options && step.options.cancelIcon && step.options.cancelIcon.enabled);\n        $$invalidate(6, hasTitle = step.options && step.options.title);\n      }\n    }\n  };\n  return [element, dataStepId, descriptionId, labelId, step, hasCancelIcon, hasTitle, handleKeyDown, firstFocusableElement, focusableElements, lastFocusableElement, classPrefix, getElement, div_binding];\n}\nclass Shepherd_element extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$1, create_fragment$1, safe_not_equal, {\n      classPrefix: 11,\n      element: 0,\n      descriptionId: 2,\n      firstFocusableElement: 8,\n      focusableElements: 9,\n      labelId: 3,\n      lastFocusableElement: 10,\n      step: 4,\n      dataStepId: 1,\n      getElement: 12\n    });\n  }\n  get getElement() {\n    return this.$$.ctx[12];\n  }\n}\n\n/**\n * A class representing steps to be added to a tour.\n * @extends {Evented}\n */\nclass Step extends Evented {\n  /**\n   * Create a step\n   * @param {Tour} tour The tour for the step\n   * @param {object} options The options for the step\n   * @param {boolean} options.arrow Whether to display the arrow for the tooltip or not. Defaults to `true`.\n   * @param {object} options.attachTo The element the step should be attached to on the page.\n   * An object with properties `element` and `on`.\n   *\n   * ```js\n   * const step = new Step(tour, {\n   *   attachTo: { element: '.some .selector-path', on: 'left' },\n   *   ...moreOptions\n   * });\n   * ```\n   *\n   * If you don’t specify an `attachTo` the element will appear in the middle of the screen. The same will happen if your `attachTo.element` callback returns `null`, `undefined`, or a selector that does not exist in the DOM.\n   * If you omit the `on` portion of `attachTo`, the element will still be highlighted, but the tooltip will appear\n   * in the middle of the screen, without an arrow pointing to the target.\n   * If the element to highlight does not yet exist while instantiating tour steps, you may use lazy evaluation by supplying a function to `attachTo.element`. The function will be called in the `before-show` phase.\n   * @param {string|HTMLElement|function} options.attachTo.element An element selector string, DOM element, or a function (returning a selector, a DOM element, `null` or `undefined`).\n   * @param {string} options.attachTo.on The optional direction to place the FloatingUI tooltip relative to the element.\n   *   - Possible string values: 'top', 'top-start', 'top-end', 'bottom', 'bottom-start', 'bottom-end', 'right', 'right-start', 'right-end', 'left', 'left-start', 'left-end'\n   * @param {Object} options.advanceOn An action on the page which should advance shepherd to the next step.\n   * It should be an object with a string `selector` and an `event` name\n   * ```js\n   * const step = new Step(tour, {\n   *   advanceOn: { selector: '.some .selector-path', event: 'click' },\n   *   ...moreOptions\n   * });\n   * ```\n   * `event` doesn’t have to be an event inside the tour, it can be any event fired on any element on the page.\n   * You can also always manually advance the Tour by calling `myTour.next()`.\n   * @param {function} options.beforeShowPromise A function that returns a promise.\n   * When the promise resolves, the rest of the `show` code for the step will execute.\n   * @param {Object[]} options.buttons An array of buttons to add to the step. These will be rendered in a\n   * footer below the main body text.\n   * @param {function} options.buttons.button.action A function executed when the button is clicked on.\n   * It is automatically bound to the `tour` the step is associated with, so things like `this.next` will\n   * work inside the action.\n   * You can use action to skip steps or navigate to specific steps, with something like:\n   * ```js\n   * action() {\n   *   return this.show('some_step_name');\n   * }\n   * ```\n   * @param {string} options.buttons.button.classes Extra classes to apply to the `<a>`\n   * @param {boolean} options.buttons.button.disabled Should the button be disabled?\n   * @param {string} options.buttons.button.label The aria-label text of the button\n   * @param {boolean} options.buttons.button.secondary If true, a shepherd-button-secondary class is applied to the button\n   * @param {string} options.buttons.button.text The HTML text of the button\n   * @param {boolean} options.canClickTarget A boolean, that when set to false, will set `pointer-events: none` on the target\n   * @param {object} options.cancelIcon Options for the cancel icon\n   * @param {boolean} options.cancelIcon.enabled Should a cancel “✕” be shown in the header of the step?\n   * @param {string} options.cancelIcon.label The label to add for `aria-label`\n   * @param {string} options.classes A string of extra classes to add to the step's content element.\n   * @param {string} options.highlightClass An extra class to apply to the `attachTo` element when it is\n   * highlighted (that is, when its step is active). You can then target that selector in your CSS.\n   * @param {string} options.id The string to use as the `id` for the step.\n   * @param {number} options.modalOverlayOpeningPadding An amount of padding to add around the modal overlay opening\n   * @param {number | { topLeft: number, bottomLeft: number, bottomRight: number, topRight: number }} options.modalOverlayOpeningRadius An amount of border radius to add around the modal overlay opening\n   * @param {object} options.floatingUIOptions Extra options to pass to FloatingUI\n   * @param {boolean|Object} options.scrollTo Should the element be scrolled to when this step is shown? If true, uses the default `scrollIntoView`,\n   * if an object, passes that object as the params to `scrollIntoView` i.e. `{behavior: 'smooth', block: 'center'}`\n   * @param {function} options.scrollToHandler A function that lets you override the default scrollTo behavior and\n   * define a custom action to do the scrolling, and possibly other logic.\n   * @param {function} options.showOn A function that, when it returns `true`, will show the step.\n   * If it returns false, the step will be skipped.\n   * @param {string} options.text The text in the body of the step. It can be one of three types:\n   * ```\n   * - HTML string\n   * - `HTMLElement` object\n   * - `Function` to be executed when the step is built. It must return one the two options above.\n   * ```\n   * @param {string} options.title The step's title. It becomes an `h3` at the top of the step. It can be one of two types:\n   * ```\n   * - HTML string\n   * - `Function` to be executed when the step is built. It must return HTML string.\n   * ```\n   * @param {object} options.when You can define `show`, `hide`, etc events inside `when`. For example:\n   * ```js\n   * when: {\n   *   show: function() {\n   *     window.scrollTo(0, 0);\n   *   }\n   * }\n   * ```\n   * @return {Step} The newly created Step instance\n   */\n  constructor(tour, options = {}) {\n    super(tour, options);\n    this.tour = tour;\n    this.classPrefix = this.tour.options ? normalizePrefix(this.tour.options.classPrefix) : '';\n    this.styles = tour.styles;\n\n    /**\n     * Resolved attachTo options. Due to lazy evaluation, we only resolve the options during `before-show` phase.\n     * Do not use this directly, use the _getResolvedAttachToOptions method instead.\n     * @type {null|{}|{element, to}}\n     * @private\n     */\n    this._resolvedAttachTo = null;\n    autoBind(this);\n    this._setOptions(options);\n    return this;\n  }\n\n  /**\n   * Cancel the tour\n   * Triggers the `cancel` event\n   */\n  cancel() {\n    this.tour.cancel();\n    this.trigger('cancel');\n  }\n\n  /**\n   * Complete the tour\n   * Triggers the `complete` event\n   */\n  complete() {\n    this.tour.complete();\n    this.trigger('complete');\n  }\n\n  /**\n   * Remove the step, delete the step's element, and destroy the FloatingUI instance for the step.\n   * Triggers `destroy` event\n   */\n  destroy() {\n    destroyTooltip(this);\n    if (isHTMLElement$1(this.el)) {\n      this.el.remove();\n      this.el = null;\n    }\n    this._updateStepTargetOnHide();\n    this.trigger('destroy');\n  }\n\n  /**\n   * Returns the tour for the step\n   * @return {Tour} The tour instance\n   */\n  getTour() {\n    return this.tour;\n  }\n\n  /**\n   * Hide the step\n   */\n  hide() {\n    this.tour.modal.hide();\n    this.trigger('before-hide');\n    if (this.el) {\n      this.el.hidden = true;\n    }\n    this._updateStepTargetOnHide();\n    this.trigger('hide');\n  }\n\n  /**\n   * Resolves attachTo options.\n   * @returns {{}|{element, on}}\n   * @private\n   */\n  _resolveAttachToOptions() {\n    this._resolvedAttachTo = parseAttachTo(this);\n    return this._resolvedAttachTo;\n  }\n\n  /**\n   * A selector for resolved attachTo options.\n   * @returns {{}|{element, on}}\n   * @private\n   */\n  _getResolvedAttachToOptions() {\n    if (this._resolvedAttachTo === null) {\n      return this._resolveAttachToOptions();\n    }\n    return this._resolvedAttachTo;\n  }\n\n  /**\n   * Check if the step is open and visible\n   * @return {boolean} True if the step is open and visible\n   */\n  isOpen() {\n    return Boolean(this.el && !this.el.hidden);\n  }\n\n  /**\n   * Wraps `_show` and ensures `beforeShowPromise` resolves before calling show\n   * @return {*|Promise}\n   */\n  show() {\n    if (isFunction(this.options.beforeShowPromise)) {\n      return Promise.resolve(this.options.beforeShowPromise()).then(() => this._show());\n    }\n    return Promise.resolve(this._show());\n  }\n\n  /**\n   * Updates the options of the step.\n   *\n   * @param {Object} options The options for the step\n   */\n  updateStepOptions(options) {\n    Object.assign(this.options, options);\n    if (this.shepherdElementComponent) {\n      this.shepherdElementComponent.$set({\n        step: this\n      });\n    }\n  }\n\n  /**\n   * Returns the element for the step\n   * @return {HTMLElement|null|undefined} The element instance. undefined if it has never been shown, null if it has been destroyed\n   */\n  getElement() {\n    return this.el;\n  }\n\n  /**\n   * Returns the target for the step\n   * @return {HTMLElement|null|undefined} The element instance. undefined if it has never been shown, null if query string has not been found\n   */\n  getTarget() {\n    return this.target;\n  }\n\n  /**\n   * Creates Shepherd element for step based on options\n   *\n   * @return {Element} The DOM element for the step tooltip\n   * @private\n   */\n  _createTooltipContent() {\n    const descriptionId = `${this.id}-description`;\n    const labelId = `${this.id}-label`;\n    this.shepherdElementComponent = new Shepherd_element({\n      target: this.tour.options.stepsContainer || document.body,\n      props: {\n        classPrefix: this.classPrefix,\n        descriptionId,\n        labelId,\n        step: this,\n        styles: this.styles\n      }\n    });\n    return this.shepherdElementComponent.getElement();\n  }\n\n  /**\n   * If a custom scrollToHandler is defined, call that, otherwise do the generic\n   * scrollIntoView call.\n   *\n   * @param {boolean|Object} scrollToOptions If true, uses the default `scrollIntoView`,\n   * if an object, passes that object as the params to `scrollIntoView` i.e. `{ behavior: 'smooth', block: 'center' }`\n   * @private\n   */\n  _scrollTo(scrollToOptions) {\n    const {\n      element\n    } = this._getResolvedAttachToOptions();\n    if (isFunction(this.options.scrollToHandler)) {\n      this.options.scrollToHandler(element);\n    } else if (isElement$1(element) && typeof element.scrollIntoView === 'function') {\n      element.scrollIntoView(scrollToOptions);\n    }\n  }\n\n  /**\n   * _getClassOptions gets all possible classes for the step\n   * @param {Object} stepOptions The step specific options\n   * @returns {String} unique string from array of classes\n   * @private\n   */\n  _getClassOptions(stepOptions) {\n    const defaultStepOptions = this.tour && this.tour.options && this.tour.options.defaultStepOptions;\n    const stepClasses = stepOptions.classes ? stepOptions.classes : '';\n    const defaultStepOptionsClasses = defaultStepOptions && defaultStepOptions.classes ? defaultStepOptions.classes : '';\n    const allClasses = [...stepClasses.split(' '), ...defaultStepOptionsClasses.split(' ')];\n    const uniqClasses = new Set(allClasses);\n    return Array.from(uniqClasses).join(' ').trim();\n  }\n\n  /**\n   * Sets the options for the step, maps `when` to events, sets up buttons\n   * @param {Object} options The options for the step\n   * @private\n   */\n  _setOptions(options = {}) {\n    let tourOptions = this.tour && this.tour.options && this.tour.options.defaultStepOptions;\n    tourOptions = cjs({}, tourOptions || {});\n    this.options = Object.assign({\n      arrow: true\n    }, tourOptions, options, mergeTooltipConfig(tourOptions, options));\n    const {\n      when\n    } = this.options;\n    this.options.classes = this._getClassOptions(options);\n    this.destroy();\n    this.id = this.options.id || `step-${uuid()}`;\n    if (when) {\n      Object.keys(when).forEach(event => {\n        this.on(event, when[event], this);\n      });\n    }\n  }\n\n  /**\n   * Create the element and set up the FloatingUI instance\n   * @private\n   */\n  _setupElements() {\n    if (!isUndefined(this.el)) {\n      this.destroy();\n    }\n    this.el = this._createTooltipContent();\n    if (this.options.advanceOn) {\n      bindAdvance(this);\n    }\n\n    // The tooltip implementation details are handled outside of the Step\n    // object.\n    setupTooltip(this);\n  }\n\n  /**\n   * Triggers `before-show`, generates the tooltip DOM content,\n   * sets up a FloatingUI instance for the tooltip, then triggers `show`.\n   * @private\n   */\n  _show() {\n    this.trigger('before-show');\n\n    // Force resolve to make sure the options are updated on subsequent shows.\n    this._resolveAttachToOptions();\n    this._setupElements();\n    if (!this.tour.modal) {\n      this.tour._setupModal();\n    }\n    this.tour.modal.setupForStep(this);\n    this._styleTargetElementForStep(this);\n    this.el.hidden = false;\n\n    // start scrolling to target before showing the step\n    if (this.options.scrollTo) {\n      setTimeout(() => {\n        this._scrollTo(this.options.scrollTo);\n      });\n    }\n    this.el.hidden = false;\n    const content = this.shepherdElementComponent.getElement();\n    const target = this.target || document.body;\n    target.classList.add(`${this.classPrefix}shepherd-enabled`);\n    target.classList.add(`${this.classPrefix}shepherd-target`);\n    content.classList.add('shepherd-enabled');\n    this.trigger('show');\n  }\n\n  /**\n   * Modulates the styles of the passed step's target element, based on the step's options and\n   * the tour's `modal` option, to visually emphasize the element\n   *\n   * @param step The step object that attaches to the element\n   * @private\n   */\n  _styleTargetElementForStep(step) {\n    const targetElement = step.target;\n    if (!targetElement) {\n      return;\n    }\n    if (step.options.highlightClass) {\n      targetElement.classList.add(step.options.highlightClass);\n    }\n    targetElement.classList.remove('shepherd-target-click-disabled');\n    if (step.options.canClickTarget === false) {\n      targetElement.classList.add('shepherd-target-click-disabled');\n    }\n  }\n\n  /**\n   * When a step is hidden, remove the highlightClass and 'shepherd-enabled'\n   * and 'shepherd-target' classes\n   * @private\n   */\n  _updateStepTargetOnHide() {\n    const target = this.target || document.body;\n    if (this.options.highlightClass) {\n      target.classList.remove(this.options.highlightClass);\n    }\n    target.classList.remove('shepherd-target-click-disabled', `${this.classPrefix}shepherd-enabled`, `${this.classPrefix}shepherd-target`);\n  }\n}\n\n/**\n * Cleanup the steps and set pointerEvents back to 'auto'\n * @param tour The tour object\n */\nfunction cleanupSteps(tour) {\n  if (tour) {\n    const {\n      steps\n    } = tour;\n    steps.forEach(step => {\n      if (step.options && step.options.canClickTarget === false && step.options.attachTo) {\n        if (step.target instanceof HTMLElement) {\n          step.target.classList.remove('shepherd-target-click-disabled');\n        }\n      }\n    });\n  }\n}\n\n/**\n * Generates the svg path data for a rounded rectangle overlay\n * @param {Object} dimension - Dimensions of rectangle.\n * @param {number} width - Width.\n * @param {number} height - Height.\n * @param {number} [x=0] - Offset from top left corner in x axis. default 0.\n * @param {number} [y=0] - Offset from top left corner in y axis. default 0.\n * @param {number | { topLeft: number, topRight: number, bottomRight: number, bottomLeft: number }} [r=0] - Corner Radius. Keep this smaller than half of width or height.\n * @returns {string} - Rounded rectangle overlay path data.\n */\nfunction makeOverlayPath({\n  width,\n  height,\n  x = 0,\n  y = 0,\n  r = 0\n}) {\n  const {\n    innerWidth: w,\n    innerHeight: h\n  } = window;\n  const {\n    topLeft = 0,\n    topRight = 0,\n    bottomRight = 0,\n    bottomLeft = 0\n  } = typeof r === 'number' ? {\n    topLeft: r,\n    topRight: r,\n    bottomRight: r,\n    bottomLeft: r\n  } : r;\n  return `M${w},${h}\\\nH0\\\nV0\\\nH${w}\\\nV${h}\\\nZ\\\nM${x + topLeft},${y}\\\na${topLeft},${topLeft},0,0,0-${topLeft},${topLeft}\\\nV${height + y - bottomLeft}\\\na${bottomLeft},${bottomLeft},0,0,0,${bottomLeft},${bottomLeft}\\\nH${width + x - bottomRight}\\\na${bottomRight},${bottomRight},0,0,0,${bottomRight}-${bottomRight}\\\nV${y + topRight}\\\na${topRight},${topRight},0,0,0-${topRight}-${topRight}\\\nZ`;\n}\n\n/* src/js/components/shepherd-modal.svelte generated by Svelte v3.59.2 */\nfunction create_fragment(ctx) {\n  let svg;\n  let path;\n  let svg_class_value;\n  let mounted;\n  let dispose;\n  return {\n    c() {\n      svg = svg_element(\"svg\");\n      path = svg_element(\"path\");\n      attr(path, \"d\", /*pathDefinition*/ctx[2]);\n      attr(svg, \"class\", svg_class_value = `${/*modalIsVisible*/ctx[1] ? 'shepherd-modal-is-visible' : ''} shepherd-modal-overlay-container`);\n    },\n    m(target, anchor) {\n      insert(target, svg, anchor);\n      append(svg, path);\n      /*svg_binding*/\n      ctx[11](svg);\n      if (!mounted) {\n        dispose = listen(svg, \"touchmove\", /*_preventModalOverlayTouch*/ctx[3]);\n        mounted = true;\n      }\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*pathDefinition*/4) {\n        attr(path, \"d\", /*pathDefinition*/ctx[2]);\n      }\n      if (dirty & /*modalIsVisible*/2 && svg_class_value !== (svg_class_value = `${/*modalIsVisible*/ctx[1] ? 'shepherd-modal-is-visible' : ''} shepherd-modal-overlay-container`)) {\n        attr(svg, \"class\", svg_class_value);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(svg);\n      /*svg_binding*/\n      ctx[11](null);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nfunction _getScrollParent(element) {\n  if (!element) {\n    return null;\n  }\n  const isHtmlElement = element instanceof HTMLElement;\n  const overflowY = isHtmlElement && window.getComputedStyle(element).overflowY;\n  const isScrollable = overflowY !== 'hidden' && overflowY !== 'visible';\n  if (isScrollable && element.scrollHeight >= element.clientHeight) {\n    return element;\n  }\n  return _getScrollParent(element.parentElement);\n}\n\n/**\n * Get the visible height of the target element relative to its scrollParent.\n * If there is no scroll parent, the height of the element is returned.\n *\n * @param {HTMLElement} element The target element\n * @param {HTMLElement} [scrollParent] The scrollable parent element\n * @returns {{y: number, height: number}}\n * @private\n */\nfunction _getVisibleHeight(element, scrollParent) {\n  const elementRect = element.getBoundingClientRect();\n  let top = elementRect.y || elementRect.top;\n  let bottom = elementRect.bottom || top + elementRect.height;\n  if (scrollParent) {\n    const scrollRect = scrollParent.getBoundingClientRect();\n    const scrollTop = scrollRect.y || scrollRect.top;\n    const scrollBottom = scrollRect.bottom || scrollTop + scrollRect.height;\n    top = Math.max(top, scrollTop);\n    bottom = Math.min(bottom, scrollBottom);\n  }\n  const height = Math.max(bottom - top, 0); // Default to 0 if height is negative\n  return {\n    y: top,\n    height\n  };\n}\nfunction instance($$self, $$props, $$invalidate) {\n  let {\n    element,\n    openingProperties\n  } = $$props;\n  uuid();\n  let modalIsVisible = false;\n  let rafId = undefined;\n  let pathDefinition;\n  closeModalOpening();\n  const getElement = () => element;\n  function closeModalOpening() {\n    $$invalidate(4, openingProperties = {\n      width: 0,\n      height: 0,\n      x: 0,\n      y: 0,\n      r: 0\n    });\n  }\n  function hide() {\n    $$invalidate(1, modalIsVisible = false);\n\n    // Ensure we cleanup all event listeners when we hide the modal\n    _cleanupStepEventListeners();\n  }\n  function positionModal(modalOverlayOpeningPadding = 0, modalOverlayOpeningRadius = 0, scrollParent, targetElement) {\n    if (targetElement) {\n      const {\n        y,\n        height\n      } = _getVisibleHeight(targetElement, scrollParent);\n      const {\n        x,\n        width,\n        left\n      } = targetElement.getBoundingClientRect();\n\n      // getBoundingClientRect is not consistent. Some browsers use x and y, while others use left and top\n      $$invalidate(4, openingProperties = {\n        width: width + modalOverlayOpeningPadding * 2,\n        height: height + modalOverlayOpeningPadding * 2,\n        x: (x || left) - modalOverlayOpeningPadding,\n        y: y - modalOverlayOpeningPadding,\n        r: modalOverlayOpeningRadius\n      });\n    } else {\n      closeModalOpening();\n    }\n  }\n  function setupForStep(step) {\n    // Ensure we move listeners from the previous step, before we setup new ones\n    _cleanupStepEventListeners();\n    if (step.tour.options.useModalOverlay) {\n      _styleForStep(step);\n      show();\n    } else {\n      hide();\n    }\n  }\n  function show() {\n    $$invalidate(1, modalIsVisible = true);\n  }\n  const _preventModalBodyTouch = e => {\n    e.preventDefault();\n  };\n  const _preventModalOverlayTouch = e => {\n    e.stopPropagation();\n  };\n\n  /**\n  * Add touchmove event listener\n  * @private\n  */\n  function _addStepEventListeners() {\n    // Prevents window from moving on touch.\n    window.addEventListener('touchmove', _preventModalBodyTouch, {\n      passive: false\n    });\n  }\n\n  /**\n  * Cancel the requestAnimationFrame loop and remove touchmove event listeners\n  * @private\n  */\n  function _cleanupStepEventListeners() {\n    if (rafId) {\n      cancelAnimationFrame(rafId);\n      rafId = undefined;\n    }\n    window.removeEventListener('touchmove', _preventModalBodyTouch, {\n      passive: false\n    });\n  }\n\n  /**\n  * Style the modal for the step\n  * @param {Step} step The step to style the opening for\n  * @private\n  */\n  function _styleForStep(step) {\n    const {\n      modalOverlayOpeningPadding,\n      modalOverlayOpeningRadius\n    } = step.options;\n    const scrollParent = _getScrollParent(step.target);\n\n    // Setup recursive function to call requestAnimationFrame to update the modal opening position\n    const rafLoop = () => {\n      rafId = undefined;\n      positionModal(modalOverlayOpeningPadding, modalOverlayOpeningRadius, scrollParent, step.target);\n      rafId = requestAnimationFrame(rafLoop);\n    };\n    rafLoop();\n    _addStepEventListeners();\n  }\n  function svg_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('openingProperties' in $$props) $$invalidate(4, openingProperties = $$props.openingProperties);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*openingProperties*/16) {\n      $$invalidate(2, pathDefinition = makeOverlayPath(openingProperties));\n    }\n  };\n  return [element, modalIsVisible, pathDefinition, _preventModalOverlayTouch, openingProperties, getElement, closeModalOpening, hide, positionModal, setupForStep, show, svg_binding];\n}\nclass Shepherd_modal extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance, create_fragment, safe_not_equal, {\n      element: 0,\n      openingProperties: 4,\n      getElement: 5,\n      closeModalOpening: 6,\n      hide: 7,\n      positionModal: 8,\n      setupForStep: 9,\n      show: 10\n    });\n  }\n  get getElement() {\n    return this.$$.ctx[5];\n  }\n  get closeModalOpening() {\n    return this.$$.ctx[6];\n  }\n  get hide() {\n    return this.$$.ctx[7];\n  }\n  get positionModal() {\n    return this.$$.ctx[8];\n  }\n  get setupForStep() {\n    return this.$$.ctx[9];\n  }\n  get show() {\n    return this.$$.ctx[10];\n  }\n}\n\nconst Shepherd = new Evented();\n\n/**\n * Class representing the site tour\n * @extends {Evented}\n */\nclass Tour extends Evented {\n  /**\n   * @param {Object} options The options for the tour\n   * @param {boolean | function(): boolean | Promise<boolean> | function(): Promise<boolean>} options.confirmCancel If true, will issue a `window.confirm` before cancelling.\n   * If it is a function(support Async Function), it will be called and wait for the return value, and will only be cancelled if the value returned is true\n   * @param {string} options.confirmCancelMessage The message to display in the `window.confirm` dialog\n   * @param {string} options.classPrefix The prefix to add to the `shepherd-enabled` and `shepherd-target` class names as well as the `data-shepherd-step-id`.\n   * @param {Object} options.defaultStepOptions Default options for Steps ({@link Step#constructor}), created through `addStep`\n   * @param {boolean} options.exitOnEsc Exiting the tour with the escape key will be enabled unless this is explicitly\n   * set to false.\n   * @param {boolean} options.keyboardNavigation Navigating the tour via left and right arrow keys will be enabled\n   * unless this is explicitly set to false.\n   * @param {HTMLElement} options.stepsContainer An optional container element for the steps.\n   * If not set, the steps will be appended to `document.body`.\n   * @param {HTMLElement} options.modalContainer An optional container element for the modal.\n   * If not set, the modal will be appended to `document.body`.\n   * @param {object[] | Step[]} options.steps An array of step options objects or Step instances to initialize the tour with\n   * @param {string} options.tourName An optional \"name\" for the tour. This will be appended to the the tour's\n   * dynamically generated `id` property.\n   * @param {boolean} options.useModalOverlay Whether or not steps should be placed above a darkened\n   * modal overlay. If true, the overlay will create an opening around the target element so that it\n   * can remain interactive\n   * @returns {Tour}\n   */\n  constructor(options = {}) {\n    super(options);\n    autoBind(this);\n    const defaultTourOptions = {\n      exitOnEsc: true,\n      keyboardNavigation: true\n    };\n    this.options = Object.assign({}, defaultTourOptions, options);\n    this.classPrefix = normalizePrefix(this.options.classPrefix);\n    this.steps = [];\n    this.addSteps(this.options.steps);\n\n    // Pass these events onto the global Shepherd object\n    const events = ['active', 'cancel', 'complete', 'inactive', 'show', 'start'];\n    events.map(event => {\n      (e => {\n        this.on(e, opts => {\n          opts = opts || {};\n          opts.tour = this;\n          Shepherd.trigger(e, opts);\n        });\n      })(event);\n    });\n    this._setTourID();\n    return this;\n  }\n\n  /**\n   * Adds a new step to the tour\n   * @param {Object|Step} options An object containing step options or a Step instance\n   * @param {number} index The optional index to insert the step at. If undefined, the step\n   * is added to the end of the array.\n   * @return {Step} The newly added step\n   */\n  addStep(options, index) {\n    let step = options;\n    if (!(step instanceof Step)) {\n      step = new Step(this, step);\n    } else {\n      step.tour = this;\n    }\n    if (!isUndefined(index)) {\n      this.steps.splice(index, 0, step);\n    } else {\n      this.steps.push(step);\n    }\n    return step;\n  }\n\n  /**\n   * Add multiple steps to the tour\n   * @param {Array<object> | Array<Step>} steps The steps to add to the tour\n   */\n  addSteps(steps) {\n    if (Array.isArray(steps)) {\n      steps.forEach(step => {\n        this.addStep(step);\n      });\n    }\n    return this;\n  }\n\n  /**\n   * Go to the previous step in the tour\n   */\n  back() {\n    const index = this.steps.indexOf(this.currentStep);\n    this.show(index - 1, false);\n  }\n\n  /**\n   * Calls _done() triggering the 'cancel' event\n   * If `confirmCancel` is true, will show a window.confirm before cancelling\n   * If `confirmCancel` is a function, will call it and wait for the return value,\n   * and only cancel when the value returned is true\n   */\n  async cancel() {\n    if (this.options.confirmCancel) {\n      const confirmCancelIsFunction = typeof this.options.confirmCancel === 'function';\n      const cancelMessage = this.options.confirmCancelMessage || 'Are you sure you want to stop the tour?';\n      const stopTour = confirmCancelIsFunction ? await this.options.confirmCancel() : window.confirm(cancelMessage);\n      if (stopTour) {\n        this._done('cancel');\n      }\n    } else {\n      this._done('cancel');\n    }\n  }\n\n  /**\n   * Calls _done() triggering the `complete` event\n   */\n  complete() {\n    this._done('complete');\n  }\n\n  /**\n   * Gets the step from a given id\n   * @param {Number|String} id The id of the step to retrieve\n   * @return {Step} The step corresponding to the `id`\n   */\n  getById(id) {\n    return this.steps.find(step => {\n      return step.id === id;\n    });\n  }\n\n  /**\n   * Gets the current step\n   * @returns {Step|null}\n   */\n  getCurrentStep() {\n    return this.currentStep;\n  }\n\n  /**\n   * Hide the current step\n   */\n  hide() {\n    const currentStep = this.getCurrentStep();\n    if (currentStep) {\n      return currentStep.hide();\n    }\n  }\n\n  /**\n   * Check if the tour is active\n   * @return {boolean}\n   */\n  isActive() {\n    return Shepherd.activeTour === this;\n  }\n\n  /**\n   * Go to the next step in the tour\n   * If we are at the end, call `complete`\n   */\n  next() {\n    const index = this.steps.indexOf(this.currentStep);\n    if (index === this.steps.length - 1) {\n      this.complete();\n    } else {\n      this.show(index + 1, true);\n    }\n  }\n\n  /**\n   * Removes the step from the tour\n   * @param {String} name The id for the step to remove\n   */\n  removeStep(name) {\n    const current = this.getCurrentStep();\n\n    // Find the step, destroy it and remove it from this.steps\n    this.steps.some((step, i) => {\n      if (step.id === name) {\n        if (step.isOpen()) {\n          step.hide();\n        }\n        step.destroy();\n        this.steps.splice(i, 1);\n        return true;\n      }\n    });\n    if (current && current.id === name) {\n      this.currentStep = undefined;\n\n      // If we have steps left, show the first one, otherwise just cancel the tour\n      this.steps.length ? this.show(0) : this.cancel();\n    }\n  }\n\n  /**\n   * Show a specific step in the tour\n   * @param {Number|String} key The key to look up the step by\n   * @param {Boolean} forward True if we are going forward, false if backward\n   */\n  show(key = 0, forward = true) {\n    const step = isString(key) ? this.getById(key) : this.steps[key];\n    if (step) {\n      this._updateStateBeforeShow();\n      const shouldSkipStep = isFunction(step.options.showOn) && !step.options.showOn();\n\n      // If `showOn` returns false, we want to skip the step, otherwise, show the step like normal\n      if (shouldSkipStep) {\n        this._skipStep(step, forward);\n      } else {\n        this.trigger('show', {\n          step,\n          previous: this.currentStep\n        });\n        this.currentStep = step;\n        step.show();\n      }\n    }\n  }\n\n  /**\n   * Start the tour\n   */\n  start() {\n    this.trigger('start');\n\n    // Save the focused element before the tour opens\n    this.focusedElBeforeOpen = document.activeElement;\n    this.currentStep = null;\n    this._setupModal();\n    this._setupActiveTour();\n    this.next();\n  }\n\n  /**\n   * Called whenever the tour is cancelled or completed, basically anytime we exit the tour\n   * @param {String} event The event name to trigger\n   * @private\n   */\n  _done(event) {\n    const index = this.steps.indexOf(this.currentStep);\n    if (Array.isArray(this.steps)) {\n      this.steps.forEach(step => step.destroy());\n    }\n    cleanupSteps(this);\n    this.trigger(event, {\n      index\n    });\n    Shepherd.activeTour = null;\n    this.trigger('inactive', {\n      tour: this\n    });\n    if (this.modal) {\n      this.modal.hide();\n    }\n    if (event === 'cancel' || event === 'complete') {\n      if (this.modal) {\n        const modalContainer = document.querySelector('.shepherd-modal-overlay-container');\n        if (modalContainer) {\n          modalContainer.remove();\n        }\n      }\n    }\n\n    // Focus the element that was focused before the tour started\n    if (isHTMLElement$1(this.focusedElBeforeOpen)) {\n      this.focusedElBeforeOpen.focus();\n    }\n  }\n\n  /**\n   * Make this tour \"active\"\n   * @private\n   */\n  _setupActiveTour() {\n    this.trigger('active', {\n      tour: this\n    });\n    Shepherd.activeTour = this;\n  }\n\n  /**\n   * _setupModal create the modal container and instance\n   * @private\n   */\n  _setupModal() {\n    this.modal = new Shepherd_modal({\n      target: this.options.modalContainer || document.body,\n      props: {\n        classPrefix: this.classPrefix,\n        styles: this.styles\n      }\n    });\n  }\n\n  /**\n   * Called when `showOn` evaluates to false, to skip the step or complete the tour if it's the last step\n   * @param {Step} step The step to skip\n   * @param {Boolean} forward True if we are going forward, false if backward\n   * @private\n   */\n  _skipStep(step, forward) {\n    const index = this.steps.indexOf(step);\n    if (index === this.steps.length - 1) {\n      this.complete();\n    } else {\n      const nextIndex = forward ? index + 1 : index - 1;\n      this.show(nextIndex, forward);\n    }\n  }\n\n  /**\n   * Before showing, hide the current step and if the tour is not\n   * already active, call `this._setupActiveTour`.\n   * @private\n   */\n  _updateStateBeforeShow() {\n    if (this.currentStep) {\n      this.currentStep.hide();\n    }\n    if (!this.isActive()) {\n      this._setupActiveTour();\n    }\n  }\n\n  /**\n   * Sets this.id to `${tourName}--${uuid}`\n   * @private\n   */\n  _setTourID() {\n    const tourName = this.options.tourName || 'tour';\n    this.id = `${tourName}--${uuid()}`;\n  }\n}\n\nconst isServerSide = typeof window === 'undefined';\nclass NoOp {\n  constructor() {}\n}\nif (isServerSide) {\n  Object.assign(Shepherd, {\n    Tour: NoOp,\n    Step: NoOp\n  });\n} else {\n  Object.assign(Shepherd, {\n    Tour,\n    Step\n  });\n}\n\nexport { Shepherd as default };\n"], "mappings": ";AAAA;;AAEA,IAAIA,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;EACxD,OAAOC,eAAe,CAACD,KAAK,CAAC,IAAI,CAACE,SAAS,CAACF,KAAK,CAAC;AACpD,CAAC;AACD,SAASC,eAAeA,CAACD,KAAK,EAAE;EAC9B,OAAO,CAAC,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ;AAC7C;AACA,SAASE,SAASA,CAACF,KAAK,EAAE;EACxB,IAAIG,WAAW,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC;EACvD,OAAOG,WAAW,KAAK,iBAAiB,IAAIA,WAAW,KAAK,eAAe,IAAIK,cAAc,CAACR,KAAK,CAAC;AACtG;;AAEA;AACA,IAAIS,YAAY,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG;AAC7D,IAAIC,kBAAkB,GAAGH,YAAY,GAAGC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM;AAC5E,SAASH,cAAcA,CAACR,KAAK,EAAE;EAC7B,OAAOA,KAAK,CAACa,QAAQ,KAAKD,kBAAkB;AAC9C;AACA,SAASE,WAAWA,CAACC,GAAG,EAAE;EACxB,OAAOC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACrC;AACA,SAASG,6BAA6BA,CAAClB,KAAK,EAAEmB,OAAO,EAAE;EACrD,OAAOA,OAAO,CAACC,KAAK,KAAK,KAAK,IAAID,OAAO,CAACpB,iBAAiB,CAACC,KAAK,CAAC,GAAGqB,SAAS,CAACP,WAAW,CAACd,KAAK,CAAC,EAAEA,KAAK,EAAEmB,OAAO,CAAC,GAAGnB,KAAK;AAC5H;AACA,SAASsB,iBAAiBA,CAACC,MAAM,EAAEC,MAAM,EAAEL,OAAO,EAAE;EAClD,OAAOI,MAAM,CAACE,MAAM,CAACD,MAAM,CAAC,CAACE,GAAG,CAAC,UAAUC,OAAO,EAAE;IAClD,OAAOT,6BAA6B,CAACS,OAAO,EAAER,OAAO,CAAC;EACxD,CAAC,CAAC;AACJ;AACA,SAASS,gBAAgBA,CAACC,GAAG,EAAEV,OAAO,EAAE;EACtC,IAAI,CAACA,OAAO,CAACW,WAAW,EAAE;IACxB,OAAOT,SAAS;EAClB;EACA,IAAIS,WAAW,GAAGX,OAAO,CAACW,WAAW,CAACD,GAAG,CAAC;EAC1C,OAAO,OAAOC,WAAW,KAAK,UAAU,GAAGA,WAAW,GAAGT,SAAS;AACpE;AACA,SAASU,+BAA+BA,CAACR,MAAM,EAAE;EAC/C,OAAOnB,MAAM,CAAC4B,qBAAqB,GAAG5B,MAAM,CAAC4B,qBAAqB,CAACT,MAAM,CAAC,CAACU,MAAM,CAAC,UAAUC,MAAM,EAAE;IAClG,OAAO9B,MAAM,CAAC+B,oBAAoB,CAAC5B,IAAI,CAACgB,MAAM,EAAEW,MAAM,CAAC;EACzD,CAAC,CAAC,GAAG,EAAE;AACT;AACA,SAASE,OAAOA,CAACb,MAAM,EAAE;EACvB,OAAOnB,MAAM,CAACiC,IAAI,CAACd,MAAM,CAAC,CAACE,MAAM,CAACM,+BAA+B,CAACR,MAAM,CAAC,CAAC;AAC5E;AACA,SAASe,kBAAkBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC5C,IAAI;IACF,OAAOA,QAAQ,IAAID,MAAM;EAC3B,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;;AAEA;AACA,SAASC,gBAAgBA,CAACnB,MAAM,EAAEM,GAAG,EAAE;EACrC,OAAOS,kBAAkB,CAACf,MAAM,EAAEM,GAAG,CAAC,CAAC;EAAA,GACpC,EAAEzB,MAAM,CAACuC,cAAc,CAACpC,IAAI,CAACgB,MAAM,EAAEM,GAAG,CAAC,CAAC;EAAA,GAC1CzB,MAAM,CAAC+B,oBAAoB,CAAC5B,IAAI,CAACgB,MAAM,EAAEM,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD;AAEA,SAASe,WAAWA,CAACrB,MAAM,EAAEC,MAAM,EAAEL,OAAO,EAAE;EAC5C,IAAI0B,WAAW,GAAG,CAAC,CAAC;EACpB,IAAI1B,OAAO,CAACpB,iBAAiB,CAACwB,MAAM,CAAC,EAAE;IACrCa,OAAO,CAACb,MAAM,CAAC,CAACuB,OAAO,CAAC,UAAUjB,GAAG,EAAE;MACrCgB,WAAW,CAAChB,GAAG,CAAC,GAAGX,6BAA6B,CAACK,MAAM,CAACM,GAAG,CAAC,EAAEV,OAAO,CAAC;IACxE,CAAC,CAAC;EACJ;EACAiB,OAAO,CAACZ,MAAM,CAAC,CAACsB,OAAO,CAAC,UAAUjB,GAAG,EAAE;IACrC,IAAIa,gBAAgB,CAACnB,MAAM,EAAEM,GAAG,CAAC,EAAE;MACjC;IACF;IACA,IAAIS,kBAAkB,CAACf,MAAM,EAAEM,GAAG,CAAC,IAAIV,OAAO,CAACpB,iBAAiB,CAACyB,MAAM,CAACK,GAAG,CAAC,CAAC,EAAE;MAC7EgB,WAAW,CAAChB,GAAG,CAAC,GAAGD,gBAAgB,CAACC,GAAG,EAAEV,OAAO,CAAC,CAACI,MAAM,CAACM,GAAG,CAAC,EAAEL,MAAM,CAACK,GAAG,CAAC,EAAEV,OAAO,CAAC;IACtF,CAAC,MAAM;MACL0B,WAAW,CAAChB,GAAG,CAAC,GAAGX,6BAA6B,CAACM,MAAM,CAACK,GAAG,CAAC,EAAEV,OAAO,CAAC;IACxE;EACF,CAAC,CAAC;EACF,OAAO0B,WAAW;AACpB;AACA,SAASxB,SAASA,CAACE,MAAM,EAAEC,MAAM,EAAEL,OAAO,EAAE;EAC1CA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvBA,OAAO,CAAC4B,UAAU,GAAG5B,OAAO,CAAC4B,UAAU,IAAIzB,iBAAiB;EAC5DH,OAAO,CAACpB,iBAAiB,GAAGoB,OAAO,CAACpB,iBAAiB,IAAIA,iBAAiB;EAC1E;EACA;EACAoB,OAAO,CAACD,6BAA6B,GAAGA,6BAA6B;EACrE,IAAI8B,aAAa,GAAGhC,KAAK,CAACC,OAAO,CAACO,MAAM,CAAC;EACzC,IAAIyB,aAAa,GAAGjC,KAAK,CAACC,OAAO,CAACM,MAAM,CAAC;EACzC,IAAI2B,yBAAyB,GAAGF,aAAa,KAAKC,aAAa;EAC/D,IAAI,CAACC,yBAAyB,EAAE;IAC9B,OAAOhC,6BAA6B,CAACM,MAAM,EAAEL,OAAO,CAAC;EACvD,CAAC,MAAM,IAAI6B,aAAa,EAAE;IACxB,OAAO7B,OAAO,CAAC4B,UAAU,CAACxB,MAAM,EAAEC,MAAM,EAAEL,OAAO,CAAC;EACpD,CAAC,MAAM;IACL,OAAOyB,WAAW,CAACrB,MAAM,EAAEC,MAAM,EAAEL,OAAO,CAAC;EAC7C;AACF;AACAE,SAAS,CAAC8B,GAAG,GAAG,SAASC,YAAYA,CAACC,KAAK,EAAElC,OAAO,EAAE;EACpD,IAAI,CAACH,KAAK,CAACC,OAAO,CAACoC,KAAK,CAAC,EAAE;IACzB,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;EACtD;EACA,OAAOD,KAAK,CAACE,MAAM,CAAC,UAAUC,IAAI,EAAEC,IAAI,EAAE;IACxC,OAAOpC,SAAS,CAACmC,IAAI,EAAEC,IAAI,EAAEtC,OAAO,CAAC;EACvC,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;AACD,IAAIuC,WAAW,GAAGrC,SAAS;AAC3B,IAAIsC,GAAG,GAAGD,WAAW;;AAErB;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAAC5D,KAAK,EAAE;EAC1B,OAAOA,KAAK,YAAY6D,OAAO;AACjC;;AAEA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAAC9D,KAAK,EAAE;EAC9B,OAAOA,KAAK,YAAY+D,WAAW;AACrC;;AAEA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAAChE,KAAK,EAAE;EACzB,OAAO,OAAOA,KAAK,KAAK,UAAU;AACpC;;AAEA;AACA;AACA;AACA;AACA,SAASiE,QAAQA,CAACjE,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,KAAK,QAAQ;AAClC;;AAEA;AACA;AACA;AACA;AACA,SAASkE,WAAWA,CAAClE,KAAK,EAAE;EAC1B,OAAOA,KAAK,KAAKmE,SAAS;AAC5B;AAEA,MAAMC,OAAO,CAAC;EACZC,EAAEA,CAACC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEC,IAAI,GAAG,KAAK,EAAE;IACpC,IAAIP,WAAW,CAAC,IAAI,CAACQ,QAAQ,CAAC,EAAE;MAC9B,IAAI,CAACA,QAAQ,GAAG,CAAC,CAAC;IACpB;IACA,IAAIR,WAAW,CAAC,IAAI,CAACQ,QAAQ,CAACJ,KAAK,CAAC,CAAC,EAAE;MACrC,IAAI,CAACI,QAAQ,CAACJ,KAAK,CAAC,GAAG,EAAE;IAC3B;IACA,IAAI,CAACI,QAAQ,CAACJ,KAAK,CAAC,CAACK,IAAI,CAAC;MACxBJ,OAAO;MACPC,GAAG;MACHC;IACF,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EACAA,IAAIA,CAACH,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACxB,OAAO,IAAI,CAACH,EAAE,CAACC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAE,IAAI,CAAC;EAC3C;EACAI,GAAGA,CAACN,KAAK,EAAEC,OAAO,EAAE;IAClB,IAAIL,WAAW,CAAC,IAAI,CAACQ,QAAQ,CAAC,IAAIR,WAAW,CAAC,IAAI,CAACQ,QAAQ,CAACJ,KAAK,CAAC,CAAC,EAAE;MACnE,OAAO,IAAI;IACb;IACA,IAAIJ,WAAW,CAACK,OAAO,CAAC,EAAE;MACxB,OAAO,IAAI,CAACG,QAAQ,CAACJ,KAAK,CAAC;IAC7B,CAAC,MAAM;MACL,IAAI,CAACI,QAAQ,CAACJ,KAAK,CAAC,CAACxB,OAAO,CAAC,CAAC+B,OAAO,EAAEC,KAAK,KAAK;QAC/C,IAAID,OAAO,CAACN,OAAO,KAAKA,OAAO,EAAE;UAC/B,IAAI,CAACG,QAAQ,CAACJ,KAAK,CAAC,CAACS,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;QACvC;MACF,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb;EACAE,OAAOA,CAACV,KAAK,EAAE,GAAGW,IAAI,EAAE;IACtB,IAAI,CAACf,WAAW,CAAC,IAAI,CAACQ,QAAQ,CAAC,IAAI,IAAI,CAACA,QAAQ,CAACJ,KAAK,CAAC,EAAE;MACvD,IAAI,CAACI,QAAQ,CAACJ,KAAK,CAAC,CAACxB,OAAO,CAAC,CAAC+B,OAAO,EAAEC,KAAK,KAAK;QAC/C,MAAM;UACJN,GAAG;UACHD,OAAO;UACPE;QACF,CAAC,GAAGI,OAAO;QACX,MAAMK,OAAO,GAAGV,GAAG,IAAI,IAAI;QAC3BD,OAAO,CAACY,KAAK,CAACD,OAAO,EAAED,IAAI,CAAC;QAC5B,IAAIR,IAAI,EAAE;UACR,IAAI,CAACC,QAAQ,CAACJ,KAAK,CAAC,CAACS,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;QACvC;MACF,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,QAAQA,CAACC,IAAI,EAAE;EACtB,MAAMhD,IAAI,GAAGjC,MAAM,CAACkF,mBAAmB,CAACD,IAAI,CAACE,WAAW,CAAClF,SAAS,CAAC;EACnE,KAAK,IAAImF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnD,IAAI,CAACoD,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,MAAM3D,GAAG,GAAGQ,IAAI,CAACmD,CAAC,CAAC;IACnB,MAAMzE,GAAG,GAAGsE,IAAI,CAACxD,GAAG,CAAC;IACrB,IAAIA,GAAG,KAAK,aAAa,IAAI,OAAOd,GAAG,KAAK,UAAU,EAAE;MACtDsE,IAAI,CAACxD,GAAG,CAAC,GAAGd,GAAG,CAAC2E,IAAI,CAACL,IAAI,CAAC;IAC5B;EACF;EACA,OAAOA,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,sBAAsBA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EAC9C,OAAOvB,KAAK,IAAI;IACd,IAAIuB,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;MACjB,MAAMC,UAAU,GAAGF,IAAI,CAACG,EAAE,IAAI1B,KAAK,CAAC2B,aAAa,KAAKJ,IAAI,CAACG,EAAE;MAC7D,MAAME,gBAAgB,GAAG,CAAChC,WAAW,CAAC0B,QAAQ,CAAC,IAAItB,KAAK,CAAC2B,aAAa,CAACE,OAAO,CAACP,QAAQ,CAAC;MACxF,IAAIM,gBAAgB,IAAIH,UAAU,EAAE;QAClCF,IAAI,CAACO,IAAI,CAAC3C,IAAI,CAAC,CAAC;MAClB;IACF;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAAS4C,WAAWA,CAACR,IAAI,EAAE;EACzB;EACA,MAAM;IACJvB,KAAK;IACLsB;EACF,CAAC,GAAGC,IAAI,CAAC1E,OAAO,CAACmF,SAAS,IAAI,CAAC,CAAC;EAChC,IAAIhC,KAAK,EAAE;IACT,MAAMC,OAAO,GAAGoB,sBAAsB,CAACC,QAAQ,EAAEC,IAAI,CAAC;;IAEtD;IACA,IAAIG,EAAE;IACN,IAAI;MACFA,EAAE,GAAGO,QAAQ,CAACC,aAAa,CAACZ,QAAQ,CAAC;IACvC,CAAC,CAAC,OAAOa,CAAC,EAAE;MACV;IAAA;IAEF,IAAI,CAACvC,WAAW,CAAC0B,QAAQ,CAAC,IAAI,CAACI,EAAE,EAAE;MACjC,OAAOU,OAAO,CAACC,KAAK,CAAC,gEAAgEf,QAAQ,EAAE,CAAC;IAClG,CAAC,MAAM,IAAII,EAAE,EAAE;MACbA,EAAE,CAACY,gBAAgB,CAACtC,KAAK,EAAEC,OAAO,CAAC;MACnCsB,IAAI,CAACxB,EAAE,CAAC,SAAS,EAAE,MAAM;QACvB,OAAO2B,EAAE,CAACa,mBAAmB,CAACvC,KAAK,EAAEC,OAAO,CAAC;MAC/C,CAAC,CAAC;IACJ,CAAC,MAAM;MACLgC,QAAQ,CAACO,IAAI,CAACF,gBAAgB,CAACtC,KAAK,EAAEC,OAAO,EAAE,IAAI,CAAC;MACpDsB,IAAI,CAACxB,EAAE,CAAC,SAAS,EAAE,MAAM;QACvB,OAAOkC,QAAQ,CAACO,IAAI,CAACD,mBAAmB,CAACvC,KAAK,EAAEC,OAAO,EAAE,IAAI,CAAC;MAChE,CAAC,CAAC;IACJ;EACF,CAAC,MAAM;IACL,OAAOmC,OAAO,CAACC,KAAK,CAAC,sDAAsD,CAAC;EAC9E;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASI,eAAeA,CAACC,MAAM,EAAE;EAC/B,IAAI,CAAC/C,QAAQ,CAAC+C,MAAM,CAAC,IAAIA,MAAM,KAAK,EAAE,EAAE;IACtC,OAAO,EAAE;EACX;EACA,OAAOA,MAAM,CAACC,MAAM,CAACD,MAAM,CAACvB,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,GAAGuB,MAAM,GAAG,GAAGA,MAAM;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACrB,IAAI,EAAE;EAC3B,MAAM1E,OAAO,GAAG0E,IAAI,CAAC1E,OAAO,CAACgG,QAAQ,IAAI,CAAC,CAAC;EAC3C,MAAMC,UAAU,GAAGhH,MAAM,CAACiH,MAAM,CAAC,CAAC,CAAC,EAAElG,OAAO,CAAC;EAC7C,IAAI6C,UAAU,CAACoD,UAAU,CAACzF,OAAO,CAAC,EAAE;IAClC;IACAyF,UAAU,CAACzF,OAAO,GAAGyF,UAAU,CAACzF,OAAO,CAACpB,IAAI,CAACsF,IAAI,CAAC;EACpD;EACA,IAAI5B,QAAQ,CAACmD,UAAU,CAACzF,OAAO,CAAC,EAAE;IAChC;IACA;IACA,IAAI;MACFyF,UAAU,CAACzF,OAAO,GAAG4E,QAAQ,CAACC,aAAa,CAACY,UAAU,CAACzF,OAAO,CAAC;IACjE,CAAC,CAAC,OAAO8E,CAAC,EAAE;MACV;IAAA;IAEF,IAAI,CAACW,UAAU,CAACzF,OAAO,EAAE;MACvB+E,OAAO,CAACC,KAAK,CAAC,oDAAoDxF,OAAO,CAACQ,OAAO,EAAE,CAAC;IACtF;EACF;EACA,OAAOyF,UAAU;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAACC,uBAAuB,EAAE;EACjD,IAAIA,uBAAuB,KAAKpD,SAAS,IAAIoD,uBAAuB,KAAK,IAAI,EAAE;IAC7E,OAAO,IAAI;EACb;EACA,OAAO,CAACA,uBAAuB,CAAC5F,OAAO,IAAI,CAAC4F,uBAAuB,CAAClD,EAAE;AACxE;;AAEA;AACA;AACA;AACA;AACA,SAASmD,IAAIA,CAAA,EAAG;EACd,IAAIC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAClB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAI;IAClE,MAAMC,CAAC,GAAG,CAACL,CAAC,GAAGM,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;IAC3CP,CAAC,GAAGM,IAAI,CAACE,KAAK,CAACR,CAAC,GAAG,EAAE,CAAC;IACtB,OAAO,CAACI,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG,GAAG,EAAExH,QAAQ,CAAC,EAAE,CAAC;EACpD,CAAC,CAAC;AACJ;AAEA,SAAS4H,QAAQA,CAAA,EAAG;EAClBA,QAAQ,GAAG9H,MAAM,CAACiH,MAAM,GAAGjH,MAAM,CAACiH,MAAM,CAAC3B,IAAI,CAAC,CAAC,GAAG,UAAUnE,MAAM,EAAE;IAClE,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,SAAS,CAAC1C,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC,IAAIhE,MAAM,GAAG2G,SAAS,CAAC3C,CAAC,CAAC;MACzB,KAAK,IAAI3D,GAAG,IAAIL,MAAM,EAAE;QACtB,IAAIpB,MAAM,CAACC,SAAS,CAACsC,cAAc,CAACpC,IAAI,CAACiB,MAAM,EAAEK,GAAG,CAAC,EAAE;UACrDN,MAAM,CAACM,GAAG,CAAC,GAAGL,MAAM,CAACK,GAAG,CAAC;QAC3B;MACF;IACF;IACA,OAAON,MAAM;EACf,CAAC;EACD,OAAO2G,QAAQ,CAAC/C,KAAK,CAAC,IAAI,EAAEgD,SAAS,CAAC;AACxC;AACA,SAASC,6BAA6BA,CAAC5G,MAAM,EAAE6G,QAAQ,EAAE;EACvD,IAAI7G,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7B,IAAID,MAAM,GAAG,CAAC,CAAC;EACf,IAAI+G,UAAU,GAAGlI,MAAM,CAACiC,IAAI,CAACb,MAAM,CAAC;EACpC,IAAIK,GAAG,EAAE2D,CAAC;EACV,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8C,UAAU,CAAC7C,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC3D,GAAG,GAAGyG,UAAU,CAAC9C,CAAC,CAAC;IACnB,IAAI6C,QAAQ,CAACE,OAAO,CAAC1G,GAAG,CAAC,IAAI,CAAC,EAAE;IAChCN,MAAM,CAACM,GAAG,CAAC,GAAGL,MAAM,CAACK,GAAG,CAAC;EAC3B;EACA,OAAON,MAAM;AACf;AAEA,MAAMiH,GAAG,GAAGT,IAAI,CAACS,GAAG;AACpB,MAAMC,GAAG,GAAGV,IAAI,CAACU,GAAG;AACpB,MAAMC,KAAK,GAAGX,IAAI,CAACW,KAAK;AACxB,MAAMT,KAAK,GAAGF,IAAI,CAACE,KAAK;AACxB,MAAMU,YAAY,GAAGC,CAAC,KAAK;EACzBC,CAAC,EAAED,CAAC;EACJE,CAAC,EAAEF;AACL,CAAC,CAAC;AACF,MAAMG,eAAe,GAAG;EACtBC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,KAAK;EACbC,GAAG,EAAE;AACP,CAAC;AACD,MAAMC,oBAAoB,GAAG;EAC3BC,KAAK,EAAE,KAAK;EACZC,GAAG,EAAE;AACP,CAAC;AACD,SAASC,KAAKA,CAACF,KAAK,EAAErJ,KAAK,EAAEsJ,GAAG,EAAE;EAChC,OAAOb,GAAG,CAACY,KAAK,EAAEb,GAAG,CAACxI,KAAK,EAAEsJ,GAAG,CAAC,CAAC;AACpC;AACA,SAASE,QAAQA,CAACxJ,KAAK,EAAEyJ,KAAK,EAAE;EAC9B,OAAO,OAAOzJ,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACyJ,KAAK,CAAC,GAAGzJ,KAAK;AAC3D;AACA,SAAS0J,OAAOA,CAACC,SAAS,EAAE;EAC1B,OAAOA,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC;AACA,SAASC,YAAYA,CAACF,SAAS,EAAE;EAC/B,OAAOA,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC;AACA,SAASE,eAAeA,CAACC,IAAI,EAAE;EAC7B,OAAOA,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AACjC;AACA,SAASC,aAAaA,CAACD,IAAI,EAAE;EAC3B,OAAOA,IAAI,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;AAC1C;AACA,SAASE,WAAWA,CAACN,SAAS,EAAE;EAC9B,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAACO,QAAQ,CAACR,OAAO,CAACC,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;AACnE;AACA,SAASQ,gBAAgBA,CAACR,SAAS,EAAE;EACnC,OAAOG,eAAe,CAACG,WAAW,CAACN,SAAS,CAAC,CAAC;AAChD;AACA,SAASS,iBAAiBA,CAACT,SAAS,EAAEU,KAAK,EAAEC,GAAG,EAAE;EAChD,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;IAClBA,GAAG,GAAG,KAAK;EACb;EACA,MAAMC,SAAS,GAAGV,YAAY,CAACF,SAAS,CAAC;EACzC,MAAMa,aAAa,GAAGL,gBAAgB,CAACR,SAAS,CAAC;EACjD,MAAMlE,MAAM,GAAGuE,aAAa,CAACQ,aAAa,CAAC;EAC3C,IAAIC,iBAAiB,GAAGD,aAAa,KAAK,GAAG,GAAGD,SAAS,MAAMD,GAAG,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,OAAO,GAAG,MAAM,GAAGC,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,KAAK;EACnJ,IAAIF,KAAK,CAACK,SAAS,CAACjF,MAAM,CAAC,GAAG4E,KAAK,CAACM,QAAQ,CAAClF,MAAM,CAAC,EAAE;IACpDgF,iBAAiB,GAAGG,oBAAoB,CAACH,iBAAiB,CAAC;EAC7D;EACA,OAAO,CAACA,iBAAiB,EAAEG,oBAAoB,CAACH,iBAAiB,CAAC,CAAC;AACrE;AACA,SAASI,qBAAqBA,CAAClB,SAAS,EAAE;EACxC,MAAMmB,iBAAiB,GAAGF,oBAAoB,CAACjB,SAAS,CAAC;EACzD,OAAO,CAACoB,6BAA6B,CAACpB,SAAS,CAAC,EAAEmB,iBAAiB,EAAEC,6BAA6B,CAACD,iBAAiB,CAAC,CAAC;AACxH;AACA,SAASC,6BAA6BA,CAACpB,SAAS,EAAE;EAChD,OAAOA,SAAS,CAAC/B,OAAO,CAAC,YAAY,EAAE2C,SAAS,IAAInB,oBAAoB,CAACmB,SAAS,CAAC,CAAC;AACtF;AACA,SAASS,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAEZ,GAAG,EAAE;EACvC,MAAMa,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;EAC5B,MAAMC,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC;EAC5B,MAAMC,EAAE,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC5B,MAAMC,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;EAC5B,QAAQL,IAAI;IACV,KAAK,KAAK;IACV,KAAK,QAAQ;MACX,IAAIX,GAAG,EAAE,OAAOY,OAAO,GAAGE,EAAE,GAAGD,EAAE;MACjC,OAAOD,OAAO,GAAGC,EAAE,GAAGC,EAAE;IAC1B,KAAK,MAAM;IACX,KAAK,OAAO;MACV,OAAOF,OAAO,GAAGG,EAAE,GAAGC,EAAE;IAC1B;MACE,OAAO,EAAE;EACb;AACF;AACA,SAASC,yBAAyBA,CAAC5B,SAAS,EAAE6B,aAAa,EAAEC,SAAS,EAAEnB,GAAG,EAAE;EAC3E,MAAMC,SAAS,GAAGV,YAAY,CAACF,SAAS,CAAC;EACzC,IAAI+B,IAAI,GAAGV,WAAW,CAACtB,OAAO,CAACC,SAAS,CAAC,EAAE8B,SAAS,KAAK,OAAO,EAAEnB,GAAG,CAAC;EACtE,IAAIC,SAAS,EAAE;IACbmB,IAAI,GAAGA,IAAI,CAAChK,GAAG,CAACuJ,IAAI,IAAIA,IAAI,GAAG,GAAG,GAAGV,SAAS,CAAC;IAC/C,IAAIiB,aAAa,EAAE;MACjBE,IAAI,GAAGA,IAAI,CAACjK,MAAM,CAACiK,IAAI,CAAChK,GAAG,CAACqJ,6BAA6B,CAAC,CAAC;IAC7D;EACF;EACA,OAAOW,IAAI;AACb;AACA,SAASd,oBAAoBA,CAACjB,SAAS,EAAE;EACvC,OAAOA,SAAS,CAAC/B,OAAO,CAAC,wBAAwB,EAAEqD,IAAI,IAAIlC,eAAe,CAACkC,IAAI,CAAC,CAAC;AACnF;AACA,SAASU,mBAAmBA,CAACC,OAAO,EAAE;EACpC,OAAO1D,QAAQ,CAAC;IACdiB,GAAG,EAAE,CAAC;IACNF,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTF,IAAI,EAAE;EACR,CAAC,EAAE4C,OAAO,CAAC;AACb;AACA,SAASC,gBAAgBA,CAACD,OAAO,EAAE;EACjC,OAAO,OAAOA,OAAO,KAAK,QAAQ,GAAGD,mBAAmB,CAACC,OAAO,CAAC,GAAG;IAClEzC,GAAG,EAAEyC,OAAO;IACZ3C,KAAK,EAAE2C,OAAO;IACd1C,MAAM,EAAE0C,OAAO;IACf5C,IAAI,EAAE4C;EACR,CAAC;AACH;AACA,SAASE,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,OAAO7D,QAAQ,CAAC,CAAC,CAAC,EAAE6D,IAAI,EAAE;IACxB5C,GAAG,EAAE4C,IAAI,CAACjD,CAAC;IACXE,IAAI,EAAE+C,IAAI,CAAClD,CAAC;IACZI,KAAK,EAAE8C,IAAI,CAAClD,CAAC,GAAGkD,IAAI,CAACC,KAAK;IAC1B9C,MAAM,EAAE6C,IAAI,CAACjD,CAAC,GAAGiD,IAAI,CAACE;EACxB,CAAC,CAAC;AACJ;AAEA,MAAMC,UAAU,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,2BAA2B,EAAE,eAAe,CAAC;EAClIC,UAAU,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC;AACnD,SAASC,0BAA0BA,CAACC,IAAI,EAAE1C,SAAS,EAAEW,GAAG,EAAE;EACxD,IAAI;IACFI,SAAS;IACTC;EACF,CAAC,GAAG0B,IAAI;EACR,MAAMC,QAAQ,GAAGrC,WAAW,CAACN,SAAS,CAAC;EACvC,MAAMa,aAAa,GAAGL,gBAAgB,CAACR,SAAS,CAAC;EACjD,MAAM4C,WAAW,GAAGvC,aAAa,CAACQ,aAAa,CAAC;EAChD,MAAMS,IAAI,GAAGvB,OAAO,CAACC,SAAS,CAAC;EAC/B,MAAM6C,UAAU,GAAGF,QAAQ,KAAK,GAAG;EACnC,MAAMG,OAAO,GAAG/B,SAAS,CAAC7B,CAAC,GAAG6B,SAAS,CAACsB,KAAK,GAAG,CAAC,GAAGrB,QAAQ,CAACqB,KAAK,GAAG,CAAC;EACtE,MAAMU,OAAO,GAAGhC,SAAS,CAAC5B,CAAC,GAAG4B,SAAS,CAACuB,MAAM,GAAG,CAAC,GAAGtB,QAAQ,CAACsB,MAAM,GAAG,CAAC;EACxE,MAAMU,WAAW,GAAGjC,SAAS,CAAC6B,WAAW,CAAC,GAAG,CAAC,GAAG5B,QAAQ,CAAC4B,WAAW,CAAC,GAAG,CAAC;EAC1E,IAAIK,MAAM;EACV,QAAQ3B,IAAI;IACV,KAAK,KAAK;MACR2B,MAAM,GAAG;QACP/D,CAAC,EAAE4D,OAAO;QACV3D,CAAC,EAAE4B,SAAS,CAAC5B,CAAC,GAAG6B,QAAQ,CAACsB;MAC5B,CAAC;MACD;IACF,KAAK,QAAQ;MACXW,MAAM,GAAG;QACP/D,CAAC,EAAE4D,OAAO;QACV3D,CAAC,EAAE4B,SAAS,CAAC5B,CAAC,GAAG4B,SAAS,CAACuB;MAC7B,CAAC;MACD;IACF,KAAK,OAAO;MACVW,MAAM,GAAG;QACP/D,CAAC,EAAE6B,SAAS,CAAC7B,CAAC,GAAG6B,SAAS,CAACsB,KAAK;QAChClD,CAAC,EAAE4D;MACL,CAAC;MACD;IACF,KAAK,MAAM;MACTE,MAAM,GAAG;QACP/D,CAAC,EAAE6B,SAAS,CAAC7B,CAAC,GAAG8B,QAAQ,CAACqB,KAAK;QAC/BlD,CAAC,EAAE4D;MACL,CAAC;MACD;IACF;MACEE,MAAM,GAAG;QACP/D,CAAC,EAAE6B,SAAS,CAAC7B,CAAC;QACdC,CAAC,EAAE4B,SAAS,CAAC5B;MACf,CAAC;EACL;EACA,QAAQe,YAAY,CAACF,SAAS,CAAC;IAC7B,KAAK,OAAO;MACViD,MAAM,CAACpC,aAAa,CAAC,IAAImC,WAAW,IAAIrC,GAAG,IAAIkC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACnE;IACF,KAAK,KAAK;MACRI,MAAM,CAACpC,aAAa,CAAC,IAAImC,WAAW,IAAIrC,GAAG,IAAIkC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACnE;EACJ;EACA,OAAOI,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB;EAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,WAAOrC,SAAS,EAAEC,QAAQ,EAAEqC,MAAM,EAAK;IAC/D,MAAM;MACJrD,SAAS,GAAG,QAAQ;MACpBsD,QAAQ,GAAG,UAAU;MACrBC,UAAU,GAAG,EAAE;MACfC;IACF,CAAC,GAAGH,MAAM;IACV,MAAMI,eAAe,GAAGF,UAAU,CAACjL,MAAM,CAACoL,OAAO,CAAC;IAClD,MAAM/C,GAAG,SAAU6C,QAAQ,CAACG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGH,QAAQ,CAACG,KAAK,CAAC3C,QAAQ,CAAE;IAC9E,IAAIN,KAAK,SAAS8C,QAAQ,CAACI,eAAe,CAAC;MACzC7C,SAAS;MACTC,QAAQ;MACRsC;IACF,CAAC,CAAC;IACF,IAAI;MACFpE,CAAC;MACDC;IACF,CAAC,GAAGsD,0BAA0B,CAAC/B,KAAK,EAAEV,SAAS,EAAEW,GAAG,CAAC;IACrD,IAAIkD,iBAAiB,GAAG7D,SAAS;IACjC,IAAI8D,cAAc,GAAG,CAAC,CAAC;IACvB,IAAIC,UAAU,GAAG,CAAC;IAClB,KAAK,IAAIlI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4H,eAAe,CAAC3H,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/C,MAAM;QACJmI,IAAI;QACJC;MACF,CAAC,GAAGR,eAAe,CAAC5H,CAAC,CAAC;MACtB,MAAM;QACJqD,CAAC,EAAEgF,KAAK;QACR/E,CAAC,EAAEgF,KAAK;QACRC,IAAI;QACJC;MACF,CAAC,SAASJ,EAAE,CAAC;QACX/E,CAAC;QACDC,CAAC;QACDmF,gBAAgB,EAAEtE,SAAS;QAC3BA,SAAS,EAAE6D,iBAAiB;QAC5BP,QAAQ;QACRQ,cAAc;QACdpD,KAAK;QACL8C,QAAQ;QACRe,QAAQ,EAAE;UACRxD,SAAS;UACTC;QACF;MACF,CAAC,CAAC;MACF9B,CAAC,GAAGgF,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGhF,CAAC;MAC7BC,CAAC,GAAGgF,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGhF,CAAC;MAC7B2E,cAAc,GAAGvF,QAAQ,CAAC,CAAC,CAAC,EAAEuF,cAAc,EAAE;QAC5C,CAACE,IAAI,GAAGzF,QAAQ,CAAC,CAAC,CAAC,EAAEuF,cAAc,CAACE,IAAI,CAAC,EAAEI,IAAI;MACjD,CAAC,CAAC;MACF,IAAIC,KAAK,IAAIN,UAAU,IAAI,EAAE,EAAE;QAC7BA,UAAU,EAAE;QACZ,IAAI,OAAOM,KAAK,KAAK,QAAQ,EAAE;UAC7B,IAAIA,KAAK,CAACrE,SAAS,EAAE;YACnB6D,iBAAiB,GAAGQ,KAAK,CAACrE,SAAS;UACrC;UACA,IAAIqE,KAAK,CAAC3D,KAAK,EAAE;YACfA,KAAK,GAAG2D,KAAK,CAAC3D,KAAK,KAAK,IAAI,SAAS8C,QAAQ,CAACI,eAAe,CAAC;cAC5D7C,SAAS;cACTC,QAAQ;cACRsC;YACF,CAAC,CAAC,GAAGe,KAAK,CAAC3D,KAAK;UAClB;UACA,CAAC;YACCxB,CAAC;YACDC;UACF,CAAC,GAAGsD,0BAA0B,CAAC/B,KAAK,EAAEmD,iBAAiB,EAAElD,GAAG,CAAC;QAC/D;QACA9E,CAAC,GAAG,CAAC,CAAC;QACN;MACF;IACF;IACA,OAAO;MACLqD,CAAC;MACDC,CAAC;MACDa,SAAS,EAAE6D,iBAAiB;MAC5BP,QAAQ;MACRQ;IACF,CAAC;EACH,CAAC;EAAA,gBA/EKZ,iBAAiBA,CAAAsB,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAvB,KAAA,CAAA3H,KAAA,OAAAgD,SAAA;EAAA;AAAA,GA+EtB;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAQemG,cAAcA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,eAAA,CAAAtJ,KAAA,OAAAgD,SAAA;AAAA;AAsD7B;AACA;AACA;AACA;AACA;AAJA,SAAAsG,gBAAA;EAAAA,eAAA,GAAA1B,iBAAA,CAtDA,WAA8B2B,KAAK,EAAEvN,OAAO,EAAE;IAC5C,IAAIwN,qBAAqB;IACzB,IAAIxN,OAAO,KAAK,KAAK,CAAC,EAAE;MACtBA,OAAO,GAAG,CAAC,CAAC;IACd;IACA,MAAM;MACJ0H,CAAC;MACDC,CAAC;MACDqE,QAAQ;MACR9C,KAAK;MACL6D,QAAQ;MACRjB;IACF,CAAC,GAAGyB,KAAK;IACT,MAAM;MACJE,QAAQ,GAAG,mBAAmB;MAC9BC,YAAY,GAAG,UAAU;MACzBC,cAAc,GAAG,UAAU;MAC3BC,WAAW,GAAG,KAAK;MACnBnD,OAAO,GAAG;IACZ,CAAC,GAAGpC,QAAQ,CAACrI,OAAO,EAAEuN,KAAK,CAAC;IAC5B,MAAMM,aAAa,GAAGnD,gBAAgB,CAACD,OAAO,CAAC;IAC/C,MAAMqD,UAAU,GAAGH,cAAc,KAAK,UAAU,GAAG,WAAW,GAAG,UAAU;IAC3E,MAAMnN,OAAO,GAAGuM,QAAQ,CAACa,WAAW,GAAGE,UAAU,GAAGH,cAAc,CAAC;IACnE,MAAMI,kBAAkB,GAAGpD,gBAAgB,OAAOqB,QAAQ,CAACgC,eAAe,CAAC;MACzExN,OAAO,EAAE,CAAC,CAACgN,qBAAqB,SAAUxB,QAAQ,CAACiC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGjC,QAAQ,CAACiC,SAAS,CAACzN,OAAO,CAAE,KAAK,IAAI,GAAGgN,qBAAqB,GAAG,IAAI,IAAIhN,OAAO,GAAGA,OAAO,CAAC0N,cAAc,WAAYlC,QAAQ,CAACmC,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGnC,QAAQ,CAACmC,kBAAkB,CAACpB,QAAQ,CAACvD,QAAQ,CAAC,CAAE;MACnSiE,QAAQ;MACRC,YAAY;MACZ5B;IACF,CAAC,CAAC,CAAC;IACH,MAAMlB,IAAI,GAAG+C,cAAc,KAAK,UAAU,GAAG5G,QAAQ,CAAC,CAAC,CAAC,EAAEmC,KAAK,CAACM,QAAQ,EAAE;MACxE9B,CAAC;MACDC;IACF,CAAC,CAAC,GAAGuB,KAAK,CAACK,SAAS;IACpB,MAAM6E,YAAY,SAAUpC,QAAQ,CAACqC,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGrC,QAAQ,CAACqC,eAAe,CAACtB,QAAQ,CAACvD,QAAQ,CAAE;IACpH,MAAM8E,WAAW,GAAG,OAAQtC,QAAQ,CAACiC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGjC,QAAQ,CAACiC,SAAS,CAACG,YAAY,CAAC,IAAK,OAAQpC,QAAQ,CAACuC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGvC,QAAQ,CAACuC,QAAQ,CAACH,YAAY,CAAC,KAAM;MACvL1G,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC,GAAG;MACFD,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;IACD,MAAM6G,iBAAiB,GAAG7D,gBAAgB,CAACqB,QAAQ,CAACyC,qDAAqD,SAASzC,QAAQ,CAACyC,qDAAqD,CAAC;MAC/K7D,IAAI;MACJwD,YAAY;MACZtC;IACF,CAAC,CAAC,GAAGlB,IAAI,CAAC;IACV,OAAO;MACL5C,GAAG,EAAE,CAAC+F,kBAAkB,CAAC/F,GAAG,GAAGwG,iBAAiB,CAACxG,GAAG,GAAG6F,aAAa,CAAC7F,GAAG,IAAIsG,WAAW,CAAC3G,CAAC;MACzFI,MAAM,EAAE,CAACyG,iBAAiB,CAACzG,MAAM,GAAGgG,kBAAkB,CAAChG,MAAM,GAAG8F,aAAa,CAAC9F,MAAM,IAAIuG,WAAW,CAAC3G,CAAC;MACrGE,IAAI,EAAE,CAACkG,kBAAkB,CAAClG,IAAI,GAAG2G,iBAAiB,CAAC3G,IAAI,GAAGgG,aAAa,CAAChG,IAAI,IAAIyG,WAAW,CAAC5G,CAAC;MAC7FI,KAAK,EAAE,CAAC0G,iBAAiB,CAAC1G,KAAK,GAAGiG,kBAAkB,CAACjG,KAAK,GAAG+F,aAAa,CAAC/F,KAAK,IAAIwG,WAAW,CAAC5G;IAClG,CAAC;EACH,CAAC;EAAA,OAAA4F,eAAA,CAAAtJ,KAAA,OAAAgD,SAAA;AAAA;AAOD,MAAM0H,KAAK,GAAG1O,OAAO,KAAK;EACxBwM,IAAI,EAAE,OAAO;EACbxM,OAAO;EACDyM,EAAEA,CAACc,KAAK,EAAE;IAAA,OAAA3B,iBAAA;MACd,MAAM;QACJlE,CAAC;QACDC,CAAC;QACDa,SAAS;QACTU,KAAK;QACL8C,QAAQ;QACRe;MACF,CAAC,GAAGQ,KAAK;MACT;MACA,MAAM;QACJ/M,OAAO;QACPiK,OAAO,GAAG;MACZ,CAAC,GAAGpC,QAAQ,CAACrI,OAAO,EAAEuN,KAAK,CAAC,IAAI,CAAC,CAAC;MAClC,IAAI/M,OAAO,IAAI,IAAI,EAAE;QACnB,OAAO,CAAC,CAAC;MACX;MACA,MAAMqN,aAAa,GAAGnD,gBAAgB,CAACD,OAAO,CAAC;MAC/C,MAAMgB,MAAM,GAAG;QACb/D,CAAC;QACDC;MACF,CAAC;MACD,MAAMiB,IAAI,GAAGI,gBAAgB,CAACR,SAAS,CAAC;MACxC,MAAMlE,MAAM,GAAGuE,aAAa,CAACD,IAAI,CAAC;MAClC,MAAM+F,eAAe,SAAS3C,QAAQ,CAAC4C,aAAa,CAACpO,OAAO,CAAC;MAC7D,MAAMqO,OAAO,GAAGjG,IAAI,KAAK,GAAG;MAC5B,MAAMkG,OAAO,GAAGD,OAAO,GAAG,KAAK,GAAG,MAAM;MACxC,MAAME,OAAO,GAAGF,OAAO,GAAG,QAAQ,GAAG,OAAO;MAC5C,MAAMG,UAAU,GAAGH,OAAO,GAAG,cAAc,GAAG,aAAa;MAC3D,MAAMI,OAAO,GAAG/F,KAAK,CAACK,SAAS,CAACjF,MAAM,CAAC,GAAG4E,KAAK,CAACK,SAAS,CAACX,IAAI,CAAC,GAAG6C,MAAM,CAAC7C,IAAI,CAAC,GAAGM,KAAK,CAACM,QAAQ,CAAClF,MAAM,CAAC;MACvG,MAAM4K,SAAS,GAAGzD,MAAM,CAAC7C,IAAI,CAAC,GAAGM,KAAK,CAACK,SAAS,CAACX,IAAI,CAAC;MACtD,MAAMuG,iBAAiB,SAAUnD,QAAQ,CAACqC,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGrC,QAAQ,CAACqC,eAAe,CAAC7N,OAAO,CAAE;MAC/G,IAAI4O,UAAU,GAAGD,iBAAiB,GAAGA,iBAAiB,CAACH,UAAU,CAAC,GAAG,CAAC;;MAEtE;MACA,IAAI,CAACI,UAAU,IAAI,QAASpD,QAAQ,CAACiC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGjC,QAAQ,CAACiC,SAAS,CAACkB,iBAAiB,CAAC,CAAE,EAAE;QACzGC,UAAU,GAAGrC,QAAQ,CAACvD,QAAQ,CAACwF,UAAU,CAAC,IAAI9F,KAAK,CAACM,QAAQ,CAAClF,MAAM,CAAC;MACtE;MACA,MAAM+K,iBAAiB,GAAGJ,OAAO,GAAG,CAAC,GAAGC,SAAS,GAAG,CAAC;;MAErD;MACA;MACA,MAAMI,sBAAsB,GAAGF,UAAU,GAAG,CAAC,GAAGT,eAAe,CAACrK,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;MAC/E,MAAMiL,UAAU,GAAGlI,GAAG,CAACwG,aAAa,CAACiB,OAAO,CAAC,EAAEQ,sBAAsB,CAAC;MACtE,MAAME,UAAU,GAAGnI,GAAG,CAACwG,aAAa,CAACkB,OAAO,CAAC,EAAEO,sBAAsB,CAAC;;MAEtE;MACA;MACA,MAAMG,KAAK,GAAGF,UAAU;MACxB,MAAMjI,GAAG,GAAG8H,UAAU,GAAGT,eAAe,CAACrK,MAAM,CAAC,GAAGkL,UAAU;MAC7D,MAAME,MAAM,GAAGN,UAAU,GAAG,CAAC,GAAGT,eAAe,CAACrK,MAAM,CAAC,GAAG,CAAC,GAAG+K,iBAAiB;MAC/E,MAAMM,MAAM,GAAGvH,KAAK,CAACqH,KAAK,EAAEC,MAAM,EAAEpI,GAAG,CAAC;;MAExC;MACA;MACA;MACA;MACA,MAAMsI,eAAe,GAAGlH,YAAY,CAACF,SAAS,CAAC,IAAI,IAAI,IAAIkH,MAAM,IAAIC,MAAM,IAAIzG,KAAK,CAACK,SAAS,CAACjF,MAAM,CAAC,GAAG,CAAC,IAAIoL,MAAM,GAAGD,KAAK,GAAGF,UAAU,GAAGC,UAAU,CAAC,GAAGb,eAAe,CAACrK,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;MACzL,MAAMuL,eAAe,GAAGD,eAAe,GAAGF,MAAM,GAAGD,KAAK,GAAGA,KAAK,GAAGC,MAAM,GAAGpI,GAAG,GAAGoI,MAAM,GAAG,CAAC;MAC5F,OAAO;QACL,CAAC9G,IAAI,GAAG6C,MAAM,CAAC7C,IAAI,CAAC,GAAGiH,eAAe;QACtCjD,IAAI,EAAE;UACJ,CAAChE,IAAI,GAAG+G,MAAM;UACdG,YAAY,EAAEJ,MAAM,GAAGC,MAAM,GAAGE;QAClC;MACF,CAAC;IAAC;EACJ;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,IAAI,GAAG,SAASA,IAAIA,CAAC/P,OAAO,EAAE;EAClC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLwM,IAAI,EAAE,MAAM;IACZxM,OAAO;IACDyM,EAAEA,CAACc,KAAK,EAAE;MAAA,OAAA3B,iBAAA;QACd,IAAIoE,oBAAoB;QACxB,MAAM;UACJxH,SAAS;UACT8D,cAAc;UACdpD,KAAK;UACL4D,gBAAgB;UAChBd,QAAQ;UACRe;QACF,CAAC,GAAGQ,KAAK;QACT,MAAM0C,UAAU,GAAG5H,QAAQ,CAACrI,OAAO,EAAEuN,KAAK,CAAC;UACzC;YACE2C,QAAQ,EAAEC,aAAa,GAAG,IAAI;YAC9BC,SAAS,EAAEC,cAAc,GAAG,IAAI;YAChCC,kBAAkB,EAAEC,2BAA2B;YAC/CC,gBAAgB,GAAG,SAAS;YAC5BC,yBAAyB,GAAG,MAAM;YAClCpG,aAAa,GAAG;UAClB,CAAC,GAAG4F,UAAU;UACdS,qBAAqB,GAAGzJ,6BAA6B,CAACgJ,UAAU,EAAElF,UAAU,CAAC;QAC/E,MAAMjB,IAAI,GAAGvB,OAAO,CAACC,SAAS,CAAC;QAC/B,MAAMmI,eAAe,GAAGpI,OAAO,CAACuE,gBAAgB,CAAC,KAAKA,gBAAgB;QACtE,MAAM3D,GAAG,SAAU6C,QAAQ,CAACG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGH,QAAQ,CAACG,KAAK,CAACY,QAAQ,CAACvD,QAAQ,CAAE;QACvF,MAAM8G,kBAAkB,GAAGC,2BAA2B,KAAKI,eAAe,IAAI,CAACtG,aAAa,GAAG,CAACZ,oBAAoB,CAACqD,gBAAgB,CAAC,CAAC,GAAGpD,qBAAqB,CAACoD,gBAAgB,CAAC,CAAC;QAClL,IAAI,CAACyD,2BAA2B,IAAIE,yBAAyB,KAAK,MAAM,EAAE;UACxEH,kBAAkB,CAAC9M,IAAI,CAAC,GAAG4G,yBAAyB,CAAC0C,gBAAgB,EAAEzC,aAAa,EAAEoG,yBAAyB,EAAEtH,GAAG,CAAC,CAAC;QACxH;QACA,MAAMyH,UAAU,GAAG,CAAC9D,gBAAgB,EAAE,GAAGwD,kBAAkB,CAAC;QAC5D,MAAMO,QAAQ,SAAS1D,cAAc,CAACI,KAAK,EAAEmD,qBAAqB,CAAC;QACnE,MAAMI,SAAS,GAAG,EAAE;QACpB,IAAIC,aAAa,GAAG,CAAC,CAACf,oBAAoB,GAAG1D,cAAc,CAACyD,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,oBAAoB,CAACc,SAAS,KAAK,EAAE;QAC1H,IAAIX,aAAa,EAAE;UACjBW,SAAS,CAACtN,IAAI,CAACqN,QAAQ,CAAC/G,IAAI,CAAC,CAAC;QAChC;QACA,IAAIuG,cAAc,EAAE;UAClB,MAAMW,KAAK,GAAG/H,iBAAiB,CAACT,SAAS,EAAEU,KAAK,EAAEC,GAAG,CAAC;UACtD2H,SAAS,CAACtN,IAAI,CAACqN,QAAQ,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEH,QAAQ,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD;QACAD,aAAa,GAAG,CAAC,GAAGA,aAAa,EAAE;UACjCvI,SAAS;UACTsI;QACF,CAAC,CAAC;;QAEF;QACA,IAAI,CAACA,SAAS,CAACG,KAAK,CAACnH,IAAI,IAAIA,IAAI,IAAI,CAAC,CAAC,EAAE;UACvC,IAAIoH,qBAAqB,EAAEC,qBAAqB;UAChD,MAAMC,SAAS,GAAG,CAAC,CAAC,CAACF,qBAAqB,GAAG5E,cAAc,CAACyD,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmB,qBAAqB,CAACvN,KAAK,KAAK,CAAC,IAAI,CAAC;UAC3H,MAAM0N,aAAa,GAAGT,UAAU,CAACQ,SAAS,CAAC;UAC3C,IAAIC,aAAa,EAAE;YACjB;YACA,OAAO;cACLzE,IAAI,EAAE;gBACJjJ,KAAK,EAAEyN,SAAS;gBAChBN,SAAS,EAAEC;cACb,CAAC;cACDlE,KAAK,EAAE;gBACLrE,SAAS,EAAE6I;cACb;YACF,CAAC;UACH;;UAEA;UACA;UACA,IAAIC,cAAc,GAAG,CAACH,qBAAqB,GAAGJ,aAAa,CAACjQ,MAAM,CAACwF,CAAC,IAAIA,CAAC,CAACwK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACV,SAAS,CAAC,CAAC,CAAC,GAAGW,CAAC,CAACX,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGK,qBAAqB,CAAC3I,SAAS;;UAEnM;UACA,IAAI,CAAC8I,cAAc,EAAE;YACnB,QAAQd,gBAAgB;cACtB,KAAK,SAAS;gBACZ;kBACE,IAAIkB,qBAAqB;kBACzB,MAAMlJ,SAAS,GAAG,CAACkJ,qBAAqB,GAAGX,aAAa,CAACxQ,GAAG,CAAC+F,CAAC,IAAI,CAACA,CAAC,CAACkC,SAAS,EAAElC,CAAC,CAACwK,SAAS,CAAChQ,MAAM,CAAC+P,QAAQ,IAAIA,QAAQ,GAAG,CAAC,CAAC,CAACzO,MAAM,CAAC,CAACuP,GAAG,EAAEd,QAAQ,KAAKc,GAAG,GAAGd,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,qBAAqB,CAAC,CAAC,CAAC;kBACvP,IAAIlJ,SAAS,EAAE;oBACb8I,cAAc,GAAG9I,SAAS;kBAC5B;kBACA;gBACF;cACF,KAAK,kBAAkB;gBACrB8I,cAAc,GAAGxE,gBAAgB;gBACjC;YACJ;UACF;UACA,IAAItE,SAAS,KAAK8I,cAAc,EAAE;YAChC,OAAO;cACLzE,KAAK,EAAE;gBACLrE,SAAS,EAAE8I;cACb;YACF,CAAC;UACH;QACF;QACA,OAAO,CAAC,CAAC;MAAC;IACZ;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMM,KAAK,GAAG,SAASA,KAAKA,CAAC5R,OAAO,EAAE;EACpC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLwM,IAAI,EAAE,OAAO;IACbxM,OAAO;IACDyM,EAAEA,CAACc,KAAK,EAAE;MAAA,OAAA3B,iBAAA;QACd,MAAM;UACJlE,CAAC;UACDC,CAAC;UACDa;QACF,CAAC,GAAG+E,KAAK;QACT,MAAMsE,UAAU,GAAGxJ,QAAQ,CAACrI,OAAO,EAAEuN,KAAK,CAAC;UACzC;YACE2C,QAAQ,EAAEC,aAAa,GAAG,IAAI;YAC9BC,SAAS,EAAEC,cAAc,GAAG,KAAK;YACjCyB,OAAO,GAAG;cACRrF,EAAE,EAAEvB,IAAI,IAAI;gBACV,IAAI;kBACFxD,CAAC;kBACDC;gBACF,CAAC,GAAGuD,IAAI;gBACR,OAAO;kBACLxD,CAAC;kBACDC;gBACF,CAAC;cACH;YACF;UACF,CAAC,GAAGkK,UAAU;UACdnB,qBAAqB,GAAGzJ,6BAA6B,CAAC4K,UAAU,EAAE7G,UAAU,CAAC;QAC/E,MAAMS,MAAM,GAAG;UACb/D,CAAC;UACDC;QACF,CAAC;QACD,MAAMkJ,QAAQ,SAAS1D,cAAc,CAACI,KAAK,EAAEmD,qBAAqB,CAAC;QACnE,MAAMN,SAAS,GAAGtH,WAAW,CAACP,OAAO,CAACC,SAAS,CAAC,CAAC;QACjD,MAAM0H,QAAQ,GAAGvH,eAAe,CAACyH,SAAS,CAAC;QAC3C,IAAI2B,aAAa,GAAGtG,MAAM,CAACyE,QAAQ,CAAC;QACpC,IAAI8B,cAAc,GAAGvG,MAAM,CAAC2E,SAAS,CAAC;QACtC,IAAID,aAAa,EAAE;UACjB,MAAM8B,OAAO,GAAG/B,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAG,MAAM;UACjD,MAAMgC,OAAO,GAAGhC,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;UACrD,MAAM7I,GAAG,GAAG0K,aAAa,GAAGlB,QAAQ,CAACoB,OAAO,CAAC;UAC7C,MAAM3K,GAAG,GAAGyK,aAAa,GAAGlB,QAAQ,CAACqB,OAAO,CAAC;UAC7CH,aAAa,GAAG3J,KAAK,CAACf,GAAG,EAAE0K,aAAa,EAAEzK,GAAG,CAAC;QAChD;QACA,IAAI+I,cAAc,EAAE;UAClB,MAAM4B,OAAO,GAAG7B,SAAS,KAAK,GAAG,GAAG,KAAK,GAAG,MAAM;UAClD,MAAM8B,OAAO,GAAG9B,SAAS,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;UACtD,MAAM/I,GAAG,GAAG2K,cAAc,GAAGnB,QAAQ,CAACoB,OAAO,CAAC;UAC9C,MAAM3K,GAAG,GAAG0K,cAAc,GAAGnB,QAAQ,CAACqB,OAAO,CAAC;UAC9CF,cAAc,GAAG5J,KAAK,CAACf,GAAG,EAAE2K,cAAc,EAAE1K,GAAG,CAAC;QAClD;QACA,MAAM6K,aAAa,GAAGL,OAAO,CAACrF,EAAE,CAAC1F,QAAQ,CAAC,CAAC,CAAC,EAAEwG,KAAK,EAAE;UACnD,CAAC2C,QAAQ,GAAG6B,aAAa;UACzB,CAAC3B,SAAS,GAAG4B;QACf,CAAC,CAAC,CAAC;QACH,OAAOjL,QAAQ,CAAC,CAAC,CAAC,EAAEoL,aAAa,EAAE;UACjCvF,IAAI,EAAE;YACJlF,CAAC,EAAEyK,aAAa,CAACzK,CAAC,GAAGA,CAAC;YACtBC,CAAC,EAAEwK,aAAa,CAACxK,CAAC,GAAGA;UACvB;QACF,CAAC,CAAC;MAAC;IACL;EACF,CAAC;AACH,CAAC;AACD;AACA;AACA;AACA,MAAMyK,UAAU,GAAG,SAASA,UAAUA,CAACpS,OAAO,EAAE;EAC9C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLA,OAAO;IACPyM,EAAEA,CAACc,KAAK,EAAE;MACR,MAAM;QACJ7F,CAAC;QACDC,CAAC;QACDa,SAAS;QACTU,KAAK;QACLoD;MACF,CAAC,GAAGiB,KAAK;MACT,MAAM;QACJoC,MAAM,GAAG,CAAC;QACVO,QAAQ,EAAEC,aAAa,GAAG,IAAI;QAC9BC,SAAS,EAAEC,cAAc,GAAG;MAC9B,CAAC,GAAGhI,QAAQ,CAACrI,OAAO,EAAEuN,KAAK,CAAC;MAC5B,MAAM9B,MAAM,GAAG;QACb/D,CAAC;QACDC;MACF,CAAC;MACD,MAAMyI,SAAS,GAAGtH,WAAW,CAACN,SAAS,CAAC;MACxC,MAAM0H,QAAQ,GAAGvH,eAAe,CAACyH,SAAS,CAAC;MAC3C,IAAI2B,aAAa,GAAGtG,MAAM,CAACyE,QAAQ,CAAC;MACpC,IAAI8B,cAAc,GAAGvG,MAAM,CAAC2E,SAAS,CAAC;MACtC,MAAMiC,SAAS,GAAGhK,QAAQ,CAACsH,MAAM,EAAEpC,KAAK,CAAC;MACzC,MAAM+E,cAAc,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAG;QACrDnC,QAAQ,EAAEmC,SAAS;QACnBjC,SAAS,EAAE;MACb,CAAC,GAAGrJ,QAAQ,CAAC;QACXmJ,QAAQ,EAAE,CAAC;QACXE,SAAS,EAAE;MACb,CAAC,EAAEiC,SAAS,CAAC;MACb,IAAIlC,aAAa,EAAE;QACjB,MAAMoC,GAAG,GAAGrC,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;QACjD,MAAMsC,QAAQ,GAAGtJ,KAAK,CAACK,SAAS,CAAC2G,QAAQ,CAAC,GAAGhH,KAAK,CAACM,QAAQ,CAAC+I,GAAG,CAAC,GAAGD,cAAc,CAACpC,QAAQ;QAC1F,MAAMuC,QAAQ,GAAGvJ,KAAK,CAACK,SAAS,CAAC2G,QAAQ,CAAC,GAAGhH,KAAK,CAACK,SAAS,CAACgJ,GAAG,CAAC,GAAGD,cAAc,CAACpC,QAAQ;QAC3F,IAAI6B,aAAa,GAAGS,QAAQ,EAAE;UAC5BT,aAAa,GAAGS,QAAQ;QAC1B,CAAC,MAAM,IAAIT,aAAa,GAAGU,QAAQ,EAAE;UACnCV,aAAa,GAAGU,QAAQ;QAC1B;MACF;MACA,IAAIpC,cAAc,EAAE;QAClB,IAAIqC,qBAAqB,EAAEC,sBAAsB;QACjD,MAAMJ,GAAG,GAAGrC,QAAQ,KAAK,GAAG,GAAG,OAAO,GAAG,QAAQ;QACjD,MAAM0C,YAAY,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC7J,QAAQ,CAACR,OAAO,CAACC,SAAS,CAAC,CAAC;QACjE,MAAMgK,QAAQ,GAAGtJ,KAAK,CAACK,SAAS,CAAC6G,SAAS,CAAC,GAAGlH,KAAK,CAACM,QAAQ,CAAC+I,GAAG,CAAC,IAAIK,YAAY,GAAG,CAAC,CAACF,qBAAqB,GAAGpG,cAAc,CAACqD,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+C,qBAAqB,CAACtC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAIwC,YAAY,GAAG,CAAC,GAAGN,cAAc,CAAClC,SAAS,CAAC;QACnP,MAAMqC,QAAQ,GAAGvJ,KAAK,CAACK,SAAS,CAAC6G,SAAS,CAAC,GAAGlH,KAAK,CAACK,SAAS,CAACgJ,GAAG,CAAC,IAAIK,YAAY,GAAG,CAAC,GAAG,CAAC,CAACD,sBAAsB,GAAGrG,cAAc,CAACqD,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgD,sBAAsB,CAACvC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAIwC,YAAY,GAAGN,cAAc,CAAClC,SAAS,GAAG,CAAC,CAAC;QACtP,IAAI4B,cAAc,GAAGQ,QAAQ,EAAE;UAC7BR,cAAc,GAAGQ,QAAQ;QAC3B,CAAC,MAAM,IAAIR,cAAc,GAAGS,QAAQ,EAAE;UACpCT,cAAc,GAAGS,QAAQ;QAC3B;MACF;MACA,OAAO;QACL,CAACvC,QAAQ,GAAG6B,aAAa;QACzB,CAAC3B,SAAS,GAAG4B;MACf,CAAC;IACH;EACF,CAAC;AACH,CAAC;AAED,SAASa,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIC,MAAM,CAACD,IAAI,CAAC,EAAE;IAChB,OAAO,CAACA,IAAI,CAACE,QAAQ,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;EAC5C;EACA;EACA;EACA;EACA,OAAO,WAAW;AACpB;AACA,SAASC,SAASA,CAACJ,IAAI,EAAE;EACvB,IAAIK,mBAAmB;EACvB,OAAO,CAACL,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACK,mBAAmB,GAAGL,IAAI,CAACM,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,mBAAmB,CAACE,WAAW,KAAKC,MAAM;AAC1I;AACA,SAASnF,kBAAkBA,CAAC2E,IAAI,EAAE;EAChC,IAAI5H,IAAI;EACR,OAAO,CAACA,IAAI,GAAG,CAAC6H,MAAM,CAACD,IAAI,CAAC,GAAGA,IAAI,CAACM,aAAa,GAAGN,IAAI,CAAC1N,QAAQ,KAAKkO,MAAM,CAAClO,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8F,IAAI,CAACqI,eAAe;AAChI;AACA,SAASR,MAAMA,CAAClU,KAAK,EAAE;EACrB,OAAOA,KAAK,YAAY2U,IAAI,IAAI3U,KAAK,YAAYqU,SAAS,CAACrU,KAAK,CAAC,CAAC2U,IAAI;AACxE;AACA,SAASvF,SAASA,CAACpP,KAAK,EAAE;EACxB,OAAOA,KAAK,YAAY6D,OAAO,IAAI7D,KAAK,YAAYqU,SAAS,CAACrU,KAAK,CAAC,CAAC6D,OAAO;AAC9E;AACA,SAAS+Q,aAAaA,CAAC5U,KAAK,EAAE;EAC5B,OAAOA,KAAK,YAAY+D,WAAW,IAAI/D,KAAK,YAAYqU,SAAS,CAACrU,KAAK,CAAC,CAAC+D,WAAW;AACtF;AACA,SAAS8Q,YAAYA,CAAC7U,KAAK,EAAE;EAC3B;EACA,IAAI,OAAO8U,UAAU,KAAK,WAAW,EAAE;IACrC,OAAO,KAAK;EACd;EACA,OAAO9U,KAAK,YAAY8U,UAAU,IAAI9U,KAAK,YAAYqU,SAAS,CAACrU,KAAK,CAAC,CAAC8U,UAAU;AACpF;AACA,SAASC,iBAAiBA,CAACpT,OAAO,EAAE;EAClC,MAAM;IACJqQ,QAAQ;IACRgD,SAAS;IACTC,SAAS;IACTC;EACF,CAAC,GAAGC,gBAAgB,CAACxT,OAAO,CAAC;EAC7B,OAAO,iCAAiC,CAACyT,IAAI,CAACpD,QAAQ,GAAGiD,SAAS,GAAGD,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC9K,QAAQ,CAACgL,OAAO,CAAC;AAC9H;AACA,SAASG,cAAcA,CAAC1T,OAAO,EAAE;EAC/B,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAACuI,QAAQ,CAAC8J,WAAW,CAACrS,OAAO,CAAC,CAAC;AAC7D;AACA,SAAS2T,iBAAiBA,CAAC3T,OAAO,EAAE;EAClC,MAAM4T,MAAM,GAAGC,QAAQ,CAAC,CAAC;EACzB,MAAMC,GAAG,GAAGN,gBAAgB,CAACxT,OAAO,CAAC;;EAErC;EACA,OAAO8T,GAAG,CAACC,SAAS,KAAK,MAAM,IAAID,GAAG,CAACE,WAAW,KAAK,MAAM,KAAKF,GAAG,CAACG,aAAa,GAAGH,GAAG,CAACG,aAAa,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,CAACL,MAAM,KAAKE,GAAG,CAACI,cAAc,GAAGJ,GAAG,CAACI,cAAc,KAAK,MAAM,GAAG,KAAK,CAAC,IAAI,CAACN,MAAM,KAAKE,GAAG,CAACxT,MAAM,GAAGwT,GAAG,CAACxT,MAAM,KAAK,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC6T,IAAI,CAAC9V,KAAK,IAAI,CAACyV,GAAG,CAACM,UAAU,IAAI,EAAE,EAAE7L,QAAQ,CAAClK,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC8V,IAAI,CAAC9V,KAAK,IAAI,CAACyV,GAAG,CAACO,OAAO,IAAI,EAAE,EAAE9L,QAAQ,CAAClK,KAAK,CAAC,CAAC;AACpc;AACA,SAASiW,kBAAkBA,CAACtU,OAAO,EAAE;EACnC,IAAIuU,WAAW,GAAGC,aAAa,CAACxU,OAAO,CAAC;EACxC,OAAOiT,aAAa,CAACsB,WAAW,CAAC,IAAI,CAACE,qBAAqB,CAACF,WAAW,CAAC,EAAE;IACxE,IAAIZ,iBAAiB,CAACY,WAAW,CAAC,EAAE;MAClC,OAAOA,WAAW;IACpB,CAAC,MAAM;MACLA,WAAW,GAAGC,aAAa,CAACD,WAAW,CAAC;IAC1C;EACF;EACA,OAAO,IAAI;AACb;AACA,SAASV,QAAQA,CAAA,EAAG;EAClB,IAAI,OAAOa,GAAG,KAAK,WAAW,IAAI,CAACA,GAAG,CAACC,QAAQ,EAAE,OAAO,KAAK;EAC7D,OAAOD,GAAG,CAACC,QAAQ,CAAC,yBAAyB,EAAE,MAAM,CAAC;AACxD;AACA,SAASF,qBAAqBA,CAACnC,IAAI,EAAE;EACnC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC/J,QAAQ,CAAC8J,WAAW,CAACC,IAAI,CAAC,CAAC;AAClE;AACA,SAASkB,gBAAgBA,CAACxT,OAAO,EAAE;EACjC,OAAO0S,SAAS,CAAC1S,OAAO,CAAC,CAACwT,gBAAgB,CAACxT,OAAO,CAAC;AACrD;AACA,SAAS4U,aAAaA,CAAC5U,OAAO,EAAE;EAC9B,IAAIyN,SAAS,CAACzN,OAAO,CAAC,EAAE;IACtB,OAAO;MACL6U,UAAU,EAAE7U,OAAO,CAAC6U,UAAU;MAC9BC,SAAS,EAAE9U,OAAO,CAAC8U;IACrB,CAAC;EACH;EACA,OAAO;IACLD,UAAU,EAAE7U,OAAO,CAAC+U,WAAW;IAC/BD,SAAS,EAAE9U,OAAO,CAACgV;EACrB,CAAC;AACH;AACA,SAASR,aAAaA,CAAClC,IAAI,EAAE;EAC3B,IAAID,WAAW,CAACC,IAAI,CAAC,KAAK,MAAM,EAAE;IAChC,OAAOA,IAAI;EACb;EACA,MAAM2C,MAAM;EACZ;EACA3C,IAAI,CAAC4C,YAAY;EACjB;EACA5C,IAAI,CAAC6C,UAAU;EACf;EACAjC,YAAY,CAACZ,IAAI,CAAC,IAAIA,IAAI,CAAC8C,IAAI;EAC/B;EACAzH,kBAAkB,CAAC2E,IAAI,CAAC;EACxB,OAAOY,YAAY,CAAC+B,MAAM,CAAC,GAAGA,MAAM,CAACG,IAAI,GAAGH,MAAM;AACpD;AACA,SAASI,0BAA0BA,CAAC/C,IAAI,EAAE;EACxC,MAAM6C,UAAU,GAAGX,aAAa,CAAClC,IAAI,CAAC;EACtC,IAAImC,qBAAqB,CAACU,UAAU,CAAC,EAAE;IACrC,OAAO7C,IAAI,CAACM,aAAa,GAAGN,IAAI,CAACM,aAAa,CAACzN,IAAI,GAAGmN,IAAI,CAACnN,IAAI;EACjE;EACA,IAAI8N,aAAa,CAACkC,UAAU,CAAC,IAAI/B,iBAAiB,CAAC+B,UAAU,CAAC,EAAE;IAC9D,OAAOA,UAAU;EACnB;EACA,OAAOE,0BAA0B,CAACF,UAAU,CAAC;AAC/C;AACA,SAASG,oBAAoBA,CAAChD,IAAI,EAAEvI,IAAI,EAAE;EACxC,IAAIwL,oBAAoB;EACxB,IAAIxL,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,EAAE;EACX;EACA,MAAMyL,kBAAkB,GAAGH,0BAA0B,CAAC/C,IAAI,CAAC;EAC3D,MAAMmD,MAAM,GAAGD,kBAAkB,MAAM,CAACD,oBAAoB,GAAGjD,IAAI,CAACM,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2C,oBAAoB,CAACpQ,IAAI,CAAC;EAChI,MAAMuQ,GAAG,GAAGhD,SAAS,CAAC8C,kBAAkB,CAAC;EACzC,IAAIC,MAAM,EAAE;IACV,OAAO1L,IAAI,CAACjK,MAAM,CAAC4V,GAAG,EAAEA,GAAG,CAACC,cAAc,IAAI,EAAE,EAAEvC,iBAAiB,CAACoC,kBAAkB,CAAC,GAAGA,kBAAkB,GAAG,EAAE,CAAC;EACpH;EACA,OAAOzL,IAAI,CAACjK,MAAM,CAAC0V,kBAAkB,EAAEF,oBAAoB,CAACE,kBAAkB,CAAC,CAAC;AAClF;AAEA,SAASI,gBAAgBA,CAAC5V,OAAO,EAAE;EACjC,MAAM8T,GAAG,GAAGN,gBAAgB,CAACxT,OAAO,CAAC;EACrC;EACA;EACA,IAAIqK,KAAK,GAAGwL,UAAU,CAAC/B,GAAG,CAACzJ,KAAK,CAAC,IAAI,CAAC;EACtC,IAAIC,MAAM,GAAGuL,UAAU,CAAC/B,GAAG,CAACxJ,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMwL,SAAS,GAAG7C,aAAa,CAACjT,OAAO,CAAC;EACxC,MAAM+V,WAAW,GAAGD,SAAS,GAAG9V,OAAO,CAAC+V,WAAW,GAAG1L,KAAK;EAC3D,MAAM2L,YAAY,GAAGF,SAAS,GAAG9V,OAAO,CAACgW,YAAY,GAAG1L,MAAM;EAC9D,MAAM2L,cAAc,GAAGlP,KAAK,CAACsD,KAAK,CAAC,KAAK0L,WAAW,IAAIhP,KAAK,CAACuD,MAAM,CAAC,KAAK0L,YAAY;EACrF,IAAIC,cAAc,EAAE;IAClB5L,KAAK,GAAG0L,WAAW;IACnBzL,MAAM,GAAG0L,YAAY;EACvB;EACA,OAAO;IACL3L,KAAK;IACLC,MAAM;IACN4L,CAAC,EAAED;EACL,CAAC;AACH;AACA,SAASE,aAAaA,CAACnW,OAAO,EAAE;EAC9B,OAAO,CAACyN,SAAS,CAACzN,OAAO,CAAC,GAAGA,OAAO,CAAC0N,cAAc,GAAG1N,OAAO;AAC/D;AACA,SAAS+N,QAAQA,CAAC/N,OAAO,EAAE;EACzB,MAAMoW,UAAU,GAAGD,aAAa,CAACnW,OAAO,CAAC;EACzC,IAAI,CAACiT,aAAa,CAACmD,UAAU,CAAC,EAAE;IAC9B,OAAOpP,YAAY,CAAC,CAAC,CAAC;EACxB;EACA,MAAMoD,IAAI,GAAGgM,UAAU,CAACC,qBAAqB,CAAC,CAAC;EAC/C,MAAM;IACJhM,KAAK;IACLC,MAAM;IACN4L;EACF,CAAC,GAAGN,gBAAgB,CAACQ,UAAU,CAAC;EAChC,IAAIlP,CAAC,GAAG,CAACgP,CAAC,GAAGnP,KAAK,CAACqD,IAAI,CAACC,KAAK,CAAC,GAAGD,IAAI,CAACC,KAAK,IAAIA,KAAK;EACpD,IAAIlD,CAAC,GAAG,CAAC+O,CAAC,GAAGnP,KAAK,CAACqD,IAAI,CAACE,MAAM,CAAC,GAAGF,IAAI,CAACE,MAAM,IAAIA,MAAM;;EAEvD;;EAEA,IAAI,CAACpD,CAAC,IAAI,CAACoP,MAAM,CAACC,QAAQ,CAACrP,CAAC,CAAC,EAAE;IAC7BA,CAAC,GAAG,CAAC;EACP;EACA,IAAI,CAACC,CAAC,IAAI,CAACmP,MAAM,CAACC,QAAQ,CAACpP,CAAC,CAAC,EAAE;IAC7BA,CAAC,GAAG,CAAC;EACP;EACA,OAAO;IACLD,CAAC;IACDC;EACF,CAAC;AACH;AACA,MAAMqP,SAAS,GAAG,aAAaxP,YAAY,CAAC,CAAC,CAAC;AAC9C,SAASyP,gBAAgBA,CAACzW,OAAO,EAAE;EACjC,MAAM0V,GAAG,GAAGhD,SAAS,CAAC1S,OAAO,CAAC;EAC9B,IAAI,CAAC6T,QAAQ,CAAC,CAAC,IAAI,CAAC6B,GAAG,CAACC,cAAc,EAAE;IACtC,OAAOa,SAAS;EAClB;EACA,OAAO;IACLtP,CAAC,EAAEwO,GAAG,CAACC,cAAc,CAACe,UAAU;IAChCvP,CAAC,EAAEuO,GAAG,CAACC,cAAc,CAACgB;EACxB,CAAC;AACH;AACA,SAASC,sBAAsBA,CAAC5W,OAAO,EAAE6W,OAAO,EAAEC,oBAAoB,EAAE;EACtE,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,KAAK;EACjB;EACA,IAAI,CAACC,oBAAoB,IAAID,OAAO,IAAIC,oBAAoB,KAAKpE,SAAS,CAAC1S,OAAO,CAAC,EAAE;IACnF,OAAO,KAAK;EACd;EACA,OAAO6W,OAAO;AAChB;AACA,SAASR,qBAAqBA,CAACrW,OAAO,EAAE+W,YAAY,EAAEC,eAAe,EAAEpJ,YAAY,EAAE;EACnF,IAAImJ,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,KAAK;EACtB;EACA,IAAIC,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAG,KAAK;EACzB;EACA,MAAMC,UAAU,GAAGjX,OAAO,CAACqW,qBAAqB,CAAC,CAAC;EAClD,MAAMD,UAAU,GAAGD,aAAa,CAACnW,OAAO,CAAC;EACzC,IAAIkX,KAAK,GAAGlQ,YAAY,CAAC,CAAC,CAAC;EAC3B,IAAI+P,YAAY,EAAE;IAChB,IAAInJ,YAAY,EAAE;MAChB,IAAIH,SAAS,CAACG,YAAY,CAAC,EAAE;QAC3BsJ,KAAK,GAAGnJ,QAAQ,CAACH,YAAY,CAAC;MAChC;IACF,CAAC,MAAM;MACLsJ,KAAK,GAAGnJ,QAAQ,CAAC/N,OAAO,CAAC;IAC3B;EACF;EACA,MAAMmX,aAAa,GAAGP,sBAAsB,CAACR,UAAU,EAAEY,eAAe,EAAEpJ,YAAY,CAAC,GAAG6I,gBAAgB,CAACL,UAAU,CAAC,GAAGpP,YAAY,CAAC,CAAC,CAAC;EACxI,IAAIE,CAAC,GAAG,CAAC+P,UAAU,CAAC5P,IAAI,GAAG8P,aAAa,CAACjQ,CAAC,IAAIgQ,KAAK,CAAChQ,CAAC;EACrD,IAAIC,CAAC,GAAG,CAAC8P,UAAU,CAACzP,GAAG,GAAG2P,aAAa,CAAChQ,CAAC,IAAI+P,KAAK,CAAC/P,CAAC;EACpD,IAAIkD,KAAK,GAAG4M,UAAU,CAAC5M,KAAK,GAAG6M,KAAK,CAAChQ,CAAC;EACtC,IAAIoD,MAAM,GAAG2M,UAAU,CAAC3M,MAAM,GAAG4M,KAAK,CAAC/P,CAAC;EACxC,IAAIiP,UAAU,EAAE;IACd,MAAMV,GAAG,GAAGhD,SAAS,CAAC0D,UAAU,CAAC;IACjC,MAAMgB,SAAS,GAAGxJ,YAAY,IAAIH,SAAS,CAACG,YAAY,CAAC,GAAG8E,SAAS,CAAC9E,YAAY,CAAC,GAAGA,YAAY;IAClG,IAAIyJ,aAAa,GAAG3B,GAAG,CAAC4B,YAAY;IACpC,OAAOD,aAAa,IAAIzJ,YAAY,IAAIwJ,SAAS,KAAK1B,GAAG,EAAE;MACzD,MAAM6B,WAAW,GAAGxJ,QAAQ,CAACsJ,aAAa,CAAC;MAC3C,MAAMG,UAAU,GAAGH,aAAa,CAAChB,qBAAqB,CAAC,CAAC;MACxD,MAAMvC,GAAG,GAAGN,gBAAgB,CAAC6D,aAAa,CAAC;MAC3C,MAAMhQ,IAAI,GAAGmQ,UAAU,CAACnQ,IAAI,GAAG,CAACgQ,aAAa,CAACI,UAAU,GAAG5B,UAAU,CAAC/B,GAAG,CAAC4D,WAAW,CAAC,IAAIH,WAAW,CAACrQ,CAAC;MACvG,MAAMM,GAAG,GAAGgQ,UAAU,CAAChQ,GAAG,GAAG,CAAC6P,aAAa,CAACM,SAAS,GAAG9B,UAAU,CAAC/B,GAAG,CAAC8D,UAAU,CAAC,IAAIL,WAAW,CAACpQ,CAAC;MACnGD,CAAC,IAAIqQ,WAAW,CAACrQ,CAAC;MAClBC,CAAC,IAAIoQ,WAAW,CAACpQ,CAAC;MAClBkD,KAAK,IAAIkN,WAAW,CAACrQ,CAAC;MACtBoD,MAAM,IAAIiN,WAAW,CAACpQ,CAAC;MACvBD,CAAC,IAAIG,IAAI;MACTF,CAAC,IAAIK,GAAG;MACR6P,aAAa,GAAG3E,SAAS,CAAC2E,aAAa,CAAC,CAACC,YAAY;IACvD;EACF;EACA,OAAOnN,gBAAgB,CAAC;IACtBE,KAAK;IACLC,MAAM;IACNpD,CAAC;IACDC;EACF,CAAC,CAAC;AACJ;AACA,SAAS8G,qDAAqDA,CAACvD,IAAI,EAAE;EACnE,IAAI;IACFN,IAAI;IACJwD,YAAY;IACZtC;EACF,CAAC,GAAGZ,IAAI;EACR,MAAMmN,uBAAuB,GAAG5E,aAAa,CAACrF,YAAY,CAAC;EAC3D,MAAMmF,eAAe,GAAGpF,kBAAkB,CAACC,YAAY,CAAC;EACxD,IAAIA,YAAY,KAAKmF,eAAe,EAAE;IACpC,OAAO3I,IAAI;EACb;EACA,IAAI0N,MAAM,GAAG;IACXjD,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE;EACb,CAAC;EACD,IAAIoC,KAAK,GAAGlQ,YAAY,CAAC,CAAC,CAAC;EAC3B,MAAM+Q,OAAO,GAAG/Q,YAAY,CAAC,CAAC,CAAC;EAC/B,IAAI6Q,uBAAuB,IAAI,CAACA,uBAAuB,IAAIvM,QAAQ,KAAK,OAAO,EAAE;IAC/E,IAAI+G,WAAW,CAACzE,YAAY,CAAC,KAAK,MAAM,IAAIwF,iBAAiB,CAACL,eAAe,CAAC,EAAE;MAC9E+E,MAAM,GAAGlD,aAAa,CAAChH,YAAY,CAAC;IACtC;IACA,IAAIqF,aAAa,CAACrF,YAAY,CAAC,EAAE;MAC/B,MAAMoK,UAAU,GAAG3B,qBAAqB,CAACzI,YAAY,CAAC;MACtDsJ,KAAK,GAAGnJ,QAAQ,CAACH,YAAY,CAAC;MAC9BmK,OAAO,CAAC7Q,CAAC,GAAG8Q,UAAU,CAAC9Q,CAAC,GAAG0G,YAAY,CAAC6J,UAAU;MAClDM,OAAO,CAAC5Q,CAAC,GAAG6Q,UAAU,CAAC7Q,CAAC,GAAGyG,YAAY,CAAC+J,SAAS;IACnD;EACF;EACA,OAAO;IACLtN,KAAK,EAAED,IAAI,CAACC,KAAK,GAAG6M,KAAK,CAAChQ,CAAC;IAC3BoD,MAAM,EAAEF,IAAI,CAACE,MAAM,GAAG4M,KAAK,CAAC/P,CAAC;IAC7BD,CAAC,EAAEkD,IAAI,CAAClD,CAAC,GAAGgQ,KAAK,CAAChQ,CAAC,GAAG4Q,MAAM,CAACjD,UAAU,GAAGqC,KAAK,CAAChQ,CAAC,GAAG6Q,OAAO,CAAC7Q,CAAC;IAC7DC,CAAC,EAAEiD,IAAI,CAACjD,CAAC,GAAG+P,KAAK,CAAC/P,CAAC,GAAG2Q,MAAM,CAAChD,SAAS,GAAGoC,KAAK,CAAC/P,CAAC,GAAG4Q,OAAO,CAAC5Q;EAC7D,CAAC;AACH;AACA,SAAS8Q,cAAcA,CAACjY,OAAO,EAAE;EAC/B,OAAOX,KAAK,CAAC6Y,IAAI,CAAClY,OAAO,CAACiY,cAAc,CAAC,CAAC,CAAC;AAC7C;AACA,SAASE,mBAAmBA,CAACnY,OAAO,EAAE;EACpC;EACA;EACA,OAAOqW,qBAAqB,CAAC1I,kBAAkB,CAAC3N,OAAO,CAAC,CAAC,CAACqH,IAAI,GAAGuN,aAAa,CAAC5U,OAAO,CAAC,CAAC6U,UAAU;AACpG;;AAEA;AACA;AACA,SAASuD,eAAeA,CAACpY,OAAO,EAAE;EAChC,MAAMqY,IAAI,GAAG1K,kBAAkB,CAAC3N,OAAO,CAAC;EACxC,MAAM8X,MAAM,GAAGlD,aAAa,CAAC5U,OAAO,CAAC;EACrC,MAAMmF,IAAI,GAAGnF,OAAO,CAAC4S,aAAa,CAACzN,IAAI;EACvC,MAAMkF,KAAK,GAAGvD,GAAG,CAACuR,IAAI,CAACC,WAAW,EAAED,IAAI,CAACE,WAAW,EAAEpT,IAAI,CAACmT,WAAW,EAAEnT,IAAI,CAACoT,WAAW,CAAC;EACzF,MAAMjO,MAAM,GAAGxD,GAAG,CAACuR,IAAI,CAACG,YAAY,EAAEH,IAAI,CAACI,YAAY,EAAEtT,IAAI,CAACqT,YAAY,EAAErT,IAAI,CAACsT,YAAY,CAAC;EAC9F,IAAIvR,CAAC,GAAG,CAAC4Q,MAAM,CAACjD,UAAU,GAAGsD,mBAAmB,CAACnY,OAAO,CAAC;EACzD,MAAMmH,CAAC,GAAG,CAAC2Q,MAAM,CAAChD,SAAS;EAC3B,IAAItB,gBAAgB,CAACrO,IAAI,CAAC,CAAC2E,SAAS,KAAK,KAAK,EAAE;IAC9C5C,CAAC,IAAIJ,GAAG,CAACuR,IAAI,CAACE,WAAW,EAAEpT,IAAI,CAACoT,WAAW,CAAC,GAAGlO,KAAK;EACtD;EACA,OAAO;IACLA,KAAK;IACLC,MAAM;IACNpD,CAAC;IACDC;EACF,CAAC;AACH;AACA,SAASuR,eAAeA,CAAC1Y,OAAO,EAAEsL,QAAQ,EAAE;EAC1C,MAAMoK,GAAG,GAAGhD,SAAS,CAAC1S,OAAO,CAAC;EAC9B,MAAMqY,IAAI,GAAG1K,kBAAkB,CAAC3N,OAAO,CAAC;EACxC,MAAM2V,cAAc,GAAGD,GAAG,CAACC,cAAc;EACzC,IAAItL,KAAK,GAAGgO,IAAI,CAACE,WAAW;EAC5B,IAAIjO,MAAM,GAAG+N,IAAI,CAACI,YAAY;EAC9B,IAAIvR,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIwO,cAAc,EAAE;IAClBtL,KAAK,GAAGsL,cAAc,CAACtL,KAAK;IAC5BC,MAAM,GAAGqL,cAAc,CAACrL,MAAM;IAC9B,MAAMqO,mBAAmB,GAAG9E,QAAQ,CAAC,CAAC;IACtC,IAAI,CAAC8E,mBAAmB,IAAIA,mBAAmB,IAAIrN,QAAQ,KAAK,OAAO,EAAE;MACvEpE,CAAC,GAAGyO,cAAc,CAACe,UAAU;MAC7BvP,CAAC,GAAGwO,cAAc,CAACgB,SAAS;IAC9B;EACF;EACA,OAAO;IACLtM,KAAK;IACLC,MAAM;IACNpD,CAAC;IACDC;EACF,CAAC;AACH;;AAEA;AACA,SAASyR,0BAA0BA,CAAC5Y,OAAO,EAAEsL,QAAQ,EAAE;EACrD,MAAM2L,UAAU,GAAGZ,qBAAqB,CAACrW,OAAO,EAAE,IAAI,EAAEsL,QAAQ,KAAK,OAAO,CAAC;EAC7E,MAAM9D,GAAG,GAAGyP,UAAU,CAACzP,GAAG,GAAGxH,OAAO,CAAC2X,SAAS;EAC9C,MAAMtQ,IAAI,GAAG4P,UAAU,CAAC5P,IAAI,GAAGrH,OAAO,CAACyX,UAAU;EACjD,MAAMP,KAAK,GAAGjE,aAAa,CAACjT,OAAO,CAAC,GAAG+N,QAAQ,CAAC/N,OAAO,CAAC,GAAGgH,YAAY,CAAC,CAAC,CAAC;EAC1E,MAAMqD,KAAK,GAAGrK,OAAO,CAACuY,WAAW,GAAGrB,KAAK,CAAChQ,CAAC;EAC3C,MAAMoD,MAAM,GAAGtK,OAAO,CAACyY,YAAY,GAAGvB,KAAK,CAAC/P,CAAC;EAC7C,MAAMD,CAAC,GAAGG,IAAI,GAAG6P,KAAK,CAAChQ,CAAC;EACxB,MAAMC,CAAC,GAAGK,GAAG,GAAG0P,KAAK,CAAC/P,CAAC;EACvB,OAAO;IACLkD,KAAK;IACLC,MAAM;IACNpD,CAAC;IACDC;EACF,CAAC;AACH;AACA,SAAS0R,iCAAiCA,CAAC7Y,OAAO,EAAE8Y,gBAAgB,EAAExN,QAAQ,EAAE;EAC9E,IAAIlB,IAAI;EACR,IAAI0O,gBAAgB,KAAK,UAAU,EAAE;IACnC1O,IAAI,GAAGsO,eAAe,CAAC1Y,OAAO,EAAEsL,QAAQ,CAAC;EAC3C,CAAC,MAAM,IAAIwN,gBAAgB,KAAK,UAAU,EAAE;IAC1C1O,IAAI,GAAGgO,eAAe,CAACzK,kBAAkB,CAAC3N,OAAO,CAAC,CAAC;EACrD,CAAC,MAAM,IAAIyN,SAAS,CAACqL,gBAAgB,CAAC,EAAE;IACtC1O,IAAI,GAAGwO,0BAA0B,CAACE,gBAAgB,EAAExN,QAAQ,CAAC;EAC/D,CAAC,MAAM;IACL,MAAM6L,aAAa,GAAGV,gBAAgB,CAACzW,OAAO,CAAC;IAC/CoK,IAAI,GAAG7D,QAAQ,CAAC,CAAC,CAAC,EAAEuS,gBAAgB,EAAE;MACpC5R,CAAC,EAAE4R,gBAAgB,CAAC5R,CAAC,GAAGiQ,aAAa,CAACjQ,CAAC;MACvCC,CAAC,EAAE2R,gBAAgB,CAAC3R,CAAC,GAAGgQ,aAAa,CAAChQ;IACxC,CAAC,CAAC;EACJ;EACA,OAAOgD,gBAAgB,CAACC,IAAI,CAAC;AAC/B;AACA,SAAS2O,wBAAwBA,CAAC/Y,OAAO,EAAEgZ,QAAQ,EAAE;EACnD,MAAM7D,UAAU,GAAGX,aAAa,CAACxU,OAAO,CAAC;EACzC,IAAImV,UAAU,KAAK6D,QAAQ,IAAI,CAACvL,SAAS,CAAC0H,UAAU,CAAC,IAAIV,qBAAqB,CAACU,UAAU,CAAC,EAAE;IAC1F,OAAO,KAAK;EACd;EACA,OAAO3B,gBAAgB,CAAC2B,UAAU,CAAC,CAAC8D,QAAQ,KAAK,OAAO,IAAIF,wBAAwB,CAAC5D,UAAU,EAAE6D,QAAQ,CAAC;AAC5G;;AAEA;AACA;AACA;AACA,SAASE,2BAA2BA,CAAClZ,OAAO,EAAEmZ,KAAK,EAAE;EACnD,MAAMC,YAAY,GAAGD,KAAK,CAACE,GAAG,CAACrZ,OAAO,CAAC;EACvC,IAAIoZ,YAAY,EAAE;IAChB,OAAOA,YAAY;EACrB;EACA,IAAInE,MAAM,GAAGK,oBAAoB,CAACtV,OAAO,CAAC,CAACM,MAAM,CAAC+D,EAAE,IAAIoJ,SAAS,CAACpJ,EAAE,CAAC,IAAIgO,WAAW,CAAChO,EAAE,CAAC,KAAK,MAAM,CAAC;EACpG,IAAIiV,mCAAmC,GAAG,IAAI;EAC9C,MAAMC,cAAc,GAAG/F,gBAAgB,CAACxT,OAAO,CAAC,CAACiZ,QAAQ,KAAK,OAAO;EACrE,IAAI1E,WAAW,GAAGgF,cAAc,GAAG/E,aAAa,CAACxU,OAAO,CAAC,GAAGA,OAAO;;EAEnE;EACA,OAAOyN,SAAS,CAAC8G,WAAW,CAAC,IAAI,CAACE,qBAAqB,CAACF,WAAW,CAAC,EAAE;IACpE,MAAMiF,aAAa,GAAGhG,gBAAgB,CAACe,WAAW,CAAC;IACnD,MAAMkF,uBAAuB,GAAG9F,iBAAiB,CAACY,WAAW,CAAC;IAC9D,IAAI,CAACkF,uBAAuB,IAAID,aAAa,CAACP,QAAQ,KAAK,OAAO,EAAE;MAClEK,mCAAmC,GAAG,IAAI;IAC5C;IACA,MAAMI,qBAAqB,GAAGH,cAAc,GAAG,CAACE,uBAAuB,IAAI,CAACH,mCAAmC,GAAG,CAACG,uBAAuB,IAAID,aAAa,CAACP,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAACK,mCAAmC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC/Q,QAAQ,CAAC+Q,mCAAmC,CAACL,QAAQ,CAAC,IAAI7F,iBAAiB,CAACmB,WAAW,CAAC,IAAI,CAACkF,uBAAuB,IAAIV,wBAAwB,CAAC/Y,OAAO,EAAEuU,WAAW,CAAC;IAC1Z,IAAImF,qBAAqB,EAAE;MACzB;MACAzE,MAAM,GAAGA,MAAM,CAAC3U,MAAM,CAACqZ,QAAQ,IAAIA,QAAQ,KAAKpF,WAAW,CAAC;IAC9D,CAAC,MAAM;MACL;MACA+E,mCAAmC,GAAGE,aAAa;IACrD;IACAjF,WAAW,GAAGC,aAAa,CAACD,WAAW,CAAC;EAC1C;EACA4E,KAAK,CAACS,GAAG,CAAC5Z,OAAO,EAAEiV,MAAM,CAAC;EAC1B,OAAOA,MAAM;AACf;;AAEA;AACA;AACA,SAASzH,eAAeA,CAAC9C,IAAI,EAAE;EAC7B,IAAI;IACF1K,OAAO;IACPiN,QAAQ;IACRC,YAAY;IACZ5B;EACF,CAAC,GAAGZ,IAAI;EACR,MAAMmP,wBAAwB,GAAG5M,QAAQ,KAAK,mBAAmB,GAAGiM,2BAA2B,CAAClZ,OAAO,EAAE,IAAI,CAAC8Z,EAAE,CAAC,GAAG,EAAE,CAACha,MAAM,CAACmN,QAAQ,CAAC;EACvI,MAAM8M,iBAAiB,GAAG,CAAC,GAAGF,wBAAwB,EAAE3M,YAAY,CAAC;EACrE,MAAM8M,qBAAqB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;EAClD,MAAME,YAAY,GAAGF,iBAAiB,CAACnY,MAAM,CAAC,CAACsY,OAAO,EAAEpB,gBAAgB,KAAK;IAC3E,MAAM1O,IAAI,GAAGyO,iCAAiC,CAAC7Y,OAAO,EAAE8Y,gBAAgB,EAAExN,QAAQ,CAAC;IACnF4O,OAAO,CAAC1S,GAAG,GAAGV,GAAG,CAACsD,IAAI,CAAC5C,GAAG,EAAE0S,OAAO,CAAC1S,GAAG,CAAC;IACxC0S,OAAO,CAAC5S,KAAK,GAAGT,GAAG,CAACuD,IAAI,CAAC9C,KAAK,EAAE4S,OAAO,CAAC5S,KAAK,CAAC;IAC9C4S,OAAO,CAAC3S,MAAM,GAAGV,GAAG,CAACuD,IAAI,CAAC7C,MAAM,EAAE2S,OAAO,CAAC3S,MAAM,CAAC;IACjD2S,OAAO,CAAC7S,IAAI,GAAGP,GAAG,CAACsD,IAAI,CAAC/C,IAAI,EAAE6S,OAAO,CAAC7S,IAAI,CAAC;IAC3C,OAAO6S,OAAO;EAChB,CAAC,EAAErB,iCAAiC,CAAC7Y,OAAO,EAAEga,qBAAqB,EAAE1O,QAAQ,CAAC,CAAC;EAC/E,OAAO;IACLjB,KAAK,EAAE4P,YAAY,CAAC3S,KAAK,GAAG2S,YAAY,CAAC5S,IAAI;IAC7CiD,MAAM,EAAE2P,YAAY,CAAC1S,MAAM,GAAG0S,YAAY,CAACzS,GAAG;IAC9CN,CAAC,EAAE+S,YAAY,CAAC5S,IAAI;IACpBF,CAAC,EAAE8S,YAAY,CAACzS;EAClB,CAAC;AACH;AACA,SAAS4G,aAAaA,CAACpO,OAAO,EAAE;EAC9B,OAAO4V,gBAAgB,CAAC5V,OAAO,CAAC;AAClC;AACA,SAASma,6BAA6BA,CAACna,OAAO,EAAE4N,YAAY,EAAEtC,QAAQ,EAAE;EACtE,MAAMuM,uBAAuB,GAAG5E,aAAa,CAACrF,YAAY,CAAC;EAC3D,MAAMmF,eAAe,GAAGpF,kBAAkB,CAACC,YAAY,CAAC;EACxD,MAAMiJ,OAAO,GAAGvL,QAAQ,KAAK,OAAO;EACpC,MAAMlB,IAAI,GAAGiM,qBAAqB,CAACrW,OAAO,EAAE,IAAI,EAAE6W,OAAO,EAAEjJ,YAAY,CAAC;EACxE,IAAIkK,MAAM,GAAG;IACXjD,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE;EACb,CAAC;EACD,MAAMiD,OAAO,GAAG/Q,YAAY,CAAC,CAAC,CAAC;EAC/B,IAAI6Q,uBAAuB,IAAI,CAACA,uBAAuB,IAAI,CAAChB,OAAO,EAAE;IACnE,IAAIxE,WAAW,CAACzE,YAAY,CAAC,KAAK,MAAM,IAAIwF,iBAAiB,CAACL,eAAe,CAAC,EAAE;MAC9E+E,MAAM,GAAGlD,aAAa,CAAChH,YAAY,CAAC;IACtC;IACA,IAAIiK,uBAAuB,EAAE;MAC3B,MAAMG,UAAU,GAAG3B,qBAAqB,CAACzI,YAAY,EAAE,IAAI,EAAEiJ,OAAO,EAAEjJ,YAAY,CAAC;MACnFmK,OAAO,CAAC7Q,CAAC,GAAG8Q,UAAU,CAAC9Q,CAAC,GAAG0G,YAAY,CAAC6J,UAAU;MAClDM,OAAO,CAAC5Q,CAAC,GAAG6Q,UAAU,CAAC7Q,CAAC,GAAGyG,YAAY,CAAC+J,SAAS;IACnD,CAAC,MAAM,IAAI5E,eAAe,EAAE;MAC1BgF,OAAO,CAAC7Q,CAAC,GAAGiR,mBAAmB,CAACpF,eAAe,CAAC;IAClD;EACF;EACA,OAAO;IACL7L,CAAC,EAAEkD,IAAI,CAAC/C,IAAI,GAAGyQ,MAAM,CAACjD,UAAU,GAAGkD,OAAO,CAAC7Q,CAAC;IAC5CC,CAAC,EAAEiD,IAAI,CAAC5C,GAAG,GAAGsQ,MAAM,CAAChD,SAAS,GAAGiD,OAAO,CAAC5Q,CAAC;IAC1CkD,KAAK,EAAED,IAAI,CAACC,KAAK;IACjBC,MAAM,EAAEF,IAAI,CAACE;EACf,CAAC;AACH;AACA,SAAS8P,mBAAmBA,CAACpa,OAAO,EAAEqa,QAAQ,EAAE;EAC9C,IAAI,CAACpH,aAAa,CAACjT,OAAO,CAAC,IAAIwT,gBAAgB,CAACxT,OAAO,CAAC,CAACiZ,QAAQ,KAAK,OAAO,EAAE;IAC7E,OAAO,IAAI;EACb;EACA,IAAIoB,QAAQ,EAAE;IACZ,OAAOA,QAAQ,CAACra,OAAO,CAAC;EAC1B;EACA,OAAOA,OAAO,CAAC4N,YAAY;AAC7B;;AAEA;AACA;AACA,SAASC,eAAeA,CAAC7N,OAAO,EAAEqa,QAAQ,EAAE;EAC1C,MAAMvH,MAAM,GAAGJ,SAAS,CAAC1S,OAAO,CAAC;EACjC,IAAI,CAACiT,aAAa,CAACjT,OAAO,CAAC,EAAE;IAC3B,OAAO8S,MAAM;EACf;EACA,IAAIlF,YAAY,GAAGwM,mBAAmB,CAACpa,OAAO,EAAEqa,QAAQ,CAAC;EACzD,OAAOzM,YAAY,IAAI8F,cAAc,CAAC9F,YAAY,CAAC,IAAI4F,gBAAgB,CAAC5F,YAAY,CAAC,CAACqL,QAAQ,KAAK,QAAQ,EAAE;IAC3GrL,YAAY,GAAGwM,mBAAmB,CAACxM,YAAY,EAAEyM,QAAQ,CAAC;EAC5D;EACA,IAAIzM,YAAY,KAAKyE,WAAW,CAACzE,YAAY,CAAC,KAAK,MAAM,IAAIyE,WAAW,CAACzE,YAAY,CAAC,KAAK,MAAM,IAAI4F,gBAAgB,CAAC5F,YAAY,CAAC,CAACqL,QAAQ,KAAK,QAAQ,IAAI,CAACtF,iBAAiB,CAAC/F,YAAY,CAAC,CAAC,EAAE;IAC9L,OAAOkF,MAAM;EACf;EACA,OAAOlF,YAAY,IAAI0G,kBAAkB,CAACtU,OAAO,CAAC,IAAI8S,MAAM;AAC9D;AACA,MAAMlH,eAAe;EAAA,IAAA0O,gBAAA,GAAAlP,iBAAA,CAAG,WAA+BV,IAAI,EAAE;IAC3D,IAAI;MACF3B,SAAS;MACTC,QAAQ;MACRsC;IACF,CAAC,GAAGZ,IAAI;IACR,MAAM6P,iBAAiB,GAAG,IAAI,CAAC1M,eAAe,IAAIA,eAAe;IACjE,MAAM2M,eAAe,GAAG,IAAI,CAACpM,aAAa;IAC1C,OAAO;MACLrF,SAAS,EAAEoR,6BAA6B,CAACpR,SAAS,QAAQwR,iBAAiB,CAACvR,QAAQ,CAAC,EAAEsC,QAAQ,CAAC;MAChGtC,QAAQ,EAAEzC,QAAQ,CAAC;QACjBW,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE;MACL,CAAC,QAAQqT,eAAe,CAACxR,QAAQ,CAAC;IACpC,CAAC;EACH,CAAC;EAAA,SAfsC4C,eAAeA,CAAA6O,GAAA;IAAA,OAAAH,gBAAA,CAAA9W,KAAA,OAAAgD,SAAA;EAAA;EAAA,OAAfoF,eAAe;AAAA,GAerD;AACD,SAASD,KAAKA,CAAC3L,OAAO,EAAE;EACtB,OAAOwT,gBAAgB,CAACxT,OAAO,CAAC,CAAC8J,SAAS,KAAK,KAAK;AACtD;AACA,MAAM0B,QAAQ,GAAG;EACfyC,qDAAqD;EACrDN,kBAAkB;EAClBH,eAAe;EACfK,eAAe;EACfjC,eAAe;EACfqM,cAAc;EACd7J,aAAa;EACbL,QAAQ;EACRN,SAAS;EACT9B;AACF,CAAC;;AAED;AACA,SAAS+O,WAAWA,CAAC1a,OAAO,EAAE2a,MAAM,EAAE;EACpC,IAAIC,EAAE,GAAG,IAAI;EACb,IAAIC,SAAS;EACb,MAAMC,IAAI,GAAGnN,kBAAkB,CAAC3N,OAAO,CAAC;EACxC,SAAS+a,OAAOA,CAAA,EAAG;IACjBC,YAAY,CAACH,SAAS,CAAC;IACvBD,EAAE,IAAIA,EAAE,CAACK,UAAU,CAAC,CAAC;IACrBL,EAAE,GAAG,IAAI;EACX;EACA,SAASM,OAAOA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAChC,IAAID,IAAI,KAAK,KAAK,CAAC,EAAE;MACnBA,IAAI,GAAG,KAAK;IACd;IACA,IAAIC,SAAS,KAAK,KAAK,CAAC,EAAE;MACxBA,SAAS,GAAG,CAAC;IACf;IACAL,OAAO,CAAC,CAAC;IACT,MAAM;MACJ1T,IAAI;MACJG,GAAG;MACH6C,KAAK;MACLC;IACF,CAAC,GAAGtK,OAAO,CAACqW,qBAAqB,CAAC,CAAC;IACnC,IAAI,CAAC8E,IAAI,EAAE;MACTR,MAAM,CAAC,CAAC;IACV;IACA,IAAI,CAACtQ,KAAK,IAAI,CAACC,MAAM,EAAE;MACrB;IACF;IACA,MAAM+Q,QAAQ,GAAG/U,KAAK,CAACkB,GAAG,CAAC;IAC3B,MAAM8T,UAAU,GAAGhV,KAAK,CAACwU,IAAI,CAACvC,WAAW,IAAIlR,IAAI,GAAGgD,KAAK,CAAC,CAAC;IAC3D,MAAMkR,WAAW,GAAGjV,KAAK,CAACwU,IAAI,CAACrC,YAAY,IAAIjR,GAAG,GAAG8C,MAAM,CAAC,CAAC;IAC7D,MAAMkR,SAAS,GAAGlV,KAAK,CAACe,IAAI,CAAC;IAC7B,MAAMoU,UAAU,GAAG,CAACJ,QAAQ,GAAG,KAAK,GAAG,CAACC,UAAU,GAAG,KAAK,GAAG,CAACC,WAAW,GAAG,KAAK,GAAG,CAACC,SAAS,GAAG,IAAI;IACrG,MAAMhc,OAAO,GAAG;MACdic,UAAU;MACVL,SAAS,EAAEtU,GAAG,CAAC,CAAC,EAAED,GAAG,CAAC,CAAC,EAAEuU,SAAS,CAAC,CAAC,IAAI;IAC1C,CAAC;IACD,IAAIM,aAAa,GAAG,IAAI;IACxB,SAASC,aAAaA,CAACC,OAAO,EAAE;MAC9B,MAAMC,KAAK,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACE,iBAAiB;MAC1C,IAAID,KAAK,KAAKT,SAAS,EAAE;QACvB,IAAI,CAACM,aAAa,EAAE;UAClB,OAAOR,OAAO,CAAC,CAAC;QAClB;QACA,IAAI,CAACW,KAAK,EAAE;UACVhB,SAAS,GAAGkB,UAAU,CAAC,MAAM;YAC3Bb,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;UACtB,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACLA,OAAO,CAAC,KAAK,EAAEW,KAAK,CAAC;QACvB;MACF;MACAH,aAAa,GAAG,KAAK;IACvB;;IAEA;IACA;IACA,IAAI;MACFd,EAAE,GAAG,IAAIoB,oBAAoB,CAACL,aAAa,EAAEpV,QAAQ,CAAC,CAAC,CAAC,EAAE/G,OAAO,EAAE;QACjE;QACAsb,IAAI,EAAEA,IAAI,CAAClI;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAO9N,CAAC,EAAE;MACV8V,EAAE,GAAG,IAAIoB,oBAAoB,CAACL,aAAa,EAAEnc,OAAO,CAAC;IACvD;IACAob,EAAE,CAACqB,OAAO,CAACjc,OAAO,CAAC;EACrB;EACAkb,OAAO,CAAC,IAAI,CAAC;EACb,OAAOH,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmB,UAAUA,CAACnT,SAAS,EAAEC,QAAQ,EAAEmT,MAAM,EAAE3c,OAAO,EAAE;EACxD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,MAAM;IACJ4c,cAAc,GAAG,IAAI;IACrBC,cAAc,GAAG,IAAI;IACrBC,aAAa,GAAG,OAAOC,cAAc,KAAK,UAAU;IACpDC,WAAW,GAAG,OAAOR,oBAAoB,KAAK,UAAU;IACxDS,cAAc,GAAG;EACnB,CAAC,GAAGjd,OAAO;EACX,MAAMkd,WAAW,GAAGvG,aAAa,CAACpN,SAAS,CAAC;EAC5C,MAAM4T,SAAS,GAAGP,cAAc,IAAIC,cAAc,GAAG,CAAC,IAAIK,WAAW,GAAGpH,oBAAoB,CAACoH,WAAW,CAAC,GAAG,EAAE,CAAC,EAAE,GAAGpH,oBAAoB,CAACtM,QAAQ,CAAC,CAAC,GAAG,EAAE;EACxJ2T,SAAS,CAACxb,OAAO,CAACwY,QAAQ,IAAI;IAC5ByC,cAAc,IAAIzC,QAAQ,CAAC1U,gBAAgB,CAAC,QAAQ,EAAEkX,MAAM,EAAE;MAC5DS,OAAO,EAAE;IACX,CAAC,CAAC;IACFP,cAAc,IAAI1C,QAAQ,CAAC1U,gBAAgB,CAAC,QAAQ,EAAEkX,MAAM,CAAC;EAC/D,CAAC,CAAC;EACF,MAAMU,SAAS,GAAGH,WAAW,IAAIF,WAAW,GAAG9B,WAAW,CAACgC,WAAW,EAAEP,MAAM,CAAC,GAAG,IAAI;EACtF,IAAIW,cAAc,GAAG,CAAC,CAAC;EACvB,IAAIC,cAAc,GAAG,IAAI;EACzB,IAAIT,aAAa,EAAE;IACjBS,cAAc,GAAG,IAAIR,cAAc,CAAC7R,IAAI,IAAI;MAC1C,IAAI,CAACsS,UAAU,CAAC,GAAGtS,IAAI;MACvB,IAAIsS,UAAU,IAAIA,UAAU,CAACpd,MAAM,KAAK8c,WAAW,IAAIK,cAAc,EAAE;QACrE;QACA;QACAA,cAAc,CAACE,SAAS,CAACjU,QAAQ,CAAC;QAClCkU,oBAAoB,CAACJ,cAAc,CAAC;QACpCA,cAAc,GAAGK,qBAAqB,CAAC,MAAM;UAC3CJ,cAAc,IAAIA,cAAc,CAACd,OAAO,CAACjT,QAAQ,CAAC;QACpD,CAAC,CAAC;MACJ;MACAmT,MAAM,CAAC,CAAC;IACV,CAAC,CAAC;IACF,IAAIO,WAAW,IAAI,CAACD,cAAc,EAAE;MAClCM,cAAc,CAACd,OAAO,CAACS,WAAW,CAAC;IACrC;IACAK,cAAc,CAACd,OAAO,CAACjT,QAAQ,CAAC;EAClC;EACA,IAAIoU,OAAO;EACX,IAAIC,WAAW,GAAGZ,cAAc,GAAGpG,qBAAqB,CAACtN,SAAS,CAAC,GAAG,IAAI;EAC1E,IAAI0T,cAAc,EAAE;IAClBa,SAAS,CAAC,CAAC;EACb;EACA,SAASA,SAASA,CAAA,EAAG;IACnB,MAAMC,WAAW,GAAGlH,qBAAqB,CAACtN,SAAS,CAAC;IACpD,IAAIsU,WAAW,KAAKE,WAAW,CAACrW,CAAC,KAAKmW,WAAW,CAACnW,CAAC,IAAIqW,WAAW,CAACpW,CAAC,KAAKkW,WAAW,CAAClW,CAAC,IAAIoW,WAAW,CAAClT,KAAK,KAAKgT,WAAW,CAAChT,KAAK,IAAIkT,WAAW,CAACjT,MAAM,KAAK+S,WAAW,CAAC/S,MAAM,CAAC,EAAE;MAC/K6R,MAAM,CAAC,CAAC;IACV;IACAkB,WAAW,GAAGE,WAAW;IACzBH,OAAO,GAAGD,qBAAqB,CAACG,SAAS,CAAC;EAC5C;EACAnB,MAAM,CAAC,CAAC;EACR,OAAO,MAAM;IACXQ,SAAS,CAACxb,OAAO,CAACwY,QAAQ,IAAI;MAC5ByC,cAAc,IAAIzC,QAAQ,CAACzU,mBAAmB,CAAC,QAAQ,EAAEiX,MAAM,CAAC;MAChEE,cAAc,IAAI1C,QAAQ,CAACzU,mBAAmB,CAAC,QAAQ,EAAEiX,MAAM,CAAC;IAClE,CAAC,CAAC;IACFU,SAAS,IAAIA,SAAS,CAAC,CAAC;IACxBE,cAAc,IAAIA,cAAc,CAAC9B,UAAU,CAAC,CAAC;IAC7C8B,cAAc,GAAG,IAAI;IACrB,IAAIN,cAAc,EAAE;MAClBS,oBAAoB,CAACE,OAAO,CAAC;IAC/B;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMI,eAAe,GAAGA,CAACzU,SAAS,EAAEC,QAAQ,EAAExJ,OAAO,KAAK;EACxD;EACA;EACA;EACA,MAAM2Z,KAAK,GAAG,IAAIsE,GAAG,CAAC,CAAC;EACvB,MAAMC,aAAa,GAAGnX,QAAQ,CAAC;IAC7BiF;EACF,CAAC,EAAEhM,OAAO,CAAC;EACX,MAAMme,iBAAiB,GAAGpX,QAAQ,CAAC,CAAC,CAAC,EAAEmX,aAAa,CAAClS,QAAQ,EAAE;IAC7DsO,EAAE,EAAEX;EACN,CAAC,CAAC;EACF,OAAOjO,iBAAiB,CAACnC,SAAS,EAAEC,QAAQ,EAAEzC,QAAQ,CAAC,CAAC,CAAC,EAAEmX,aAAa,EAAE;IACxElS,QAAQ,EAAEmS;EACZ,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAC1Z,IAAI,EAAE;EAC1B,IAAIA,IAAI,CAAC6W,OAAO,EAAE;IAChB7W,IAAI,CAAC6W,OAAO,CAAC,CAAC;EAChB;EACA,MAAM8C,eAAe,GAAG3Z,IAAI,CAAC4Z,2BAA2B,CAAC,CAAC;EAC1D,IAAIle,MAAM,GAAGie,eAAe,CAAC7d,OAAO;EACpC,MAAM+d,iBAAiB,GAAGC,oBAAoB,CAACH,eAAe,EAAE3Z,IAAI,CAAC;EACrE,MAAM+Z,YAAY,GAAGtY,gBAAgB,CAACkY,eAAe,CAAC;EACtD,IAAII,YAAY,EAAE;IAChBre,MAAM,GAAGgF,QAAQ,CAACO,IAAI;IACtB,MAAM+Y,OAAO,GAAGha,IAAI,CAACia,wBAAwB,CAACC,UAAU,CAAC,CAAC;IAC1DF,OAAO,CAACG,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAC5C;EACApa,IAAI,CAAC6W,OAAO,GAAGmB,UAAU,CAACtc,MAAM,EAAEsE,IAAI,CAACG,EAAE,EAAE,MAAM;IAC/C;IACA,IAAI,CAACH,IAAI,CAACG,EAAE,EAAE;MACZH,IAAI,CAAC6W,OAAO,CAAC,CAAC;MACd;IACF;IACAwD,WAAW,CAAC3e,MAAM,EAAEsE,IAAI,EAAE6Z,iBAAiB,EAAEE,YAAY,CAAC;EAC5D,CAAC,CAAC;EACF/Z,IAAI,CAACtE,MAAM,GAAGie,eAAe,CAAC7d,OAAO;EACrC,OAAO+d,iBAAiB;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,kBAAkBA,CAACC,WAAW,EAAEjf,OAAO,EAAE;EAChD,OAAO;IACLue,iBAAiB,EAAE/b,GAAG,CAACyc,WAAW,CAACV,iBAAiB,IAAI,CAAC,CAAC,EAAEve,OAAO,CAACue,iBAAiB,IAAI,CAAC,CAAC;EAC7F,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASW,cAAcA,CAACxa,IAAI,EAAE;EAC5B,IAAIA,IAAI,CAAC6W,OAAO,EAAE;IAChB7W,IAAI,CAAC6W,OAAO,CAAC,CAAC;EAChB;EACA7W,IAAI,CAAC6W,OAAO,GAAG,IAAI;AACrB;;AAEA;AACA;AACA;AACA;AACA,SAASwD,WAAWA,CAAC3e,MAAM,EAAEsE,IAAI,EAAE6Z,iBAAiB,EAAEE,YAAY,EAAE;EAClE,OAAOT,eAAe,CAAC5d,MAAM,EAAEsE,IAAI,CAACG,EAAE,EAAE0Z,iBAAiB,CAAC,CAACY,IAAI,CAACC,kBAAkB,CAAC1a,IAAI,EAAE+Z,YAAY,CAAC;EACtG;EAAA,CACCU,IAAI,CAACza,IAAI,IAAI,IAAI2a,OAAO,CAACC,OAAO,IAAI;IACnC/C,UAAU,CAAC,MAAM+C,OAAO,CAAC5a,IAAI,CAAC,EAAE,GAAG,CAAC;EACtC,CAAC,CAAC;EACF;EAAA,CACCya,IAAI,CAACza,IAAI,IAAI;IACZ,IAAIA,IAAI,IAAIA,IAAI,CAACG,EAAE,EAAE;MACnBH,IAAI,CAACG,EAAE,CAAC0a,KAAK,CAAC;QACZC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,kBAAkBA,CAAC1a,IAAI,EAAE+Z,YAAY,EAAE;EAC9C,OAAO,CAAC;IACN/W,CAAC;IACDC,CAAC;IACDa,SAAS;IACT8D;EACF,CAAC,KAAK;IACJ,IAAI,CAAC5H,IAAI,CAACG,EAAE,EAAE;MACZ,OAAOH,IAAI;IACb;IACA,IAAI+Z,YAAY,EAAE;MAChBxf,MAAM,CAACiH,MAAM,CAACxB,IAAI,CAACG,EAAE,CAAC4a,KAAK,EAAE;QAC3BhG,QAAQ,EAAE,OAAO;QACjB5R,IAAI,EAAE,KAAK;QACXG,GAAG,EAAE,KAAK;QACVuM,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,MAAM;MACLtV,MAAM,CAACiH,MAAM,CAACxB,IAAI,CAACG,EAAE,CAAC4a,KAAK,EAAE;QAC3BhG,QAAQ,EAAE,UAAU;QACpB5R,IAAI,EAAE,GAAGH,CAAC,IAAI;QACdM,GAAG,EAAE,GAAGL,CAAC;MACX,CAAC,CAAC;IACJ;IACAjD,IAAI,CAACG,EAAE,CAAC6a,OAAO,CAACC,eAAe,GAAGnX,SAAS;IAC3CoX,UAAU,CAAClb,IAAI,CAACG,EAAE,EAAEyH,cAAc,CAAC;IACnC,OAAO5H,IAAI;EACb,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASkb,UAAUA,CAAC/a,EAAE,EAAEyH,cAAc,EAAE;EACtC,MAAMuT,OAAO,GAAGhb,EAAE,CAACQ,aAAa,CAAC,iBAAiB,CAAC;EACnD,IAAIwa,OAAO,IAAIvT,cAAc,CAACoC,KAAK,EAAE;IACnC,MAAM;MACJhH,CAAC,EAAEoY,MAAM;MACTnY,CAAC,EAAEoY;IACL,CAAC,GAAGzT,cAAc,CAACoC,KAAK;IACxBzP,MAAM,CAACiH,MAAM,CAAC2Z,OAAO,CAACJ,KAAK,EAAE;MAC3B5X,IAAI,EAAEiY,MAAM,IAAI,IAAI,GAAG,GAAGA,MAAM,IAAI,GAAG,EAAE;MACzC9X,GAAG,EAAE+X,MAAM,IAAI,IAAI,GAAG,GAAGA,MAAM,IAAI,GAAG;IACxC,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASvB,oBAAoBA,CAACH,eAAe,EAAE3Z,IAAI,EAAE;EACnD,MAAM1E,OAAO,GAAG;IACd8L,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE;EACd,CAAC;EACD,MAAM8T,OAAO,GAAGG,QAAQ,CAACtb,IAAI,CAAC;EAC9B,MAAM+Z,YAAY,GAAGtY,gBAAgB,CAACkY,eAAe,CAAC;EACtD,IAAI,CAACI,YAAY,EAAE;IACjBze,OAAO,CAAC+L,UAAU,CAACvI,IAAI,CAACuM,IAAI,CAAC,CAAC;IAC9B;IACA6B,KAAK,CAAC;MACJE,OAAO,EAAEM,UAAU,CAAC,CAAC;MACrBhC,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;IACH,IAAIyP,OAAO,EAAE;MACX7f,OAAO,CAAC+L,UAAU,CAACvI,IAAI,CAACkL,KAAK,CAAC;QAC5BlO,OAAO,EAAEqf;MACX,CAAC,CAAC,CAAC;IACL;IACA7f,OAAO,CAACwI,SAAS,GAAG6V,eAAe,CAACnb,EAAE;EACxC;EACA,OAAOV,GAAG,CAACkC,IAAI,CAAC1E,OAAO,CAACue,iBAAiB,IAAI,CAAC,CAAC,EAAEve,OAAO,CAAC;AAC3D;;AAEA;AACA;AACA;AACA;AACA,SAASggB,QAAQA,CAACtb,IAAI,EAAE;EACtB,IAAIA,IAAI,CAAC1E,OAAO,CAAC0O,KAAK,IAAIhK,IAAI,CAACG,EAAE,EAAE;IACjC,OAAOH,IAAI,CAACG,EAAE,CAACQ,aAAa,CAAC,iBAAiB,CAAC;EACjD;EACA,OAAO,KAAK;AACd;AAEA,SAAS4a,IAAIA,CAAA,EAAG,CAAC;AACjB,SAAS/Z,MAAMA,CAACga,GAAG,EAAEC,GAAG,EAAE;EACxB;EACA,KAAK,MAAMC,CAAC,IAAID,GAAG,EAAED,GAAG,CAACE,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC;EACpC,OAAOF,GAAG;AACZ;AACA,SAASG,GAAGA,CAAC5T,EAAE,EAAE;EACf,OAAOA,EAAE,CAAC,CAAC;AACb;AACA,SAAS6T,YAAYA,CAAA,EAAG;EACtB,OAAOrhB,MAAM,CAACshB,MAAM,CAAC,IAAI,CAAC;AAC5B;AACA,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpBA,GAAG,CAAC9e,OAAO,CAAC0e,GAAG,CAAC;AAClB;AACA,SAASK,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,OAAOA,KAAK,KAAK,UAAU;AACpC;AACA,SAASC,cAAcA,CAACpP,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOD,CAAC,IAAIA,CAAC,GAAGC,CAAC,IAAIA,CAAC,GAAGD,CAAC,KAAKC,CAAC,IAAID,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAI,OAAOA,CAAC,KAAK,UAAU;AAC3F;AACA,SAASqP,QAAQA,CAACC,GAAG,EAAE;EACrB,OAAO7hB,MAAM,CAACiC,IAAI,CAAC4f,GAAG,CAAC,CAACxc,MAAM,KAAK,CAAC;AACtC;AACA,SAASyc,MAAMA,CAAC3gB,MAAM,EAAE0S,IAAI,EAAE;EAC5B1S,MAAM,CAAC4gB,WAAW,CAAClO,IAAI,CAAC;AAC1B;AACA,SAASmO,MAAMA,CAAC7gB,MAAM,EAAE0S,IAAI,EAAEoO,MAAM,EAAE;EACpC9gB,MAAM,CAAC+gB,YAAY,CAACrO,IAAI,EAAEoO,MAAM,IAAI,IAAI,CAAC;AAC3C;AACA,SAASE,MAAMA,CAACtO,IAAI,EAAE;EACpB,IAAIA,IAAI,CAAC6C,UAAU,EAAE;IACnB7C,IAAI,CAAC6C,UAAU,CAAC0L,WAAW,CAACvO,IAAI,CAAC;EACnC;AACF;AACA,SAASwO,YAAYA,CAACC,UAAU,EAAEC,SAAS,EAAE;EAC3C,KAAK,IAAInd,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkd,UAAU,CAACjd,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC7C,IAAIkd,UAAU,CAACld,CAAC,CAAC,EAAEkd,UAAU,CAACld,CAAC,CAAC,CAACiC,CAAC,CAACkb,SAAS,CAAC;EAC/C;AACF;AACA,SAAShhB,OAAOA,CAACgM,IAAI,EAAE;EACrB,OAAOpH,QAAQ,CAACqc,aAAa,CAACjV,IAAI,CAAC;AACrC;AACA,SAASkV,WAAWA,CAAClV,IAAI,EAAE;EACzB,OAAOpH,QAAQ,CAACuc,eAAe,CAAC,4BAA4B,EAAEnV,IAAI,CAAC;AACrE;AACA,SAASoV,IAAIA,CAAChV,IAAI,EAAE;EAClB,OAAOxH,QAAQ,CAACyc,cAAc,CAACjV,IAAI,CAAC;AACtC;AACA,SAASkV,KAAKA,CAAA,EAAG;EACf,OAAOF,IAAI,CAAC,GAAG,CAAC;AAClB;AACA,SAASG,KAAKA,CAAA,EAAG;EACf,OAAOH,IAAI,CAAC,EAAE,CAAC;AACjB;AACA,SAASI,MAAMA,CAAClP,IAAI,EAAE3P,KAAK,EAAEC,OAAO,EAAEpD,OAAO,EAAE;EAC7C8S,IAAI,CAACrN,gBAAgB,CAACtC,KAAK,EAAEC,OAAO,EAAEpD,OAAO,CAAC;EAC9C,OAAO,MAAM8S,IAAI,CAACpN,mBAAmB,CAACvC,KAAK,EAAEC,OAAO,EAAEpD,OAAO,CAAC;AAChE;AACA,SAASiiB,IAAIA,CAACnP,IAAI,EAAEoP,SAAS,EAAErjB,KAAK,EAAE;EACpC,IAAIA,KAAK,IAAI,IAAI,EAAEiU,IAAI,CAACqP,eAAe,CAACD,SAAS,CAAC,CAAC,KAAK,IAAIpP,IAAI,CAACsP,YAAY,CAACF,SAAS,CAAC,KAAKrjB,KAAK,EAAEiU,IAAI,CAACuP,YAAY,CAACH,SAAS,EAAErjB,KAAK,CAAC;AACzI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyjB,gCAAgC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC5D,SAASC,cAAcA,CAACzP,IAAI,EAAE0P,UAAU,EAAE;EACxC;EACA,MAAMC,WAAW,GAAGxjB,MAAM,CAACyjB,yBAAyB,CAAC5P,IAAI,CAAC6P,SAAS,CAAC;EACpE,KAAK,MAAMjiB,GAAG,IAAI8hB,UAAU,EAAE;IAC5B,IAAIA,UAAU,CAAC9hB,GAAG,CAAC,IAAI,IAAI,EAAE;MAC3BoS,IAAI,CAACqP,eAAe,CAACzhB,GAAG,CAAC;IAC3B,CAAC,MAAM,IAAIA,GAAG,KAAK,OAAO,EAAE;MAC1BoS,IAAI,CAAC2M,KAAK,CAACmD,OAAO,GAAGJ,UAAU,CAAC9hB,GAAG,CAAC;IACtC,CAAC,MAAM,IAAIA,GAAG,KAAK,SAAS,EAAE;MAC5BoS,IAAI,CAACjU,KAAK,GAAGiU,IAAI,CAACpS,GAAG,CAAC,GAAG8hB,UAAU,CAAC9hB,GAAG,CAAC;IAC1C,CAAC,MAAM,IAAI+hB,WAAW,CAAC/hB,GAAG,CAAC,IAAI+hB,WAAW,CAAC/hB,GAAG,CAAC,CAAC0Z,GAAG,IAAIkI,gCAAgC,CAAClb,OAAO,CAAC1G,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3GoS,IAAI,CAACpS,GAAG,CAAC,GAAG8hB,UAAU,CAAC9hB,GAAG,CAAC;IAC7B,CAAC,MAAM;MACLuhB,IAAI,CAACnP,IAAI,EAAEpS,GAAG,EAAE8hB,UAAU,CAAC9hB,GAAG,CAAC,CAAC;IAClC;EACF;AACF;AACA,SAASmiB,QAAQA,CAACriB,OAAO,EAAE;EACzB,OAAOX,KAAK,CAAC6Y,IAAI,CAAClY,OAAO,CAACsiB,UAAU,CAAC;AACvC;AACA,SAASC,YAAYA,CAACviB,OAAO,EAAEgM,IAAI,EAAEwW,MAAM,EAAE;EAC3CxiB,OAAO,CAACqe,SAAS,CAACmE,MAAM,GAAG,KAAK,GAAG,QAAQ,CAAC,CAACxW,IAAI,CAAC;AACpD;AACA,IAAIyW,iBAAiB;AACrB,SAASC,qBAAqBA,CAACC,SAAS,EAAE;EACxCF,iBAAiB,GAAGE,SAAS;AAC/B;AACA,SAASC,qBAAqBA,CAAA,EAAG;EAC/B,IAAI,CAACH,iBAAiB,EAAE,MAAM,IAAI9gB,KAAK,CAAC,kDAAkD,CAAC;EAC3F,OAAO8gB,iBAAiB;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,OAAOA,CAAC5W,EAAE,EAAE;EACnB2W,qBAAqB,CAAC,CAAC,CAACE,EAAE,CAACC,QAAQ,CAAC/f,IAAI,CAACiJ,EAAE,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+W,WAAWA,CAAC/W,EAAE,EAAE;EACvB2W,qBAAqB,CAAC,CAAC,CAACE,EAAE,CAACG,YAAY,CAACjgB,IAAI,CAACiJ,EAAE,CAAC;AAClD;AACA,MAAMiX,gBAAgB,GAAG,EAAE;AAC3B,MAAMC,iBAAiB,GAAG,EAAE;AAC5B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,MAAMC,eAAe,GAAG,EAAE;AAC1B,MAAMC,gBAAgB,GAAG,eAAezE,OAAO,CAACC,OAAO,CAAC,CAAC;AACzD,IAAIyE,gBAAgB,GAAG,KAAK;AAC5B,SAASC,eAAeA,CAAA,EAAG;EACzB,IAAI,CAACD,gBAAgB,EAAE;IACrBA,gBAAgB,GAAG,IAAI;IACvBD,gBAAgB,CAAC3E,IAAI,CAAC8E,KAAK,CAAC;EAC9B;AACF;AACA,SAASC,mBAAmBA,CAACzX,EAAE,EAAE;EAC/BmX,gBAAgB,CAACpgB,IAAI,CAACiJ,EAAE,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0X,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;AAChC,IAAIC,QAAQ,GAAG,CAAC,CAAC,CAAC;AAClB,SAASJ,KAAKA,CAAA,EAAG;EACf;EACA;EACA;EACA,IAAII,QAAQ,KAAK,CAAC,EAAE;IAClB;EACF;EACA,MAAMC,eAAe,GAAGrB,iBAAiB;EACzC,GAAG;IACD;IACA;IACA,IAAI;MACF,OAAOoB,QAAQ,GAAGX,gBAAgB,CAACpf,MAAM,EAAE;QACzC,MAAM6e,SAAS,GAAGO,gBAAgB,CAACW,QAAQ,CAAC;QAC5CA,QAAQ,EAAE;QACVnB,qBAAqB,CAACC,SAAS,CAAC;QAChCxG,MAAM,CAACwG,SAAS,CAACG,EAAE,CAAC;MACtB;IACF,CAAC,CAAC,OAAOhe,CAAC,EAAE;MACV;MACAoe,gBAAgB,CAACpf,MAAM,GAAG,CAAC;MAC3B+f,QAAQ,GAAG,CAAC;MACZ,MAAM/e,CAAC;IACT;IACA4d,qBAAqB,CAAC,IAAI,CAAC;IAC3BQ,gBAAgB,CAACpf,MAAM,GAAG,CAAC;IAC3B+f,QAAQ,GAAG,CAAC;IACZ,OAAOV,iBAAiB,CAACrf,MAAM,EAAEqf,iBAAiB,CAACY,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1D;IACA;IACA;IACA,KAAK,IAAIlgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuf,gBAAgB,CAACtf,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACnD,MAAMmgB,QAAQ,GAAGZ,gBAAgB,CAACvf,CAAC,CAAC;MACpC,IAAI,CAAC8f,cAAc,CAACM,GAAG,CAACD,QAAQ,CAAC,EAAE;QACjC;QACAL,cAAc,CAACrF,GAAG,CAAC0F,QAAQ,CAAC;QAC5BA,QAAQ,CAAC,CAAC;MACZ;IACF;IACAZ,gBAAgB,CAACtf,MAAM,GAAG,CAAC;EAC7B,CAAC,QAAQof,gBAAgB,CAACpf,MAAM;EAChC,OAAOuf,eAAe,CAACvf,MAAM,EAAE;IAC7Buf,eAAe,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC;EACzB;EACAR,gBAAgB,GAAG,KAAK;EACxBI,cAAc,CAACO,KAAK,CAAC,CAAC;EACtBxB,qBAAqB,CAACoB,eAAe,CAAC;AACxC;AACA,SAAS3H,MAAMA,CAAC2G,EAAE,EAAE;EAClB,IAAIA,EAAE,CAACqB,QAAQ,KAAK,IAAI,EAAE;IACxBrB,EAAE,CAAC3G,MAAM,CAAC,CAAC;IACX6D,OAAO,CAAC8C,EAAE,CAACsB,aAAa,CAAC;IACzB,MAAMC,KAAK,GAAGvB,EAAE,CAACuB,KAAK;IACtBvB,EAAE,CAACuB,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;IACfvB,EAAE,CAACqB,QAAQ,IAAIrB,EAAE,CAACqB,QAAQ,CAACG,CAAC,CAACxB,EAAE,CAACjgB,GAAG,EAAEwhB,KAAK,CAAC;IAC3CvB,EAAE,CAACG,YAAY,CAAC9hB,OAAO,CAACuiB,mBAAmB,CAAC;EAC9C;AACF;AACA;AACA;AACA;AACA,SAASa,sBAAsBA,CAACtE,GAAG,EAAE;EACnC,MAAMuE,QAAQ,GAAG,EAAE;EACnB,MAAMC,OAAO,GAAG,EAAE;EAClBrB,gBAAgB,CAACjiB,OAAO,CAAC+E,CAAC,IAAI+Z,GAAG,CAACrZ,OAAO,CAACV,CAAC,CAAC,KAAK,CAAC,CAAC,GAAGse,QAAQ,CAACxhB,IAAI,CAACkD,CAAC,CAAC,GAAGue,OAAO,CAACzhB,IAAI,CAACkD,CAAC,CAAC,CAAC;EACzFue,OAAO,CAACtjB,OAAO,CAAC+E,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;EACzBkd,gBAAgB,GAAGoB,QAAQ;AAC7B;AACA,MAAME,QAAQ,GAAG,IAAId,GAAG,CAAC,CAAC;AAC1B,IAAIe,MAAM;AACV,SAASC,YAAYA,CAAA,EAAG;EACtBD,MAAM,GAAG;IACPxe,CAAC,EAAE,CAAC;IACJD,CAAC,EAAE,EAAE;IACLoe,CAAC,EAAEK,MAAM,CAAC;EACZ,CAAC;AACH;AAEA,SAASE,YAAYA,CAAA,EAAG;EACtB,IAAI,CAACF,MAAM,CAACxe,CAAC,EAAE;IACb6Z,OAAO,CAAC2E,MAAM,CAACze,CAAC,CAAC;EACnB;EACAye,MAAM,GAAGA,MAAM,CAACL,CAAC;AACnB;AACA,SAASQ,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACnC,IAAID,KAAK,IAAIA,KAAK,CAAClhB,CAAC,EAAE;IACpB6gB,QAAQ,CAACO,MAAM,CAACF,KAAK,CAAC;IACtBA,KAAK,CAAClhB,CAAC,CAACmhB,KAAK,CAAC;EAChB;AACF;AACA,SAASE,cAAcA,CAACH,KAAK,EAAEC,KAAK,EAAEpE,MAAM,EAAEoD,QAAQ,EAAE;EACtD,IAAIe,KAAK,IAAIA,KAAK,CAACI,CAAC,EAAE;IACpB,IAAIT,QAAQ,CAACT,GAAG,CAACc,KAAK,CAAC,EAAE;IACzBL,QAAQ,CAACpG,GAAG,CAACyG,KAAK,CAAC;IACnBJ,MAAM,CAACze,CAAC,CAAClD,IAAI,CAAC,MAAM;MAClB0hB,QAAQ,CAACO,MAAM,CAACF,KAAK,CAAC;MACtB,IAAIf,QAAQ,EAAE;QACZ,IAAIpD,MAAM,EAAEmE,KAAK,CAACjf,CAAC,CAAC,CAAC,CAAC;QACtBke,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,CAAC;IACFe,KAAK,CAACI,CAAC,CAACH,KAAK,CAAC;EAChB,CAAC,MAAM,IAAIhB,QAAQ,EAAE;IACnBA,QAAQ,CAAC,CAAC;EACZ;AACF;AACA,SAASoB,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC1C,MAAMnJ,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMoJ,WAAW,GAAG,CAAC,CAAC;EACtB,MAAMC,aAAa,GAAG;IACpBC,OAAO,EAAE;EACX,CAAC;EACD,IAAI5hB,CAAC,GAAGwhB,MAAM,CAACvhB,MAAM;EACrB,OAAOD,CAAC,EAAE,EAAE;IACV,MAAMshB,CAAC,GAAGE,MAAM,CAACxhB,CAAC,CAAC;IACnB,MAAM6hB,CAAC,GAAGJ,OAAO,CAACzhB,CAAC,CAAC;IACpB,IAAI6hB,CAAC,EAAE;MACL,KAAK,MAAMxlB,GAAG,IAAIilB,CAAC,EAAE;QACnB,IAAI,EAAEjlB,GAAG,IAAIwlB,CAAC,CAAC,EAAEH,WAAW,CAACrlB,GAAG,CAAC,GAAG,CAAC;MACvC;MACA,KAAK,MAAMA,GAAG,IAAIwlB,CAAC,EAAE;QACnB,IAAI,CAACF,aAAa,CAACtlB,GAAG,CAAC,EAAE;UACvBic,MAAM,CAACjc,GAAG,CAAC,GAAGwlB,CAAC,CAACxlB,GAAG,CAAC;UACpBslB,aAAa,CAACtlB,GAAG,CAAC,GAAG,CAAC;QACxB;MACF;MACAmlB,MAAM,CAACxhB,CAAC,CAAC,GAAG6hB,CAAC;IACf,CAAC,MAAM;MACL,KAAK,MAAMxlB,GAAG,IAAIilB,CAAC,EAAE;QACnBK,aAAa,CAACtlB,GAAG,CAAC,GAAG,CAAC;MACxB;IACF;EACF;EACA,KAAK,MAAMA,GAAG,IAAIqlB,WAAW,EAAE;IAC7B,IAAI,EAAErlB,GAAG,IAAIic,MAAM,CAAC,EAAEA,MAAM,CAACjc,GAAG,CAAC,GAAGsC,SAAS;EAC/C;EACA,OAAO2Z,MAAM;AACf;AACA,SAASwJ,gBAAgBA,CAACZ,KAAK,EAAE;EAC/BA,KAAK,IAAIA,KAAK,CAAC7e,CAAC,CAAC,CAAC;AACpB;AACA,SAAS0f,eAAeA,CAACjD,SAAS,EAAE/iB,MAAM,EAAE8gB,MAAM,EAAEmF,aAAa,EAAE;EACjE,MAAM;IACJ1B,QAAQ;IACRlB;EACF,CAAC,GAAGN,SAAS,CAACG,EAAE;EAChBqB,QAAQ,IAAIA,QAAQ,CAAC2B,CAAC,CAAClmB,MAAM,EAAE8gB,MAAM,CAAC;EACtC,IAAI,CAACmF,aAAa,EAAE;IAClB;IACAnC,mBAAmB,CAAC,MAAM;MACxB,MAAMqC,cAAc,GAAGpD,SAAS,CAACG,EAAE,CAACC,QAAQ,CAAChjB,GAAG,CAAC8f,GAAG,CAAC,CAACvf,MAAM,CAAC4f,WAAW,CAAC;MACzE;MACA;MACA;MACA,IAAIyC,SAAS,CAACG,EAAE,CAACkD,UAAU,EAAE;QAC3BrD,SAAS,CAACG,EAAE,CAACkD,UAAU,CAAChjB,IAAI,CAAC,GAAG+iB,cAAc,CAAC;MACjD,CAAC,MAAM;QACL;QACA;QACA/F,OAAO,CAAC+F,cAAc,CAAC;MACzB;MACApD,SAAS,CAACG,EAAE,CAACC,QAAQ,GAAG,EAAE;IAC5B,CAAC,CAAC;EACJ;EACAE,YAAY,CAAC9hB,OAAO,CAACuiB,mBAAmB,CAAC;AAC3C;AACA,SAASuC,iBAAiBA,CAACtD,SAAS,EAAE3B,SAAS,EAAE;EAC/C,MAAM8B,EAAE,GAAGH,SAAS,CAACG,EAAE;EACvB,IAAIA,EAAE,CAACqB,QAAQ,KAAK,IAAI,EAAE;IACxBI,sBAAsB,CAACzB,EAAE,CAACG,YAAY,CAAC;IACvCjD,OAAO,CAAC8C,EAAE,CAACkD,UAAU,CAAC;IACtBlD,EAAE,CAACqB,QAAQ,IAAIrB,EAAE,CAACqB,QAAQ,CAACre,CAAC,CAACkb,SAAS,CAAC;IACvC;IACA;IACA8B,EAAE,CAACkD,UAAU,GAAGlD,EAAE,CAACqB,QAAQ,GAAG,IAAI;IAClCrB,EAAE,CAACjgB,GAAG,GAAG,EAAE;EACb;AACF;AACA,SAASqjB,UAAUA,CAACvD,SAAS,EAAE9e,CAAC,EAAE;EAChC,IAAI8e,SAAS,CAACG,EAAE,CAACuB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;IAChCnB,gBAAgB,CAAClgB,IAAI,CAAC2f,SAAS,CAAC;IAChCa,eAAe,CAAC,CAAC;IACjBb,SAAS,CAACG,EAAE,CAACuB,KAAK,CAAC8B,IAAI,CAAC,CAAC,CAAC;EAC5B;EACAxD,SAAS,CAACG,EAAE,CAACuB,KAAK,CAACxgB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE;AAC/C;AACA,SAASuiB,IAAIA,CAACzD,SAAS,EAAEnjB,OAAO,EAAE6mB,QAAQ,EAAEC,eAAe,EAAEC,SAAS,EAAEC,KAAK,EAAEC,aAAa,EAAEpC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;EAC1G,MAAMqC,gBAAgB,GAAGjE,iBAAiB;EAC1CC,qBAAqB,CAACC,SAAS,CAAC;EAChC,MAAMG,EAAE,GAAGH,SAAS,CAACG,EAAE,GAAG;IACxBqB,QAAQ,EAAE,IAAI;IACdthB,GAAG,EAAE,EAAE;IACP;IACA2jB,KAAK;IACLrK,MAAM,EAAEsD,IAAI;IACZ8G,SAAS;IACTI,KAAK,EAAE7G,YAAY,CAAC,CAAC;IACrB;IACAiD,QAAQ,EAAE,EAAE;IACZiD,UAAU,EAAE,EAAE;IACdY,aAAa,EAAE,EAAE;IACjBxC,aAAa,EAAE,EAAE;IACjBnB,YAAY,EAAE,EAAE;IAChB1f,OAAO,EAAE,IAAIka,GAAG,CAACje,OAAO,CAAC+D,OAAO,KAAKmjB,gBAAgB,GAAGA,gBAAgB,CAAC5D,EAAE,CAACvf,OAAO,GAAG,EAAE,CAAC,CAAC;IAC1F;IACAsjB,SAAS,EAAE/G,YAAY,CAAC,CAAC;IACzBuE,KAAK;IACLyC,UAAU,EAAE,KAAK;IACjBhM,IAAI,EAAEtb,OAAO,CAACI,MAAM,IAAI8mB,gBAAgB,CAAC5D,EAAE,CAAChI;EAC9C,CAAC;EACD2L,aAAa,IAAIA,aAAa,CAAC3D,EAAE,CAAChI,IAAI,CAAC;EACvC,IAAIiM,KAAK,GAAG,KAAK;EACjBjE,EAAE,CAACjgB,GAAG,GAAGwjB,QAAQ,GAAGA,QAAQ,CAAC1D,SAAS,EAAEnjB,OAAO,CAACgnB,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC3iB,CAAC,EAAEmjB,GAAG,EAAE,GAAGC,IAAI,KAAK;IAChF,MAAM5oB,KAAK,GAAG4oB,IAAI,CAACnjB,MAAM,GAAGmjB,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG;IACzC,IAAIlE,EAAE,CAACjgB,GAAG,IAAI0jB,SAAS,CAACzD,EAAE,CAACjgB,GAAG,CAACgB,CAAC,CAAC,EAAEif,EAAE,CAACjgB,GAAG,CAACgB,CAAC,CAAC,GAAGxF,KAAK,CAAC,EAAE;MACrD,IAAI,CAACykB,EAAE,CAACgE,UAAU,IAAIhE,EAAE,CAAC6D,KAAK,CAAC9iB,CAAC,CAAC,EAAEif,EAAE,CAAC6D,KAAK,CAAC9iB,CAAC,CAAC,CAACxF,KAAK,CAAC;MACrD,IAAI0oB,KAAK,EAAEb,UAAU,CAACvD,SAAS,EAAE9e,CAAC,CAAC;IACrC;IACA,OAAOmjB,GAAG;EACZ,CAAC,CAAC,GAAG,EAAE;EACPlE,EAAE,CAAC3G,MAAM,CAAC,CAAC;EACX4K,KAAK,GAAG,IAAI;EACZ/G,OAAO,CAAC8C,EAAE,CAACsB,aAAa,CAAC;EACzB;EACAtB,EAAE,CAACqB,QAAQ,GAAGmC,eAAe,GAAGA,eAAe,CAACxD,EAAE,CAACjgB,GAAG,CAAC,GAAG,KAAK;EAC/D,IAAIrD,OAAO,CAACI,MAAM,EAAE;IAClB,IAAIJ,OAAO,CAAC0nB,OAAO,EAAE;MACnB,MAAMC,KAAK,GAAG9E,QAAQ,CAAC7iB,OAAO,CAACI,MAAM,CAAC;MACtC;MACAkjB,EAAE,CAACqB,QAAQ,IAAIrB,EAAE,CAACqB,QAAQ,CAACiD,CAAC,CAACD,KAAK,CAAC;MACnCA,KAAK,CAAChmB,OAAO,CAACyf,MAAM,CAAC;IACvB,CAAC,MAAM;MACL;MACAkC,EAAE,CAACqB,QAAQ,IAAIrB,EAAE,CAACqB,QAAQ,CAACje,CAAC,CAAC,CAAC;IAChC;IACA,IAAI1G,OAAO,CAAC6nB,KAAK,EAAEvC,aAAa,CAACnC,SAAS,CAACG,EAAE,CAACqB,QAAQ,CAAC;IACvDyB,eAAe,CAACjD,SAAS,EAAEnjB,OAAO,CAACI,MAAM,EAAEJ,OAAO,CAACkhB,MAAM,EAAElhB,OAAO,CAACqmB,aAAa,CAAC;IACjFpC,KAAK,CAAC,CAAC;EACT;EACAf,qBAAqB,CAACgE,gBAAgB,CAAC;AACzC;AACA;AACA;AACA;AACA,MAAMY,eAAe,CAAC;EACpBC,QAAQA,CAAA,EAAG;IACTtB,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1B,IAAI,CAACsB,QAAQ,GAAG9H,IAAI;EACtB;EACA+H,GAAGA,CAACC,IAAI,EAAEzD,QAAQ,EAAE;IAClB,IAAI,CAAC9D,WAAW,CAAC8D,QAAQ,CAAC,EAAE;MAC1B,OAAOvE,IAAI;IACb;IACA,MAAMoH,SAAS,GAAG,IAAI,CAAC/D,EAAE,CAAC+D,SAAS,CAACY,IAAI,CAAC,KAAK,IAAI,CAAC3E,EAAE,CAAC+D,SAAS,CAACY,IAAI,CAAC,GAAG,EAAE,CAAC;IAC3EZ,SAAS,CAAC7jB,IAAI,CAACghB,QAAQ,CAAC;IACxB,OAAO,MAAM;MACX,MAAM7gB,KAAK,GAAG0jB,SAAS,CAACjgB,OAAO,CAACod,QAAQ,CAAC;MACzC,IAAI7gB,KAAK,KAAK,CAAC,CAAC,EAAE0jB,SAAS,CAACzjB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAC9C,CAAC;EACH;EACAukB,IAAIA,CAACC,OAAO,EAAE;IACZ,IAAI,IAAI,CAACC,KAAK,IAAI,CAACvH,QAAQ,CAACsH,OAAO,CAAC,EAAE;MACpC,IAAI,CAAC7E,EAAE,CAACgE,UAAU,GAAG,IAAI;MACzB,IAAI,CAACc,KAAK,CAACD,OAAO,CAAC;MACnB,IAAI,CAAC7E,EAAE,CAACgE,UAAU,GAAG,KAAK;IAC5B;EACF;AACF;;AAEA;AACA,SAASe,iBAAiBA,CAAChlB,GAAG,EAAE;EAC9B,IAAIilB,MAAM;EACV,IAAIC,uBAAuB;EAC3B,IAAIC,kBAAkB;EACtB,IAAIC,OAAO;EACX,IAAIC,OAAO;EACX,OAAO;IACLhiB,CAACA,CAAA,EAAG;MACF4hB,MAAM,GAAG9nB,OAAO,CAAC,QAAQ,CAAC;MAC1ByhB,IAAI,CAACqG,MAAM,EAAE,YAAY,EAAEC,uBAAuB,GAAG,SAASllB,GAAG,CAAC,CAAC,CAAC,GAAG,SAASA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;MAC9F4e,IAAI,CAACqG,MAAM,EAAE,OAAO,EAAEE,kBAAkB,GAAG,GAAG,WAAWnlB,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,oBAAoB,aAAaA,GAAG,CAAC,CAAC,CAAC,GAAG,2BAA2B,GAAG,EAAE,EAAE,CAAC;MAClJilB,MAAM,CAACK,QAAQ,GAAG,YAAYtlB,GAAG,CAAC,CAAC,CAAC;MACpC4e,IAAI,CAACqG,MAAM,EAAE,UAAU,EAAE,GAAG,CAAC;IAC/B,CAAC;IACDhC,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBD,MAAM,CAAC7gB,MAAM,EAAEkoB,MAAM,EAAEpH,MAAM,CAAC;MAC9BoH,MAAM,CAACM,SAAS,GAAG,QAAQvlB,GAAG,CAAC,CAAC,CAAC;MACjC,IAAI,CAAColB,OAAO,EAAE;QACZC,OAAO,GAAG1G,MAAM,CAACsG,MAAM,EAAE,OAAO,EAAE,YAAY;UAC5C,IAAI5H,WAAW,EAAE,UAAUrd,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,UAAUA,GAAG,CAAC,CAAC,CAAC,CAACW,KAAK,CAAC,IAAI,EAAEgD,SAAS,CAAC;QAC7E,CAAC,CAAC;QACFyhB,OAAO,GAAG,IAAI;MAChB;IACF,CAAC;IACD3D,CAACA,CAAC+D,OAAO,EAAE,CAAChE,KAAK,CAAC,EAAE;MAClBxhB,GAAG,GAAGwlB,OAAO;MACb,IAAIhE,KAAK,GAAG,QAAQ,EAAE,EAAEyD,MAAM,CAACM,SAAS,GAAG,QAAQvlB,GAAG,CAAC,CAAC,CAAC;MACzD,IAAIwhB,KAAK,GAAG,SAAS,CAAC,IAAI0D,uBAAuB,MAAMA,uBAAuB,GAAG,SAASllB,GAAG,CAAC,CAAC,CAAC,GAAG,SAASA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE;QAC1H4e,IAAI,CAACqG,MAAM,EAAE,YAAY,EAAEC,uBAAuB,CAAC;MACrD;MACA,IAAI1D,KAAK,GAAG,sBAAsB,EAAE,IAAI2D,kBAAkB,MAAMA,kBAAkB,GAAG,GAAG,WAAWnlB,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,oBAAoB,aAAaA,GAAG,CAAC,CAAC,CAAC,GAAG,2BAA2B,GAAG,EAAE,EAAE,CAAC,EAAE;QAC5L4e,IAAI,CAACqG,MAAM,EAAE,OAAO,EAAEE,kBAAkB,CAAC;MAC3C;MACA,IAAI3D,KAAK,GAAG,YAAY,CAAC,EAAE;QACzByD,MAAM,CAACK,QAAQ,GAAG,YAAYtlB,GAAG,CAAC,CAAC,CAAC;MACtC;IACF,CAAC;IACDgB,CAAC,EAAE4b,IAAI;IACP0F,CAAC,EAAE1F,IAAI;IACP3Z,CAACA,CAACkb,SAAS,EAAE;MACX,IAAIA,SAAS,EAAEJ,MAAM,CAACkH,MAAM,CAAC;MAC7BG,OAAO,GAAG,KAAK;MACfC,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH;AACA,SAASI,UAAUA,CAACC,MAAM,EAAEZ,OAAO,EAAEa,YAAY,EAAE;EACjD,IAAI;IACFnd,MAAM;IACNnH;EACF,CAAC,GAAGyjB,OAAO;EACX,IAAIc,MAAM,EAAEC,OAAO,EAAEP,QAAQ,EAAEQ,KAAK,EAAEC,SAAS,EAAExH,IAAI;EACrD,SAASyH,eAAeA,CAACC,MAAM,EAAE;IAC/B,IAAIzmB,UAAU,CAACymB,MAAM,CAAC,EAAE;MACtB,OAAOA,MAAM,GAAGA,MAAM,CAAClqB,IAAI,CAACsF,IAAI,CAAC;IACnC;IACA,OAAO4kB,MAAM;EACf;EACAP,MAAM,CAACX,KAAK,GAAGD,OAAO,IAAI;IACxB,IAAI,QAAQ,IAAIA,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEnd,MAAM,GAAGsc,OAAO,CAACtc,MAAM,CAAC;IACjE,IAAI,MAAM,IAAIsc,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEtkB,IAAI,GAAGyjB,OAAO,CAACzjB,IAAI,CAAC;EAC7D,CAAC;EACDqkB,MAAM,CAACzF,EAAE,CAAC3G,MAAM,GAAG,MAAM;IACvB,IAAIoM,MAAM,CAACzF,EAAE,CAACuB,KAAK,GAAG,gBAAgB,GAAG,EAAE;MACzC;QACEmE,YAAY,CAAC,CAAC,EAAEC,MAAM,GAAGpd,MAAM,CAACod,MAAM,GAAGpd,MAAM,CAACod,MAAM,CAAC1kB,IAAI,CAACG,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAAC;QAC9E+jB,YAAY,CAAC,CAAC,EAAEE,OAAO,GAAGrd,MAAM,CAACqd,OAAO,CAAC;QACzCF,YAAY,CAAC,CAAC,EAAEL,QAAQ,GAAG9c,MAAM,CAAC8c,QAAQ,GAAGU,eAAe,CAACxd,MAAM,CAAC8c,QAAQ,CAAC,GAAG,KAAK,CAAC;QACtFK,YAAY,CAAC,CAAC,EAAEG,KAAK,GAAGtd,MAAM,CAACsd,KAAK,GAAGE,eAAe,CAACxd,MAAM,CAACsd,KAAK,CAAC,GAAG,IAAI,CAAC;QAC5EH,YAAY,CAAC,CAAC,EAAEI,SAAS,GAAGvd,MAAM,CAACud,SAAS,CAAC;QAC7CJ,YAAY,CAAC,CAAC,EAAEpH,IAAI,GAAG/V,MAAM,CAAC+V,IAAI,GAAGyH,eAAe,CAACxd,MAAM,CAAC+V,IAAI,CAAC,GAAG,IAAI,CAAC;MAC3E;IACF;EACF,CAAC;EACD,OAAO,CAACqH,MAAM,EAAEC,OAAO,EAAEP,QAAQ,EAAEQ,KAAK,EAAEC,SAAS,EAAExH,IAAI,EAAE/V,MAAM,EAAEnH,IAAI,CAAC;AAC1E;AACA,MAAM6kB,eAAe,SAASzB,eAAe,CAAC;EAC5C1jB,WAAWA,CAACpE,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IACP4mB,IAAI,CAAC,IAAI,EAAE5mB,OAAO,EAAE8oB,UAAU,EAAET,iBAAiB,EAAEzH,cAAc,EAAE;MACjE/U,MAAM,EAAE,CAAC;MACTnH,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,SAAS8kB,gBAAgBA,CAACnmB,GAAG,EAAEkH,IAAI,EAAElG,CAAC,EAAE;EACtC,MAAMolB,SAAS,GAAGpmB,GAAG,CAACqmB,KAAK,CAAC,CAAC;EAC7BD,SAAS,CAAC,CAAC,CAAC,GAAGlf,IAAI,CAAClG,CAAC,CAAC;EACtB,OAAOolB,SAAS;AAClB;;AAEA;AACA,SAASE,iBAAiBA,CAACtmB,GAAG,EAAE;EAC9B,IAAIumB,aAAa;EACjB,IAAIC,OAAO;EACX,IAAIC,UAAU,GAAG,WAAWzmB,GAAG,CAAC,CAAC,CAAC;EAClC,IAAI0mB,WAAW,GAAG,EAAE;EACpB,KAAK,IAAI1lB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGylB,UAAU,CAACxlB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC7C0lB,WAAW,CAAC1lB,CAAC,CAAC,GAAG2lB,iBAAiB,CAACR,gBAAgB,CAACnmB,GAAG,EAAEymB,UAAU,EAAEzlB,CAAC,CAAC,CAAC;EAC1E;EACA,MAAM4lB,GAAG,GAAG5lB,CAAC,IAAIqhB,cAAc,CAACqE,WAAW,CAAC1lB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM;IAC1D0lB,WAAW,CAAC1lB,CAAC,CAAC,GAAG,IAAI;EACvB,CAAC,CAAC;EACF,OAAO;IACLqC,CAACA,CAAA,EAAG;MACF,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0lB,WAAW,CAACzlB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC9C0lB,WAAW,CAAC1lB,CAAC,CAAC,CAACqC,CAAC,CAAC,CAAC;MACpB;MACAkjB,aAAa,GAAG7H,KAAK,CAAC,CAAC;IACzB,CAAC;IACDuE,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChB,KAAK,IAAI7c,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0lB,WAAW,CAACzlB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC9C,IAAI0lB,WAAW,CAAC1lB,CAAC,CAAC,EAAE;UAClB0lB,WAAW,CAAC1lB,CAAC,CAAC,CAACiiB,CAAC,CAAClmB,MAAM,EAAE8gB,MAAM,CAAC;QAClC;MACF;MACAD,MAAM,CAAC7gB,MAAM,EAAEwpB,aAAa,EAAE1I,MAAM,CAAC;MACrC2I,OAAO,GAAG,IAAI;IAChB,CAAC;IACD/E,CAACA,CAACzhB,GAAG,EAAEwhB,KAAK,EAAE;MACZ,IAAIA,KAAK,GAAG,iBAAiB,CAAC,EAAE;QAC9BiF,UAAU,GAAG,WAAWzmB,GAAG,CAAC,CAAC,CAAC;QAC9B,IAAIgB,CAAC;QACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGylB,UAAU,CAACxlB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;UACzC,MAAMolB,SAAS,GAAGD,gBAAgB,CAACnmB,GAAG,EAAEymB,UAAU,EAAEzlB,CAAC,CAAC;UACtD,IAAI0lB,WAAW,CAAC1lB,CAAC,CAAC,EAAE;YAClB0lB,WAAW,CAAC1lB,CAAC,CAAC,CAACygB,CAAC,CAAC2E,SAAS,EAAE5E,KAAK,CAAC;YAClCS,aAAa,CAACyE,WAAW,CAAC1lB,CAAC,CAAC,EAAE,CAAC,CAAC;UAClC,CAAC,MAAM;YACL0lB,WAAW,CAAC1lB,CAAC,CAAC,GAAG2lB,iBAAiB,CAACP,SAAS,CAAC;YAC7CM,WAAW,CAAC1lB,CAAC,CAAC,CAACqC,CAAC,CAAC,CAAC;YAClB4e,aAAa,CAACyE,WAAW,CAAC1lB,CAAC,CAAC,EAAE,CAAC,CAAC;YAChC0lB,WAAW,CAAC1lB,CAAC,CAAC,CAACiiB,CAAC,CAACsD,aAAa,CAACjU,UAAU,EAAEiU,aAAa,CAAC;UAC3D;QACF;QACAxE,YAAY,CAAC,CAAC;QACd,KAAK/gB,CAAC,GAAGylB,UAAU,CAACxlB,MAAM,EAAED,CAAC,GAAG0lB,WAAW,CAACzlB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;UAC1D4lB,GAAG,CAAC5lB,CAAC,CAAC;QACR;QACAghB,YAAY,CAAC,CAAC;MAChB;IACF,CAAC;IACDhhB,CAACA,CAACmhB,KAAK,EAAE;MACP,IAAIqE,OAAO,EAAE;MACb,KAAK,IAAIxlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGylB,UAAU,CAACxlB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC7CihB,aAAa,CAACyE,WAAW,CAAC1lB,CAAC,CAAC,CAAC;MAC/B;MACAwlB,OAAO,GAAG,IAAI;IAChB,CAAC;IACDlE,CAACA,CAACH,KAAK,EAAE;MACPuE,WAAW,GAAGA,WAAW,CAACjpB,MAAM,CAACoL,OAAO,CAAC;MACzC,KAAK,IAAI7H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0lB,WAAW,CAACzlB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC9CqhB,cAAc,CAACqE,WAAW,CAAC1lB,CAAC,CAAC,CAAC;MAChC;MACAwlB,OAAO,GAAG,KAAK;IACjB,CAAC;IACDvjB,CAACA,CAACkb,SAAS,EAAE;MACXF,YAAY,CAACyI,WAAW,EAAEvI,SAAS,CAAC;MACpC,IAAIA,SAAS,EAAEJ,MAAM,CAACwI,aAAa,CAAC;IACtC;EACF,CAAC;AACH;;AAEA;AACA,SAASI,iBAAiBA,CAAC3mB,GAAG,EAAE;EAC9B,IAAI6mB,cAAc;EAClB,IAAIL,OAAO;EACXK,cAAc,GAAG,IAAIX,eAAe,CAAC;IACnCvC,KAAK,EAAE;MACLnb,MAAM,EAAE,UAAUxI,GAAG,CAAC,CAAC,CAAC;MACxBqB,IAAI,EAAE,QAAQrB,GAAG,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;EACF,OAAO;IACLqD,CAACA,CAAA,EAAG;MACFyf,gBAAgB,CAAC+D,cAAc,CAAC5G,EAAE,CAACqB,QAAQ,CAAC;IAC9C,CAAC;IACD2B,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBkF,eAAe,CAAC8D,cAAc,EAAE9pB,MAAM,EAAE8gB,MAAM,CAAC;MAC/C2I,OAAO,GAAG,IAAI;IAChB,CAAC;IACD/E,CAACA,CAACzhB,GAAG,EAAEwhB,KAAK,EAAE;MACZ,MAAMsF,sBAAsB,GAAG,CAAC,CAAC;MACjC,IAAItF,KAAK,GAAG,WAAW,CAAC,EAAEsF,sBAAsB,CAACte,MAAM,GAAG,UAAUxI,GAAG,CAAC,CAAC,CAAC;MAC1E,IAAIwhB,KAAK,GAAG,QAAQ,CAAC,EAAEsF,sBAAsB,CAACzlB,IAAI,GAAG,QAAQrB,GAAG,CAAC,CAAC,CAAC;MACnE6mB,cAAc,CAAChC,IAAI,CAACiC,sBAAsB,CAAC;IAC7C,CAAC;IACD9lB,CAACA,CAACmhB,KAAK,EAAE;MACP,IAAIqE,OAAO,EAAE;MACbvE,aAAa,CAAC4E,cAAc,CAAC5G,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MAChDqE,OAAO,GAAG,IAAI;IAChB,CAAC;IACDlE,CAACA,CAACH,KAAK,EAAE;MACPE,cAAc,CAACwE,cAAc,CAAC5G,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MACjDqE,OAAO,GAAG,KAAK;IACjB,CAAC;IACDvjB,CAACA,CAACkb,SAAS,EAAE;MACXiF,iBAAiB,CAACyD,cAAc,EAAE1I,SAAS,CAAC;IAC9C;EACF,CAAC;AACH;AACA,SAAS4I,iBAAiBA,CAAC/mB,GAAG,EAAE;EAC9B,IAAIgnB,MAAM;EACV,IAAIR,OAAO;EACX,IAAIS,QAAQ,GAAG,WAAWjnB,GAAG,CAAC,CAAC,CAAC,IAAIsmB,iBAAiB,CAACtmB,GAAG,CAAC;EAC1D,OAAO;IACLqD,CAACA,CAAA,EAAG;MACF2jB,MAAM,GAAG7pB,OAAO,CAAC,QAAQ,CAAC;MAC1B,IAAI8pB,QAAQ,EAAEA,QAAQ,CAAC5jB,CAAC,CAAC,CAAC;MAC1Bub,IAAI,CAACoI,MAAM,EAAE,OAAO,EAAE,iBAAiB,CAAC;IAC1C,CAAC;IACD/D,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBD,MAAM,CAAC7gB,MAAM,EAAEiqB,MAAM,EAAEnJ,MAAM,CAAC;MAC9B,IAAIoJ,QAAQ,EAAEA,QAAQ,CAAChE,CAAC,CAAC+D,MAAM,EAAE,IAAI,CAAC;MACtCR,OAAO,GAAG,IAAI;IAChB,CAAC;IACD/E,CAACA,CAACzhB,GAAG,EAAE,CAACwhB,KAAK,CAAC,EAAE;MACd,KAAK,WAAWxhB,GAAG,CAAC,CAAC,CAAC,EAAE;QACtB,IAAIinB,QAAQ,EAAE;UACZA,QAAQ,CAACxF,CAAC,CAACzhB,GAAG,EAAEwhB,KAAK,CAAC;UACtB,IAAIA,KAAK,GAAG,WAAW,CAAC,EAAE;YACxBS,aAAa,CAACgF,QAAQ,EAAE,CAAC,CAAC;UAC5B;QACF,CAAC,MAAM;UACLA,QAAQ,GAAGX,iBAAiB,CAACtmB,GAAG,CAAC;UACjCinB,QAAQ,CAAC5jB,CAAC,CAAC,CAAC;UACZ4e,aAAa,CAACgF,QAAQ,EAAE,CAAC,CAAC;UAC1BA,QAAQ,CAAChE,CAAC,CAAC+D,MAAM,EAAE,IAAI,CAAC;QAC1B;MACF,CAAC,MAAM,IAAIC,QAAQ,EAAE;QACnBlF,YAAY,CAAC,CAAC;QACdM,cAAc,CAAC4E,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM;UACnCA,QAAQ,GAAG,IAAI;QACjB,CAAC,CAAC;QACFjF,YAAY,CAAC,CAAC;MAChB;IACF,CAAC;IACDhhB,CAACA,CAACmhB,KAAK,EAAE;MACP,IAAIqE,OAAO,EAAE;MACbvE,aAAa,CAACgF,QAAQ,CAAC;MACvBT,OAAO,GAAG,IAAI;IAChB,CAAC;IACDlE,CAACA,CAACH,KAAK,EAAE;MACPE,cAAc,CAAC4E,QAAQ,CAAC;MACxBT,OAAO,GAAG,KAAK;IACjB,CAAC;IACDvjB,CAACA,CAACkb,SAAS,EAAE;MACX,IAAIA,SAAS,EAAEJ,MAAM,CAACiJ,MAAM,CAAC;MAC7B,IAAIC,QAAQ,EAAEA,QAAQ,CAAChkB,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;AACH;AACA,SAASikB,UAAUA,CAACxB,MAAM,EAAEZ,OAAO,EAAEa,YAAY,EAAE;EACjD,IAAIwB,OAAO;EACX,IAAI;IACF9lB;EACF,CAAC,GAAGyjB,OAAO;EACXY,MAAM,CAACX,KAAK,GAAGD,OAAO,IAAI;IACxB,IAAI,MAAM,IAAIA,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEtkB,IAAI,GAAGyjB,OAAO,CAACzjB,IAAI,CAAC;EAC7D,CAAC;EACDqkB,MAAM,CAACzF,EAAE,CAAC3G,MAAM,GAAG,MAAM;IACvB,IAAIoM,MAAM,CAACzF,EAAE,CAACuB,KAAK,GAAG,QAAQ,CAAC,EAAE;MAC/BmE,YAAY,CAAC,CAAC,EAAEwB,OAAO,GAAG9lB,IAAI,CAAC1E,OAAO,CAACwqB,OAAO,CAAC;IACjD;EACF,CAAC;EACD,OAAO,CAAC9lB,IAAI,EAAE8lB,OAAO,CAAC;AACxB;AACA,MAAMC,eAAe,SAAS3C,eAAe,CAAC;EAC5C1jB,WAAWA,CAACpE,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IACP4mB,IAAI,CAAC,IAAI,EAAE5mB,OAAO,EAAEuqB,UAAU,EAAEH,iBAAiB,EAAExJ,cAAc,EAAE;MACjElc,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,SAASgmB,iBAAiBA,CAACrnB,GAAG,EAAE;EAC9B,IAAIilB,MAAM;EACV,IAAIqC,IAAI;EACR,IAAIpC,uBAAuB;EAC3B,IAAIE,OAAO;EACX,IAAIC,OAAO;EACX,OAAO;IACLhiB,CAACA,CAAA,EAAG;MACF4hB,MAAM,GAAG9nB,OAAO,CAAC,QAAQ,CAAC;MAC1BmqB,IAAI,GAAGnqB,OAAO,CAAC,MAAM,CAAC;MACtBmqB,IAAI,CAACC,WAAW,GAAG,GAAG;MACtB3I,IAAI,CAAC0I,IAAI,EAAE,aAAa,EAAE,MAAM,CAAC;MACjC1I,IAAI,CAACqG,MAAM,EAAE,YAAY,EAAEC,uBAAuB,GAAG,cAAcllB,GAAG,CAAC,CAAC,CAAC,CAAC8lB,KAAK,GAAG,cAAc9lB,GAAG,CAAC,CAAC,CAAC,CAAC8lB,KAAK,GAAG,YAAY,CAAC;MAC5HlH,IAAI,CAACqG,MAAM,EAAE,OAAO,EAAE,sBAAsB,CAAC;MAC7CrG,IAAI,CAACqG,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;IAChC,CAAC;IACDhC,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBD,MAAM,CAAC7gB,MAAM,EAAEkoB,MAAM,EAAEpH,MAAM,CAAC;MAC9BH,MAAM,CAACuH,MAAM,EAAEqC,IAAI,CAAC;MACpB,IAAI,CAAClC,OAAO,EAAE;QACZC,OAAO,GAAG1G,MAAM,CAACsG,MAAM,EAAE,OAAO,EAAE,qBAAqBjlB,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9DolB,OAAO,GAAG,IAAI;MAChB;IACF,CAAC;IACD3D,CAACA,CAACzhB,GAAG,EAAE,CAACwhB,KAAK,CAAC,EAAE;MACd,IAAIA,KAAK,GAAG,cAAc,CAAC,IAAI0D,uBAAuB,MAAMA,uBAAuB,GAAG,cAAcllB,GAAG,CAAC,CAAC,CAAC,CAAC8lB,KAAK,GAAG,cAAc9lB,GAAG,CAAC,CAAC,CAAC,CAAC8lB,KAAK,GAAG,YAAY,CAAC,EAAE;QAC7JlH,IAAI,CAACqG,MAAM,EAAE,YAAY,EAAEC,uBAAuB,CAAC;MACrD;IACF,CAAC;IACDlkB,CAAC,EAAE4b,IAAI;IACP0F,CAAC,EAAE1F,IAAI;IACP3Z,CAACA,CAACkb,SAAS,EAAE;MACX,IAAIA,SAAS,EAAEJ,MAAM,CAACkH,MAAM,CAAC;MAC7BG,OAAO,GAAG,KAAK;MACfC,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH;AACA,SAASmC,UAAUA,CAAC9B,MAAM,EAAEZ,OAAO,EAAEa,YAAY,EAAE;EACjD,IAAI;IACF8B,UAAU;IACVpmB;EACF,CAAC,GAAGyjB,OAAO;;EAEX;AACF;AACA;EACE,MAAM4C,iBAAiB,GAAGzlB,CAAC,IAAI;IAC7BA,CAAC,CAAC0lB,cAAc,CAAC,CAAC;IAClBtmB,IAAI,CAACumB,MAAM,CAAC,CAAC;EACf,CAAC;EACDlC,MAAM,CAACX,KAAK,GAAGD,OAAO,IAAI;IACxB,IAAI,YAAY,IAAIA,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAE8B,UAAU,GAAG3C,OAAO,CAAC2C,UAAU,CAAC;IAC7E,IAAI,MAAM,IAAI3C,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEtkB,IAAI,GAAGyjB,OAAO,CAACzjB,IAAI,CAAC;EAC7D,CAAC;EACD,OAAO,CAAComB,UAAU,EAAEC,iBAAiB,EAAErmB,IAAI,CAAC;AAC9C;AACA,MAAMwmB,oBAAoB,SAASpD,eAAe,CAAC;EACjD1jB,WAAWA,CAACpE,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IACP4mB,IAAI,CAAC,IAAI,EAAE5mB,OAAO,EAAE6qB,UAAU,EAAEH,iBAAiB,EAAE9J,cAAc,EAAE;MACjEkK,UAAU,EAAE,CAAC;MACbpmB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,SAASymB,iBAAiBA,CAAC9nB,GAAG,EAAE;EAC9B,IAAI+nB,EAAE;EACN,OAAO;IACL1kB,CAACA,CAAA,EAAG;MACF0kB,EAAE,GAAG5qB,OAAO,CAAC,IAAI,CAAC;MAClByhB,IAAI,CAACmJ,EAAE,EAAE,IAAI,EAAE,WAAW/nB,GAAG,CAAC,CAAC,CAAC,CAAC;MACjC4e,IAAI,CAACmJ,EAAE,EAAE,OAAO,EAAE,gBAAgB,CAAC;IACrC,CAAC;IACD9E,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBD,MAAM,CAAC7gB,MAAM,EAAEgrB,EAAE,EAAElK,MAAM,CAAC;MAC1B;MACA7d,GAAG,CAAC,CAAC,CAAC,CAAC+nB,EAAE,CAAC;IACZ,CAAC;IACDtG,CAACA,CAACzhB,GAAG,EAAE,CAACwhB,KAAK,CAAC,EAAE;MACd,IAAIA,KAAK,GAAG,WAAW,CAAC,EAAE;QACxB5C,IAAI,CAACmJ,EAAE,EAAE,IAAI,EAAE,WAAW/nB,GAAG,CAAC,CAAC,CAAC,CAAC;MACnC;IACF,CAAC;IACDgB,CAAC,EAAE4b,IAAI;IACP0F,CAAC,EAAE1F,IAAI;IACP3Z,CAACA,CAACkb,SAAS,EAAE;MACX,IAAIA,SAAS,EAAEJ,MAAM,CAACgK,EAAE,CAAC;MACzB;MACA/nB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH;AACA,SAASgoB,UAAUA,CAACtC,MAAM,EAAEZ,OAAO,EAAEa,YAAY,EAAE;EACjD,IAAI;IACFsC,OAAO;IACP9qB,OAAO;IACP+qB;EACF,CAAC,GAAGpD,OAAO;EACX3E,WAAW,CAAC,MAAM;IAChB,IAAI3gB,UAAU,CAAC0oB,KAAK,CAAC,EAAE;MACrBvC,YAAY,CAAC,CAAC,EAAEuC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;IAClC;IACAvC,YAAY,CAAC,CAAC,EAAExoB,OAAO,CAACooB,SAAS,GAAG2C,KAAK,EAAE/qB,OAAO,CAAC;EACrD,CAAC,CAAC;EACF,SAASgrB,UAAUA,CAACC,OAAO,EAAE;IAC3B9H,iBAAiB,CAAC8H,OAAO,GAAG,SAAS,GAAG,MAAM,CAAC,CAAC,MAAM;MACpDjrB,OAAO,GAAGirB,OAAO;MACjBzC,YAAY,CAAC,CAAC,EAAExoB,OAAO,CAAC;IAC1B,CAAC,CAAC;EACJ;EACAuoB,MAAM,CAACX,KAAK,GAAGD,OAAO,IAAI;IACxB,IAAI,SAAS,IAAIA,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEsC,OAAO,GAAGnD,OAAO,CAACmD,OAAO,CAAC;IACpE,IAAI,SAAS,IAAInD,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAExoB,OAAO,GAAG2nB,OAAO,CAAC3nB,OAAO,CAAC;IACpE,IAAI,OAAO,IAAI2nB,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEuC,KAAK,GAAGpD,OAAO,CAACoD,KAAK,CAAC;EAChE,CAAC;EACD,OAAO,CAAC/qB,OAAO,EAAE8qB,OAAO,EAAEC,KAAK,EAAEC,UAAU,CAAC;AAC9C;AACA,MAAME,cAAc,SAAS5D,eAAe,CAAC;EAC3C1jB,WAAWA,CAACpE,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IACP4mB,IAAI,CAAC,IAAI,EAAE5mB,OAAO,EAAEqrB,UAAU,EAAEF,iBAAiB,EAAEvK,cAAc,EAAE;MACjE0K,OAAO,EAAE,CAAC;MACV9qB,OAAO,EAAE,CAAC;MACV+qB,KAAK,EAAE;IACT,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,SAASI,mBAAmBA,CAACtoB,GAAG,EAAE;EAChC,IAAIuoB,aAAa;EACjB,IAAI/B,OAAO;EACX+B,aAAa,GAAG,IAAIF,cAAc,CAAC;IACjC1E,KAAK,EAAE;MACLsE,OAAO,EAAE,WAAWjoB,GAAG,CAAC,CAAC,CAAC;MAC1BkoB,KAAK,EAAE,SAASloB,GAAG,CAAC,CAAC;IACvB;EACF,CAAC,CAAC;EACF,OAAO;IACLqD,CAACA,CAAA,EAAG;MACFyf,gBAAgB,CAACyF,aAAa,CAACtI,EAAE,CAACqB,QAAQ,CAAC;IAC7C,CAAC;IACD2B,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBkF,eAAe,CAACwF,aAAa,EAAExrB,MAAM,EAAE8gB,MAAM,CAAC;MAC9C2I,OAAO,GAAG,IAAI;IAChB,CAAC;IACD/E,CAACA,CAACzhB,GAAG,EAAEwhB,KAAK,EAAE;MACZ,MAAMgH,qBAAqB,GAAG,CAAC,CAAC;MAChC,IAAIhH,KAAK,GAAG,WAAW,CAAC,EAAEgH,qBAAqB,CAACP,OAAO,GAAG,WAAWjoB,GAAG,CAAC,CAAC,CAAC;MAC3E,IAAIwhB,KAAK,GAAG,SAAS,CAAC,EAAEgH,qBAAqB,CAACN,KAAK,GAAG,SAASloB,GAAG,CAAC,CAAC,CAAC;MACrEuoB,aAAa,CAAC1D,IAAI,CAAC2D,qBAAqB,CAAC;IAC3C,CAAC;IACDxnB,CAACA,CAACmhB,KAAK,EAAE;MACP,IAAIqE,OAAO,EAAE;MACbvE,aAAa,CAACsG,aAAa,CAACtI,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MAC/CqE,OAAO,GAAG,IAAI;IAChB,CAAC;IACDlE,CAACA,CAACH,KAAK,EAAE;MACPE,cAAc,CAACkG,aAAa,CAACtI,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MAChDqE,OAAO,GAAG,KAAK;IACjB,CAAC;IACDvjB,CAACA,CAACkb,SAAS,EAAE;MACXiF,iBAAiB,CAACmF,aAAa,EAAEpK,SAAS,CAAC;IAC7C;EACF,CAAC;AACH;;AAEA;AACA,SAASsK,iBAAiBA,CAACzoB,GAAG,EAAE;EAC9B,IAAI0oB,kBAAkB;EACtB,IAAIlC,OAAO;EACXkC,kBAAkB,GAAG,IAAIb,oBAAoB,CAAC;IAC5ClE,KAAK,EAAE;MACL8D,UAAU,EAAE,cAAcznB,GAAG,CAAC,CAAC,CAAC;MAChCqB,IAAI,EAAE,QAAQrB,GAAG,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;EACF,OAAO;IACLqD,CAACA,CAAA,EAAG;MACFyf,gBAAgB,CAAC4F,kBAAkB,CAACzI,EAAE,CAACqB,QAAQ,CAAC;IAClD,CAAC;IACD2B,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBkF,eAAe,CAAC2F,kBAAkB,EAAE3rB,MAAM,EAAE8gB,MAAM,CAAC;MACnD2I,OAAO,GAAG,IAAI;IAChB,CAAC;IACD/E,CAACA,CAACzhB,GAAG,EAAEwhB,KAAK,EAAE;MACZ,MAAMmH,0BAA0B,GAAG,CAAC,CAAC;MACrC,IAAInH,KAAK,GAAG,cAAc,CAAC,EAAEmH,0BAA0B,CAAClB,UAAU,GAAG,cAAcznB,GAAG,CAAC,CAAC,CAAC;MACzF,IAAIwhB,KAAK,GAAG,QAAQ,CAAC,EAAEmH,0BAA0B,CAACtnB,IAAI,GAAG,QAAQrB,GAAG,CAAC,CAAC,CAAC;MACvE0oB,kBAAkB,CAAC7D,IAAI,CAAC8D,0BAA0B,CAAC;IACrD,CAAC;IACD3nB,CAACA,CAACmhB,KAAK,EAAE;MACP,IAAIqE,OAAO,EAAE;MACbvE,aAAa,CAACyG,kBAAkB,CAACzI,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MACpDqE,OAAO,GAAG,IAAI;IAChB,CAAC;IACDlE,CAACA,CAACH,KAAK,EAAE;MACPE,cAAc,CAACqG,kBAAkB,CAACzI,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MACrDqE,OAAO,GAAG,KAAK;IACjB,CAAC;IACDvjB,CAACA,CAACkb,SAAS,EAAE;MACXiF,iBAAiB,CAACsF,kBAAkB,EAAEvK,SAAS,CAAC;IAClD;EACF,CAAC;AACH;AACA,SAASyK,iBAAiBA,CAAC5oB,GAAG,EAAE;EAC9B,IAAI6oB,MAAM;EACV,IAAIC,CAAC;EACL,IAAItC,OAAO;EACX,IAAIuC,SAAS,GAAG,SAAS/oB,GAAG,CAAC,CAAC,CAAC,IAAIsoB,mBAAmB,CAACtoB,GAAG,CAAC;EAC3D,IAAIgpB,SAAS,GAAG,cAAchpB,GAAG,CAAC,CAAC,CAAC,IAAI,cAAcA,GAAG,CAAC,CAAC,CAAC,CAACipB,OAAO,IAAIR,iBAAiB,CAACzoB,GAAG,CAAC;EAC9F,OAAO;IACLqD,CAACA,CAAA,EAAG;MACFwlB,MAAM,GAAG1rB,OAAO,CAAC,QAAQ,CAAC;MAC1B,IAAI4rB,SAAS,EAAEA,SAAS,CAAC1lB,CAAC,CAAC,CAAC;MAC5BylB,CAAC,GAAGrK,KAAK,CAAC,CAAC;MACX,IAAIuK,SAAS,EAAEA,SAAS,CAAC3lB,CAAC,CAAC,CAAC;MAC5Bub,IAAI,CAACiK,MAAM,EAAE,OAAO,EAAE,iBAAiB,CAAC;IAC1C,CAAC;IACD5F,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBD,MAAM,CAAC7gB,MAAM,EAAE8rB,MAAM,EAAEhL,MAAM,CAAC;MAC9B,IAAIkL,SAAS,EAAEA,SAAS,CAAC9F,CAAC,CAAC4F,MAAM,EAAE,IAAI,CAAC;MACxCnL,MAAM,CAACmL,MAAM,EAAEC,CAAC,CAAC;MACjB,IAAIE,SAAS,EAAEA,SAAS,CAAC/F,CAAC,CAAC4F,MAAM,EAAE,IAAI,CAAC;MACxCrC,OAAO,GAAG,IAAI;IAChB,CAAC;IACD/E,CAACA,CAACzhB,GAAG,EAAE,CAACwhB,KAAK,CAAC,EAAE;MACd,KAAK,SAASxhB,GAAG,CAAC,CAAC,CAAC,EAAE;QACpB,IAAI+oB,SAAS,EAAE;UACbA,SAAS,CAACtH,CAAC,CAACzhB,GAAG,EAAEwhB,KAAK,CAAC;UACvB,IAAIA,KAAK,GAAG,SAAS,CAAC,EAAE;YACtBS,aAAa,CAAC8G,SAAS,EAAE,CAAC,CAAC;UAC7B;QACF,CAAC,MAAM;UACLA,SAAS,GAAGT,mBAAmB,CAACtoB,GAAG,CAAC;UACpC+oB,SAAS,CAAC1lB,CAAC,CAAC,CAAC;UACb4e,aAAa,CAAC8G,SAAS,EAAE,CAAC,CAAC;UAC3BA,SAAS,CAAC9F,CAAC,CAAC4F,MAAM,EAAEC,CAAC,CAAC;QACxB;MACF,CAAC,MAAM,IAAIC,SAAS,EAAE;QACpBhH,YAAY,CAAC,CAAC;QACdM,cAAc,CAAC0G,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM;UACpCA,SAAS,GAAG,IAAI;QAClB,CAAC,CAAC;QACF/G,YAAY,CAAC,CAAC;MAChB;MACA,KAAK,cAAchiB,GAAG,CAAC,CAAC,CAAC,IAAI,cAAcA,GAAG,CAAC,CAAC,CAAC,CAACipB,OAAO,EAAE;QACzD,IAAID,SAAS,EAAE;UACbA,SAAS,CAACvH,CAAC,CAACzhB,GAAG,EAAEwhB,KAAK,CAAC;UACvB,IAAIA,KAAK,GAAG,cAAc,CAAC,EAAE;YAC3BS,aAAa,CAAC+G,SAAS,EAAE,CAAC,CAAC;UAC7B;QACF,CAAC,MAAM;UACLA,SAAS,GAAGP,iBAAiB,CAACzoB,GAAG,CAAC;UAClCgpB,SAAS,CAAC3lB,CAAC,CAAC,CAAC;UACb4e,aAAa,CAAC+G,SAAS,EAAE,CAAC,CAAC;UAC3BA,SAAS,CAAC/F,CAAC,CAAC4F,MAAM,EAAE,IAAI,CAAC;QAC3B;MACF,CAAC,MAAM,IAAIG,SAAS,EAAE;QACpBjH,YAAY,CAAC,CAAC;QACdM,cAAc,CAAC2G,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM;UACpCA,SAAS,GAAG,IAAI;QAClB,CAAC,CAAC;QACFhH,YAAY,CAAC,CAAC;MAChB;IACF,CAAC;IACDhhB,CAACA,CAACmhB,KAAK,EAAE;MACP,IAAIqE,OAAO,EAAE;MACbvE,aAAa,CAAC8G,SAAS,CAAC;MACxB9G,aAAa,CAAC+G,SAAS,CAAC;MACxBxC,OAAO,GAAG,IAAI;IAChB,CAAC;IACDlE,CAACA,CAACH,KAAK,EAAE;MACPE,cAAc,CAAC0G,SAAS,CAAC;MACzB1G,cAAc,CAAC2G,SAAS,CAAC;MACzBxC,OAAO,GAAG,KAAK;IACjB,CAAC;IACDvjB,CAACA,CAACkb,SAAS,EAAE;MACX,IAAIA,SAAS,EAAEJ,MAAM,CAAC8K,MAAM,CAAC;MAC7B,IAAIE,SAAS,EAAEA,SAAS,CAAC9lB,CAAC,CAAC,CAAC;MAC5B,IAAI+lB,SAAS,EAAEA,SAAS,CAAC/lB,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC;AACH;AACA,SAASimB,UAAUA,CAACxD,MAAM,EAAEZ,OAAO,EAAEa,YAAY,EAAE;EACjD,IAAI;IACFsC,OAAO;IACP5mB;EACF,CAAC,GAAGyjB,OAAO;EACX,IAAIoD,KAAK,EAAET,UAAU;EACrB/B,MAAM,CAACX,KAAK,GAAGD,OAAO,IAAI;IACxB,IAAI,SAAS,IAAIA,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEsC,OAAO,GAAGnD,OAAO,CAACmD,OAAO,CAAC;IACpE,IAAI,MAAM,IAAInD,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEtkB,IAAI,GAAGyjB,OAAO,CAACzjB,IAAI,CAAC;EAC7D,CAAC;EACDqkB,MAAM,CAACzF,EAAE,CAAC3G,MAAM,GAAG,MAAM;IACvB,IAAIoM,MAAM,CAACzF,EAAE,CAACuB,KAAK,GAAG,QAAQ,CAAC,EAAE;MAC/B;QACEmE,YAAY,CAAC,CAAC,EAAEuC,KAAK,GAAG7mB,IAAI,CAAC1E,OAAO,CAACurB,KAAK,CAAC;QAC3CvC,YAAY,CAAC,CAAC,EAAE8B,UAAU,GAAGpmB,IAAI,CAAC1E,OAAO,CAAC8qB,UAAU,CAAC;MACvD;IACF;EACF,CAAC;EACD,OAAO,CAACQ,OAAO,EAAE5mB,IAAI,EAAE6mB,KAAK,EAAET,UAAU,CAAC;AAC3C;AACA,MAAM0B,eAAe,SAAS1E,eAAe,CAAC;EAC5C1jB,WAAWA,CAACpE,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IACP4mB,IAAI,CAAC,IAAI,EAAE5mB,OAAO,EAAEusB,UAAU,EAAEN,iBAAiB,EAAErL,cAAc,EAAE;MACjE0K,OAAO,EAAE,CAAC;MACV5mB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,SAAS+nB,iBAAiBA,CAACppB,GAAG,EAAE;EAC9B,IAAIqpB,GAAG;EACP,OAAO;IACLhmB,CAACA,CAAA,EAAG;MACFgmB,GAAG,GAAGlsB,OAAO,CAAC,KAAK,CAAC;MACpByhB,IAAI,CAACyK,GAAG,EAAE,OAAO,EAAE,eAAe,CAAC;MACnCzK,IAAI,CAACyK,GAAG,EAAE,IAAI,EAAE,iBAAiBrpB,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;IACDijB,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBD,MAAM,CAAC7gB,MAAM,EAAEssB,GAAG,EAAExL,MAAM,CAAC;MAC3B;MACA7d,GAAG,CAAC,CAAC,CAAC,CAACqpB,GAAG,CAAC;IACb,CAAC;IACD5H,CAACA,CAACzhB,GAAG,EAAE,CAACwhB,KAAK,CAAC,EAAE;MACd,IAAIA,KAAK,GAAG,iBAAiB,CAAC,EAAE;QAC9B5C,IAAI,CAACyK,GAAG,EAAE,IAAI,EAAE,iBAAiBrpB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1C;IACF,CAAC;IACDgB,CAAC,EAAE4b,IAAI;IACP0F,CAAC,EAAE1F,IAAI;IACP3Z,CAACA,CAACkb,SAAS,EAAE;MACX,IAAIA,SAAS,EAAEJ,MAAM,CAACsL,GAAG,CAAC;MAC1B;MACArpB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH;AACA,SAASspB,UAAUA,CAAC5D,MAAM,EAAEZ,OAAO,EAAEa,YAAY,EAAE;EACjD,IAAI;IACF4D,aAAa;IACbpsB,OAAO;IACPkE;EACF,CAAC,GAAGyjB,OAAO;EACX3E,WAAW,CAAC,MAAM;IAChB,IAAI;MACF5B;IACF,CAAC,GAAGld,IAAI,CAAC1E,OAAO;IAChB,IAAI6C,UAAU,CAAC+e,IAAI,CAAC,EAAE;MACpBA,IAAI,GAAGA,IAAI,CAACxiB,IAAI,CAACsF,IAAI,CAAC;IACxB;IACA,IAAI/B,eAAe,CAACif,IAAI,CAAC,EAAE;MACzBphB,OAAO,CAACwgB,WAAW,CAACY,IAAI,CAAC;IAC3B,CAAC,MAAM;MACLoH,YAAY,CAAC,CAAC,EAAExoB,OAAO,CAACooB,SAAS,GAAGhH,IAAI,EAAEphB,OAAO,CAAC;IACpD;EACF,CAAC,CAAC;EACF,SAASqsB,WAAWA,CAACpB,OAAO,EAAE;IAC5B9H,iBAAiB,CAAC8H,OAAO,GAAG,SAAS,GAAG,MAAM,CAAC,CAAC,MAAM;MACpDjrB,OAAO,GAAGirB,OAAO;MACjBzC,YAAY,CAAC,CAAC,EAAExoB,OAAO,CAAC;IAC1B,CAAC,CAAC;EACJ;EACAuoB,MAAM,CAACX,KAAK,GAAGD,OAAO,IAAI;IACxB,IAAI,eAAe,IAAIA,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAE4D,aAAa,GAAGzE,OAAO,CAACyE,aAAa,CAAC;IACtF,IAAI,SAAS,IAAIzE,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAExoB,OAAO,GAAG2nB,OAAO,CAAC3nB,OAAO,CAAC;IACpE,IAAI,MAAM,IAAI2nB,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEtkB,IAAI,GAAGyjB,OAAO,CAACzjB,IAAI,CAAC;EAC7D,CAAC;EACD,OAAO,CAAClE,OAAO,EAAEosB,aAAa,EAAEloB,IAAI,EAAEmoB,WAAW,CAAC;AACpD;AACA,MAAMC,aAAa,SAAShF,eAAe,CAAC;EAC1C1jB,WAAWA,CAACpE,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IACP4mB,IAAI,CAAC,IAAI,EAAE5mB,OAAO,EAAE2sB,UAAU,EAAEF,iBAAiB,EAAE7L,cAAc,EAAE;MACjEgM,aAAa,EAAE,CAAC;MAChBpsB,OAAO,EAAE,CAAC;MACVkE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,SAASqoB,iBAAiBA,CAAC1pB,GAAG,EAAE;EAC9B,IAAI2pB,cAAc;EAClB,IAAInD,OAAO;EACXmD,cAAc,GAAG,IAAIR,eAAe,CAAC;IACnCxF,KAAK,EAAE;MACLsE,OAAO,EAAE,WAAWjoB,GAAG,CAAC,CAAC,CAAC;MAC1BqB,IAAI,EAAE,QAAQrB,GAAG,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;EACF,OAAO;IACLqD,CAACA,CAAA,EAAG;MACFyf,gBAAgB,CAAC6G,cAAc,CAAC1J,EAAE,CAACqB,QAAQ,CAAC;IAC9C,CAAC;IACD2B,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBkF,eAAe,CAAC4G,cAAc,EAAE5sB,MAAM,EAAE8gB,MAAM,CAAC;MAC/C2I,OAAO,GAAG,IAAI;IAChB,CAAC;IACD/E,CAACA,CAACzhB,GAAG,EAAEwhB,KAAK,EAAE;MACZ,MAAMoI,sBAAsB,GAAG,CAAC,CAAC;MACjC,IAAIpI,KAAK,GAAG,WAAW,CAAC,EAAEoI,sBAAsB,CAAC3B,OAAO,GAAG,WAAWjoB,GAAG,CAAC,CAAC,CAAC;MAC5E,IAAIwhB,KAAK,GAAG,QAAQ,CAAC,EAAEoI,sBAAsB,CAACvoB,IAAI,GAAG,QAAQrB,GAAG,CAAC,CAAC,CAAC;MACnE2pB,cAAc,CAAC9E,IAAI,CAAC+E,sBAAsB,CAAC;IAC7C,CAAC;IACD5oB,CAACA,CAACmhB,KAAK,EAAE;MACP,IAAIqE,OAAO,EAAE;MACbvE,aAAa,CAAC0H,cAAc,CAAC1J,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MAChDqE,OAAO,GAAG,IAAI;IAChB,CAAC;IACDlE,CAACA,CAACH,KAAK,EAAE;MACPE,cAAc,CAACsH,cAAc,CAAC1J,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MACjDqE,OAAO,GAAG,KAAK;IACjB,CAAC;IACDvjB,CAACA,CAACkb,SAAS,EAAE;MACXiF,iBAAiB,CAACuG,cAAc,EAAExL,SAAS,CAAC;IAC9C;EACF,CAAC;AACH;;AAEA;AACA,SAAS0L,iBAAiBA,CAAC7pB,GAAG,EAAE;EAC9B,IAAI8pB,YAAY;EAChB,IAAItD,OAAO;EACXsD,YAAY,GAAG,IAAIL,aAAa,CAAC;IAC/B9F,KAAK,EAAE;MACL4F,aAAa,EAAE,iBAAiBvpB,GAAG,CAAC,CAAC,CAAC;MACtCqB,IAAI,EAAE,QAAQrB,GAAG,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;EACF,OAAO;IACLqD,CAACA,CAAA,EAAG;MACFyf,gBAAgB,CAACgH,YAAY,CAAC7J,EAAE,CAACqB,QAAQ,CAAC;IAC5C,CAAC;IACD2B,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBkF,eAAe,CAAC+G,YAAY,EAAE/sB,MAAM,EAAE8gB,MAAM,CAAC;MAC7C2I,OAAO,GAAG,IAAI;IAChB,CAAC;IACD/E,CAACA,CAACzhB,GAAG,EAAEwhB,KAAK,EAAE;MACZ,MAAMuI,oBAAoB,GAAG,CAAC,CAAC;MAC/B,IAAIvI,KAAK,GAAG,iBAAiB,CAAC,EAAEuI,oBAAoB,CAACR,aAAa,GAAG,iBAAiBvpB,GAAG,CAAC,CAAC,CAAC;MAC5F,IAAIwhB,KAAK,GAAG,QAAQ,CAAC,EAAEuI,oBAAoB,CAAC1oB,IAAI,GAAG,QAAQrB,GAAG,CAAC,CAAC,CAAC;MACjE8pB,YAAY,CAACjF,IAAI,CAACkF,oBAAoB,CAAC;IACzC,CAAC;IACD/oB,CAACA,CAACmhB,KAAK,EAAE;MACP,IAAIqE,OAAO,EAAE;MACbvE,aAAa,CAAC6H,YAAY,CAAC7J,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MAC9CqE,OAAO,GAAG,IAAI;IAChB,CAAC;IACDlE,CAACA,CAACH,KAAK,EAAE;MACPE,cAAc,CAACyH,YAAY,CAAC7J,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MAC/CqE,OAAO,GAAG,KAAK;IACjB,CAAC;IACDvjB,CAACA,CAACkb,SAAS,EAAE;MACXiF,iBAAiB,CAAC0G,YAAY,EAAE3L,SAAS,CAAC;IAC5C;EACF,CAAC;AACH;;AAEA;AACA,SAAS6L,iBAAiBA,CAAChqB,GAAG,EAAE;EAC9B,IAAIiqB,cAAc;EAClB,IAAIzD,OAAO;EACXyD,cAAc,GAAG,IAAI7C,eAAe,CAAC;IACnCzD,KAAK,EAAE;MACLtiB,IAAI,EAAE,QAAQrB,GAAG,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;EACF,OAAO;IACLqD,CAACA,CAAA,EAAG;MACFyf,gBAAgB,CAACmH,cAAc,CAAChK,EAAE,CAACqB,QAAQ,CAAC;IAC9C,CAAC;IACD2B,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBkF,eAAe,CAACkH,cAAc,EAAEltB,MAAM,EAAE8gB,MAAM,CAAC;MAC/C2I,OAAO,GAAG,IAAI;IAChB,CAAC;IACD/E,CAACA,CAACzhB,GAAG,EAAEwhB,KAAK,EAAE;MACZ,MAAM0I,sBAAsB,GAAG,CAAC,CAAC;MACjC,IAAI1I,KAAK,GAAG,QAAQ,CAAC,EAAE0I,sBAAsB,CAAC7oB,IAAI,GAAG,QAAQrB,GAAG,CAAC,CAAC,CAAC;MACnEiqB,cAAc,CAACpF,IAAI,CAACqF,sBAAsB,CAAC;IAC7C,CAAC;IACDlpB,CAACA,CAACmhB,KAAK,EAAE;MACP,IAAIqE,OAAO,EAAE;MACbvE,aAAa,CAACgI,cAAc,CAAChK,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MAChDqE,OAAO,GAAG,IAAI;IAChB,CAAC;IACDlE,CAACA,CAACH,KAAK,EAAE;MACPE,cAAc,CAAC4H,cAAc,CAAChK,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MACjDqE,OAAO,GAAG,KAAK;IACjB,CAAC;IACDvjB,CAACA,CAACkb,SAAS,EAAE;MACXiF,iBAAiB,CAAC6G,cAAc,EAAE9L,SAAS,CAAC;IAC9C;EACF,CAAC;AACH;AACA,SAASgM,iBAAiBA,CAACnqB,GAAG,EAAE;EAC9B,IAAIqpB,GAAG;EACP,IAAIe,SAAS,GAAG,CAAC1qB,WAAW,EAAE,QAAQM,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACurB,KAAK,CAAC,IAAI,QAAQloB,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAAC8qB,UAAU,IAAI,QAAQznB,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAAC8qB,UAAU,CAACwB,OAAO;EAC7I,IAAIoB,EAAE;EACN,IAAIC,SAAS,GAAG,CAAC5qB,WAAW,EAAE,QAAQM,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAAC4hB,IAAI,CAAC;EAC1D,IAAIgM,EAAE;EACN,IAAIC,OAAO,GAAGhuB,KAAK,CAACC,OAAO,EAAE,QAAQuD,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACwqB,OAAO,CAAC,IAAI,QAAQnnB,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACwqB,OAAO,CAAClmB,MAAM;EACrG,IAAIulB,OAAO;EACX,IAAIuC,SAAS,GAAGqB,SAAS,IAAIV,iBAAiB,CAAC1pB,GAAG,CAAC;EACnD,IAAIgpB,SAAS,GAAGsB,SAAS,IAAIT,iBAAiB,CAAC7pB,GAAG,CAAC;EACnD,IAAIyqB,SAAS,GAAGD,OAAO,IAAIR,iBAAiB,CAAChqB,GAAG,CAAC;EACjD,OAAO;IACLqD,CAACA,CAAA,EAAG;MACFgmB,GAAG,GAAGlsB,OAAO,CAAC,KAAK,CAAC;MACpB,IAAI4rB,SAAS,EAAEA,SAAS,CAAC1lB,CAAC,CAAC,CAAC;MAC5BgnB,EAAE,GAAG5L,KAAK,CAAC,CAAC;MACZ,IAAIuK,SAAS,EAAEA,SAAS,CAAC3lB,CAAC,CAAC,CAAC;MAC5BknB,EAAE,GAAG9L,KAAK,CAAC,CAAC;MACZ,IAAIgM,SAAS,EAAEA,SAAS,CAACpnB,CAAC,CAAC,CAAC;MAC5Bub,IAAI,CAACyK,GAAG,EAAE,OAAO,EAAE,kBAAkB,CAAC;IACxC,CAAC;IACDpG,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBD,MAAM,CAAC7gB,MAAM,EAAEssB,GAAG,EAAExL,MAAM,CAAC;MAC3B,IAAIkL,SAAS,EAAEA,SAAS,CAAC9F,CAAC,CAACoG,GAAG,EAAE,IAAI,CAAC;MACrC3L,MAAM,CAAC2L,GAAG,EAAEgB,EAAE,CAAC;MACf,IAAIrB,SAAS,EAAEA,SAAS,CAAC/F,CAAC,CAACoG,GAAG,EAAE,IAAI,CAAC;MACrC3L,MAAM,CAAC2L,GAAG,EAAEkB,EAAE,CAAC;MACf,IAAIE,SAAS,EAAEA,SAAS,CAACxH,CAAC,CAACoG,GAAG,EAAE,IAAI,CAAC;MACrC7C,OAAO,GAAG,IAAI;IAChB,CAAC;IACD/E,CAACA,CAACzhB,GAAG,EAAE,CAACwhB,KAAK,CAAC,EAAE;MACd,IAAIA,KAAK,GAAG,QAAQ,CAAC,EAAE4I,SAAS,GAAG,CAAC1qB,WAAW,EAAE,QAAQM,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACurB,KAAK,CAAC,IAAI,QAAQloB,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAAC8qB,UAAU,IAAI,QAAQznB,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAAC8qB,UAAU,CAACwB,OAAO;MAChK,IAAImB,SAAS,EAAE;QACb,IAAIrB,SAAS,EAAE;UACbA,SAAS,CAACtH,CAAC,CAACzhB,GAAG,EAAEwhB,KAAK,CAAC;UACvB,IAAIA,KAAK,GAAG,QAAQ,CAAC,EAAE;YACrBS,aAAa,CAAC8G,SAAS,EAAE,CAAC,CAAC;UAC7B;QACF,CAAC,MAAM;UACLA,SAAS,GAAGW,iBAAiB,CAAC1pB,GAAG,CAAC;UAClC+oB,SAAS,CAAC1lB,CAAC,CAAC,CAAC;UACb4e,aAAa,CAAC8G,SAAS,EAAE,CAAC,CAAC;UAC3BA,SAAS,CAAC9F,CAAC,CAACoG,GAAG,EAAEgB,EAAE,CAAC;QACtB;MACF,CAAC,MAAM,IAAItB,SAAS,EAAE;QACpBhH,YAAY,CAAC,CAAC;QACdM,cAAc,CAAC0G,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM;UACpCA,SAAS,GAAG,IAAI;QAClB,CAAC,CAAC;QACF/G,YAAY,CAAC,CAAC;MAChB;MACA,IAAIR,KAAK,GAAG,QAAQ,CAAC,EAAE8I,SAAS,GAAG,CAAC5qB,WAAW,EAAE,QAAQM,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAAC4hB,IAAI,CAAC;MAC7E,IAAI+L,SAAS,EAAE;QACb,IAAItB,SAAS,EAAE;UACbA,SAAS,CAACvH,CAAC,CAACzhB,GAAG,EAAEwhB,KAAK,CAAC;UACvB,IAAIA,KAAK,GAAG,QAAQ,CAAC,EAAE;YACrBS,aAAa,CAAC+G,SAAS,EAAE,CAAC,CAAC;UAC7B;QACF,CAAC,MAAM;UACLA,SAAS,GAAGa,iBAAiB,CAAC7pB,GAAG,CAAC;UAClCgpB,SAAS,CAAC3lB,CAAC,CAAC,CAAC;UACb4e,aAAa,CAAC+G,SAAS,EAAE,CAAC,CAAC;UAC3BA,SAAS,CAAC/F,CAAC,CAACoG,GAAG,EAAEkB,EAAE,CAAC;QACtB;MACF,CAAC,MAAM,IAAIvB,SAAS,EAAE;QACpBjH,YAAY,CAAC,CAAC;QACdM,cAAc,CAAC2G,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM;UACpCA,SAAS,GAAG,IAAI;QAClB,CAAC,CAAC;QACFhH,YAAY,CAAC,CAAC;MAChB;MACA,IAAIR,KAAK,GAAG,QAAQ,CAAC,EAAEgJ,OAAO,GAAGhuB,KAAK,CAACC,OAAO,EAAE,QAAQuD,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACwqB,OAAO,CAAC,IAAI,QAAQnnB,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACwqB,OAAO,CAAClmB,MAAM;MACxH,IAAIupB,OAAO,EAAE;QACX,IAAIC,SAAS,EAAE;UACbA,SAAS,CAAChJ,CAAC,CAACzhB,GAAG,EAAEwhB,KAAK,CAAC;UACvB,IAAIA,KAAK,GAAG,QAAQ,CAAC,EAAE;YACrBS,aAAa,CAACwI,SAAS,EAAE,CAAC,CAAC;UAC7B;QACF,CAAC,MAAM;UACLA,SAAS,GAAGT,iBAAiB,CAAChqB,GAAG,CAAC;UAClCyqB,SAAS,CAACpnB,CAAC,CAAC,CAAC;UACb4e,aAAa,CAACwI,SAAS,EAAE,CAAC,CAAC;UAC3BA,SAAS,CAACxH,CAAC,CAACoG,GAAG,EAAE,IAAI,CAAC;QACxB;MACF,CAAC,MAAM,IAAIoB,SAAS,EAAE;QACpB1I,YAAY,CAAC,CAAC;QACdM,cAAc,CAACoI,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM;UACpCA,SAAS,GAAG,IAAI;QAClB,CAAC,CAAC;QACFzI,YAAY,CAAC,CAAC;MAChB;IACF,CAAC;IACDhhB,CAACA,CAACmhB,KAAK,EAAE;MACP,IAAIqE,OAAO,EAAE;MACbvE,aAAa,CAAC8G,SAAS,CAAC;MACxB9G,aAAa,CAAC+G,SAAS,CAAC;MACxB/G,aAAa,CAACwI,SAAS,CAAC;MACxBjE,OAAO,GAAG,IAAI;IAChB,CAAC;IACDlE,CAACA,CAACH,KAAK,EAAE;MACPE,cAAc,CAAC0G,SAAS,CAAC;MACzB1G,cAAc,CAAC2G,SAAS,CAAC;MACzB3G,cAAc,CAACoI,SAAS,CAAC;MACzBjE,OAAO,GAAG,KAAK;IACjB,CAAC;IACDvjB,CAACA,CAACkb,SAAS,EAAE;MACX,IAAIA,SAAS,EAAEJ,MAAM,CAACsL,GAAG,CAAC;MAC1B,IAAIN,SAAS,EAAEA,SAAS,CAAC9lB,CAAC,CAAC,CAAC;MAC5B,IAAI+lB,SAAS,EAAEA,SAAS,CAAC/lB,CAAC,CAAC,CAAC;MAC5B,IAAIwnB,SAAS,EAAEA,SAAS,CAACxnB,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC;AACH;AACA,SAASynB,UAAUA,CAAChF,MAAM,EAAEZ,OAAO,EAAEa,YAAY,EAAE;EACjD,IAAI;IACF4D,aAAa;IACbtB,OAAO;IACP5mB;EACF,CAAC,GAAGyjB,OAAO;EACXY,MAAM,CAACX,KAAK,GAAGD,OAAO,IAAI;IACxB,IAAI,eAAe,IAAIA,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAE4D,aAAa,GAAGzE,OAAO,CAACyE,aAAa,CAAC;IACtF,IAAI,SAAS,IAAIzE,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEsC,OAAO,GAAGnD,OAAO,CAACmD,OAAO,CAAC;IACpE,IAAI,MAAM,IAAInD,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEtkB,IAAI,GAAGyjB,OAAO,CAACzjB,IAAI,CAAC;EAC7D,CAAC;EACD,OAAO,CAACkoB,aAAa,EAAEtB,OAAO,EAAE5mB,IAAI,CAAC;AACvC;AACA,MAAMspB,gBAAgB,SAASlG,eAAe,CAAC;EAC7C1jB,WAAWA,CAACpE,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IACP4mB,IAAI,CAAC,IAAI,EAAE5mB,OAAO,EAAE+tB,UAAU,EAAEP,iBAAiB,EAAE5M,cAAc,EAAE;MACjEgM,aAAa,EAAE,CAAC;MAChBtB,OAAO,EAAE,CAAC;MACV5mB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,SAASupB,eAAeA,CAAC5qB,GAAG,EAAE;EAC5B,IAAIqpB,GAAG;EACP,OAAO;IACLhmB,CAACA,CAAA,EAAG;MACFgmB,GAAG,GAAGlsB,OAAO,CAAC,KAAK,CAAC;MACpByhB,IAAI,CAACyK,GAAG,EAAE,OAAO,EAAE,gBAAgB,CAAC;MACpCzK,IAAI,CAACyK,GAAG,EAAE,mBAAmB,EAAE,EAAE,CAAC;IACpC,CAAC;IACDpG,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBD,MAAM,CAAC7gB,MAAM,EAAEssB,GAAG,EAAExL,MAAM,CAAC;IAC7B,CAAC;IACD5a,CAACA,CAACkb,SAAS,EAAE;MACX,IAAIA,SAAS,EAAEJ,MAAM,CAACsL,GAAG,CAAC;IAC5B;EACF,CAAC;AACH;AACA,SAASwB,iBAAiBA,CAAC7qB,GAAG,EAAE;EAC9B,IAAIqpB,GAAG;EACP,IAAIP,CAAC;EACL,IAAIgC,eAAe;EACnB,IAAIC,0BAA0B;EAC9B,IAAIC,yBAAyB;EAC7B,IAAIxE,OAAO;EACX,IAAIpB,OAAO;EACX,IAAIC,OAAO;EACX,IAAI4B,QAAQ,GAAG,QAAQjnB,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAAC0O,KAAK,IAAI,QAAQrL,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACgG,QAAQ,IAAI,QAAQ3C,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACgG,QAAQ,CAACxF,OAAO,IAAI,QAAQ6C,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACgG,QAAQ,CAAC9C,EAAE,IAAI+qB,eAAe,CAAC,CAAC;EACpLE,eAAe,GAAG,IAAIH,gBAAgB,CAAC;IACrChH,KAAK,EAAE;MACL4F,aAAa,EAAE,iBAAiBvpB,GAAG,CAAC,CAAC,CAAC;MACtCioB,OAAO,EAAE,WAAWjoB,GAAG,CAAC,CAAC,CAAC;MAC1BqB,IAAI,EAAE,QAAQrB,GAAG,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;EACF,IAAIirB,UAAU,GAAG,CAAC;IAChB,kBAAkB,EAAEF,0BAA0B,GAAG,CAACrrB,WAAW,EAAE,QAAQM,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAAC4hB,IAAI,CAAC,GAAG,iBAAiBve,GAAG,CAAC,CAAC,CAAC,GAAG;EAC1H,CAAC,EAAE;IACD,iBAAiB,EAAEgrB,yBAAyB,GAAG,QAAQhrB,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACurB,KAAK,GAAG,WAAWloB,GAAG,CAAC,CAAC,CAAC,GAAG;EACpG,CAAC,EAAE,cAAcA,GAAG,CAAC,CAAC,CAAC,EAAE;IACvBkrB,IAAI,EAAE;EACR,CAAC,EAAE;IACDC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,KAAK,IAAIpqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiqB,UAAU,CAAChqB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC7CoqB,QAAQ,GAAGvoB,MAAM,CAACuoB,QAAQ,EAAEH,UAAU,CAACjqB,CAAC,CAAC,CAAC;EAC5C;EACA,OAAO;IACLqC,CAACA,CAAA,EAAG;MACFgmB,GAAG,GAAGlsB,OAAO,CAAC,KAAK,CAAC;MACpB,IAAI8pB,QAAQ,EAAEA,QAAQ,CAAC5jB,CAAC,CAAC,CAAC;MAC1BylB,CAAC,GAAGrK,KAAK,CAAC,CAAC;MACXqE,gBAAgB,CAACgI,eAAe,CAAC7K,EAAE,CAACqB,QAAQ,CAAC;MAC7CpC,cAAc,CAACmK,GAAG,EAAE+B,QAAQ,CAAC;MAC7B1L,YAAY,CAAC2J,GAAG,EAAE,0BAA0B,EAAE,iBAAiBrpB,GAAG,CAAC,CAAC,CAAC,CAAC;MACtE0f,YAAY,CAAC2J,GAAG,EAAE,oBAAoB,EAAE,YAAYrpB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC3D0f,YAAY,CAAC2J,GAAG,EAAE,kBAAkB,EAAE,IAAI,CAAC;IAC7C,CAAC;IACDpG,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBD,MAAM,CAAC7gB,MAAM,EAAEssB,GAAG,EAAExL,MAAM,CAAC;MAC3B,IAAIoJ,QAAQ,EAAEA,QAAQ,CAAChE,CAAC,CAACoG,GAAG,EAAE,IAAI,CAAC;MACnC3L,MAAM,CAAC2L,GAAG,EAAEP,CAAC,CAAC;MACd/F,eAAe,CAAC+H,eAAe,EAAEzB,GAAG,EAAE,IAAI,CAAC;MAC3C;MACArpB,GAAG,CAAC,EAAE,CAAC,CAACqpB,GAAG,CAAC;MACZ7C,OAAO,GAAG,IAAI;MACd,IAAI,CAACpB,OAAO,EAAE;QACZC,OAAO,GAAG1G,MAAM,CAAC0K,GAAG,EAAE,SAAS,EAAE,iBAAiBrpB,GAAG,CAAC,CAAC,CAAC,CAAC;QACzDolB,OAAO,GAAG,IAAI;MAChB;IACF,CAAC;IACD3D,CAACA,CAACzhB,GAAG,EAAE,CAACwhB,KAAK,CAAC,EAAE;MACd,KAAK,QAAQxhB,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAAC0O,KAAK,IAAI,QAAQrL,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACgG,QAAQ,IAAI,QAAQ3C,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACgG,QAAQ,CAACxF,OAAO,IAAI,QAAQ6C,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACgG,QAAQ,CAAC9C,EAAE,EAAE;QACrJ,IAAIonB,QAAQ,EAAE,CAAC,KAAM;UACnBA,QAAQ,GAAG2D,eAAe,CAAC,CAAC;UAC5B3D,QAAQ,CAAC5jB,CAAC,CAAC,CAAC;UACZ4jB,QAAQ,CAAChE,CAAC,CAACoG,GAAG,EAAEP,CAAC,CAAC;QACpB;MACF,CAAC,MAAM,IAAI7B,QAAQ,EAAE;QACnBA,QAAQ,CAAChkB,CAAC,CAAC,CAAC,CAAC;QACbgkB,QAAQ,GAAG,IAAI;MACjB;MACA,MAAMoE,uBAAuB,GAAG,CAAC,CAAC;MAClC,IAAI7J,KAAK,GAAG,iBAAiB,CAAC,EAAE6J,uBAAuB,CAAC9B,aAAa,GAAG,iBAAiBvpB,GAAG,CAAC,CAAC,CAAC;MAC/F,IAAIwhB,KAAK,GAAG,WAAW,CAAC,EAAE6J,uBAAuB,CAACpD,OAAO,GAAG,WAAWjoB,GAAG,CAAC,CAAC,CAAC;MAC7E,IAAIwhB,KAAK,GAAG,QAAQ,EAAE,EAAE6J,uBAAuB,CAAChqB,IAAI,GAAG,QAAQrB,GAAG,CAAC,CAAC,CAAC;MACrE8qB,eAAe,CAACjG,IAAI,CAACwG,uBAAuB,CAAC;MAC7CnM,cAAc,CAACmK,GAAG,EAAE+B,QAAQ,GAAG7I,iBAAiB,CAAC0I,UAAU,EAAE,CAAC,CAAC,CAACzE,OAAO,IAAIhF,KAAK,GAAG,uBAAuB,EAAE,IAAIuJ,0BAA0B,MAAMA,0BAA0B,GAAG,CAACrrB,WAAW,EAAE,QAAQM,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAAC4hB,IAAI,CAAC,GAAG,iBAAiBve,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK;QAC5P,kBAAkB,EAAE+qB;MACtB,CAAC,EAAE,CAAC,CAACvE,OAAO,IAAIhF,KAAK,GAAG,iBAAiB,EAAE,IAAIwJ,yBAAyB,MAAMA,yBAAyB,GAAG,QAAQhrB,GAAG,CAAC,CAAC,CAAC,CAACrD,OAAO,CAACurB,KAAK,GAAG,WAAWloB,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK;QACrK,iBAAiB,EAAEgrB;MACrB,CAAC,EAAExJ,KAAK,GAAG,cAAc,CAAC,IAAI,cAAcxhB,GAAG,CAAC,CAAC,CAAC,EAAE;QAClDkrB,IAAI,EAAE;MACR,CAAC,EAAE;QACDC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC;MACJzL,YAAY,CAAC2J,GAAG,EAAE,0BAA0B,EAAE,iBAAiBrpB,GAAG,CAAC,CAAC,CAAC,CAAC;MACtE0f,YAAY,CAAC2J,GAAG,EAAE,oBAAoB,EAAE,YAAYrpB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC3D0f,YAAY,CAAC2J,GAAG,EAAE,kBAAkB,EAAE,IAAI,CAAC;IAC7C,CAAC;IACDroB,CAACA,CAACmhB,KAAK,EAAE;MACP,IAAIqE,OAAO,EAAE;MACbvE,aAAa,CAAC6I,eAAe,CAAC7K,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MACjDqE,OAAO,GAAG,IAAI;IAChB,CAAC;IACDlE,CAACA,CAACH,KAAK,EAAE;MACPE,cAAc,CAACyI,eAAe,CAAC7K,EAAE,CAACqB,QAAQ,EAAEa,KAAK,CAAC;MAClDqE,OAAO,GAAG,KAAK;IACjB,CAAC;IACDvjB,CAACA,CAACkb,SAAS,EAAE;MACX,IAAIA,SAAS,EAAEJ,MAAM,CAACsL,GAAG,CAAC;MAC1B,IAAIpC,QAAQ,EAAEA,QAAQ,CAAChkB,CAAC,CAAC,CAAC;MAC1BmgB,iBAAiB,CAAC0H,eAAe,CAAC;MAClC;MACA9qB,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;MACbolB,OAAO,GAAG,KAAK;MACfC,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH;AACA,MAAMiG,OAAO,GAAG,CAAC;AACjB,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,WAAW,GAAG,EAAE;AACtB,SAASC,eAAeA,CAAC7F,OAAO,EAAE;EAChC,OAAOA,OAAO,CAACzgB,KAAK,CAAC,GAAG,CAAC,CAAC3H,MAAM,CAACkuB,SAAS,IAAI,CAAC,CAACA,SAAS,CAAC1qB,MAAM,CAAC;AACnE;AACA,SAAS2qB,UAAUA,CAAClG,MAAM,EAAEZ,OAAO,EAAEa,YAAY,EAAE;EACjD,IAAI;IACFkG,WAAW;IACX1uB,OAAO;IACPosB,aAAa;IACbuC,qBAAqB;IACrBC,iBAAiB;IACjB9D,OAAO;IACP+D,oBAAoB;IACpB3qB,IAAI;IACJ4qB;EACF,CAAC,GAAGnH,OAAO;EACX,IAAIoH,aAAa,EAAEC,QAAQ,EAAEtG,OAAO;EACpC,MAAMtK,UAAU,GAAGA,CAAA,KAAMpe,OAAO;EAChC6iB,OAAO,CAAC,MAAM;IACZ;IACA2F,YAAY,CAAC,CAAC,EAAEsG,UAAU,GAAG;MAC3B,CAAC,QAAQJ,WAAW,kBAAkB,GAAGxqB,IAAI,CAAC+qB;IAChD,CAAC,CAAC;IACFzG,YAAY,CAAC,CAAC,EAAEoG,iBAAiB,GAAG5uB,OAAO,CAACkvB,gBAAgB,CAAC,sIAAsI,CAAC,CAAC;IACrM1G,YAAY,CAAC,CAAC,EAAEmG,qBAAqB,GAAGC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC7DpG,YAAY,CAAC,EAAE,EAAEqG,oBAAoB,GAAGD,iBAAiB,CAACA,iBAAiB,CAAC9qB,MAAM,GAAG,CAAC,CAAC,CAAC;EAC1F,CAAC,CAAC;EACFkf,WAAW,CAAC,MAAM;IAChB,IAAI0F,OAAO,KAAKxkB,IAAI,CAAC1E,OAAO,CAACkpB,OAAO,EAAE;MACpCyG,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,CAAC;EACF,SAASA,oBAAoBA,CAAA,EAAG;IAC9BC,aAAa,CAAC1G,OAAO,CAAC;IACtBA,OAAO,GAAGxkB,IAAI,CAAC1E,OAAO,CAACkpB,OAAO;IAC9B2G,UAAU,CAAC3G,OAAO,CAAC;EACrB;EACA,SAAS0G,aAAaA,CAAC1G,OAAO,EAAE;IAC9B,IAAIpmB,QAAQ,CAAComB,OAAO,CAAC,EAAE;MACrB,MAAM4G,UAAU,GAAGf,eAAe,CAAC7F,OAAO,CAAC;MAC3C,IAAI4G,UAAU,CAACxrB,MAAM,EAAE;QACrB9D,OAAO,CAACqe,SAAS,CAACkR,MAAM,CAAC,GAAGD,UAAU,CAAC;MACzC;IACF;EACF;EACA,SAASD,UAAUA,CAAC3G,OAAO,EAAE;IAC3B,IAAIpmB,QAAQ,CAAComB,OAAO,CAAC,EAAE;MACrB,MAAM8G,UAAU,GAAGjB,eAAe,CAAC7F,OAAO,CAAC;MAC3C,IAAI8G,UAAU,CAAC1rB,MAAM,EAAE;QACrB9D,OAAO,CAACqe,SAAS,CAACC,GAAG,CAAC,GAAGkR,UAAU,CAAC;MACtC;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,aAAa,GAAG3qB,CAAC,IAAI;IACzB,MAAM;MACJL;IACF,CAAC,GAAGP,IAAI;IACR,QAAQY,CAAC,CAAC4qB,OAAO;MACf,KAAKvB,OAAO;QACV,IAAIS,iBAAiB,CAAC9qB,MAAM,KAAK,CAAC,EAAE;UAClCgB,CAAC,CAAC0lB,cAAc,CAAC,CAAC;UAClB;QACF;QACA;QACA,IAAI1lB,CAAC,CAAC6qB,QAAQ,EAAE;UACd,IAAI/qB,QAAQ,CAACgrB,aAAa,KAAKjB,qBAAqB,IAAI/pB,QAAQ,CAACgrB,aAAa,CAACvR,SAAS,CAACwR,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YACrH/qB,CAAC,CAAC0lB,cAAc,CAAC,CAAC;YAClBqE,oBAAoB,CAAC9P,KAAK,CAAC,CAAC;UAC9B;QACF,CAAC,MAAM;UACL,IAAIna,QAAQ,CAACgrB,aAAa,KAAKf,oBAAoB,EAAE;YACnD/pB,CAAC,CAAC0lB,cAAc,CAAC,CAAC;YAClBmE,qBAAqB,CAAC5P,KAAK,CAAC,CAAC;UAC/B;QACF;QACA;MACF,KAAKqP,OAAO;QACV,IAAI3pB,IAAI,CAACjF,OAAO,CAACswB,SAAS,EAAE;UAC1BhrB,CAAC,CAACirB,eAAe,CAAC,CAAC;UACnB7rB,IAAI,CAACumB,MAAM,CAAC,CAAC;QACf;QACA;MACF,KAAK4D,UAAU;QACb,IAAI5pB,IAAI,CAACjF,OAAO,CAACwwB,kBAAkB,EAAE;UACnClrB,CAAC,CAACirB,eAAe,CAAC,CAAC;UACnBtrB,IAAI,CAACwrB,IAAI,CAAC,CAAC;QACb;QACA;MACF,KAAK3B,WAAW;QACd,IAAI7pB,IAAI,CAACjF,OAAO,CAACwwB,kBAAkB,EAAE;UACnClrB,CAAC,CAACirB,eAAe,CAAC,CAAC;UACnBtrB,IAAI,CAAC3C,IAAI,CAAC,CAAC;QACb;QACA;IACJ;EACF,CAAC;EACD,SAASuqB,WAAWA,CAACpB,OAAO,EAAE;IAC5B9H,iBAAiB,CAAC8H,OAAO,GAAG,SAAS,GAAG,MAAM,CAAC,CAAC,MAAM;MACpDjrB,OAAO,GAAGirB,OAAO;MACjBzC,YAAY,CAAC,CAAC,EAAExoB,OAAO,CAAC;IAC1B,CAAC,CAAC;EACJ;EACAuoB,MAAM,CAACX,KAAK,GAAGD,OAAO,IAAI;IACxB,IAAI,aAAa,IAAIA,OAAO,EAAEa,YAAY,CAAC,EAAE,EAAEkG,WAAW,GAAG/G,OAAO,CAAC+G,WAAW,CAAC;IACjF,IAAI,SAAS,IAAI/G,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAExoB,OAAO,GAAG2nB,OAAO,CAAC3nB,OAAO,CAAC;IACpE,IAAI,eAAe,IAAI2nB,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAE4D,aAAa,GAAGzE,OAAO,CAACyE,aAAa,CAAC;IACtF,IAAI,uBAAuB,IAAIzE,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEmG,qBAAqB,GAAGhH,OAAO,CAACgH,qBAAqB,CAAC;IAC9G,IAAI,mBAAmB,IAAIhH,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEoG,iBAAiB,GAAGjH,OAAO,CAACiH,iBAAiB,CAAC;IAClG,IAAI,SAAS,IAAIjH,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEsC,OAAO,GAAGnD,OAAO,CAACmD,OAAO,CAAC;IACpE,IAAI,sBAAsB,IAAInD,OAAO,EAAEa,YAAY,CAAC,EAAE,EAAEqG,oBAAoB,GAAGlH,OAAO,CAACkH,oBAAoB,CAAC;IAC5G,IAAI,MAAM,IAAIlH,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEtkB,IAAI,GAAGyjB,OAAO,CAACzjB,IAAI,CAAC;IAC3D,IAAI,YAAY,IAAIyjB,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAEsG,UAAU,GAAGnH,OAAO,CAACmH,UAAU,CAAC;EAC/E,CAAC;EACDvG,MAAM,CAACzF,EAAE,CAAC3G,MAAM,GAAG,MAAM;IACvB,IAAIoM,MAAM,CAACzF,EAAE,CAACuB,KAAK,GAAG,QAAQ,EAAE,EAAE;MAChC;QACEmE,YAAY,CAAC,CAAC,EAAEuG,aAAa,GAAG7qB,IAAI,CAAC1E,OAAO,IAAI0E,IAAI,CAAC1E,OAAO,CAAC8qB,UAAU,IAAIpmB,IAAI,CAAC1E,OAAO,CAAC8qB,UAAU,CAACwB,OAAO,CAAC;QAC3GtD,YAAY,CAAC,CAAC,EAAEwG,QAAQ,GAAG9qB,IAAI,CAAC1E,OAAO,IAAI0E,IAAI,CAAC1E,OAAO,CAACurB,KAAK,CAAC;MAChE;IACF;EACF,CAAC;EACD,OAAO,CAAC/qB,OAAO,EAAE8uB,UAAU,EAAE1C,aAAa,EAAEtB,OAAO,EAAE5mB,IAAI,EAAE6qB,aAAa,EAAEC,QAAQ,EAAES,aAAa,EAAEd,qBAAqB,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEH,WAAW,EAAEtQ,UAAU,EAAEiO,WAAW,CAAC;AAC1M;AACA,MAAM6D,gBAAgB,SAAS5I,eAAe,CAAC;EAC7C1jB,WAAWA,CAACpE,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IACP4mB,IAAI,CAAC,IAAI,EAAE5mB,OAAO,EAAEivB,UAAU,EAAEf,iBAAiB,EAAEtN,cAAc,EAAE;MACjEsO,WAAW,EAAE,EAAE;MACf1uB,OAAO,EAAE,CAAC;MACVosB,aAAa,EAAE,CAAC;MAChBuC,qBAAqB,EAAE,CAAC;MACxBC,iBAAiB,EAAE,CAAC;MACpB9D,OAAO,EAAE,CAAC;MACV+D,oBAAoB,EAAE,EAAE;MACxB3qB,IAAI,EAAE,CAAC;MACP4qB,UAAU,EAAE,CAAC;MACb1Q,UAAU,EAAE;IACd,CAAC,CAAC;EACJ;EACA,IAAIA,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC0E,EAAE,CAACjgB,GAAG,CAAC,EAAE,CAAC;EACxB;AACF;;AAEA;AACA;AACA;AACA;AACA,MAAMstB,IAAI,SAAS1tB,OAAO,CAAC;EACzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEmB,WAAWA,CAACa,IAAI,EAAEjF,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9B,KAAK,CAACiF,IAAI,EAAEjF,OAAO,CAAC;IACpB,IAAI,CAACiF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACiqB,WAAW,GAAG,IAAI,CAACjqB,IAAI,CAACjF,OAAO,GAAG4F,eAAe,CAAC,IAAI,CAACX,IAAI,CAACjF,OAAO,CAACkvB,WAAW,CAAC,GAAG,EAAE;IAC1F,IAAI,CAAC0B,MAAM,GAAG3rB,IAAI,CAAC2rB,MAAM;;IAEzB;AACJ;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B5sB,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI,CAAC6sB,WAAW,CAAC9wB,OAAO,CAAC;IACzB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEirB,MAAMA,CAAA,EAAG;IACP,IAAI,CAAChmB,IAAI,CAACgmB,MAAM,CAAC,CAAC;IAClB,IAAI,CAACpnB,OAAO,CAAC,QAAQ,CAAC;EACxB;;EAEA;AACF;AACA;AACA;EACEktB,QAAQA,CAAA,EAAG;IACT,IAAI,CAAC9rB,IAAI,CAAC8rB,QAAQ,CAAC,CAAC;IACpB,IAAI,CAACltB,OAAO,CAAC,UAAU,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;EACEmtB,OAAOA,CAAA,EAAG;IACR9R,cAAc,CAAC,IAAI,CAAC;IACpB,IAAIvc,eAAe,CAAC,IAAI,CAACkC,EAAE,CAAC,EAAE;MAC5B,IAAI,CAACA,EAAE,CAACkrB,MAAM,CAAC,CAAC;MAChB,IAAI,CAAClrB,EAAE,GAAG,IAAI;IAChB;IACA,IAAI,CAACosB,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACptB,OAAO,CAAC,SAAS,CAAC;EACzB;;EAEA;AACF;AACA;AACA;EACEqtB,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjsB,IAAI;EAClB;;EAEA;AACF;AACA;EACEksB,IAAIA,CAAA,EAAG;IACL,IAAI,CAAClsB,IAAI,CAACmsB,KAAK,CAACD,IAAI,CAAC,CAAC;IACtB,IAAI,CAACttB,OAAO,CAAC,aAAa,CAAC;IAC3B,IAAI,IAAI,CAACgB,EAAE,EAAE;MACX,IAAI,CAACA,EAAE,CAACwsB,MAAM,GAAG,IAAI;IACvB;IACA,IAAI,CAACJ,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACptB,OAAO,CAAC,MAAM,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;EACEytB,uBAAuBA,CAAA,EAAG;IACxB,IAAI,CAACT,iBAAiB,GAAG9qB,aAAa,CAAC,IAAI,CAAC;IAC5C,OAAO,IAAI,CAAC8qB,iBAAiB;EAC/B;;EAEA;AACF;AACA;AACA;AACA;EACEvS,2BAA2BA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAACuS,iBAAiB,KAAK,IAAI,EAAE;MACnC,OAAO,IAAI,CAACS,uBAAuB,CAAC,CAAC;IACvC;IACA,OAAO,IAAI,CAACT,iBAAiB;EAC/B;;EAEA;AACF;AACA;AACA;EACElsB,MAAMA,CAAA,EAAG;IACP,OAAOuH,OAAO,CAAC,IAAI,CAACrH,EAAE,IAAI,CAAC,IAAI,CAACA,EAAE,CAACwsB,MAAM,CAAC;EAC5C;;EAEA;AACF;AACA;AACA;EACEE,IAAIA,CAAA,EAAG;IACL,IAAI1uB,UAAU,CAAC,IAAI,CAAC7C,OAAO,CAACwxB,iBAAiB,CAAC,EAAE;MAC9C,OAAOnS,OAAO,CAACC,OAAO,CAAC,IAAI,CAACtf,OAAO,CAACwxB,iBAAiB,CAAC,CAAC,CAAC,CAACrS,IAAI,CAAC,MAAM,IAAI,CAACsS,KAAK,CAAC,CAAC,CAAC;IACnF;IACA,OAAOpS,OAAO,CAACC,OAAO,CAAC,IAAI,CAACmS,KAAK,CAAC,CAAC,CAAC;EACtC;;EAEA;AACF;AACA;AACA;AACA;EACEC,iBAAiBA,CAAC1xB,OAAO,EAAE;IACzBf,MAAM,CAACiH,MAAM,CAAC,IAAI,CAAClG,OAAO,EAAEA,OAAO,CAAC;IACpC,IAAI,IAAI,CAAC2e,wBAAwB,EAAE;MACjC,IAAI,CAACA,wBAAwB,CAACuJ,IAAI,CAAC;QACjCxjB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;AACA;EACEka,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC/Z,EAAE;EAChB;;EAEA;AACF;AACA;AACA;EACE8sB,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACvxB,MAAM;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEwxB,qBAAqBA,CAAA,EAAG;IACtB,MAAMhF,aAAa,GAAG,GAAG,IAAI,CAAC6C,EAAE,cAAc;IAC9C,MAAMnE,OAAO,GAAG,GAAG,IAAI,CAACmE,EAAE,QAAQ;IAClC,IAAI,CAAC9Q,wBAAwB,GAAG,IAAI+R,gBAAgB,CAAC;MACnDtwB,MAAM,EAAE,IAAI,CAAC6E,IAAI,CAACjF,OAAO,CAAC6xB,cAAc,IAAIzsB,QAAQ,CAACO,IAAI;MACzDqhB,KAAK,EAAE;QACLkI,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BtC,aAAa;QACbtB,OAAO;QACP5mB,IAAI,EAAE,IAAI;QACVksB,MAAM,EAAE,IAAI,CAACA;MACf;IACF,CAAC,CAAC;IACF,OAAO,IAAI,CAACjS,wBAAwB,CAACC,UAAU,CAAC,CAAC;EACnD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEkT,SAASA,CAACC,eAAe,EAAE;IACzB,MAAM;MACJvxB;IACF,CAAC,GAAG,IAAI,CAAC8d,2BAA2B,CAAC,CAAC;IACtC,IAAIzb,UAAU,CAAC,IAAI,CAAC7C,OAAO,CAACgyB,eAAe,CAAC,EAAE;MAC5C,IAAI,CAAChyB,OAAO,CAACgyB,eAAe,CAACxxB,OAAO,CAAC;IACvC,CAAC,MAAM,IAAIiC,WAAW,CAACjC,OAAO,CAAC,IAAI,OAAOA,OAAO,CAACyxB,cAAc,KAAK,UAAU,EAAE;MAC/EzxB,OAAO,CAACyxB,cAAc,CAACF,eAAe,CAAC;IACzC;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEG,gBAAgBA,CAACC,WAAW,EAAE;IAC5B,MAAMC,kBAAkB,GAAG,IAAI,CAACntB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACjF,OAAO,IAAI,IAAI,CAACiF,IAAI,CAACjF,OAAO,CAACoyB,kBAAkB;IACjG,MAAMC,WAAW,GAAGF,WAAW,CAACjJ,OAAO,GAAGiJ,WAAW,CAACjJ,OAAO,GAAG,EAAE;IAClE,MAAMoJ,yBAAyB,GAAGF,kBAAkB,IAAIA,kBAAkB,CAAClJ,OAAO,GAAGkJ,kBAAkB,CAAClJ,OAAO,GAAG,EAAE;IACpH,MAAMqJ,UAAU,GAAG,CAAC,GAAGF,WAAW,CAAC5pB,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG6pB,yBAAyB,CAAC7pB,KAAK,CAAC,GAAG,CAAC,CAAC;IACvF,MAAM+pB,WAAW,GAAG,IAAIpO,GAAG,CAACmO,UAAU,CAAC;IACvC,OAAO1yB,KAAK,CAAC6Y,IAAI,CAAC8Z,WAAW,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;EACjD;;EAEA;AACF;AACA;AACA;AACA;EACE5B,WAAWA,CAAC9wB,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,IAAIif,WAAW,GAAG,IAAI,CAACha,IAAI,IAAI,IAAI,CAACA,IAAI,CAACjF,OAAO,IAAI,IAAI,CAACiF,IAAI,CAACjF,OAAO,CAACoyB,kBAAkB;IACxFnT,WAAW,GAAGzc,GAAG,CAAC,CAAC,CAAC,EAAEyc,WAAW,IAAI,CAAC,CAAC,CAAC;IACxC,IAAI,CAACjf,OAAO,GAAGf,MAAM,CAACiH,MAAM,CAAC;MAC3BwI,KAAK,EAAE;IACT,CAAC,EAAEuQ,WAAW,EAAEjf,OAAO,EAAEgf,kBAAkB,CAACC,WAAW,EAAEjf,OAAO,CAAC,CAAC;IAClE,MAAM;MACJ2yB;IACF,CAAC,GAAG,IAAI,CAAC3yB,OAAO;IAChB,IAAI,CAACA,OAAO,CAACkpB,OAAO,GAAG,IAAI,CAACgJ,gBAAgB,CAAClyB,OAAO,CAAC;IACrD,IAAI,CAACgxB,OAAO,CAAC,CAAC;IACd,IAAI,CAACvB,EAAE,GAAG,IAAI,CAACzvB,OAAO,CAACyvB,EAAE,IAAI,QAAQppB,IAAI,CAAC,CAAC,EAAE;IAC7C,IAAIssB,IAAI,EAAE;MACR1zB,MAAM,CAACiC,IAAI,CAACyxB,IAAI,CAAC,CAAChxB,OAAO,CAACwB,KAAK,IAAI;QACjC,IAAI,CAACD,EAAE,CAACC,KAAK,EAAEwvB,IAAI,CAACxvB,KAAK,CAAC,EAAE,IAAI,CAAC;MACnC,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;AACA;EACEyvB,cAAcA,CAAA,EAAG;IACf,IAAI,CAAC7vB,WAAW,CAAC,IAAI,CAAC8B,EAAE,CAAC,EAAE;MACzB,IAAI,CAACmsB,OAAO,CAAC,CAAC;IAChB;IACA,IAAI,CAACnsB,EAAE,GAAG,IAAI,CAAC+sB,qBAAqB,CAAC,CAAC;IACtC,IAAI,IAAI,CAAC5xB,OAAO,CAACmF,SAAS,EAAE;MAC1BD,WAAW,CAAC,IAAI,CAAC;IACnB;;IAEA;IACA;IACAkZ,YAAY,CAAC,IAAI,CAAC;EACpB;;EAEA;AACF;AACA;AACA;AACA;EACEqT,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC5tB,OAAO,CAAC,aAAa,CAAC;;IAE3B;IACA,IAAI,CAACytB,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACsB,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC,IAAI,CAAC3tB,IAAI,CAACmsB,KAAK,EAAE;MACpB,IAAI,CAACnsB,IAAI,CAAC4tB,WAAW,CAAC,CAAC;IACzB;IACA,IAAI,CAAC5tB,IAAI,CAACmsB,KAAK,CAAC0B,YAAY,CAAC,IAAI,CAAC;IAClC,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAAC;IACrC,IAAI,CAACluB,EAAE,CAACwsB,MAAM,GAAG,KAAK;;IAEtB;IACA,IAAI,IAAI,CAACrxB,OAAO,CAACgzB,QAAQ,EAAE;MACzBzW,UAAU,CAAC,MAAM;QACf,IAAI,CAACuV,SAAS,CAAC,IAAI,CAAC9xB,OAAO,CAACgzB,QAAQ,CAAC;MACvC,CAAC,CAAC;IACJ;IACA,IAAI,CAACnuB,EAAE,CAACwsB,MAAM,GAAG,KAAK;IACtB,MAAM3S,OAAO,GAAG,IAAI,CAACC,wBAAwB,CAACC,UAAU,CAAC,CAAC;IAC1D,MAAMxe,MAAM,GAAG,IAAI,CAACA,MAAM,IAAIgF,QAAQ,CAACO,IAAI;IAC3CvF,MAAM,CAACye,SAAS,CAACC,GAAG,CAAC,GAAG,IAAI,CAACoQ,WAAW,kBAAkB,CAAC;IAC3D9uB,MAAM,CAACye,SAAS,CAACC,GAAG,CAAC,GAAG,IAAI,CAACoQ,WAAW,iBAAiB,CAAC;IAC1DxQ,OAAO,CAACG,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACzC,IAAI,CAACjb,OAAO,CAAC,MAAM,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEkvB,0BAA0BA,CAACruB,IAAI,EAAE;IAC/B,MAAMuuB,aAAa,GAAGvuB,IAAI,CAACtE,MAAM;IACjC,IAAI,CAAC6yB,aAAa,EAAE;MAClB;IACF;IACA,IAAIvuB,IAAI,CAAC1E,OAAO,CAACkzB,cAAc,EAAE;MAC/BD,aAAa,CAACpU,SAAS,CAACC,GAAG,CAACpa,IAAI,CAAC1E,OAAO,CAACkzB,cAAc,CAAC;IAC1D;IACAD,aAAa,CAACpU,SAAS,CAACkR,MAAM,CAAC,gCAAgC,CAAC;IAChE,IAAIrrB,IAAI,CAAC1E,OAAO,CAACmzB,cAAc,KAAK,KAAK,EAAE;MACzCF,aAAa,CAACpU,SAAS,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC/D;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEmS,uBAAuBA,CAAA,EAAG;IACxB,MAAM7wB,MAAM,GAAG,IAAI,CAACA,MAAM,IAAIgF,QAAQ,CAACO,IAAI;IAC3C,IAAI,IAAI,CAAC3F,OAAO,CAACkzB,cAAc,EAAE;MAC/B9yB,MAAM,CAACye,SAAS,CAACkR,MAAM,CAAC,IAAI,CAAC/vB,OAAO,CAACkzB,cAAc,CAAC;IACtD;IACA9yB,MAAM,CAACye,SAAS,CAACkR,MAAM,CAAC,gCAAgC,EAAE,GAAG,IAAI,CAACb,WAAW,kBAAkB,EAAE,GAAG,IAAI,CAACA,WAAW,iBAAiB,CAAC;EACxI;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASkE,YAAYA,CAACnuB,IAAI,EAAE;EAC1B,IAAIA,IAAI,EAAE;IACR,MAAM;MACJouB;IACF,CAAC,GAAGpuB,IAAI;IACRouB,KAAK,CAAC1xB,OAAO,CAAC+C,IAAI,IAAI;MACpB,IAAIA,IAAI,CAAC1E,OAAO,IAAI0E,IAAI,CAAC1E,OAAO,CAACmzB,cAAc,KAAK,KAAK,IAAIzuB,IAAI,CAAC1E,OAAO,CAACgG,QAAQ,EAAE;QAClF,IAAItB,IAAI,CAACtE,MAAM,YAAYwC,WAAW,EAAE;UACtC8B,IAAI,CAACtE,MAAM,CAACye,SAAS,CAACkR,MAAM,CAAC,gCAAgC,CAAC;QAChE;MACF;IACF,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuD,eAAeA,CAAC;EACvBzoB,KAAK;EACLC,MAAM;EACNpD,CAAC,GAAG,CAAC;EACLC,CAAC,GAAG,CAAC;EACLhB,CAAC,GAAG;AACN,CAAC,EAAE;EACD,MAAM;IACJ4sB,UAAU,EAAEC,CAAC;IACbC,WAAW,EAAEC;EACf,CAAC,GAAGpgB,MAAM;EACV,MAAM;IACJqgB,OAAO,GAAG,CAAC;IACXC,QAAQ,GAAG,CAAC;IACZC,WAAW,GAAG,CAAC;IACfC,UAAU,GAAG;EACf,CAAC,GAAG,OAAOntB,CAAC,KAAK,QAAQ,GAAG;IAC1BgtB,OAAO,EAAEhtB,CAAC;IACVitB,QAAQ,EAAEjtB,CAAC;IACXktB,WAAW,EAAEltB,CAAC;IACdmtB,UAAU,EAAEntB;EACd,CAAC,GAAGA,CAAC;EACL,OAAO,IAAI6sB,CAAC,IAAIE,CAAC;AACnB;AACA;AACA,GAAGF,CAAC;AACJ,GAAGE,CAAC;AACJ;AACA,GAAGhsB,CAAC,GAAGisB,OAAO,IAAIhsB,CAAC;AACnB,GAAGgsB,OAAO,IAAIA,OAAO,UAAUA,OAAO,IAAIA,OAAO;AACjD,GAAG7oB,MAAM,GAAGnD,CAAC,GAAGmsB,UAAU;AAC1B,GAAGA,UAAU,IAAIA,UAAU,UAAUA,UAAU,IAAIA,UAAU;AAC7D,GAAGjpB,KAAK,GAAGnD,CAAC,GAAGmsB,WAAW;AAC1B,GAAGA,WAAW,IAAIA,WAAW,UAAUA,WAAW,IAAIA,WAAW;AACjE,GAAGlsB,CAAC,GAAGisB,QAAQ;AACf,GAAGA,QAAQ,IAAIA,QAAQ,UAAUA,QAAQ,IAAIA,QAAQ;AACrD,EAAE;AACF;;AAEA;AACA,SAAS9M,eAAeA,CAACzjB,GAAG,EAAE;EAC5B,IAAI0wB,GAAG;EACP,IAAIC,IAAI;EACR,IAAIC,eAAe;EACnB,IAAIxL,OAAO;EACX,IAAIC,OAAO;EACX,OAAO;IACLhiB,CAACA,CAAA,EAAG;MACFqtB,GAAG,GAAGrS,WAAW,CAAC,KAAK,CAAC;MACxBsS,IAAI,GAAGtS,WAAW,CAAC,MAAM,CAAC;MAC1BO,IAAI,CAAC+R,IAAI,EAAE,GAAG,EAAE,kBAAkB3wB,GAAG,CAAC,CAAC,CAAC,CAAC;MACzC4e,IAAI,CAAC8R,GAAG,EAAE,OAAO,EAAEE,eAAe,GAAG,GAAG,kBAAkB5wB,GAAG,CAAC,CAAC,CAAC,GAAG,2BAA2B,GAAG,EAAE,mCAAmC,CAAC;IACzI,CAAC;IACDijB,CAACA,CAAClmB,MAAM,EAAE8gB,MAAM,EAAE;MAChBD,MAAM,CAAC7gB,MAAM,EAAE2zB,GAAG,EAAE7S,MAAM,CAAC;MAC3BH,MAAM,CAACgT,GAAG,EAAEC,IAAI,CAAC;MACjB;MACA3wB,GAAG,CAAC,EAAE,CAAC,CAAC0wB,GAAG,CAAC;MACZ,IAAI,CAACtL,OAAO,EAAE;QACZC,OAAO,GAAG1G,MAAM,CAAC+R,GAAG,EAAE,WAAW,EAAE,6BAA6B1wB,GAAG,CAAC,CAAC,CAAC,CAAC;QACvEolB,OAAO,GAAG,IAAI;MAChB;IACF,CAAC;IACD3D,CAACA,CAACzhB,GAAG,EAAE,CAACwhB,KAAK,CAAC,EAAE;MACd,IAAIA,KAAK,GAAG,kBAAkB,CAAC,EAAE;QAC/B5C,IAAI,CAAC+R,IAAI,EAAE,GAAG,EAAE,kBAAkB3wB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC3C;MACA,IAAIwhB,KAAK,GAAG,kBAAkB,CAAC,IAAIoP,eAAe,MAAMA,eAAe,GAAG,GAAG,kBAAkB5wB,GAAG,CAAC,CAAC,CAAC,GAAG,2BAA2B,GAAG,EAAE,mCAAmC,CAAC,EAAE;QAC5K4e,IAAI,CAAC8R,GAAG,EAAE,OAAO,EAAEE,eAAe,CAAC;MACrC;IACF,CAAC;IACD5vB,CAAC,EAAE4b,IAAI;IACP0F,CAAC,EAAE1F,IAAI;IACP3Z,CAACA,CAACkb,SAAS,EAAE;MACX,IAAIA,SAAS,EAAEJ,MAAM,CAAC2S,GAAG,CAAC;MAC1B;MACA1wB,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;MACbolB,OAAO,GAAG,KAAK;MACfC,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH;AACA,SAASwL,gBAAgBA,CAAC1zB,OAAO,EAAE;EACjC,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EACA,MAAM2zB,aAAa,GAAG3zB,OAAO,YAAYoC,WAAW;EACpD,MAAMkR,SAAS,GAAGqgB,aAAa,IAAI7gB,MAAM,CAACU,gBAAgB,CAACxT,OAAO,CAAC,CAACsT,SAAS;EAC7E,MAAMsgB,YAAY,GAAGtgB,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,SAAS;EACtE,IAAIsgB,YAAY,IAAI5zB,OAAO,CAACwY,YAAY,IAAIxY,OAAO,CAACyY,YAAY,EAAE;IAChE,OAAOzY,OAAO;EAChB;EACA,OAAO0zB,gBAAgB,CAAC1zB,OAAO,CAAC6zB,aAAa,CAAC;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAAC9zB,OAAO,EAAE+zB,YAAY,EAAE;EAChD,MAAMC,WAAW,GAAGh0B,OAAO,CAACqW,qBAAqB,CAAC,CAAC;EACnD,IAAI7O,GAAG,GAAGwsB,WAAW,CAAC7sB,CAAC,IAAI6sB,WAAW,CAACxsB,GAAG;EAC1C,IAAID,MAAM,GAAGysB,WAAW,CAACzsB,MAAM,IAAIC,GAAG,GAAGwsB,WAAW,CAAC1pB,MAAM;EAC3D,IAAIypB,YAAY,EAAE;IAChB,MAAME,UAAU,GAAGF,YAAY,CAAC1d,qBAAqB,CAAC,CAAC;IACvD,MAAMvB,SAAS,GAAGmf,UAAU,CAAC9sB,CAAC,IAAI8sB,UAAU,CAACzsB,GAAG;IAChD,MAAM0sB,YAAY,GAAGD,UAAU,CAAC1sB,MAAM,IAAIuN,SAAS,GAAGmf,UAAU,CAAC3pB,MAAM;IACvE9C,GAAG,GAAGpB,IAAI,CAACU,GAAG,CAACU,GAAG,EAAEsN,SAAS,CAAC;IAC9BvN,MAAM,GAAGnB,IAAI,CAACS,GAAG,CAACU,MAAM,EAAE2sB,YAAY,CAAC;EACzC;EACA,MAAM5pB,MAAM,GAAGlE,IAAI,CAACU,GAAG,CAACS,MAAM,GAAGC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1C,OAAO;IACLL,CAAC,EAAEK,GAAG;IACN8C;EACF,CAAC;AACH;AACA,SAAS+b,QAAQA,CAACkC,MAAM,EAAEZ,OAAO,EAAEa,YAAY,EAAE;EAC/C,IAAI;IACFxoB,OAAO;IACPm0B;EACF,CAAC,GAAGxM,OAAO;EACX9hB,IAAI,CAAC,CAAC;EACN,IAAIuuB,cAAc,GAAG,KAAK;EAC1B,IAAIC,KAAK,GAAG7xB,SAAS;EACrB,IAAI8xB,cAAc;EAClBC,iBAAiB,CAAC,CAAC;EACnB,MAAMnW,UAAU,GAAGA,CAAA,KAAMpe,OAAO;EAChC,SAASu0B,iBAAiBA,CAAA,EAAG;IAC3B/L,YAAY,CAAC,CAAC,EAAE2L,iBAAiB,GAAG;MAClC9pB,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTpD,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJhB,CAAC,EAAE;IACL,CAAC,CAAC;EACJ;EACA,SAASwqB,IAAIA,CAAA,EAAG;IACdnI,YAAY,CAAC,CAAC,EAAE4L,cAAc,GAAG,KAAK,CAAC;;IAEvC;IACAI,0BAA0B,CAAC,CAAC;EAC9B;EACA,SAASC,aAAaA,CAACC,0BAA0B,GAAG,CAAC,EAAEC,yBAAyB,GAAG,CAAC,EAAEZ,YAAY,EAAEtB,aAAa,EAAE;IACjH,IAAIA,aAAa,EAAE;MACjB,MAAM;QACJtrB,CAAC;QACDmD;MACF,CAAC,GAAGwpB,iBAAiB,CAACrB,aAAa,EAAEsB,YAAY,CAAC;MAClD,MAAM;QACJ7sB,CAAC;QACDmD,KAAK;QACLhD;MACF,CAAC,GAAGorB,aAAa,CAACpc,qBAAqB,CAAC,CAAC;;MAEzC;MACAmS,YAAY,CAAC,CAAC,EAAE2L,iBAAiB,GAAG;QAClC9pB,KAAK,EAAEA,KAAK,GAAGqqB,0BAA0B,GAAG,CAAC;QAC7CpqB,MAAM,EAAEA,MAAM,GAAGoqB,0BAA0B,GAAG,CAAC;QAC/CxtB,CAAC,EAAE,CAACA,CAAC,IAAIG,IAAI,IAAIqtB,0BAA0B;QAC3CvtB,CAAC,EAAEA,CAAC,GAAGutB,0BAA0B;QACjCvuB,CAAC,EAAEwuB;MACL,CAAC,CAAC;IACJ,CAAC,MAAM;MACLJ,iBAAiB,CAAC,CAAC;IACrB;EACF;EACA,SAASjC,YAAYA,CAACpuB,IAAI,EAAE;IAC1B;IACAswB,0BAA0B,CAAC,CAAC;IAC5B,IAAItwB,IAAI,CAACO,IAAI,CAACjF,OAAO,CAACo1B,eAAe,EAAE;MACrCC,aAAa,CAAC3wB,IAAI,CAAC;MACnB6sB,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACLJ,IAAI,CAAC,CAAC;IACR;EACF;EACA,SAASI,IAAIA,CAAA,EAAG;IACdvI,YAAY,CAAC,CAAC,EAAE4L,cAAc,GAAG,IAAI,CAAC;EACxC;EACA,MAAMU,sBAAsB,GAAGhwB,CAAC,IAAI;IAClCA,CAAC,CAAC0lB,cAAc,CAAC,CAAC;EACpB,CAAC;EACD,MAAMuK,yBAAyB,GAAGjwB,CAAC,IAAI;IACrCA,CAAC,CAACirB,eAAe,CAAC,CAAC;EACrB,CAAC;;EAED;AACF;AACA;AACA;EACE,SAASiF,sBAAsBA,CAAA,EAAG;IAChC;IACAliB,MAAM,CAAC7N,gBAAgB,CAAC,WAAW,EAAE6vB,sBAAsB,EAAE;MAC3DlY,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;EACE,SAAS4X,0BAA0BA,CAAA,EAAG;IACpC,IAAIH,KAAK,EAAE;MACTnX,oBAAoB,CAACmX,KAAK,CAAC;MAC3BA,KAAK,GAAG7xB,SAAS;IACnB;IACAsQ,MAAM,CAAC5N,mBAAmB,CAAC,WAAW,EAAE4vB,sBAAsB,EAAE;MAC9DlY,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASiY,aAAaA,CAAC3wB,IAAI,EAAE;IAC3B,MAAM;MACJwwB,0BAA0B;MAC1BC;IACF,CAAC,GAAGzwB,IAAI,CAAC1E,OAAO;IAChB,MAAMu0B,YAAY,GAAGL,gBAAgB,CAACxvB,IAAI,CAACtE,MAAM,CAAC;;IAElD;IACA,MAAMq1B,OAAO,GAAGA,CAAA,KAAM;MACpBZ,KAAK,GAAG7xB,SAAS;MACjBiyB,aAAa,CAACC,0BAA0B,EAAEC,yBAAyB,EAAEZ,YAAY,EAAE7vB,IAAI,CAACtE,MAAM,CAAC;MAC/Fy0B,KAAK,GAAGlX,qBAAqB,CAAC8X,OAAO,CAAC;IACxC,CAAC;IACDA,OAAO,CAAC,CAAC;IACTD,sBAAsB,CAAC,CAAC;EAC1B;EACA,SAASE,WAAWA,CAACjK,OAAO,EAAE;IAC5B9H,iBAAiB,CAAC8H,OAAO,GAAG,SAAS,GAAG,MAAM,CAAC,CAAC,MAAM;MACpDjrB,OAAO,GAAGirB,OAAO;MACjBzC,YAAY,CAAC,CAAC,EAAExoB,OAAO,CAAC;IAC1B,CAAC,CAAC;EACJ;EACAuoB,MAAM,CAACX,KAAK,GAAGD,OAAO,IAAI;IACxB,IAAI,SAAS,IAAIA,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAExoB,OAAO,GAAG2nB,OAAO,CAAC3nB,OAAO,CAAC;IACpE,IAAI,mBAAmB,IAAI2nB,OAAO,EAAEa,YAAY,CAAC,CAAC,EAAE2L,iBAAiB,GAAGxM,OAAO,CAACwM,iBAAiB,CAAC;EACpG,CAAC;EACD5L,MAAM,CAACzF,EAAE,CAAC3G,MAAM,GAAG,MAAM;IACvB,IAAIoM,MAAM,CAACzF,EAAE,CAACuB,KAAK,GAAG,qBAAqB,EAAE,EAAE;MAC7CmE,YAAY,CAAC,CAAC,EAAE8L,cAAc,GAAGxB,eAAe,CAACqB,iBAAiB,CAAC,CAAC;IACtE;EACF,CAAC;EACD,OAAO,CAACn0B,OAAO,EAAEo0B,cAAc,EAAEE,cAAc,EAAES,yBAAyB,EAAEZ,iBAAiB,EAAE/V,UAAU,EAAEmW,iBAAiB,EAAE5D,IAAI,EAAE8D,aAAa,EAAEnC,YAAY,EAAEvB,IAAI,EAAEmE,WAAW,CAAC;AACrL;AACA,MAAMC,cAAc,SAAS7N,eAAe,CAAC;EAC3C1jB,WAAWA,CAACpE,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IACP4mB,IAAI,CAAC,IAAI,EAAE5mB,OAAO,EAAE6mB,QAAQ,EAAEC,eAAe,EAAElG,cAAc,EAAE;MAC7DpgB,OAAO,EAAE,CAAC;MACVm0B,iBAAiB,EAAE,CAAC;MACpB/V,UAAU,EAAE,CAAC;MACbmW,iBAAiB,EAAE,CAAC;MACpB5D,IAAI,EAAE,CAAC;MACP8D,aAAa,EAAE,CAAC;MAChBnC,YAAY,EAAE,CAAC;MACfvB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;EACA,IAAI3S,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC0E,EAAE,CAACjgB,GAAG,CAAC,CAAC,CAAC;EACvB;EACA,IAAI0xB,iBAAiBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACzR,EAAE,CAACjgB,GAAG,CAAC,CAAC,CAAC;EACvB;EACA,IAAI8tB,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC7N,EAAE,CAACjgB,GAAG,CAAC,CAAC,CAAC;EACvB;EACA,IAAI4xB,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC3R,EAAE,CAACjgB,GAAG,CAAC,CAAC,CAAC;EACvB;EACA,IAAIyvB,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACxP,EAAE,CAACjgB,GAAG,CAAC,CAAC,CAAC;EACvB;EACA,IAAIkuB,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACjO,EAAE,CAACjgB,GAAG,CAAC,EAAE,CAAC;EACxB;AACF;AAEA,MAAMuyB,QAAQ,GAAG,IAAI3yB,OAAO,CAAC,CAAC;;AAE9B;AACA;AACA;AACA;AACA,MAAM4yB,IAAI,SAAS5yB,OAAO,CAAC;EACzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEmB,WAAWA,CAACpE,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,KAAK,CAACA,OAAO,CAAC;IACdiE,QAAQ,CAAC,IAAI,CAAC;IACd,MAAM6xB,kBAAkB,GAAG;MACzBxF,SAAS,EAAE,IAAI;MACfE,kBAAkB,EAAE;IACtB,CAAC;IACD,IAAI,CAACxwB,OAAO,GAAGf,MAAM,CAACiH,MAAM,CAAC,CAAC,CAAC,EAAE4vB,kBAAkB,EAAE91B,OAAO,CAAC;IAC7D,IAAI,CAACkvB,WAAW,GAAGtpB,eAAe,CAAC,IAAI,CAAC5F,OAAO,CAACkvB,WAAW,CAAC;IAC5D,IAAI,CAACmE,KAAK,GAAG,EAAE;IACf,IAAI,CAAC0C,QAAQ,CAAC,IAAI,CAAC/1B,OAAO,CAACqzB,KAAK,CAAC;;IAEjC;IACA,MAAM2C,MAAM,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;IAC5EA,MAAM,CAACz1B,GAAG,CAAC4C,KAAK,IAAI;MAClB,CAACmC,CAAC,IAAI;QACJ,IAAI,CAACpC,EAAE,CAACoC,CAAC,EAAE2wB,IAAI,IAAI;UACjBA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;UACjBA,IAAI,CAAChxB,IAAI,GAAG,IAAI;UAChB2wB,QAAQ,CAAC/xB,OAAO,CAACyB,CAAC,EAAE2wB,IAAI,CAAC;QAC3B,CAAC,CAAC;MACJ,CAAC,EAAE9yB,KAAK,CAAC;IACX,CAAC,CAAC;IACF,IAAI,CAAC+yB,UAAU,CAAC,CAAC;IACjB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,OAAOA,CAACn2B,OAAO,EAAE2D,KAAK,EAAE;IACtB,IAAIe,IAAI,GAAG1E,OAAO;IAClB,IAAI,EAAE0E,IAAI,YAAYisB,IAAI,CAAC,EAAE;MAC3BjsB,IAAI,GAAG,IAAIisB,IAAI,CAAC,IAAI,EAAEjsB,IAAI,CAAC;IAC7B,CAAC,MAAM;MACLA,IAAI,CAACO,IAAI,GAAG,IAAI;IAClB;IACA,IAAI,CAAClC,WAAW,CAACY,KAAK,CAAC,EAAE;MACvB,IAAI,CAAC0vB,KAAK,CAACzvB,MAAM,CAACD,KAAK,EAAE,CAAC,EAAEe,IAAI,CAAC;IACnC,CAAC,MAAM;MACL,IAAI,CAAC2uB,KAAK,CAAC7vB,IAAI,CAACkB,IAAI,CAAC;IACvB;IACA,OAAOA,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEqxB,QAAQA,CAAC1C,KAAK,EAAE;IACd,IAAIxzB,KAAK,CAACC,OAAO,CAACuzB,KAAK,CAAC,EAAE;MACxBA,KAAK,CAAC1xB,OAAO,CAAC+C,IAAI,IAAI;QACpB,IAAI,CAACyxB,OAAO,CAACzxB,IAAI,CAAC;MACpB,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACE+rB,IAAIA,CAAA,EAAG;IACL,MAAM9sB,KAAK,GAAG,IAAI,CAAC0vB,KAAK,CAACjsB,OAAO,CAAC,IAAI,CAACgvB,WAAW,CAAC;IAClD,IAAI,CAAC7E,IAAI,CAAC5tB,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;AACA;AACA;EACQsnB,MAAMA,CAAA,EAAG;IAAA,IAAAoL,KAAA;IAAA,OAAAzqB,iBAAA;MACb,IAAIyqB,KAAI,CAACr2B,OAAO,CAACs2B,aAAa,EAAE;QAC9B,MAAMC,uBAAuB,GAAG,OAAOF,KAAI,CAACr2B,OAAO,CAACs2B,aAAa,KAAK,UAAU;QAChF,MAAME,aAAa,GAAGH,KAAI,CAACr2B,OAAO,CAACy2B,oBAAoB,IAAI,yCAAyC;QACpG,MAAMC,QAAQ,GAAGH,uBAAuB,SAASF,KAAI,CAACr2B,OAAO,CAACs2B,aAAa,CAAC,CAAC,GAAGhjB,MAAM,CAACqjB,OAAO,CAACH,aAAa,CAAC;QAC7G,IAAIE,QAAQ,EAAE;UACZL,KAAI,CAACO,KAAK,CAAC,QAAQ,CAAC;QACtB;MACF,CAAC,MAAM;QACLP,KAAI,CAACO,KAAK,CAAC,QAAQ,CAAC;MACtB;IAAC;EACH;;EAEA;AACF;AACA;EACE7F,QAAQA,CAAA,EAAG;IACT,IAAI,CAAC6F,KAAK,CAAC,UAAU,CAAC;EACxB;;EAEA;AACF;AACA;AACA;AACA;EACEC,OAAOA,CAACpH,EAAE,EAAE;IACV,OAAO,IAAI,CAAC4D,KAAK,CAACyD,IAAI,CAACpyB,IAAI,IAAI;MAC7B,OAAOA,IAAI,CAAC+qB,EAAE,KAAKA,EAAE;IACvB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;EACEsH,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAACX,WAAW;EACzB;;EAEA;AACF;AACA;EACEjF,IAAIA,CAAA,EAAG;IACL,MAAMiF,WAAW,GAAG,IAAI,CAACW,cAAc,CAAC,CAAC;IACzC,IAAIX,WAAW,EAAE;MACf,OAAOA,WAAW,CAACjF,IAAI,CAAC,CAAC;IAC3B;EACF;;EAEA;AACF;AACA;AACA;EACE6F,QAAQA,CAAA,EAAG;IACT,OAAOpB,QAAQ,CAACqB,UAAU,KAAK,IAAI;EACrC;;EAEA;AACF;AACA;AACA;EACE30B,IAAIA,CAAA,EAAG;IACL,MAAMqB,KAAK,GAAG,IAAI,CAAC0vB,KAAK,CAACjsB,OAAO,CAAC,IAAI,CAACgvB,WAAW,CAAC;IAClD,IAAIzyB,KAAK,KAAK,IAAI,CAAC0vB,KAAK,CAAC/uB,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAACysB,QAAQ,CAAC,CAAC;IACjB,CAAC,MAAM;MACL,IAAI,CAACQ,IAAI,CAAC5tB,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC;IAC5B;EACF;;EAEA;AACF;AACA;AACA;EACEuzB,UAAUA,CAAC1qB,IAAI,EAAE;IACf,MAAMqd,OAAO,GAAG,IAAI,CAACkN,cAAc,CAAC,CAAC;;IAErC;IACA,IAAI,CAAC1D,KAAK,CAAC1e,IAAI,CAAC,CAACjQ,IAAI,EAAEL,CAAC,KAAK;MAC3B,IAAIK,IAAI,CAAC+qB,EAAE,KAAKjjB,IAAI,EAAE;QACpB,IAAI9H,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;UACjBD,IAAI,CAACysB,IAAI,CAAC,CAAC;QACb;QACAzsB,IAAI,CAACssB,OAAO,CAAC,CAAC;QACd,IAAI,CAACqC,KAAK,CAACzvB,MAAM,CAACS,CAAC,EAAE,CAAC,CAAC;QACvB,OAAO,IAAI;MACb;IACF,CAAC,CAAC;IACF,IAAIwlB,OAAO,IAAIA,OAAO,CAAC4F,EAAE,KAAKjjB,IAAI,EAAE;MAClC,IAAI,CAAC4pB,WAAW,GAAGpzB,SAAS;;MAE5B;MACA,IAAI,CAACqwB,KAAK,CAAC/uB,MAAM,GAAG,IAAI,CAACitB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAACtG,MAAM,CAAC,CAAC;IAClD;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEsG,IAAIA,CAAC7wB,GAAG,GAAG,CAAC,EAAEy2B,OAAO,GAAG,IAAI,EAAE;IAC5B,MAAMzyB,IAAI,GAAG5B,QAAQ,CAACpC,GAAG,CAAC,GAAG,IAAI,CAACm2B,OAAO,CAACn2B,GAAG,CAAC,GAAG,IAAI,CAAC2yB,KAAK,CAAC3yB,GAAG,CAAC;IAChE,IAAIgE,IAAI,EAAE;MACR,IAAI,CAAC0yB,sBAAsB,CAAC,CAAC;MAC7B,MAAMC,cAAc,GAAGx0B,UAAU,CAAC6B,IAAI,CAAC1E,OAAO,CAACs3B,MAAM,CAAC,IAAI,CAAC5yB,IAAI,CAAC1E,OAAO,CAACs3B,MAAM,CAAC,CAAC;;MAEhF;MACA,IAAID,cAAc,EAAE;QAClB,IAAI,CAACE,SAAS,CAAC7yB,IAAI,EAAEyyB,OAAO,CAAC;MAC/B,CAAC,MAAM;QACL,IAAI,CAACtzB,OAAO,CAAC,MAAM,EAAE;UACnBa,IAAI;UACJ8yB,QAAQ,EAAE,IAAI,CAACpB;QACjB,CAAC,CAAC;QACF,IAAI,CAACA,WAAW,GAAG1xB,IAAI;QACvBA,IAAI,CAAC6sB,IAAI,CAAC,CAAC;MACb;IACF;EACF;;EAEA;AACF;AACA;EACErpB,KAAKA,CAAA,EAAG;IACN,IAAI,CAACrE,OAAO,CAAC,OAAO,CAAC;;IAErB;IACA,IAAI,CAAC4zB,mBAAmB,GAAGryB,QAAQ,CAACgrB,aAAa;IACjD,IAAI,CAACgG,WAAW,GAAG,IAAI;IACvB,IAAI,CAACvD,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC6E,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACp1B,IAAI,CAAC,CAAC;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEs0B,KAAKA,CAACzzB,KAAK,EAAE;IACX,MAAMQ,KAAK,GAAG,IAAI,CAAC0vB,KAAK,CAACjsB,OAAO,CAAC,IAAI,CAACgvB,WAAW,CAAC;IAClD,IAAIv2B,KAAK,CAACC,OAAO,CAAC,IAAI,CAACuzB,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACA,KAAK,CAAC1xB,OAAO,CAAC+C,IAAI,IAAIA,IAAI,CAACssB,OAAO,CAAC,CAAC,CAAC;IAC5C;IACAoC,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI,CAACvvB,OAAO,CAACV,KAAK,EAAE;MAClBQ;IACF,CAAC,CAAC;IACFiyB,QAAQ,CAACqB,UAAU,GAAG,IAAI;IAC1B,IAAI,CAACpzB,OAAO,CAAC,UAAU,EAAE;MACvBoB,IAAI,EAAE;IACR,CAAC,CAAC;IACF,IAAI,IAAI,CAACmsB,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAACD,IAAI,CAAC,CAAC;IACnB;IACA,IAAIhuB,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,UAAU,EAAE;MAC9C,IAAI,IAAI,CAACiuB,KAAK,EAAE;QACd,MAAMuG,cAAc,GAAGvyB,QAAQ,CAACC,aAAa,CAAC,mCAAmC,CAAC;QAClF,IAAIsyB,cAAc,EAAE;UAClBA,cAAc,CAAC5H,MAAM,CAAC,CAAC;QACzB;MACF;IACF;;IAEA;IACA,IAAIptB,eAAe,CAAC,IAAI,CAAC80B,mBAAmB,CAAC,EAAE;MAC7C,IAAI,CAACA,mBAAmB,CAAClY,KAAK,CAAC,CAAC;IAClC;EACF;;EAEA;AACF;AACA;AACA;EACEmY,gBAAgBA,CAAA,EAAG;IACjB,IAAI,CAAC7zB,OAAO,CAAC,QAAQ,EAAE;MACrBoB,IAAI,EAAE;IACR,CAAC,CAAC;IACF2wB,QAAQ,CAACqB,UAAU,GAAG,IAAI;EAC5B;;EAEA;AACF;AACA;AACA;EACEpE,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACzB,KAAK,GAAG,IAAIuE,cAAc,CAAC;MAC9Bv1B,MAAM,EAAE,IAAI,CAACJ,OAAO,CAAC23B,cAAc,IAAIvyB,QAAQ,CAACO,IAAI;MACpDqhB,KAAK,EAAE;QACLkI,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7B0B,MAAM,EAAE,IAAI,CAACA;MACf;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE2G,SAASA,CAAC7yB,IAAI,EAAEyyB,OAAO,EAAE;IACvB,MAAMxzB,KAAK,GAAG,IAAI,CAAC0vB,KAAK,CAACjsB,OAAO,CAAC1C,IAAI,CAAC;IACtC,IAAIf,KAAK,KAAK,IAAI,CAAC0vB,KAAK,CAAC/uB,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAACysB,QAAQ,CAAC,CAAC;IACjB,CAAC,MAAM;MACL,MAAM3f,SAAS,GAAG+lB,OAAO,GAAGxzB,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC;MACjD,IAAI,CAAC4tB,IAAI,CAACngB,SAAS,EAAE+lB,OAAO,CAAC;IAC/B;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEC,sBAAsBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAChB,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACjF,IAAI,CAAC,CAAC;IACzB;IACA,IAAI,CAAC,IAAI,CAAC6F,QAAQ,CAAC,CAAC,EAAE;MACpB,IAAI,CAACU,gBAAgB,CAAC,CAAC;IACzB;EACF;;EAEA;AACF;AACA;AACA;EACExB,UAAUA,CAAA,EAAG;IACX,MAAM0B,QAAQ,GAAG,IAAI,CAAC53B,OAAO,CAAC43B,QAAQ,IAAI,MAAM;IAChD,IAAI,CAACnI,EAAE,GAAG,GAAGmI,QAAQ,KAAKvxB,IAAI,CAAC,CAAC,EAAE;EACpC;AACF;AAEA,MAAMwxB,YAAY,GAAG,OAAOvkB,MAAM,KAAK,WAAW;AAClD,MAAMwkB,IAAI,CAAC;EACT1zB,WAAWA,CAAA,EAAG,CAAC;AACjB;AACA,IAAIyzB,YAAY,EAAE;EAChB54B,MAAM,CAACiH,MAAM,CAAC0vB,QAAQ,EAAE;IACtBC,IAAI,EAAEiC,IAAI;IACVnH,IAAI,EAAEmH;EACR,CAAC,CAAC;AACJ,CAAC,MAAM;EACL74B,MAAM,CAACiH,MAAM,CAAC0vB,QAAQ,EAAE;IACtBC,IAAI;IACJlF;EACF,CAAC,CAAC;AACJ;AAEA,SAASiF,QAAQ,IAAImC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}