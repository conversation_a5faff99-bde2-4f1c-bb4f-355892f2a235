{"version": 3, "file": "menu.mjs", "sources": ["../../../../../../src/material/menu/menu-panel.ts", "../../../../../../src/material/menu/menu-item.ts", "../../../../../../src/material/menu/menu-item.html", "../../../../../../src/material/menu/menu-errors.ts", "../../../../../../src/material/menu/menu-content.ts", "../../../../../../src/material/menu/menu-animations.ts", "../../../../../../src/material/menu/menu.ts", "../../../../../../src/material/menu/menu.html", "../../../../../../src/material/menu/menu-trigger.ts", "../../../../../../src/material/menu/module.ts", "../../../../../../src/material/menu/menu_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {EventEmitter, TemplateRef, InjectionToken} from '@angular/core';\nimport {MenuPositionX, MenuPositionY} from './menu-positions';\nimport {Direction} from '@angular/cdk/bidi';\nimport {FocusOrigin} from '@angular/cdk/a11y';\nimport {MatMenuContent} from './menu-content';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nexport const MAT_MENU_PANEL = new InjectionToken<MatMenuPanel>('MAT_MENU_PANEL');\n\n/**\n * Interface for a custom menu panel that can be used with `matMenuTriggerFor`.\n * @docs-private\n */\nexport interface MatMenuPanel<T = any> {\n  xPosition: MenuPositionX;\n  yPosition: MenuPositionY;\n  overlapTrigger: boolean;\n  templateRef: TemplateRef<any>;\n  readonly close: EventEmitter<void | 'click' | 'keydown' | 'tab'>;\n  parentMenu?: MatMenuPanel | undefined;\n  direction?: Direction;\n  focusFirstItem: (origin?: FocusOrigin) => void;\n  resetActiveItem: () => void;\n  setPositionClasses?: (x: MenuPositionX, y: MenuPositionY) => void;\n  setElevation?(depth: number): void;\n  lazyContent?: MatMenuContent;\n  backdropClass?: string;\n  overlayPanelClass?: string | string[];\n  hasBackdrop?: boolean;\n  readonly panelId?: string;\n\n  /**\n   * @deprecated To be removed.\n   * @breaking-change 8.0.0\n   */\n  addItem?: (item: T) => void;\n\n  /**\n   * @deprecated To be removed.\n   * @breaking-change 8.0.0\n   */\n  removeItem?: (item: T) => void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  OnDestroy,\n  ViewEncapsulation,\n  Inject,\n  Optional,\n  Input,\n  AfterViewInit,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport {\n  CanDisable,\n  CanDisableRipple,\n  mixinDisabled,\n  mixinDisableRipple,\n} from '@angular/material/core';\nimport {FocusableOption, FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {Subject} from 'rxjs';\nimport {DOCUMENT} from '@angular/common';\nimport {MatMenuPanel, MAT_MENU_PANEL} from './menu-panel';\n\n// Boilerplate for applying mixins to MatMenuItem.\n/** @docs-private */\nconst _MatMenuItemBase = mixinDisableRipple(mixinDisabled(class {}));\n\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\n@Component({\n  selector: '[mat-menu-item]',\n  exportAs: 'matMenuItem',\n  inputs: ['disabled', 'disableRipple'],\n  host: {\n    '[attr.role]': 'role',\n    'class': 'mat-mdc-menu-item mat-mdc-focus-indicator',\n    '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n    '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n    '[attr.tabindex]': '_getTabIndex()',\n    '[attr.aria-disabled]': 'disabled',\n    '[attr.disabled]': 'disabled || null',\n    '(click)': '_checkDisabled($event)',\n    '(mouseenter)': '_handleMouseEnter()',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  templateUrl: 'menu-item.html',\n})\nexport class MatMenuItem\n  extends _MatMenuItemBase\n  implements FocusableOption, CanDisable, CanDisableRipple, AfterViewInit, OnDestroy\n{\n  /** ARIA role for the menu item. */\n  @Input() role: 'menuitem' | 'menuitemradio' | 'menuitemcheckbox' = 'menuitem';\n\n  /** Stream that emits when the menu item is hovered. */\n  readonly _hovered: Subject<MatMenuItem> = new Subject<MatMenuItem>();\n\n  /** Stream that emits when the menu item is focused. */\n  readonly _focused = new Subject<MatMenuItem>();\n\n  /** Whether the menu item is highlighted. */\n  _highlighted: boolean = false;\n\n  /** Whether the menu item acts as a trigger for a sub-menu. */\n  _triggersSubmenu: boolean = false;\n\n  constructor(\n    elementRef: ElementRef<HTMLElement>,\n    document: any,\n    focusMonitor: FocusMonitor,\n    parentMenu: MatMenuPanel<MatMenuItem> | undefined,\n    changeDetectorRef: ChangeDetectorRef,\n  );\n\n  /**\n   * @deprecated `document`, `changeDetectorRef` and `focusMonitor` to become required.\n   * @breaking-change 12.0.0\n   */\n  constructor(\n    elementRef: ElementRef<HTMLElement>,\n    document?: any,\n    focusMonitor?: FocusMonitor,\n    parentMenu?: MatMenuPanel<MatMenuItem>,\n    changeDetectorRef?: ChangeDetectorRef,\n  );\n\n  constructor(\n    private _elementRef: ElementRef<HTMLElement>,\n    @Inject(DOCUMENT) private _document?: any,\n    private _focusMonitor?: FocusMonitor,\n    @Inject(MAT_MENU_PANEL) @Optional() public _parentMenu?: MatMenuPanel<MatMenuItem>,\n    private _changeDetectorRef?: ChangeDetectorRef,\n  ) {\n    super();\n    _parentMenu?.addItem?.(this);\n  }\n\n  /** Focuses the menu item. */\n  focus(origin?: FocusOrigin, options?: FocusOptions): void {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n    } else {\n      this._getHostElement().focus(options);\n    }\n\n    this._focused.next(this);\n  }\n\n  ngAfterViewInit() {\n    if (this._focusMonitor) {\n      // Start monitoring the element, so it gets the appropriate focused classes. We want\n      // to show the focus style for menu items only when the focus was not caused by a\n      // mouse or touch interaction.\n      this._focusMonitor.monitor(this._elementRef, false);\n    }\n  }\n\n  ngOnDestroy() {\n    if (this._focusMonitor) {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n\n    if (this._parentMenu && this._parentMenu.removeItem) {\n      this._parentMenu.removeItem(this);\n    }\n\n    this._hovered.complete();\n    this._focused.complete();\n  }\n\n  /** Used to set the `tabindex`. */\n  _getTabIndex(): string {\n    return this.disabled ? '-1' : '0';\n  }\n\n  /** Returns the host DOM element. */\n  _getHostElement(): HTMLElement {\n    return this._elementRef.nativeElement;\n  }\n\n  /** Prevents the default element actions if it is disabled. */\n  _checkDisabled(event: Event): void {\n    if (this.disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n\n  /** Emits to the hover stream. */\n  _handleMouseEnter() {\n    this._hovered.next(this);\n  }\n\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel(): string {\n    const clone = this._elementRef.nativeElement.cloneNode(true) as HTMLElement;\n    const icons = clone.querySelectorAll('mat-icon, .material-icons');\n\n    // Strip away icons, so they don't show up in the text.\n    for (let i = 0; i < icons.length; i++) {\n      icons[i].remove();\n    }\n\n    return clone.textContent?.trim() || '';\n  }\n\n  _setHighlighted(isHighlighted: boolean) {\n    // We need to mark this for check for the case where the content is coming from a\n    // `matMenuContent` whose change detection tree is at the declaration position,\n    // not the insertion position. See #23175.\n    // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n    this._highlighted = isHighlighted;\n    this._changeDetectorRef?.markForCheck();\n  }\n\n  _setTriggersSubmenu(triggersSubmenu: boolean) {\n    // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n    this._triggersSubmenu = triggersSubmenu;\n    this._changeDetectorRef?.markForCheck();\n  }\n\n  _hasFocus(): boolean {\n    return this._document && this._document.activeElement === this._getHostElement();\n  }\n}\n", "<ng-content select=\"mat-icon, [matMenuItemIcon]\"></ng-content>\n<span class=\"mat-mdc-menu-item-text\"><ng-content></ng-content></span>\n<div class=\"mat-mdc-menu-ripple\" matRipple\n     [matRippleDisabled]=\"disableRipple || disabled\"\n     [matRippleTrigger]=\"_getHostElement()\">\n</div>\n<svg\n  *ngIf=\"_triggersSubmenu\"\n  class=\"mat-mdc-menu-submenu-icon\"\n  viewBox=\"0 0 5 10\"\n  focusable=\"false\"\n  aria-hidden=\"true\"><polygon points=\"0,0 5,5 0,10\"/></svg>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nexport function throwMatMenuInvalidPositionX() {\n  throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nexport function throwMatMenuInvalidPositionY() {\n  throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nexport function throwMatMenuRecursiveError() {\n  throw Error(\n    `matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` +\n      `not a parent of the trigger or move the trigger outside of the menu.`,\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DomPortalOutlet, TemplatePortal} from '@angular/cdk/portal';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  ApplicationRef,\n  ChangeDetectorRef,\n  ComponentFactoryResolver,\n  Directive,\n  Inject,\n  InjectionToken,\n  Injector,\n  OnDestroy,\n  TemplateRef,\n  ViewContainerRef,\n} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_MENU_CONTENT = new InjectionToken<MatMenuContent>('MatMenuContent');\n\n@Directive()\nexport abstract class _MatMenuContentBase implements OnDestroy {\n  private _portal: TemplatePortal<any>;\n  private _outlet: DomPortalOutlet;\n\n  /** Emits when the menu content has been attached. */\n  readonly _attached = new Subject<void>();\n\n  constructor(\n    template: TemplateRef<any>,\n    componentFactoryResolver: ComponentFactoryResolver,\n    appRef: ApplicationRef,\n    injector: Injector,\n    viewContainerRef: ViewContainerRef,\n    document: any,\n    changeDetectorRef: ChangeDetectorRef,\n  );\n\n  /**\n   * @deprecated `changeDetectorRef` is now a required parameter.\n   * @breaking-change 9.0.0\n   */\n  constructor(\n    template: TemplateRef<any>,\n    componentFactoryResolver: ComponentFactoryResolver,\n    appRef: ApplicationRef,\n    injector: Injector,\n    viewContainerRef: ViewContainerRef,\n    document: any,\n    changeDetectorRef?: ChangeDetectorRef,\n  );\n\n  constructor(\n    private _template: TemplateRef<any>,\n    private _componentFactoryResolver: ComponentFactoryResolver,\n    private _appRef: ApplicationRef,\n    private _injector: Injector,\n    private _viewContainerRef: ViewContainerRef,\n    @Inject(DOCUMENT) private _document: any,\n    private _changeDetectorRef?: ChangeDetectorRef,\n  ) {}\n\n  /**\n   * Attaches the content with a particular context.\n   * @docs-private\n   */\n  attach(context: any = {}) {\n    if (!this._portal) {\n      this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n    }\n\n    this.detach();\n\n    if (!this._outlet) {\n      this._outlet = new DomPortalOutlet(\n        this._document.createElement('div'),\n        this._componentFactoryResolver,\n        this._appRef,\n        this._injector,\n      );\n    }\n\n    const element: HTMLElement = this._template.elementRef.nativeElement;\n\n    // Because we support opening the same menu from different triggers (which in turn have their\n    // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n    // risk it staying attached to a pane that's no longer in the DOM.\n    element.parentNode!.insertBefore(this._outlet.outletElement, element);\n\n    // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n    // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n    // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n    // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n    // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n    // @breaking-change 9.0.0 Make change detector ref required\n    this._changeDetectorRef?.markForCheck();\n    this._portal.attach(this._outlet, context);\n    this._attached.next();\n  }\n\n  /**\n   * Detaches the content.\n   * @docs-private\n   */\n  detach() {\n    if (this._portal.isAttached) {\n      this._portal.detach();\n    }\n  }\n\n  ngOnDestroy() {\n    if (this._outlet) {\n      this._outlet.dispose();\n    }\n  }\n}\n\n/** Menu content that will be rendered lazily once the menu is opened. */\n@Directive({\n  selector: 'ng-template[matMenuContent]',\n  providers: [{provide: MAT_MENU_CONTENT, useExisting: MatMenuContent}],\n})\nexport class MatMenuContent extends _MatMenuContentBase {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  trigger,\n  state,\n  style,\n  animate,\n  transition,\n  AnimationTriggerMetadata,\n} from '@angular/animations';\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n */\nexport const matMenuAnimations: {\n  readonly transformMenu: AnimationTriggerMetadata;\n  readonly fadeInItems: AnimationTriggerMetadata;\n} = {\n  /**\n   * This animation controls the menu panel's entry and exit from the page.\n   *\n   * When the menu panel is added to the DOM, it scales in and fades in its border.\n   *\n   * When the menu panel is removed from the DOM, it simply fades out after a brief\n   * delay to display the ripple.\n   */\n  transformMenu: trigger('transformMenu', [\n    state(\n      'void',\n      style({\n        opacity: 0,\n        transform: 'scale(0.8)',\n      }),\n    ),\n    transition(\n      'void => enter',\n      animate(\n        '120ms cubic-bezier(0, 0, 0.2, 1)',\n        style({\n          opacity: 1,\n          transform: 'scale(1)',\n        }),\n      ),\n    ),\n    transition('* => void', animate('100ms 25ms linear', style({opacity: 0}))),\n  ]),\n\n  /**\n   * This animation fades in the background color and content of the menu panel\n   * after its containing element is scaled in.\n   */\n  fadeInItems: trigger('fadeInItems', [\n    // TODO(crisbeto): this is inside the `transformMenu`\n    // now. Remove next time we do breaking changes.\n    state('showing', style({opacity: 1})),\n    transition('void => *', [\n      style({opacity: 0}),\n      animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n    ]),\n  ]),\n};\n\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nexport const fadeInItems = matMenuAnimations.fadeInItems;\n\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nexport const transformMenu = matMenuAnimations.transformMenu;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  Component,\n  ContentChild,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnDestroy,\n  Output,\n  TemplateRef,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  OnInit,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport {AnimationEvent} from '@angular/animations';\nimport {FocusKeyManager, FocusOrigin} from '@angular/cdk/a11y';\nimport {Direction} from '@angular/cdk/bidi';\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {\n  ESCAPE,\n  LEFT_ARROW,\n  RIGHT_ARROW,\n  DOWN_ARROW,\n  UP_ARROW,\n  hasModifierKey,\n} from '@angular/cdk/keycodes';\nimport {merge, Observable, Subject, Subscription} from 'rxjs';\nimport {startWith, switchMap, take} from 'rxjs/operators';\nimport {MatMenuItem} from './menu-item';\nimport {MatMenuPanel, MAT_MENU_PANEL} from './menu-panel';\nimport {MenuPositionX, MenuPositionY} from './menu-positions';\nimport {throwMatMenuInvalidPositionX, throwMatMenuInvalidPositionY} from './menu-errors';\nimport {MatMenuContent, MAT_MENU_CONTENT} from './menu-content';\nimport {matMenuAnimations} from './menu-animations';\n\nlet menuPanelUid = 0;\n\n/** Reason why the menu was closed. */\nexport type MenuCloseReason = void | 'click' | 'keydown' | 'tab';\n\n/** Default `mat-menu` options that can be overridden. */\nexport interface MatMenuDefaultOptions {\n  /** The x-axis position of the menu. */\n  xPosition: MenuPositionX;\n\n  /** The y-axis position of the menu. */\n  yPosition: MenuPositionY;\n\n  /** Whether the menu should overlap the menu trigger. */\n  overlapTrigger: boolean;\n\n  /** Class to be applied to the menu's backdrop. */\n  backdropClass: string;\n\n  /** Class or list of classes to be applied to the menu's overlay panel. */\n  overlayPanelClass?: string | string[];\n\n  /** Whether the menu has a backdrop. */\n  hasBackdrop?: boolean;\n}\n\n/** Injection token to be used to override the default options for `mat-menu`. */\nexport const MAT_MENU_DEFAULT_OPTIONS = new InjectionToken<MatMenuDefaultOptions>(\n  'mat-menu-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/** @docs-private */\nexport function MAT_MENU_DEFAULT_OPTIONS_FACTORY(): MatMenuDefaultOptions {\n  return {\n    overlapTrigger: false,\n    xPosition: 'after',\n    yPosition: 'below',\n    backdropClass: 'cdk-overlay-transparent-backdrop',\n  };\n}\n\n/** Base class with all of the `MatMenu` functionality. */\n@Directive()\nexport class _MatMenuBase\n  implements AfterContentInit, MatMenuPanel<MatMenuItem>, OnInit, OnDestroy\n{\n  private _keyManager: FocusKeyManager<MatMenuItem>;\n  private _xPosition: MenuPositionX;\n  private _yPosition: MenuPositionY;\n  private _firstItemFocusSubscription?: Subscription;\n  private _previousElevation: string;\n  protected _elevationPrefix: string;\n  protected _baseElevation: number;\n\n  /** All items inside the menu. Includes items nested inside another menu. */\n  @ContentChildren(MatMenuItem, {descendants: true}) _allItems: QueryList<MatMenuItem>;\n\n  /** Only the direct descendant menu items. */\n  _directDescendantItems = new QueryList<MatMenuItem>();\n\n  /** Config object to be passed into the menu's ngClass */\n  _classList: {[key: string]: boolean} = {};\n\n  /** Current state of the panel animation. */\n  _panelAnimationState: 'void' | 'enter' = 'void';\n\n  /** Emits whenever an animation on the menu completes. */\n  readonly _animationDone = new Subject<AnimationEvent>();\n\n  /** Whether the menu is animating. */\n  _isAnimating: boolean;\n\n  /** Parent menu of the current menu panel. */\n  parentMenu: MatMenuPanel | undefined;\n\n  /** Layout direction of the menu. */\n  direction: Direction;\n\n  /** Class or list of classes to be added to the overlay panel. */\n  overlayPanelClass: string | string[];\n\n  /** Class to be added to the backdrop element. */\n  @Input() backdropClass: string;\n\n  /** aria-label for the menu panel. */\n  @Input('aria-label') ariaLabel: string;\n\n  /** aria-labelledby for the menu panel. */\n  @Input('aria-labelledby') ariaLabelledby: string;\n\n  /** aria-describedby for the menu panel. */\n  @Input('aria-describedby') ariaDescribedby: string;\n\n  /** Position of the menu in the X axis. */\n  @Input()\n  get xPosition(): MenuPositionX {\n    return this._xPosition;\n  }\n  set xPosition(value: MenuPositionX) {\n    if (\n      value !== 'before' &&\n      value !== 'after' &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throwMatMenuInvalidPositionX();\n    }\n    this._xPosition = value;\n    this.setPositionClasses();\n  }\n\n  /** Position of the menu in the Y axis. */\n  @Input()\n  get yPosition(): MenuPositionY {\n    return this._yPosition;\n  }\n  set yPosition(value: MenuPositionY) {\n    if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionY();\n    }\n    this._yPosition = value;\n    this.setPositionClasses();\n  }\n\n  /** @docs-private */\n  @ViewChild(TemplateRef) templateRef: TemplateRef<any>;\n\n  /**\n   * List of the items inside of a menu.\n   * @deprecated\n   * @breaking-change 8.0.0\n   */\n  @ContentChildren(MatMenuItem, {descendants: false}) items: QueryList<MatMenuItem>;\n\n  /**\n   * Menu content that will be rendered lazily.\n   * @docs-private\n   */\n  @ContentChild(MAT_MENU_CONTENT) lazyContent: MatMenuContent;\n\n  /** Whether the menu should overlap its trigger. */\n  @Input()\n  get overlapTrigger(): boolean {\n    return this._overlapTrigger;\n  }\n  set overlapTrigger(value: BooleanInput) {\n    this._overlapTrigger = coerceBooleanProperty(value);\n  }\n  private _overlapTrigger: boolean;\n\n  /** Whether the menu has a backdrop. */\n  @Input()\n  get hasBackdrop(): boolean | undefined {\n    return this._hasBackdrop;\n  }\n  set hasBackdrop(value: BooleanInput) {\n    this._hasBackdrop = coerceBooleanProperty(value);\n  }\n  private _hasBackdrop: boolean | undefined;\n\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @param classes list of class names\n   */\n  @Input('class')\n  set panelClass(classes: string) {\n    const previousPanelClass = this._previousPanelClass;\n\n    if (previousPanelClass && previousPanelClass.length) {\n      previousPanelClass.split(' ').forEach((className: string) => {\n        this._classList[className] = false;\n      });\n    }\n\n    this._previousPanelClass = classes;\n\n    if (classes && classes.length) {\n      classes.split(' ').forEach((className: string) => {\n        this._classList[className] = true;\n      });\n\n      this._elementRef.nativeElement.className = '';\n    }\n  }\n  private _previousPanelClass: string;\n\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @deprecated Use `panelClass` instead.\n   * @breaking-change 8.0.0\n   */\n  @Input()\n  get classList(): string {\n    return this.panelClass;\n  }\n  set classList(classes: string) {\n    this.panelClass = classes;\n  }\n\n  /** Event emitted when the menu is closed. */\n  @Output() readonly closed: EventEmitter<MenuCloseReason> = new EventEmitter<MenuCloseReason>();\n\n  /**\n   * Event emitted when the menu is closed.\n   * @deprecated Switch to `closed` instead\n   * @breaking-change 8.0.0\n   */\n  @Output() readonly close: EventEmitter<MenuCloseReason> = this.closed;\n\n  readonly panelId = `mat-menu-panel-${menuPanelUid++}`;\n\n  constructor(\n    elementRef: ElementRef<HTMLElement>,\n    ngZone: NgZone,\n    defaultOptions: MatMenuDefaultOptions,\n    changeDetectorRef: ChangeDetectorRef,\n  );\n\n  /**\n   * @deprecated `_changeDetectorRef` to become a required parameter.\n   * @breaking-change 15.0.0\n   */\n  constructor(\n    elementRef: ElementRef<HTMLElement>,\n    ngZone: NgZone,\n    defaultOptions: MatMenuDefaultOptions,\n    changeDetectorRef?: ChangeDetectorRef,\n  );\n\n  constructor(\n    private _elementRef: ElementRef<HTMLElement>,\n    private _ngZone: NgZone,\n    @Inject(MAT_MENU_DEFAULT_OPTIONS) defaultOptions: MatMenuDefaultOptions,\n    // @breaking-change 15.0.0 `_changeDetectorRef` to become a required parameter.\n    private _changeDetectorRef?: ChangeDetectorRef,\n  ) {\n    this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n    this._xPosition = defaultOptions.xPosition;\n    this._yPosition = defaultOptions.yPosition;\n    this.backdropClass = defaultOptions.backdropClass;\n    this._overlapTrigger = defaultOptions.overlapTrigger;\n    this._hasBackdrop = defaultOptions.hasBackdrop;\n  }\n\n  ngOnInit() {\n    this.setPositionClasses();\n  }\n\n  ngAfterContentInit() {\n    this._updateDirectDescendants();\n    this._keyManager = new FocusKeyManager(this._directDescendantItems)\n      .withWrap()\n      .withTypeAhead()\n      .withHomeAndEnd();\n    this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n\n    // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n    // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n    // is internal and we know that it gets completed on destroy.\n    this._directDescendantItems.changes\n      .pipe(\n        startWith(this._directDescendantItems),\n        switchMap(items => merge(...items.map((item: MatMenuItem) => item._focused))),\n      )\n      .subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem as MatMenuItem));\n\n    this._directDescendantItems.changes.subscribe((itemsList: QueryList<MatMenuItem>) => {\n      // Move focus to another item, if the active item is removed from the list.\n      // We need to debounce the callback, because multiple items might be removed\n      // in quick succession.\n      const manager = this._keyManager;\n\n      if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n        const items = itemsList.toArray();\n        const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n\n        if (items[index] && !items[index].disabled) {\n          manager.setActiveItem(index);\n        } else {\n          manager.setNextItemActive();\n        }\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._directDescendantItems.destroy();\n    this.closed.complete();\n    this._firstItemFocusSubscription?.unsubscribe();\n  }\n\n  /** Stream that emits whenever the hovered menu item changes. */\n  _hovered(): Observable<MatMenuItem> {\n    // Coerce the `changes` property because Angular types it as `Observable<any>`\n    const itemChanges = this._directDescendantItems.changes as Observable<QueryList<MatMenuItem>>;\n    return itemChanges.pipe(\n      startWith(this._directDescendantItems),\n      switchMap(items => merge(...items.map((item: MatMenuItem) => item._hovered))),\n    ) as Observable<MatMenuItem>;\n  }\n\n  /*\n   * Registers a menu item with the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  addItem(_item: MatMenuItem) {}\n\n  /**\n   * Removes an item from the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  removeItem(_item: MatMenuItem) {}\n\n  /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n  _handleKeydown(event: KeyboardEvent) {\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n\n    switch (keyCode) {\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this.closed.emit('keydown');\n        }\n        break;\n      case LEFT_ARROW:\n        if (this.parentMenu && this.direction === 'ltr') {\n          this.closed.emit('keydown');\n        }\n        break;\n      case RIGHT_ARROW:\n        if (this.parentMenu && this.direction === 'rtl') {\n          this.closed.emit('keydown');\n        }\n        break;\n      default:\n        if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n          manager.setFocusOrigin('keyboard');\n        }\n\n        manager.onKeydown(event);\n        return;\n    }\n\n    // Don't allow the event to propagate if we've already handled it, or it may\n    // end up reaching other overlays that were opened earlier (see #22694).\n    event.stopPropagation();\n  }\n\n  /**\n   * Focus the first item in the menu.\n   * @param origin Action from which the focus originated. Used to set the correct styling.\n   */\n  focusFirstItem(origin: FocusOrigin = 'program'): void {\n    // Wait for `onStable` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n    this._firstItemFocusSubscription?.unsubscribe();\n    this._firstItemFocusSubscription = this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      let menuPanel: HTMLElement | null = null;\n\n      if (this._directDescendantItems.length) {\n        // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n        // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n        // because the panel is inside an `ng-template`. We work around it by starting from one of\n        // the items and walking up the DOM.\n        menuPanel = this._directDescendantItems.first!._getHostElement().closest('[role=\"menu\"]');\n      }\n\n      // If an item in the menuPanel is already focused, avoid overriding the focus.\n      if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n        const manager = this._keyManager;\n        manager.setFocusOrigin(origin).setFirstItemActive();\n\n        // If there's no active item at this point, it means that all the items are disabled.\n        // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n        // give _some_ feedback to screen readers.\n        if (!manager.activeItem && menuPanel) {\n          menuPanel.focus();\n        }\n      }\n    });\n  }\n\n  /**\n   * Resets the active item in the menu. This is used when the menu is opened, allowing\n   * the user to start from the first option when pressing the down arrow.\n   */\n  resetActiveItem() {\n    this._keyManager.setActiveItem(-1);\n  }\n\n  /**\n   * Sets the menu panel elevation.\n   * @param depth Number of parent menus that come before the menu.\n   */\n  setElevation(depth: number): void {\n    // The elevation starts at the base and increases by one for each level.\n    // Capped at 24 because that's the maximum elevation defined in the Material design spec.\n    const elevation = Math.min(this._baseElevation + depth, 24);\n    const newElevation = `${this._elevationPrefix}${elevation}`;\n    const customElevation = Object.keys(this._classList).find(className => {\n      return className.startsWith(this._elevationPrefix);\n    });\n\n    if (!customElevation || customElevation === this._previousElevation) {\n      if (this._previousElevation) {\n        this._classList[this._previousElevation] = false;\n      }\n\n      this._classList[newElevation] = true;\n      this._previousElevation = newElevation;\n    }\n  }\n\n  /**\n   * Adds classes to the menu panel based on its position. Can be used by\n   * consumers to add specific styling based on the position.\n   * @param posX Position of the menu along the x axis.\n   * @param posY Position of the menu along the y axis.\n   * @docs-private\n   */\n  setPositionClasses(posX: MenuPositionX = this.xPosition, posY: MenuPositionY = this.yPosition) {\n    const classes = this._classList;\n    classes['mat-menu-before'] = posX === 'before';\n    classes['mat-menu-after'] = posX === 'after';\n    classes['mat-menu-above'] = posY === 'above';\n    classes['mat-menu-below'] = posY === 'below';\n\n    // @breaking-change 15.0.0 Remove null check for `_changeDetectorRef`.\n    this._changeDetectorRef?.markForCheck();\n  }\n\n  /** Starts the enter animation. */\n  _startAnimation() {\n    // @breaking-change 8.0.0 Combine with _resetAnimation.\n    this._panelAnimationState = 'enter';\n  }\n\n  /** Resets the panel animation to its initial state. */\n  _resetAnimation() {\n    // @breaking-change 8.0.0 Combine with _startAnimation.\n    this._panelAnimationState = 'void';\n  }\n\n  /** Callback that is invoked when the panel animation completes. */\n  _onAnimationDone(event: AnimationEvent) {\n    this._animationDone.next(event);\n    this._isAnimating = false;\n  }\n\n  _onAnimationStart(event: AnimationEvent) {\n    this._isAnimating = true;\n\n    // Scroll the content element to the top as soon as the animation starts. This is necessary,\n    // because we move focus to the first item while it's still being animated, which can throw\n    // the browser off when it determines the scroll position. Alternatively we can move focus\n    // when the animation is done, however moving focus asynchronously will interrupt screen\n    // readers which are in the process of reading out the menu already. We take the `element`\n    // from the `event` since we can't use a `ViewChild` to access the pane.\n    if (event.toState === 'enter' && this._keyManager.activeItemIndex === 0) {\n      event.element.scrollTop = 0;\n    }\n  }\n\n  /**\n   * Sets up a stream that will keep track of any newly-added menu items and will update the list\n   * of direct descendants. We collect the descendants this way, because `_allItems` can include\n   * items that are part of child menus, and using a custom way of registering items is unreliable\n   * when it comes to maintaining the item order.\n   */\n  private _updateDirectDescendants() {\n    this._allItems.changes\n      .pipe(startWith(this._allItems))\n      .subscribe((items: QueryList<MatMenuItem>) => {\n        this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n        this._directDescendantItems.notifyOnChanges();\n      });\n  }\n}\n\n@Component({\n  selector: 'mat-menu',\n  templateUrl: 'menu.html',\n  styleUrls: ['menu.css'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  exportAs: 'matMenu',\n  host: {\n    '[attr.aria-label]': 'null',\n    '[attr.aria-labelledby]': 'null',\n    '[attr.aria-describedby]': 'null',\n    'ngSkipHydration': '',\n  },\n  animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems],\n  providers: [{provide: MAT_MENU_PANEL, useExisting: MatMenu}],\n})\nexport class MatMenu extends _MatMenuBase {\n  protected override _elevationPrefix = 'mat-elevation-z';\n  protected override _baseElevation = 8;\n\n  /*\n   * @deprecated `changeDetectorRef` parameter will become a required parameter.\n   * @breaking-change 15.0.0\n   */\n  constructor(\n    elementRef: ElementRef<HTMLElement>,\n    ngZone: NgZone,\n    defaultOptions: MatMenuDefaultOptions,\n  );\n\n  constructor(\n    _elementRef: ElementRef<HTMLElement>,\n    _ngZone: NgZone,\n    @Inject(MAT_MENU_DEFAULT_OPTIONS) _defaultOptions: MatMenuDefaultOptions,\n    changeDetectorRef?: ChangeDetectorRef,\n  ) {\n    super(_elementRef, _ngZone, _defaultOptions, changeDetectorRef);\n  }\n}\n", "<ng-template>\n  <div\n    class=\"mat-mdc-menu-panel mat-mdc-elevation-specific\"\n    [id]=\"panelId\"\n    [ngClass]=\"_classList\"\n    (keydown)=\"_handleKeydown($event)\"\n    (click)=\"closed.emit('click')\"\n    [@transformMenu]=\"_panelAnimationState\"\n    (@transformMenu.start)=\"_onAnimationStart($event)\"\n    (@transformMenu.done)=\"_onAnimationDone($event)\"\n    tabindex=\"-1\"\n    role=\"menu\"\n    [attr.aria-label]=\"ariaLabel || null\"\n    [attr.aria-labelledby]=\"ariaLabelledby || null\"\n    [attr.aria-describedby]=\"ariaDescribedby || null\">\n    <div class=\"mat-mdc-menu-content\">\n      <ng-content></ng-content>\n    </div>\n  </div>\n</ng-template>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  FocusMonitor,\n  FocusOrigin,\n  isFakeMousedownFromScreenReader,\n  isFakeTouchstartFromScreenReader,\n} from '@angular/cdk/a11y';\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {ENTER, LEFT_ARROW, RIGHT_ARROW, SPACE} from '@angular/cdk/keycodes';\nimport {\n  FlexibleConnectedPositionStrategy,\n  HorizontalConnectionPos,\n  Overlay,\n  OverlayConfig,\n  OverlayRef,\n  ScrollStrategy,\n  VerticalConnectionPos,\n} from '@angular/cdk/overlay';\nimport {TemplatePortal} from '@angular/cdk/portal';\nimport {\n  AfterContentInit,\n  ChangeDetectorRef,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  inject,\n  Inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnDestroy,\n  Optional,\n  Output,\n  Self,\n  ViewContainerRef,\n} from '@angular/core';\nimport {normalizePassiveListenerOptions} from '@angular/cdk/platform';\nimport {asapScheduler, merge, Observable, of as observableOf, Subscription} from 'rxjs';\nimport {delay, filter, take, takeUntil} from 'rxjs/operators';\nimport {_MatMenuBase, MenuCloseReason} from './menu';\nimport {throwMatMenuRecursiveError} from './menu-errors';\nimport {MatMenuItem} from './menu-item';\nimport {MAT_MENU_PANEL, MatMenuPanel} from './menu-panel';\nimport {MenuPositionX, MenuPositionY} from './menu-positions';\n\n/** Injection token that determines the scroll handling while the menu is open. */\nexport const MAT_MENU_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'mat-menu-scroll-strategy',\n);\n\n/** @docs-private */\nexport function MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay: Overlay): () => ScrollStrategy {\n  return () => overlay.scrollStrategies.reposition();\n}\n\n/** @docs-private */\nexport const MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_MENU_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY,\n};\n\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({passive: true});\n\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nexport const MENU_PANEL_TOP_PADDING = 8;\n\n@Directive({\n  host: {\n    '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n    '[attr.aria-expanded]': 'menuOpen',\n    '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n    '(click)': '_handleClick($event)',\n    '(mousedown)': '_handleMousedown($event)',\n    '(keydown)': '_handleKeydown($event)',\n  },\n})\nexport abstract class _MatMenuTriggerBase implements AfterContentInit, OnDestroy {\n  private _portal: TemplatePortal;\n  private _overlayRef: OverlayRef | null = null;\n  private _menuOpen: boolean = false;\n  private _closingActionsSubscription = Subscription.EMPTY;\n  private _hoverSubscription = Subscription.EMPTY;\n  private _menuCloseSubscription = Subscription.EMPTY;\n  private _scrollStrategy: () => ScrollStrategy;\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n\n  /**\n   * We're specifically looking for a `MatMenu` here since the generic `MatMenuPanel`\n   * interface lacks some functionality around nested menus and animations.\n   */\n  private _parentMaterialMenu: _MatMenuBase | undefined;\n\n  /**\n   * Cached value of the padding of the parent menu panel.\n   * Used to offset sub-menus to compensate for the padding.\n   */\n  private _parentInnerPadding: number | undefined;\n\n  /**\n   * Handles touch start events on the trigger.\n   * Needs to be an arrow function so we can easily use addEventListener and removeEventListener.\n   */\n  private _handleTouchStart = (event: TouchEvent) => {\n    if (!isFakeTouchstartFromScreenReader(event)) {\n      this._openedBy = 'touch';\n    }\n  };\n\n  // Tracking input type is necessary so it's possible to only auto-focus\n  // the first item of the list when the menu is opened via the keyboard\n  _openedBy: Exclude<FocusOrigin, 'program' | null> | undefined = undefined;\n\n  /**\n   * @deprecated\n   * @breaking-change 8.0.0\n   */\n  @Input('mat-menu-trigger-for')\n  get _deprecatedMatMenuTriggerFor(): MatMenuPanel | null {\n    return this.menu;\n  }\n  set _deprecatedMatMenuTriggerFor(v: MatMenuPanel | null) {\n    this.menu = v;\n  }\n\n  /** References the menu instance that the trigger is associated with. */\n  @Input('matMenuTriggerFor')\n  get menu(): MatMenuPanel | null {\n    return this._menu;\n  }\n  set menu(menu: MatMenuPanel | null) {\n    if (menu === this._menu) {\n      return;\n    }\n\n    this._menu = menu;\n    this._menuCloseSubscription.unsubscribe();\n\n    if (menu) {\n      if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuRecursiveError();\n      }\n\n      this._menuCloseSubscription = menu.close.subscribe((reason: MenuCloseReason) => {\n        this._destroyMenu(reason);\n\n        // If a click closed the menu, we should close the entire chain of nested menus.\n        if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n          this._parentMaterialMenu.closed.emit(reason);\n        }\n      });\n    }\n\n    this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n  }\n  private _menu: MatMenuPanel | null;\n\n  /** Data to be passed along to any lazily-rendered content. */\n  @Input('matMenuTriggerData') menuData: any;\n\n  /**\n   * Whether focus should be restored when the menu is closed.\n   * Note that disabling this option can have accessibility implications\n   * and it's up to you to manage focus, if you decide to turn it off.\n   */\n  @Input('matMenuTriggerRestoreFocus') restoreFocus: boolean = true;\n\n  /** Event emitted when the associated menu is opened. */\n  @Output() readonly menuOpened: EventEmitter<void> = new EventEmitter<void>();\n\n  /**\n   * Event emitted when the associated menu is opened.\n   * @deprecated Switch to `menuOpened` instead\n   * @breaking-change 8.0.0\n   */\n  // tslint:disable-next-line:no-output-on-prefix\n  @Output() readonly onMenuOpen: EventEmitter<void> = this.menuOpened;\n\n  /** Event emitted when the associated menu is closed. */\n  @Output() readonly menuClosed: EventEmitter<void> = new EventEmitter<void>();\n\n  /**\n   * Event emitted when the associated menu is closed.\n   * @deprecated Switch to `menuClosed` instead\n   * @breaking-change 8.0.0\n   */\n  // tslint:disable-next-line:no-output-on-prefix\n  @Output() readonly onMenuClose: EventEmitter<void> = this.menuClosed;\n\n  constructor(\n    overlay: Overlay,\n    element: ElementRef<HTMLElement>,\n    viewContainerRef: ViewContainerRef,\n    scrollStrategy: any,\n    parentMenu: MatMenuPanel,\n    menuItemInstance: MatMenuItem,\n    dir: Directionality,\n    focusMonitor: FocusMonitor,\n    ngZone: NgZone,\n  );\n\n  /**\n   * @deprecated `focusMonitor` will become a required parameter.\n   * @breaking-change 8.0.0\n   */\n  constructor(\n    overlay: Overlay,\n    element: ElementRef<HTMLElement>,\n    viewContainerRef: ViewContainerRef,\n    scrollStrategy: any,\n    parentMenu: MatMenuPanel,\n    menuItemInstance: MatMenuItem,\n    dir: Directionality,\n    focusMonitor?: FocusMonitor | null,\n  );\n\n  /**\n   * @deprecated `ngZone` will become a required parameter.\n   * @breaking-change 15.0.0\n   */\n  constructor(\n    overlay: Overlay,\n    element: ElementRef<HTMLElement>,\n    viewContainerRef: ViewContainerRef,\n    scrollStrategy: any,\n    parentMenu: MatMenuPanel,\n    menuItemInstance: MatMenuItem,\n    dir: Directionality,\n    focusMonitor: FocusMonitor,\n  );\n\n  constructor(\n    private _overlay: Overlay,\n    private _element: ElementRef<HTMLElement>,\n    private _viewContainerRef: ViewContainerRef,\n    @Inject(MAT_MENU_SCROLL_STRATEGY) scrollStrategy: any,\n    @Inject(MAT_MENU_PANEL) @Optional() parentMenu: MatMenuPanel,\n    // `MatMenuTrigger` is commonly used in combination with a `MatMenuItem`.\n    // tslint:disable-next-line: lightweight-tokens\n    @Optional() @Self() private _menuItemInstance: MatMenuItem,\n    @Optional() private _dir: Directionality,\n    private _focusMonitor: FocusMonitor | null,\n    private _ngZone?: NgZone,\n  ) {\n    this._scrollStrategy = scrollStrategy;\n    this._parentMaterialMenu = parentMenu instanceof _MatMenuBase ? parentMenu : undefined;\n\n    _element.nativeElement.addEventListener(\n      'touchstart',\n      this._handleTouchStart,\n      passiveEventListenerOptions,\n    );\n  }\n\n  ngAfterContentInit() {\n    this._handleHover();\n  }\n\n  ngOnDestroy() {\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._overlayRef = null;\n    }\n\n    this._element.nativeElement.removeEventListener(\n      'touchstart',\n      this._handleTouchStart,\n      passiveEventListenerOptions,\n    );\n\n    this._menuCloseSubscription.unsubscribe();\n    this._closingActionsSubscription.unsubscribe();\n    this._hoverSubscription.unsubscribe();\n  }\n\n  /** Whether the menu is open. */\n  get menuOpen(): boolean {\n    return this._menuOpen;\n  }\n\n  /** The text direction of the containing app. */\n  get dir(): Direction {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n\n  /** Whether the menu triggers a sub-menu or a top-level one. */\n  triggersSubmenu(): boolean {\n    return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n  }\n\n  /** Toggles the menu between the open and closed states. */\n  toggleMenu(): void {\n    return this._menuOpen ? this.closeMenu() : this.openMenu();\n  }\n\n  /** Opens the menu. */\n  openMenu(): void {\n    const menu = this.menu;\n\n    if (this._menuOpen || !menu) {\n      return;\n    }\n\n    const overlayRef = this._createOverlay(menu);\n    const overlayConfig = overlayRef.getConfig();\n    const positionStrategy = overlayConfig.positionStrategy as FlexibleConnectedPositionStrategy;\n\n    this._setPosition(menu, positionStrategy);\n    overlayConfig.hasBackdrop =\n      menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n    overlayRef.attach(this._getPortal(menu));\n\n    if (menu.lazyContent) {\n      menu.lazyContent.attach(this.menuData);\n    }\n\n    this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n    this._initMenu(menu);\n\n    if (menu instanceof _MatMenuBase) {\n      menu._startAnimation();\n      menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n        // Re-adjust the position without locking when the amount of items\n        // changes so that the overlay is allowed to pick a new optimal position.\n        positionStrategy.withLockedPosition(false).reapplyLastPosition();\n        positionStrategy.withLockedPosition(true);\n      });\n    }\n  }\n\n  /** Closes the menu. */\n  closeMenu(): void {\n    this.menu?.close.emit();\n  }\n\n  /**\n   * Focuses the menu trigger.\n   * @param origin Source of the menu trigger's focus.\n   */\n  focus(origin?: FocusOrigin, options?: FocusOptions) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n\n  /**\n   * Updates the position of the menu to ensure that it fits all options within the viewport.\n   */\n  updatePosition(): void {\n    this._overlayRef?.updatePosition();\n  }\n\n  /** Closes the menu and does the necessary cleanup. */\n  private _destroyMenu(reason: MenuCloseReason) {\n    if (!this._overlayRef || !this.menuOpen) {\n      return;\n    }\n\n    const menu = this.menu;\n    this._closingActionsSubscription.unsubscribe();\n    this._overlayRef.detach();\n\n    // Always restore focus if the user is navigating using the keyboard or the menu was opened\n    // programmatically. We don't restore for non-root triggers, because it can prevent focus\n    // from making it back to the root trigger when closing a long chain of menus by clicking\n    // on the backdrop.\n    if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n      this.focus(this._openedBy);\n    }\n\n    this._openedBy = undefined;\n\n    if (menu instanceof _MatMenuBase) {\n      menu._resetAnimation();\n\n      if (menu.lazyContent) {\n        // Wait for the exit animation to finish before detaching the content.\n        menu._animationDone\n          .pipe(\n            filter(event => event.toState === 'void'),\n            take(1),\n            // Interrupt if the content got re-attached.\n            takeUntil(menu.lazyContent._attached),\n          )\n          .subscribe({\n            next: () => menu.lazyContent!.detach(),\n            // No matter whether the content got re-attached, reset the menu.\n            complete: () => this._setIsMenuOpen(false),\n          });\n      } else {\n        this._setIsMenuOpen(false);\n      }\n    } else {\n      this._setIsMenuOpen(false);\n      menu?.lazyContent?.detach();\n    }\n  }\n\n  /**\n   * This method sets the menu state to open and focuses the first item if\n   * the menu was opened via the keyboard.\n   */\n  private _initMenu(menu: MatMenuPanel): void {\n    menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n    menu.direction = this.dir;\n    this._setMenuElevation(menu);\n    menu.focusFirstItem(this._openedBy || 'program');\n    this._setIsMenuOpen(true);\n  }\n\n  /** Updates the menu elevation based on the amount of parent menus that it has. */\n  private _setMenuElevation(menu: MatMenuPanel): void {\n    if (menu.setElevation) {\n      let depth = 0;\n      let parentMenu = menu.parentMenu;\n\n      while (parentMenu) {\n        depth++;\n        parentMenu = parentMenu.parentMenu;\n      }\n\n      menu.setElevation(depth);\n    }\n  }\n\n  // set state rather than toggle to support triggers sharing a menu\n  private _setIsMenuOpen(isOpen: boolean): void {\n    if (isOpen !== this._menuOpen) {\n      this._menuOpen = isOpen;\n      this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n\n      if (this.triggersSubmenu()) {\n        this._menuItemInstance._setHighlighted(isOpen);\n      }\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /**\n   * This method creates the overlay from the provided menu's template and saves its\n   * OverlayRef so that it can be attached to the DOM when openMenu is called.\n   */\n  private _createOverlay(menu: MatMenuPanel): OverlayRef {\n    if (!this._overlayRef) {\n      const config = this._getOverlayConfig(menu);\n      this._subscribeToPositions(\n        menu,\n        config.positionStrategy as FlexibleConnectedPositionStrategy,\n      );\n      this._overlayRef = this._overlay.create(config);\n\n      // Consume the `keydownEvents` in order to prevent them from going to another overlay.\n      // Ideally we'd also have our keyboard event logic in here, however doing so will\n      // break anybody that may have implemented the `MatMenuPanel` themselves.\n      this._overlayRef.keydownEvents().subscribe();\n    }\n\n    return this._overlayRef;\n  }\n\n  /**\n   * This method builds the configuration object needed to create the overlay, the OverlayState.\n   * @returns OverlayConfig\n   */\n  private _getOverlayConfig(menu: MatMenuPanel): OverlayConfig {\n    return new OverlayConfig({\n      positionStrategy: this._overlay\n        .position()\n        .flexibleConnectedTo(this._element)\n        .withLockedPosition()\n        .withGrowAfterOpen()\n        .withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n      backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n      panelClass: menu.overlayPanelClass,\n      scrollStrategy: this._scrollStrategy(),\n      direction: this._dir,\n    });\n  }\n\n  /**\n   * Listens to changes in the position of the overlay and sets the correct classes\n   * on the menu based on the new position. This ensures the animation origin is always\n   * correct, even if a fallback position is used for the overlay.\n   */\n  private _subscribeToPositions(menu: MatMenuPanel, position: FlexibleConnectedPositionStrategy) {\n    if (menu.setPositionClasses) {\n      position.positionChanges.subscribe(change => {\n        const posX: MenuPositionX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n        const posY: MenuPositionY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n\n        // @breaking-change 15.0.0 Remove null check for `ngZone`.\n        // `positionChanges` fires outside of the `ngZone` and `setPositionClasses` might be\n        // updating something in the view so we need to bring it back in.\n        if (this._ngZone) {\n          this._ngZone.run(() => menu.setPositionClasses!(posX, posY));\n        } else {\n          menu.setPositionClasses!(posX, posY);\n        }\n      });\n    }\n  }\n\n  /**\n   * Sets the appropriate positions on a position strategy\n   * so the overlay connects with the trigger correctly.\n   * @param positionStrategy Strategy whose position to update.\n   */\n  private _setPosition(menu: MatMenuPanel, positionStrategy: FlexibleConnectedPositionStrategy) {\n    let [originX, originFallbackX]: HorizontalConnectionPos[] =\n      menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n\n    let [overlayY, overlayFallbackY]: VerticalConnectionPos[] =\n      menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n\n    let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n    let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n    let offsetY = 0;\n\n    if (this.triggersSubmenu()) {\n      // When the menu is a sub-menu, it should always align itself\n      // to the edges of the trigger, instead of overlapping it.\n      overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n      originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n\n      if (this._parentMaterialMenu) {\n        if (this._parentInnerPadding == null) {\n          const firstItem = this._parentMaterialMenu.items.first;\n          this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n        }\n\n        offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n      }\n    } else if (!menu.overlapTrigger) {\n      originY = overlayY === 'top' ? 'bottom' : 'top';\n      originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n    }\n\n    positionStrategy.withPositions([\n      {originX, originY, overlayX, overlayY, offsetY},\n      {originX: originFallbackX, originY, overlayX: overlayFallbackX, overlayY, offsetY},\n      {\n        originX,\n        originY: originFallbackY,\n        overlayX,\n        overlayY: overlayFallbackY,\n        offsetY: -offsetY,\n      },\n      {\n        originX: originFallbackX,\n        originY: originFallbackY,\n        overlayX: overlayFallbackX,\n        overlayY: overlayFallbackY,\n        offsetY: -offsetY,\n      },\n    ]);\n  }\n\n  /** Returns a stream that emits whenever an action that should close the menu occurs. */\n  private _menuClosingActions() {\n    const backdrop = this._overlayRef!.backdropClick();\n    const detachments = this._overlayRef!.detachments();\n    const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : observableOf();\n    const hover = this._parentMaterialMenu\n      ? this._parentMaterialMenu._hovered().pipe(\n          filter(active => active !== this._menuItemInstance),\n          filter(() => this._menuOpen),\n        )\n      : observableOf();\n\n    return merge(backdrop, parentClose as Observable<MenuCloseReason>, hover, detachments);\n  }\n\n  /** Handles mouse presses on the trigger. */\n  _handleMousedown(event: MouseEvent): void {\n    if (!isFakeMousedownFromScreenReader(event)) {\n      // Since right or middle button clicks won't trigger the `click` event,\n      // we shouldn't consider the menu as opened by mouse in those cases.\n      this._openedBy = event.button === 0 ? 'mouse' : undefined;\n\n      // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n      // we should prevent focus from moving onto it via click to avoid the\n      // highlight from lingering on the menu item.\n      if (this.triggersSubmenu()) {\n        event.preventDefault();\n      }\n    }\n  }\n\n  /** Handles key presses on the trigger. */\n  _handleKeydown(event: KeyboardEvent): void {\n    const keyCode = event.keyCode;\n\n    // Pressing enter on the trigger will trigger the click handler later.\n    if (keyCode === ENTER || keyCode === SPACE) {\n      this._openedBy = 'keyboard';\n    }\n\n    if (\n      this.triggersSubmenu() &&\n      ((keyCode === RIGHT_ARROW && this.dir === 'ltr') ||\n        (keyCode === LEFT_ARROW && this.dir === 'rtl'))\n    ) {\n      this._openedBy = 'keyboard';\n      this.openMenu();\n    }\n  }\n\n  /** Handles click events on the trigger. */\n  _handleClick(event: MouseEvent): void {\n    if (this.triggersSubmenu()) {\n      // Stop event propagation to avoid closing the parent menu.\n      event.stopPropagation();\n      this.openMenu();\n    } else {\n      this.toggleMenu();\n    }\n  }\n\n  /** Handles the cases where the user hovers over the trigger. */\n  private _handleHover() {\n    // Subscribe to changes in the hovered item in order to toggle the panel.\n    if (!this.triggersSubmenu() || !this._parentMaterialMenu) {\n      return;\n    }\n\n    this._hoverSubscription = this._parentMaterialMenu\n      ._hovered()\n      // Since we might have multiple competing triggers for the same menu (e.g. a sub-menu\n      // with different data and triggers), we have to delay it by a tick to ensure that\n      // it won't be closed immediately after it is opened.\n      .pipe(\n        filter(active => active === this._menuItemInstance && !active.disabled),\n        delay(0, asapScheduler),\n      )\n      .subscribe(() => {\n        this._openedBy = 'mouse';\n\n        // If the same menu is used between multiple triggers, it might still be animating\n        // while the new trigger tries to re-open it. Wait for the animation to finish\n        // before doing so. Also interrupt if the user moves to another item.\n        if (this.menu instanceof _MatMenuBase && this.menu._isAnimating) {\n          // We need the `delay(0)` here in order to avoid\n          // 'changed after checked' errors in some cases. See #12194.\n          this.menu._animationDone\n            .pipe(take(1), delay(0, asapScheduler), takeUntil(this._parentMaterialMenu!._hovered()))\n            .subscribe(() => this.openMenu());\n        } else {\n          this.openMenu();\n        }\n      });\n  }\n\n  /** Gets the portal that should be attached to the overlay. */\n  private _getPortal(menu: MatMenuPanel): TemplatePortal {\n    // Note that we can avoid this check by keeping the portal on the menu panel.\n    // While it would be cleaner, we'd have to introduce another required method on\n    // `MatMenuPanel`, making it harder to consume.\n    if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n      this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n    }\n\n    return this._portal;\n  }\n}\n\n/** Directive applied to an element that should trigger a `mat-menu`. */\n@Directive({\n  selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n  host: {\n    'class': 'mat-mdc-menu-trigger',\n  },\n  exportAs: 'matMenuTrigger',\n})\nexport class MatMenuTrigger extends _MatMenuTriggerBase {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '@angular/material/core';\nimport {OverlayModule} from '@angular/cdk/overlay';\nimport {CdkScrollableModule} from '@angular/cdk/scrolling';\nimport {MatMenu} from './menu';\nimport {MatMenuItem} from './menu-item';\nimport {MatMenuContent} from './menu-content';\nimport {MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MatMenuTrigger} from './menu-trigger';\n\n@NgModule({\n  imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule],\n  exports: [\n    CdkScrollableModule,\n    MatMenu,\n    MatCommonModule,\n    MatMenuItem,\n    MatMenuContent,\n    MatMenuTrigger,\n  ],\n  declarations: [MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger],\n  providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n})\nexport class MatMenuModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["i1", "observableOf", "i2.MatMenuItem", "i3", "i4"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAcA;;;AAGG;MACU,cAAc,GAAG,IAAI,cAAc,CAAe,gBAAgB;;ACa/E;AACA;AACA,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,aAAa,CAAC,MAAA;AAAQ,CAAA,CAAC,CAAC,CAAC;AAErE;;AAEG;AAoBG,MAAO,WACX,SAAQ,gBAAgB,CAAA;IAsCxB,WACU,CAAA,WAAoC,EAClB,SAAe,EACjC,aAA4B,EACO,WAAuC,EAC1E,kBAAsC,EAAA;AAE9C,QAAA,KAAK,EAAE,CAAC;QANA,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QAClB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAM;QACjC,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;QACO,IAAW,CAAA,WAAA,GAAX,WAAW,CAA4B;QAC1E,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAoB;;QAvCvC,IAAI,CAAA,IAAA,GAAsD,UAAU,CAAC;;AAGrE,QAAA,IAAA,CAAA,QAAQ,GAAyB,IAAI,OAAO,EAAe,CAAC;;AAG5D,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,OAAO,EAAe,CAAC;;QAG/C,IAAY,CAAA,YAAA,GAAY,KAAK,CAAC;;QAG9B,IAAgB,CAAA,gBAAA,GAAY,KAAK,CAAC;AA8BhC,QAAA,WAAW,EAAE,OAAO,GAAG,IAAI,CAAC,CAAC;KAC9B;;IAGD,KAAK,CAAC,MAAoB,EAAE,OAAsB,EAAA;AAChD,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,MAAM,EAAE;AAChC,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACtE,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC1B;IAED,eAAe,GAAA;QACb,IAAI,IAAI,CAAC,aAAa,EAAE;;;;YAItB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AACrD,SAAA;KACF;IAED,WAAW,GAAA;QACT,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACrD,SAAA;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;AACnD,YAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACnC,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;KAC1B;;IAGD,YAAY,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,GAAG,CAAC;KACnC;;IAGD,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;KACvC;;AAGD,IAAA,cAAc,CAAC,KAAY,EAAA;QACzB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,KAAK,CAAC,eAAe,EAAE,CAAC;AACzB,SAAA;KACF;;IAGD,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC1B;;IAGD,QAAQ,GAAA;AACN,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAgB,CAAC;QAC5E,MAAM,KAAK,GAAG,KAAK,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;;AAGlE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,YAAA,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AACnB,SAAA;QAED,OAAO,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;KACxC;AAED,IAAA,eAAe,CAAC,aAAsB,EAAA;;;;;AAKpC,QAAA,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC;AAClC,QAAA,IAAI,CAAC,kBAAkB,EAAE,YAAY,EAAE,CAAC;KACzC;AAED,IAAA,mBAAmB,CAAC,eAAwB,EAAA;;AAE1C,QAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;AACxC,QAAA,IAAI,CAAC,kBAAkB,EAAE,YAAY,EAAE,CAAC;KACzC;IAED,SAAS,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;KAClF;8GAxIU,WAAW,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAyCZ,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAER,cAAc,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AA3Cb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,4lBCzDxB,seAYA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FD6Ca,WAAW,EAAA,UAAA,EAAA,CAAA;kBAnBvB,SAAS;+BACE,iBAAiB,EAAA,QAAA,EACjB,aAAa,EACf,MAAA,EAAA,CAAC,UAAU,EAAE,eAAe,CAAC,EAC/B,IAAA,EAAA;AACJ,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,OAAO,EAAE,2CAA2C;AACpD,wBAAA,uCAAuC,EAAE,cAAc;AACvD,wBAAA,2CAA2C,EAAE,kBAAkB;AAC/D,wBAAA,iBAAiB,EAAE,gBAAgB;AACnC,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,iBAAiB,EAAE,kBAAkB;AACrC,wBAAA,SAAS,EAAE,wBAAwB;AACnC,wBAAA,cAAc,EAAE,qBAAqB;AACtC,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,seAAA,EAAA,CAAA;;0BA4ClC,MAAM;2BAAC,QAAQ,CAAA;;0BAEf,MAAM;2BAAC,cAAc,CAAA;;0BAAG,QAAQ;4EAtC1B,IAAI,EAAA,CAAA;sBAAZ,KAAK;;;AEtDR;;;;AAIG;SACa,4BAA4B,GAAA;AAC1C,IAAA,MAAM,KAAK,CAAC,CAAA;AAC0D,uEAAA,CAAA,CAAC,CAAC;AAC1E,CAAC;AAED;;;;AAIG;SACa,4BAA4B,GAAA;AAC1C,IAAA,MAAM,KAAK,CAAC,CAAA;AACyD,sEAAA,CAAA,CAAC,CAAC;AACzE,CAAC;AAED;;;;AAIG;SACa,0BAA0B,GAAA;IACxC,MAAM,KAAK,CACT,CAAgF,8EAAA,CAAA;AAC9E,QAAA,CAAA,oEAAA,CAAsE,CACzE,CAAC;AACJ;;ACdA;;;;AAIG;MACU,gBAAgB,GAAG,IAAI,cAAc,CAAiB,gBAAgB,EAAE;MAG/D,mBAAmB,CAAA;AA+BvC,IAAA,WAAA,CACU,SAA2B,EAC3B,yBAAmD,EACnD,OAAuB,EACvB,SAAmB,EACnB,iBAAmC,EACjB,SAAc,EAChC,kBAAsC,EAAA;QANtC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAkB;QAC3B,IAAyB,CAAA,yBAAA,GAAzB,yBAAyB,CAA0B;QACnD,IAAO,CAAA,OAAA,GAAP,OAAO,CAAgB;QACvB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QACjB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAK;QAChC,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAoB;;AAjCvC,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,OAAO,EAAQ,CAAC;KAkCrC;AAEJ;;;AAGG;IACH,MAAM,CAAC,UAAe,EAAE,EAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC3E,SAAA;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;AAEd,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,IAAI,eAAe,CAChC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,EACnC,IAAI,CAAC,yBAAyB,EAC9B,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,SAAS,CACf,CAAC;AACH,SAAA;QAED,MAAM,OAAO,GAAgB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,aAAa,CAAC;;;;AAKrE,QAAA,OAAO,CAAC,UAAW,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;;;;;;;AAQtE,QAAA,IAAI,CAAC,kBAAkB,EAAE,YAAY,EAAE,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;KACvB;AAED;;;AAGG;IACH,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AAC3B,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;AACvB,SAAA;KACF;IAED,WAAW,GAAA;QACT,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;AACxB,SAAA;KACF;AA7FmB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,2KAqC7B,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGArCE,mBAAmB,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBADxC,SAAS;;0BAsCL,MAAM;2BAAC,QAAQ,CAAA;;AA2DpB;AAKM,MAAO,cAAe,SAAQ,mBAAmB,CAAA;8GAA1C,cAAc,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,QAAA,EAAA,6BAAA,EAAA,SAAA,EAFd,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAE1D,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,6BAA6B;oBACvC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAgB,cAAA,EAAC,CAAC;AACtE,iBAAA,CAAA;;;ACnHD;;;;;AAKG;AACU,MAAA,iBAAiB,GAG1B;AACF;;;;;;;AAOG;AACH,IAAA,aAAa,EAAE,OAAO,CAAC,eAAe,EAAE;AACtC,QAAA,KAAK,CACH,MAAM,EACN,KAAK,CAAC;AACJ,YAAA,OAAO,EAAE,CAAC;AACV,YAAA,SAAS,EAAE,YAAY;AACxB,SAAA,CAAC,CACH;QACD,UAAU,CACR,eAAe,EACf,OAAO,CACL,kCAAkC,EAClC,KAAK,CAAC;AACJ,YAAA,OAAO,EAAE,CAAC;AACV,YAAA,SAAS,EAAE,UAAU;AACtB,SAAA,CAAC,CACH,CACF;AACD,QAAA,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,mBAAmB,EAAE,KAAK,CAAC,EAAC,OAAO,EAAE,CAAC,EAAC,CAAC,CAAC,CAAC;KAC3E,CAAC;AAEF;;;AAGG;AACH,IAAA,WAAW,EAAE,OAAO,CAAC,aAAa,EAAE;;;QAGlC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,EAAC,OAAO,EAAE,CAAC,EAAC,CAAC,CAAC;QACrC,UAAU,CAAC,WAAW,EAAE;AACtB,YAAA,KAAK,CAAC,EAAC,OAAO,EAAE,CAAC,EAAC,CAAC;YACnB,OAAO,CAAC,8CAA8C,CAAC;SACxD,CAAC;KACH,CAAC;EACF;AAEF;;;;AAIG;AACU,MAAA,WAAW,GAAG,iBAAiB,CAAC,YAAY;AAEzD;;;;AAIG;AACU,MAAA,aAAa,GAAG,iBAAiB,CAAC;;AChC/C,IAAI,YAAY,GAAG,CAAC,CAAC;AA0BrB;MACa,wBAAwB,GAAG,IAAI,cAAc,CACxD,0BAA0B,EAC1B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,gCAAgC;AAC1C,CAAA,EACD;AAEF;SACgB,gCAAgC,GAAA;IAC9C,OAAO;AACL,QAAA,cAAc,EAAE,KAAK;AACrB,QAAA,SAAS,EAAE,OAAO;AAClB,QAAA,SAAS,EAAE,OAAO;AAClB,QAAA,aAAa,EAAE,kCAAkC;KAClD,CAAC;AACJ,CAAC;AAED;MAEa,YAAY,CAAA;;AAmDvB,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IACD,IAAI,SAAS,CAAC,KAAoB,EAAA;QAChC,IACE,KAAK,KAAK,QAAQ;AAClB,YAAA,KAAK,KAAK,OAAO;AACjB,aAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;AACA,YAAA,4BAA4B,EAAE,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC3B;;AAGD,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IACD,IAAI,SAAS,CAAC,KAAoB,EAAA;AAChC,QAAA,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC7F,YAAA,4BAA4B,EAAE,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC3B;;AAmBD,IAAA,IACI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;IACD,IAAI,cAAc,CAAC,KAAmB,EAAA;AACpC,QAAA,IAAI,CAAC,eAAe,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KACrD;;AAID,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;IACD,IAAI,WAAW,CAAC,KAAmB,EAAA;AACjC,QAAA,IAAI,CAAC,YAAY,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAClD;AAGD;;;;;AAKG;IACH,IACI,UAAU,CAAC,OAAe,EAAA;AAC5B,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAEpD,QAAA,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,EAAE;YACnD,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,SAAiB,KAAI;AAC1D,gBAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AACrC,aAAC,CAAC,CAAC;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC;AAEnC,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;YAC7B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,SAAiB,KAAI;AAC/C,gBAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;AACpC,aAAC,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,GAAG,EAAE,CAAC;AAC/C,SAAA;KACF;AAGD;;;;;;AAMG;AACH,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IACD,IAAI,SAAS,CAAC,OAAe,EAAA;AAC3B,QAAA,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC;KAC3B;AAgCD,IAAA,WAAA,CACU,WAAoC,EACpC,OAAe,EACW,cAAqC;;IAE/D,kBAAsC,EAAA;QAJtC,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACpC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QAGf,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAoB;;AAnLhD,QAAA,IAAA,CAAA,sBAAsB,GAAG,IAAI,SAAS,EAAe,CAAC;;QAGtD,IAAU,CAAA,UAAA,GAA6B,EAAE,CAAC;;QAG1C,IAAoB,CAAA,oBAAA,GAAqB,MAAM,CAAC;;AAGvC,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,OAAO,EAAkB,CAAC;;AAwIrC,QAAA,IAAA,CAAA,MAAM,GAAkC,IAAI,YAAY,EAAmB,CAAC;AAE/F;;;;AAIG;AACgB,QAAA,IAAA,CAAA,KAAK,GAAkC,IAAI,CAAC,MAAM,CAAC;AAE7D,QAAA,IAAA,CAAA,OAAO,GAAG,CAAA,eAAA,EAAkB,YAAY,EAAE,EAAE,CAAC;QA2BpD,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,iBAAiB,IAAI,EAAE,CAAC;AAChE,QAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,SAAS,CAAC;AAC3C,QAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,SAAS,CAAC;AAC3C,QAAA,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa,CAAC;AAClD,QAAA,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC,cAAc,CAAC;AACrD,QAAA,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,WAAW,CAAC;KAChD;IAED,QAAQ,GAAA;QACN,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC3B;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC;AAChE,aAAA,QAAQ,EAAE;AACV,aAAA,aAAa,EAAE;AACf,aAAA,cAAc,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;;QAKjE,IAAI,CAAC,sBAAsB,CAAC,OAAO;AAChC,aAAA,IAAI,CACH,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,EACtC,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAiB,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAC9E;AACA,aAAA,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,WAA0B,CAAC,CAAC,CAAC;QAE3F,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,SAAiC,KAAI;;;;AAIlF,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;AAEjC,YAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE;AAC5E,gBAAA,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;gBAClC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,eAAe,IAAI,CAAC,CAAC,CAAC,CAAC;AAEpF,gBAAA,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;AAC1C,oBAAA,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9B,iBAAA;AAAM,qBAAA;oBACL,OAAO,CAAC,iBAAiB,EAAE,CAAC;AAC7B,iBAAA;AACF,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,CAAC;AACtC,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,2BAA2B,EAAE,WAAW,EAAE,CAAC;KACjD;;IAGD,QAAQ,GAAA;;AAEN,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAA6C,CAAC;AAC9F,QAAA,OAAO,WAAW,CAAC,IAAI,CACrB,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,EACtC,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAiB,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CACnD,CAAC;KAC9B;AAED;;;;;AAKG;IACH,OAAO,CAAC,KAAkB,EAAA,GAAI;AAE9B;;;;;AAKG;IACH,UAAU,CAAC,KAAkB,EAAA,GAAI;;AAGjC,IAAA,cAAc,CAAC,KAAoB,EAAA;AACjC,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC9B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;AAEjC,QAAA,QAAQ,OAAO;AACb,YAAA,KAAK,MAAM;AACT,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7B,iBAAA;gBACD,MAAM;AACR,YAAA,KAAK,UAAU;gBACb,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;AAC/C,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7B,iBAAA;gBACD,MAAM;AACR,YAAA,KAAK,WAAW;gBACd,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;AAC/C,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7B,iBAAA;gBACD,MAAM;AACR,YAAA;AACE,gBAAA,IAAI,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,UAAU,EAAE;AAClD,oBAAA,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AACpC,iBAAA;AAED,gBAAA,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACzB,OAAO;AACV,SAAA;;;QAID,KAAK,CAAC,eAAe,EAAE,CAAC;KACzB;AAED;;;AAGG;IACH,cAAc,CAAC,SAAsB,SAAS,EAAA;;AAE5C,QAAA,IAAI,CAAC,2BAA2B,EAAE,WAAW,EAAE,CAAC;QAChD,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YACpF,IAAI,SAAS,GAAuB,IAAI,CAAC;AAEzC,YAAA,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;;;;;AAKtC,gBAAA,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAM,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AAC3F,aAAA;;AAGD,YAAA,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;AAC7D,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;gBACjC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE,CAAC;;;;AAKpD,gBAAA,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,SAAS,EAAE;oBACpC,SAAS,CAAC,KAAK,EAAE,CAAC;AACnB,iBAAA;AACF,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;AAED;;;AAGG;IACH,eAAe,GAAA;QACb,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;KACpC;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,KAAa,EAAA;;;AAGxB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,KAAK,EAAE,EAAE,CAAC,CAAC;QAC5D,MAAM,YAAY,GAAG,CAAG,EAAA,IAAI,CAAC,gBAAgB,CAAA,EAAG,SAAS,CAAA,CAAE,CAAC;AAC5D,QAAA,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,IAAG;YACpE,OAAO,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACrD,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,IAAI,eAAe,KAAK,IAAI,CAAC,kBAAkB,EAAE;YACnE,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC;AAClD,aAAA;AAED,YAAA,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;AACrC,YAAA,IAAI,CAAC,kBAAkB,GAAG,YAAY,CAAC;AACxC,SAAA;KACF;AAED;;;;;;AAMG;IACH,kBAAkB,CAAC,OAAsB,IAAI,CAAC,SAAS,EAAE,IAAA,GAAsB,IAAI,CAAC,SAAS,EAAA;AAC3F,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC;AAChC,QAAA,OAAO,CAAC,iBAAiB,CAAC,GAAG,IAAI,KAAK,QAAQ,CAAC;AAC/C,QAAA,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,KAAK,OAAO,CAAC;AAC7C,QAAA,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,KAAK,OAAO,CAAC;AAC7C,QAAA,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,KAAK,OAAO,CAAC;;AAG7C,QAAA,IAAI,CAAC,kBAAkB,EAAE,YAAY,EAAE,CAAC;KACzC;;IAGD,eAAe,GAAA;;AAEb,QAAA,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;KACrC;;IAGD,eAAe,GAAA;;AAEb,QAAA,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC;KACpC;;AAGD,IAAA,gBAAgB,CAAC,KAAqB,EAAA;AACpC,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,KAAqB,EAAA;AACrC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;;;;;;;AAQzB,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,KAAK,CAAC,EAAE;AACvE,YAAA,KAAK,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;AAC7B,SAAA;KACF;AAED;;;;;AAKG;IACK,wBAAwB,GAAA;QAC9B,IAAI,CAAC,SAAS,CAAC,OAAO;AACnB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/B,aAAA,SAAS,CAAC,CAAC,KAA6B,KAAI;YAC3C,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,CAAC,CAAC;AACnF,YAAA,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE,CAAC;AAChD,SAAC,CAAC,CAAC;KACN;AAzbU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,kEAgMb,wBAAwB,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAhMvB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,keA8FT,gBAAgB,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAlFb,WAAW,EA4EX,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAW,0EAPjB,WAAW,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAjFX,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB,SAAS;;0BAiML,MAAM;2BAAC,wBAAwB,CAAA;4EApLiB,SAAS,EAAA,CAAA;sBAA3D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBA2BxC,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAGe,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY,CAAA;gBAGO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB,CAAA;gBAGG,eAAe,EAAA,CAAA;sBAAzC,KAAK;uBAAC,kBAAkB,CAAA;gBAIrB,SAAS,EAAA,CAAA;sBADZ,KAAK;gBAkBF,SAAS,EAAA,CAAA;sBADZ,KAAK;gBAakB,WAAW,EAAA,CAAA;sBAAlC,SAAS;uBAAC,WAAW,CAAA;gBAO8B,KAAK,EAAA,CAAA;sBAAxD,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAC,WAAW,EAAE,KAAK,EAAC,CAAA;gBAMlB,WAAW,EAAA,CAAA;sBAA1C,YAAY;uBAAC,gBAAgB,CAAA;gBAI1B,cAAc,EAAA,CAAA;sBADjB,KAAK;gBAWF,WAAW,EAAA,CAAA;sBADd,KAAK;gBAgBF,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,OAAO,CAAA;gBA8BV,SAAS,EAAA,CAAA;sBADZ,KAAK;gBASa,MAAM,EAAA,CAAA;sBAAxB,MAAM;gBAOY,KAAK,EAAA,CAAA;sBAAvB,MAAM;;AAqSH,MAAO,OAAQ,SAAQ,YAAY,CAAA;AAcvC,IAAA,WAAA,CACE,WAAoC,EACpC,OAAe,EACmB,eAAsC,EACxE,iBAAqC,EAAA;QAErC,KAAK,CAAC,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAAC;QAnB/C,IAAgB,CAAA,gBAAA,GAAG,iBAAiB,CAAC;QACrC,IAAc,CAAA,cAAA,GAAG,CAAC,CAAC;KAmBrC;AArBU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,OAAO,kEAiBR,wBAAwB,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAjBvB,OAAO,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,SAAA,EAFP,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,OAAO,EAAC,CAAC,EC5iB9D,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,krBAoBA,EDuhBc,MAAA,EAAA,CAAA,+kGAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,iBAAiB,CAAC,aAAa,EAAE,iBAAiB,CAAC,WAAW,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAGjE,OAAO,EAAA,UAAA,EAAA,CAAA;kBAhBnB,SAAS;+BACE,UAAU,EAAA,eAAA,EAGH,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAC3B,QAAA,EAAA,SAAS,EACb,IAAA,EAAA;AACJ,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,wBAAwB,EAAE,MAAM;AAChC,wBAAA,yBAAyB,EAAE,MAAM;AACjC,wBAAA,iBAAiB,EAAE,EAAE;AACtB,qBAAA,EAAA,UAAA,EACW,CAAC,iBAAiB,CAAC,aAAa,EAAE,iBAAiB,CAAC,WAAW,CAAC,aACjE,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAA,OAAS,EAAC,CAAC,EAAA,QAAA,EAAA,krBAAA,EAAA,MAAA,EAAA,CAAA,+kGAAA,CAAA,EAAA,CAAA;;0BAmBzD,MAAM;2BAAC,wBAAwB,CAAA;;;AE3gBpC;MACa,wBAAwB,GAAG,IAAI,cAAc,CACxD,0BAA0B,EAC1B;AAEF;AACM,SAAU,gCAAgC,CAAC,OAAgB,EAAA;IAC/D,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;AACrD,CAAC;AAED;AACa,MAAA,yCAAyC,GAAG;AACvD,IAAA,OAAO,EAAE,wBAAwB;IACjC,IAAI,EAAE,CAAC,OAAO,CAAC;AACf,IAAA,UAAU,EAAE,gCAAgC;EAC5C;AAEF;AACA,MAAM,2BAA2B,GAAG,+BAA+B,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;AAErF;;;;AAIG;AACI,MAAM,sBAAsB,GAAG,EAAE;MAYlB,mBAAmB,CAAA;AAoCvC;;;AAGG;AACH,IAAA,IACI,4BAA4B,GAAA;QAC9B,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;IACD,IAAI,4BAA4B,CAAC,CAAsB,EAAA;AACrD,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;KACf;;AAGD,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IACD,IAAI,IAAI,CAAC,IAAyB,EAAA;AAChC,QAAA,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE;YACvB,OAAO;AACR,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;AAE1C,QAAA,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,IAAI,KAAK,IAAI,CAAC,mBAAmB,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACxF,gBAAA,0BAA0B,EAAE,CAAC;AAC9B,aAAA;AAED,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAuB,KAAI;AAC7E,gBAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;;AAG1B,gBAAA,IAAI,CAAC,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,KAAK,KAAK,IAAI,CAAC,mBAAmB,EAAE;oBACxE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC9C,iBAAA;AACH,aAAC,CAAC,CAAC;AACJ,SAAA;QAED,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;KACrE;IA6ED,WACU,CAAA,QAAiB,EACjB,QAAiC,EACjC,iBAAmC,EACT,cAAmB,EACjB,UAAwB;;;AAGhC,IAAA,iBAA8B,EACtC,IAAoB,EAChC,aAAkC,EAClC,OAAgB,EAAA;QAVhB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAS;QACjB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAyB;QACjC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QAKf,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAa;QACtC,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;QAChC,IAAa,CAAA,aAAA,GAAb,aAAa,CAAqB;QAClC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;QAnKlB,IAAW,CAAA,WAAA,GAAsB,IAAI,CAAC;QACtC,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;AAC3B,QAAA,IAAA,CAAA,2BAA2B,GAAG,YAAY,CAAC,KAAK,CAAC;AACjD,QAAA,IAAA,CAAA,kBAAkB,GAAG,YAAY,CAAC,KAAK,CAAC;AACxC,QAAA,IAAA,CAAA,sBAAsB,GAAG,YAAY,CAAC,KAAK,CAAC;AAE5C,QAAA,IAAA,CAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAcvD;;;AAGG;AACK,QAAA,IAAA,CAAA,iBAAiB,GAAG,CAAC,KAAiB,KAAI;AAChD,YAAA,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,EAAE;AAC5C,gBAAA,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;AAC1B,aAAA;AACH,SAAC,CAAC;;;QAIF,IAAS,CAAA,SAAA,GAAuD,SAAS,CAAC;AAiD1E;;;;AAIG;QACkC,IAAY,CAAA,YAAA,GAAY,IAAI,CAAC;;AAG/C,QAAA,IAAA,CAAA,UAAU,GAAuB,IAAI,YAAY,EAAQ,CAAC;AAE7E;;;;AAIG;;AAEgB,QAAA,IAAA,CAAA,UAAU,GAAuB,IAAI,CAAC,UAAU,CAAC;;AAGjD,QAAA,IAAA,CAAA,UAAU,GAAuB,IAAI,YAAY,EAAQ,CAAC;AAE7E;;;;AAIG;;AAEgB,QAAA,IAAA,CAAA,WAAW,GAAuB,IAAI,CAAC,UAAU,CAAC;AAyDnE,QAAA,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;AACtC,QAAA,IAAI,CAAC,mBAAmB,GAAG,UAAU,YAAY,YAAY,GAAG,UAAU,GAAG,SAAS,CAAC;AAEvF,QAAA,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CACrC,YAAY,EACZ,IAAI,CAAC,iBAAiB,EACtB,2BAA2B,CAC5B,CAAC;KACH;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,YAAY,EAAE,CAAC;KACrB;IAED,WAAW,GAAA;QACT,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACzB,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,mBAAmB,CAC7C,YAAY,EACZ,IAAI,CAAC,iBAAiB,EACtB,2BAA2B,CAC5B,CAAC;AAEF,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE,CAAC;AAC/C,QAAA,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;KACvC;;AAGD,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;;AAGD,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;KAC/D;;IAGD,eAAe,GAAA;AACb,QAAA,OAAO,CAAC,EAAE,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;KAC5E;;IAGD,UAAU,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;KAC5D;;IAGD,QAAQ,GAAA;AACN,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAEvB,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE;YAC3B,OAAO;AACR,SAAA;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAC7C,QAAA,MAAM,aAAa,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;AAC7C,QAAA,MAAM,gBAAgB,GAAG,aAAa,CAAC,gBAAqD,CAAC;AAE7F,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AAC1C,QAAA,aAAa,CAAC,WAAW;AACvB,YAAA,IAAI,CAAC,WAAW,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC;QACxE,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;QAEzC,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxC,SAAA;AAED,QAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;AAChG,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAErB,IAAI,IAAI,YAAY,YAAY,EAAE;YAChC,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,YAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;;;gBAG7E,gBAAgB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,mBAAmB,EAAE,CAAC;AACjE,gBAAA,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC5C,aAAC,CAAC,CAAC;AACJ,SAAA;KACF;;IAGD,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;KACzB;AAED;;;AAGG;IACH,KAAK,CAAC,MAAoB,EAAE,OAAsB,EAAA;AAChD,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,MAAM,EAAE;AAChC,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC7D,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC5C,SAAA;KACF;AAED;;AAEG;IACH,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC;KACpC;;AAGO,IAAA,YAAY,CAAC,MAAuB,EAAA;QAC1C,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACvC,OAAO;AACR,SAAA;AAED,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE,CAAC;AAC/C,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;;;;;QAM1B,IAAI,IAAI,CAAC,YAAY,KAAK,MAAM,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE;AAC7F,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC5B,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,IAAI,YAAY,YAAY,EAAE;YAChC,IAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,IAAI,IAAI,CAAC,WAAW,EAAE;;AAEpB,gBAAA,IAAI,CAAC,cAAc;AAChB,qBAAA,IAAI,CACH,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,CAAC,EACzC,IAAI,CAAC,CAAC,CAAC;;AAEP,gBAAA,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CACtC;AACA,qBAAA,SAAS,CAAC;oBACT,IAAI,EAAE,MAAM,IAAI,CAAC,WAAY,CAAC,MAAM,EAAE;;oBAEtC,QAAQ,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;AAC3C,iBAAA,CAAC,CAAC;AACN,aAAA;AAAM,iBAAA;AACL,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC5B,aAAA;AACF,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC3B,YAAA,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;AAC7B,SAAA;KACF;AAED;;;AAGG;AACK,IAAA,SAAS,CAAC,IAAkB,EAAA;AAClC,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;AAChF,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC;AAC1B,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC;AACjD,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;KAC3B;;AAGO,IAAA,iBAAiB,CAAC,IAAkB,EAAA;QAC1C,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,YAAA,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAEjC,YAAA,OAAO,UAAU,EAAE;AACjB,gBAAA,KAAK,EAAE,CAAC;AACR,gBAAA,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;AACpC,aAAA;AAED,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC1B,SAAA;KACF;;AAGO,IAAA,cAAc,CAAC,MAAe,EAAA;AACpC,QAAA,IAAI,MAAM,KAAK,IAAI,CAAC,SAAS,EAAE;AAC7B,YAAA,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;YACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AAEjE,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;AAC1B,gBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAChD,aAAA;AAED,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACxC,SAAA;KACF;AAED;;;AAGG;AACK,IAAA,cAAc,CAAC,IAAkB,EAAA;AACvC,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,CAAC,qBAAqB,CACxB,IAAI,EACJ,MAAM,CAAC,gBAAqD,CAC7D,CAAC;YACF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;;;;YAKhD,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,SAAS,EAAE,CAAC;AAC9C,SAAA;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;AAED;;;AAGG;AACK,IAAA,iBAAiB,CAAC,IAAkB,EAAA;QAC1C,OAAO,IAAI,aAAa,CAAC;YACvB,gBAAgB,EAAE,IAAI,CAAC,QAAQ;AAC5B,iBAAA,QAAQ,EAAE;AACV,iBAAA,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC;AAClC,iBAAA,kBAAkB,EAAE;AACpB,iBAAA,iBAAiB,EAAE;iBACnB,qBAAqB,CAAC,sCAAsC,CAAC;AAChE,YAAA,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,kCAAkC;YACvE,UAAU,EAAE,IAAI,CAAC,iBAAiB;AAClC,YAAA,cAAc,EAAE,IAAI,CAAC,eAAe,EAAE;YACtC,SAAS,EAAE,IAAI,CAAC,IAAI;AACrB,SAAA,CAAC,CAAC;KACJ;AAED;;;;AAIG;IACK,qBAAqB,CAAC,IAAkB,EAAE,QAA2C,EAAA;QAC3F,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,YAAA,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,IAAG;AAC1C,gBAAA,MAAM,IAAI,GAAkB,MAAM,CAAC,cAAc,CAAC,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,QAAQ,CAAC;AAC5F,gBAAA,MAAM,IAAI,GAAkB,MAAM,CAAC,cAAc,CAAC,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO,CAAC;;;;gBAKzF,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,oBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,kBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAC9D,iBAAA;AAAM,qBAAA;AACL,oBAAA,IAAI,CAAC,kBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACtC,iBAAA;AACH,aAAC,CAAC,CAAC;AACJ,SAAA;KACF;AAED;;;;AAIG;IACK,YAAY,CAAC,IAAkB,EAAE,gBAAmD,EAAA;QAC1F,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,GAC5B,IAAI,CAAC,SAAS,KAAK,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEpE,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,GAC9B,IAAI,CAAC,SAAS,KAAK,OAAO,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAErE,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QAC9D,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QAC9D,IAAI,OAAO,GAAG,CAAC,CAAC;AAEhB,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;;;AAG1B,YAAA,gBAAgB,GAAG,OAAO,GAAG,IAAI,CAAC,SAAS,KAAK,QAAQ,GAAG,OAAO,GAAG,KAAK,CAAC;AAC3E,YAAA,eAAe,GAAG,QAAQ,GAAG,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK,CAAC;YAEjE,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,gBAAA,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,EAAE;oBACpC,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,KAAK,CAAC;AACvD,oBAAA,IAAI,CAAC,mBAAmB,GAAG,SAAS,GAAG,SAAS,CAAC,eAAe,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC;AAClF,iBAAA;AAED,gBAAA,OAAO,GAAG,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC;AACxF,aAAA;AACF,SAAA;AAAM,aAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAC/B,YAAA,OAAO,GAAG,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK,CAAC;AAChD,YAAA,eAAe,GAAG,gBAAgB,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK,CAAC;AACjE,SAAA;QAED,gBAAgB,CAAC,aAAa,CAAC;YAC7B,EAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAC;AAC/C,YAAA,EAAC,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAAC;AAClF,YAAA;gBACE,OAAO;AACP,gBAAA,OAAO,EAAE,eAAe;gBACxB,QAAQ;AACR,gBAAA,QAAQ,EAAE,gBAAgB;gBAC1B,OAAO,EAAE,CAAC,OAAO;AAClB,aAAA;AACD,YAAA;AACE,gBAAA,OAAO,EAAE,eAAe;AACxB,gBAAA,OAAO,EAAE,eAAe;AACxB,gBAAA,QAAQ,EAAE,gBAAgB;AAC1B,gBAAA,QAAQ,EAAE,gBAAgB;gBAC1B,OAAO,EAAE,CAAC,OAAO;AAClB,aAAA;AACF,SAAA,CAAC,CAAC;KACJ;;IAGO,mBAAmB,GAAA;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAY,CAAC,aAAa,EAAE,CAAC;QACnD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAY,CAAC,WAAW,EAAE,CAAC;AACpD,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAGC,EAAY,EAAE,CAAC;AAChG,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB;AACpC,cAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC,IAAI,CACtC,MAAM,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,iBAAiB,CAAC,EACnD,MAAM,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,CAC7B;cACDA,EAAY,EAAE,CAAC;QAEnB,OAAO,KAAK,CAAC,QAAQ,EAAE,WAA0C,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;KACxF;;AAGD,IAAA,gBAAgB,CAAC,KAAiB,EAAA;AAChC,QAAA,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,EAAE;;;AAG3C,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;;;;AAK1D,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;gBAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;AACxB,aAAA;AACF,SAAA;KACF;;AAGD,IAAA,cAAc,CAAC,KAAoB,EAAA;AACjC,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;;AAG9B,QAAA,IAAI,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,EAAE;AAC1C,YAAA,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;AAC7B,SAAA;QAED,IACE,IAAI,CAAC,eAAe,EAAE;aACrB,CAAC,OAAO,KAAK,WAAW,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK;iBAC5C,OAAO,KAAK,UAAU,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,EACjD;AACA,YAAA,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;YAC5B,IAAI,CAAC,QAAQ,EAAE,CAAC;AACjB,SAAA;KACF;;AAGD,IAAA,YAAY,CAAC,KAAiB,EAAA;AAC5B,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;;YAE1B,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;AACjB,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,UAAU,EAAE,CAAC;AACnB,SAAA;KACF;;IAGO,YAAY,GAAA;;QAElB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YACxD,OAAO;AACR,SAAA;AAED,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB;AAC/C,aAAA,QAAQ,EAAE;;;;aAIV,IAAI,CACH,MAAM,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EACvE,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC,CACxB;aACA,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;;;;YAKzB,IAAI,IAAI,CAAC,IAAI,YAAY,YAAY,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;;;gBAG/D,IAAI,CAAC,IAAI,CAAC,cAAc;qBACrB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,mBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC;qBACvF,SAAS,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AACrC,aAAA;AAAM,iBAAA;gBACL,IAAI,CAAC,QAAQ,EAAE,CAAC;AACjB,aAAA;AACH,SAAC,CAAC,CAAC;KACN;;AAGO,IAAA,UAAU,CAAC,IAAkB,EAAA;;;;AAInC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,EAAE;AAClE,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC7E,SAAA;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;8GA5kBmB,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAD,IAAA,CAAA,OAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EA8J7B,wBAAwB,EAAA,EAAA,EAAA,KAAA,EACxB,cAAc,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAAE,WAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,cAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGA/JJ,mBAAmB,EAAA,MAAA,EAAA,EAAA,4BAAA,EAAA,CAAA,sBAAA,EAAA,8BAAA,CAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,MAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,EAAA,UAAA,CAAA,EAAA,YAAA,EAAA,CAAA,4BAAA,EAAA,cAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,sBAAA,EAAA,WAAA,EAAA,0BAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,wBAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,gCAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAVxC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,IAAI,EAAE;AACJ,wBAAA,sBAAsB,EAAE,sBAAsB;AAC9C,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,sBAAsB,EAAE,gCAAgC;AACxD,wBAAA,SAAS,EAAE,sBAAsB;AACjC,wBAAA,aAAa,EAAE,0BAA0B;AACzC,wBAAA,WAAW,EAAE,wBAAwB;AACtC,qBAAA;AACF,iBAAA,CAAA;;0BA+JI,MAAM;2BAAC,wBAAwB,CAAA;;0BAC/B,MAAM;2BAAC,cAAc,CAAA;;0BAAG,QAAQ;;0BAGhC,QAAQ;;0BAAI,IAAI;;0BAChB,QAAQ;4FA1HP,4BAA4B,EAAA,CAAA;sBAD/B,KAAK;uBAAC,sBAAsB,CAAA;gBAUzB,IAAI,EAAA,CAAA;sBADP,KAAK;uBAAC,mBAAmB,CAAA;gBAgCG,QAAQ,EAAA,CAAA;sBAApC,KAAK;uBAAC,oBAAoB,CAAA;gBAOU,YAAY,EAAA,CAAA;sBAAhD,KAAK;uBAAC,4BAA4B,CAAA;gBAGhB,UAAU,EAAA,CAAA;sBAA5B,MAAM;gBAQY,UAAU,EAAA,CAAA;sBAA5B,MAAM;gBAGY,UAAU,EAAA,CAAA;sBAA5B,MAAM;gBAQY,WAAW,EAAA,CAAA;sBAA7B,MAAM;;AAieT;AAQM,MAAO,cAAe,SAAQ,mBAAmB,CAAA;8GAA1C,cAAc,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,QAAA,EAAA,6CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAP1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAA6C,2CAAA,CAAA;AACvD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAChC,qBAAA;AACD,oBAAA,QAAQ,EAAE,gBAAgB;AAC3B,iBAAA,CAAA;;;MChpBY,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,iBAHT,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,CATzD,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,aAAa,aAErE,mBAAmB;YACnB,OAAO;YACP,eAAe;YACf,WAAW;YACX,cAAc;YACd,cAAc,CAAA,EAAA,CAAA,CAAA,EAAA;AAKL,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAFb,SAAA,EAAA,CAAC,yCAAyC,CAAC,EAV5C,OAAA,EAAA,CAAA,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,aAAa,EAErE,mBAAmB;YAEnB,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAQN,aAAa,EAAA,UAAA,EAAA,CAAA;kBAbzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,aAAa,CAAC;AACxE,oBAAA,OAAO,EAAE;wBACP,mBAAmB;wBACnB,OAAO;wBACP,eAAe;wBACf,WAAW;wBACX,cAAc;wBACd,cAAc;AACf,qBAAA;oBACD,YAAY,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,CAAC;oBACpE,SAAS,EAAE,CAAC,yCAAyC,CAAC;AACvD,iBAAA,CAAA;;;AC9BD;;AAEG;;;;"}