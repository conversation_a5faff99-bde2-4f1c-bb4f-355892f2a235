{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Breton [br]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/jbleduigou\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function relativeTimeWithMutation(number, withoutSuffix, key) {\n    var format = {\n      mm: 'munutenn',\n      MM: 'miz',\n      dd: 'devezh'\n    };\n    return number + ' ' + mutation(format[key], number);\n  }\n  function specialMutationForYears(number) {\n    switch (lastNumber(number)) {\n      case 1:\n      case 3:\n      case 4:\n      case 5:\n      case 9:\n        return number + ' bloaz';\n      default:\n        return number + ' vloaz';\n    }\n  }\n  function lastNumber(number) {\n    if (number > 9) {\n      return lastNumber(number % 10);\n    }\n    return number;\n  }\n  function mutation(text, number) {\n    if (number === 2) {\n      return softMutation(text);\n    }\n    return text;\n  }\n  function softMutation(text) {\n    var mutationTable = {\n      m: 'v',\n      b: 'v',\n      d: 'z'\n    };\n    if (mutationTable[text.charAt(0)] === undefined) {\n      return text;\n    }\n    return mutationTable[text.charAt(0)] + text.substring(1);\n  }\n  var monthsParse = [/^gen/i, /^c[ʼ\\']hwe/i, /^meu/i, /^ebr/i, /^mae/i, /^(mez|eve)/i, /^gou/i, /^eos/i, /^gwe/i, /^her/i, /^du/i, /^ker/i],\n    monthsRegex = /^(genver|c[ʼ\\']hwevrer|meurzh|ebrel|mae|mezheven|gouere|eost|gwengolo|here|du|kerzu|gen|c[ʼ\\']hwe|meu|ebr|mae|eve|gou|eos|gwe|her|du|ker)/i,\n    monthsStrictRegex = /^(genver|c[ʼ\\']hwevrer|meurzh|ebrel|mae|mezheven|gouere|eost|gwengolo|here|du|kerzu)/i,\n    monthsShortStrictRegex = /^(gen|c[ʼ\\']hwe|meu|ebr|mae|eve|gou|eos|gwe|her|du|ker)/i,\n    fullWeekdaysParse = [/^sul/i, /^lun/i, /^meurzh/i, /^merc[ʼ\\']her/i, /^yaou/i, /^gwener/i, /^sadorn/i],\n    shortWeekdaysParse = [/^Sul/i, /^Lun/i, /^Meu/i, /^Mer/i, /^Yao/i, /^Gwe/i, /^Sad/i],\n    minWeekdaysParse = [/^Su/i, /^Lu/i, /^Me([^r]|$)/i, /^Mer/i, /^Ya/i, /^Gw/i, /^Sa/i];\n  var br = moment.defineLocale('br', {\n    months: 'Genver_Cʼhwevrer_Meurzh_Ebrel_Mae_Mezheven_Gouere_Eost_Gwengolo_Here_Du_Kerzu'.split('_'),\n    monthsShort: 'Gen_Cʼhwe_Meu_Ebr_Mae_Eve_Gou_Eos_Gwe_Her_Du_Ker'.split('_'),\n    weekdays: 'Sul_Lun_Meurzh_Mercʼher_Yaou_Gwener_Sadorn'.split('_'),\n    weekdaysShort: 'Sul_Lun_Meu_Mer_Yao_Gwe_Sad'.split('_'),\n    weekdaysMin: 'Su_Lu_Me_Mer_Ya_Gw_Sa'.split('_'),\n    weekdaysParse: minWeekdaysParse,\n    fullWeekdaysParse: fullWeekdaysParse,\n    shortWeekdaysParse: shortWeekdaysParse,\n    minWeekdaysParse: minWeekdaysParse,\n    monthsRegex: monthsRegex,\n    monthsShortRegex: monthsRegex,\n    monthsStrictRegex: monthsStrictRegex,\n    monthsShortStrictRegex: monthsShortStrictRegex,\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: monthsParse,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D [a viz] MMMM YYYY',\n      LLL: 'D [a viz] MMMM YYYY HH:mm',\n      LLLL: 'dddd, D [a viz] MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Hiziv da] LT',\n      nextDay: '[Warcʼhoazh da] LT',\n      nextWeek: 'dddd [da] LT',\n      lastDay: '[Decʼh da] LT',\n      lastWeek: 'dddd [paset da] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'a-benn %s',\n      past: '%s ʼzo',\n      s: 'un nebeud segondennoù',\n      ss: '%d eilenn',\n      m: 'ur vunutenn',\n      mm: relativeTimeWithMutation,\n      h: 'un eur',\n      hh: '%d eur',\n      d: 'un devezh',\n      dd: relativeTimeWithMutation,\n      M: 'ur miz',\n      MM: relativeTimeWithMutation,\n      y: 'ur bloaz',\n      yy: specialMutationForYears\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(añ|vet)/,\n    ordinal: function (number) {\n      var output = number === 1 ? 'añ' : 'vet';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    },\n    meridiemParse: /a.m.|g.m./,\n    // goude merenn | a-raok merenn\n    isPM: function (token) {\n      return token === 'g.m.';\n    },\n    meridiem: function (hour, minute, isLower) {\n      return hour < 12 ? 'a.m.' : 'g.m.';\n    }\n  });\n  return br;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "relativeTimeWithMutation", "number", "withoutSuffix", "key", "format", "mm", "MM", "dd", "mutation", "specialMutationForYears", "lastNumber", "text", "softMutation", "mutationTable", "m", "b", "d", "char<PERSON>t", "undefined", "substring", "<PERSON><PERSON><PERSON>e", "monthsRegex", "monthsStrictRegex", "monthsShortStrictRegex", "fullWeekdaysParse", "shortWeekdaysParse", "minWeekdaysParse", "br", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParse", "monthsShortRegex", "longMonthsParse", "shortMonthsParse", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "h", "hh", "M", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "output", "week", "dow", "doy", "meridiemParse", "isPM", "token", "meridiem", "hour", "minute", "isLower"], "sources": ["C:/e-syndic/esyndic-velzon/node_modules/moment/locale/br.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Breton [br]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/jbleduigou\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function relativeTimeWithMutation(number, withoutSuffix, key) {\n        var format = {\n            mm: 'munutenn',\n            MM: 'miz',\n            dd: 'devezh',\n        };\n        return number + ' ' + mutation(format[key], number);\n    }\n    function specialMutationForYears(number) {\n        switch (lastNumber(number)) {\n            case 1:\n            case 3:\n            case 4:\n            case 5:\n            case 9:\n                return number + ' bloaz';\n            default:\n                return number + ' vloaz';\n        }\n    }\n    function lastNumber(number) {\n        if (number > 9) {\n            return lastNumber(number % 10);\n        }\n        return number;\n    }\n    function mutation(text, number) {\n        if (number === 2) {\n            return softMutation(text);\n        }\n        return text;\n    }\n    function softMutation(text) {\n        var mutationTable = {\n            m: 'v',\n            b: 'v',\n            d: 'z',\n        };\n        if (mutationTable[text.charAt(0)] === undefined) {\n            return text;\n        }\n        return mutationTable[text.charAt(0)] + text.substring(1);\n    }\n\n    var monthsParse = [\n            /^gen/i,\n            /^c[ʼ\\']hwe/i,\n            /^meu/i,\n            /^ebr/i,\n            /^mae/i,\n            /^(mez|eve)/i,\n            /^gou/i,\n            /^eos/i,\n            /^gwe/i,\n            /^her/i,\n            /^du/i,\n            /^ker/i,\n        ],\n        monthsRegex =\n            /^(genver|c[ʼ\\']hwevrer|meurzh|ebrel|mae|mezheven|gouere|eost|gwengolo|here|du|kerzu|gen|c[ʼ\\']hwe|meu|ebr|mae|eve|gou|eos|gwe|her|du|ker)/i,\n        monthsStrictRegex =\n            /^(genver|c[ʼ\\']hwevrer|meurzh|ebrel|mae|mezheven|gouere|eost|gwengolo|here|du|kerzu)/i,\n        monthsShortStrictRegex =\n            /^(gen|c[ʼ\\']hwe|meu|ebr|mae|eve|gou|eos|gwe|her|du|ker)/i,\n        fullWeekdaysParse = [\n            /^sul/i,\n            /^lun/i,\n            /^meurzh/i,\n            /^merc[ʼ\\']her/i,\n            /^yaou/i,\n            /^gwener/i,\n            /^sadorn/i,\n        ],\n        shortWeekdaysParse = [\n            /^Sul/i,\n            /^Lun/i,\n            /^Meu/i,\n            /^Mer/i,\n            /^Yao/i,\n            /^Gwe/i,\n            /^Sad/i,\n        ],\n        minWeekdaysParse = [\n            /^Su/i,\n            /^Lu/i,\n            /^Me([^r]|$)/i,\n            /^Mer/i,\n            /^Ya/i,\n            /^Gw/i,\n            /^Sa/i,\n        ];\n\n    var br = moment.defineLocale('br', {\n        months: 'Genver_Cʼhwevrer_Meurzh_Ebrel_Mae_Mezheven_Gouere_Eost_Gwengolo_Here_Du_Kerzu'.split(\n            '_'\n        ),\n        monthsShort: 'Gen_Cʼhwe_Meu_Ebr_Mae_Eve_Gou_Eos_Gwe_Her_Du_Ker'.split('_'),\n        weekdays: 'Sul_Lun_Meurzh_Mercʼher_Yaou_Gwener_Sadorn'.split('_'),\n        weekdaysShort: 'Sul_Lun_Meu_Mer_Yao_Gwe_Sad'.split('_'),\n        weekdaysMin: 'Su_Lu_Me_Mer_Ya_Gw_Sa'.split('_'),\n        weekdaysParse: minWeekdaysParse,\n        fullWeekdaysParse: fullWeekdaysParse,\n        shortWeekdaysParse: shortWeekdaysParse,\n        minWeekdaysParse: minWeekdaysParse,\n\n        monthsRegex: monthsRegex,\n        monthsShortRegex: monthsRegex,\n        monthsStrictRegex: monthsStrictRegex,\n        monthsShortStrictRegex: monthsShortStrictRegex,\n        monthsParse: monthsParse,\n        longMonthsParse: monthsParse,\n        shortMonthsParse: monthsParse,\n\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D [a viz] MMMM YYYY',\n            LLL: 'D [a viz] MMMM YYYY HH:mm',\n            LLLL: 'dddd, D [a viz] MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Hiziv da] LT',\n            nextDay: '[Warcʼhoazh da] LT',\n            nextWeek: 'dddd [da] LT',\n            lastDay: '[Decʼh da] LT',\n            lastWeek: 'dddd [paset da] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'a-benn %s',\n            past: '%s ʼzo',\n            s: 'un nebeud segondennoù',\n            ss: '%d eilenn',\n            m: 'ur vunutenn',\n            mm: relativeTimeWithMutation,\n            h: 'un eur',\n            hh: '%d eur',\n            d: 'un devezh',\n            dd: relativeTimeWithMutation,\n            M: 'ur miz',\n            MM: relativeTimeWithMutation,\n            y: 'ur bloaz',\n            yy: specialMutationForYears,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(añ|vet)/,\n        ordinal: function (number) {\n            var output = number === 1 ? 'añ' : 'vet';\n            return number + output;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n        meridiemParse: /a.m.|g.m./, // goude merenn | a-raok merenn\n        isPM: function (token) {\n            return token === 'g.m.';\n        },\n        meridiem: function (hour, minute, isLower) {\n            return hour < 12 ? 'a.m.' : 'g.m.';\n        },\n    });\n\n    return br;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,wBAAwBA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAE;IAC1D,IAAIC,MAAM,GAAG;MACTC,EAAE,EAAE,UAAU;MACdC,EAAE,EAAE,KAAK;MACTC,EAAE,EAAE;IACR,CAAC;IACD,OAAON,MAAM,GAAG,GAAG,GAAGO,QAAQ,CAACJ,MAAM,CAACD,GAAG,CAAC,EAAEF,MAAM,CAAC;EACvD;EACA,SAASQ,uBAAuBA,CAACR,MAAM,EAAE;IACrC,QAAQS,UAAU,CAACT,MAAM,CAAC;MACtB,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACF,OAAOA,MAAM,GAAG,QAAQ;MAC5B;QACI,OAAOA,MAAM,GAAG,QAAQ;IAChC;EACJ;EACA,SAASS,UAAUA,CAACT,MAAM,EAAE;IACxB,IAAIA,MAAM,GAAG,CAAC,EAAE;MACZ,OAAOS,UAAU,CAACT,MAAM,GAAG,EAAE,CAAC;IAClC;IACA,OAAOA,MAAM;EACjB;EACA,SAASO,QAAQA,CAACG,IAAI,EAAEV,MAAM,EAAE;IAC5B,IAAIA,MAAM,KAAK,CAAC,EAAE;MACd,OAAOW,YAAY,CAACD,IAAI,CAAC;IAC7B;IACA,OAAOA,IAAI;EACf;EACA,SAASC,YAAYA,CAACD,IAAI,EAAE;IACxB,IAAIE,aAAa,GAAG;MAChBC,CAAC,EAAE,GAAG;MACNC,CAAC,EAAE,GAAG;MACNC,CAAC,EAAE;IACP,CAAC;IACD,IAAIH,aAAa,CAACF,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC,KAAKC,SAAS,EAAE;MAC7C,OAAOP,IAAI;IACf;IACA,OAAOE,aAAa,CAACF,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACQ,SAAS,CAAC,CAAC,CAAC;EAC5D;EAEA,IAAIC,WAAW,GAAG,CACV,OAAO,EACP,aAAa,EACb,OAAO,EACP,OAAO,EACP,OAAO,EACP,aAAa,EACb,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,CACV;IACDC,WAAW,GACP,4IAA4I;IAChJC,iBAAiB,GACb,uFAAuF;IAC3FC,sBAAsB,GAClB,0DAA0D;IAC9DC,iBAAiB,GAAG,CAChB,OAAO,EACP,OAAO,EACP,UAAU,EACV,gBAAgB,EAChB,QAAQ,EACR,UAAU,EACV,UAAU,CACb;IACDC,kBAAkB,GAAG,CACjB,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACV;IACDC,gBAAgB,GAAG,CACf,MAAM,EACN,MAAM,EACN,cAAc,EACd,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,CACT;EAEL,IAAIC,EAAE,GAAG5B,MAAM,CAAC6B,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,+EAA+E,CAACC,KAAK,CACzF,GACJ,CAAC;IACDC,WAAW,EAAE,kDAAkD,CAACD,KAAK,CAAC,GAAG,CAAC;IAC1EE,QAAQ,EAAE,4CAA4C,CAACF,KAAK,CAAC,GAAG,CAAC;IACjEG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,uBAAuB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC/CK,aAAa,EAAET,gBAAgB;IAC/BF,iBAAiB,EAAEA,iBAAiB;IACpCC,kBAAkB,EAAEA,kBAAkB;IACtCC,gBAAgB,EAAEA,gBAAgB;IAElCL,WAAW,EAAEA,WAAW;IACxBe,gBAAgB,EAAEf,WAAW;IAC7BC,iBAAiB,EAAEA,iBAAiB;IACpCC,sBAAsB,EAAEA,sBAAsB;IAC9CH,WAAW,EAAEA,WAAW;IACxBiB,eAAe,EAAEjB,WAAW;IAC5BkB,gBAAgB,EAAElB,WAAW;IAE7BmB,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,qBAAqB;MACzBC,GAAG,EAAE,2BAA2B;MAChCC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE,oBAAoB;MAC7BC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,oBAAoB;MAC9BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,uBAAuB;MAC1BC,EAAE,EAAE,WAAW;MACf3C,CAAC,EAAE,aAAa;MAChBT,EAAE,EAAEL,wBAAwB;MAC5B0D,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZ3C,CAAC,EAAE,WAAW;MACdT,EAAE,EAAEP,wBAAwB;MAC5B4D,CAAC,EAAE,QAAQ;MACXtD,EAAE,EAAEN,wBAAwB;MAC5B6D,CAAC,EAAE,UAAU;MACbC,EAAE,EAAErD;IACR,CAAC;IACDsD,sBAAsB,EAAE,iBAAiB;IACzCC,OAAO,EAAE,SAAAA,CAAU/D,MAAM,EAAE;MACvB,IAAIgE,MAAM,GAAGhE,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK;MACxC,OAAOA,MAAM,GAAGgE,MAAM;IAC1B,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ,CAAC;IACDC,aAAa,EAAE,WAAW;IAAE;IAC5BC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAOA,KAAK,KAAK,MAAM;IAC3B,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,OAAOF,IAAI,GAAG,EAAE,GAAG,MAAM,GAAG,MAAM;IACtC;EACJ,CAAC,CAAC;EAEF,OAAO9C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}